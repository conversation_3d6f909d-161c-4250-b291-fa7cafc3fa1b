# 本地开发环境配置示例
# 复制此文件为 settings.local.yaml 并根据需要修改

# MySQL 数据库连接配置（本地开发时可以使用测试数据库或跳过）
database:
  url:
    driver_name: "mysql+pymysql"
    username: "your_username"
    password: "your_password"
    host: "localhost"
    port: 3306
    database: "deepwiki_local_dev"

  connect_timeout: 10
  pool:
    size: 5           # 本地开发时减少连接池大小
    max_overflow: 5
    recycle: 3600
    pre_ping: true
    timeout: 30
  echo: false

sso:
  app_key: "your_app_key"
  app_secret: "your_app_secret"
  base_url: "https://your-sso-server.com/auth"

session:
  cookie:
    max-age: -1

wiki_generation_mode: 'thread'

# Wiki任务管理配置 - 本地开发模式
wiki_jobs:
  global_max_concurrent: 2      # 本地开发时减少并发数
  instance_max_concurrent: 1    # 本地开发时单实例并发数
  timeout_minutes: 60           # 本地开发时缩短超时时间
  lock_timeout_minutes: 30
  heartbeat_timeout_minutes: 3
  cleanup_interval_seconds: 120
  
  # 【重要】本地开发模式设置
  # 设为 false 时不会注册到数据库，不参与分布式任务调度
  # 适用于本地开发和调试，避免影响生产环境
  enable_distributed_lock: false

# 后端请求拦截配置
app:
  security:
    excludeUrl:
      ^/api/lang/config$:
        - GET
      ^/auth/.*:
        - GET
      ^/api/processed_projects$:
        - GET
      ^/api/wiki_cache$:
        - GET
      ^/health$:
        - GET
    developMode: false    # 本地开发模式，设为true时不拦截请求
    jwt:
      secret_key: "your_jwt_secret_key_for_local_dev"
      expires_minutes: 1440
      token_min_refresh_interval: 300
      refresh_token: true
    aes:
      secret_key: "your_aes_secret_key" 