# =============================================================================
# Base Image for DeepWiki
#
# This Dockerfile creates a base image with Python, UV, and Node.js pre-installed.
# It starts from the official UV image, adds Node.js, and sets up
# optimized APT sources for faster builds within the corporate network.
#
# How to build and push:
# 1. docker build -f Dockerfile.base -t hub.iwhalecloud.com:5555/deepwiki/base:v1.0 .
# 2. docker push hub.iwhalecloud.com:5555/deepwiki/base:v1.0
# =============================================================================

# Global ARG for base image selection
ARG BASE_IMAGE=hub-nj.iwhalecloud.com/public/python:3.12-slim

# Stage 1: Node.js stage to extract Node.js binaries
FROM hub.iwhalecloud.com:5555/hub_docker/library/node:20-bookworm-slim AS node_base

# Stage 2: Main base image with Python
FROM ${BASE_IMAGE}

# Set ARG for non-interactive frontend to avoid prompts
ARG DEBIAN_FRONTEND=noninteractive

# Set up optimized and reliable APT sources and install common tools.
# This is the slowest step, so we do it here in the base image.
RUN rm -f /etc/apt/sources.list.d/* && \
    echo "deb http://mirrors.aliyun.com/debian/ bookworm main" > /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/debian-security bookworm-security main" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/debian/ bookworm-updates main" >> /etc/apt/sources.list && \
    apt-get update && \
    apt-get install -y --no-install-recommends \
        curl git ca-certificates xz-utils \
        ripgrep fd-find nfs4-acl-tools acl \
        findutils && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install Node.js from the node_base stage
COPY --from=node_base /usr/local/bin/node /usr/local/bin/
COPY --from=node_base /usr/local/bin/npm /usr/local/bin/
COPY --from=node_base /usr/local/lib/node_modules /usr/local/lib/node_modules

# Create symlinks for npm and npx to ensure they're accessible
RUN ln -sf /usr/local/lib/node_modules/npm/bin/npm-cli.js /usr/local/bin/npm && \
    ln -sf /usr/local/lib/node_modules/npm/bin/npx-cli.js /usr/local/bin/npx

# 添加 UV 安装
COPY --from=ghcr.io/astral-sh/uv:latest /uv /bin/uv

# Clean up to reduce image size
RUN apt-get autoremove -y && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Verify installations
RUN echo "Verifying installations..." && \
    python --version && \
    uv --version && \
    node --version && \
    npm --version && \
    rg --version && \
    fdfind --version && \
    find --version && \
    echo "Verification complete."

# Set default working directory
WORKDIR /app

LABEL maintainer="<EMAIL>"
LABEL description="Base image for DeepWiki with Python 3.12, UV, and Node.js 20 (Alpine)."