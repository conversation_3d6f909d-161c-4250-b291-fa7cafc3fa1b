OPENAI_API_KEY=ailab_kzU6wYkoMfpf+/oSfb6IvoxveA9PUAckq9PCARsdhXscBEC4f3Y+I0xJZg8tp1dycd669ST4oIypjH8bmilRNw9t5N2qV+RE+HebxthiextwNKkEFHcL3p4=
OPENAI_API_BASE=https://lab.iwhalecloud.com/gpt-proxy/v1
OPENAI_BASE_URL=https://lab.iwhalecloud.com/gpt-proxy/v1
# 可选：如果您想使用OpenRouter模型，添加此项 20250526
OPENROUTER_API_KEY=your_openrouter_api_key
GOOGLE_API_KEY=AIzaSyBZGfiF_5e1MU4xCe_iPkM91iIb6lOfO7g

USE_DOCCHAIN=true
DOCCHAIN_BASE_URL=http://***********:7000
DOCCHAIN_API_KEY=o-WI70edqv5VRXt7R4VrQd3BrbA8eRFMq-v6v1M6w9U
DOCCHAIN_DIRECT_MODE=false
NEXT_PUBLIC_SERVER_BASE_URL=http://localhost:8001
# NEXT_PUBLIC_SERVER_BASE_URL=http://*************:3006
# 生产dockchain地址
LAB_DOCCHAIN_BASE_URL=https://lab.iwhalecloud.com/docchain
LAB_DOCCHAIN_API_KEY=AnfvNc7gF4PCU0rdzVaXuGWLmoT90otWbtwWYNK5fpg
LAB_DOCCHAIN_DIRECT_MODE=false


LANGFUSE_SECRET_KEY="******************************************"
LANGFUSE_PUBLIC_KEY="pk-lf-b7204cba-432a-4754-bad0-272600a8d6a4"
LANGFUSE_HOST="http://************:3000"
LANGFUSE_ENABLED=true
LANGFUSE_ENVIRONMENT=dev
