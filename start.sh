#!/bin/bash

# =============================================================================
# 改进版Docker启动脚本 - 支持优雅停止
# =============================================================================

set -e

# 确保脚本在 Bash 下运行，以支持数组和 ERR trap
if [ -z "${BASH_VERSION:-}" ]; then
    if command -v bash >/dev/null 2>&1; then
        exec bash "$0" "$@"
    else
        echo "[ERROR] Bash is required to run this script." >&2
        exit 1
    fi
fi

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" >&2
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" >&2
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1" >&2
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

# PID文件路径
API_PID_FILE="/tmp/api.pid"
FRONTEND_PID_FILE="/tmp/frontend.pid"

# 清理函数
cleanup() {
    local signal_received="$1"
    log_info "收到信号 ${signal_received}，开始优雅停止所有服务..."
    
    # 停止API服务器
    if [ -f "$API_PID_FILE" ]; then
        API_PID=$(cat "$API_PID_FILE" 2>/dev/null || echo "")
        if [ -n "$API_PID" ] && kill -0 "$API_PID" 2>/dev/null; then
            log_info "停止API服务器 (PID: $API_PID)..."
            # 发送SIGTERM给API进程，让Python应用处理优雅停止
            kill -TERM "$API_PID" 2>/dev/null || true
            
            # 等待进程优雅停止，最多等待30秒
            local count=0
            while kill -0 "$API_PID" 2>/dev/null && [ $count -lt 30 ]; do
                sleep 1
                count=$((count + 1))
                if [ $((count % 5)) -eq 0 ]; then
                    log_info "等待API服务器停止... ($count/30秒)"
                fi
            done
            
            # 如果进程还在运行，强制终止
            if kill -0 "$API_PID" 2>/dev/null; then
                log_warn "API服务器未在30秒内停止，强制终止..."
                kill -KILL "$API_PID" 2>/dev/null || true
            else
                log_success "API服务器已优雅停止"
            fi
        fi
        rm -f "$API_PID_FILE"
    fi
    
    # 停止前端服务器
    if [ -f "$FRONTEND_PID_FILE" ]; then
        FRONTEND_PID=$(cat "$FRONTEND_PID_FILE" 2>/dev/null || echo "")
        if [ -n "$FRONTEND_PID" ] && kill -0 "$FRONTEND_PID" 2>/dev/null; then
            log_info "停止前端服务器 (PID: $FRONTEND_PID)..."
            kill -TERM "$FRONTEND_PID" 2>/dev/null || true
            
            # 等待前端进程停止，最多等待10秒
            local count=0
            while kill -0 "$FRONTEND_PID" 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            if kill -0 "$FRONTEND_PID" 2>/dev/null; then
                log_warn "前端服务器未在10秒内停止，强制终止..."
                kill -KILL "$FRONTEND_PID" 2>/dev/null || true
            else
                log_success "前端服务器已停止"
            fi
        fi
        rm -f "$FRONTEND_PID_FILE"
    fi
    
    log_success "所有服务已停止，容器即将退出"
    exit 0
}

# 错误处理函数
handle_error() {
    local exit_code=$?
    log_error "启动过程中发生错误 (退出码: $exit_code)"
    cleanup "ERROR"
}

# 设置错误处理
trap 'handle_error' ERR

# 设置信号处理器
trap 'cleanup "SIGTERM"' SIGTERM
trap 'cleanup "SIGINT"' SIGINT
trap 'cleanup "SIGQUIT"' SIGQUIT

# =============================================================================
# 环境检查
# =============================================================================

log_info "检查Docker环境和配置..."

# 检查必需的环境变量
MISSING_VARS=()

if [ -z "$OPENAI_API_KEY" ]; then
    MISSING_VARS+=("OPENAI_API_KEY")
fi

if [ ${#MISSING_VARS[@]} -gt 0 ]; then
    log_warn "以下环境变量未设置: ${MISSING_VARS[*]}"
    log_warn "这些变量对于 DeepWiki 正常运行是必需的"
fi

# =============================================================================
# 服务启动
# =============================================================================

log_info "启动DeepWiki服务..."

# 启动API服务器
API_HOST=${API_HOST:-0.0.0.0}
API_PORT=${API_PORT:-${PORT:-8001}}
API_WORKERS=${API_WORKERS:-8}

log_info "启动后端API服务器（地址: ${API_HOST}:${API_PORT}, workers: ${API_WORKERS})"

UVICORN_CMD=("uvicorn" "api.main:app" "--host" "$API_HOST" "--port" "$API_PORT")

# 仅当worker数量大于1时启用多worker模式
if [[ "$API_WORKERS" =~ ^[0-9]+$ ]] && [ "$API_WORKERS" -gt 1 ]; then
    UVICORN_CMD+=("--workers" "$API_WORKERS")
fi

# 支持自定义日志级别
if [ -n "${UVICORN_LOG_LEVEL:-}" ]; then
    UVICORN_CMD+=("--log-level" "$UVICORN_LOG_LEVEL")
fi

# 启动Uvicorn并记录PID
"${UVICORN_CMD[@]}" &
API_PID=$!
echo "$API_PID" > "$API_PID_FILE"

# 等待后端服务启动
sleep 3

if kill -0 "$API_PID" 2>/dev/null; then
    log_success "后端API服务器启动成功 (PID: $API_PID)"
else
    log_error "后端API服务器启动失败"
    exit 1
fi

# 启动前端服务器
log_info "启动前端服务器（端口: 3000）"
PORT=3000 HOSTNAME=0.0.0.0 node server.js &
FRONTEND_PID=$!
echo "$FRONTEND_PID" > "$FRONTEND_PID_FILE"

# 等待前端服务启动
sleep 3

if kill -0 "$FRONTEND_PID" 2>/dev/null; then
    log_success "前端服务器启动成功 (PID: $FRONTEND_PID)"
else
    log_error "前端服务器启动失败"
    # 清理已启动的API服务器
    kill "$API_PID" 2>/dev/null || true
    rm -f "$API_PID_FILE"
    exit 1
fi

log_success "DeepWiki应用启动完成！"
log_info "前端访问地址: http://localhost:3000"
log_info "后端API地址: http://localhost:${API_PORT}"
log_info "容器已准备就绪，等待请求或停止信号..."

# =============================================================================
# 进程监控和等待
# =============================================================================

# 监控函数
monitor_processes() {
    while true; do
        # 检查API进程
        if ! kill -0 "$API_PID" 2>/dev/null; then
            log_error "API服务器意外退出"
            cleanup "API_EXIT"
            return 1
        fi
        
        # 检查前端进程
        if ! kill -0 "$FRONTEND_PID" 2>/dev/null; then
            log_error "前端服务器意外退出"
            cleanup "FRONTEND_EXIT"
            return 1
        fi
        
        # 每30秒检查一次
        sleep 30
    done
}

# 在后台启动进程监控
monitor_processes &
MONITOR_PID=$!

# 等待任意信号或进程退出
wait

# 如果到达这里，说明收到了信号或进程异常退出
cleanup "PROCESS_EXIT" 
