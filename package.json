{"name": "deepwiki-open", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start", "lint": "next lint", "test": "next test src", "prepare": "husky"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix"]}, "pnpm": {"overrides": {"brace-expansion": "^2.0.2", "minimatch": "^8.0.4"}}, "dependencies": {"@heroicons/react": "^2.2.0", "@tanstack/react-table": "^8.21.3", "github-slugger": "^2.0.0", "i18next": "^25.3.2", "mermaid": "^11.4.1", "next": "15.5.4", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "react": "19.1.0", "react-countup": "^6.5.3", "react-dom": "19.1.0", "react-fast-marquee": "^1.6.5", "react-i18next": "^15.6.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-paginate": "^8.3.0", "react-select": "^5.10.2", "react-syntax-highlighter": "^15.6.1", "rehype-raw": "^7.0.0", "rehype-slug": "^6.0.0", "remark-gfm": "^4.0.1", "svg-pan-zoom": "^3.6.2", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4", "@types/github-slugger": "^2.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "eslint": "^9", "eslint-config-next": "15.5.4", "husky": "^9.1.7", "lint-staged": "^16.1.2", "tailwindcss": "^4", "typescript": "^5"}}