# Job池管理系统

## 概述

Job池管理系统用于管理Kubernetes Job资源池，实现Job的批量创建、分配和回收。系统使用Redis作为存储，支持高并发场景下的Job分配。

## 架构设计

### Redis数据结构

1. **job_pool:available** (Set)
   - 存储所有可用的job名称
   - 使用SPOP实现原子性的job分配

2. **job_pool:all** (Set)
   - 存储所有job名称（包括可用和已分配）
   - 用于快速检查job是否存在

3. **job_pool:user_allocation** (Hash)
   - Key: `user_id:wiki_id`
   - Value: job_name
   - 存储用户与job的分配关系

4. **job_pool:job_info** (Hash)
   - Key: job_name
   - Value: job信息JSON
   - 存储job的详细信息（创建时间、分配状态等）

5. **job_pool:lock:{user_id}:{wiki_id}** (String)
   - 分布式锁，防止并发分配冲突
   - 超时时间：10秒

### 并发控制

- 使用Redis的SETNX实现分布式锁
- 双重检查机制防止重复分配
- SPOP原子操作保证job分配的唯一性

## API接口

### 1. 批量创建Job池

**接口**: `POST /api/linux/group/batch/jobs`

**请求参数**:
```json
{
  "job_number": 50,
  "wct_api_key": "your_api_key",
  "start_id": 1
}
```

**参数说明**:
- `job_number`: 要创建的job数量（必填）
- `wct_api_key`: WCT API密钥（必填）
- `start_id`: 起始ID，默认为1（可选）

**响应示例**:
```json
{
  "success": true,
  "message": "批量创建完成: 成功50个, 跳过0个, 失败0个",
  "created_count": 50,
  "skipped_count": 0,
  "failed_count": 0,
  "created_jobs": [
    {
      "job_name": "job-dev-1",
      "job_id": 1
    },
    ...
  ]
}
```

**Job命名规则**: `job-{environment}-{id}`
- environment: 环境标识（如dev、test、prod）
- id: 从start_id开始递增

### 2. 分配Job

**接口**: `POST /api/linux/group/bind`

**请求参数**:
```json
{
  "wiki_id": 123
}
```

**参数说明**:
- `wiki_id`: Wiki ID（必填）

**响应示例**:
```json
{
  "success": true,
  "message": "Job分配成功",
  "job_name": "job-dev-1",
  "user_id": 456,
  "wiki_id": 123
}
```

**分配逻辑**:
1. 检查用户是否已分配job，如果已分配则直接返回
2. 使用分布式锁防止并发冲突
3. 从可用池中原子性地获取一个job
4. 记录分配关系到Redis
5. 如果池中无可用job，返回"Job额度已用完"

### 3. 获取用户已分配的Job

**接口**: `GET /api/linux/group/user/job?wiki_id=123`

**参数说明**:
- `wiki_id`: Wiki ID（必填，查询参数）

**响应示例**:
```json
{
  "success": true,
  "job_name": "job-dev-1",
  "user_id": 456,
  "wiki_id": 123
}
```

### 4. 释放Job

**接口**: `POST /api/linux/group/release`

**请求参数**:
```json
{
  "wiki_id": 123
}
```

**参数说明**:
- `wiki_id`: Wiki ID（必填）

**响应示例**:
```json
{
  "success": true,
  "message": "Job释放成功"
}
```

**释放逻辑**:
1. 查找用户已分配的job
2. 删除Redis中的分配记录
3. 清除job的分配信息
4. 将job放回可用池

### 5. 查询Job池状态

**接口**: `GET /api/linux/group/pool/status`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "available_count": 30,
    "allocated_count": 20,
    "total_count": 50
  }
}
```

**状态说明**:
- `available_count`: 可用job数量
- `allocated_count`: 已分配job数量
- `total_count`: 总job数量

## 使用流程

### 1. 系统启动时初始化Job池

```bash
curl -X POST http://localhost:8000/api/linux/group/batch/jobs \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "job_number": 50,
    "wct_api_key": "your_api_key",
    "start_id": 1
  }'
```

### 2. 用户点击Wiki进行问答时分配Job

```bash
curl -X POST http://localhost:8000/api/linux/group/bind \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "wiki_id": 123
  }'
```

### 3. 查询用户已分配的Job

```bash
curl -X GET "http://localhost:8000/api/linux/group/user/job?wiki_id=123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 4. 释放不再使用的Job

```bash
curl -X POST http://localhost:8000/api/linux/group/release \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "wiki_id": 123
  }'
```

### 5. 监控Job池状态

```bash
curl -X GET http://localhost:8000/api/linux/group/pool/status \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 并发场景处理

### 场景1: 多个用户同时请求分配Job

**处理方式**:
1. 使用Redis分布式锁，确保同一时刻只有一个请求在处理
2. SPOP原子操作保证每个job只被分配一次
3. 双重检查机制防止重复分配

### 场景2: 用户重复点击Wiki

**处理方式**:
1. 首先检查Redis中是否已有分配记录
2. 如果已分配，直接返回已分配的job
3. 避免重复分配和资源浪费

### 场景3: Job池耗尽

**处理方式**:
1. 从可用池中获取job时，如果返回None
2. 返回错误信息："Job额度已用完，请稍后再试"
3. 建议实现Job自动回收机制或扩容策略

## 性能优化

### Redis操作优化

1. **使用Set数据结构**
   - SPOP操作时间复杂度O(1)
   - SISMEMBER检查存在性O(1)

2. **使用Hash存储详细信息**
   - 避免大量独立key
   - 减少内存碎片

3. **分布式锁超时设置**
   - 10秒超时防止死锁
   - 自动释放避免资源占用

### 并发性能

- 支持高并发job分配请求
- Redis单线程模型保证原子性
- 分布式锁粒度细化到user_id:wiki_id级别

## 注意事项

1. **Redis连接**
   - 确保Redis服务正常运行
   - 配置合适的连接池大小

2. **Job命名冲突**
   - 不同环境使用不同的environment标识
   - 确保job_id的唯一性

3. **资源清理**
   - 定期检查并清理长时间未使用的job
   - 实现Job自动回收机制

4. **监控告警**
   - 监控可用job数量
   - 当可用数量低于阈值时告警
   - 及时扩容或回收资源

## 故障处理

### Redis连接失败

**现象**: 接口返回"Redis未连接"

**处理**:
1. 检查Redis服务状态
2. 检查网络连接
3. 检查Redis配置

### Job分配失败

**现象**: 返回"Job额度已用完"

**处理**:
1. 查询job池状态
2. 释放不再使用的job
3. 批量创建新的job

### 分布式锁超时

**现象**: 返回"正在分配中，请稍后重试"

**处理**:
1. 等待10秒后重试
2. 检查是否有死锁情况
3. 必要时手动清理锁

## 扩展功能

### 1. Job自动回收

可以实现定时任务，自动回收长时间未使用的job：

```python
# 伪代码
def auto_recycle_jobs():
    # 获取所有已分配的job
    # 检查最后使用时间
    # 超过阈值的job自动释放
    pass
```

### 2. Job池动态扩容

当可用job数量低于阈值时自动扩容：

```python
# 伪代码
def auto_scale_pool():
    status = get_pool_status()
    if status['available_count'] < threshold:
        batch_create_jobs(count=scale_size)
```

### 3. Job使用统计

记录job的使用情况，用于分析和优化：

```python
# 伪代码
def record_job_usage():
    # 记录分配时间
    # 记录使用时长
    # 生成统计报表
    pass
