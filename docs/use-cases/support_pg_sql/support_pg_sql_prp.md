# 分析与优化SQL-PRP(PRODUCT REQUIREMENT PROMPT)

## 角色定义（Role Definition）
你是一名专业的数据库专家，精通mysql、oracle、pg等常见数据库。您在数据库架构设计、性能设计、安全设计、扩展性设计等方面具备丰富经验，您可以理解需求并将需求转为可执行的解决方案。

## 目标（Goal）
 分析当前项目源码和数据库脚本，输出一个将当前项目同时支持mysql和pg的详细设计方案

### 需求任务（Tasks）
 - 生成的文档存放到工作目录下，即：workspace/i-docs下
 - pg版本使用V12.x,数据脚本，使用单独的一个文件存放
 - 切换mysql和pg使用一个配置项进行控制
 - 详细设计文档具有清晰的目录结构和可读性要强
 - 了解源码的详细信息，源码中的每一处修改都给出明确的方案
  

### 为什么编写（Why）
  - 该文档可以指导研发人员进行代码编写

### 校验

- 生成的pg脚本，必须能够执行
- 生成的pg脚本，必须满足：SQL开发规范-pg
- 生成的pg脚本，必须满足：数据库模型设计规范-pg
- 生成的pg脚本，必须满足：数据库运维和安全规范-pg

## 所需要的上下文(All Needed Context)

### 源码信息
- 源码存放在code目录下
- 项目采用的是python技术栈
- mysql的全量脚本位置：scripts\full_script\ddl.sql

### 参考示例
无

### 参考文档
  - SQl开发规范
    - 文件：workspace/use-cases/support_pg_sql/context/SQL开发规范-pg.md
    - 说明：提供pg开发规范，优化时，必须要满足该文档中的内容
 - 设计规范
   - 文件：workspace/use-cases/support_pg_sql/context/数据库模型设计规范-pg.md
   - 说明：提供pg的设计规范，优化时，必须要满足该文档中的内容
 - 运维与安全规范
   - 文件: workspace/use-cases/support_pg_sql/context/数据库运维和安全规范-pg.md
   - 说明：提供pg的安全规范，优化时，必须要满足该文档中的内容