-- 自动更新 "updated_time" 或类似字段的函数
CREATE OR REPLACE FUNCTION update_timestamp_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.update_date = NOW(); 
   RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION update_updated_time_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_time = NOW();
   RETURN NEW;
END;
$$ language 'plpgsql';

-- deepwiki公告表
CREATE TABLE "ai_dw_announce" (
  "id" SERIAL PRIMARY KEY,
  "title" varchar(255) NOT NULL,
  "type" varchar(30) NOT NULL DEFAULT 'link',
  "content" varchar(1000) NOT NULL,
  "seq" smallint NOT NULL,
  "state" smallint NOT NULL,
  "created_by" bigint NOT NULL,
  "created_date" timestamp NOT NULL,
  "update_by" bigint,
  "update_date" timestamp
);

COMMENT ON TABLE "ai_dw_announce" IS 'deepwiki公告表';
COMMENT ON COLUMN "ai_dw_announce"."id" IS '公告表主键标识';
COMMENT ON COLUMN "ai_dw_announce"."title" IS '公告标题';
COMMENT ON COLUMN "ai_dw_announce"."type" IS '公告类型: link超链接模式';
COMMENT ON COLUMN "ai_dw_announce"."content" IS '公告内容,如链接地址';
COMMENT ON COLUMN "ai_dw_announce"."seq" IS '公告排列顺序,从1开始,依次加1';
COMMENT ON COLUMN "ai_dw_announce"."state" IS '公告状态,1:上架 2:下架';
COMMENT ON COLUMN "ai_dw_announce"."created_by" IS '公告记录创建者';
COMMENT ON COLUMN "ai_dw_announce"."created_date" IS '公告记录创建时间';
COMMENT ON COLUMN "ai_dw_announce"."update_by" IS '公告记录更新者';
COMMENT ON COLUMN "ai_dw_announce"."update_date" IS '公告记录更新时间';

-- deepwiki角色表
CREATE TABLE "ai_dw_role" (
  "id" SMALLSERIAL PRIMARY KEY,
  "role_name" varchar(60) NOT NULL,
  "role_code" varchar(30) NOT NULL,
  "role_type" char(1) NOT NULL,
  "access_level" smallint NOT NULL,
  "comments" varchar(255)
);

COMMENT ON TABLE "ai_dw_role" IS 'deepwiki角色表';
COMMENT ON COLUMN "ai_dw_role"."id" IS '角色标识';
COMMENT ON COLUMN "ai_dw_role"."role_name" IS '角色名称';
COMMENT ON COLUMN "ai_dw_role"."role_code" IS '角色编码';
COMMENT ON COLUMN "ai_dw_role"."role_type" IS '角色类型 S:系统角色 D:数据角色';
COMMENT ON COLUMN "ai_dw_role"."access_level" IS '权限级别: 数值越大,权限越大';
COMMENT ON COLUMN "ai_dw_role"."comments" IS '角色描述';

-- 角色服务权限
CREATE TABLE "ai_dw_role_priv" (
  "id" SERIAL PRIMARY KEY,
  "role_id" smallint NOT NULL,
  "priv_el" varchar(1000) NOT NULL,
  CONSTRAINT "ux_adrp_rid_pel" UNIQUE ("role_id", "priv_el")
);

COMMENT ON TABLE "ai_dw_role_priv" IS '角色服务权限';
COMMENT ON COLUMN "ai_dw_role_priv"."id" IS '角色权限唯一标识';
COMMENT ON COLUMN "ai_dw_role_priv"."role_id" IS '角色标识';
COMMENT ON COLUMN "ai_dw_role_priv"."priv_el" IS '角色服务权限el表达式';

-- deepwiki用户表
CREATE TABLE "ai_dw_user" (
  "id" BIGSERIAL PRIMARY KEY,
  "wcp_user_id" bigint NOT NULL,
  "user_code" varchar(30) NOT NULL,
  "user_name" varchar(255) NOT NULL,
  "email" varchar(255),
  "phone" varchar(30),
  "dept" varchar(120),
  "org" varchar(100),
  "job" varchar(120),
  "dept_id" int,
  "org_id" int,
  "job_id" int,
  "state" smallint NOT NULL DEFAULT 1,
  "last_login_date" timestamp,
  "created_by" bigint,
  "created_date" timestamp,
  "update_by" bigint,
  "update_date" timestamp,
  CONSTRAINT "idx_adu_wcp_user_id" UNIQUE ("wcp_user_id"),
  CONSTRAINT "idx_adu_user_code" UNIQUE ("user_code")
);

COMMENT ON TABLE "ai_dw_user" IS 'deepwiki用户表';
COMMENT ON COLUMN "ai_dw_user"."id" IS '用户表主键';
COMMENT ON COLUMN "ai_dw_user"."wcp_user_id" IS '鲸加用户标识';
COMMENT ON COLUMN "ai_dw_user"."user_code" IS '用户鲸加工号';
COMMENT ON COLUMN "ai_dw_user"."user_name" IS '用户姓名';
COMMENT ON COLUMN "ai_dw_user"."email" IS '用户邮箱';
COMMENT ON COLUMN "ai_dw_user"."phone" IS '用户电话号码';
COMMENT ON COLUMN "ai_dw_user"."dept" IS '用户隶属部门';
COMMENT ON COLUMN "ai_dw_user"."org" IS '用户隶属组织';
COMMENT ON COLUMN "ai_dw_user"."job" IS '用户职位';
COMMENT ON COLUMN "ai_dw_user"."dept_id" IS '用户隶属部门标识';
COMMENT ON COLUMN "ai_dw_user"."org_id" IS '用户隶属组织标识';
COMMENT ON COLUMN "ai_dw_user"."job_id" IS '用户职位标识';
COMMENT ON COLUMN "ai_dw_user"."state" IS '用户是否有效,1:有效 0:无效';
COMMENT ON COLUMN "ai_dw_user"."last_login_date" IS '上次登录时间';
COMMENT ON COLUMN "ai_dw_user"."created_by" IS '记录创建者';
COMMENT ON COLUMN "ai_dw_user"."created_date" IS '记录创建时间';
COMMENT ON COLUMN "ai_dw_user"."update_by" IS '记录更新者';
COMMENT ON COLUMN "ai_dw_user"."update_date" IS '记录更新时间';


-- 用户信息扩展表
CREATE TABLE "ai_dw_user_ext" (
  "id" BIGSERIAL PRIMARY KEY,
  "user_id" bigint NOT NULL,
  "ai_api_key" varchar(255),
  "dev_cloud_token" varchar(255),
  "sandbox_quota" int,
  "created_by" bigint,
  "created_date" timestamp,
  "update_by" bigint,
  "update_date" timestamp,
  CONSTRAINT "ux_ai_dw_user_ext_user_id" UNIQUE ("user_id")
);

COMMENT ON TABLE "ai_dw_user_ext" IS '用户信息扩展表';
COMMENT ON COLUMN "ai_dw_user_ext"."id" IS '主键标识';
COMMENT ON COLUMN "ai_dw_user_ext"."user_id" IS '用户标识';
COMMENT ON COLUMN "ai_dw_user_ext"."ai_api_key" IS '公司大模型token';
COMMENT ON COLUMN "ai_dw_user_ext"."dev_cloud_token" IS '研发云token';
COMMENT ON COLUMN "ai_dw_user_ext"."sandbox_quota" IS '个性化沙盒并发配额';
COMMENT ON COLUMN "ai_dw_user_ext"."created_by" IS '创建者';
COMMENT ON COLUMN "ai_dw_user_ext"."created_date" IS '记录创建时间';
COMMENT ON COLUMN "ai_dw_user_ext"."update_by" IS '记录更新者';
COMMENT ON COLUMN "ai_dw_user_ext"."update_date" IS '记录更新时间';

-- 用户角色关联关系表
CREATE TABLE "ai_dw_user_role" (
  "id" BIGSERIAL PRIMARY KEY,
  "user_id" bigint NOT NULL,
  "role_id" smallint NOT NULL,
  CONSTRAINT "ux_ai_dw_user_role_uid_rid" UNIQUE ("user_id", "role_id")
);

COMMENT ON TABLE "ai_dw_user_role" IS '用户角色关联关系表';
COMMENT ON COLUMN "ai_dw_user_role"."id" IS '用户角色标识';
COMMENT ON COLUMN "ai_dw_user_role"."user_id" IS '用户标识';
COMMENT ON COLUMN "ai_dw_user_role"."role_id" IS '角色标识';

-- 会话聊天历史表
CREATE TABLE "ai_dw_chat_history" (
  "id" BIGSERIAL PRIMARY KEY,
  "msg_sid" varchar(63),
  "chat_id" bigint NOT NULL,
  "role" varchar(30) NOT NULL DEFAULT '',
  "content" text,
  "provider" varchar(60) NOT NULL,
  "model" varchar(255),
  "msg_data" varchar(1000) DEFAULT '',
  "deep_research" smallint DEFAULT 0,
  "deep_research_iter" varchar(30),
  "parent_id" bigint,
  "tool_calls" text,
  "state" smallint NOT NULL DEFAULT 1,
  "qa_src" smallint NOT NULL DEFAULT 1,
  "error_code" varchar(30),
  "created_by" bigint,
  "created_date" timestamp NOT NULL,
  "update_by" bigint,
  "update_date" timestamp
);
CREATE INDEX "idx_adch_msg_sid" ON "ai_dw_chat_history" ("msg_sid");
CREATE INDEX "idx_adch_chat_id" ON "ai_dw_chat_history" ("chat_id");

COMMENT ON TABLE "ai_dw_chat_history" IS '会话聊天历史表';
COMMENT ON COLUMN "ai_dw_chat_history"."id" IS '主键,自增1';
COMMENT ON COLUMN "ai_dw_chat_history"."msg_sid" IS '大模型生成的id';
COMMENT ON COLUMN "ai_dw_chat_history"."chat_id" IS '会话id';
COMMENT ON COLUMN "ai_dw_chat_history"."role" IS '角色';
COMMENT ON COLUMN "ai_dw_chat_history"."content" IS '消息内容';
COMMENT ON COLUMN "ai_dw_chat_history"."provider" IS '问答提供商';
COMMENT ON COLUMN "ai_dw_chat_history"."model" IS 'ai大模型';
COMMENT ON COLUMN "ai_dw_chat_history"."msg_data" IS 'json格式的额外数据';
COMMENT ON COLUMN "ai_dw_chat_history"."deep_research" IS '是否启用深度搜索,0:否 1:是';
COMMENT ON COLUMN "ai_dw_chat_history"."deep_research_iter" IS '深度搜索迭代步骤标识 第一条:start 最后一条:end';
COMMENT ON COLUMN "ai_dw_chat_history"."parent_id" IS '助手消息关联的用户消息主键';
COMMENT ON COLUMN "ai_dw_chat_history"."tool_calls" IS '大模型工具调用信息';
COMMENT ON COLUMN "ai_dw_chat_history"."state" IS '0:失效 1:有效';
COMMENT ON COLUMN "ai_dw_chat_history"."qa_src" IS '问答来源 1: deepwiki 2: mcp tool';
COMMENT ON COLUMN "ai_dw_chat_history"."error_code" IS '问答错误码';
COMMENT ON COLUMN "ai_dw_chat_history"."created_by" IS '消息归属者';
COMMENT ON COLUMN "ai_dw_chat_history"."created_date" IS '生成时间';
COMMENT ON COLUMN "ai_dw_chat_history"."update_by" IS '消息更新者';
COMMENT ON COLUMN "ai_dw_chat_history"."update_date" IS '更新时间';


-- 会话表
CREATE TABLE "ai_dw_chat_session" (
  "id" BIGSERIAL PRIMARY KEY,
  "chat_sid" varchar(60) NOT NULL,
  "title" varchar(60),
  "wiki_id" varchar(32) NOT NULL DEFAULT '',
  "ip" char(15) DEFAULT '',
  "state" smallint NOT NULL DEFAULT 1,
  "created_by" bigint NOT NULL,
  "created_date" timestamp NOT NULL,
  "update_by" bigint,
  "update_date" timestamp,
  CONSTRAINT "idx_chat_sid" UNIQUE ("chat_sid")
);
CREATE INDEX "idx_adcs_cid_wid" ON "ai_dw_chat_session" ("created_by", "wiki_id");

COMMENT ON TABLE "ai_dw_chat_session" IS '会话表';
COMMENT ON COLUMN "ai_dw_chat_session"."id" IS '主键,自增1';
COMMENT ON COLUMN "ai_dw_chat_session"."chat_sid" IS '前端使用的会话id';
COMMENT ON COLUMN "ai_dw_chat_session"."title" IS '会话标题';
COMMENT ON COLUMN "ai_dw_chat_session"."wiki_id" IS 'wiki标识';
COMMENT ON COLUMN "ai_dw_chat_session"."ip" IS '用户所在ip';
COMMENT ON COLUMN "ai_dw_chat_session"."state" IS '0/1/2,删除/创建但无消息/创建已存在消息';
COMMENT ON COLUMN "ai_dw_chat_session"."created_by" IS '记录创建者';
COMMENT ON COLUMN "ai_dw_chat_session"."created_date" IS '创建时间';
COMMENT ON COLUMN "ai_dw_chat_session"."update_by" IS '记录更新者';
COMMENT ON COLUMN "ai_dw_chat_session"."update_date" IS '更新时间';


-- 用户反馈表
CREATE TABLE "ai_dw_chat_user_feedback" (
  "id" BIGSERIAL PRIMARY KEY,
  "msg_sid" varchar(63),
  "evaluate_type" smallint DEFAULT 0,
  "feedback" varchar(255),
  "category" varchar(255),
  "created_by" bigint NOT NULL,
  "created_date" timestamp NOT NULL,
  "update_by" bigint,
  "update_date" timestamp
);
CREATE INDEX "idx_msg_sid" ON "ai_dw_chat_user_feedback" ("msg_sid");

COMMENT ON TABLE "ai_dw_chat_user_feedback" IS '用户反馈表';
COMMENT ON COLUMN "ai_dw_chat_user_feedback"."id" IS '主键,自增1';
COMMENT ON COLUMN "ai_dw_chat_user_feedback"."msg_sid" IS '大模型生成的id';
COMMENT ON COLUMN "ai_dw_chat_user_feedback"."evaluate_type" IS '0:NEUTRAL,GOOD:1,BAD:2';
COMMENT ON COLUMN "ai_dw_chat_user_feedback"."feedback" IS '用户反馈';
COMMENT ON COLUMN "ai_dw_chat_user_feedback"."category" IS '点踩时用户选择的选项,多个逗号分隔';
COMMENT ON COLUMN "ai_dw_chat_user_feedback"."created_by" IS '记录创建者';
COMMENT ON COLUMN "ai_dw_chat_user_feedback"."created_date" IS '创建时间';
COMMENT ON COLUMN "ai_dw_chat_user_feedback"."update_by" IS '记录更新者';
COMMENT ON COLUMN "ai_dw_chat_user_feedback"."update_date" IS '更新时间';


-- 挂起的任务
CREATE TABLE "global_pending_tasks" (
  "task_id" varchar(100) PRIMARY KEY,
  "job_id" varchar(100) NOT NULL,
  "priority" int NOT NULL DEFAULT 3,
  "preferred_instance_id" varchar(100),
  "job_context_json" jsonb NOT NULL,
  "submit_time" timestamp DEFAULT CURRENT_TIMESTAMP,
  "retry_count" int DEFAULT 0,
  "last_retry_time" timestamp
);
CREATE INDEX "idx_priority_submit_time" ON "global_pending_tasks" ("priority", "submit_time");

COMMENT ON TABLE "global_pending_tasks" IS '挂起的任务';


-- JobManager实例注册表,用于分布式部署管理
CREATE TABLE "job_manager_instance" (
  "instance_id" varchar(100) PRIMARY KEY,
  "hostname" varchar(255) NOT NULL,
  "pid" int NOT NULL,
  "status" varchar(20) NOT NULL DEFAULT 'active',
  "max_concurrent_jobs" int NOT NULL DEFAULT 3,
  "current_jobs" int NOT NULL DEFAULT 0,
  "last_heartbeat" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "created_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX "idx_instance_last_heartbeat" ON "job_manager_instance" ("last_heartbeat");
CREATE INDEX "idx_instance_status" ON "job_manager_instance" ("status");
CREATE INDEX "idx_hostname_pid" ON "job_manager_instance" ("hostname", "pid");
CREATE INDEX "idx_instance_status_heartbeat" ON "job_manager_instance" ("status", "last_heartbeat");

COMMENT ON TABLE "job_manager_instance" IS 'JobManager实例注册表,用于分布式部署管理';
COMMENT ON COLUMN "job_manager_instance"."instance_id" IS '实例ID,UUID格式';
COMMENT ON COLUMN "job_manager_instance"."hostname" IS '主机名';
COMMENT ON COLUMN "job_manager_instance"."pid" IS '进程ID';
COMMENT ON COLUMN "job_manager_instance"."status" IS '实例状态：active(活跃), stopped(已停止), crashed(崩溃)';
COMMENT ON COLUMN "job_manager_instance"."max_concurrent_jobs" IS '最大并发任务数';
COMMENT ON COLUMN "job_manager_instance"."current_jobs" IS '当前处理的任务数';
COMMENT ON COLUMN "job_manager_instance"."last_heartbeat" IS '最后心跳时间';
COMMENT ON COLUMN "job_manager_instance"."created_time" IS '创建时间';
COMMENT ON COLUMN "job_manager_instance"."updated_time" IS '更新时间';

CREATE TRIGGER "trg_job_manager_instance_updated_time"
BEFORE UPDATE ON "job_manager_instance"
FOR EACH ROW
EXECUTE FUNCTION update_updated_time_column();


-- Wiki信息表
CREATE TABLE "wiki_info" (
  "id" SERIAL PRIMARY KEY,
  "wiki_id" varchar(255) NOT NULL,
  "repo_url" varchar(255) NOT NULL,
  "branch" varchar(255) NOT NULL DEFAULT 'master',
  "repo_owner" varchar(255) NOT NULL,
  "repo_name" varchar(255) NOT NULL,
  "repo_type" varchar(50) NOT NULL DEFAULT 'whaleDevCloud',
  "topic_id" varchar(255),
  "topic_id_code" varchar(255),
  "topic_id_doc" varchar(255),
  "provider" varchar(255) NOT NULL DEFAULT 'google',
  "model" varchar(255) NOT NULL DEFAULT 'gemini-pro',
  "language" varchar(255) NOT NULL DEFAULT 'zh',
  "excluded_dirs" text,
  "excluded_files" text,
  "included_dirs" text,
  "included_files" text,
  "comprehensive" boolean NOT NULL DEFAULT TRUE,
  "created_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "created_by" bigint NOT NULL DEFAULT 0,
  "updated_by" bigint NOT NULL DEFAULT 0,
  "owner_id" bigint NOT NULL,
  "status" varchar(255) NOT NULL DEFAULT 'pending',
  "error_message" text,
  "wiki_data" jsonb,
  "sub_repos" text,
  "visibility" smallint NOT NULL DEFAULT 2,
  "comments" varchar(255),
  "type" smallint NOT NULL DEFAULT 1,
  CONSTRAINT "uix_wiki_info_repo_branch" UNIQUE ("repo_url", "branch")
);
CREATE INDEX "idx_wiki_info_owner_id" ON "wiki_info" ("owner_id");

COMMENT ON TABLE "wiki_info" IS 'Wiki信息表';
COMMENT ON COLUMN "wiki_info"."id" IS '主键ID';
COMMENT ON COLUMN "wiki_info"."wiki_id" IS 'Wiki唯一标识';
COMMENT ON COLUMN "wiki_info"."repo_url" IS 'Git仓库路径';
COMMENT ON COLUMN "wiki_info"."branch" IS 'Git分支';
COMMENT ON COLUMN "wiki_info"."repo_owner" IS '仓库所有者';
COMMENT ON COLUMN "wiki_info"."repo_name" IS '仓库名称';
COMMENT ON COLUMN "wiki_info"."repo_type" IS '仓库类型(github、gitlab等)';
COMMENT ON COLUMN "wiki_info"."topic_id" IS '公司大模型团队DocChain主题ID';
COMMENT ON COLUMN "wiki_info"."topic_id_code" IS 'DocChain代码主题ID';
COMMENT ON COLUMN "wiki_info"."topic_id_doc" IS 'DocChain文档主题ID 废弃';
COMMENT ON COLUMN "wiki_info"."provider" IS '模型提供商';
COMMENT ON COLUMN "wiki_info"."model" IS '模型名称';
COMMENT ON COLUMN "wiki_info"."language" IS '语言';
COMMENT ON COLUMN "wiki_info"."excluded_dirs" IS '排除的目录 (使用 TEXT 存储,逗号分隔)';
COMMENT ON COLUMN "wiki_info"."excluded_files" IS '排除的文件 (使用 TEXT 存储,逗号分隔)';
COMMENT ON COLUMN "wiki_info"."included_dirs" IS '包含的目录 (使用 TEXT 存储,逗号分隔)';
COMMENT ON COLUMN "wiki_info"."included_files" IS '包含的文件 (使用 TEXT 存储,逗号分隔)';
COMMENT ON COLUMN "wiki_info"."comprehensive" IS '是否生成全面Wiki';
COMMENT ON COLUMN "wiki_info"."created_time" IS '创建时间';
COMMENT ON COLUMN "wiki_info"."updated_time" IS '更新时间';
COMMENT ON COLUMN "wiki_info"."created_by" IS '创建人ID';
COMMENT ON COLUMN "wiki_info"."updated_by" IS '更新人ID';
COMMENT ON COLUMN "wiki_info"."owner_id" IS 'wiki拥有者ID';
COMMENT ON COLUMN "wiki_info"."status" IS '状态：pending、processing、completed、failed';
COMMENT ON COLUMN "wiki_info"."error_message" IS '错误信息';
COMMENT ON COLUMN "wiki_info"."wiki_data" IS 'Wiki数据,包含结构和页面内容 (使用 JSON 或 TEXT)';
COMMENT ON COLUMN "wiki_info"."sub_repos" IS '子仓库信息[{"url":"","branch":""}]';
COMMENT ON COLUMN "wiki_info"."visibility" IS 'wiki可见性, 1:全员可见 2: 未授权其他人则仅创建人可见';
COMMENT ON COLUMN "wiki_info"."comments" IS '仓库描述';
COMMENT ON COLUMN "wiki_info"."type" IS 'wiki类型: 1:产品  2:项目';


-- Wiki生成任务表
CREATE TABLE "wiki_job" (
  "id" varchar(36) PRIMARY KEY,
  "status" varchar(50) NOT NULL DEFAULT 'pending',
  "stage" varchar(50),
  "stage_progress" int DEFAULT 0,
  "stage_message" varchar(255),
  "progress" int DEFAULT 0,
  "total_files" int,
  "processed_files" int,
  "error_message" text,
  "repo_url" varchar(255) NOT NULL,
  "branch" varchar(100) DEFAULT 'main',
  "token" varchar(255),
  "language" varchar(10) DEFAULT 'zh',
  "comprehensive" boolean DEFAULT TRUE,
  "excluded_dirs" text,
  "excluded_files" text,
  "included_dirs" text,
  "included_files" text,
  "topic_id" varchar(100),
  "topic_id_code" varchar(100),
  "topic_id_doc" varchar(100),
  "wiki_info_id" varchar(36),
  "model_settings" jsonb,
  "result" jsonb,
  "created_by" bigint NOT NULL DEFAULT 0,
  "updated_by" bigint NOT NULL DEFAULT 0,
  "created_time" timestamp DEFAULT CURRENT_TIMESTAMP,
  "updated_time" timestamp DEFAULT CURRENT_TIMESTAMP,
  "sub_repos" varchar(255)
);
CREATE INDEX "idx_status" ON "wiki_job" ("status");
CREATE INDEX "idx_repo_url" ON "wiki_job" ("repo_url");
CREATE INDEX "idx_created_time" ON "wiki_job" ("created_time");

COMMENT ON TABLE "wiki_job" IS 'Wiki生成任务表';
COMMENT ON COLUMN "wiki_job"."id" IS '任务ID,UUID格式';
COMMENT ON COLUMN "wiki_job"."status" IS '任务状态：pending(待处理), pending_resume(等待恢复), processing(处理中), resuming(恢复中), completed(已完成), failed(失败), cancelled(已取消), timeout(超时)';
COMMENT ON COLUMN "wiki_job"."stage" IS '任务阶段：init(初始化), download(下载), upload(上传), structure(结构生成), pages(页面生成), completed(已完成)';
COMMENT ON COLUMN "wiki_job"."stage_progress" IS '当前阶段的进度百分比(0-100)';
COMMENT ON COLUMN "wiki_job"."stage_message" IS '当前阶段的状态信息';
COMMENT ON COLUMN "wiki_job"."progress" IS '整体进度百分比(0-100)';
COMMENT ON COLUMN "wiki_job"."total_files" IS '总文件数';
COMMENT ON COLUMN "wiki_job"."processed_files" IS '已处理文件数';
COMMENT ON COLUMN "wiki_job"."error_message" IS '错误信息';
COMMENT ON COLUMN "wiki_job"."repo_url" IS '代码仓库URL';
COMMENT ON COLUMN "wiki_job"."branch" IS '代码仓库分支';
COMMENT ON COLUMN "wiki_job"."token" IS '访问令牌(如果需要认证)';
COMMENT ON COLUMN "wiki_job"."language" IS 'Wiki语言';
COMMENT ON COLUMN "wiki_job"."comprehensive" IS '是否生成全面的Wiki(1:是,0:否)';
COMMENT ON COLUMN "wiki_job"."excluded_dirs" IS '排除的目录列表,逗号分隔';
COMMENT ON COLUMN "wiki_job"."excluded_files" IS '排除的文件列表,逗号分隔';
COMMENT ON COLUMN "wiki_job"."included_dirs" IS '包含的目录列表,逗号分隔';
COMMENT ON COLUMN "wiki_job"."included_files" IS '包含的文件列表,逗号分隔';
COMMENT ON COLUMN "wiki_job"."topic_id" IS '兼容旧版本的单个topic ID';
COMMENT ON COLUMN "wiki_job"."topic_id_code" IS '代码部分的topic ID';
COMMENT ON COLUMN "wiki_job"."topic_id_doc" IS '文档部分的topic ID';
COMMENT ON COLUMN "wiki_job"."wiki_info_id" IS '关联的WikiInfo记录ID';
COMMENT ON COLUMN "wiki_job"."model_settings" IS '模型配置信息,包括提供商、模型名称等';
COMMENT ON COLUMN "wiki_job"."result" IS '任务结果数据';
COMMENT ON COLUMN "wiki_job"."created_by" IS '创建人ID';
COMMENT ON COLUMN "wiki_job"."updated_by" IS '更新人ID';
COMMENT ON COLUMN "wiki_job"."created_time" IS '创建时间';
COMMENT ON COLUMN "wiki_job"."updated_time" IS '更新时间';
COMMENT ON COLUMN "wiki_job"."sub_repos" IS '子仓库信息';

CREATE TRIGGER "trg_wiki_job_updated_time"
BEFORE UPDATE ON "wiki_job"
FOR EACH ROW
EXECUTE FUNCTION update_updated_time_column();


-- 全局任务状态表,用于跨实例状态同步
CREATE TABLE "global_job_state" (
  "job_id" varchar(100) PRIMARY KEY,
  "processing_instance_id" varchar(100),
  "global_status" varchar(50) NOT NULL,
  "last_processing_instance" varchar(100),
  "job_metadata" jsonb,
  "created_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT "fk_global_job_processing_instance" FOREIGN KEY ("processing_instance_id") REFERENCES "job_manager_instance" ("instance_id") ON DELETE SET NULL,
  CONSTRAINT "fk_global_job_last_instance" FOREIGN KEY ("last_processing_instance") REFERENCES "job_manager_instance" ("instance_id") ON DELETE SET NULL
);
CREATE INDEX "idx_global_job_state_status" ON "global_job_state" ("global_status");
CREATE INDEX "idx_processing_instance" ON "global_job_state" ("processing_instance_id");
CREATE INDEX "idx_global_status_instance" ON "global_job_state" ("global_status", "processing_instance_id");

COMMENT ON TABLE "global_job_state" IS '全局任务状态表,用于跨实例状态同步';
COMMENT ON COLUMN "global_job_state"."job_id" IS '任务ID';
COMMENT ON COLUMN "global_job_state"."processing_instance_id" IS '当前处理该任务的实例ID';
COMMENT ON COLUMN "global_job_state"."global_status" IS '全局状态：available(可用), locked(已锁定), processing(处理中), completed(已完成), failed(失败)';
COMMENT ON COLUMN "global_job_state"."last_processing_instance" IS '最后处理该任务的实例ID';
COMMENT ON COLUMN "global_job_state"."job_metadata" IS '元数据,JSON格式';
COMMENT ON COLUMN "global_job_state"."created_time" IS '创建时间';
COMMENT ON COLUMN "global_job_state"."updated_time" IS '更新时间';

CREATE TRIGGER "trg_global_job_state_updated_time"
BEFORE UPDATE ON "global_job_state"
FOR EACH ROW
EXECUTE FUNCTION update_updated_time_column();


-- 任务锁表,用于分布式任务管理
CREATE TABLE "job_lock" (
  "job_id" varchar(100) PRIMARY KEY,
  "instance_id" varchar(100) NOT NULL,
  "hostname" varchar(255) NOT NULL,
  "operation" varchar(50) NOT NULL,
  "acquired_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "expires_at" timestamp NOT NULL,
  "updated_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT "fk_job_lock_instance" FOREIGN KEY ("instance_id") REFERENCES "job_manager_instance" ("instance_id") ON DELETE CASCADE
);
CREATE INDEX "idx_job_lock_expires_at" ON "job_lock" ("expires_at");
CREATE INDEX "idx_job_lock_instance_id" ON "job_lock" ("instance_id");
CREATE INDEX "idx_job_lock_hostname" ON "job_lock" ("hostname");
CREATE INDEX "idx_job_lock_expires_instance" ON "job_lock" ("expires_at", "instance_id");

COMMENT ON TABLE "job_lock" IS '任务锁表,用于分布式任务管理';
COMMENT ON COLUMN "job_lock"."job_id" IS '任务ID';
COMMENT ON COLUMN "job_lock"."instance_id" IS '持有锁的实例ID';
COMMENT ON COLUMN "job_lock"."hostname" IS '持有锁的主机名';
COMMENT ON COLUMN "job_lock"."operation" IS '操作类型：submit(提交), process(处理), recover(恢复), cancel(取消)';
COMMENT ON COLUMN "job_lock"."acquired_time" IS '获取锁的时间';
COMMENT ON COLUMN "job_lock"."expires_at" IS '锁过期时间';
COMMENT ON COLUMN "job_lock"."updated_time" IS '更新时间';

CREATE TRIGGER "trg_job_lock_updated_time"
BEFORE UPDATE ON "job_lock"
FOR EACH ROW
EXECUTE FUNCTION update_updated_time_column();


-- wiki用户角色表
CREATE TABLE "ai_dw_wiki_user_role" (
  "id" BIGSERIAL PRIMARY KEY,
  "wiki_id" bigint NOT NULL,
  "user_id" bigint NOT NULL,
  "role_id" bigint NOT NULL,
  "created_by" bigint NOT NULL,
  "created_date" timestamp NOT NULL,
  "update_by" bigint,
  "update_date" timestamp,
  CONSTRAINT "ux_adwur_uid_wid_rid" UNIQUE ("user_id", "wiki_id", "role_id")
);

COMMENT ON TABLE "ai_dw_wiki_user_role" IS 'wiki用户角色表';
COMMENT ON COLUMN "ai_dw_wiki_user_role"."id" IS 'wiki-user-role主键';
COMMENT ON COLUMN "ai_dw_wiki_user_role"."wiki_id" IS 'wiki标识';
COMMENT ON COLUMN "ai_dw_wiki_user_role"."user_id" IS '用户标识';
COMMENT ON COLUMN "ai_dw_wiki_user_role"."role_id" IS '角色标识';
COMMENT ON COLUMN "ai_dw_wiki_user_role"."created_by" IS '记录创建者';
COMMENT ON COLUMN "ai_dw_wiki_user_role"."created_date" IS '记录创建时间';
COMMENT ON COLUMN "ai_dw_wiki_user_role"."update_by" IS '记录更新者';
COMMENT ON COLUMN "ai_dw_wiki_user_role"."update_date" IS '记录更新时间';

-- deepwiki权限表
CREATE TABLE "ai_dw_priv" (
  "id" SERIAL PRIMARY KEY,
  "priv_type" char(1) NOT NULL,
  "priv_code" varchar(30) NOT NULL,
  "priv_name" varchar(120) NOT NULL,
  "priv_el" varchar(1000),
  "state" smallint NOT NULL,
  "created_by" bigint NOT NULL,
  "created_date" timestamp NOT NULL,
  "update_by" bigint,
  "update_date" timestamp
);

COMMENT ON TABLE "ai_dw_priv" IS 'deepwiki权限表';
COMMENT ON COLUMN "ai_dw_priv"."id" IS '权限主键标识';
COMMENT ON COLUMN "ai_dw_priv"."priv_type" IS '权限类型,S:服务权限,C:组件权限';
COMMENT ON COLUMN "ai_dw_priv"."priv_code" IS '权限编码';
COMMENT ON COLUMN "ai_dw_priv"."priv_name" IS '权限名称';
COMMENT ON COLUMN "ai_dw_priv"."priv_el" IS '权限表达式';
COMMENT ON COLUMN "ai_dw_priv"."state" IS '权限状态:0: 失效 1: 有效 ';
COMMENT ON COLUMN "ai_dw_priv"."created_by" IS '记录创建者';
COMMENT ON COLUMN "ai_dw_priv"."created_date" IS '记录创建日期';
COMMENT ON COLUMN "ai_dw_priv"."update_by" IS '记录更新者';
COMMENT ON COLUMN "ai_dw_priv"."update_date" IS '记录更新日期';

-- 系统服务操作日志表
CREATE TABLE "ai_dw_serv_log" (
  "id" BIGSERIAL PRIMARY KEY,
  "event_src" varchar(60) NOT NULL DEFAULT 'deepwiki',
  "event_type" varchar(255) NOT NULL,
  "event_code" varchar(255) NOT NULL,
  "party_type" varchar(30) NOT NULL,
  "party_code" varchar(255),
  "party_name" varchar(60),
  "party_id" varchar(60) NOT NULL,
  "oper_id" bigint NOT NULL,
  "dept_id" int,
  "dept_name" varchar(120),
  "oper_data" text,
  "src_ip" varchar(60),
  "server_ip" varchar(60),
  "is_success" smallint NOT NULL,
  "log_date" timestamp NOT NULL,
  "comments" varchar(1000)
);

COMMENT ON TABLE "ai_dw_serv_log" IS '系统服务操作日志表';
COMMENT ON COLUMN "ai_dw_serv_log"."id" IS '日志记录唯一标识，自增主键';
COMMENT ON COLUMN "ai_dw_serv_log"."event_src" IS '事件来源系统,默认deepwiki';
COMMENT ON COLUMN "ai_dw_serv_log"."event_type" IS '事件类型分类';
COMMENT ON COLUMN "ai_dw_serv_log"."event_code" IS '事件编码/操作编码';
COMMENT ON COLUMN "ai_dw_serv_log"."party_type" IS '被操作对象的类型(user,wiki)';
COMMENT ON COLUMN "ai_dw_serv_log"."party_code" IS '被操作对象的唯一编码';
COMMENT ON COLUMN "ai_dw_serv_log"."party_name" IS '被操作对象的名称';
COMMENT ON COLUMN "ai_dw_serv_log"."party_id" IS '被操作对象的ID(如:用户ID或者wiki id)';
COMMENT ON COLUMN "ai_dw_serv_log"."oper_id" IS '操作人ID';
COMMENT ON COLUMN "ai_dw_serv_log"."dept_id" IS '操作人所属部门ID';
COMMENT ON COLUMN "ai_dw_serv_log"."dept_name" IS '操作人所属部门名称';
COMMENT ON COLUMN "ai_dw_serv_log"."oper_data" IS '操作数据';
COMMENT ON COLUMN "ai_dw_serv_log"."src_ip" IS '来源IP地址';
COMMENT ON COLUMN "ai_dw_serv_log"."server_ip" IS '服务器IP地址';
COMMENT ON COLUMN "ai_dw_serv_log"."is_success" IS '是否成功(1成功/0失败)';
COMMENT ON COLUMN "ai_dw_serv_log"."log_date" IS '日志记录时间';
COMMENT ON COLUMN "ai_dw_serv_log"."comments" IS '操作备注/详细说明';

-- 事件编码配置表
CREATE TABLE "ai_dw_event_code" (
  "id" SERIAL PRIMARY KEY,
  "event_code" varchar(255) NOT NULL,
  "event_type" varchar(255),
  "event_src_code" varchar(60) DEFAULT 'deepwiki',
  "is_audit" smallint,
  "comments" varchar(255),
  CONSTRAINT "ux_ad_ec_event_code" UNIQUE ("event_code")
);

COMMENT ON TABLE "ai_dw_event_code" IS '事件编码配置表';
COMMENT ON COLUMN "ai_dw_event_code"."id" IS '自增主键ID';
COMMENT ON COLUMN "ai_dw_event_code"."event_code" IS '事件唯一编码';
COMMENT ON COLUMN "ai_dw_event_code"."event_type" IS '事件类型分类';
COMMENT ON COLUMN "ai_dw_event_code"."event_src_code" IS '事件来源系统编码';
COMMENT ON COLUMN "ai_dw_event_code"."is_audit" IS '是否需要审计(1是/0否)';
COMMENT ON COLUMN "ai_dw_event_code"."comments" IS '事件描述说明';

-- 标签表
CREATE TABLE "ai_dw_tag" (
  "id" BIGSERIAL PRIMARY KEY,
  "name" varchar(60) NOT NULL,
  "type" smallint NOT NULL,
  "color" varchar(16) NOT NULL,
  "comments" varchar(255),
  "module_type" smallint NOT NULL DEFAULT 1,
  "state" smallint NOT NULL DEFAULT 1,
  "created_by" bigint NOT NULL,
  "created_date" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "update_by" bigint,
  "update_date" timestamp DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT "ux_adt_created_by_name" UNIQUE ("created_by", "name")
);

COMMENT ON TABLE "ai_dw_tag" IS '标签表';
COMMENT ON COLUMN "ai_dw_tag"."id" IS '标签ID';
COMMENT ON COLUMN "ai_dw_tag"."name" IS '标签名称';
COMMENT ON COLUMN "ai_dw_tag"."type" IS '标签类型（1:系统 2:用户）';
COMMENT ON COLUMN "ai_dw_tag"."color" IS '标签颜色（如#FF0000）';
COMMENT ON COLUMN "ai_dw_tag"."comments" IS '标签描述';
COMMENT ON COLUMN "ai_dw_tag"."module_type" IS '标签归属模块, 1:deepwiki';
COMMENT ON COLUMN "ai_dw_tag"."state" IS '标签状态, 1:有效 0:失效';
COMMENT ON COLUMN "ai_dw_tag"."created_by" IS '创建人ID';
COMMENT ON COLUMN "ai_dw_tag"."created_date" IS '创建时间';
COMMENT ON COLUMN "ai_dw_tag"."update_by" IS '修改人ID';
COMMENT ON COLUMN "ai_dw_tag"."update_date" IS '更新时间';

CREATE TRIGGER "trg_ai_dw_tag_update_date"
BEFORE UPDATE ON "ai_dw_tag"
FOR EACH ROW
EXECUTE FUNCTION update_timestamp_column();

-- wiki与标签多对多关联表
CREATE TABLE "ai_dw_wiki_tag" (
  "id" BIGSERIAL PRIMARY KEY,
  "wiki_id" bigint NOT NULL,
  "tag_id" bigint NOT NULL,
  "created_by" bigint NOT NULL,
  "created_date" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "update_by" bigint,
  "update_date" timestamp DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX "idx_wiki_id" ON "ai_dw_wiki_tag" ("wiki_id");
CREATE INDEX "idx_tag_id" ON "ai_dw_wiki_tag" ("tag_id");

COMMENT ON TABLE "ai_dw_wiki_tag" IS 'wiki与标签多对多关联表';
COMMENT ON COLUMN "ai_dw_wiki_tag"."id" IS '关联关系ID';
COMMENT ON COLUMN "ai_dw_wiki_tag"."wiki_id" IS 'wiki主键ID';
COMMENT ON COLUMN "ai_dw_wiki_tag"."tag_id" IS '标签ID';
COMMENT ON COLUMN "ai_dw_wiki_tag"."created_by" IS '创建人ID';
COMMENT ON COLUMN "ai_dw_wiki_tag"."created_date" IS '创建时间';
COMMENT ON COLUMN "ai_dw_wiki_tag"."update_by" IS '更新人ID';
COMMENT ON COLUMN "ai_dw_wiki_tag"."update_date" IS '更新时间';

CREATE TRIGGER "trg_ai_dw_wiki_tag_update_date"
BEFORE UPDATE ON "ai_dw_wiki_tag"
FOR EACH ROW
EXECUTE FUNCTION update_timestamp_column();

-- wiki仓库关联的研发云扩展信息
CREATE TABLE "ai_dw_wiki_dc_ext_1" (
  "id" BIGSERIAL PRIMARY KEY,
  "wiki_id" bigint NOT NULL,
  "dc_repo_id" bigint,
  "dc_project_id" int,
  "branch_version_id" int,
  "branch_version_name" varchar(1000),
  "product_version_id" int,
  "product_version_code" varchar(1000),
  "product_name" varchar(1000),
  "product_id" bigint,
  "product_line_id" bigint,
  "product_line_name" varchar(1000),
  "created_by" bigint NOT NULL,
  "created_date" timestamp NOT NULL,
  "update_by" bigint,
  "update_date" timestamp,
  CONSTRAINT "ux_adwe_wiki_id" UNIQUE ("wiki_id")
);

COMMENT ON TABLE "ai_dw_wiki_dc_ext_1" IS 'wiki仓库关联的研发云扩展信息';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_1"."id" IS 'wiki扩展信息表主键标识';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_1"."wiki_id" IS 'wiki标识';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_1"."dc_repo_id" IS '研发云仓库ID';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_1"."dc_project_id" IS '研发云项目ID';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_1"."branch_version_id" IS '分支版本ID';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_1"."branch_version_name" IS '分支版本名称';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_1"."product_version_id" IS '产品版本ID';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_1"."product_version_code" IS '产品版本编码';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_1"."product_name" IS '产品名称';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_1"."product_id" IS '产品ID';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_1"."product_line_id" IS '产品线ID';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_1"."product_line_name" IS '产品线名称';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_1"."created_by" IS '记录创建者';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_1"."created_date" IS '创建时间';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_1"."update_by" IS '记录更新者';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_1"."update_date" IS '记录更新者';

-- wiki仓库关联的研发云扩展信息:发布包解决方案
CREATE TABLE "ai_dw_wiki_dc_ext_2" (
  "id" BIGSERIAL PRIMARY KEY,
  "wiki_id" bigint NOT NULL,
  "release_pkg_id" int,
  "release_pkg_code" varchar(1000),
  "solution_name" varchar(1000),
  "solution_id" bigint,
  "created_by" bigint NOT NULL,
  "created_date" timestamp NOT NULL,
  "update_by" bigint,
  "update_date" timestamp
);
CREATE INDEX "idx_adwdce_wiki_id" ON "ai_dw_wiki_dc_ext_2" ("wiki_id");

COMMENT ON TABLE "ai_dw_wiki_dc_ext_2" IS 'wiki仓库关联的研发云扩展信息:发布包解决方案';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_2"."id" IS 'wiki扩展信息表主键标识';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_2"."wiki_id" IS 'wiki标识';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_2"."release_pkg_id" IS '发布包ID';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_2"."release_pkg_code" IS '发布包编码';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_2"."solution_name" IS '解决方案名称';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_2"."solution_id" IS '解决方案ID';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_2"."created_by" IS '记录创建者';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_2"."created_date" IS '创建时间';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_2"."update_by" IS '记录更新者';
COMMENT ON COLUMN "ai_dw_wiki_dc_ext_2"."update_date" IS '记录更新者';

-- API接口定义表
CREATE TABLE "ai_dw_api_def" (
  "id" SERIAL PRIMARY KEY,
  "api_code" varchar(30) NOT NULL,
  "api_name" varchar(60) NOT NULL,
  "api_path" varchar(60) NOT NULL,
  "api_method" varchar(20) NOT NULL,
  "comments" varchar(255),
  "category" varchar(30),
  "state" smallint NOT NULL DEFAULT 1,
  "created_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "update_time" timestamp,
  CONSTRAINT "uk_api_code" UNIQUE ("api_code")
);
CREATE INDEX "idx_adad_state" ON "ai_dw_api_def" ("state");

COMMENT ON TABLE "ai_dw_api_def" IS 'API接口定义表';
COMMENT ON COLUMN "ai_dw_api_def"."id" IS '主键ID';
COMMENT ON COLUMN "ai_dw_api_def"."api_code" IS 'API编码，全局唯一';
COMMENT ON COLUMN "ai_dw_api_def"."api_name" IS 'API名称';
COMMENT ON COLUMN "ai_dw_api_def"."api_path" IS 'API路径';
COMMENT ON COLUMN "ai_dw_api_def"."api_method" IS 'HTTP方法：GET/POST/PUT/DELETE';
COMMENT ON COLUMN "ai_dw_api_def"."comments" IS 'API描述';
COMMENT ON COLUMN "ai_dw_api_def"."category" IS 'API分类';
COMMENT ON COLUMN "ai_dw_api_def"."state" IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN "ai_dw_api_def"."created_time" IS '创建时间';
COMMENT ON COLUMN "ai_dw_api_def"."update_time" IS '更新时间';

-- 应用信息表
CREATE TABLE "ai_dw_app" (
  "id" SERIAL PRIMARY KEY,
  "app_id" varchar(60) NOT NULL,
  "app_secret" varchar(120) NOT NULL,
  "app_code" varchar(60) NOT NULL,
  "app_name" varchar(60) NOT NULL,
  "comments" varchar(255),
  "state" smallint NOT NULL DEFAULT 1,
  "created_by" bigint NOT NULL,
  "created_date" timestamp NOT NULL,
  "update_by" bigint,
  "update_date" timestamp,
  CONSTRAINT "uk_ada_app_id" UNIQUE ("app_id"),
  CONSTRAINT "uk_ada_app_code" UNIQUE ("app_code")
);
CREATE INDEX "idx_ada_state" ON "ai_dw_app" ("state");

COMMENT ON TABLE "ai_dw_app" IS '应用信息表';
COMMENT ON COLUMN "ai_dw_app"."id" IS '主键ID';
COMMENT ON COLUMN "ai_dw_app"."app_id" IS '应用ID，全局唯一';
COMMENT ON COLUMN "ai_dw_app"."app_secret" IS '应用密钥(加密存储)';
COMMENT ON COLUMN "ai_dw_app"."app_code" IS '应用编码，全局唯一';
COMMENT ON COLUMN "ai_dw_app"."app_name" IS '应用名称';
COMMENT ON COLUMN "ai_dw_app"."comments" IS '应用描述';
COMMENT ON COLUMN "ai_dw_app"."state" IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN "ai_dw_app"."created_by" IS '创建人工号';
COMMENT ON COLUMN "ai_dw_app"."created_date" IS '创建时间';
COMMENT ON COLUMN "ai_dw_app"."update_by" IS '更新人工号';
COMMENT ON COLUMN "ai_dw_app"."update_date" IS '更新时间';

-- Token管理表
CREATE TABLE "ai_dw_app_access_token" (
  "id" SERIAL PRIMARY KEY,
  "token" varchar(255) NOT NULL,
  "app_id" int NOT NULL,
  "token_type" smallint NOT NULL DEFAULT 1,
  "effective_at" timestamp,
  "expires_at" timestamp,
  "state" smallint NOT NULL DEFAULT 1,
  "created_by" bigint,
  "created_date" timestamp NOT NULL,
  "last_used_time" timestamp,
  "update_by" bigint,
  "update_date" timestamp,
  CONSTRAINT "uk_adat_token" UNIQUE ("token")
);
CREATE INDEX "idx_asat_app_id" ON "ai_dw_app_access_token" ("app_id");

COMMENT ON TABLE "ai_dw_app_access_token" IS 'Token管理表';
COMMENT ON COLUMN "ai_dw_app_access_token"."id" IS '主键ID';
COMMENT ON COLUMN "ai_dw_app_access_token"."token" IS 'Access Token';
COMMENT ON COLUMN "ai_dw_app_access_token"."app_id" IS '应用ID';
COMMENT ON COLUMN "ai_dw_app_access_token"."token_type" IS 'Token类型：1-系统级，2-用户级 用户级要求调用接口时必须携带用户信息';
COMMENT ON COLUMN "ai_dw_app_access_token"."effective_at" IS 'token生效时间，为空表示立即生效';
COMMENT ON COLUMN "ai_dw_app_access_token"."expires_at" IS '过期时间，NULL表示永久有效';
COMMENT ON COLUMN "ai_dw_app_access_token"."state" IS '状态：0-无效，1-有效';
COMMENT ON COLUMN "ai_dw_app_access_token"."created_by" IS '记录创建者id';
COMMENT ON COLUMN "ai_dw_app_access_token"."created_date" IS '记录创建时间';
COMMENT ON COLUMN "ai_dw_app_access_token"."last_used_time" IS '最后使用时间';
COMMENT ON COLUMN "ai_dw_app_access_token"."update_by" IS '记录更新者ID';
COMMENT ON COLUMN "ai_dw_app_access_token"."update_date" IS '记录更新时间';

-- 应用API权限关联表
CREATE TABLE "ai_dw_app_api_rel" (
  "id" BIGSERIAL PRIMARY KEY,
  "app_id" int NOT NULL,
  "api_id" int NOT NULL,
  "state" smallint NOT NULL DEFAULT 1,
  "created_by" bigint NOT NULL,
  "created_date" timestamp NOT NULL,
  "update_by" bigint,
  "update_date" timestamp,
  CONSTRAINT "uk_app_api" UNIQUE ("app_id", "api_id")
);

COMMENT ON TABLE "ai_dw_app_api_rel" IS '应用API权限关联表';
COMMENT ON COLUMN "ai_dw_app_api_rel"."id" IS '主键ID';
COMMENT ON COLUMN "ai_dw_app_api_rel"."app_id" IS '应用ID';
COMMENT ON COLUMN "ai_dw_app_api_rel"."api_id" IS 'API ID';
COMMENT ON COLUMN "ai_dw_app_api_rel"."state" IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN "ai_dw_app_api_rel"."created_by" IS '授权人ID';
COMMENT ON COLUMN "ai_dw_app_api_rel"."created_date" IS '创建时间';
COMMENT ON COLUMN "ai_dw_app_api_rel"."update_by" IS '记录更新者ID';
COMMENT ON COLUMN "ai_dw_app_api_rel"."update_date" IS '记录更新时间';

-- 应用审批记录表
CREATE TABLE "ai_dw_app_approval_records" (
  "id" SERIAL PRIMARY KEY,
  "app_id" int NOT NULL,
  "applicant_id" bigint NOT NULL,
  "approver_id" bigint,
  "state" smallint NOT NULL DEFAULT 0,
  "comments" varchar(255),
  "applied_time" timestamp NOT NULL,
  "approved_time" timestamp
);
CREATE INDEX "idx_adaar_app_id" ON "ai_dw_app_approval_records" ("app_id");

COMMENT ON TABLE "ai_dw_app_approval_records" IS '应用审批记录表';
COMMENT ON COLUMN "ai_dw_app_approval_records"."id" IS '主键ID';
COMMENT ON COLUMN "ai_dw_app_approval_records"."app_id" IS '应用主键ID';
COMMENT ON COLUMN "ai_dw_app_approval_records"."applicant_id" IS '申请人主键标识';
COMMENT ON COLUMN "ai_dw_app_approval_records"."approver_id" IS '审批人主键标识';
COMMENT ON COLUMN "ai_dw_app_approval_records"."state" IS '审批状态：0-待审批，1-通过，2-拒绝';
COMMENT ON COLUMN "ai_dw_app_approval_records"."comments" IS '审批意见';
COMMENT ON COLUMN "ai_dw_app_approval_records"."applied_time" IS '申请时间';
COMMENT ON COLUMN "ai_dw_app_approval_records"."approved_time" IS '审批时间';

-- 项目表
CREATE TABLE "ai_dw_project" (
  "id" SERIAL PRIMARY KEY,
  "wiki_id" bigint NOT NULL,
  "project_code" varchar(60) NOT NULL,
  "project_name" varchar(120) NOT NULL,
  "pm_id" bigint,
  "pm_name" varchar(120) NOT NULL,
  "created_by" bigint NOT NULL,
  "created_date" timestamp NOT NULL,
  "update_by" bigint,
  "update_date" timestamp
);
CREATE INDEX "idx_adp_wiki_id" ON "ai_dw_project" ("wiki_id");

COMMENT ON TABLE "ai_dw_project" IS '项目表';
COMMENT ON COLUMN "ai_dw_project"."id" IS '项目表主键';
COMMENT ON COLUMN "ai_dw_project"."wiki_id" IS 'wiki_info表主键id';
COMMENT ON COLUMN "ai_dw_project"."project_code" IS '项目编码';
COMMENT ON COLUMN "ai_dw_project"."project_name" IS '项目名称';
COMMENT ON COLUMN "ai_dw_project"."pm_id" IS '项目负责人ID';
COMMENT ON COLUMN "ai_dw_project"."pm_name" IS '项目负责人名称';
COMMENT ON COLUMN "ai_dw_project"."created_by" IS '记录创建者主键ID';
COMMENT ON COLUMN "ai_dw_project"."created_date" IS '记录创建时间';
COMMENT ON COLUMN "ai_dw_project"."update_by" IS '记录更新者主键ID';
COMMENT ON COLUMN "ai_dw_project"."update_date" IS '记录更新者';

-- 用户级token与用户关联关系表
CREATE TABLE "ai_dw_app_token_user_rel" (
  "id" SERIAL PRIMARY KEY,
  "user_id" bigint NOT NULL,
  "token_id" int NOT NULL,
  "created_by" int NOT NULL,
  "created_date" timestamp NOT NULL,
  CONSTRAINT "ux_adatur_tid_uid" UNIQUE ("token_id", "user_id")
);

COMMENT ON TABLE "ai_dw_app_token_user_rel" IS '用户级token与用户关联关系表';
COMMENT ON COLUMN "ai_dw_app_token_user_rel"."id" IS 'token与user关联关系表主键';
COMMENT ON COLUMN "ai_dw_app_token_user_rel"."user_id" IS '用户表主键';
COMMENT ON COLUMN "ai_dw_app_token_user_rel"."token_id" IS '应用token表主键';
COMMENT ON COLUMN "ai_dw_app_token_user_rel"."created_by" IS '记录创建者标识';
COMMENT ON COLUMN "ai_dw_app_token_user_rel"."created_date" IS '创建时间';