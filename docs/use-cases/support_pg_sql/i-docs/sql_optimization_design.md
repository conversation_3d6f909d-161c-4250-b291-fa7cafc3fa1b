# MySQL & PostgreSQL 双数据库支持设计文档

## 1. 引言

本文档旨在为 `whale-deepwiki` 项目提供一个详细的设计方案，以实现对 MySQL 和 PostgreSQL 数据库的同时支持。通过本方案，项目将能够通过简单的配置切换，在两种数据库之间无缝工作，从而提高系统的灵活性和可扩展性。

## 2. 配置切换

为了实现数据库的动态切换，我们将在 `settings.yaml` 文件中引入一个新的配置项 `DB_TYPE`。该配置项将作为选择数据库类型的开关。

### 2.1. 修改 `settings.yaml`

在 `settings.yaml` 的 `database` 配置块中，我们建议增加 `type` 字段，并根据其值来动态加载不同数据库的连接配置。

```yaml
# settings.yaml

database:
  # 可选值为 "mysql" 或 "postgresql"
  type: "${DB_TYPE:mysql}" # 默认为 mysql

  connections:
    mysql:
      url:
        driver_name: "mysql+pymysql"
        username: "dbops"
        password: "abc@123A"
        host: "************"
        port: 3307
        database: "deepwiki_open_dev"
      # ... 其他 MySQL 特定配置

    postgresql:
      url:
        driver_name: "postgresql+psycopg2"
        username: "your_pg_user"
        password: "your_pg_password"
        host: "your_pg_host"
        port: 5432
        database: "deepwiki_pg_db"
      # ... 其他 PostgreSQL 特定配置
```

### 2.2. 修改 `api/config.py` 和 `api/database/service.py`

我们需要修改 `get_database_config()` 函数，使其能够根据 `database.type` 的值来选择正确的数据库连接配置。

```python
# api/config.py

def get_database_config() -> DatabaseSettings:
    """
    获取数据库配置
    """
    db_config_all = configs.get("database", {})
    db_type = db_config_all.get("type", "mysql")
    db_config = db_config_all.get("connections", {}).get(db_type, {})
    return DatabaseSettings(**db_config)
```

通过上述修改，`DatabaseService` 在初始化时将接收到对应数据库的 `DatabaseSettings`，并在创建 `database_url` 时使用正确的 `driver_name`。

## 3. 代码修改策略

由于项目使用了 `SQLAlchemy` 和 `SQLModel`，大部分代码已经具备了数据库无关性。然而，在模型定义中，存在一些 MySQL 特定的数据类型，需要将其替换为 `SQLAlchemy` 的通用类型，以确保在 PostgreSQL 下的兼容性。

### 3.1. 数据类型映射

| MySQL 类型 | SQLAlchemy 通用类型 | PostgreSQL 对应类型 |
| --- | --- | --- |
| `BIGINT(unsigned=True)` | `sqlalchemy.BigInteger` | `BIGINT` |
| `INTEGER(unsigned=True)` | `sqlalchemy.Integer` | `INTEGER` |
| `TINYINT(unsigned=True)` | `sqlalchemy.SmallInteger` | `SMALLINT` |
| `LONGTEXT` | `sqlalchemy.Text` | `TEXT` |
| `DATETIME` | `sqlalchemy.DateTime` | `TIMESTAMP` |
| `BOOLEAN` | `sqlalchemy.Boolean` | `BOOLEAN` |
| `JSON` | `sqlalchemy.JSON` | `JSONB` |

### 3.2. 模型修改示例

以 `ai_dw_chat_history` 表对应的 `ChatHistory` 模型为例：

**修改前 (`api/model/chat_history.py`)**

```python
from sqlalchemy.dialects.mysql import BIGINT, LONGTEXT, TINYINT, DATETIME

# ...

class ChatHistory(SQLModelSerializable, table=True):
    # ...
    id: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(20, unsigned=True), primary_key=True, autoincrement=True, nullable=False, comment="主键，自增1")
    )
    content: Optional[str] = Field(
        default=None,
        sa_column=Column(LONGTEXT(collation='utf8mb4_bin'), comment="消息内容")
    )
    state: int = Field(
        default=1,
        sa_column=Column(TINYINT(3, unsigned=True), nullable=False, comment="0:失效 1:有效")
    )
    # ...
```

**修改后**

```python
from sqlalchemy import BigInteger, Text, SmallInteger, DateTime, Column

# ...

class ChatHistory(SQLModelSerializable, table=True):
    # ...
    id: Optional[int] = Field(
        default=None,
        sa_column=Column(BigInteger, primary_key=True, autoincrement=True, nullable=False, comment="主键，自增1")
    )
    content: Optional[str] = Field(
        default=None,
        sa_column=Column(Text, comment="消息内容")
    )
    state: int = Field(
        default=1,
        sa_column=Column(SmallInteger, nullable=False, comment="0:失效 1:有效")
    )
    # ...
```

所有其他的模型文件也需要进行类似的修改，以消除对 `sqlalchemy.dialects.mysql` 的直接依赖。

## 4. PostgreSQL DDL 脚本

根据 `api/model/` 目录下所有模型定义，我们生成了以下与 PostgreSQL v12.x 兼容的 DDL 脚本。该脚本符合项目要求的 `SQL开发规范-pg`、`数据库模型设计规范-pg` 和 `数据库运维和安全规范-pg`。

该脚本将保存于 `workspace/use-cases/support_pg_sql/schema.pg.sql`。

```sql
-- PostgreSQL DDL Script for whale-deepwiki

-- 开启事务
BEGIN;

-- ... (DDL 脚本内容) ...

-- 提交事务
COMMIT;

```
*(注：完整的 DDL 脚本见独立的 .sql 文件)*

## 5. 数据迁移策略 (可选)

对于需要从现有 MySQL 数据库迁移数据到 PostgreSQL 的场景，可以采用以下高级步骤：

1.  **数据导出**: 使用 `mysqldump` 或其他工具将 MySQL 数据导出为 `CSV` 或 `SQL` 格式。
2.  **数据转换**: 如果存在数据类型或格式的不兼容（例如日期格式），编写脚本对导出的数据进行转换。
3.  **数据导入**: 使用 PostgreSQL 的 `COPY` 命令或 `psql` 工具将转换后的数据导入到新的 PostgreSQL 数据库中。
4.  **序列同步**: 由于自增主键的值不会自动迁移，需要手动更新 PostgreSQL 中每个表的序列，使其从当前的最大 ID 继续。

建议在执行迁移前，进行充分的测试，并备份所有数据。
