<blockquote class="read-author-info">
<p>Documentation Path：epg/浩鲸在线规范库/MySQL规范/数据库运维和安全规范(MySQL分册)</p>
</blockquote>

![image](uploads/4065/7256ebc3-a79f-48ec-82aa-a5d9619681cb/PG_OPE_MySQL.png)

# 概述

## 编写目的

本文用于指导浩鲸科技运维人员进行MySQL5.7、MySQL5.6版本的数据库运维和安全操作。

## 适用范围

浩鲸科技所有使用MySQL5.7、MySQL5.6版本数据库的项目。

## 角色和职责

1. 运维人员: 参考文档对项目数据库进行运维。

2. DBA : 负责运维文档的更新并对运维人员进行技术指导。

# MySQL运维规范

## 数据量

【建议】

1. 未参与分布式分库分表环境的MySQL数据库，单表数据量超过2000W的表需要考虑进行分库分表、数据清理等工作。如不能执行分库分表方案，单表数据量控制在5000W以内，历史数据归档存放归档库，整库大小控制在 500G以内。

2. 参与分布式分库分表环境的MySQL数据库，物理表单表数据量建议控制在500W以内。

说明：

1. 大表对查询的影响：

   - 造成慢查询，数据库引擎很难在一定的时间内过滤出所需要的数据。

   - 区分度低、大量磁盘IO。

2. 大表对DDL操作的影响：

   - 建立、维护索引需要很长时间。

   - 修改表结构需要长时间锁表，业务繁忙期操作极易造成生产事故。

3. 建议处理大表问题：（注意：MySQL不使用分区表）

   - 分库分表把一张表分成多个小表。需要考虑的问题：分表主键的选择、分表后数据的查询和统计。

   - 大表的历史数据归档。需要考虑的问题：时间点选择，如何进行归档操作，归档数据的存储，数据仓库的建设等问题。

4. 整库数据量过大（数据文件大小超过500G）：拆库，基于数据库中间件实现水平分库或者垂直分库。

## 禁用功能

【强制】禁止使用：触发器、自定义函数、存储过程、视图、事件等MySQL高级功能

说明：

1. 存储过程难以调试和扩展，更没有移植性。

2. 应避免业务逻辑与数据存储发生耦合，否则不利于后期分库分表。业务逻辑应在应用代码中实现。

3. MySQL数据库原生函数可以用，自定义函数不可创建并使用。

## 数据变更

【强制】数据变更时，如删除和修改记录，删除整张表，要先select，校验操作的数据，确认数据无误，并备份原始数据，准备好回滚方案后，才能执行生产变更。

说明：保证数据安全及变更失败后的快速恢复。

检查方式：人工检查

## 表结构发布要求

【强制】禁止将无主键的表结构发布到生产。

说明：

1. 性能方面，MySQL的InnoDB存储引擎，存储的表类型为索引组织表。
2. 集群搭建方面，如MGR等复制集群要求MySQL节点的表，必须有主键。

检查方式：人工检查

## 从库禁止写操作发生

【强制】严禁在任何复制架构的从库上执行select以外的其他语句，严禁任何写操作的发生。

说明：保证MySQL集群各节点，数据的强一致性。

## 主库禁止发生导出操作

【强制】严禁在任何复制架构的主库上执行导出操作（比如MySQLdump等），只能在从库执行。

说明：保证主库的性能。负载均衡。

## 清理临时表

【强制】运维人员手工创建的临时存储数据的普通表（非create temporary table创建的临时表），在确定无需保留的情况下，应及时进行清理。

【强制】应用创建的临时存储数据的普通表（非create temporary table创建的临时表）由应用自行维护，并及时进行清理。

检查方式：人工检查

## 禁止大事务

【强制】禁止在生产环境发布大事务sql。代码中发现有大事务的，应及时拆分或者采用其他方式避免。

重点说明：

1. 在MySQL数据库架构中，产生大事务是极其危险的事情。大事务极易产生生产事故。

2. 大事务缺点：

   - 会长时间锁定资源，造成大量阻塞。只有在大事务执行完毕后锁才会释放。

   - 长时间阻塞，会造成MySQL连接不释放，并发情况高的情况下，容易撑爆MySQL。

   - MySQL的主从复制，从节点只有在主节点的大事务执行完毕才会复制，因为只有事务执行完毕后才会写入到主节点的binlog日志中。所以大事务会造成从节点的复制延迟。

   - 大事务如果执行失败，回滚也会很耗时。

注：在项目环境，运行的时间比较长，操作的数据比较多的事务我们定义为大事务。数据库记录慢SQL都是1秒以内的。如果一个事务执行大几十秒或者几分钟以上，就算的上大事务了。

## 整表删数据

【强制】整表数据删除时，TRUNCATE比 DELETE FROM速度快，且使用的系统和事务日志资源少，但TRUNCATE执行不当有可能造成事故，且需要create和drop两个权限，故不建议在应用开发代码中使用此语句。MySQL任何复制架构严禁在业务高峰期使用没有where条件的delete from 语句一次性删除所有数据，需分批删除。delete from删掉的大表数据，应选业务空闲期，整理表碎片，回收存储空间。

说明：建议在业务空闲期，人工使用show create table命令查询建表语句后，原表drop table和create table新表，重建替代。注意MySQL没有序列，新表自增id默认从1开始。

## 高峰期禁止的操作

### 高峰期禁止对大批量数据进行DML和查询操作

【强制】严禁在业务高峰期，人为大批量更新、插入或查询大量数据。

说明：如违反，极易造成生产事故。

### 高峰期禁止DDL操作

【强制】DDL语句禁止在系统繁忙时执行，应选择系统空闲时执行，并提前通知客户，得到客户许可。

说明：如果违反，极易造成生产事故。

### 高峰期禁止更新统计信息操作

【强制】严禁在业务高峰期更新统计信息（ANALYZE TABLE 表名）。

说明：如果违反，极易造成生产事故。

### 高峰期禁止整理表碎片操作

【强制】严禁在业务高峰期整理表碎片（OPTIMIZE TABLE XXXX或者ALTER TABLE XXXX ENGINE = INNODB）。

说明：如果违反，极易造成生产事故。

## 数据目录空间使用率

【建议】定期监控数据库数据目录空间使用率。

说明：MySQL数据库数据目录空间如果耗尽，相关DML操作会失败，相应的应用操作也会失败，所以数据目录空间使用率的检查必须要做。一般数据目录空间使用率超过80%，就必须要进行人工干预，数据目录扩容，或者进行数据清理并整理碎片（注意：MySQL整理碎片的时候，数据文件会先变大（大约变大一倍）后缩小，操作前请合理评估磁盘空间和回收碎片的表的数量）

## binlog目录空间使用率

【建议】业务发生高并发插入、更新数据时，应监控binlog目录空间使用率，防止binlog占满文件系统。

说明：MySQL数据库binlog目录空间如果耗尽，相关DML操作会失败，相应的应用操作也会失败。

## 动态参数修改

【强制】数据库动态参数修改

1. 若修改动态参数，则主库和备库均需要修改，并保持参数一致

2. 动态参数通过MySQL命令修改后，必须同时将参数写入配置文件中，以免重启失效，并按指定格式在修改项上方注释本次修改人工号及修改时间。

## 静态参数修改

【强制】数据库静态参数修改

1. 若修改静态参数，则主库和备库均需要修改，并保持参数一致。

2. 静态参数修改后，按指定格式在修改项上方注释本次修改人工号及修改时间

注意：静态参数需要重启数据库实例才能生效。

## GTID事务原子性

【强制】MySQL开启gtid\_mode且enforce_gtid_consistency= ON，Create Table .... Select ... 会报错：

```
ERROR 1786 (HY000): Statement violates GTID consistency: CREATE TABLE ... SELECT. 
```

原因该语句违反了GTID事务原子性。

说明：改为分两步执行即可，第一步建表，第二步插数据：

```sql
create table xxx like xxx;
insert into xxx select * from xxx;
```

## MySQL运维常用参数说明

参数详解见MySQL官方文档

https://dev.mysql.com/doc/refman/5.7/en/server-system-variables.html

1. innodb_buffer_pool_size

参数说明：该参数定义了 InnoDB存储引擎的表数据和索引数据的最大内存缓冲区大小。设置太小MySQL会使用到swap，设置太大影响操作系统运行，一般推荐服务器上MySQL实例总内存设置不要超过主机内存的75%。

2. innodb_flush_log_at_trx_commit和sync_binlog

参数说明：分别是redo log刷新和binlog刷新的参数，保证异常断电等情况下的数据安全。也保证主从数据一致性。标准配置都是1。

3. slow_query_log

参数说明：慢查询日志开启状态。标准配置开启。

4. long_query_time和log_queries_not_using_indexes

参数说明：

long_query_time：

设置慢查询日志需要记录慢sql的时间阈值， 最小值和默认值分别为0和10，如果启用了慢查询日志，则慢查询将记录到慢查询日志文件中。

log_queries_not_using_indexes：

如果启用此变量并启用慢速查询日志，则将记录预期检索所有行的查询。生产环境关闭，测试环境开启。此选项不一定意味着不       使用索引。例如，使用全索引扫描的查询使用索引，但由于索引不会限制行数，因此将记录日志。

5. max_connections

参数说明：MySQL的最大连接数。

6. log_error

   参数说明：错误日志记录MySQL的启动、运行、关闭过程中出现的问题。标准配置开启。

7. datadir

参数说明：指定了 MySQL 的数据库文件的存储路径。

8. expire_logs_days

参数说明：该参数控制binlog自动清理时间，单位天。

9. log_bin

参数说明：my.cnf中，该参数可查看binlog存放路径。

# MySQL安全规范

## 最小权限

【强制】只授予能满足需要的最小权限。

例子：

脚本发布用户：

```sql
GRANT ALL PRIVILEGES ON *.* TO fabu@192.168.1.* WITH GRANT OPTION; 
```

查询用户：

```sql
GRANT SELECT ON *.* TO selectuser@192.168.1.*; 
```

不能存在非法的账户

【强制】数据库账户用途要明确，不能存在非法的账户。

## 权限调整

【强制】数据库账户新增或已存在账户的权限调整均需要经过流程审批、并录入系统。

## 限制用户的登录主机

【强制】创建用户的时候限制用户的登录主机，主机使用 IP地址或IP网段，禁止使用主机名或%。

## 删除无用的用户和测试库

【强制】初始化数据库后删除无密码的用户，删除测试库。

## 满足密码复杂度的密码

【强制】为每个用户设置满足密码复杂度的密码。

## 定期清理不需要的用户

【强制】定期清理不需要的用户。

## 禁止授予普通用户 super 权限

【强制】禁止授予普通用户 super 权限。

## 脚本管理

【强制】脚本涉及账户、口令等重要信息的脚本不得随意泄漏和传播。脚本需项目专人统一保存，并进行版本管理。上线前需做到同行评审。

## 生产导出要求

【强制】生产数据库导出到非生产环境的，需要经过流程报备，并对敏感数据进行脱敏处理。

# 分布式环境运维规范

## 分布式建表规范

【强制】不支持CREATE TABLE tbl_name LIKE old_tbl_name；不支持CREATE TABLE tbl_name SELECT statement。

## 参数修改规范

【强制】不支持 GLOBAL 的变更，比如SET GLOBAL var=xx 或者 SET @@global.var=xx。

## 分布式事务操作

【强制】运维场景数据操作，注意不要触发使用分布式事务，每次事务只限定单库，并带上分库字段。

# SQL开发规范

内容详见 [SQL开发规范(MySQL分册)](/didc7db6v.md) 。

# 本文件评审、修正记录

| 文件名称 | 数据库运维和安全规范 – MySQL分册 |               |               |        |
| -------- | -------------------------------- | ------------- | ------------- | ------ |
| 序号     | 流程                             | 拟制人/审核人 | 拟制/审核时间 | 版本号 |
| 1        | 编写                             | 王佳琦        | 2020/04/17    | V0.1   |
| 2        | 第一次评审、修订                 | 王可心        | 2020/06/15    | V0.2   |
| 3        | 第二次评审、修订、标准化         | 王可心        | 2020/07/01    | V0.3   |
| 4        | 评审、修订、标准化               | 王佳琦        | 2020/07/20    | V0.4   |
| 5        | 发布前终审                       | 王佳琦        | 2020/08/12    | V0.5   |
| 6        | 分布式数据库相关评审             | 王佳琦        | 2020/09/24    | V0.6   |
| 7        | 最后评审、修订、标准化           | 王佳琦        | 2020/11/27    | V0.7   |
| 8        | 标准化                           | 数据库规范组  | 2020/12/21    | V1.0   |
| 9        | 去掉pxc下的truncate说明，log_queries_not_using_indexes改成生产关闭测试开启         | 王为  | 2022/05/24    | V1.0.1   |
| 10        | 增加章节，引用SQL开发规范       | 朱颖 | 2022/06/02 | V1.0.2  |
| 11        | 发布       | 数据库规范组 | 2022/06/14 | V1.1  |
