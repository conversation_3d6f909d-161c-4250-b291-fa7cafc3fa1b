# DeepWiki SQL 脚本性能与安全优化设计

## 背景与目标
- 对 `scripts/full_script/ddl.sql` 进行系统性体检，结合实际 ORM 使用，提出可落地的性能与安全优化方案。
- 输出索引、约束、字段类型、字符集、分区/归档、访问控制等方面的改进清单与迁移步骤。

## 评审基线与参考
- 参考规范：
  - `docs/use-cases/check_sql/context/SQL开发规范(MySQL分册.md`
  - `docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md`
  - `docs/use-cases/check_sql/context/数据库运维和安全规范(MySQL分册).md`
- 代码侧主要模型参考：`api/model/*`（如 `chat_history.py`, `chat_session.py`, `wiki_info.py`, `wiki_job.py`, `user_info.py`）。

## 总体结论
- 字符集/校对规则未统一，建议统一为 `utf8mb4` 与合适的 collation；长文本字段显式指定。
- 多数外键未建立或未声明，存在数据一致性风险；需按使用关系补建。
- 热点查询缺少必要二级索引或索引命名与代码不一致，应补充并与 ORM 对齐。
- 若干字段长度与语义不一致（如 `chat_sid`），需统一模型与 DDL。
- 安全方面：敏感令牌字段需加密/脱敏存储、必要唯一性/有效期索引缺失、审计表缺少分区/归档策略。

## 详细问题与优化建议

### 1. 字符集与排序规则
- 现状：部分表/字段未显式指定字符集/校对；部分长文本需要 `utf8mb4_bin`（代码中有指定）。
- 建议：
  - 数据库与表级统一 `DEFAULT CHARSET = utf8mb4`，通用 `COLLATE = utf8mb4_unicode_ci`。
  - 需要严格大小写或二进制比较的字段（如 token、外部ID、EL 表达式）保留/设置为 `utf8mb4_bin`。

### 2. 键与约束一致性
- 现状：
  - `ai_dw_role_priv` 中存在 `UNIQUE KEY (role_id, priv_id)`，但 DDL 未定义 `priv_id` 字段（疑似缺失）。
  - 多数外键关系未声明（如 `ai_dw_user_role.user_id -> ai_dw_user.id`）。
- 建议：
  - 修复缺失字段或调整唯一键为实际列（例如 `role_id, priv_el` 若表达唯一性）。
  - 为强一致实体关系声明外键并指定 `ON DELETE/UPDATE` 策略，或用应用层+索引替代并在文档中注明。

### 3. 索引设计（结合代码热路径）
- 现状与建议：
  - `ai_dw_chat_history`
    - 已有：`idx_adch_msg_sid`、`idx_adch_chat_id`。
    - 建议：补充复合索引 `(chat_id, created_date)` 以支撑按会话时间线查询；为 `parent_id` 增加索引（线程化检索）。
  - `ai_dw_chat_session`
    - DDL 唯一键 `idx_chat_sid`；ORM 中还定义了 `Index('idx_create_by_cid', 'created_by','wiki_id')`，建议与 DDL 对齐并改名统一为 `idx_adcs_cid_wid`。
  - `wiki_info`
    - 已有唯一键 `(repo_url, branch)`；结合使用，建议对 `owner_id`、`status` 建索引（DDL 注释已有但未真正建）。
  - `wiki_job`
    - 已有 `idx_status`, `idx_repo_url`, `idx_created_time`；建议补充 `(status, updated_time)` 复合索引支持任务轮询；`repo_url, branch` 复合索引提升查询命中。
  - 标签与关联（`ai_dw_tag`, `ai_dw_wiki_tag`）
    - 现有单列索引；建议增加 `(wiki_id, tag_id)` 复合唯一或普通索引，视业务是否允许重复。
  - 审计日志（`ai_dw_serv_log`）
    - 建议为 `event_code`, `oper_id`, `log_date` 建索引；并考虑按月/周分区或归档表。
  - 访问控制/应用（`ai_dw_app`, `ai_dw_app_access_token`, `ai_dw_app_api_rel`）
    - 为 `token` 已唯一；建议为 `app_id`、`state`、`expires_at` 建索引，加快鉴权与回收；`app_secret` 需加密存储并避免明文导出。

### 4. 字段定义与长度统一
- `ai_dw_chat_session.chat_sid`：DDL 为 `varchar(60)`，ORM 使用 `VARCHAR(32)`；建议统一为 `varchar(63)` 并在 ORM/DDL 一致。
- `provider/model` 等枚举/短字符串可设置检查约束或以字典表替代，减少脏值。
- IP 字段 `CHAR(15)` 仅支持 IPv4，若未来有 IPv6 需求，改为 `VARBINARY(16)` 或 `VARCHAR(39)`。

### 5. 安全与合规
- 敏感字段：`ai_dw_app.app_secret`, `ai_dw_app_access_token.token`, 用户扩展中的令牌字段等需：
  - 加密存储（应用层 KMS/透明加密）；数据库端避免明文导出。
  - 限制长度与字符集为 `utf8mb4_bin`；增加 `last_used_time`、`effective_at/expires_at` 索引与自动清理。
- 审计：确保关键业务表 `UPDATE/DELETE` 被审计到 `ai_dw_serv_log`；考虑行级审计触发器或应用日志闭环。

### 6. 归档与容量管理
- 大表：`ai_dw_chat_history`, `ai_dw_serv_log` 体量增长快。
  - 方案A：时间范围索引并配合定期归档（按月迁移到 `_yyyyMM` 归档表）。
  - 方案B：MySQL 分区（`RANGE COLUMNS (created_date)`），但需评估运维复杂度与备份策略。

### 7. 事务与外键策略
- 若采用外键：建议全部 InnoDB，引擎统一；对高写入表可只保留必要外键，其余以延迟校验+后台校正。
- 不采用外键：所有“外键列”建立相应索引，应用层保障一致性，提供清理任务与报表。

## 推荐 DDL 变更清单（节选）
- 统一字符集/校对规则，新增或调整字段 collation。
- 修复 `ai_dw_role_priv` 唯一键依赖字段缺失问题：增加 `priv_id` 或改为 `(role_id, priv_el)` 唯一。
- 新增索引：
  - `ai_dw_chat_history`: `(chat_id, created_date)`, `parent_id`。
  - `ai_dw_chat_session`: `(created_by, wiki_id)`；统一名为 `idx_adcs_cid_wid`。
  - `wiki_info`: `owner_id`, `status`。
  - `wiki_job`: `(status, updated_time)`, `(repo_url, branch)`。
  - `ai_dw_serv_log`: `event_code`, `oper_id`, `log_date`。
  - `ai_dw_app_access_token`: `app_id`, `state`, `expires_at`。
- 归档策略：对 `ai_dw_chat_history`, `ai_dw_serv_log` 采用按月归档或分区。

## 迁移与回滚方案
1. 建立影子索引与元数据核对，不阻塞业务。
2. 校正 DDL/ORM 不一致项（字段长度/类型/索引名）。
3. 分批增加索引（在线 DDL，如 gh-ost/pt-online-schema-change 或 MySQL 8 Online DDL）。
4. 敏感字段加密迁移：新列写入加密值，双写验证后切换读写，再清理明文列。
5. 部署归档任务（定时器/ETL），验证查询改造命中新索引。
6. 回滚：保留旧索引与列到稳定窗口，提供反向迁移脚本。

## 验收标准
- 关键查询平均响应时间下降（>30%）或稳定在 SLO 内。
- 全量一致性检查通过（外键/唯一/业务校验）。
- 安全项：敏感字段落地加密，Token 过期/回收有效，审计全覆盖。

## 附录：建议的补充 DDL 片段（示例）
```sql
-- 统一部分索引/字段
ALTER TABLE ai_dw_chat_history 
  ADD INDEX idx_adch_chat_created (chat_id, created_date),
  ADD INDEX idx_adch_parent (parent_id);

ALTER TABLE ai_dw_chat_session 
  ADD INDEX idx_adcs_cid_wid (created_by, wiki_id);

ALTER TABLE wiki_info 
  ADD INDEX idx_wiki_info_owner_id (owner_id),
  ADD INDEX idx_wiki_info_status (status);

ALTER TABLE wiki_job 
  ADD INDEX idx_wiki_job_status_utime (status, updated_time),
  ADD INDEX idx_wiki_job_repo_branch (repo_url, branch);

ALTER TABLE ai_dw_serv_log 
  ADD INDEX idx_adsl_event_code (event_code),
  ADD INDEX idx_adsl_oper_id (oper_id),
  ADD INDEX idx_adsl_log_date (log_date);

ALTER TABLE ai_dw_app_access_token 
  ADD INDEX idx_adat_app_id (app_id),
  ADD INDEX idx_adat_state (state),
  ADD INDEX idx_adat_expires (expires_at);

-- 修复 role_priv 唯一键
-- 选项1：补列
-- ALTER TABLE ai_dw_role_priv ADD COLUMN priv_id INT UNSIGNED NOT NULL COMMENT '权限标识' AFTER role_id;
-- 选项2：改唯一键
-- ALTER TABLE ai_dw_role_priv DROP INDEX ux_adrp_rid_pid, ADD UNIQUE KEY ux_adrp_rid_el (role_id, priv_el(255));
```

## 外键策略与落地方案（依据《数据库模型设计规范(MySQL分册)》）

依据规范要点：
- 【强制】实例表（高并发/大体量业务表）禁止建立外键，也不要作为外键父表；参照完整性在业务端保障。
- 【强制】需要做分库分表的表不能建立外键。
- 【推荐】尽量保证约束完整；如因性能/归档放弃外键，则必须用索引+业务校验+清理机制替代，并在模型文档中标注参照关系。

结合本项目表结构与读写特性，采取如下外键策略：

### A. 明确不使用外键（以性能与可用性优先）
- 高写入/大表：`ai_dw_chat_history`, `ai_dw_serv_log`, `wiki_job`。
  - 措施：
    - 对“外键列”建立必要索引（如 `chat_id`, `oper_id`, `wiki_info_id`）。
    - 应用层写入前做存在性校验；提供后台一致性巡检与孤儿数据清理任务。
    - 归档或分区时不受 FK 牵制，降低维护成本。
- 任务协调：`global_job_state`, `job_lock`, `global_pending_tasks`。
  - DDL 现有部分 FK（如指向 `job_manager_instance`）。建议评估写入压力后，优先改为“逻辑参照+索引”，避免跨表锁与级联开销。

建议 DDL（示例）：
```sql
-- 如评估后决定移除外键
-- ALTER TABLE global_job_state DROP FOREIGN KEY fk_global_job_last_instance, DROP FOREIGN KEY fk_global_job_processing_instance;
-- ALTER TABLE job_lock DROP FOREIGN KEY fk_job_lock_instance;
-- 并确保被参照列均有索引
CREATE INDEX idx_gjs_processing_instance ON global_job_state(processing_instance_id);
CREATE INDEX idx_gjs_last_instance ON global_job_state(last_processing_instance);
CREATE INDEX idx_jl_instance ON job_lock(instance_id);
```

### B. 业务弱关系、避免强耦合（不建 FK，配唯一/普通索引）
- 关联表：`ai_dw_user_role(user_id, role_id)`, `ai_dw_wiki_user_role(user_id, wiki_id, role_id)`, `ai_dw_wiki_tag(wiki_id, tag_id)`。
  - 措施：保留唯一/去重索引，应用层保障被参照记录存在；提供定期清理孤儿关联的作业。

建议 DDL（示例）：
```sql
-- 已有唯一索引场景保持；若缺失则补充
ALTER TABLE ai_dw_user_role ADD UNIQUE KEY ux_adur_uid_rid (user_id, role_id);
ALTER TABLE ai_dw_wiki_user_role ADD UNIQUE KEY ux_adwur_uid_wid_rid (user_id, wiki_id, role_id);
ALTER TABLE ai_dw_wiki_tag ADD INDEX idx_awt_wiki_tag (wiki_id, tag_id);
```

### C. 小体量“字典/配置”类，可选外键（慎用）
- 如 `ai_dw_role_priv(role_id -> ai_dw_role.id)` 等，如果读多写少且体量小，可按需保留 FK；但需确认写入路径不会受阻。
- 当前 `ai_dw_role_priv` 存在唯一键依赖未定义列问题（`priv_id` 缺失）。优先修复列定义与唯一性规则；FK 仅在确认无明显写入抖动后再考虑。

建议 DDL（示例）：
```sql
-- 先修复唯一约束，再评估是否需要 FK
-- ALTER TABLE ai_dw_role_priv DROP INDEX ux_adrp_rid_pid, ADD UNIQUE KEY ux_adrp_rid_el (role_id, priv_el(255));
-- 可选：如需外键
-- ALTER TABLE ai_dw_role_priv ADD CONSTRAINT fk_adrp_role_id FOREIGN KEY (role_id) REFERENCES ai_dw_role(id) ON DELETE CASCADE ON UPDATE CASCADE;
```

### D. 文档与巡检
- 在 `docs/i-docs` 中对每个“逻辑参照关系”标注：来源表、目标表、业务含义、约束方式（索引/应用校验/清理任务）。
- 提供月度巡检 SQL：统计孤儿记录、统计参照失配，输出报表并自动修复或告警。

以上策略与公司规范一致：高并发与可扩展性优先，放弃 FK 的同时以工程化手段严格保障数据一致性与可回收性。

## 按表分析结论（逐表建议）

以下逐表给出：用途概述、性能/索引建议、约束一致性、敏感信息与安全、其它注意点。

### ai_dw_announce（公告）
- 性能/索引：如有按 state/seq/created_date 查询，建议索引 `idx_adan_state_seq (state, seq)`，或 `idx_adan_created_date (created_date)`。
- 约束：`title` 可考虑唯一性视业务需要；`seq` 建唯一约束需谨慎，通常同一可见范围内唯一。
- 安全：无敏感字段。

### ai_dw_role（角色）
- 性能/索引：为 `role_code` 建唯一索引（若业务全局唯一），`role_type` 过滤可加普通索引。
- 约束：`role_code` 建唯一，避免重复。
- 安全：无敏感字段。

### ai_dw_role_priv（角色权限）
- 性能/索引：高频按 `role_id` 查询，建索引；`priv_el` 如作去重依据，考虑前缀唯一 `(role_id, priv_el(255))`。
- 约束：DDL 存在缺失列 `priv_id` 的唯一键，需修复为新增列或改唯一键至 `(role_id, priv_el)`。
- 安全：`priv_el` 建议 `utf8mb4_bin` 以严格比对。

### ai_dw_user（用户）
- 性能/索引：已有唯一 `wcp_user_id`、`user_code`；补充 `state`、`email`、`phone` 按需索引。
- 约束：邮箱/电话若用于登录，建议唯一并做格式校验。
- 安全：避免存敏感隐私的明文，考虑脱敏展示与访问控制。

### ai_dw_user_ext（用户扩展）
- 性能/索引：已唯一 `user_id`；如按 `sandbox_quota` 过滤可加索引。
- 约束：保证与 `ai_dw_user` 的一致性（逻辑参照+索引）。
- 安全：`ai_api_key`、`dev_cloud_token` 需加密/脱敏存储，限制导出权限。

### ai_dw_user_role（用户-角色关联）
- 性能/索引：已有唯一 `(user_id, role_id)`；补充 `user_id`、`role_id` 单列索引可优化反向查询。
- 约束：不建 FK，应用层保障存在性，提供孤儿清理任务。

### ai_dw_chat_history（会话消息）
- 性能/索引：补 `(chat_id, created_date)`、`parent_id` 索引；`msg_sid` 保留但注意选择性。
- 约束：与 `chat_session` 不建 FK；应用层校验。
- 其它：归档/分区建议；`content/tool_calls` 使用 LONGTEXT/MEDIUMTEXT 已合理。

### ai_dw_chat_session（会话）
- 性能/索引：与 ORM 对齐 `(created_by, wiki_id)`；`chat_sid` 唯一已设。
- 约束：统一 `chat_sid` 长度（建议 63），与代码一致。
- 其它：`ip` 若支持 IPv6，调整类型。

### ai_dw_chat_user_feedback（反馈）
- 性能/索引：`msg_sid` 已索引；按 `evaluate_type/created_date` 检索可加索引。
- 约束：与消息逻辑参照，无 FK。

### global_pending_tasks（挂起任务）
- 性能/索引：已有 `(priority, submit_time)`；补 `job_id` 索引以便清理/查询。
- 约束：不建议 FK；任务系统去耦。

### job_manager_instance（实例注册）
- 性能/索引：已有多维索引合理；按 `status,last_heartbeat` 组合查询已覆盖。
- 约束：主键非自增，字符主键可保留。

### wiki_info（Wiki 基础信息）
- 性能/索引：补 `owner_id`、`status` 索引；`(repo_url, branch)` 唯一保留。
- 约束：JSON 字段使用到的键不索引于 MySQL，避免在 WHERE 中对 JSON 做函数过滤。

### wiki_job（Wiki 任务）
- 性能/索引：补 `(status, updated_time)`、`(repo_url, branch)`；现有 `status/repo_url/created_time` 基本可用。
- 约束：`wiki_info_id` 不建 FK，建索引。
- 其它：长文本列 TEXT 合理；定期清理历史任务。

### global_job_state（全局任务状态）
- 性能/索引：已有多索引；补 `job_id` 相关唯一性策略视系统设计。
- 约束：建议移除 FK，用逻辑参照与索引替代，避免级联影响任务执行。

### job_lock（任务锁）
- 性能/索引：已有过期与实例索引；保障 `job_id` 的唯一主键可加速锁获取。
- 约束：同上，不使用 FK。

### ai_dw_wiki_user_role（Wiki 用户-角色）
- 性能/索引：已有唯一 `(user_id, wiki_id, role_id)`；可补 `wiki_id` 单列索引。
- 约束：逻辑参照，无 FK；提供孤儿清理。

### ai_dw_priv（权限定义）
- 性能/索引：按 `priv_code/priv_type/state` 检索可加索引；`priv_code` 建唯一。
- 约束：状态字段枚举建议 CHECK 或字典。

### ai_dw_serv_log（操作日志）
- 性能/索引：补 `event_code`、`oper_id`、`log_date` 索引。
- 其它：强烈建议按月归档或分区；避免 FK。

### ai_dw_event_code（事件编码）
- 性能/索引：`event_code` 唯一已设；按 `event_type` 检索可加索引。
- 约束：小表可选 FK 被引用方，但建议保持去耦。

### ai_dw_tag（标签）
- 性能/索引：已唯一 `(created_by, name)`；如跨用户全局唯一则改为 `name` 唯一。
- 约束：颜色值格式在应用层校验。

### ai_dw_wiki_tag（Wiki-标签关联）
- 性能/索引：已有 `wiki_id`、`tag_id` 单列；补 `(wiki_id, tag_id)` 复合索引/唯一按业务选择。
- 约束：不建 FK；清理孤儿。

### ai_dw_wiki_dc_ext_1 / ai_dw_wiki_dc_ext_2（研发云扩展）
- 性能/索引：`wiki_id` 建唯一/索引已覆盖；其它外部ID视查询加索引。
- 约束：与 `wiki_info` 逻辑参照，无 FK。

### ai_dw_api_def（API 定义）
- 性能/索引：`api_code` 唯一已设；按 `state/category` 检索加索引。
- 约束：方法与路径组合可建唯一以避免重复（`api_path, api_method`）。

### ai_dw_app（应用）
- 性能/索引：`app_id`、`app_code` 唯一已设；按 `state` 检索已索引。
- 安全：`app_secret` 加密存储；访问控制与审计。

### ai_dw_app_access_token（应用 Token）
- 性能/索引：补 `app_id`、`state`、`expires_at` 索引（鉴权与回收）。
- 约束：`token` 唯一已设；有效期、类型约束在应用层校验。
- 安全：`token` 加密/哈希存储，避免明文。

### ai_dw_app_api_rel（应用-API 关联）
- 性能/索引：唯一 `(app_id, api_id)` 已设；按 `app_id`、`api_id` 单列查询可加索引（若频繁反向）。
- 约束：不建 FK；定期孤儿清理。

### ai_dw_app_approval_records（应用审批）
- 性能/索引：`app_id` 已索引；按 `state/applied_time` 检索可加索引 `(state, applied_time)`。
- 约束：状态流转由应用控制；无 FK。

### ai_dw_project（项目）
- 性能/索引：`wiki_id` 已索引；`project_code`、`project_name` 如需唯一/搜索，加唯一或全文（注意场景）。
- 约束：与 `wiki_info` 逻辑参照；不建 FK。

### ai_dw_app_token_user_rel（Token-用户关联）
- 性能/索引：唯一 `(token_id, user_id)` 已设；可补 `user_id` 单列索引便于反查。
- 约束：不建 FK；清理孤儿。

## 阅读指南（给读者）
- 想快速落地：先看「快速执行清单」与「执行用 SQL 片段」。
- 需要宏观判断：看「总体结论」。
- 具体怎么干：看「外键策略与落地方案」与「详细问题与优化建议」。
- 只关心某张表：看「按表分析结论」。

## 目录
- [背景与目标](#背景与目标)
- [评审基线与参考](#评审基线与参考)
- [总体结论](#总体结论)
- [快速执行清单](#快速执行清单)
- [外键策略与落地方案（依据《数据库模型设计规范(MySQL分册)》）](#外键策略与落地方案依据数据库模型设计规范mysql分册)
- [详细问题与优化建议](#详细问题与优化建议)
  - [1. 字符集与排序规则](#1-字符集与排序规则)
  - [2. 键与约束一致性](#2-键与约束一致性)
  - [3. 索引设计（结合代码热路径）](#3-索引设计结合代码热路径)
  - [4. 字段定义与长度统一](#4-字段定义与长度统一)
  - [5. 安全与合规](#5-安全与合规)
  - [6. 归档与容量管理](#6-归档与容量管理)
  - [7. 事务与外键策略补充](#7-事务与外键策略)
- [按表分析结论（逐表建议）](#按表分析结论逐表建议)
- [执行用 SQL 片段（可直接落地）](#执行用-sql-片段可直接落地)
- [迁移与回滚方案](#迁移与回滚方案)
- [验收标准](#验收标准)

## 快速执行清单
1. 统一字符集与校对：库/表/关键字段统一 `utf8mb4`，默认 `utf8mb4_unicode_ci`，[高优]。
2. 修复约束错误：`ai_dw_role_priv` 唯一键依赖缺失列问题，[高优]。
3. 热点索引补齐：
   - `ai_dw_chat_history (chat_id, created_date)`, `parent_id`，[高优]。
   - `ai_dw_chat_session (created_by, wiki_id)` 与 ORM 命名对齐，[高优]。
   - `wiki_info (owner_id)`, `status`，[中优]。
   - `wiki_job (status, updated_time)`, `(repo_url, branch)`，[中优]。
   - `ai_dw_serv_log event_code/oper_id/log_date`，[中优]。
   - `ai_dw_app_access_token app_id/state/expires_at`，[高优]。
4. 敏感信息治理：`app_secret`、`token` 等加密/哈希存储，补 `last_used_time` 管理策略，[高优]。
5. 归档/分区：对 `ai_dw_chat_history`、`ai_dw_serv_log` 建立按月归档/分区方案，[中优]。
6. 外键策略：大表与任务表取消/不引入 FK，改为索引+巡检+清理，[高优]。
