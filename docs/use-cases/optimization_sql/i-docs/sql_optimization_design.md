# SQL 脚本分析与优化详细设计文档

## 1. 引言
本文档旨在对当前项目的 `scripts/full_script/ddl.sql` 脚本进行全面的性能和安全分析，并提出优化建议。分析将严格遵循《SQL开发规范(MySQL分册)》、《数据库模型设计规范(MySQL分册)》和《数据库运维和安全规范(MySQL分册)》中的相关规定。

## 2. 角色定义
本文作者是一名专业的数据库专家，精通 MySQL 数据库，在数据库性能设计、安全设计、扩展性设计等方面具备丰富经验。

## 3. 目标
通过对 `ddl.sql` 脚本的分析，识别潜在的性能和安全隐患，并输出一个结构清晰、可读性强的详细设计文档，以指导研发人员进行项目 SQL 的优化。

## 4. 分析范围
*   **主要分析脚本：** `scripts/full_script/ddl.sql`
*   **参考规范文档：**
    *   《SQL开发规范(MySQL分册)》
    *   《数据库模型设计规范(MySQL分册)》
    *   《数据库运维和安全规范(MySQL分册)》

## 5. `ddl.sql` 脚本分析与优化建议

### 5.1. `ai_dw_announce` 表
```sql
CREATE TABLE `ai_dw_announce` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '公告表主键标识',
  `title` varchar(255) NOT NULL COMMENT '公告标题',
  `type` varchar(30) NOT NULL DEFAULT 'link' COMMENT '公告类型: link超链接模式',
  `content` varchar(1000) NOT NULL COMMENT '公告内容,如链接地址',
  `seq` smallint(5) unsigned NOT NULL COMMENT '公告排列顺序,从1开始,依次加1',
  `state` tinyint(3) unsigned NOT NULL COMMENT '公告状态,1:上架 2:下架',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '公告记录创建者',
  `created_date` datetime NOT NULL COMMENT '公告记录创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '公告记录更新者',
  `update_date` datetime DEFAULT NULL COMMENT '公告记录更新时间',
  PRIMARY KEY (`id`)
) COMMENT='deepwiki公告表';
```
**分析：**
*   **命名规范：** 表名和字段名均符合小写和前缀规范。`id`、`created_date`、`update_date` 等字段命名符合推荐。`state` 字段命名符合推荐。
*   **字段数据类型：** 各字段数据类型选择合理，例如 `id` 为 `int unsigned auto_increment`，`state` 为 `tinyint unsigned`，`created_date` 为 `datetime`。`varchar` 字段长度也在推荐范围内。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **约束：** 未设置外键，符合实例表不建议设置外键的原则。
*   **字段注释：** 所有字段均包含注释，符合强制要求。

**优化建议：**
*   **索引：** 考虑到公告可能需要按标题进行模糊查询，或按创建日期进行范围查询，建议根据实际业务查询模式，为 `title` 和 `created_date` 字段添加普通索引，以提升查询性能。例如：
    *   `KEY idx_announce_title (title)`
    *   `KEY idx_announce_created_date (created_date)`
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/SQL开发规范(MySQL分册.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/SQL开发规范(MySQL分册).md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md&owner=$owner&repo=$repo&action=view)

### 5.2. `ai_dw_role` 表
```sql
CREATE TABLE `ai_dw_role` (
  `id` smallint(5) unsigned NOT NULL AUTO_INCREMENT COMMENT '角色标识',
  `role_name` varchar(60) NOT NULL COMMENT '角色名称',
  `role_code` varchar(30) NOT NULL COMMENT '角色编码',
  `role_type` char(1) NOT NULL COMMENT '角色类型 S:系统角色 D:数据角色',
  `access_level` tinyint(3) unsigned NOT NULL COMMENT '权限级别: 数值越大,权限越大',
  `comments` varchar(255) DEFAULT NULL COMMENT '角色描述',
  PRIMARY KEY (`id`)
) COMMENT='deepwiki角色表';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `smallint unsigned auto_increment`，`role_name`、`role_code` 为 `varchar`，`role_type` 为 `char(1)`，`access_level` 为 `tinyint unsigned`，`comments` 为 `varchar(255)`，数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **唯一约束：** 缺少针对 `role_code` 的唯一约束。`role_code` 作为角色编码，在业务上应具有唯一性，以避免数据冗余和逻辑混乱。
*   **字段注释：** 所有字段均包含注释，符合强制要求。

**优化建议：**
*   **添加唯一约束：** 为 `role_code` 字段添加唯一索引，以保证角色编码的唯一性。
    ```sql
    ALTER TABLE `ai_dw_role` ADD UNIQUE KEY `uk_role_code` (`role_code`);
    ```
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md&owner=$owner&repo=$repo&action=view)

### 5.3. `ai_dw_role_priv` 表
```sql
CREATE TABLE `ai_dw_role_priv` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '角色权限唯一标识',
  `role_id` smallint(5) unsigned NOT NULL COMMENT '角色标识',
  `priv_el` varchar(1000) NOT NULL COMMENT '角色服务权限el表达式',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_adrp_rid_pid` (`role_id`,`priv_id`)
) COMMENT='角色服务权限';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `int unsigned auto_increment`，`role_id` 为 `smallint unsigned`，`priv_el` 为 `varchar(1000)`，数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **唯一约束：** 存在联合唯一索引 `ux_adrp_rid_pid` (`role_id`,`priv_id`)。
*   **潜在问题：** 联合唯一索引 `ux_adrp_rid_pid` 中引用了 `priv_id` 字段，但该字段在表定义中不存在。这会导致 SQL 脚本执行失败。

**优化建议：**
*   **修正唯一约束：** 移除不存在的 `priv_id` 字段，或者如果 `priv_id` 是一个实际存在的字段且忘记定义，则需要添加该字段的定义。根据表注释 "角色服务权限el表达式"，推测 `priv_el` 字段可能就是权限的具体内容，那么联合唯一索引应该只包含 `role_id` 和 `priv_el`。如果 `priv_id` 是一个独立的权限ID，则需要在表中定义。假设 `priv_id` 是一个漏掉的字段，且应该与 `priv_el` 相关联，这里需要用户进一步确认。**在此，我假设 `priv_id` 是一个独立的权限ID，并且应该存在。**
    *   **方案一 (如果 `priv_id` 是一个独立的字段)：**
        ```sql
        CREATE TABLE `ai_dw_role_priv` (
          `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '角色权限唯一标识',
          `role_id` smallint(5) unsigned NOT NULL COMMENT '角色标识',
          `priv_id` int(10) unsigned NOT NULL COMMENT '权限标识', -- 假设 priv_id 是一个独立的权限ID
          `priv_el` varchar(1000) NOT NULL COMMENT '角色服务权限el表达式',
          PRIMARY KEY (`id`),
          UNIQUE KEY `ux_adrp_rid_pid` (`role_id`,`priv_id`)
        ) COMMENT='角色服务权限';
        ```
    *   **方案二 (如果 `priv_el` 才是唯一标识的一部分)：**
        ```sql
        CREATE TABLE `ai_dw_role_priv` (
          `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '角色权限唯一标识',
          `role_id` smallint(5) unsigned NOT NULL COMMENT '角色标识',
          `priv_el` varchar(1000) NOT NULL COMMENT '角色服务权限el表达式',
          PRIMARY KEY (`id`),
          UNIQUE KEY `ux_adrp_rid_el` (`role_id`,`priv_el`) -- 修正为 priv_el
        ) COMMENT='角色服务权限';
        ```
    *   **建议：** 考虑到 `priv_el` 是一个 `varchar(1000)` 字段，将其作为联合唯一索引的一部分可能会导致索引过大，影响性能。如果 `priv_id` 确实是权限的唯一标识，那么方案一更优。如果 `priv_id` 不存在，且 `priv_el` 是权限的唯一标识，那么需要评估 `priv_el` 的长度和实际数据，考虑是否需要对 `priv_el` 进行哈希或截断作为索引。**强烈建议确认 `priv_id` 的业务含义和来源。**
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view)

### 5.4. `ai_dw_user` 表
```sql
CREATE TABLE `ai_dw_user` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户表主键',
  `wcp_user_id` bigint(20) unsigned NOT NULL COMMENT '鲸加用户标识',
  `user_code` varchar(30) NOT NULL COMMENT '用户鲸加工号',
  `user_name` varchar(255) NOT NULL COMMENT '用户姓名',
  `email` varchar(255) DEFAULT NULL COMMENT '用户邮箱',
  `phone` varchar(30) DEFAULT NULL COMMENT '用户电话号码',
  `dept` varchar(120) DEFAULT NULL COMMENT '用户隶属部门',
  `org` varchar(100) DEFAULT NULL COMMENT '用户隶属组织',
  `job` varchar(120) DEFAULT NULL COMMENT '用户职位',
  `dept_id` int(10) unsigned DEFAULT NULL COMMENT '用户隶属部门标识',
  `org_id` int(10) unsigned DEFAULT NULL COMMENT '用户隶属组织标识',
  `job_id` int(10) unsigned DEFAULT NULL COMMENT '用户职位标识',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '用户是否有效,1:有效 0:无效',
  `last_login_date` datetime DEFAULT NULL COMMENT '上次登录时间',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录创建者',
  `created_date` datetime DEFAULT NULL COMMENT '记录创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_adu_wcp_user_id_` (`wcp_user_id`),
  UNIQUE KEY `idx_adu_user_code` (`user_code`)
) COMMENT='deepwiki用户表';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `bigint unsigned auto_increment`，`wcp_user_id` 和 `user_code` 具有唯一性并设置了唯一索引，`email` 和 `phone` 等字段允许 `NULL`，`state` 为 `tinyint unsigned`，`datetime` 字段用于时间戳，整体数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **唯一约束：** `wcp_user_id` 和 `user_code` 均有唯一索引，符合业务唯一性要求。
*   **字段注释：** 所有字段均包含注释，符合强制要求。

**优化建议：**
*   **索引：**
    *   如果经常通过 `email` 或 `phone` 进行用户查找，可以考虑为这些字段添加普通索引。
    *   对于 `dept_id`, `org_id`, `job_id` 等关联字段，如果存在大量基于这些ID的查询，也应考虑添加索引。
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/SQL开发规范(MySQL分册.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/SQL开发规范(MySQL分册).md&owner=$owner&repo=$repo&action=view)

### 5.5. `ai_dw_user_ext` 表
```sql
CREATE TABLE `ai_dw_user_ext` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键标识',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户标识',
  `ai_api_key` varchar(255) DEFAULT NULL COMMENT '公司大模型token',
  `dev_cloud_token` varchar(255) DEFAULT NULL COMMENT '研发云token',
  `sandbox_quota` int(10) unsigned DEFAULT NULL COMMENT '个性化沙盒并发配额',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '记录创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_ai_dw_user_ext_user_id` (`user_id`)
) COMMENT='用户信息扩展表';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `bigint auto_increment`，`user_id` 为 `bigint unsigned` 且有唯一索引，`ai_api_key` 和 `dev_cloud_token` 为 `varchar(255)`，`sandbox_quota` 为 `int unsigned`，数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **唯一约束：** `user_id` 具有唯一索引 `ux_ai_dw_user_ext_user_id`，符合业务逻辑。
*   **字段注释：** 所有字段均包含注释，符合强制要求。

**优化建议：**
*   **安全性：** `ai_api_key` 和 `dev_cloud_token` 字段存储敏感信息。虽然 `varchar(255)` 可以存储加密后的数据，但注释中直接写“token”可能暗示明文存储。**强烈建议对这些敏感信息进行加密存储，并在应用程序层面进行解密和使用，以符合安全规范。**
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库运维和安全规范(MySQL分册).md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库运维和安全规范(MySQL分册).md&owner=$owner&repo=$repo&action=view)

### 5.6. `ai_dw_user_role` 表
```sql
CREATE TABLE `ai_dw_user_role` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户角色标识',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户标识',
  `role_id` smallint(5) unsigned NOT NULL COMMENT '角色标识',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_ai_dw_user_role_uid_rid` (`user_id`,`role_id`)
) COMMENT='用户角色关联关系表';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `bigint unsigned auto_increment`，`user_id` 为 `bigint unsigned`，`role_id` 为 `smallint unsigned`，数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **唯一约束：** `user_id` 和 `role_id` 的联合唯一索引 `ux_ai_dw_user_role_uid_rid` 确保了用户和角色关联的唯一性，符合业务逻辑。
*   **字段注释：** 所有字段均包含注释，符合强制要求。

**优化建议：**
*   **无明显性能或安全隐患。** 这是一个典型的多对多关联表，设计合理。
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view)

### 5.7. `ai_dw_chat_history` 表
```sql
CREATE TABLE `ai_dw_chat_history` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键,自增1',
  `msg_sid` varchar(63) DEFAULT NULL COMMENT '大模型生成的id',
  `chat_id` bigint(20) unsigned NOT NULL COMMENT '会话id',
  `role` varchar(30) NOT NULL DEFAULT '' COMMENT '角色',
  `content` longtext COMMENT '消息内容',
  `provider` varchar(60) NOT NULL COMMENT '问答提供商',
  `model` varchar(255) DEFAULT NULL COMMENT 'ai大模型',
  `msg_data` varchar(1000) DEFAULT '' COMMENT 'json格式的额外数据',
  `deep_research` tinyint(3) unsigned DEFAULT '0' COMMENT '是否启用深度搜索,0:否 1:是',
  `deep_research_iter` varchar(30) DEFAULT NULL COMMENT '深度搜索迭代步骤标识 第一条:start 最后一条:end',
  `parent_id` bigint(20) unsigned DEFAULT NULL COMMENT '助手消息关联的用户消息主键',
  `tool_calls` mediumtext COMMENT '大模型工具调用信息',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '0:失效 1:有效',
  `qa_src` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '问答来源 1: deepwiki 2: mcp tool',
  `error_code` varchar(30) DEFAULT NULL COMMENT '问答错误码',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '消息归属者',
  `created_date` datetime NOT NULL COMMENT '生成时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '消息更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_adch_msg_sid` (`msg_sid`),
  KEY `idx_adch_chat_id` (`chat_id`)
) COMMENT='会话聊天历史表';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `bigint unsigned auto_increment`，`content` 为 `longtext` 用于存储大量文本，`tool_calls` 为 `mediumtext`，其他字段类型也合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **索引：** `msg_sid` 和 `chat_id` 均有普通索引，有利于按消息 SID 或会话 ID 进行查询。
*   **TEXT/BLOB 字段：** `content` 和 `tool_calls` 使用了 `longtext` 和 `mediumtext`。规范中提到“不在text、blob字段上建立索引”，目前这两个字段没有直接建立索引，符合此条。但如果需要对 `content` 或 `tool_calls` 进行全文搜索，则需要考虑使用全文索引或其他外部搜索方案。
*   **字段注释：** 所有字段均包含注释，符合强制要求。

**优化建议：**
*   **大文本字段处理：** `content` 和 `tool_calls` 字段存储大文本内容。虽然目前没有在这些字段上直接建立索引，但如果业务上需要对这些内容进行频繁的关键词搜索，则需要考虑引入全文索引（如 MySQL 的 `FULLTEXT` 索引）或集成外部搜索服务（如 Elasticsearch）。
*   **`msg_sid` 的唯一性：** `msg_sid` 注释为“大模型生成的id”，如果该 ID 在业务上是唯一的，可以考虑将其改为唯一索引，以确保数据一致性。
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/SQL开发规范(MySQL分册.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/SQL开发规范(MySQL分册).md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md&owner=$owner&repo=$repo&action=view)

### 5.8. `ai_dw_chat_session` 表
```sql
CREATE TABLE `ai_dw_chat_session` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键,自增1',
  `chat_sid` varchar(60) NOT NULL COMMENT '前端使用的会话id',
  `title` varchar(60) DEFAULT NULL COMMENT '会话标题',
  `wiki_id` varchar(32) NOT NULL DEFAULT '' COMMENT 'wiki标识',
  `ip` char(15) DEFAULT '' COMMENT '用户所在ip',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '0/1/2,删除/创建但无消息/创建已存在消息',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '记录创建者',
  `created_date` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_chat_sid` (`chat_sid`),
  KEY `idx_adcs_cid_wid` (`created_by`,`wiki_id`)
) COMMENT='会话表';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `bigint unsigned auto_increment`，`chat_sid` 为 `varchar(60)` 且有唯一索引，`wiki_id` 为 `varchar(32)`，`ip` 为 `char(15)`，`state` 为 `tinyint unsigned`，数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **唯一约束：** `chat_sid` 具有唯一索引 `idx_chat_sid`，符合业务唯一性要求。
*   **索引：** `created_by` 和 `wiki_id` 的联合索引 `idx_adcs_cid_wid` 有利于按创建者和 wiki ID 进行查询。
*   **字段注释：** 所有字段均包含注释，符合强制要求。

**优化建议：**
*   **无明显性能或安全隐患。** 设计合理。
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view)

### 5.9. `ai_dw_chat_user_feedback` 表
```sql
CREATE TABLE `ai_dw_chat_user_feedback` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键,自增1',
  `msg_sid` varchar(63) DEFAULT NULL COMMENT '大模型生成的id',
  `evaluate_type` tinyint(3) unsigned DEFAULT '0' COMMENT '0:NEUTRAL,GOOD:1,BAD:2',
  `feedback` varchar(255) DEFAULT NULL COMMENT '用户反馈',
  `category` varchar(255) DEFAULT NULL COMMENT '点踩时用户选择的选项,多个逗号分隔',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '记录创建者',
  `created_date` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_msg_sid` (`msg_sid`)
) COMMENT='用户反馈表';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `bigint unsigned auto_increment`，`msg_sid` 为 `varchar(63)`，`evaluate_type` 为 `tinyint unsigned`，`feedback` 和 `category` 为 `varchar(255)`，数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **索引：** `msg_sid` 存在普通索引。
*   **字段注释：** 所有字段均包含注释，符合强制要求。

**优化建议：**
*   **`msg_sid` 的唯一性：** 如果 `msg_sid` 在业务上是唯一的，可以考虑将其索引 `idx_msg_sid` 升级为唯一索引，以确保数据一致性。
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view)

### 5.10. `global_pending_tasks` 表
```sql
CREATE TABLE `global_pending_tasks` (
  `task_id` varchar(100) NOT NULL,
  `job_id` varchar(100) NOT NULL,
  `priority` int(11) NOT NULL DEFAULT '3',
  `preferred_instance_id` varchar(100) DEFAULT NULL,
  `job_context_json` text NOT NULL,
  `submit_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `retry_count` int(11) DEFAULT '0',
  `last_retry_time` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`task_id`),
  KEY `idx_priority_submit_time` (`priority`,`submit_time`)
) COMMENT='挂起的任务';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `task_id` 和 `job_id` 为 `varchar(100)`，`priority` 为 `int`，`job_context_json` 为 `text`，`timestamp` 用于时间戳，数据类型选择合理。
*   **主键：** `task_id` 作为主键，符合规范。
*   **索引：** `priority` 和 `submit_time` 的联合索引 `idx_priority_submit_time` 有利于按优先级和提交时间进行任务查询。
*   **TEXT 字段：** `job_context_json` 使用 `text` 存储。如果该字段内容非常大且频繁读写，可能会对性能造成一定影响。
*   **字段注释：** 缺少字段注释。

**优化建议：**
*   **添加字段注释：** 建议为所有字段添加清晰的注释，以便于理解和维护。
*   **`job_context_json` 字段优化：** 如果 `job_context_json` 存储的内容非常大，并且业务上不需要对其进行全文搜索，可以考虑将其存储到对象存储服务（如 S3）中，只在数据库中存储对应的 URL。如果需要频繁读取，并且内容不是特别巨大，可以保留 `text` 类型。
*   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md&owner=$owner&repo=$repo&action=view)

### 5.11. `job_manager_instance` 表
```sql
CREATE TABLE `job_manager_instance` (
  `instance_id` varchar(100) NOT NULL COMMENT '实例ID,UUID格式',
  `hostname` varchar(255) NOT NULL COMMENT '主机名',
  `pid` int(11) NOT NULL COMMENT '进程ID',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '实例状态：active(活跃), stopped(已停止), crashed(崩溃)',
  `max_concurrent_jobs` int(11) NOT NULL DEFAULT '3' COMMENT '最大并发任务数',
  `current_jobs` int(11) NOT NULL DEFAULT '0' COMMENT '当前处理的任务数',
  `last_heartbeat` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后心跳时间',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`instance_id`),
  KEY `idx_instance_last_heartbeat` (`last_heartbeat`),
  KEY `idx_instance_status` (`status`),
  KEY `idx_hostname_pid` (`hostname`,`pid`),
  KEY `idx_instance_status_heartbeat` (`status`,`last_heartbeat`)
) COMMENT='JobManager实例注册表,用于分布式部署管理';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `instance_id` 为 `varchar(100)`，`hostname` 为 `varchar(255)`，`pid` 为 `int`，`status` 为 `varchar(20)`，`datetime` 用于时间戳，数据类型选择合理。
*   **主键：** `instance_id` 作为主键，符合规范。
*   **索引：** 存在多个索引，包括 `last_heartbeat`、`status`、`hostname` 和 `pid` 的联合索引，以及 `status` 和 `last_heartbeat` 的联合索引，这些索引有助于提高查询效率。
*   **字段注释：** 所有字段均包含注释，符合强制要求。

**优化建议：**
*   **无明显性能或安全隐患。** 索引设计合理，符合分布式管理的需求。
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view)

### 5.12. `wiki_info` 表
```sql
CREATE TABLE `wiki_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `wiki_id` varchar(255) NOT NULL COMMENT 'Wiki唯一标识',
  `repo_url` varchar(255) NOT NULL COMMENT 'Git仓库路径',
  `branch` varchar(255) NOT NULL DEFAULT 'master' COMMENT 'Git分支',
  `repo_owner` varchar(255) NOT NULL COMMENT '仓库所有者',
  `repo_name` varchar(255) NOT NULL COMMENT '仓库名称',
  `repo_type` varchar(50) NOT NULL DEFAULT 'whaleDevCloud' COMMENT '仓库类型(github、gitlab等)',
  `topic_id` varchar(255) DEFAULT NULL COMMENT '公司大模型团队DocChain主题ID',
  `topic_id_code` varchar(255) DEFAULT NULL COMMENT 'DocChain代码主题ID',
  `topic_id_doc` varchar(255) DEFAULT NULL COMMENT 'DocChain文档主题ID 废弃',
  `provider` varchar(255) NOT NULL DEFAULT 'google' COMMENT '模型提供商',
  `model` varchar(255) NOT NULL DEFAULT 'gemini-pro' COMMENT '模型名称',
  `language` varchar(255) NOT NULL DEFAULT 'zh' COMMENT '语言',
  `excluded_dirs` text COMMENT '排除的目录 (使用 TEXT 存储,逗号分隔)',
  `excluded_files` text COMMENT '排除的文件 (使用 TEXT 存储,逗号分隔)',
  `included_dirs` text COMMENT '包含的目录 (使用 TEXT 存储,逗号分隔)',
  `included_files` text COMMENT '包含的文件 (使用 TEXT 存储,逗号分隔)',
  `comprehensive` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否生成全面Wiki',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `updated_by` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新人ID',
  `owner_id` bigint(20) unsigned NOT NULL COMMENT 'wiki拥有者ID',
  `status` varchar(255) NOT NULL DEFAULT 'pending' COMMENT '状态：pending、processing、completed、failed',
  `error_message` text COMMENT '错误信息',
  `wiki_data` json DEFAULT NULL COMMENT 'Wiki数据,包含结构和页面内容 (使用 JSON 或 TEXT)',
  `sub_repos` text DEFAULT NULL COMMENT '子仓库信息[{"url":"","branch":""}]',
  `visibility` tinyint(3) unsigned NOT NULL DEFAULT '2' COMMENT 'wiki可见性, 1:全员可见 2: 未授权其他人则仅创建人可见',
  `comments` varchar(255) DEFAULT NULL COMMENT '仓库描述',
  `type` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT 'wiki类型: 1:产品  2:项目',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uix_wiki_info_repo_branch` (`repo_url`,`branch`)
  KEY `idx_wiki_info_owner_id` (`owner_id`)
) COMMENT='Wiki信息表';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `int auto_increment`，`wiki_id`、`repo_url`、`branch`、`repo_owner`、`repo_name` 等为 `varchar(255)`，`text` 和 `json` 类型字段用于存储变长或结构化数据，数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **唯一约束：** `repo_url` 和 `branch` 的联合唯一索引 `uix_wiki_info_repo_branch` 确保了仓库 URL 和分支的唯一性，符合业务逻辑。
*   **索引：** `owner_id` 存在普通索引 `idx_wiki_info_owner_id`，有利于按拥有者查询。
*   **TEXT/JSON 字段：** 存在多个 `text` 和 `json` 类型的字段 (`excluded_dirs`, `excluded_files`, `included_dirs`, `included_files`, `error_message`, `wiki_data`, `sub_repos`)。
*   **字段注释：** 所有字段均包含注释，符合强制要求。
*   **潜在问题：** `topic_id_doc` 字段注释为“废弃”，但仍在表定义中。建议清理废弃字段。

**优化建议：**
*   **废弃字段清理：** 建议移除 `topic_id_doc` 字段，保持表结构的精简和清晰。
*   **TEXT/JSON 字段优化：**
    *   对于 `excluded_dirs`, `excluded_files`, `included_dirs`, `included_files` 这类字段，如果内容较短且数量不多，`text` 类型尚可接受。如果内容可能很长，或者需要进行频繁的字符串匹配查询，可以考虑将这些信息拆分到独立的关联表，或者使用 JSON 数组存储并利用 MySQL 8+ 的 JSON 函数进行查询。
    *   `wiki_data` 存储 Wiki 的结构和页面内容，可能非常大。如果查询不频繁或不需要对内容进行全文搜索，可以考虑将其存储到对象存储服务中，只在数据库中存储对应的 URL。如果需要频繁查询且内容大小可控，可以继续使用 `json` 类型，并利用 MySQL 的 JSON 函数进行查询优化。
    *   `error_message` 和 `sub_repos` 字段如果内容可能很长，但查询不频繁，`text` 类型是合适的。
*   **索引：**
    *   如果经常需要按 `status` 或 `repo_type` 进行查询，可以考虑为这些字段添加索引。
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/SQL开发规范(MySQL分册).md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md&owner=$owner&repo=$repo&action=view)

### 5.13. `wiki_job` 表
```sql
CREATE TABLE `wiki_job` (
  `id` varchar(36) NOT NULL COMMENT '任务ID,UUID格式',
  `status` varchar(50) NOT NULL DEFAULT 'pending' COMMENT '任务状态：pending(待处理), pending_resume(等待恢复), processing(处理中), resuming(恢复中), completed(已完成), failed(失败), cancelled(已取消), timeout(超时)',
  `stage` varchar(50) DEFAULT NULL COMMENT '任务阶段：init(初始化), download(下载), upload(上传), structure(结构生成), pages(页面生成), completed(已完成)',
  `stage_progress` int(11) DEFAULT '0' COMMENT '当前阶段的进度百分比(0-100)',
  `stage_message` varchar(255) DEFAULT NULL COMMENT '当前阶段的状态信息',
  `progress` int(11) DEFAULT '0' COMMENT '整体进度百分比(0-100)',
  `total_files` int(11) DEFAULT NULL COMMENT '总文件数',
  `processed_files` int(11) DEFAULT NULL COMMENT '已处理文件数',
  `error_message` text COMMENT '错误信息',
  `repo_url` varchar(255) NOT NULL COMMENT '代码仓库URL',
  `branch` varchar(100) DEFAULT 'main' COMMENT '代码仓库分支',
  `token` varchar(255) DEFAULT NULL COMMENT '访问令牌(如果需要认证)',
  `language` varchar(10) DEFAULT 'zh' COMMENT 'Wiki语言',
  `comprehensive` tinyint(1) DEFAULT '1' COMMENT '是否生成全面的Wiki(1:是,0:否)',
  `excluded_dirs` text COMMENT '排除的目录列表,逗号分隔',
  `excluded_files` text COMMENT '排除的文件列表,逗号分隔',
  `included_dirs` text COMMENT '包含的目录列表,逗号分隔',
  `included_files` text COMMENT '包含的文件列表,逗号分隔',
  `topic_id` varchar(100) DEFAULT NULL COMMENT '兼容旧版本的单个topic ID',
  `topic_id_code` varchar(100) DEFAULT NULL COMMENT '代码部分的topic ID',
  `topic_id_doc` varchar(100) DEFAULT NULL COMMENT '文档部分的topic ID',
  `wiki_info_id` varchar(36) DEFAULT NULL COMMENT '关联的WikiInfo记录ID',
  `model_settings` json DEFAULT NULL COMMENT '模型配置信息,包括提供商、模型名称等',
  `result` json DEFAULT NULL COMMENT '任务结果数据',
  `created_by` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `updated_by` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新人ID',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `sub_repos` varchar(255) DEFAULT NULL COMMENT '子仓库信息',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_repo_url` (`repo_url`),
  KEY `idx_created_time` (`created_time`)
) COMMENT='Wiki生成任务表';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `varchar(36)` 作为主键，`status`、`stage` 为 `varchar`，`error_message`、`excluded_dirs` 等为 `text`，`model_settings`、`result` 为 `json`，数据类型选择合理。
*   **主键：** `id` 作为主键，符合规范。
*   **索引：** `status`、`repo_url`、`created_time` 均有普通索引，有利于按状态、仓库 URL 和创建时间进行查询。
*   **TEXT/JSON 字段：** 存在多个 `text` 和 `json` 类型的字段。
*   **字段注释：** 所有字段均包含注释，符合强制要求。

**优化建议：**
*   **TEXT/JSON 字段优化：** 同 `wiki_info` 表，对 `error_message`、`excluded_dirs`、`excluded_files`、`included_dirs`、`included_files`、`model_settings`、`result` 等 `text` 或 `json` 字段，根据实际查询需求和数据量，根据实际查询需求和数据量，考虑是否需要引入全文索引或外部存储方案。如果内容巨大且不常查询，可以考虑外部存储。
*   **`token` 字段安全性：** `token` 字段存储敏感信息，**强烈建议进行加密存储。**
*   **索引：** 如果经常需要按 `wiki_info_id` 查询关联任务，可以考虑为 `wiki_info_id` 添加索引。
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/SQL开发规范(MySQL分册).md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/SQL开发规范(MySQL分册).md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md&owner=$owner&repo=$repo&action=view)

### 5.14. `global_job_state` 表
```sql
CREATE TABLE `global_job_state` (
  `job_id` varchar(100) NOT NULL COMMENT '任务ID',
  `processing_instance_id` varchar(100) DEFAULT NULL COMMENT '当前处理该任务的实例ID',
  `global_status` varchar(50) NOT NULL COMMENT '全局状态：available(可用), locked(已锁定), processing(处理中), completed(已完成), failed(失败)',
  `last_processing_instance` varchar(100) DEFAULT NULL COMMENT '最后处理该任务的实例ID',
  `job_metadata` text COMMENT '元数据,JSON格式',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`job_id`),
  KEY `idx_global_job_state_status` (`global_status`),
  KEY `idx_processing_instance` (`processing_instance_id`),
  KEY `fk_global_job_last_instance` (`last_processing_instance`),
  KEY `idx_global_status_instance` (`global_status`,`processing_instance_id`),
  CONSTRAINT `fk_global_job_last_instance` FOREIGN KEY (`last_processing_instance`) REFERENCES `job_manager_instance` (`instance_id`) ON DELETE SET NULL,
  CONSTRAINT `fk_global_job_processing_instance` FOREIGN KEY (`processing_instance_id`) REFERENCES `job_manager_instance` (`instance_id`) ON DELETE SET NULL
) COMMENT='全局任务状态表,用于跨实例状态同步';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `job_id`、`processing_instance_id`、`global_status` 等字段类型合理，`job_metadata` 为 `text`。
*   **主键：** `job_id` 作为主键，符合规范。
*   **索引：** 存在多个索引，包括 `global_status`、`processing_instance_id`、`last_processing_instance` 以及 `global_status` 和 `processing_instance_id` 的联合索引，有助于提高查询效率。
*   **外键：** 存在外键约束 `fk_global_job_last_instance` 和 `fk_global_job_processing_instance` 关联到 `job_manager_instance` 表。这在分布式数据库规范中是被强制禁止的，因为“对于需要做分库分表的表，不能建立外键”。尽管此表可能不分库分表，但仍需注意此规范。
*   **字段注释：** 所有字段均包含注释，符合强制要求。

**优化建议：**
*   **外键处理：** 虽然外键可以保证数据完整性，但在分布式或高并发场景下，外键可能会引入性能开销和复杂性。根据《数据库模型设计规范(MySQL分册)》中“实例表上禁止建立外键”的规定，建议移除外键约束，并在应用程序层面维护数据一致性。
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md&owner=$owner&repo=$repo&action=view)

### 5.15. `job_lock` 表
```sql
CREATE TABLE `job_lock` (
  `job_id` varchar(100) NOT NULL COMMENT '任务ID',
  `instance_id` varchar(100) NOT NULL COMMENT '持有锁的实例ID',
  `hostname` varchar(255) NOT NULL COMMENT '持有锁的主机名',
  `operation` varchar(50) NOT NULL COMMENT '操作类型：submit(提交), process(处理), recover(恢复), cancel(取消)',
  `acquired_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获取锁的时间',
  `expires_at` datetime NOT NULL COMMENT '锁过期时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`job_id`),
  KEY `idx_job_lock_expires_at` (`expires_at`),
  KEY `idx_job_lock_instance_id` (`instance_id`),
  KEY `idx_job_lock_hostname` (`hostname`),
  KEY `idx_job_lock_expires_instance` (`expires_at`,`instance_id`),
  CONSTRAINT `fk_job_lock_instance` FOREIGN KEY (`instance_id`) REFERENCES `job_manager_instance` (`instance_id`) ON DELETE CASCADE
) COMMENT='任务锁表,用于分布式任务管理';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `job_id`、`instance_id`、`hostname`、`operation` 等字段类型合理，时间戳字段使用 `datetime`。
*   **主键：** `job_id` 作为主键，符合规范。
*   **索引：** 存在多个索引，包括 `expires_at`、`instance_id`、`hostname` 以及 `expires_at` 和 `instance_id` 的联合索引，有助于提高查询效率。
*   **外键：** 存在外键约束 `fk_job_lock_instance` 关联到 `job_manager_instance` 表。同样，这在分布式数据库规范中是被强制禁止的，建议移除。
*   **字段注释：** 所有字段均包含注释，符合强制要求。

**优化建议：**
*   **外键处理：** 同 `global_job_state` 表，建议移除外键约束，并在应用程序层面维护数据一致性。
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md&owner=$owner&repo=$repo&action=view)

### 5.16. `ai_dw_wiki_user_role` 表
```sql
CREATE TABLE `ai_dw_wiki_user_role` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'wiki-user-role主键',
  `wiki_id` bigint(20) unsigned NOT NULL COMMENT 'wiki标识',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户标识',
  `role_id` bigint(20) unsigned NOT NULL COMMENT '角色标识',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '记录创建者',
  `created_date` datetime NOT NULL COMMENT '记录创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_adwur_uid_wid_rid` (`user_id`,`wiki_id`,`role_id`)
) COMMENT='wiki用户角色表';
```
**分析：：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `bigint unsigned auto_increment`，`wiki_id`、`user_id`、`role_id` 均为 `bigint unsigned`，数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **唯一约束：** `user_id`、`wiki_id` 和 `role_id` 的联合唯一索引 `ux_adwur_uid_wid_rid` 确保了用户、Wiki 和角色关联的唯一性，符合业务逻辑。
*   **字段注释：** 所有字段均包含注释，符合强制要求。

**优化建议：**
*   **无明显性能或安全隐患。** 这是一个典型的多对多关联表，设计合理。
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view)

### 5.17. `ai_dw_priv` 表
```sql
CREATE TABLE `ai_dw_priv` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '权限主键标识',
  `priv_type` char(1) NOT NULL COMMENT '权限类型,S:服务权限,C:组件权限',
  `priv_code` varchar(30) NOT NULL COMMENT '权限编码',
  `priv_name` varchar(120) NOT NULL COMMENT '权限名称',
  `priv_el` varchar(1000) DEFAULT NULL COMMENT '权限表达式',
  `state` tinyint(3) unsigned NOT NULL COMMENT '权限状态:0: 失效 1: 有效 ',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '记录创建者',
  `created_date` datetime NOT NULL COMMENT '记录创建日期',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新日期',
  PRIMARY KEY (`id`)
) COMMENT='deepwiki权限表';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `int unsigned auto_increment`，`priv_type` 为 `char(1)`，`priv_code` 为 `varchar(30)`，`priv_name` 为 `varchar(120)`，`priv_el` 为 `varchar(1000)`，`state` 为 `tinyint unsigned`，数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **唯一约束：** 缺少针对 `priv_code` 的唯一约束。`priv_code` 作为权限编码，在业务上应具有唯一性。
*   **字段注释：** 所有字段均包含注释，符合强制要求。

**优化建议：**
*   **添加唯一约束：** 为 `priv_code` 字段添加唯一索引，以保证权限编码的唯一性。
    ```sql
    ALTER TABLE `ai_dw_priv` ADD UNIQUE KEY `uk_priv_code` (`priv_code`);
    ```
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md&owner=$owner&repo=$repo&action=view)

### 5.18. `ai_dw_serv_log` 表
```sql
CREATE TABLE `ai_dw_serv_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志记录唯一标识，自增主键',
  `event_src` varchar(60) NOT NULL DEFAULT 'deepwiki' COMMENT '事件来源系统,默认deepwiki',
  `event_type` varchar(255) NOT NULL COMMENT '事件类型分类',
  `event_code` varchar(255) NOT NULL COMMENT '事件编码/操作编码',
  `party_type` varchar(30) NOT NULL COMMENT '被操作对象的类型(user,wiki)',
  `party_code` varchar(255) DEFAULT NULL COMMENT '被操作对象的唯一编码',
  `party_name` varchar(60) DEFAULT NULL COMMENT '被操作对象的名称',
  `party_id` varchar(60) NOT NULL COMMENT '被操作对象的ID(如:用户ID或者wiki id)',
  `oper_id` bigint(20) unsigned NOT NULL COMMENT '操作人ID',
  `dept_id` int(10) unsigned DEFAULT NULL COMMENT '操作人所属部门ID',
  `dept_name` varchar(120) DEFAULT NULL COMMENT '操作人所属部门名称',
  `oper_data` text DEFAULT NULL COMMENT '操作数据',
  `src_ip` varchar(60) DEFAULT NULL COMMENT '来源IP地址',
  `server_ip` varchar(60) DEFAULT NULL COMMENT '服务器IP地址',
  `is_success` tinyint(3) unsigned NOT NULL COMMENT '是否成功(1成功/0失败)',
  `log_date` datetime NOT NULL COMMENT '日志记录时间',
  `comments` varchar(1000) DEFAULT NULL COMMENT '操作备注/详细说明',
  PRIMARY KEY (`id`)
) COMMENT='系统服务操作日志表';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `bigint unsigned auto_increment`，`event_src`、`event_type`、`event_code` 等为 `varchar`，`oper_data` 为 `text`，`is_success` 为 `tinyint unsigned`，`log_date` 为 `datetime`，数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **TEXT 字段：** `oper_data` 使用 `text` 存储。作为日志表，该字段可能存储大量操作数据。
*   **字段注释：** 所有字段均包含注释，符合强制要求。
*   **索引：** 缺少必要的查询索引。日志表通常会根据时间、事件类型、操作人、被操作对象等进行查询。

**优化建议：**
*   **添加索引：** 建议根据常见的查询模式添加索引，以提高日志查询效率。例如：
    *   按时间范围查询：`KEY idx_servlog_log_date (log_date)`
    *   按事件类型和编码查询：`KEY idx_servlog_event (event_type, event_code)`
    *   按操作人或被操作对象查询：`KEY idx_servlog_oper_party (oper_id, party_id)`
*   **数据归档：** 日志表通常数据量增长非常快。建议考虑数据归档策略，定期将旧数据迁移到归档库，以保持主表性能。
*   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/SQL开发规范(MySQL分册).md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/SQL开发规范(MySQL分册).md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库运维和安全规范(MySQL分册).md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库运维和安全规范(MySQL分册).md&owner=$owner&repo=$repo&action=view)

### 5.19. `ai_dw_event_code` 表
```sql
CREATE TABLE `ai_dw_event_code` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `event_code` varchar(255) NOT NULL COMMENT '事件唯一编码',
  `event_type` varchar(255) DEFAULT NULL COMMENT '事件类型分类',
  `event_src_code` varchar(60) DEFAULT 'deepwiki' COMMENT '事件来源系统编码',
  `is_audit` tinyint(3) unsigned DEFAULT NULL COMMENT '是否需要审计(1是/0否)',
  `comments` varchar(255) DEFAULT NULL COMMENT '事件描述说明',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_ad_ec_event_code` (`event_code`)
) COMMENT='事件编码配置表';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `int auto_increment`，`event_code`、`event_type`、`event_src_code` 为 `varchar`，`is_audit` 为 `tinyint unsigned`，数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **唯一约束：** `event_code` 具有唯一索引 `ux_ad_ec_event_code`，确保事件编码的唯一性，符合业务逻辑。
*   **字段注释：** 所有字段均包含注释，符合强制要求。

**优化建议：**
*   **无明显性能或安全隐患。** 设计合理。
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view)

### 5.20. `ai_dw_tag` 表
```sql
CREATE TABLE `ai_dw_tag` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `name` varchar(60) NOT NULL COMMENT '标签名称',
  `type` tinyint(3) unsigned NOT NULL COMMENT '标签类型（1:系统 2:用户）',
  `color` varchar(16) NOT NULL COMMENT '标签颜色（如#FF0000）',
  `comments` varchar(255) DEFAULT NULL COMMENT '标签描述',
  `module_type` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '标签归属模块, 1:deepwiki',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '标签状态, 1:有效 0:失效',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '创建人ID',
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '修改人ID',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_adt_created_by_name` (`created_by`,`name`)
) COMMENT='标签表';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `bigint unsigned auto_increment`，`name` 为 `varchar(60)`，`type`、`module_type`、`state` 为 `tinyint unsigned`，`color` 为 `varchar(16)`，数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **唯一约束：** `created_by` 和 `name` 的联合唯一索引 `ux_adt_created_by_name` 确保了同一创建者下标签名称的唯一性，符合业务逻辑。
*   **字段注释：** 所有字段均包含注释，符合强制要求。

**优化建议：**
*   **无明显性能或安全隐患。** 设计合理。
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view)

### 5.21. `ai_dw_wiki_tag` 表
```sql
CREATE TABLE `ai_dw_wiki_tag` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '关联关系ID',
  `wiki_id` bigint(20) unsigned NOT NULL COMMENT 'wiki主键ID',
  `tag_id` bigint(20) unsigned NOT NULL COMMENT '标签ID',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '创建人ID',
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '更新人ID',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_wiki_id` (`wiki_id`),
  KEY `idx_tag_id` (`tag_id`)
) COMMENT='wiki与标签多对多关联表';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `bigint unsigned auto_increment`，`wiki_id` 和 `tag_id` 均为 `bigint unsigned`，数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **索引：** `wiki_id` 和 `tag_id` 均有普通索引，有利于按 Wiki ID 或标签 ID 进行查询。
*   **字段注释：** 所有字段均包含注释，符合强制要求。
*   **潜在问题：** 缺少 `wiki_id` 和 `tag_id` 的联合唯一索引。一个 Wiki 不应该重复关联同一个标签。

**优化建议：**
*   **添加联合唯一索引：** 为 `wiki_id` 和 `tag_id` 字段添加联合唯一索引，以确保一个 Wiki 不会重复关联同一个标签。
    ```sql
    ALTER TABLE `ai_dw_wiki_tag` ADD UNIQUE KEY `ux_adwt_wid_tid` (`wiki_id`,`tag_id`);
    ```
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md&owner=$owner&repo=$repo&action=view)

### 5.22. `ai_dw_wiki_dc_ext_1` 表
```sql
CREATE TABLE `ai_dw_wiki_dc_ext_1` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'wiki扩展信息表主键标识',
  `wiki_id` bigint(20) unsigned NOT NULL COMMENT 'wiki标识',
  `dc_repo_id` bigint(20) unsigned DEFAULT NULL COMMENT '研发云仓库ID',
  `dc_project_id` int(10) unsigned DEFAULT NULL COMMENT '研发云项目ID',
  `branch_version_id` int(10) unsigned DEFAULT NULL COMMENT '分支版本ID',
  `branch_version_name` varchar(1000) DEFAULT NULL COMMENT '分支版本名称',
  `product_version_id` int(10) unsigned DEFAULT NULL COMMENT '产品版本ID',
  `product_version_code` varchar(1000) DEFAULT NULL COMMENT '产品版本编码',
  `product_name` varchar(1000) DEFAULT NULL COMMENT '产品名称',
  `product_id` bigint(20) unsigned DEFAULT NULL COMMENT '产品ID',
  `product_line_id` bigint(20) unsigned DEFAULT NULL COMMENT '产品线ID',
  `product_line_name` varchar(1000) DEFAULT NULL COMMENT '产品线名称',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '记录创建者',
  `created_date` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_adwe_wiki_id` (`wiki_id`)
) COMMENT='wiki仓库关联的研发云扩展信息';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `bigint unsigned auto_increment`，`wiki_id` 为 `bigint unsigned` 且有唯一索引，多个 `varchar(1000)` 字段，数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **唯一约束：** `wiki_id` 具有唯一索引 `ux_adwe_wiki_id`，确保每个 Wiki 只有一条扩展信息，符合业务逻辑。
*   **字段注释：** 所有字段均包含注释，符合强制要求。
*   **潜在问题：** 存在多个 `varchar(1000)` 字段，如果这些字段实际存储的内容通常较短，或者不需要这么长的长度，可以考虑缩短，以节省存储空间和提高性能。

**优化建议：**
*   **字段长度优化：** 评估 `branch_version_name`、`product_version_code`、`product_name`、`product_line_name` 等 `varchar(1000)` 字段的实际存储需求。如果大多数情况下内容较短，可以适当缩短字段长度，例如 `varchar(255)` 或 `varchar(500)`，以节省存储空间。
*   **索引：** 如果经常需要通过 `dc_repo_id`、`dc_project_id`、`product_id` 或 `product_line_id` 进行查询，可以考虑为这些字段添加普通索引。
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md&owner=$owner&repo=$repo&action=view)

### 5.23. `ai_dw_wiki_dc_ext_2` 表
```sql
CREATE TABLE `ai_dw_wiki_dc_ext_2` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'wiki扩展信息表主键标识',
  `wiki_id` bigint(20) unsigned NOT NULL COMMENT 'wiki标识',
  `release_pkg_id` int(10) unsigned DEFAULT NULL COMMENT '发布包ID',
  `release_pkg_code` varchar(1000) DEFAULT NULL COMMENT '发布包编码',
  `solution_name` varchar(1000) DEFAULT NULL COMMENT '解决方案名称',
  `solution_id` bigint(20) unsigned DEFAULT NULL COMMENT '解决方案ID',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '记录创建者',
  `created_date` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新者',
  PRIMARY KEY (`id`),
  KEY `idx_adwdce_wiki_id` (`wiki_id`)
) COMMENT='wiki仓库关联的研发云扩展信息:发布包解决方案';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `bigint unsigned auto_increment`，`wiki_id` 为 `bigint unsigned`，`release_pkg_id` 为 `int unsigned`，多个 `varchar(1000)` 字段，数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **索引：** `wiki_id` 存在普通索引 `idx_adwdce_wiki_id`，有利于按 Wiki ID 进行查询。
*   **字段注释：** 所有字段均包含注释，符合强制要求。
*   **潜在问题：** 存在多个 `varchar(1000)` 字段，同 `ai_dw_wiki_dc_ext_1` 表。

**优化建议：**
*   **字段长度优化：** 评估 `release_pkg_code`、`solution_name` 等 `varchar(1000)` 字段的实际存储需求，考虑适当缩短字段长度。
*   **索引：** 如果经常需要通过 `release_pkg_id` 或 `solution_id` 进行查询，可以考虑为这些字段添加普通索引。
*   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md&owner=$owner&repo=$repo&action=view)

### 5.24. `ai_dw_api_def` 表
```sql
CREATE TABLE `ai_dw_api_def` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `api_code` varchar(30) NOT NULL COMMENT 'API编码，全局唯一',
  `api_name` varchar(60) NOT NULL COMMENT 'API名称',
  `api_path` varchar(60) NOT NULL COMMENT 'API路径',
  `api_method` varchar(20) NOT NULL COMMENT 'HTTP方法：GET/POST/PUT/DELETE',
  `comments` varchar(255) DEFAULT NULL COMMENT 'API描述',
  `category` varchar(30) DEFAULT NULL COMMENT 'API分类',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_api_code` (`api_code`),
  KEY `idx_adad_state` (`state`)
) COMMENT='API接口定义表';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `int unsigned auto_increment`，`api_code` 为 `varchar(30)` 且有唯一索引，`api_name`、`api_path`、`api_method` 为 `varchar`，`state` 为 `tinyint unsigned`，数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **唯一约束：** `api_code` 具有唯一索引 `uk_api_code`，确保 API 编码的唯一性，符合业务逻辑。
*   **索引：** `state` 存在普通索引 `idx_adad_state`，有利于按状态查询。
*   **字段注释：** 所有字段均包含注释，符合强制要求。

**优化建议：**
*   **索引：** 如果经常需要按 `api_path` 或 `api_method` 进行查询，可以考虑为这些字段添加普通索引，或者根据实际查询模式添加联合索引，例如 `(api_path, api_method)`。
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/SQL开发规范(MySQL分册).md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/SQL开发规范(MySQL分册).md&owner=$owner&repo=$repo&action=view)

### 5.25. `ai_dw_app` 表
```sql
CREATE TABLE `ai_dw_app` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` varchar(60) NOT NULL COMMENT '应用ID，全局唯一',
  `app_secret` varchar(120) NOT NULL COMMENT '应用密钥(加密存储)',
  `app_code` varchar(60) NOT NULL COMMENT '应用编码，全局唯一',
  `app_name` varchar(60) NOT NULL COMMENT '应用名称',
  `comments` varchar(255) DEFAULT NULL COMMENT '应用描述',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '创建人工号',
  `created_date` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '更新人工号',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_ada_app_id` (`app_id`),
  UNIQUE KEY `uk_ada_app_code` (`app_code`),
  KEY `idx_ada_state` (`state`)
) COMMENT='应用信息表';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `int unsigned auto_increment`，`app_id` 和 `app_code` 为 `varchar(60)` 且有唯一索引，`app_secret` 为 `varchar(120)`，`state` 为 `tinyint unsigned`，数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **唯一约束：** `app_id` 和 `app_code` 均有唯一索引，确保应用 ID 和编码的唯一性，符合业务逻辑。
*   **索引：** `state` 存在普通索引 `idx_ada_state`，有利于按状态查询。
*   **字段注释：** 所有字段均包含注释，符合强制要求。
*   **安全性：** `app_secret` 字段明确注释为“应用密钥(加密存储)”，符合安全规范。

**优化建议：**
*   **无明显性能或安全隐患。** 设计合理。
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view)

### 5.26. `ai_dw_app_access_token` 表
```sql
CREATE TABLE `ai_dw_app_access_token` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `token` varchar(255) NOT NULL COMMENT 'Access Token',
  `app_id` int(10) unsigned NOT NULL COMMENT '应用ID',
  `token_type` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT 'Token类型：1-系统级，2-用户级 用户级要求调用接口时必须携带用户信息',
  `effective_at` datetime DEFAULT NULL COMMENT 'token生效时间，为空表示立即生效',
  `expires_at` datetime DEFAULT NULL COMMENT '过期时间，NULL表示永久有效',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：0-无效，1-有效',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录创建者id',
  `created_date` datetime NOT NULL COMMENT '记录创建时间',
  `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者ID',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_adat_token` (`token`),
  KEY `idx_asat_app_id` (`app_id`)
) COMMENT='Token管理表';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `int unsigned auto_increment`，`token` 为 `varchar(255)` 且有唯一索引，`app_id` 为 `int unsigned`，`token_type` 和 `state` 为 `tinyint unsigned`，时间戳字段使用 `datetime`，数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **唯一约束：** `token` 具有唯一索引 `uk_adat_token`，确保 Access Token 的唯一性，符合业务逻辑。
*   **索引：** `app_id` 存在普通索引 `idx_asat_app_id`，有利于按应用 ID 查询。
*   **字段注释：** 所有字段均包含注释，符合强制要求。
*   **安全性：** `token` 字段存储 Access Token，**强烈建议进行加密存储。**

**优化建议：**
*   **`token` 字段安全性：** `Access Token` 是敏感信息，**强烈建议对其进行加密存储。**
*   **索引：** 如果经常需要按 `expires_at` 或 `state` 进行查询，可以考虑为这些字段添加普通索引。
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库运维和安全规范(MySQL分册).md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库运维和安全规范(MySQL分册).md&owner=$owner&repo=$repo&action=view)

### 5.27. `ai_dw_app_api_rel` 表
```sql
CREATE TABLE `ai_dw_app_api_rel` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` int(10) unsigned NOT NULL COMMENT '应用ID',
  `api_id` int(10) unsigned NOT NULL COMMENT 'API ID',
  `state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '授权人ID',
  `created_date` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者ID',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_api` (`app_id`,`api_id`)
) COMMENT='应用API权限关联表';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `bigint unsigned auto_increment`，`app_id` 和 `api_id` 为 `int unsigned`，`state` 为 `tinyint`，数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **唯一约束：** `app_id` 和 `api_id` 的联合唯一索引 `uk_app_api` 确保了应用和 API 关联的唯一性，符合业务逻辑。
*   **字段注释：** 所有字段均包含注释，符合强制要求。

**优化建议：**
*   **无明显性能或安全隐患。** 设计合理。
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view)

### 5.28. `ai_dw_app_approval_records` 表
```sql
CREATE TABLE `ai_dw_app_approval_records` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` int(10) unsigned NOT NULL COMMENT '应用主键ID',
  `applicant_id` bigint(20) unsigned NOT NULL COMMENT '申请人主键标识',
  `approver_id` bigint(20) unsigned DEFAULT NULL COMMENT '审批人主键标识',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '审批状态：0-待审批，1-通过，2-拒绝',
  `comments` varchar(255) DEFAULT NULL COMMENT '审批意见',
  `applied_time` datetime NOT NULL COMMENT '申请时间',
  `approved_time` datetime DEFAULT NULL COMMENT '审批时间',
  PRIMARY KEY (`id`),
  KEY `idx_adaar_app_id` (`app_id`)
) COMMENT='应用审批记录表';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `int unsigned auto_increment`，`app_id` 为 `int unsigned`，`applicant_id` 和 `approver_id` 为 `bigint unsigned`，`state` 为 `tinyint unsigned`，时间戳字段使用 `datetime`，数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **索引：** `app_id` 存在普通索引 `idx_adaar_app_id`，有利于按应用 ID 查询。
*   **字段注释：** 所有字段均包含注释，符合强制要求。

**优化建议：**
*   **索引：** 如果经常需要按 `applicant_id`、`approver_id` 或 `state` 进行查询，可以考虑为这些字段添加普通索引。
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view)

### 5.29. `ai_dw_project` 表
```sql
CREATE TABLE `ai_dw_project` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '项目表主键',
  `wiki_id` bigint(20) unsigned NOT NULL COMMENT 'wiki_info表主键id',
  `project_code` varchar(60) COLLATE utf8mb4_bin NOT NULL COMMENT '项目编码',
  `project_name` varchar(120) COLLATE utf8mb4_bin NOT NULL COMMENT '项目名称',
  `pm_id` bigint(20) unsigned DEFAULT NULL COMMENT '项目负责人ID',
  `pm_name` varchar(120) COLLATE utf8mb4_bin NOT NULL COMMENT '项目负责人名称',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '记录创建者主键ID',
  `created_date` datetime NOT NULL COMMENT '记录创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者主键ID',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新者',
  PRIMARY KEY (`id`),
  KEY `idx_adp_wiki_id` (`wiki_id`)
) COMMENT='项目表';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `int unsigned auto_increment`，`wiki_id` 为 `bigint unsigned`，`project_code`、`project_name`、`pm_name` 为 `varchar`，数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **索引：** `wiki_id` 存在普通索引 `idx_adp_wiki_id`，有利于按 Wiki ID 查询。
*   **字符集：** `project_code`、`project_name`、`pm_name` 指定了 `COLLATE utf8mb4_bin`。虽然规范中建议“创建表不能指定字符集和collation类型(使用数据库默认字符集)”，但如果整个数据库或项目统一使用 `utf8mb4_bin` 字符集，则可以接受。如果数据库默认字符集不是 `utf8mb4_bin`，则可能存在不一致问题。
*   **字段注释：** 所有字段均包含注释，符合强制要求。
*   **潜在问题：** `project_code` 作为项目编码，在业务上应具有唯一性。

**优化建议：**
*   **添加唯一约束：** 为 `project_code` 字段添加唯一索引，以保证项目编码的唯一性。
    ```sql
    ALTER TABLE `ai_dw_project` ADD UNIQUE KEY `uk_project_code` (`project_code`);
    ```
*   **字符集一致性：** 确保整个数据库的字符集和 collation 保持一致，避免因字符集不一致导致的潜在问题。
*   **索引：** 如果经常需要按 `project_code` 或 `pm_id` 进行查询，可以考虑为这些字段添加普通索引。
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view), [code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/context/数据库模型设计规范(MySQL分册).md&owner=$owner&repo=$repo&action=view)

### 5.30. `ai_dw_app_token_user_rel` 表
```sql
CREATE TABLE `ai_dw_app_token_user_rel` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'token与user关联关系表主键',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户表主键',
  `token_id` int(10) unsigned NOT NULL COMMENT '应用token表主键',
  `created_by` int(10) unsigned NOT NULL COMMENT '记录创建者标识',
  `created_date` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_adatur_tid_uid` (`token_id`,`user_id`)
) COMMENT='用户级token与用户关联关系表';
```
**分析：**
*   **命名规范：** 表名和字段名符合规范。
*   **字段数据类型：** `id` 为 `int unsigned auto_increment`，`user_id` 为 `bigint unsigned`，`token_id` 和 `created_by` 为 `int unsigned`，数据类型选择合理。
*   **主键：** 存在单一主键 `id`，且为自增列，符合规范。
*   **唯一约束：** `token_id` 和 `user_id` 的联合唯一索引 `ux_adatur_tid_uid` 确保了 token 和用户关联的唯一性，符合业务逻辑。
*   **字段注释：** 所有字段均包含注释，符合强制要求。

**优化建议：**
*   **无明显性能或安全隐患。** 这是一个典型的多对多关联表，设计合理。
    *   Sources: [code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md](/api/file-action?filePath=code/whale-deepwiki-develop/docs/use-cases/check_sql/check_sql_prp.md&owner=$owner&repo=$repo&action=view)

## 6. 总结
本次对 `ddl.sql` 脚本的分析，主要发现了以下几类优化点：
*   **索引优化：** 根据业务查询模式，为一些常用查询字段或组合字段添加索引，以提高查询效率。
*   **唯一约束：** 为业务上具有唯一性的字段添加唯一约束，确保数据的一致性。
*   **敏感信息存储：** 对 `app_secret`、`ai_api_key`、`dev_cloud_token`、`token` 等敏感信息，建议进行加密存储，并确保应用程序层面的安全处理。
*   **大文本字段处理：** 对于 `text` 或 `json` 类型的大文本字段，根据实际查询需求和数据量，考虑是否需要引入全文索引或外部存储方案。
*   **外键处理：** 根据分布式数据库规范，建议移除 `global_job_state` 和 `job_lock` 表中的外键约束，并在应用程序层面维护数据一致性。
*   **废弃字段清理：** 及时清理表中的废弃字段，保持表结构的精简和清晰。

通过实施这些优化建议，可以有效提升数据库性能、增强数据完整性和安全性，并提高代码的可维护性。

## 7. 附录
*   《SQL开发规范(MySQL分册)》
*   《数据库模型设计规范(MySQL分册)》
*   《数据库运维和安全规范(MySQL分册)》
