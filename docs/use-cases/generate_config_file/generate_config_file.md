# 核心原则（Core Principles）
- 上下文为王：包含所有必要的文档、示例和注意事项
- 验证循环：提供 AI 可以运行和修复的可执行测试/检查
- 信息密集：使用代码库中的关键词和模式
- 渐进成功：从简单开始，验证，然后增强
  
# 目标（Goal）
 为当前项目编写一个configuration.md文档，指导研发、测试、运维交付人员等进行项目配置。

## 功能（Features）
 - 编写一个配置文件文档，存放到docs/i-docs目录下，使用markdown格式
 - 阅读整体的项目内容，然后编写文档内容
 - 每个配置项必须包含：功能描述、取值类型、默认值、取值范围，使用表格进行描述
 - 针对配置文件中存在的配置项，在源码中没有使用，在配置项的描述中，给出明确的说明

## 为什么编写（Why）
  - 该文档可以指导研发人员配置本地开发环境
  - 该文档可以指导测试人员部署测试环境
  - 该文档可以指导交付人员部署交付环境
  - 该文档可以指导运维人员进行运维排障

# 所需要的上下文(All Needed Context)

## 参考示例
配置文件的示例参考docs\use-cases\generate_config_file\example\configuration_gemini_cli.md

## 参考文档
  - 环境变量
    - 文件：env-example
    - 说明：分析环境变量相关配置
 - 后端配置变量
   - 文件：api\settings.yaml
   - 说明：分析后端服务相关配置项
 - chat模式支持的模型配置
   - 文件：api\config\generator.json
   - 说明：分析chat功能支持的AI模型列表
 - dockerfile配置
   - 文件：Dockerfile.base、Dockerfile
   - 说明：分析基础镜像和deepwiki的镜像文件中的环境变量，base是基础镜像


