# DeepWiki 配置指南

> 本文档面向研发/测试/交付/运维人员，提供 DeepWiki 项目的完整配置说明、默认值与使用指南。

## 📋 目录

- [快速开始](#快速开始)
- [环境变量配置](#环境变量配置)
- [后端配置文件](#后端配置文件)
- [模型配置](#模型配置)
- [Docker 配置](#docker-配置)
- [常见问题](#常见问题)
- [配置校验](#配置校验)

## 🚀 快速开始

### 基础配置步骤

1. **环境变量配置**
   - 在项目根目录创建 `.env` 文件（参考 `env-example`）
   - 配置必要的 API 密钥和基础 URL

2. **后端配置**
   - 可选通过 `api/settings.yaml` 管理后端运行参数
   - 包含数据库、会话、安全、K8s、Redis 等配置

3. **容器部署**
   - `Dockerfile` 中的环境变量影响前后端对接地址与启动端口
   - 支持构建时和运行时配置注入

### 最小化配置示例

```bash
# 基础环境变量
OPENAI_API_KEY=your_api_key_here
NEXT_PUBLIC_SERVER_BASE_URL=http://localhost:8001
```

---

## 🔧 环境变量配置

> 参考样例：`env-example`

### 一、AI 模型与供应商配置

#### OpenAI 兼容服务

| 变量名 | 类型 | 默认值 | 说明 | 使用位置 |
|--------|------|--------|------|----------|
| `OPENAI_API_KEY` | string | - | OpenAI 或兼容服务 API Key | 后端与部分客户端 |
| `OPENAI_BASE_URL` | string | `https://api.openai.com/v1` | OpenAI 兼容接口基础 URL | 后端客户端 |

#### 其他 AI 服务

| 变量名 | 类型 | 默认值 | 说明 | 使用位置 |
|--------|------|--------|------|----------|
| `OPENROUTER_API_KEY` | string | - | OpenRouter API Key | `api/openrouter_client.py` |
| `GOOGLE_API_KEY` | string | - | Google Gemini API Key | `api/main.py`, `api/sse_chat.py`, `api/websocket_wiki.py` |

> ⚠️ **注意**：以下变量当前未被源码使用，请避免使用：
> - `OPENAI_API_BASE`：建议使用 `OPENAI_BASE_URL`
> - `OPENAI_API_BASE_URL`：当前代码未读取

### 二、DocChain 集成配置

| 变量名 | 类型 | 默认值 | 说明 | 使用位置 |
|--------|------|--------|------|----------|
| `USE_DOCCHAIN` | boolean | `false` | 是否启用 DocChain | `api/config.py` |
| `DOCCHAIN_BASE_URL` | string | - | DocChain 服务地址 | `api/api.py`, `api/data_pipeline.py`, 前端 API 路由 |
| `DOCCHAIN_API_KEY` | string | - | DocChain API Key | 后端及前端路由 |
| `DOCCHAIN_DIRECT_MODE` | boolean | `false` | 是否直连 DocChain | 后端及前端路由 |

#### 实验/生产环境 DocChain 配置

| 变量名 | 类型 | 说明 | 使用位置 |
|--------|------|------|----------|
| `LAB_DOCCHAIN_BASE_URL` | string | 实验/生产 DocChain 地址 | 后端及前端路由 |
| `LAB_DOCCHAIN_API_KEY` | string | 实验/生产 DocChain API Key | 后端及前端路由 |
| `LAB_DOCCHAIN_DIRECT_MODE` | boolean | 实验/生产直连模式 | 后端及前端路由 |

### 三、前端对接配置

| 变量名 | 类型 | 默认值 | 说明 | 使用位置 |
|--------|------|--------|------|----------|
| `NEXT_PUBLIC_SERVER_BASE_URL` | string | - | 前端访问后端 API 的基础地址 | `next.config.ts`, `src/utils/*` |
| `NEXT_PUBLIC_DXP_BASE_URL` | string | - | 可选外链地址 | `next.config.ts`, `src/utils/config.ts` |

---

## ⚙️ 后端配置文件

> 配置文件位置：`api/settings.yaml`

### 一、数据库配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `database.url.driver_name` | string | `mysql+pymysql` | 数据库驱动 |
| `database.url.username` | string | - | 数据库用户名 |
| `database.url.password` | string | - | 数据库密码 |
| `database.url.host` | string | - | 数据库主机 |
| `database.url.port` | integer | - | 数据库端口 |
| `database.url.database` | string | - | 数据库名称 |
| `database.connect_timeout` | integer | `10` | 连接超时秒数 |
| `database.pool.size` | integer | `20` | 连接池保持的连接数 |
| `database.pool.max_overflow` | integer | `20` | 允许临时扩展的连接数 |
| `database.pool.recycle` | integer | `3600` | 连接自动回收时间(秒) |
| `database.pool.pre_ping` | boolean | `true` | 执行前检查连接活性 |
| `database.pool.timeout` | integer | `30` | 获取连接超时时间(秒) |
| `database.echo` | boolean | `false` | 是否输出SQL日志 |

### 二、SSO 单点登录配置

| 配置项 | 类型 | 说明 |
|--------|------|------|
| `sso.app_key` | string | 平台 SSO 应用密钥 |
| `sso.app_secret` | string | 平台 SSO 应用密钥 |
| `sso.base_url` | string | 平台 SSO 基础 URL |

### 三、会话配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `session.timeout` | integer | `3600` | 服务端 session 超时(秒) |
| `session.cookie.max-age` | integer | `-1` | 浏览器 Cookie 有效期(秒，-1表示会话级) |

### 四、Wiki 任务管理配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `wiki_generation_mode` | string | `thread` | 生成任务执行模式 |
| `wiki_jobs.global_max_concurrent` | integer | `6` | 全局最大并发任务数 |
| `wiki_jobs.instance_max_concurrent` | integer | `2` | 单实例最大并发任务数 |
| `wiki_jobs.timeout_minutes` | integer | `180` | 任务超时时间(分钟) |
| `wiki_jobs.lock_timeout_minutes` | integer | `90` | 分布式锁超时时间(分钟) |
| `wiki_jobs.heartbeat_timeout_minutes` | integer | `1` | 心跳检测阈值(分钟) |
| `wiki_jobs.cleanup_interval_seconds` | integer | `45` | 清理检查间隔(秒) |
| `wiki_jobs.enable_distributed_lock` | boolean | `true` | 启用分布式锁 |
| `wiki_jobs.consistency_check_strict` | boolean | `false` | 启用严格的一致性检查 |
| `wiki_jobs.consistency_check_grace_period` | integer | `300` | 一致性检查宽限期(秒) |
| `wiki_jobs.consistency_check_threshold` | integer | `2` | 触发一致性检查的差异阈值 |
| `wiki_jobs.fast_mode` | boolean | `true` | 快速模式开关，跳过RAG直接生成 |

### 五、安全配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `app.security.developMode` | boolean | `false` | 开发模式，为true时不拦截请求 |
| `app.security.enable_role_service_privilege` | boolean | `true` | 是否开启角色服务权限 |
| `app.security.jwt.secret_key` | string | - | JWT 密钥 |
| `app.security.jwt.expires_minutes` | integer | `1440` | token过期时间(分钟) |
| `app.security.jwt.token_min_refresh_interval` | integer | `300` | token最小刷新间隔(秒) |
| `app.security.jwt.refresh_token` | boolean | `true` | 是否启用token刷新功能 |
| `app.security.jwt.leeway` | integer | `5` | token解析时间宽容值(分钟) |
| `app.security.aes.secret_key` | string | - | AES加密密钥 |

#### 安全放行规则配置

| 配置项 | 类型 | 说明 |
|--------|------|------|
| `app.security.excludeUrl` | object | 不需要拦截的地址规则(正则表达式) |

### 六、Kubernetes 配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `kubernetes.api_server` | string | - | K8s API 地址 |
| `kubernetes.namespace` | string | - | 命名空间 |
| `kubernetes.environment` | string | - | 环境标识 |
| `kubernetes.verify_ssl` | boolean | `false` | 是否校验证书 |
| `kubernetes.base_path` | string | - | 容器内基础路径 |
| `kubernetes.job.idle_timeout_minutes` | integer | `60` | Job空闲超时时间(分钟) |
| `kubernetes.job.cleanup_interval_minutes` | integer | `10` | Job清理检查间隔(分钟) |
| `kubernetes.job.default_image` | string | - | 默认Job镜像 |
| `kubernetes.job.max_containers` | integer | `10` | 全局最大容器数量限制 |
| `kubernetes.job.per_user_max_containers` | integer | `2` | 每个用户最大容器数量限制 |
| `kubernetes.job.min_occupancy_minutes` | integer | `5` | 最小占用安全期(分钟) |
| `kubernetes.job.max_lifetime_minutes` | integer | `10` | 最大生命周期(分钟) |

#### 容器资源配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `kubernetes.job.resources.limits.cpu` | string | `100m` | CPU限制 |
| `kubernetes.job.resources.limits.memory` | string | `256Mi` | 内存限制 |
| `kubernetes.job.resources.requests.cpu` | string | `10m` | CPU请求 |
| `kubernetes.job.resources.requests.memory` | string | `256Mi` | 内存请求 |

#### 挂载配置

| 配置项 | 类型 | 说明 |
|--------|------|------|
| `kubernetes.job.volumes.code_path` | string | 代码目录(只读) |
| `kubernetes.job.volumes.workspace_path` | string | 工作空间目录(读写) |
| `kubernetes.job.volumes.gemini_path` | string | Gemini数据目录 |

### 七、Redis 配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `redis.enabled` | boolean | `false` | Redis开关 |
| `redis.mode` | string | `cluster` | 模式：single/sentinel/cluster |
| `redis.key_prefix` | string | - | 业务前缀 |
| `redis.default_ttl` | integer | `3600` | 默认过期时间(秒) |
| `redis.serializer` | string | `json` | 序列化方式：json/pickle |
| `redis.connection.decode_responses` | boolean | `false` | 是否解码响应 |
| `redis.connection.encoding` | string | `utf-8` | 编码格式 |
| `redis.connection.socket_timeout` | float | `5.0` | socket超时时间(秒) |
| `redis.connection.socket_connect_timeout` | float | `5.0` | socket连接超时时间(秒) |
| `redis.connection.socket_keepalive` | boolean | `true` | 是否启用socket保活 |
| `redis.pool.max_connections` | integer | `50` | 最大连接数 |
| `redis.pool.retry_on_timeout` | boolean | `true` | 超时重试 |
| `redis.health_check.enabled` | boolean | `true` | 启用健康检查 |
| `redis.health_check.interval` | integer | `30` | 健康检查间隔(秒) |
| `redis.retry.retries` | integer | `6` | 重试次数 |

#### 单机模式配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `redis.single.host` | string | `localhost` | Redis主机 |
| `redis.single.port` | integer | `6379` | Redis端口 |
| `redis.single.db` | integer | `0` | 数据库编号 |
| `redis.single.password` | string | `null` | 密码 |

#### 哨兵模式配置

| 配置项 | 类型 | 说明 |
|--------|------|------|
| `redis.sentinel.sentinels` | array | 哨兵节点列表 |
| `redis.sentinel.service_name` | string | 主节点服务名 |
| `redis.sentinel.password` | string | 主节点密码 |
| `redis.sentinel.sentinel_password` | string | 哨兵密码 |
| `redis.sentinel.db` | integer | 数据库编号 |

#### 集群模式配置

| 配置项 | 类型 | 说明 |
|--------|------|------|
| `redis.cluster.nodes` | array | 集群节点列表 |
| `redis.cluster.password` | string | 集群密码 |

### 八、文件管理配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `file_manager.max_file_size` | integer | `10485760` | 最大文件大小(字节) |
| `file_manager.allowed_extensions` | array | - | 允许的文件扩展名 |
| `file_manager.blocked_extensions` | array | - | 禁止的文件扩展名 |
| `file_manager.blocked_patterns` | array | - | 禁止的文件名模式 |
| `file_manager.allowed_hidden` | array | - | 允许的隐藏文件/目录 |

### 九、全局配置

| 配置项 | 类型 | 说明 |
|--------|------|------|
| `global_config.monitor_url` | string | 监控大屏地址 |
| `global_config.operation_url` | string | 运营大屏地址 |

### 十、ZCM DevSpace 配置

| 配置项 | 类型 | 说明 |
|--------|------|------|
| `zcm.devspace.base_url` | string | DevSpace 基础 URL |
| `zcm.devspace.token` | string | DevSpace Token |

---

## 🤖 模型配置

### 一、生成器配置 (api/config/generator.json)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `default_provider` | string | `whalecloud` | 默认提供商 |
| `providers.{provider}.client_class` | string | - | 客户端实现类 |
| `providers.{provider}.default_model` | string | - | 默认模型 |
| `providers.{provider}.supportsCustomModel` | boolean | - | 是否支持自定义模型 |
| `providers.{provider}.models.{model}.temperature` | float | `0.4` | 温度参数 |
| `providers.{provider}.models.{model}.top_p` | float | `0.8` | Top-p 参数 |

### 二、嵌入模型配置 (api/config/embedder.json)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `embedder.client_class` | string | `OpenAIClient` | 嵌入客户端类 |
| `embedder.batch_size` | integer | `500` | 批处理大小 |
| `embedder.model_kwargs.model` | string | `text-embedding-3-small` | 嵌入模型名称 |
| `embedder.model_kwargs.dimensions` | integer | `256` | 嵌入维度 |
| `embedder.model_kwargs.encoding_format` | string | `float` | 编码格式 |
| `retriever.top_k` | integer | `20` | 检索返回数量 |
| `text_splitter.split_by` | string | `word` | 分割方式 |
| `text_splitter.chunk_size` | integer | `350` | 分块大小 |
| `text_splitter.chunk_overlap` | integer | `100` | 分块重叠 |

### 三、语言配置 (api/config/lang.json)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `supported_languages` | object | - | 支持的语言列表 |
| `default` | string | `en` | 默认语言 |

---

## 🐳 Docker 配置

### 一、Dockerfile 环境变量

| 变量名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `PORT` | integer | `3006` | 前端服务端口 |
| `NODE_ENV` | string | `production` | Node 运行环境 |
| `BASE_URL` | string | `http://localhost` | 基础主机名 |
| `SERVER_BASE_URL` | string | - | 后端服务地址 |
| `NEXT_PUBLIC_SERVER_BASE_URL` | string | - | 前端访问后端地址 |
| `NEXT_PUBLIC_DXP_BASE_URL` | string | - | 外链地址 |

### 二、构建时参数

| 参数名 | 说明 |
|--------|------|
| `NEXT_PUBLIC_SERVER_BASE_URL` | 构建时注入到前端静态产物 |
| `NEXT_PUBLIC_DXP_BASE_URL` | 构建时注入到前端静态产物 |

---

## ❓ 常见问题

### 一、配置问题

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 前端无法连接后端 | `NEXT_PUBLIC_SERVER_BASE_URL` 配置错误 | 检查环境变量配置，确保地址正确 |
| AI 模型调用失败 | API Key 未配置或无效 | 检查 `OPENAI_API_KEY` 等密钥配置 |
| DocChain 连接失败 | DocChain 服务地址或密钥错误 | 检查 `DOCCHAIN_BASE_URL` 和 `DOCCHAIN_API_KEY` |
| 数据库连接失败 | 数据库配置参数错误 | 检查 `api/settings.yaml` 中的数据库配置 |
| Redis 连接失败 | Redis 配置错误或服务未启动 | 检查 Redis 配置和服务状态 |

### 二、性能优化

| 配置项 | 建议值 | 说明 |
|--------|--------|------|
| `database.pool.size` | 20-50 | 根据并发量调整连接池大小 |
| `wiki_jobs.global_max_concurrent` | 6-12 | 根据服务器性能调整并发数 |
| `redis.pool.max_connections` | 50-100 | 根据 Redis 服务器性能调整 |
| `kubernetes.job.resources.limits.memory` | 512Mi-1Gi | 根据任务复杂度调整内存限制 |

---

## ✅ 配置校验

### 一、环境变量校验

```bash
# 检查关键环境变量
printenv | grep -E "OPENAI_|GOOGLE_API_KEY|OPENROUTER|DOCCHAIN|NEXT_PUBLIC_SERVER_BASE_URL|NEXT_PUBLIC_DXP_BASE_URL"

# 基础健康检查
curl -sf http://localhost:8001/health && echo "后端服务正常" || echo "后端服务异常"
```

### 二、配置验证清单

| 检查项 | 命令/方法 | 预期结果 |
|--------|-----------|----------|
| 环境变量加载 | `printenv | grep OPENAI` | 显示配置的 API Key |
| 后端服务状态 | `curl http://localhost:8001/health` | 返回 200 状态码 |
| 数据库连接 | 查看后端日志 | 无连接错误信息 |
| Redis 连接 | 查看后端日志 | 无连接错误信息 |
| 前端页面访问 | 浏览器访问 | 页面正常加载 |

### 三、容器运行示例

```bash
# 构建镜像
docker build -t deepwiki:local .

# 运行容器
docker run --rm -p 3006:3006 -p 8001:8001 \
  -e OPENAI_API_KEY=your_api_key \
  -e OPENAI_BASE_URL=https://api.openai.com/v1 \
  -e NEXT_PUBLIC_SERVER_BASE_URL=http://localhost:8001 \
  deepwiki:local
```

---

## 📝 配置最佳实践

### 一、开发环境配置

| 配置项 | 推荐值 | 说明 |
|--------|--------|------|
| `app.security.developMode` | `true` | 开发时关闭请求拦截 |
| `database.echo` | `true` | 开发时显示 SQL 日志 |
| `wiki_jobs.enable_distributed_lock` | `false` | 本地开发时关闭分布式锁 |
| `redis.enabled` | `false` | 本地开发时可关闭 Redis |

### 二、生产环境配置

| 配置项 | 推荐值 | 说明 |
|--------|--------|------|
| `app.security.developMode` | `false` | 生产环境必须开启安全拦截 |
| `database.echo` | `false` | 生产环境关闭 SQL 日志 |
| `wiki_jobs.enable_distributed_lock` | `true` | 生产环境开启分布式锁 |
| `redis.enabled` | `true` | 生产环境开启 Redis 缓存 |

### 三、安全配置建议

| 配置项 | 建议 | 说明 |
|--------|------|------|
| JWT 密钥 | 使用强随机字符串 | 定期轮换密钥 |
| AES 密钥 | 使用 16 字节密钥 | 确保密钥安全存储 |
| 数据库密码 | 使用复杂密码 | 定期更换密码 |
| API 密钥 | 使用环境变量存储 | 避免硬编码在配置文件中 |

---

*最后更新：2024年12月*