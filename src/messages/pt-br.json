{"common": {"appName": "DeepWiki", "tagline": "Documentação com IA", "generateWiki": "Gerar Wiki", "processing": "Processando...", "error": "Erro", "submit": "Enviar", "cancel": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "loading": "Carregando..."}, "loading": {"initializing": "Inicializando geração da wiki...", "fetchingStructure": "Obtendo estrutura do repositório...", "determiningStructure": "Determinando estrutura da wiki...", "clearingCache": "Limpando cache do servidor...", "preparingDownload": "Aguarde enquanto preparamos seu download..."}, "home": {"welcome": "Bem-vindo ao DeepWiki", "welcomeTagline": "Documentação com IA para seus repositórios de código", "description": "Gere documentação completa a partir de repositórios GitHub, GitLab ou Bitbucket com apenas alguns cliques.", "quickStart": "<PERSON><PERSON><PERSON>", "enterRepoUrl": "Digite uma URL de repositório em um destes formatos:", "advancedVisualization": "Visualização Avançada com Diagramas Mermaid", "diagramDescription": "O DeepWiki gera automaticamente diagramas interativos para ajudar você a entender a estrutura e os relacionamentos do código:", "flowDiagram": "Diagrama de Fluxo", "sequenceDiagram": "Diagrama de Sequência"}, "form": {"repository": "Repositório", "configureWiki": "Configurar Wiki", "repoPlaceholder": "propriet<PERSON><PERSON>/repo ou URL do GitHub/GitLab/Bitbucket", "wikiLanguage": "Idioma da Wiki", "modelOptions": "Opções de Modelo", "modelProvider": "<PERSON><PERSON><PERSON>", "modelSelection": "Seleção de Modelo", "wikiType": "Tipo de Wiki", "comprehensive": "Abrangente", "concise": "Concisa", "comprehensiveDescription": "Wiki detalhada com seções estruturadas e mais páginas", "conciseDescription": "Wiki simplificada com menos páginas e informações essenciais", "providerGoogle": "Google", "providerOpenAI": "OpenAI", "providerOpenRouter": "OpenRouter", "providerOllama": "<PERSON><PERSON><PERSON> (Local)", "localOllama": "Modelo <PERSON>ma Local", "experimental": "Experimental", "useOpenRouter": "Usar API OpenRouter", "openRouterModel": "<PERSON><PERSON> OpenRouter", "useOpenai": "Usar API OpenAI", "openaiModel": "Modelo OpenAI", "useCustomModel": "Usar modelo personalizado", "customModelPlaceholder": "Digite o nome do modelo personalizado", "addTokens": "+ Adicionar tokens de acesso para repositórios privados", "hideTokens": "- Ocultar tokens de acesso", "accessToken": "Token de Acesso para Repositórios Privados", "selectPlatform": "Selecionar Plataforma", "personalAccessToken": "Token de Acesso Pessoal do {platform}", "tokenPlaceholder": "Digite seu token do {platform}", "tokenSecurityNote": "O token é armazenado apenas na memória e nunca é persistido.", "defaultFiltersInfo": "Os filtros padrão incluem diretórios comuns como node_modules, .git e arquivos de artefatos de compilação comuns.", "fileFilterTitle": "Configuração de Filtro de Arquivos", "advancedOptions": "Opções Avançadas", "viewDefaults": "Ver Filt<PERSON>", "showFilters": "<PERSON><PERSON>", "hideFilters": "O<PERSON>lta<PERSON>", "excludedDirs": "Diretórios a Excluir", "excludedDirsHelp": "Um caminho de diretório por linha. Caminhos começando com ./ são relativos à raiz do repositório.", "enterExcludedDirs": "Digite os diretórios excluídos, um por linha...", "excludedFiles": "Arquivos a Excluir", "excludedFilesHelp": "Um nome de arquivo por linha. Curingas (*) são suportados.", "enterExcludedFiles": "Digite os arquivos excluídos, um por linha...", "defaultFilters": "Arquivos e Diretórios Excluídos por Padrão", "directories": "Diretórios", "files": "<PERSON>r<PERSON><PERSON>", "scrollToViewMore": "Role para ver mais", "changeModel": "Alterar <PERSON>o", "defaultNote": "Esses padrões já estão aplicados. Adicione suas exclusões personalizadas acima.", "hideDefault": "<PERSON><PERSON><PERSON><PERSON>", "viewDefault": "<PERSON><PERSON>", "includedDirs": "Diretórios Incluídos", "includedFiles": "Arquivos Incluídos", "enterIncludedDirs": "Digite os diretórios incluídos, um por linha...", "enterIncludedFiles": "Digite os arquivos incluídos, um por linha...", "filterMode": "<PERSON><PERSON>", "excludeMode": "Excluir <PERSON>", "includeMode": "Incluir <PERSON>", "excludeModeDescription": "Especificar caminhos a serem excluídos do processamento (comportamento padrão)", "includeModeDescription": "Especificar apenas os caminhos a serem incluídos, ignorando todos os outros", "authorizationCode": "Código de Autorização", "authorizationRequired": "A autenticação é necessária para gerar a wiki."}, "footer": {"copyright": "DeepWiki - Documentação com IA para repositórios de código"}, "ask": {"placeholder": "Faça uma pergunta sobre este repositório...", "askButton": "Pergun<PERSON>", "deepResearch": "Pesquisa Aprofundada", "researchInProgress": "Pesquisa em andamento...", "continueResearch": "<PERSON><PERSON><PERSON><PERSON>", "viewPlan": "Ver Plano", "viewUpdates": "Ver Atualizações", "viewConclusion": "<PERSON><PERSON>"}, "repoPage": {"refreshWiki": "Atualizar Wiki", "confirmRefresh": "Confirmar <PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "home": "Início", "errorTitle": "Erro", "errorMessageDefault": "Verifique se o seu repositório existe e é público. Formatos válidos são \"proprietário/repo\", \"https://github.com/proprietário/repo\", \"https://gitlab.com/proprietário/repo\", \"https://bitbucket.org/proprietário/repo\", ou caminhos de pastas locais como \"C:\\\\caminho\\\\para\\\\pasta\" ou \"/caminho/para/pasta\".", "backToHome": "Voltar ao Início", "exportWiki": "Exportar Wiki", "exportAsMarkdown": "Exportar como Markdown", "exportAsJson": "Exportar como JSON", "pages": "<PERSON><PERSON><PERSON><PERSON>", "relatedFiles": "Arquivos Relacionados:", "relatedPages": "Páginas Relacionadas:", "selectPagePrompt": "Selecione uma página da navegação para ver seu conteúdo", "askAboutRepo": "Faça perguntas sobre este repositório"}, "nav": {"wikiProjects": "Projetos Wiki"}, "projects": {"title": "Projetos Wiki Processados", "searchPlaceholder": "Pesquisar projetos por nome, proprietário ou repositório...", "noProjects": "Nenhum projeto encontrado no cache do servidor. O cache pode estar vazio ou o servidor encontrou um problema.", "noSearchResults": "Nenhum projeto corresponde aos seus critérios de pesquisa.", "processedOn": "Processado em:", "loadingProjects": "Carregando projetos...", "errorLoading": "Erro ao carregar projetos:", "backToHome": "Voltar ao Início", "browseExisting": "Navegar por Projetos Existentes", "existingProjects": "Projetos Existentes", "recentProjects": "Projetos Recentes"}}