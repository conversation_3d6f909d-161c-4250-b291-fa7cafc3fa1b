{"common": {"appName": "DeepWiki", "tagline": "AI驱动的文档", "generateWiki": "生成Wiki", "processing": "处理中...", "error": "错误", "submit": "提交", "cancel": "取消", "close": "关闭", "loading": "加载中...", "remove": "移除", "invalidApiKey": "大模型令牌无效，请前往设置页面正确配置。", "requestFail": "请求失败", "requestSuccess": "请求成功", "confirm": "确认", "employeeId": "工号", "department": "部门", "show": "展示", "hide": "隐藏", "deepwikiDescription": "DeepWiki提供智能感知代码、提炼代码知识、一键生成产品wiki，智能驱动研发新范式，赋能生产提效，已接入", "deepwikiCount": "个代码仓库", "allStatus": "所有状态", "pending": "待处理", "completed": "已完成", "failed": "失败", "yes": "是", "no": "否", "entered": "已输入", "notEntered": "未输入", "allJobStatus": "所有Job状态", "runningJobs": "运行中的Job", "failedJobs": "失败的Job", "cancelledJobs": "已取消的Job", "pendingJobs": "待处理的Job", "allWikiData": "所有Wiki数据", "wikiData": "Wiki数据", "hasData": "有数据", "noData": "无数据", "setToCompleted": "设置为已完成", "setToProcessing": "设置为处理中", "setToPending": "设置为待处理", "setToFailed": "设置为失败", "errorMessage": "错误信息", "stage": "阶段", "progress": "进度", "overall": "总体", "refresh": "刷新", "networkError": "网络错误", "unknownError": "未知错误", "deleteFailed": "删除失败", "type": "类型"}, "loading": {"initializing": "初始化Wiki生成...", "fetchingCache": "检查缓存中的Wiki...", "fetchingStructure": "获取仓库结构...", "determiningStructure": "正在上传并生成知识文档，过程可能需要一点时间，请您耐心等待...", "clearingCache": "清除服务器缓存...", "preparingDownload": "请等待，我们正在准备您的下载..."}, "home": {"welcome": "欢迎使用DeepWiki", "welcomeTagline": "为代码仓库提供AI驱动的文档", "description": "只需一次点击，即可从GitHub、GitLab或Bitbucket仓库生成全面的文档。", "quickStart": "快速开始", "enterRepoUrl": "请以下列格式之一输入仓库URL：", "advancedVisualization": "使用Mermaid图表进行高级可视化", "diagramDescription": "DeepWiki自动生成交互式图表，帮助您理解代码结构和关系：", "flowDiagram": "流程图", "sequenceDiagram": "序列图", "authError": "没有权限执行该操作"}, "form": {"model": "模型", "predefinedModel": "预定义模型", "selectModel": "选择模型", "customModel": "自定义模型", "enterCustomModel": "输入自定义模型名称", "fileFilters": "文件过滤", "excludeFiles": "排除文件", "excludeDirectories": "排除目录", "includeFiles": "包含文件", "includeDirectories": "包含目录", "excludeDirectoriesPlaceholder": "输入要排除的目录，每行一个...", "includeDirectoriesPlaceholder": "输入要包含的目录，每行一个...", "defaultExcludedDirs": "默认排除目录", "excludeFilesPlaceholder": "输入要排除的文件，每行一个...", "includeFilesPlaceholder": "输入要包含的文件，每行一个...", "defaultExcludedFiles": "默认排除文件", "repository": "主仓库", "branch": "分支", "subRepositories": "子仓库", "addSubRepository": "添加子仓库", "branchSelection": "选择分支", "configureWiki": "配置Wiki", "repoPlaceholder": "所有者/仓库或GitHub/GitLab/Bitbucket URL", "wikiLanguage": "Wiki语言", "modelOptions": "模型选项", "modelProvider": "模型提供商", "modelSelection": "模型选择", "wikiType": "Wiki类型", "comprehensive": "全面型", "concise": "简洁型", "comprehensiveDescription": "包含结构化章节和更多页面的详细Wiki", "conciseDescription": "页面更少，仅包含核心信息的简化Wiki", "providerGoogle": "Google", "providerOpenAI": "OpenAI", "providerOpenRouter": "OpenRouter", "providerOllama": "<PERSON><PERSON><PERSON> (本地)", "providerwhaleDevCloud": "whaleDevCloud", "localOllama": "本地Ollama模型", "experimental": "实验性", "useOpenRouter": "使用OpenRouter API", "openRouterModel": "OpenRouter模型", "useOpenai": "使用Openai API", "openaiModel": "Openai 模型", "useCustomModel": "使用自定义模型", "customModelPlaceholder": "输入自定义模型名称", "addTokens": "+ 添加私有仓库访问令牌", "hideTokens": "- 隐藏访问令牌", "accessToken": "私有仓库访问令牌", "selectPlatform": "选择平台", "providerGitHub": "GitHub", "providerBitbucket": "Bitbucket", "providerGitLab": "GitLab", "personalAccessToken": "{platform}个人访问令牌", "tokenPlaceholder": "输入您的{platform}令牌", "tokenSecurityNote": "令牌仅存储在内存中，从不持久化。", "defaultFiltersInfo": "默认过滤器包括node_modules、.git和常见的构建文件。", "fileFilterTitle": "文件过滤配置", "advancedOptions": "高级选项", "viewDefaults": "查看默认过滤", "showFilters": "显示过滤器", "hideFilters": "隐藏过滤器", "excludedDirs": "要排除的目录", "excludedDirsHelp": "每行一个目录路径。以./开头表示相对于仓库根目录的路径。", "enterExcludedDirs": "输入要排除的目录，每行一个...", "excludedFiles": "要排除的文件", "excludedFilesHelp": "每行一个文件名。支持通配符(*)。", "enterExcludedFiles": "输入要排除的文件，每行一个...", "defaultFilters": "默认排除的文件和目录", "directories": "目录", "files": "文件", "scrollToViewMore": "可滑动查看更多", "changeModel": "修改模型", "defaultNote": "这些默认配置已经被应用。请在上方添加您的自定义排除项。", "hideDefault": "隐藏默认配置", "viewDefault": "查看默认配置", "authorizationCode": "授权码", "authorizationRequired": "生成wiki页面需要填写授权码", "docchainTopic": "<PERSON><PERSON><PERSON> (Doc)", "docchainInfo": "文档库Topic来自于生产环境多模态文档工具链，地址：https://lab.iwhalecloud.com/docchain", "projectDocchainTips": "项目文档在docchain中的topic", "showTopic": "+ 使用已有Topic", "hideTopic": "- 隐藏Topic选择器", "topicSelectorDescription": "若产品文档已经同步到DocChain中，可选择产品文档的Topic。(可选配置)", "topicSelectorCodeDescription": "若产品仓库代码已经同步到DocChain中，可选择Topic。(可选配置)", "topicSelectorDocDescription": "若产品仓库代码文档已经同步到DocChain中，可选择Topic。(可选配置)", "selectTopic": "默认为空", "noTopicsAvailable": "没有可用的Topic，将创建新Topic。", "topicSelectedNote": "将直接使用选定的Topic，跳过代码上传和处理步骤。", "noMatchingTopics": "没有匹配的Topic", "docchainTopicCode": "Docchain 主题 (Code)", "selectBranch": "请输入主仓库地址后选择分支", "subRepoBranch": "请输入子仓库地址后选择分支", "removeSubRepository": "移除子仓库"}, "footer": {"copyright": "DeepWiki - 为代码仓库提供AI驱动的文档"}, "ask": {"placeholder": "询问关于此仓库的问题...", "askButton": "提问", "deepResearch": "深度研究", "researchInProgress": "研究进行中...", "continueResearch": "继续研究", "viewPlan": "查看计划", "viewUpdates": "查看更新", "viewConclusion": "查看结论"}, "repoPage": {"refreshWiki": "刷新Wiki", "confirmRefresh": "确认刷新", "cancel": "取消", "home": "首页", "errorTitle": "错误", "errorMessageDefault": "请检查您的仓库是否存在且为公开仓库。有效格式为\"https://git-nj.iwhalecloud.com/project/repo.git\", 或本地文件夹路径，如\"C:\\\\path\\\\to\\\\folder\"或\"/path/to/folder\"。", "backToHome": "返回首页", "exportWiki": "导出Wiki", "exportAsMarkdown": "导出为Markdown", "exportAsJson": "导出为JSON", "pages": "页面", "relatedFiles": "相关文件：", "relatedPages": "相关页面：", "selectPagePrompt": "从导航中选择一个页面以查看其内容", "askAboutRepo": "询问关于此仓库的问题", "snadBoxFailed": "沙箱创建失败", "wikiNotFound": "未找到Wiki", "cannotRefreshWiki": "无法刷新Wiki - Wiki ID缺失", "tokenIsNotValid": "令牌无效", "taskAlreadyRunning": "任务已运行", "refreshStarted": "Wiki刷新开始", "wikiRefreshRunning": "Wiki刷新任务正在后台运行", "refreshFailed": "刷新失败", "cannotRefreshWikiWithNoExistingWiki": "无法刷新Wiki - 未找到Wiki或Wiki ID缺失", "noPermissionToViewWiki": "没有权限查看该Wiki,请联系Wiki的创建者申请权限。", "currentlyProcessing": "正在处理：", "andMorePages": "和其他{count}页", "pagesCompleted": "{completed} of {total} 页已完成", "generatingWiki": "你的wiki页面正在生成哦~", "wikiGenerationInProgress": "请耐心等候，这通常需要数十分钟...可以在右上角查看进度~", "wikiGenerationInProgressWithStyle": "等待期间，<span class=\"font-bold text-green-600\">您可先用下方输入框提问，体验代码问答功能</span>。Wiki 生成后即可使用全部功能", "wikiActions": "Wiki 操作", "syncProgressTitle": "同步进度", "syncProgressPendingLabel": "待同步 {pending}/{total}", "syncProgressCompletedLabel": "已同步 {completed}/{total}", "syncProgressExpectedLabel": "预期 {expected}，差值 {delta}", "syncProgressRefresh": "刷新同步状态", "syncProgressCalculating": "索引数据计算中...", "syncProgressStatusLoading": "正在获取索引状态...", "syncProgressStatusSyncing": "索引同步进行中...", "syncProgressStatusReady": "索引数据已就绪", "syncProgressStatusFailed": "索引同步失败，请重试", "syncProgressStatusError": "索引状态获取失败，请稍后重试", "syncProgressActionTip": "同步会拉取最新代码，重新计算索引并同步到 DocChain。", "syncProgressAction": "重新同步索引", "syncProgressActionRunning": "同步中...", "syncProgressActionWaiting": "等待计算完成", "syncProgressInlineTitle": "任务进度", "syncProgressInlineFiles": "文件", "lastUpdated": "更新时间:"}, "user": {"confirm": "确认", "noPermissionToView": "您没有访问此资源的权限", "successModifyUserState": "成功修改用户状态", "modifyUserStateFailed": "修改用户状态失败", "userSearchPlaceholder": "用户名称/用户编码", "selectDepartment": "请选择部门", "search": "搜索", "username": "用户名", "userCode": "用户编码", "department": "部门", "organization": "组织", "position": "职位", "role": "角色", "effective": "有效", "invalid": "无效", "freeze": "冻结", "activate": "激活", "grant": "授权", "freezeConfirm": "冻结用户确认", "activateConfirm": "激活用户确认", "state": "状态"}, "nav": {"wikiProjects": "项目列表"}, "projects": {"title": "已处理的Wiki项目", "searchPlaceholder": "按项目名称、所有者、仓库名称、产品线、产品名称、产品版本、解决方案或标签搜索...", "noProjects": "服务器缓存中未找到项目。缓存可能为空或服务器遇到问题。", "noSearchResults": "没有项目符合您的搜索条件。", "processedOn": "处理时间:", "loadingProjects": "正在加载项目...", "errorLoading": "加载项目时出错:", "backToHome": "返回首页", "browseExisting": "浏览现有项目", "existingProjects": "现有项目", "recentProjects": "最近项目", "browseProjects": "浏览和管理您已生成的Wiki", "createNewWiki": "创建新Wiki", "unknownError": "未知错误", "deletePending": "待处理状态，无法删除", "failedToDelete": "删除项目失败", "applyPermission": "申请权限", "cannotApply": "无法申请，请先登录", "grant": "授权", "transferWikiOwner": "转移所有者", "queryWikDetail": "详情", "modify": "编辑", "delete": "删除", "deleteConfirm": "确定要删除这个项目吗？", "deleteConfirmMessage": "删除后无法恢复，请谨慎操作。", "deleteDialogTitlePrefix": "删除 ", "deleteDialogWarningTitle": "请先阅读以下重要安全提示！", "deleteDialogWarningDescriptionPrefix": "此操作将永久删除 ", "deleteDialogWarningDescriptionSuffix": "，并移除其下的代码、文档及协作者关联，且不可恢复。", "deleteDialogUnderstand": "我已经阅读并明白这些后果", "deleteDialogInstructionPrefix": "为确认操作，请在下方输入\"", "deleteDialogInstructionSuffix": "\" 以确认删除。", "deleteDialogConfirm": "删除该Wiki", "deleteDialogProcessing": "正在删除...", "cannotAccess": "无法访问", "pleaseApplyPermission": "请先申请权限。", "pleaseLogin": "你还未登录，请先登录。", "productLine": "产品线", "productName": "产品", "productVersion": "产品版本", "projectVersion": "发布包", "updatedTime": "更新时间", "createdTime": "创建时间", "owner": "所有者", "projectManager": "项目负责人", "solution": "解决方案"}, "alert": {"title": "需要配置", "message": "请在设置中配置您的大模型令牌和研发云令牌。", "action_label": "前往设置"}, "settings": {"title": "设置", "modelTab": "模型", "whaleDevCloudTab": "研发云", "tagManagementTab": "标签管理", "systemSettings": "系统设置", "announcementDisplay": "公告显示", "announcementDisplayDescription": "控制页面顶部公告的显示和隐藏", "showAnnouncement": "显示公告", "hideAnnouncement": "隐藏公告", "apiKey": "大模型令牌", "defaultWikiModel": "默认Wiki生成模型", "defaultChatModel": "默认对话模型", "whaleDevCloudToken": "研发云GitLab令牌", "testConnection": "测试连接", "verifyWhaleDevCloudToken": "校验令牌", "verifyApiKey": "测试API密钥", "save": "保存", "configTooltip": "请先配置API密钥和模型设置", "userInfo": "用户信息", "personalSandbox": "个人沙箱", "name": "姓名", "employeeId": "工号", "department": "部门", "position": "职位", "role": "角色", "notRetrieved": "未获取", "quota": "配额", "mySandbox": "我的沙箱", "noSandbox": "暂无沙箱", "refresh": "刷新", "deleting": "删除中", "delete": "删除", "cannotDelete": "无法删除：缺少Git地址", "deleteSuccess": "删除成功", "deleteFailed": "删除失败", "deleteFailedNetwork": "删除失败：网络异常", "saveSuccess": "保存成功", "connectionTestSuccess": "大模型连接测试成功", "connectionTestFailed": "大模型令牌连接失败，你的令牌可能有误，请返回主页面查看右上角的操作指南中'配置API-KEY'的部分：连接失败：", "whaleTokenVerifySuccess": "研发云令牌校验成功", "whaleTokenVerifyFailed": "研发云令牌校验失败", "connectionFailed": "连接失败，你的令牌可能有误，请返回主页面查看右上角的操作指南中'配置API-KEY'的部分:", "connectionFailedUnknown": "连接失败: 未知错误!", "verifyFailed": "校验失败:", "verifyFailedUnknown": "校验失败: 未知识错误!", "tagDisableSuccess": "标签禁用成功", "tagDisableFailed": "禁用标签失败:", "tagEnableSuccess": "标签启用成功", "tagEnableFailed": "启用标签失败:", "status": "状态", "type": "类型", "description": "描述", "actions": "操作", "system": "系统", "user": "用户", "active": "生效中", "disabled": "已禁用", "disable": "禁用", "enable": "启用", "edit": "编辑", "onlyAdminCanCreateSystemTag": "只有管理员才能创建系统标签", "clickButtonToCreateFirstTag": "点击上方按钮创建第一个标签", "gettingQuota": "正在获取配额...", "sandbox": "沙箱", "configHint": "请根据操作指南配置正确的大模型令牌，配置错误会影响AI会话功能使用", "tokenConfigHint": "这些令牌用于外部接口访问鉴权使用，请妥善保管", "apiValidating": "⏳ 大模型令牌正在验证中...", "apiCorrect": "✅ 大模型令牌验证通过，可以正常使用大模型功能.", "apiError": "❌ 大模型令牌验证失败，请检查配置", "apiNotSet": "❌ 大模型令牌未配置，请先配置大模型令牌", "apiSaveFailed": "保存大模型令牌失败, 大模型令牌校验未通过", "whaleCloudSaveFailed": "保存Access Token失败，Token校验未通过", "whalecloudTitle": "研发云 Access Token", "whalecloudValidating": "研发云令牌验证中", "whalecloudCorrect": "✅ 研发云令牌验证通过, 可以正常使用wiki生成等功能", "whalecloudError": "❌ 研发云令牌验证失败, 请检查配置", "whalecloudNotSet": "研发云令牌未配置", "whalecloudConfigHint": "请配置正确的研发云 Access Token，配置错误会影响wiki生成等相关功能使用", "apiKeyNotSet": "请先配置大模型令牌", "waittingValidate": "等待大模型令牌验证完成", "apiKeyValidateFailed": "大模型令牌校验未通过，无法保存", "saving": "保存中...", "unsavedChanges": "未保存的更改", "unsavedChangesMessage": "您有未保存的配置变更，确定要关闭吗？", "closeAnyway": "仍然关闭", "returnToEdit": "返回继续编辑", "available": "可用", "detailedInfo": "详细信息", "tokenDeleted": "Token删除成功", "tokenDeletedFailed": "Token删除失败", "tokenCreated": "Token创建成功", "tokenCreatedFailed": "Token创建失败", "tokenManagementTab": "Token管理", "tokenManagement": "Access Token管理", "tokenCreatedSuccess": "Token创建成功。请拷贝，因为令牌将只会显示一次。", "noTokens": "暂无Token", "clickButtonToCreateFirstToken": "点击上方按钮创建第一个Token", "addedAt": "增加于 {new Date(token.created_date).toLocaleString()} - 最后一次使用于 {new Date(token.last_used_time).toLocaleString()}", "addedAtNoActivity": "增加于 {new Date(token.created_date).toLocaleString()} - 最近无活动", "enterTokenName": "请输入Token名称", "enterTokenType": "请输入Token类型", "effectiveTimeMustBeFuture": "生效时间必须是未来时间", "expireTimeMustBeAfterEffectiveTime": "失效时间必须晚于生效时间", "expireTimeMustBeFuture": "失效时间必须是未来时间", "createToken": "创建Token", "tokenName": "Token名称", "tokenType": "Token类型", "effectiveTime": "生效时间", "expireTime": "失效时间", "deleteConfirmMessage": "确定要删除Token \"{name}\" 吗？此操作不可撤销。"}, "tagManagement": {"title": "标签管理", "searchPlaceholder": "搜索标签...", "addNewTag": "新建标签", "editTag": "编辑标签", "deleteTag": "删除标签", "tagName": "标签名称", "tagColor": "标签颜色", "customColor": "自定义颜色", "tagDescriptionPlaceholder": "输入标签描述（可选）", "tagDescription": "标签描述", "tagType": "标签类型", "onlyAdminCanCreateSystemTag": "只有管理员才能创建和管理系统标签", "systemTag": "系统标签", "userTag": "用户标签", "createdBy": "创建人", "createdDate": "创建时间", "actions": "操作", "confirmDelete": "确认删除", "deleteConfirmMessage": "确定要删除标签 \"{name}\" 吗？此操作不可撤销。", "tagCreated": "标签创建成功", "tagUpdated": "标签更新成功", "tagDeleted": "标签删除成功", "tagNameRequired": "标签名称不能为空", "tagColorRequired": "标签颜色不能为空", "tagNameExists": "标签名称已存在", "noTags": "暂无标签", "loadingTags": "正在加载标签...", "errorLoadingTags": "加载标签失败", "errorCreatingTag": "创建标签失败", "errorUpdatingTag": "更新标签失败", "errorDeletingTag": "删除标签失败", "addTag": "添加标签", "removeTag": "移除标签", "searchAndAddTag": "搜索并添加标签", "noMatchingTags": "未找到匹配的标签", "noAvailableTags": "暂无可用标签", "createNewTag": "创建新标签", "tagNamePlaceholder": "输入标签名称", "tagColorPlaceholder": "#FF0000", "tagCreatedSuccess": "标签创建成功", "tagAddedSuccess": "标签已添加到Wiki", "tagRemovedSuccess": "标签已从Wiki移除", "addTagError": "添加标签时发生错误", "removeTagError": "移除标签时发生错误", "createTagError": "创建标签时发生错误", "loading": "加载中...", "creating": "创建中...", "removeTagTitle": "移除标签", "loadTagsError": "加载标签时发生错误", "success": "成功", "createTag": "创建标签"}, "token": {"clientError": "客户端环境错误", "noCode": "未收到授权码", "processing": "登录成功，正在跳转...", "returnHome": "返回首页", "networkError": "网络请求失败", "login": "正在处理登录信息..."}, "k8s": {"createSandbox": {"title": "创建沙箱", "employeeId": "工号", "enterEmplyeeId": "请输入工号", "gitUrl": "Git仓库地址", "enterGitUrl": "请输入Git仓库地址", "branch": "分支", "enterBranch": "请输入分支名称", "create": "创建", "creating": "创建中...", "cancel": "取消"}, "JobDetail": {"title": "任务详情", "realTimeResourceUsage": "实时资源使用情况", "jobCpuAndMemoryRealTimeMonitoring": "任务CPU和内存实时监控", "lastUpdated": "最后更新", "stopMonitoring": "停止监控", "startMonitoring": "开始监控", "jobInfo": "任务信息", "sandboxStatus": "沙箱状态", "resourceMetrics": "资源指标", "logs": "日志", "events": "事件", "close": "关闭", "totalCpuUsage": "总CPU使用量", "totalMemoryUsage": "总内存使用量", "podDetailedUsage": "Pod详细使用情况", "noMonitoringData": "暂无监控数据", "clickStartMonitoring": "点击开始监控按钮开始监控", "volumeMounts": "卷挂载", "volumeMountsDescription": "显示所有卷挂载信息", "volumeName": "卷名称", "containerPath": "容器路径", "hostPath": "主机路径", "permissions": "权限", "readOnly": "只读", "readWrite": "读写", "noMountInfo": "暂无挂载信息", "basicInfo": "基本信息", "basicInfoDescription": "显示任务的基本信息", "userCode": "用户工号", "branch": "分支", "jobStatus": "任务状态", "querying": "查询中...", "refresh": "刷新", "queryStatus": "查询状态", "creationTime": "创建时间", "lastAccess": "最后访问", "namespace": "命名空间", "selectPod": "选择Pod", "selectContainer": "选择容器", "jumpContainer": "跳转容器", "gitRepoUrl": "Git仓库地址", "sandboxStatusTitle": "沙箱状态", "sandboxStatusDescription": "显示沙箱的详细状态信息", "notQueried": "未查询", "statusDescription": "状态描述", "podIp": "Pod IP", "apiResponse": "API响应", "updateTime": "更新时间", "labels": "标签", "labelsDescription": "显示所有标签信息", "noLabels": "暂无标签", "noLabelsDescription": "该任务没有配置标签", "annotations": "注解", "annotationsDescription": "显示所有注解信息", "noAnnotations": "暂无注解", "noAnnotationsDescription": "该任务没有配置注解", "metadataManagement": "元数据管理", "metadataDescription": "在此编辑或新增标签与注解", "editMetadata": "编辑元数据", "saveMetadata": "保存变更", "metadataKeyPlaceholder": "键名称", "metadataValuePlaceholder": "键对应的值", "metadataDelete": "删除", "metadataCurrentKey": "当前键名", "metadataNewKey": "新键名", "metadataKeyLabel": "键名", "metadataValueLabel": "键值", "addLabel": "新增标签", "addAnnotation": "新增注解", "metadataSaveSuccess": "元数据已更新", "metadataSaveFailed": "元数据更新失败", "metadataNoChanges": "没有需要保存的修改", "podInfo": "Pod信息", "podInfoDescription": "显示Pod的详细信息", "podNumber": "Pod #{index}", "node": "节点", "resourceConfig": "资源配置", "cpu": "CPU", "memory": "内存", "request": "请求", "limit": "限制", "containerStatus": "容器状态", "restartCount": "重启次数", "containerId": "容器ID", "noPodInfo": "暂无Pod信息", "noPodInfoDescription": "该任务没有关联的Pod信息"}, "jobsTable": {"jobList": "任务列表", "currentPage": "当前页", "remainingJobs": "剩余任务", "perPage": "每页", "previousPage": "上一页", "nextPage": "下一页", "page": "页", "loading": "加载中...", "noJobsFound": "未找到任务", "tryCreateSandbox": "尝试创建一个沙箱", "nameAndNamespace": "名称和命名空间", "userCode": "用户工号", "repositoryInfo": "仓库信息", "branch": "分支", "jobStatus": "任务状态", "sandboxStatus": "沙箱状态", "podIp": "Pod IP", "creationTime": "创建时间", "actions": "操作", "details": "详情", "delete": "删除", "queryStatus": "查询状态", "querying": "查询中...", "refresh": "刷新", "jumpServer": "跳板机", "copyJobName": "复制任务名称", "copied": "已复制!", "copiedDescription": "任务名称已成功复制到剪贴板", "copyFailed": "复制失败", "copyFailedDescription": "无法复制到剪贴板,请手动复制", "portalConfigMissing": "未配置门户地址或K8s参数，无法跳转", "portalDataMissing": "缺少Pod或容器信息，无法跳转", "loginContainer": "登录容器"}, "sandboxUserManagementTable": {"title": "沙箱用户管理", "totalRecords": "共 {total} 条记录", "perPage": "每页", "previousPage": "上一页", "nextPage": "下一页", "pageNumber": "第", "page": "页", "userCode": "用户工号", "userName": "用户姓名", "personalizedQuota": "个性化配额", "effectiveQuota": "有效配额", "edit": "编辑", "actions": "操作", "save": "保存", "clear": "清除", "viewSandbox": "查看沙箱", "collapseSandbox": "收起沙箱", "sandboxList": "沙箱列表", "refresh": "刷新", "loading": "加载中...", "noSandbox": "暂无沙箱", "sandboxName": "沙箱名称", "repository": "仓库", "branch": "分支", "status": "状态", "podIp": "Pod IP", "delete": "删除", "leaveEmptyToClear": "留空以清除", "noData": "暂无数据"}, "wikiInfoManagementTable": {"title": "Wiki信息管理", "searchPlaceholder": "搜索Wiki...", "addNewWiki": "新建Wiki", "editWiki": "编辑Wiki", "deleteWiki": "删除Wiki", "wikiName": "Wiki名称", "wikiDescription": "Wiki描述", "wikiStatus": "Wiki状态", "createdBy": "创建人", "createdDate": "创建时间", "actions": "操作", "confirmDelete": "确认删除", "deleteConfirmMessage": "确定要删除Wiki \"{name}\" 吗？此操作不可撤销。", "wikiCreated": "Wiki创建成功", "wikiUpdated": "Wiki更新成功", "wikiDeleted": "Wiki删除成功", "wikiNameRequired": "Wiki名称不能为空", "wikiDescriptionRequired": "Wiki描述不能为空", "wikiNameExists": "Wiki名称已存在", "noWikis": "暂无Wiki", "loadingWikis": "正在加载Wiki...", "errorLoadingWikis": "加载Wiki失败", "errorCreatingWiki": "创建Wiki失败", "errorUpdatingWiki": "更新Wiki失败", "errorDeletingWiki": "删除Wiki失败", "addWiki": "添加Wiki", "removeWiki": "移除Wiki", "searchAndAddWiki": "搜索并添加Wiki", "noMatchingWikis": "未找到匹配的Wiki", "noAvailableWikis": "暂无可用Wiki", "createNewWiki": "创建新Wiki", "wikiNamePlaceholder": "输入Wiki名称", "wikiDescriptionPlaceholder": "输入Wiki描述", "wikiCreatedSuccess": "Wiki创建成功", "wikiAddedSuccess": "Wiki已添加", "wikiRemovedSuccess": "Wiki已移除", "addWikiError": "添加Wiki时发生错误", "removeWikiError": "移除Wiki时发生错误", "createWikiError": "创建Wiki时发生错误", "loading": "加载中...", "creating": "创建中...", "removeWikiTitle": "移除Wiki", "loadWikisError": "加载Wiki时发生错误", "success": "成功", "filterByJobStatus": "按关联Job状态过滤", "filterByWikiData": "按是否有Wiki数据过滤", "fastGenerationStarted": "快速生成已启动！Job ID: {jobId}", "fastGenerationFailed": "快速生成失败: {error}", "manuallySetToFailed": "手动设置为失败状态", "fastRefreshTooltip": "为 {repoOwner}/{repoName} 启动快速刷新 - 点击后将弹出模型选择框", "jobTypeGeneration": "生成任务", "jobTypeRefresh": "刷新任务", "jobTypeSync": "同步索引任务", "jobTypeUnknown": "未知任务类型", "preparing": "正在准备...", "prepareStarted": "准备已启动", "prepareWiki": "准备Wiki"}, "problemWikisModal": {"title": "问题Wiki", "problemWikis": "问题Wiki列表", "fastGenerate": "快速生成", "refresh": "刷新", "close": "关闭", "noProblemWikis": "暂无问题Wiki", "loadingProblemWikis": "正在加载问题Wiki...", "errorLoadingProblemWikis": "加载问题Wiki失败", "modelSelection": "模型选择", "selectModel": "选择模型", "confirm": "确认", "cancel": "取消", "provider": "提供商", "model": "模型", "customModel": "自定义模型", "comprehensive": "全面型", "apiKey": "API密钥", "enterApiKey": "请输入API密钥", "modelRequired": "请选择一个模型", "generating": "生成中...", "generationSuccess": "生成成功", "generationFailed": "生成失败", "selectGenerationModel": "选择生成模型", "modelProvider": "模型提供商", "presetModel": "预设模型", "loadingModels": "加载模型中...", "noAvailableModels": "暂无可用模型", "enterModelName": "输入模型名称", "wikiType": "Wiki类型", "detailedMode": "详细模式", "detailedModeDescription": "包含更多细节的完整Wiki", "conciseMode": "简洁模式", "conciseModeDescription": "精简内容的简洁Wiki", "apiKeyOptional": "API密钥（可选）", "enterApiKeyNote": "如有特殊需求，可输入自定义API密钥", "currentStatus": "当前状态", "customModelValue": "自定义模型值", "isCustom": "是否自定义", "apiKeyStatus": "API密钥状态", "confirmGeneration": "确认生成", "searchPlaceholder": "搜索...", "noData": "暂无数据", "noMatchingProblemWikis": "没有匹配的问题Wiki", "copyId": "复制ID", "copyRepoUrl": "复制仓库URL", "refreshData": "刷新数据", "refreshing": "刷新中...", "repository": "仓库", "branch": "分支", "creator": "创建人", "createdTime": "创建时间", "status": "状态", "actions": "操作", "yes": "是", "no": "否", "detailed": "详细", "concise": "简洁", "entered": "已输入", "notEntered": "未输入"}, "quickWikiModal": {"title": "快速Wiki生成", "repositoryUrl": "仓库地址", "enterRepositoryUrl": "请输入仓库地址", "branch": "分支", "selectBranch": "选择分支", "language": "语言", "selectLanguage": "选择语言", "wikiType": "Wiki类型", "comprehensive": "全面型", "concise": "简洁型", "comprehensiveDescription": "包含结构化章节和更多页面的详细Wiki", "conciseDescription": "页面更少，仅包含核心信息的简化Wiki", "userSelection": "用户选择", "selectUser": "选择用户", "searchUser": "搜索用户", "noUsersFound": "未找到用户", "modelSettings": "模型设置", "provider": "提供商", "selectProvider": "选择提供商", "model": "模型", "selectModel": "选择模型", "customModel": "自定义模型", "enterCustomModel": "输入自定义模型名称", "apiKey": "API密钥", "enterApiKey": "请输入API密钥", "fileFilters": "文件过滤", "excludedDirs": "排除目录", "excludedDirsHelp": "每行一个目录路径", "excludedFiles": "排除文件", "excludedFilesHelp": "每行一个文件名，支持通配符", "includedDirs": "包含目录", "includedDirsHelp": "每行一个目录路径", "includedFiles": "包含文件", "includedFilesHelp": "每行一个文件名，支持通配符", "subRepositories": "子仓库", "addSubRepository": "添加子仓库", "removeSubRepository": "移除子仓库", "generate": "生成", "generating": "生成中...", "close": "关闭", "error": "错误", "success": "成功", "validationError": "验证错误", "repositoryUrlRequired": "仓库地址不能为空", "userRequired": "请选择用户", "modelRequired": "请选择模型", "generationStarted": "Wiki生成已开始", "generationFailed": "Wiki生成失败", "quickCreateWiki": "快速创建Wiki", "enterUserSearchPlaceholder": "搜索用户名或工号", "clearSearch": "清除搜索", "enterAtLeastOneCharacter": "请输入至少一个字符进行搜索", "searching": "搜索中...", "selectedUser": "已选择用户", "reselectUser": "重新选择", "checkUserInput": "请检查输入的用户信息", "pageInfo": "第 {page} 页，共 {total} 页", "previousPage": "上一页", "nextPage": "下一页", "foundExceptionWiki": "发现异常Wiki", "status": "状态", "creator": "创建者", "wikiLanguage": "Wiki语言", "chinese": "中文", "detailedMode": "详细模式", "detailedModeDescription": "包含更多细节的完整Wiki", "conciseMode": "简洁模式", "conciseModeDescription": "精简内容的简洁Wiki", "modelSelection": "模型选择", "delete": "删除", "createWiki": "创建Wiki", "creating": "创建中...", "pleaseEnterRepositoryUrl": "请输入仓库地址", "pleaseSelectUser": "请选择用户", "requestFailed": "请求失败", "createFailed": "创建失败"}, "toolbar": {"filterUserCode": "输入用户工号筛选", "refresh": "刷新", "batchQueryStatus": "批量查询状态"}, "toast": {"success": "成功", "error": "错误", "info": "信息", "close": "关闭"}}, "newWiki": {"fileFilters": "文件过滤", "excludedDirs": "排除文件夹", "excludedFiles": "排除文件", "excludedDirsHelp": "输入想要排除的文件夹列表 (每行一个)", "excludedFilesHelp": "输入想要排除的文件列表 (每行一个)", "includedDirs": "包含文件夹", "includedFiles": "包含文件", "includedDirsHelp": "输入想要包含的文件夹列表 (每行一个)", "includedFilesHelp": "输入想要包含的文件列表 (每行一个)", "subRepositories": "子仓库", "parameterVerificationFailed": "参数校验失败", "pleaseEnterValidRepositoryUrl": "请输入有效的仓库地址", "pleaseSelectBranch": "请选择分支", "taskExists": "已存在任务", "taskExistsDescription": "已有相同仓库的Wiki生成任务正在进行中，无需重复创建", "createSuccess": "创建成功", "createSuccessDescription": "Wiki生成任务已成功创建，您可以在任务窗口中查看进度", "createFailed": "创建失败", "createFailedDescription": "创建Wiki生成任务时出现错误", "configureWiki": "配置wiki", "basicInfo": "基础信息", "wikiStyleSettings": "Wiki风格设置", "aiSettings": "AI相关设置", "advancedSettings": "高级设置", "aiProvider": "AI提供商"}, "components": {"alert": {"title": "提示", "confirm": "确定", "cancel": "取消"}, "fileSearchSuggestions": {"searchPlaceholder": "搜索文件或目录...", "goUp": "返回上级 (Alt+←)", "close": "Esc 关闭", "searching": "正在搜索…", "noResults": "未找到匹配项", "select": "选择", "selectDirectory": "选择目录 (Ctrl+Enter)", "directory": "目录", "file": "文件", "totalResults": "共 {count} 个结果", "keyboardShortcuts": "↑↓ 选择 • Enter 进入/选择 • Ctrl+Enter 选择目录 • Esc 取消", "rootDescriptions": {"code": "源代码", "i-doc": "AI生成文档", "o-doc": "产品文档", "workspace": "用户个性化文档", "prj-doc": "项目文档", ".gemini": "gemini-cli配置目录"}}, "fileReferenceInput": {"generateWikiTitle": "优化wiki内容", "wikiTitle": "选择wiki页面标题", "noResults": "未找到匹配项", "addFileReference": "添加文件引用 (@) • 快捷键: Ctrl+Shift+F", "removeFileReference": "删除文件引用", "imageLimit": "最多支持4张图片", "imageOnly": "只支持图片类型的文件", "imageSizeLimit": "图片大小不能超过5M"}, "fileManager": {"title": "文件管理器", "localFiles": "本地文件", "sandboxFiles": "沙箱文件", "selectDirectory": "选择目录", "selectDirectoryDescription": "请选择一个本地目录（例如您的用户主目录）以开始浏览和上传文件。", "filesNotAutoUploaded": "您的文件不会被自动上传。", "refresh": "刷新", "reselectDirectory": "重新选择本地目录", "rootDirectory": "根目录", "goUp": "返回上级", "name": "名称", "size": "大小", "modifiedTime": "修改时间", "loading": "加载中...", "emptyDirectory": "空目录", "createFolder": "新建文件夹", "uploadFile": "上传文件", "downloadFile": "下载文件", "delete": "删除", "enterDirectory": "进入目录", "uploadToSandbox": "上传到沙箱", "uploadFolderToSandbox": "上传文件夹到沙箱", "batchUpload": "批量上传到沙箱", "batchDownload": "批量下载", "batchDelete": "批量删除", "cancelSelection": "取消选择", "folderNamePlaceholder": "输入文件夹名称...", "confirmCreate": "确认创建", "cancelCreate": "取消创建", "processingProgress": "处理进度", "viewProgress": "查看上传下载处理进度", "taskStatus": "任务状态（上传/下载）", "noTasks": "暂无任务", "processing": "进行中", "completed": "完成", "failed": "失败", "waiting": "等待", "uploadSuccess": "上传成功", "uploadFailed": "上传失败", "downloadSuccess": "下载成功", "downloadFailed": "下载失败", "deleteSuccess": "删除成功", "deleteFailed": "删除失败", "createSuccess": "创建成功", "createFailed": "创建失败", "batchUploadComplete": "批量上传完成", "batchUploadFailed": "批量上传失败", "batchDownloadComplete": "批量下载完成", "batchDownloadFailed": "批量下载失败", "batchDeleteComplete": "批量删除完成", "batchDeleteFailed": "批量删除失败", "fileUploadSuccess": "文件上传成功", "fileUploadFailed": "文件上传失败", "fileDownloadSuccess": "文件已成功下载", "fileDownloadFailed": "文件下载失败，请重试", "itemDeleted": "项目已删除", "deleteRetry": "删除失败，请重试", "folderCreated": "文件夹创建成功", "folderCreateFailed": "文件夹创建失败，请重试", "folderNameRequired": "文件夹名称不能为空", "enterFolderName": "请输入文件夹名称", "folderUploadSuccess": "文件夹上传完成", "folderUploadFailed": "文件夹上传失败，请重试", "uploadError": "上传过程中发生错误", "createRemoteRootFailed": "创建远程根目录失败", "fileTypeNotAllowed": "不支持的文件类型", "fileTypeNotAllowedMessage": "仅支持 {types} 文件", "networkError": "网络或服务错误", "fsApiNotSupportedTitle": "不支持的浏览器", "fsApiNotSupportedMessage": "您的浏览器不支持文件系统访问 API，请使用最新版本的 Chrome、Edge 或 Opera。", "successUploaded": "成功上传 {count} 个项目", "failedUploaded": "失败 {count} 个", "allFailed": "所有 {count} 个项目上传失败", "successDownloaded": "成功下载 {count} 个文件", "failedDownloaded": "失败 {count} 个", "allDownloadFailed": "所有 {count} 个文件下载失败", "successDeleted": "成功删除 {count} 个项目", "failedDeleted": "失败 {count} 个", "allDeleteFailed": "所有 {count} 个项目删除失败", "folderUploadComplete": "文件夹上传完成", "fileUploadFailedWithTypes": "上传失败。仅支持 {types} 文件", "uploadFailedTitle": "上传失败", "downloadDirectory": "下载目录", "directoryDownloadSuccess": "目录下载成功", "directoryDownloadFailed": "目录下载失败，请重试", "deleteNotAllowed": "禁止删除", "deleteNotAllowedAtRoot": "禁止在根目录删除任何项目", "deleteFailedMessage": "删除失败，请重试"}, "chatHistory": {"title": "问答历史", "refreshList": "刷新会话列表", "searchPlaceholder": "搜索会话...", "fetchError": "获取会话历史失败", "noSessions": "暂无会话记录", "save": "保存", "cancel": "取消", "edit": "编辑会话", "delete": "删除会话", "confirmDelete": "确定删除会话吗？", "sessionInUse": "会话正在使用中，无法删除", "deleteSuccess": "删除成功", "sessionDeleted": "会话已删除", "deleteFailed": "删除失败", "deleteError": "删除会话时发生错误", "saveSuccess": "保存成功", "titleUpdated": "会话标题已更新", "saveFailed": "保存失败", "updateError": "更新会话标题时发生错误", "updateFailed": "更新失败", "previousPage": "上一页", "nextPage": "下一页", "contextCleared": "上下文已清除"}, "chatInput": {"deepResearch": "深度研究", "deepResearchMode": "深度研究模式", "deepResearchModeDescription": "AI将更深入地分析代码库，并生成更详细的解释。这将消耗更多的API调用，但会提供更全面的答案。", "noModels": "暂无可用模型", "processing": "正在处理中，请稍候...", "askQuestion": "向AI询问关于代码库的问题...", "sendMessage": "发送消息", "optimizePrompt": "美化用户输入", "clearChatSession": "清除会话上下文", "clearChatSessionFailed": "清除消息会话上下文失败", "clearChatSessionSuccess": "成功清除消息会话上下文", "sandboxNotReady": "沙箱不可用或已被回收", "uploadImage": "上传图片", "openContainer": "登录容器", "openingContainer": "正在打开容器...", "containerUnavailable": "暂未查询到沙箱或容器信息", "containerOpenFailed": "容器跳转失败，请稍后重试", "containerContextMissing": "缺少Wiki或代码仓库信息，无法打开容器", "containerPopupBlocked": "浏览器阻止了弹窗，请允许打开新窗口", "openContainerTooltip": "登录沙箱容器", "openContainerConfirmTitle": "确认要打开沙箱容器？", "openContainerConfirmDesc": "确认后将在新窗口打开沙箱容器终端。", "openContainerConfirmAccept": "确认跳转", "openContainerConfirmCancel": "取消"}, "ownerTransfer": {"loadUserFail": "获取用户失败", "parameterVerification": "参数校验", "selectUser": "请选择用户", "successModifyOwner": "成功修改wiki所有者", "modifyOwnerFailed": "修改wiki所有者失败", "transferOwner": "转移所有者", "userSearchPlaceholder": "用户名称/用户编码", "noUsers": "暂无用户", "perPage": "每页", "items": "条", "previousPage": "上一页", "nextPage": "下一页", "page": "第 {page} 页，共 {total} 页", "transferOwnerConfirm": "转移所有者确认", "transferOwnerMessage": "确定要转移Wiki所有者为 {user}[{userCode}] 吗？"}, "personInfoDropdown": {"settings": "个人设置", "monitor": "监控大屏", "operation": "运营大屏", "operationData": "今日运营", "sandboxManagement": "沙箱管理", "userManagement": "用户管理", "logout": "登出", "appManagement": "应用管理"}, "projectDetail": {"wikiAdmin": "Wiki管理员", "wikiUser": "Wiki用户", "copySuccess": "复制成功", "copySuccessMessage": "文本已复制到剪贴板", "copyFailedMessage": "请手动复制", "loadFailed": "加载失败", "loadFailedMessage": "无法加载Wiki信息", "deleteSuccess": "删除成功", "deleteFailed": "删除失败", "deleteSuccessMessage": "用户 {user} 已删除", "deleteFailedMessage": "删除用户失败", "modifySuccess": "修改成功", "modifyFailed": "修改失败", "modifySuccessMessage": "用户 {user} 的角色已修改", "modifyFailedMessage": "修改用户角色失败", "notFoundWikiInfo": "未找到Wiki信息", "wikiDetail": "Wiki详情", "projectName": "项目名称", "projectUrl": "项目文档地址", "repoName": "仓库名称", "repoBranch": "仓库分支", "creator": "创建人", "createdTime": "创建时间", "productLine": "产品线", "solution": "解决方案", "copySolution": "复制解决方案", "repoUrl": "仓库地址", "copyRepoUrl": "复制仓库地址", "repoOwner": "归属项目", "whalecloudProject": "研发云项目", "language": "语言", "status": "状态", "completed": "已完成", "processing": "处理中", "failed": "失败", "pending": "待处理", "product": "产品", "productVersion": "产品版本", "searchUser": "搜索用户", "user": "用户", "role": "角色", "grantor": "授权者", "grantTime": "授权时间", "operation": "操作", "unknown": "未知", "modify": "编辑", "delete": "删除", "deleteConfirm": "确定删除用户吗？", "noUsers": "暂无用户数据", "addUser": "添加用户", "projectVersion": "项目版本", "publishPackage": "发布包", "productList": "产品列表", "productName": "产品名称", "productRepoUrl": "产品仓库地址", "noData": "暂无数据", "codeIndexTitle": "代码库索引", "codeIndexTip": "同步会拉取最新代码并更新索引", "codeIndexRepoDetails": "仓库进度详情", "syncProgressStatusLoading": "正在获取索引状态...", "syncProgressStatusSyncing": "索引同步进行中...", "syncProgressStatusReady": "索引数据已就绪", "syncProgressStatusFailed": "索引同步失败，请重试", "syncProgressStatusError": "索引状态获取失败，请稍后重试", "syncProgressAction": "重新同步索引", "syncProgressActionRunning": "同步中...", "syncProgressActionTip": "同步会拉取最新代码，重新计算索引并同步到 DocChain。", "syncProgressActionRunningTip": "同步执行中，请等待任务完成。", "syncProgressCalculating": "索引数据计算中...", "requestFailed": "请求失败", "syncIndexFailed": "同步索引失败", "loadWikiInfoFailed": "加载Wiki信息失败", "loadUsersFailed": "加载用户列表失败", "deleteUserFailed": "删除用户失败", "modifyUserRoleFailed": "修改用户角色失败", "copyFailed": "复制失败", "unknownRepo": "未知仓库", "unknownUser": "未知", "syncTaskQueued": "同步任务排队中", "syncTaskInProgress": "同步进行中", "syncTaskExecuting": "同步任务执行中", "noDocChainData": "尚未获取 <PERSON><PERSON><PERSON><PERSON> 同步数据", "syncCompleted": "已同步", "filesRemaining": "个文件", "localHeadInconsistent": "检测到本地 HEAD 与 DocChain 数据不一致", "localHeadNotSynced": "本地 HEAD 尚未同步到 DocChain", "historicDataExists": "Doc<PERSON><PERSON><PERSON> 中存在历史记录，尚未与本地关联", "noConvertibleFiles": "没有找到可转换的文件", "wikiBasicInfo": "Wiki基本信息", "leftArea": "左区域 - 基本信息", "rightArea": "右区域 - 详细信息", "codeIndex": "代码索引", "userList": "用户列表", "searchBoxAndAddButton": "搜索框和添加按钮", "userTable": "用户表格", "addUserModal": "添加用户模态框"}, "refreshModal": {"title": "刷新Wiki设置", "loadingModelConfigurations": "加载模型配置...", "aiProvider": "AI提供商", "whaleCloud": "WhaleCloud", "selectModel": "选择模型", "forceRefresh": "强制刷新", "forceRefreshDescription": "不管是否有代码变更，强制重新生成整个Wiki", "cancel": "取消", "confirmRefresh": "确认刷新"}, "roleSelectionModal": {"userAuthorization": "用户授权", "selectRole": "选择角色", "modifyUserRolesSuccess": "成功修改用户角色", "modifyUserRolesFailed": "修改用户角色失败", "cancel": "取消", "submit": "提交"}, "sandboxStatusDisplay": {"smartSandboxEnvironment": "智能沙箱环境", "currentSmartSandboxResourceBusy": "当前智能沙箱资源繁忙", "preparingSmartSandboxEnvironment": "正在准备智能沙箱环境...", "initializingEnvironmentConfiguration": "正在初始化环境配置，请稍候...", "environmentPreparationTime": "环境准备需要1-2分钟，请耐心等待...", "assigningComputeResourcesAndPullingCodeRepository": "正在分配计算资源和拉取代码仓库", "installingDependenciesAndConfiguringDevelopmentEnvironment": "正在安装依赖和配置开发环境", "detectingGeminiCliSession": "正在检测 Gemini-CLI 会话", "retry": "重试", "smartSandboxProvidesIndependentCodeExecutionEnvironment": "智能沙箱为每个用户提供独立的代码执行环境", "supportSafeCodeAnalysisAndToolCall": "支持安全的代码分析、工具调用", "firstCreationEnvironmentMayTakeLongTime": "首次创建环境可能需要较长时间，后续使用将更快速"}, "searchPage": {"sessionOrWikiInfoNotExist": "会话或Wiki信息不存在。", "sessionNotExist": "会话不存在。", "wikiInfoNotExist": "Wiki信息不存在。", "sessionNotRelatedToWiki": "会话关联不到wiki。", "noPermissionToDialogWiki": "没有权限对话该wiki，请联系wiki的创建者申请权限。", "sessionNotBelongToCurrentUser": "该会话不属于当前用户，请登录后发起会话。", "shareSuccess": "分享成功", "shareSuccessMessage": "分享链接已复制到剪贴板", "shareFailedMessage": "复制失败，请手动复制", "shareFailed": "分享失败", "shareDialog": "分享对话", "shareExpireTime": "分享有效期", "enterExpireTime": "请输入有效期（天）", "hours": "小时", "day": "天", "shareExpireHint": "分享链接将在指定时长后失效", "quickSelect": "快捷选择", "confirmShare": "确认分享", "invalidExpireTime": "请输入有效的过期时长", "researchProgress": "研究进度", "iteration": "迭代", "initializing": "正在初始化...", "errorDetails": "错误详情", "aiThinking": "AI 正在思考...", "deepResearch": "深度研究", "toolCall": "工具调用", "collapsePanel": "折叠面板", "noToolCall": "暂无工具调用", "noToolCallDescription": "AI调用工具时，这里会显示详细的执行过程和结果", "toolExecution": "工具执行", "executing": "执行中", "completed": "已完成", "dialogRound": "对话轮次", "total": "总计", "toolCallParameter": "调用参数", "originalParameter": "原始参数", "toolExecutionResult": "执行结果", "expandPanel": "展开面板", "errorDetailsTitle": "错误详情"}, "stepper": {"cancel": "取消", "previous": "上一步", "next": "下一步", "generateWiki": "生成Wiki"}, "wikiInfoEditor": {"repositoryMetadata": "仓库元数据", "repositoryMetadataEmpty": "仓库元数据暂时为空", "repositoryMetadataException": "同步仓库元数据异常", "updateWikiInfo": "更新wiki信息", "updateWikiInfoSuccess": "成功更新wiki信息", "editWiki": "编辑wiki", "productLine": "归属产线", "productLinePlaceholder": "输入产品线名称进行搜索", "product": "归属产品", "productPlaceholder": "输入产品名称进行搜索", "productVersion": "归属产品版本", "productVersionPlaceholder": "输入产品版本名称进行搜索", "solution": "归属解决方案", "solutionPlaceholder": "输入解决方案名称进行搜索", "tags": "标签", "comments": "仓库说明", "commentsPlaceholder": "输入仓库说明", "syncRepositoryMetadata": "同步仓库元数据", "syncRepositoryMetadataDescription": "根据仓库地址从研发云获取产品线、产品、产品版本、解决方案等信息，如果没有相关信息请自行搜索补充", "docchainTopic": "<PERSON><PERSON><PERSON><PERSON>主题 (Doc)", "projectKnowledge": "项目知识库", "searchTopics": "输入主题名称进行搜索", "cancel": "取消", "submit": "提交", "desc": "说明", "descPlaceholder": "输入说明"}, "grantUser": {"loadUsersFailed": "加载用户失败", "userName": "用户名", "projectName": "项目名称", "title": "Wiki授权", "isPublicVisible": "是否为全员可见", "isPublicVisibleDescription": "开启后，所有用户都可以查看此Wiki内容", "selectUser": "选择用户", "searchUser": "搜索用户名、工号或部门...", "noUsers": "暂无用户", "perPage": "每页", "unit": "条", "lastPage": "上一页", "paging": "第 {page} 页，共 {total} 页", "nextPage": "下一页", "selected": "选中", "searchSelectedUser": "搜索已选用户...", "noSelectedUsers": "暂无已选用户", "selectUserInLeftTable": "请在左侧表格中选择用户", "selectPriv": "选择权限：", "wikiUser": "wiki用户", "wikiAdmin": "wiki管理员", "grantSuccess": "授权成功", "grantFailed": "授权失败", "confirmGrant": "确认授权"}, "header": {"announcement": "公告", "hideAnnouncement": "隐藏公告", "showAnnouncement": "显示公告", "taskProgress": "任务进度", "helpDocument": "DeepWiki 操作指南", "share": "分享", "login": "登录", "user": "用户", "product": "产品", "project": "项目", "switchToProductMode": "切换到产品模式", "switchToProjectMode": "切换到项目模式"}, "mermaid": {"diagramDisplay": "图表显示", "diagramDisplaying": "图表显示中...", "clickToZoom": "点击缩放", "clickToViewFullscreen": "点击全屏", "diagramRenderingError": "图表渲染错误", "diagramSyntaxError": "图表存在语法错误，无法渲染。", "autoFixAttempted": "已尝试自动修复：", "autoFixSuccess": "图表自动修复成功", "fixesApplied": "应用的修复："}, "JobsProgressModal": {"title": "任务进度", "stages": {"init": "初始化", "download": "下载代码", "upload": "分析仓库", "structure": "AI生成结构", "pages": "AI生成页面", "generate": "生成Wiki", "completed": "已完成", "resuming": "恢复中", "paused": "已暂停"}, "status": {"resuming": "恢复中", "pending_resume": "等待恢复", "processing": "处理中", "paused": "已暂停", "completed": "已完成", "cancelled": "已取消", "failed": "失败", "pending": "等待中"}, "jobTypes": {"generation": "生成任务", "refresh": "刷新任务", "sync": "同步索引任务", "unknown": "未知任务类型"}, "actions": {"retrying": "重试中...", "retry": "智能重试", "deleting": "删除中...", "deleteTask": "删除任务", "resuming": "继续中...", "resumeTask": "继续任务", "restarting": "重新开始中...", "restart": "重新开始", "cancelling": "取消中...", "cancelTask": "取消任务", "viewWiki": "查看wiki"}, "tabs": {"processing": "进行中", "queued": "排队中", "failed": "失败", "cancelled": "已取消"}, "details": {"branch": "分支", "status": "状态", "createdBy": "创建人", "overallProgress": "总体进度", "taskStages": "任务阶段", "currentStage": "当前阶段", "processing": "处理中...", "fileProgress": "文件处理进度", "files": "个文件", "taskInfo": "任务信息", "taskId": "任务ID", "createdTime": "创建时间", "repoUrl": "仓库URL", "mainRepo": "主仓库", "jobType": "任务类型", "subRepos": "子仓库", "startTime": "开始时间", "retryCount": "重试次数", "statusInfo": "状态信息", "errorInfo": "错误信息", "statusMessage": "状态消息", "stageMessage": "阶段消息", "executionStatus": "执行状态", "running": "运行中", "stopped": "已停止"}, "emptyStates": {"noProcessingTasks": "当前没有正在进行的任务", "noProcessingTasksDesc": "当您创建新的Wiki后，任务会在这里显示", "noQueuedTasks": "当前没有排队的任务", "noQueuedTasksDesc": "您可以在这里查看已完成的任务", "noFailedTasks": "当前没有失败的任务", "noFailedTasksDesc": "所有任务都已成功完成", "noCancelledTasks": "当前没有取消的任务", "noCancelledTasksDesc": "您可以在这里重新开始已取消的任务", "selectTask": "选择一个任务查看详情", "selectTaskDesc": "点击左侧的任务卡片可以查看wiki的详细信息、进度和状态"}, "errors": {"stageError": "阶段发生异常，请联系管理员。", "currentStage": "当前"}, "deleteConfirm": {"title": "删除任务确认", "message": "确定要删除这个已取消的任务吗？此操作不可撤销。"}}, "K8SJobManagement": {"createCustomJob": {"fillJobNameAndImage": "请填写Job名称和容器镜像", "fillAtLeastOneValidVolume": "请至少配置一个有效的卷挂载", "createCustomJobSuccess": "自定义Job创建成功", "createCustomJobFailed": "创建自定义Job失败", "networkError": "网络错误", "statusQuerySuccess": "状态查询成功", "statusQueryFailed": "状态查询失败", "loadJobListFailed": "加载Job列表失败", "batchQueryStatusSuccess": "批量查询沙箱状态完成", "batchQueryStatusFailed": "批量查询失败"}, "fillUserCodeAndGitUrl": "请填写用户工号和Git仓库URL", "createSandboxSuccess": "沙箱创建成功", "createSandboxFailed": "创建沙箱失败", "networkError": "网络错误", "cannotIdentifySandboxParams": "无法识别该Job对应的沙箱参数（user_code/git_url）", "confirmDeleteSandbox": "确定要删除沙箱吗？\n用户: ${userCode}\n分支: ${branch}\n仓库: ${gitUrl}", "deleteSandboxSuccess": "沙箱删除成功", "deleteSandboxFailed": "删除沙箱失败", "getJobDetailsFailed": "获取Job详情失败", "getJobMetricsFailed": "获取Job metrics失败", "confirmCleanupIdleJobs": "确定要清理所有空闲Job吗？", "cleanupIdleJobsSuccess": "成功清理空闲Job", "cleanupIdleJobsFailed": "清理空闲Job失败", "confirmFastGenerate": "确定要以快速模式重新生成此Wiki吗？", "wikiFastGenerateSuccess": "Wiki快速生成已启动", "wikiFastGenerateFailed": "启动快速生成失败", "loadUserQuotasFailed": "加载配额失败", "invalidQuota": "请输入有效的非负整数配额", "saveUserQuotasSuccess": "保存成功", "saveUserQuotasFailed": "保存失败", "getUserSandboxesFailed": "获取用户沙箱失败", "cannotIdentifySandboxGitUrl": "无法识别该沙箱的git_url", "actions": {"expandActions": "展开操作", "createSandbox": "创建沙箱", "createCustomJob": "自定义Job", "quickCreateWiki": "快速创建Wiki", "cleanupIdle": "清理空闲", "problemWikis": "异常Wiki", "batchCreateJobs": "批量创建Jobs"}, "requestFailed": "请求失败", "loadProblemWikisFailed": "加载异常Wiki失败", "sandboxCenter": "沙箱管理中心", "sandboxCenterDescription": "现代化的 Kubernetes Job 与沙箱管理", "currentEnvironment": "当前环境：", "searchUserPlaceholder": "搜索用户（工号/姓名）", "searchWikiPlaceholder": "搜索Wiki（ID/仓库URL/分支）", "tabs": {"jobs": "Jobs", "sandboxUsers": "沙箱用户", "wikiInfo": "Wiki信息"}, "createCustomJobModal": {"title": "创建自定义Job", "jobName": "Job名称", "containerImage": "容器镜像", "basicInfo": "基本信息", "resourceConfig": "资源配置", "cpuRequest": "CPU请求", "memoryRequest": "内存请求", "cpuLimit": "CPU限制", "memoryLimit": "内存限制", "volumeMountConfig": "卷挂载配置", "addVolume": "添加卷", "volumeName": "卷名称", "hostPath": "主机路径", "containerPath": "容器路径", "readOnly": "只读", "delete": "删除", "envVarConfig": "环境变量配置", "addEnvVar": "添加环境变量", "varName": "变量名", "varValue": "变量值", "optionalInfo": "可选信息", "userCode": "用户代码", "userName": "用户姓名", "gitRepoUrl": "Git仓库URL", "branchName": "分支名称", "namespace": "命名空间", "cancel": "取消", "createJob": "创建Job", "creating": "创建中...", "userCodePlaceholder": "用户工号（可选）", "userNamePlaceholder": "用户姓名（可选）", "gitRepoUrlPlaceholder": "https://github.com/user/repo.git（可选）", "branchNamePlaceholder": "main（可选）", "namespacePlaceholder": "默认使用系统配置（可选）", "currentNamespace": "当前命名空间", "unknownError": "未知错误", "requestFailedNoResponse": "请求失败：无响应", "stopMetricsMonitoring": "停止metrics监控", "startMetricsMonitoring": "开始监控Job {jobName} 的metrics，每10秒刷新一次", "gettingJobMetrics": "正在获取Job {jobName} 的metrics...", "jobMetricsSuccess": "成功获取Job {jobName} 的metrics:", "jobMetricsFailed": "获取Job metrics失败:", "jobMetricsNetworkError": "获取Job metrics网络错误:", "refreshProblemWikisFailed": "刷新异常wiki列表失败:", "idleJobsCount": "个空闲Job", "varNamePlaceholder": "变量名", "varValuePlaceholder": "变量值"}, "batchCreateJobsModal": {"title": "批量创建Jobs", "jobNumber": "Job数量", "jobNumberPlaceholder": "请输入要创建的Job数量", "wctApiKey": "WCT API Key", "wctApiKeyPlaceholder": "请输入WCT API Key", "cancel": "取消", "confirm": "确定", "creating": "创建中...", "fillAllFields": "请填写所有字段", "invalidJobNumber": "请输入有效的Job数量（大于0的整数）", "createSuccess": "批量创建Jobs成功", "createFailed": "批量创建Jobs失败"}, "currentJobs": "当前 Jobs"}, "Markdown": {"copyCode": "复制代码", "viewFile": "查看文件"}}, "useChat": {"toolExecutionUnknownError": "工具执行出现未知错误", "toolExecutionFailedMessage": "工具执行失败", "unknownError": "未知错误", "modelExecutionToolError": "模型执行工具错误", "modelQuotaLimit": "模型额度限制", "modelQuotaLimitDescription": "当前模型今日额度已用完，请明天再试或联系管理员获取更多额度。", "toolCallParameterError": "工具调用参数错误", "toolCallParameterErrorDescription": "模型工具调用参数不正确，请稍后重试。如问题持续，请联系技术支持。", "apiForwardError": "API转发错误", "apiForwardErrorDescription": "模型API转发出现异常，请稍后重试或联系技术支持。", "modelCallTimeout": "大模型工具调用超时", "modelCallTimeoutDescription": "模型调用工具超时，请稍后重试或联系技术支持。", "modelServiceTimeout": "大模型调用超时", "modelServiceTimeoutDescription": "大模型调用超时，请稍后重试或联系技术支持。", "modelServiceError": "模型服务异常", "modelServiceErrorDescription": "模型服务出现异常，请稍后重试或联系技术支持。", "apiKeyError": "API密钥错误", "apiKeyErrorDescription": "未提供有效的API-KEY，请确认API-KEY格式正确且未过期。如问题持续，请联系技术支持。", "apiCallError": "API调用错误", "apiCallErrorDescription": "模型API调用失败，请检查网络连接或稍后重试。", "modelApiError": "模型接口错误", "modelApiErrorDescription": "模型接口调用时出现异常，请稍后重试。"}, "authApp": {"Search": "搜索", "appName": "应用名称", "appCode": "应用编码", "copyAppId": "复制APP_ID", "copyAppSecret": "复制APP_SECRET", "comments": "描述", "createdTime": "创建时间", "state": "状态", "operation": "操作", "edit": "编辑", "delete": "删除", "more": "更多", "deleteApp": "删除应用", "deleteAppSuccess": "成功删除应用", "deleteAppFail": "删除应用失败", "modifyAppState": "修改应用状态", "modifyAppStateSucess": "成功修改应用状态", "modifyAppStateFail": "修改应用状态失败", "copySuccess": "复制成功", "copyFail": "复制失败", "copyToClipboard": "文本已复制到剪贴板", "copyManual": "请手动复制", "appNameOrAppCode": "应用名称/应用编码", "createApp": "创建应用", "noData": "暂无数据", "total": "共", "records": "条", "recoredPerPage": "条/页", "disableApp": "禁用应用", "enableApp": "启用应用", "confirmDisableAppDesc": "确认禁用应用[{app_name}]?", "confirmEnableAppDesc": "确认启用应用[{app_name}]?", "tokenManagement": "Token管理", "apiManagement": "接口管理", "wikiManagement": "Wiki管理", "appSandbox": "应用沙箱", "bindWiki": "绑定Wiki", "bindWikiSuccess": "更新Wiki绑定成功", "bindWikiFail": "更新Wiki绑定失败", "wikiName": "Wiki名称", "wikiRepo": "仓库", "wikiBranch": "分支", "unbindWiki": "解绑", "unbindWikiSuccess": "解绑Wiki成功", "unbindWikiFail": "解绑Wiki失败", "confirmUnbindWiki": "确认解绑该Wiki？", "wikiBindingHint": "沙盒仅挂载绑定的Wiki代码目录。", "wikiBindingEmptyHint": "未选择将解绑全部Wiki。", "boundWikiList": "已绑定的Wiki", "unboundWikiList": "可绑定的Wiki", "noBoundWiki": "当前没有已绑定的Wiki", "noAvailableWiki": "没有可供绑定的Wiki", "noWikiData": "暂无Wiki数据", "searchWikiPlaceholder": "搜索Wiki", "refresh": "刷新", "selectAll": "全选", "unselectAll": "取消全选", "start": "启动", "pause": "暂停", "restart": "重启", "creator": "创建人", "sandboxName": "沙箱名称", "sandboxStatus": "沙箱状态", "podIp": "Pod IP", "notCreated": "尚未创建", "startSuccess": "启动成功", "startFailed": "启动失败", "stopSuccess": "停止成功", "stopFailed": "停止失败", "sandboxInfo": "沙箱信息", "validateParams": "参数校验", "appNameNotEmpty": "应用名称不能为空", "appCodeNotEmpty": "应用编码不能为空", "createAppSuccess": "成功创建应用", "createAppFail": "创建应用失败", "editApp": "编辑应用", "editAppSuccess": "成功编辑应用", "editAppFail": "编辑应用失败", "appNamePlaceholder": "请输入应用名称", "appCodePlaceholder": "请输入应用编码", "commentsPlaceholder": "请输入应用描述", "cancel": "取消", "submit": "提交", "ystem": "系统", "user": "用户", "copyToken": "复制Token", "type": "类型", "generatedTime": "生成时间", "effectiveTime": "生效时间", "expireTime": "过期时间", "deleteToken": "删除Token", "deleteTokenSuccess": "成功删除Token", "deleteTokenFail": "删除Token失败", "modifyTokenState": "修改Token状态", "modifyTokenStateSuccess": "成功修改Token状态", "modiToken管理fyTokenStateFail": "修改Token状态失败", "generateToken": "生成Token", "prePage": "上一页", "nextPage": "下一页", "page": "第 {page} 页，共 {total} 页", "confirmDeleteToken": "确认删除Token", "generateTokenSuccess": "成功生成Token", "generateTokenFail": "生成Token失败", "tokenType": "Token类型", "systemLevel": "系统级", "userLevel": "用户级", "systemLevelComments": "调用接口时无需携带用户信息", "userLevelComments": "调用接口时需要携带用户个人的X-User-Token进行身份验证", "apiName": "名称", "apiCode": "编码", "apiPath": "路径", "apiMethod": "方法", "category": "分类", "unbind": "解绑", "copyPath": "复制路径", "unbindApi": "解绑接口", "unbindApiSuccess": "成功解绑接口", "unbindApiFail": "解绑接口失败", "modifyApiState": "修改接口状态", "modifyApiStateSuccess": "成功修改接口状态", "modifyApiStateFail": "修改接口状态失败", "bindApi": "绑定接口", "confirmUnbindApi": "确认解绑接口?", "selectApiPlaceholder": "请选择接口", "bindApiSuccess": "成功绑定接口", "bindApiFail": "绑定接口失败", "confirmDeleteAppDesc": "确认删除应用[{app_name}]?", "disableToken": "禁用Token", "enableToken": "启动Token", "confirmDisableTokenDesc": "确认禁用Token?", "confirmEnableTokenDesc": "确认启动Token?", "enable": "启用", "disable": "禁用", "confirmDelteToken": "确认删除Token?", "disableApi": "禁用接口", "enableApi": "启用接口", "confirmDisableApi": "确认禁用接口?", "confirmEnableApi": "确认启用接口?", "updateToken": "更新Token信息", "updateTokenSuccess": "成功更新Token信息", "updateTokenFail": "更新Token信息失败", "createdBy": "创建人"}}