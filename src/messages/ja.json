{"common": {"appName": "DeepWiki", "tagline": "AI駆動のドキュメンテーション", "generateWiki": "Wikiを生成", "processing": "処理中...", "error": "エラー", "submit": "送信", "cancel": "キャンセル", "close": "閉じる", "loading": "読み込み中..."}, "loading": {"initializing": "Wiki生成を初期化中...", "fetchingStructure": "リポジトリ構造を取得中...", "determiningStructure": "Wiki構造を決定中...", "clearingCache": "サーバーキャッシュをクリア中...", "preparingDownload": "ダウンロードの準備中です..."}, "home": {"welcome": "DeepWikiへようこそ", "welcomeTagline": "コードリポジトリのためのAI駆動ドキュメンテーション", "description": "GitHub、GitLab、またはBitbucketリポジトリから包括的なドキュメントを数クリックで生成します。", "quickStart": "クイックスタート", "enterRepoUrl": "以下のいずれかの形式でリポジトリURLを入力してください：", "advancedVisualization": "Mermaidダイアグラムによる高度な可視化", "diagramDescription": "DeepWikiは、コード構造と関係を理解するのに役立つインタラクティブな図を自動的に生成します：", "flowDiagram": "フロー図", "sequenceDiagram": "シーケンス図"}, "form": {"repository": "リポジトリ", "configureWiki": "Wiki設定", "repoPlaceholder": "所有者/リポジトリまたはGitHub/GitLab/BitbucketのURL", "wikiLanguage": "Wiki言語", "modelOptions": "モデルオプション", "modelProvider": "モデルプロバイダー", "modelSelection": "モデル選択", "wikiType": "Wikiタイプ", "comprehensive": "包括的", "concise": "簡潔", "comprehensiveDescription": "構造化されたセクションとより多くのページを持つ詳細なWiki", "conciseDescription": "ページ数が少なく、必要な情報のみを含む簡素化されたWiki", "providerGoogle": "Google", "providerOpenAI": "OpenAI", "providerOpenRouter": "OpenRouter", "providerOllama": "<PERSON><PERSON><PERSON>（ローカル）", "localOllama": "ローカルOllamaモデル", "experimental": "実験的", "useOpenRouter": "OpenRouter APIを使用", "openRouterModel": "OpenRouterモデル", "useOpenai": "Openai APIを使用", "openaiModel": "Openaiモデル", "useCustomModel": "カスタムモデルを使用", "customModelPlaceholder": "カスタムモデル名を入力", "addTokens": "+ プライベートリポジトリ用のアクセストークンを追加", "hideTokens": "- アクセストークンを隠す", "accessToken": "プライベートリポジトリ用のアクセストークン", "selectPlatform": "プラットフォームを選択", "personalAccessToken": "{platform}個人アクセストークン", "tokenPlaceholder": "{platform}トークンを入力してください", "tokenSecurityNote": "トークンはメモリ内にのみ保存され、永続化されることはありません。", "defaultFiltersInfo": "デフォルトのフィルターは、node_modules、.git、および一般的なビルドアーティファクトファイルのような一般的なディレクトリを含みます。", "fileFilterTitle": "ファイルフィルター設定", "advancedOptions": "詳細オプション", "viewDefaults": "デフォルトフィルターを表示", "showFilters": "フィルターを表示", "hideFilters": "フィルターを非表示", "excludedDirs": "除外するディレクトリ", "excludedDirsHelp": "一行につき一つのディレクトリパス。./で始まるパスはリポジトリルートからの相対パスです。", "enterExcludedDirs": "除外するディレクトリを一行ずつ入力...", "excludedFiles": "除外するファイル", "excludedFilesHelp": "一行につき一つのファイル名。ワイルドカード(*)が使用可能です。", "enterExcludedFiles": "除外するファイルを一行ずつ入力...", "defaultFilters": "デフォルトで除外されるファイルとディレクトリ", "directories": "ディレクトリ", "files": "ファイル", "scrollToViewMore": "スクロールしてさらに表示", "changeModel": "モデルを変更", "defaultNote": "これらのデフォルト設定は既に適用されています。上記に追加の除外項目を入力してください。", "hideDefault": "デフォルトを隠す", "viewDefault": "デフォルトを表示", "authorizationCode": "認証コード", "authorizationRequired": "Wiki生成には認証コードが必要です"}, "footer": {"copyright": "DeepWiki - コードリポジトリのためのAI駆動ドキュメンテーション"}, "ask": {"placeholder": "このリポジトリについて質問する...", "askButton": "質問する", "deepResearch": "詳細調査", "researchInProgress": "調査進行中...", "continueResearch": "調査を続ける", "viewPlan": "計画を見る", "viewUpdates": "更新を見る", "viewConclusion": "結論を見る"}, "repoPage": {"refreshWiki": "Wikiを更新", "confirmRefresh": "更新を確認", "cancel": "キャンセル", "home": "ホーム", "errorTitle": "エラー", "errorMessageDefault": "リポジトリが存在し、公開されていることを確認してください。有効な形式は「owner/repo」、「https://github.com/owner/repo」、「https://gitlab.com/owner/repo」、「https://bitbucket.org/owner/repo」、またはローカルフォルダパス（例: 「C:\\\\path\\\\to\\\\folder」、「/path/to/folder」）です。", "backToHome": "ホームに戻る", "exportWiki": "Wikiをエクスポート", "exportAsMarkdown": "Markdownとしてエクスポート", "exportAsJson": "JSONとしてエクスポート", "pages": "ページ", "relatedFiles": "関連ファイル:", "relatedPages": "関連ページ:", "selectPagePrompt": "ナビゲーションからページを選択してコンテンツを表示", "askAboutRepo": "このリポジトリについて質問する"}, "nav": {"wikiProjects": "プロジェクト一覧"}, "projects": {"title": "処理済みWikiプロジェクト", "searchPlaceholder": "プロジェクト名、所有者、リポジトリで検索...", "noProjects": "サーバーキャッシュにプロジェクトが見つかりません。キャッシュが空であるか、サーバーで問題が発生した可能性があります。", "noSearchResults": "検索条件に一致するプロジェクトがありません。", "processedOn": "処理日時:", "loadingProjects": "プロジェクトを読み込み中...", "errorLoading": "プロジェクトの読み込みエラー:", "backToHome": "ホームに戻る", "browseExisting": "既存プロジェクトを閲覧", "existingProjects": "既存プロジェクト", "recentProjects": "最近のプロジェクト"}}