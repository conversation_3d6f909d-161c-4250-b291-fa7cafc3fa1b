{"common": {"appName": "DeepWiki", "tagline": "AI-powered documentation", "generateWiki": "Generate Wiki", "processing": "Processing...", "error": "Error", "submit": "Submit", "cancel": "Cancel", "close": "Close", "loading": "Loading...", "remove": "Remove", "invalidApiKey": "Large Model Token invalid. Please configure it in Settings.", "requestFail": "Request failed", "requestSuccess": "Request successful", "confirm": "Confirm", "employeeId": "Employee ID", "department": "Department", "show": "Show", "hide": "<PERSON>de", "deepwikiDescription": "DeepWiki provides intelligent code perception, code knowledge extraction, one-click generation of product wiki, intelligent driving of the new development paradigm, empowering production efficiency, already integrated", "deepwikiCount": "code repositories", "allStatus": "All Status", "pending": "Pending", "completed": "Completed", "failed": "Failed", "yes": "Yes", "no": "No", "entered": "Entered", "notEntered": "Not Entered", "allJobStatus": "All Job Status", "runningJobs": "Running Jobs", "failedJobs": "Failed Jobs", "cancelledJobs": "Cancelled Jobs", "pendingJobs": "Pending Jobs", "allWikiData": "All Wiki Data", "wikiData": "Wiki Data", "hasData": "Has Data", "noData": "No Data", "setToCompleted": "Set to Completed", "setToProcessing": "Set to Processing", "setToPending": "Set to Pending", "setToFailed": "Set to Failed", "errorMessage": "Error Message", "stage": "Stage", "progress": "Progress", "overall": "Overall", "refresh": "Refresh", "networkError": "Network Error", "unknownError": "Unknown Error", "deleteFailed": "Delete Failed", "type": "Type"}, "loading": {"initializing": "Initializing wiki generation...", "fetchingCache": "Checking for cached wiki...", "fetchingStructure": "Fetching repository structure...", "determiningStructure": "We are uploading and generating knowledge documents. The process may take a little time, so please be patient.", "clearingCache": "Clearing server cache...", "preparingDownload": "Please wait while we prepare your download..."}, "home": {"welcome": "Welcome to DeepWiki", "welcomeTagline": "AI-powered documentation for your code repositories", "description": "Generate comprehensive documentation from GitHub, GitLab, or Bitbucket repositories with just a few clicks.", "quickStart": "Quick Start", "enterRepoUrl": "Enter a repository URL in one of these formats:", "advancedVisualization": "Advanced Visualization with Mermaid Diagrams", "diagramDescription": "DeepWiki automatically generates interactive diagrams to help you understand code structure and relationships:", "flowDiagram": "Flow Diagram", "sequenceDiagram": "Sequence Diagram", "authError": "No permission to perform this operation"}, "form": {"model": "Model", "predefinedModel": "Predefined Model", "selectModel": "Select Model", "customModel": "Custom Model", "enterCustomModel": "Enter Custom Model Name", "fileFilters": "File Filters", "excludeFiles": "Exclude Files", "excludeDirectories": "Exclude Directories", "includeFiles": "Include Files", "includeDirectories": "Include Directories", "excludeDirectoriesPlaceholder": "Enter directories to exclude, one per line...", "includeDirectoriesPlaceholder": "Enter directories to include, one per line...", "defaultExcludedDirs": "Default Excluded Directories", "excludeFilesPlaceholder": "Enter files to exclude, one per line...", "includeFilesPlaceholder": "Enter files to include, one per line...", "defaultExcludedFiles": "Default Excluded Files", "repository": "Repository", "branch": "Branch", "subRepositories": "Sub Repositories", "addSubRepository": "Add Sub Repository", "branchSelection": "Select Branch", "configureWiki": "Configure Wiki", "repoPlaceholder": "owner/repo or GitHub/GitLab/Bitbucket URL", "wikiLanguage": "Wiki Language", "modelOptions": "Model Options", "modelProvider": "Model Provider", "modelSelection": "Model Selection", "wikiType": "Wiki Type", "comprehensive": "Comprehensive", "concise": "Concise", "comprehensiveDescription": "Detailed wiki with structured sections and more pages", "conciseDescription": "Simplified wiki with fewer pages and essential information", "providerGoogle": "Google", "providerOpenAI": "OpenAI", "providerOpenRouter": "OpenRouter", "providerOllama": "<PERSON><PERSON><PERSON> (Local)", "providerwhaleDevCloud": "whaleDevCloud", "localOllama": "Local Ollama Model", "experimental": "Experimental", "useOpenRouter": "Use Openrouter API", "openRouterModel": "OpenRouter Model", "useOpenai": "Use Openai API", "openaiModel": "Openai Model", "useCustomModel": "Use custom model", "customModelPlaceholder": "Enter custom model name", "addTokens": "+ Add access tokens for private repositories", "hideTokens": "- Hide access tokens", "accessToken": "Access Token for Private Repositories", "selectPlatform": "Select Platform", "providerGitHub": "GitHub", "providerBitbucket": "Bitbucket", "providerGitLab": "GitLab", "personalAccessToken": "{platform} Personal Access Token", "tokenPlaceholder": "Enter your {platform} token", "tokenSecurityNote": "Token is stored in memory only and never persisted.", "defaultFiltersInfo": "Default filters include common directories like node_modules, .git, and common build artifact files.", "fileFilterTitle": "File Filter Configuration", "advancedOptions": "Advanced Options", "viewDefaults": "View Default Filters", "showFilters": "Show Filters", "hideFilters": "Hide Filters", "excludedDirs": "Directories to Exclude", "excludedDirsHelp": "One directory path per line. Paths starting with ./ are relative to repository root.", "enterExcludedDirs": "Enter excluded directories, one per line...", "excludedFiles": "Files to Exclude", "excludedFilesHelp": "One filename per line. Wildcards (*) are supported.", "enterExcludedFiles": "Enter excluded files, one per line...", "defaultFilters": "Default Excluded Files & Directories", "directories": "Directories", "files": "Files", "scrollToViewMore": "Scroll to view more", "changeModel": "Change Model", "defaultNote": "These defaults are already applied. Add your custom exclusions above.", "hideDefault": "<PERSON><PERSON>", "viewDefault": "View Default", "includedDirs": "Included Directories", "includedFiles": "Included Files", "enterIncludedDirs": "Enter included directories, one per line...", "enterIncludedFiles": "Enter included files, one per line...", "filterMode": "Filter Mode", "excludeMode": "Exclude Paths", "includeMode": "Include Only Paths", "excludeModeDescription": "Specify paths to exclude from processing (default behavior)", "includeModeDescription": "Specify only the paths to include, ignoring all others", "authorizationCode": "Authorization Code", "authorizationRequired": "Authentication is required to generate the wiki.", "docchainTopic": "<PERSON><PERSON><PERSON> (Doc)", "docchainInfo": "The Docchain Topic comes from the production Multimodal document toolchain. Service Address: https://lab.iwhalecloud.com/docchain", "projectDocchainTips": "The topic in Doc<PERSON>hain of project documents", "showTopic": "+ Use Existing Topic", "hideTopic": "- Hide Topic Selector", "topicSelectorDescription": "Select the topic of the docchain corresponding to an existing product docs.(Non-mandatory configuration)", "topicSelectorCodeDescription": "If the product repository code has been synchronized to DocChain, you can select the Topic. (Optional configuration)", "topicSelectorDocDescription": "If the product repository code documentation has been synchronized to DocChain, you can select the Topic. (Optional configuration)", "selectTopic": "Default to being empty", "noTopicsAvailable": "No topics available. A new topic will be created.", "topicSelectedNote": "Selected topic will be used directly. Code upload and processing will be skipped.", "noMatchingTopics": "No matching topics found", "docchainTopicCode": "<PERSON><PERSON><PERSON> (Code)", "selectBranch": "Please enter the main repository address and select the branch", "subRepoBranch": "Please enter the sub repository address and select the branch", "removeSubRepository": "Remove Sub Repository"}, "alert": {"title": "Configuration Required", "message": "Please set your Large Model Token and WhaleDevCloud Token in the settings.", "action_label": "Go to Settings"}, "footer": {"copyright": "DeepWiki - AI-powered documentation for code repositories"}, "ask": {"placeholder": "Ask a question about this repository...", "askButton": "Ask", "deepResearch": "Deep Research", "researchInProgress": "Research in progress...", "continueResearch": "Continue Research", "viewPlan": "View Plan", "viewUpdates": "View Updates", "viewConclusion": "View Conclusion"}, "repoPage": {"refreshWiki": "Refresh Wiki", "confirmRefresh": "Confirm Refresh", "cancel": "Cancel", "home": "Home", "errorTitle": "Error", "errorMessageDefault": "Please check that your repository exists and is public. Valid formats are \"https://git-nj.iwhalecloud.com/project/repo.git\", or local folder paths like \"C:\\\\path\\\\to\\\\folder\" or \"/path/to/folder\".", "backToHome": "Back to Home", "exportWiki": "Export Wiki", "exportAsMarkdown": "Export as <PERSON><PERSON>", "exportAsJson": "Export as JSON", "pages": "Pages", "relatedFiles": "Related Files:", "relatedPages": "Related Pages:", "selectPagePrompt": "Select a page from the navigation to view its content", "askAboutRepo": "Ask questions about this repository", "snadBoxFailed": "Sandbox creation failed", "wikiNotFound": "No Wiki Found", "cannotRefreshWiki": "Cannot refresh wiki - wiki ID missing", "tokenIsNotValid": "Token is not valid", "taskAlreadyRunning": "Task already running", "refreshStarted": "Wiki Refresh Started", "wikiRefreshRunning": "Wiki refresh task is running in the background", "refreshFailed": "Refresh Failed", "cannotRefreshWikiWithNoExistingWiki": "Cannot refresh wiki - no existing wiki found or wiki ID missing", "noPermissionToViewWiki": "No permission to view this wiki, please contact the creator of the wiki to apply for permission.", "currentlyProcessing": "Currently processing:", "andMorePages": "and {count} more", "pagesCompleted": "{completed} of {total} pages completed", "generatingWiki": "Your wiki page is being generated...", "wikiGenerationInProgress": "Please wait patiently, this usually takes tens of minutes... You can check the progress in the upper right corner.", "wikiGenerationInProgressWithStyle": "During this period, <span class=\"font-bold text-green-600\">you can use the input box below to ask questions, experience the code question and answer function.</span> After the wiki is generated, you can use all functions.", "wikiActions": "Wiki Actions", "syncProgressTitle": "Sync Progress", "syncProgressPendingLabel": "{pending}/{total} files pending", "syncProgressCompletedLabel": "Synced {completed}/{total}", "syncProgressExpectedLabel": "Expected {expected}, Δ {delta}", "syncProgressRefresh": "Refresh sync status", "syncProgressCalculating": "Calculating repository metrics…", "syncProgressStatusLoading": "Fetching index status…", "syncProgressStatusSyncing": "Synchronising index…", "syncProgressStatusReady": "Index data is up to date", "syncProgressStatusFailed": "Index synchronisation failed, please retry", "syncProgressStatusError": "Unable to load index status, please retry later", "syncProgressActionTip": "Synchronisation pulls the latest code, rebuilds the index and uploads it to DocChain.", "syncProgressAction": "Resync index", "syncProgressActionRunning": "Synchronising…", "syncProgressActionWaiting": "Waiting for calculation", "syncProgressInlineTitle": "Task progress", "syncProgressInlineFiles": "files", "lastUpdated": "Updated Time:"}, "user": {"confirm": "Confirm", "noPermissionToView": "You do not have permission to access this resource", "successModifyUserState": "Successfully modified user state", "modifyUserStateFailed": "Failed to modify user state", "userSearchPlaceholder": "User Name/User Code", "selectDepartment": "Select Department", "search": "Search", "username": "Username", "userCode": "User Code", "department": "Department", "organization": "Organization", "position": "Position", "role": "Role", "effective": "Effective", "invalid": "Invalid", "freeze": "Freeze", "activate": "Activate", "grant": "<PERSON>", "freezeConfirm": "Freeze user confirmation", "activateConfirm": "Activate user confirmation", "state": "State"}, "nav": {"wikiProjects": "Project List"}, "projects": {"title": "Processed Wiki Projects", "searchPlaceholder": "Search projects by name, owner, repository name, product line, product name, product version, solution, or tags...", "noProjects": "No projects found in the server cache. The cache might be empty or the server encountered an issue.", "noSearchResults": "No projects match your search criteria.", "processedOn": "Processed on:", "loadingProjects": "Loading projects...", "errorLoading": "Error loading projects:", "backToHome": "Back to Home", "browseExisting": "Browse Existing Projects", "existingProjects": "Existing Projects", "recentProjects": "Recent Projects", "browseProjects": "Browse and manage your generated wikis", "createNewWiki": "Create New Wiki", "unknownError": "Unknown error", "deletePending": "pending status, cannot be deleted", "failedToDelete": "Failed to delete project", "applyPermission": "Apply Permission", "cannotApply": "Cannot apply, please login first", "grant": "<PERSON>", "transferWikiOwner": "Transfer Owner", "queryWikDetail": "Detail", "modify": "Edit", "delete": "Delete", "deleteConfirm": "Are you sure you want to delete this project?", "deleteConfirmMessage": "This action cannot be undone. Please be sure.", "deleteDialogTitlePrefix": "Delete ", "deleteDialogWarningTitle": "Unexpected bad things will happen if you don’t read this!", "deleteDialogWarningDescriptionPrefix": "This will permanently delete ", "deleteDialogWarningDescriptionSuffix": ", including its code, documents, and collaborators. This action cannot be undone.", "deleteDialogUnderstand": "I have read and understand these effects", "deleteDialogInstructionPrefix": "To confirm, type \"", "deleteDialogInstructionSuffix": "\" in the box below", "deleteDialogConfirm": "Delete this wiki", "deleteDialogProcessing": "Deleting...", "cannotAccess": "Cannot Access", "pleaseApplyPermission": "Please apply for permission first.", "pleaseLogin": "You are not logged in. Please log in first.", "productLine": "Product Line", "productName": "Product", "productVersion": "Product Version", "projectVersion": "Release Package", "updatedTime": "Updated Time", "createdTime": "Created Time", "owner": "Owner", "projectManager": "Project Manager", "solution": "Solution"}, "settings": {"title": "Settings", "modelTab": "Model", "whaleDevCloudTab": "WhaleDevCloud", "tagManagementTab": "Tag Management", "systemSettings": "System Settings", "announcementDisplay": "Announcement Display", "announcementDisplayDescription": "Control the display and hide of announcements at the top of the page", "showAnnouncement": "Show Announcement", "hideAnnouncement": "Hide Announcement", "apiKey": "Large Model Token", "defaultWikiModel": "Default Wiki Generation Model", "defaultChatModel": "<PERSON><PERSON><PERSON>", "whaleDevCloudToken": "WhaleDevCloud GitLab Token", "testConnection": "Test Connection", "verifyWhaleDevCloudToken": "Verify <PERSON>", "verifyApiKey": "Test API Key", "save": "Save", "configTooltip": "Please configure API key and model settings first", "userInfo": "User Info", "personalSandbox": "Personal Sandbox", "name": "Name", "employeeId": "Employee ID", "department": "Department", "position": "Position", "role": "Role", "notRetrieved": "Not Retrieved", "quota": "<PERSON><PERSON><PERSON>", "mySandbox": "My Sandbox", "noSandbox": "No Sandbox", "refresh": "Refresh", "deleting": "Deleting", "delete": "Delete", "cannotDelete": "Cannot delete: Missing Git address", "deleteSuccess": "Delete successful", "deleteFailed": "Delete failed", "deleteFailedNetwork": "Delete failed: Network error", "saveSuccess": "Save successful", "connectionTestSuccess": "Large model connection test successful", "connectionTestFailed": "Large model token connection failed, your token may be incorrect. Please return to the main page and check the 'Configure API-KEY' section in the operation guide in the upper right corner: Connection failed:", "whaleTokenVerifySuccess": "WhaleDevCloud token verification successful", "whaleTokenVerifyFailed": "WhaleDevCloud token verification failed", "connectionFailed": "Connection failed, your token may be incorrect. Please return to the main page and check the 'Configure API-KEY' section in the operation guide in the upper right corner:", "connectionFailedUnknown": "Connection failed: Unknown error!", "verifyFailed": "Verification failed:", "verifyFailedUnknown": "Verification failed: Unknown error!", "tagDisableSuccess": "Tag disabled successfully", "tagDisableFailed": "Failed to disable tag:", "tagEnableSuccess": "Tag enabled successfully", "tagEnableFailed": "Failed to enable tag:", "status": "Status", "type": "Type", "description": "Description", "actions": "Actions", "system": "System", "user": "User", "active": "Active", "disabled": "Disabled", "disable": "Disable", "enable": "Enable", "edit": "Edit", "onlyAdminCanCreateSystemTag": "Only administrators can create system tags", "clickButtonToCreateFirstTag": "Click the button above to create the first tag", "gettingQuota": "Getting quota...", "sandbox": "Sandbox", "configHint": "Please configure the correct large model token according to the operation guide. Incorrect configuration will affect the use of AI session functions", "tokenConfigHint": "These tokens are used for external interface access authentication. Please keep them safe", "apiValidating": "⏳ API Key is being validated...", "apiCorrect": "✅ API Key validation passed, you can use large model functions normally.", "apiError": "❌ API Key validation failed, please check the configuration", "apiNotSet": "❌ API Key not configured, please configure API Key first", "apiSaveFailed": "Saved Failed, API Key validation failed", "whaleCloudSaveFailed": "Saved Failed,WhaleDevCloud token validation failed,", "whalecloudTitle": "WhaleDevCloud Access Token", "whalecloudValidating": "WhaleDevCloud token is being validated", "whalecloudCorrect": "✅ WhaleDevCloud token validation passed, can use wiki generation and other related functions", "whalecloudError": "❌ WhaleDevCloud token validation failed, please check the configuration", "whalecloudNotSet": "WhaleDevCloud token not configured", "whalecloudConfigHint": "Please configure the correct WhaleDevCloud Access Token. Incorrect configuration will affect wiki generation and other related functions", "apiKeyNotSet": "Please configure API Key first", "waittingValidate": "Waiting for API Key validation to complete", "apiKeyValidateFailed": "API Key validation failed, cannot save", "saving": "Saving...", "unsavedChanges": "Unsaved Changes", "unsavedChangesMessage": "You have unsaved configuration changes. Are you sure you want to close?", "closeAnyway": "Close Anyway", "returnToEdit": "Return to Continue Editing", "available": "Available", "detailedInfo": "Detailed Information", "tokenDeleted": "Token deleted successfully", "tokenDeletedFailed": "<PERSON>ken deleted failed", "tokenCreated": "<PERSON>ken created successfully", "tokenCreatedFailed": "Token created failed", "tokenManagementTab": "Token Management", "tokenManagement": "Access Token Management", "tokenCreatedSuccess": "<PERSON><PERSON> created successfully. Please copy it because it will only be displayed once.", "noTokens": "No tokens available", "clickButtonToCreateFirstToken": "Click the button above to create the first token", "addedAt": "Added at {new Date(token.created_date).toLocaleString()} - Last used at {new Date(token.last_used_time).toLocaleString()}", "addedAtNoActivity": "Added at {new Date(token.created_date).toLocaleString()} - No activity", "enterTokenName": "Enter token name", "enterTokenType": "Enter token type", "effectiveTimeMustBeFuture": "Effective time must be in the future", "expireTimeMustBeAfterEffectiveTime": "Expire time must be after effective time", "expireTimeMustBeFuture": "Expire time must be in the future", "createToken": "Create Token", "tokenName": "Token Name", "tokenType": "Token Type", "effectiveTime": "Effective Time", "expireTime": "Expire Time", "deleteConfirmMessage": "Are you sure you want to delete token \"{name}\"? This action cannot be undone."}, "tagManagement": {"title": "Tag Management", "searchPlaceholder": "Search tags...", "addNewTag": "Add New Tag", "editTag": "Edit Tag", "deleteTag": "Delete", "tagName": "Tag Name", "tagColor": "Tag Color", "customColor": "Custom Color", "tagDescriptionPlaceholder": "Enter tag description (optional)", "tagDescription": "Tag Description", "tagType": "Tag Type", "onlyAdminCanCreateSystemTag": "Only administrators can create and manage system tags", "systemTag": "System Tag", "userTag": "User Tag", "createdBy": "Created By", "createdDate": "Created Date", "actions": "Actions", "confirmDelete": "Confirm Delete", "deleteConfirmMessage": "Are you sure you want to delete tag \"{name}\"? Removing this tag will disassociate all wikis linked to it.", "tagCreated": "Tag created successfully", "tagUpdated": "Tag updated successfully", "tagDeleted": "Tag deleted successfully", "tagNameRequired": "Tag name is required", "tagColorRequired": "Tag color is required", "tagNameExists": "Tag name already exists", "noTags": "No tags available", "loadingTags": "Loading tags...", "errorLoadingTags": "Failed to load tags", "errorCreatingTag": "Failed to create tag", "errorUpdatingTag": "Failed to update tag", "errorDeletingTag": "Failed to delete tag", "addTag": "Add Tag", "removeTag": "Remove Tag", "searchAndAddTag": "Search and add tags", "noMatchingTags": "No matching tags found", "noAvailableTags": "No available tags", "createNewTag": "Create New Tag", "tagNamePlaceholder": "Enter tag name", "tagColorPlaceholder": "#FF0000", "tagCreatedSuccess": "Tag created successfully", "tagAddedSuccess": "Tag added to Wiki successfully", "tagRemovedSuccess": "Tag removed from Wiki successfully", "addTagError": "Error occurred while adding tag", "removeTagError": "Error occurred while removing tag", "createTagError": "Error occurred while creating tag", "loading": "Loading...", "creating": "Creating...", "removeTagTitle": "Remove Tag", "loadTagsError": "Error occurred while loading tags", "success": "Success", "createTag": "Create Tag"}, "token": {"clientError": "Client environment error", "noCode": "Authorization code not received", "processing": "Login successful, redirecting...", "returnHome": "Return to Home", "networkError": "Network request failed", "login": "Processing login information..."}, "k8s": {"createSandbox": {"title": "Create Sandbox", "employeeId": "Employee ID", "enterEmplyeeId": "Enter employee ID", "gitUrl": "Git Repository URL", "enterGitUrl": "Enter Git repository URL", "branch": "Branch", "enterBranch": "Enter branch name", "create": "Create", "creating": "Creating...", "cancel": "Cancel"}, "JobDetail": {"title": "Job Details", "realTimeResourceUsage": "Real-time Resource Usage", "jobCpuAndMemoryRealTimeMonitoring": "Job CPU and Memory Real-time Monitoring", "lastUpdated": "Last Updated", "stopMonitoring": "Stop Monitoring", "startMonitoring": "Start Monitoring", "jobInfo": "Job Information", "sandboxStatus": "Sandbox Status", "resourceMetrics": "Resource Metrics", "logs": "Logs", "events": "Events", "close": "Close", "totalCpuUsage": "Total CPU Usage", "totalMemoryUsage": "Total Memory Usage", "podDetailedUsage": "Pod Detailed Usage", "noMonitoringData": "No monitoring data", "clickStartMonitoring": "Click start monitoring button to begin monitoring", "volumeMounts": "Volume Mounts", "volumeMountsDescription": "Display all volume mount information", "volumeName": "Volume Name", "containerPath": "Container Path", "hostPath": "Host Path", "permissions": "Permissions", "readOnly": "Read Only", "readWrite": "Read Write", "noMountInfo": "No mount information", "basicInfo": "Basic Information", "basicInfoDescription": "Display basic job information", "userCode": "User Code", "branch": "Branch", "jobStatus": "Job Status", "querying": "Querying...", "refresh": "Refresh", "queryStatus": "Query Status", "creationTime": "Creation Time", "lastAccess": "Last Access", "namespace": "Namespace", "selectPod": "Select Pod", "selectContainer": "Select Container", "jumpContainer": "Open Container", "gitRepoUrl": "Git Repository URL", "sandboxStatusTitle": "Sandbox Status", "sandboxStatusDescription": "Display detailed sandbox status information", "notQueried": "Not Queried", "statusDescription": "Status Description", "podIp": "Pod IP", "apiResponse": "API Response", "updateTime": "Update Time", "labels": "Labels", "labelsDescription": "Display all label information", "noLabels": "No Labels", "noLabelsDescription": "This job has no labels configured", "annotations": "Annotations", "annotationsDescription": "Display all annotation information", "noAnnotations": "No Annotations", "noAnnotationsDescription": "This job has no annotations configured", "metadataManagement": "<PERSON><PERSON><PERSON>", "metadataDescription": "Edit labels and annotations for this job", "editMetadata": "<PERSON>", "saveMetadata": "Save Changes", "metadataKeyPlaceholder": "Key", "metadataValuePlaceholder": "Value", "metadataDelete": "Delete", "metadataCurrentKey": "Current Key", "metadataNewKey": "New Key", "metadataKeyLabel": "Key", "metadataValueLabel": "Value", "addLabel": "Add Label", "addAnnotation": "Add Annotation", "metadataSaveSuccess": "Metadata updated", "metadataSaveFailed": "Failed to update metadata", "metadataNoChanges": "No changes to save", "podInfo": "Pod Information", "podInfoDescription": "Display detailed pod information", "podNumber": "Pod #{index}", "node": "Node", "resourceConfig": "Resource Configuration", "cpu": "CPU", "memory": "Memory", "request": "Request", "limit": "Limit", "containerStatus": "Container Status", "restartCount": "Restart Count", "containerId": "Container ID", "noPodInfo": "No Pod Information", "noPodInfoDescription": "This job has no associated pod information"}, "jobsTable": {"jobList": "Job List", "currentPage": "Current Page", "remainingJobs": "Remaining Jobs", "perPage": "Per Page", "previousPage": "Previous", "nextPage": "Next", "page": "Page", "loading": "Loading...", "noJobsFound": "No Jobs Found", "tryCreateSandbox": "Try creating a sandbox", "nameAndNamespace": "Name and Namespace", "userCode": "User Code", "repositoryInfo": "Repository Info", "branch": "Branch", "jobStatus": "Job Status", "sandboxStatus": "Sandbox Status", "podIp": "Pod IP", "creationTime": "Creation Time", "actions": "Actions", "details": "Details", "delete": "Delete", "queryStatus": "Query Status", "querying": "Querying...", "refresh": "Refresh", "jumpServer": "Server", "copyJobName": "Copy Job Name", "copied": "Copied!", "copiedDescription": "Job name has been copied to clipboard", "copyFailed": "<PERSON><PERSON> Failed", "copyFailedDescription": "Failed to copy to clipboard, please copy manually", "portalConfigMissing": "Portal base URL or K8s settings are missing", "portalDataMissing": "Pod or container information is incomplete", "loginContainer": "<PERSON><PERSON> Container"}, "sandboxUserManagementTable": {"title": "Sandbox User Management", "totalRecords": "Total {total} records", "perPage": "Per Page", "previousPage": "Previous", "nextPage": "Next", "pageNumber": "Page", "page": "Page", "userCode": "User Code", "userName": "User Name", "personalizedQuota": "Personalized Quota", "effectiveQuota": "Effective Quota", "edit": "Edit", "actions": "Actions", "save": "Save", "clear": "Clear", "viewSandbox": "View Sandbox", "collapseSandbox": "Collapse Sandbox", "sandboxList": "Sandbox List", "refresh": "Refresh", "loading": "Loading...", "noSandbox": "No Sandbox", "sandboxName": "Sandbox Name", "repository": "Repository", "branch": "Branch", "status": "Status", "podIp": "Pod IP", "delete": "Delete", "leaveEmptyToClear": "Leave empty to clear", "noData": "No Data"}, "wikiInfoManagementTable": {"title": "Wiki Information Management", "searchPlaceholder": "Search Wiki...", "addNewWiki": "Add New Wiki", "editWiki": "Edit Wiki", "deleteWiki": "Delete Wiki", "wikiName": "Wiki Name", "wikiDescription": "Wiki Description", "wikiStatus": "Wiki Status", "createdBy": "Created By", "createdDate": "Created Date", "actions": "Actions", "confirmDelete": "Confirm Delete", "deleteConfirmMessage": "Are you sure you want to delete Wiki \"{name}\"? This action cannot be undone.", "wikiCreated": "Wiki created successfully", "wikiUpdated": "Wiki updated successfully", "wikiDeleted": "Wiki deleted successfully", "wikiNameRequired": "Wiki name is required", "wikiDescriptionRequired": "Wiki description is required", "wikiNameExists": "Wiki name already exists", "noWikis": "No Wikis", "loadingWikis": "Loading Wikis...", "errorLoadingWikis": "Failed to load Wikis", "errorCreatingWiki": "Failed to create Wiki", "errorUpdatingWiki": "Failed to update Wiki", "errorDeletingWiki": "Failed to delete Wiki", "addWiki": "Add Wiki", "removeWiki": "Remove Wiki", "searchAndAddWiki": "Search and add Wiki", "noMatchingWikis": "No matching Wikis found", "noAvailableWikis": "No available Wikis", "createNewWiki": "Create New Wiki", "wikiNamePlaceholder": "Enter Wiki name", "wikiDescriptionPlaceholder": "Enter Wiki description", "wikiCreatedSuccess": "Wiki created successfully", "wikiAddedSuccess": "Wiki added successfully", "wikiRemovedSuccess": "Wiki removed successfully", "addWikiError": "Error adding Wiki", "removeWikiError": "Error removing Wiki", "createWikiError": "Error creating Wiki", "loading": "Loading...", "creating": "Creating...", "removeWikiTitle": "Remove Wiki", "loadWikisError": "Error loading Wikis", "success": "Success", "filterByJobStatus": "Filter by related Job status", "filterByWikiData": "Filter by Wiki data availability", "fastGenerationStarted": "Fast generation started! Job ID: {jobId}", "fastGenerationFailed": "Fast generation failed: {error}", "manuallySetToFailed": "Manually set to failed status", "fastRefreshTooltip": "Start fast refresh for {repoOwner}/{repoName} - Click to open model selection dialog", "jobTypeGeneration": "Generation Job", "jobTypeRefresh": "Refresh Job", "jobTypeSync": "Sync Job", "jobTypeUnknown": "Unknown Job Type", "preparing": "Preparing...", "prepareStarted": "Preparation started", "prepareWiki": "Prepare Wiki"}, "problemWikisModal": {"title": "Problem Wikis", "problemWikis": "Problem Wiki List", "fastGenerate": "Fast Generate", "refresh": "Refresh", "close": "Close", "noProblemWikis": "No Problem Wikis", "loadingProblemWikis": "Loading Problem Wikis...", "errorLoadingProblemWikis": "Failed to load Problem Wikis", "modelSelection": "Model Selection", "selectModel": "Select Model", "confirm": "Confirm", "cancel": "Cancel", "provider": "Provider", "model": "Model", "customModel": "Custom Model", "comprehensive": "Comprehensive", "apiKey": "API Key", "enterApiKey": "Enter API Key", "modelRequired": "Please select a model", "generating": "Generating...", "generationSuccess": "Generation successful", "generationFailed": "Generation failed", "selectGenerationModel": "Select Generation Model", "modelProvider": "Model Provider", "presetModel": "Preset Model", "loadingModels": "Loading models...", "noAvailableModels": "No available models", "enterModelName": "Enter model name", "wikiType": "Wiki Type", "detailedMode": "Detailed Mode", "detailedModeDescription": "Complete Wiki with more details", "conciseMode": "Concise Mode", "conciseModeDescription": "Streamlined Wiki content", "apiKeyOptional": "API Key (Optional)", "enterApiKeyNote": "Enter custom API Key if needed", "currentStatus": "Current Status", "customModelValue": "Custom Model Value", "isCustom": "Is Custom", "apiKeyStatus": "API Key Status", "confirmGeneration": "Confirm Generation", "searchPlaceholder": "Search...", "noData": "No Data", "noMatchingProblemWikis": "No matching Problem Wikis", "copyId": "Copy ID", "copyRepoUrl": "Copy Repository URL", "refreshData": "Refresh Data", "refreshing": "Refreshing...", "repository": "Repository", "branch": "Branch", "creator": "Creator", "createdTime": "Created Time", "status": "Status", "actions": "Actions", "yes": "Yes", "no": "No", "detailed": "Detailed", "concise": "Concise", "entered": "Entered", "notEntered": "Not Entered"}, "quickWikiModal": {"title": "Quick Wiki Generation", "repositoryUrl": "Repository URL", "enterRepositoryUrl": "Enter repository URL", "branch": "Branch", "selectBranch": "Select Branch", "language": "Language", "selectLanguage": "Select Language", "wikiType": "Wiki Type", "comprehensive": "Comprehensive", "concise": "Concise", "comprehensiveDescription": "Detailed Wiki with structured sections and more pages", "conciseDescription": "Simplified Wiki with fewer pages and essential information", "userSelection": "User Selection", "selectUser": "Select User", "searchUser": "Search User", "noUsersFound": "No users found", "modelSettings": "Model Settings", "provider": "Provider", "selectProvider": "Select Provider", "model": "Model", "selectModel": "Select Model", "customModel": "Custom Model", "enterCustomModel": "Enter custom model name", "apiKey": "API Key", "enterApiKey": "Enter API Key", "fileFilters": "File Filters", "excludedDirs": "Excluded Directories", "excludedDirsHelp": "One directory path per line", "excludedFiles": "Excluded Files", "excludedFilesHelp": "One filename per line, wildcards supported", "includedDirs": "Included Directories", "includedDirsHelp": "One directory path per line", "includedFiles": "Included Files", "includedFilesHelp": "One filename per line, wildcards supported", "subRepositories": "Sub Repositories", "addSubRepository": "Add Sub Repository", "removeSubRepository": "Remove Sub Repository", "generate": "Generate", "generating": "Generating...", "close": "Close", "error": "Error", "success": "Success", "validationError": "Validation Error", "repositoryUrlRequired": "Repository URL is required", "userRequired": "Please select a user", "modelRequired": "Please select a model", "generationStarted": "Wiki generation started", "generationFailed": "Wiki generation failed", "quickCreateWiki": "Quick Create Wiki", "enterUserSearchPlaceholder": "Search username or employee ID", "clearSearch": "Clear Search", "enterAtLeastOneCharacter": "Enter at least one character to search", "searching": "Searching...", "selectedUser": "Selected User", "reselectUser": "Reselect User", "checkUserInput": "Please check user input", "pageInfo": "Page {page} of {total}", "previousPage": "Previous", "nextPage": "Next", "foundExceptionWiki": "Found Exception Wiki", "status": "Status", "creator": "Creator", "wikiLanguage": "Wiki Language", "chinese": "Chinese", "detailedMode": "Detailed Mode", "detailedModeDescription": "Complete Wiki with more details", "conciseMode": "Concise Mode", "conciseModeDescription": "Streamlined Wiki content", "modelSelection": "Model Selection", "delete": "Delete", "createWiki": "Create Wiki", "creating": "Creating...", "pleaseEnterRepositoryUrl": "Please enter repository URL", "pleaseSelectUser": "Please select user", "requestFailed": "Request failed", "createFailed": "Creation failed"}, "toolbar": {"filterUserCode": "Filter by user code", "refresh": "Refresh", "batchQueryStatus": "Batch Query Status"}, "toast": {"success": "Success", "error": "Error", "info": "Info", "close": "Close"}}, "newWiki": {"fileFilters": "File Filters", "excludedDirs": "Excluded Directories", "excludedFiles": "Excluded Files", "excludedDirsHelp": "Enter directories to exclude (one per line)", "excludedFilesHelp": "Enter files to exclude (one per line)", "includedDirs": "Included Directories", "includedDirsHelp": "Enter directories to include (one per line)", "includedFiles": "Included Files", "includedFilesHelp": "Enter files to include (one per line)", "subRepositories": "Sub Repositories", "parameterVerificationFailed": "Parameter Verification Failed", "pleaseEnterValidRepositoryUrl": "Please enter valid repository URL", "pleaseSelectBranch": "Please select branch", "taskExists": "Task Exists", "taskExistsDescription": "A task for this repository already exists, no need to create a new one", "createSuccess": "Create Success", "createSuccessDescription": "Wiki generation task has been created successfully, you can view the progress in the task window", "createFailed": "Create Failed", "createFailedDescription": "Error creating Wiki generation task", "configureWiki": "Configure Wiki", "basicInfo": "Basic Information", "wikiStyleSettings": "Wiki Style Settings", "aiSettings": "AI Related Settings", "advancedSettings": "Advanced Settings", "aiProvider": "AI Provider"}, "components": {"alert": {"title": "<PERSON><PERSON>", "confirm": "Confirm", "cancel": "Cancel"}, "fileSearchSuggestions": {"searchPlaceholder": "Search files or directories...", "goUp": "Go Up (Alt+←)", "close": "Esc Close", "searching": "Searching...", "noResults": "No matches found", "select": "Select", "selectDirectory": "Select Directory (Ctrl+Enter)", "directory": "Directory", "file": "File", "totalResults": "{count} results", "keyboardShortcuts": "↑↓ Select • Enter Enter/Select • Ctrl+Enter Select Directory • Esc Cancel", "rootDescriptions": {"code": "Source Code", "i-doc": "AI Generated Documentation", "o-doc": "Product Documentation", "workspace": "User Personalized Documentation", "prj-doc": "Project Documentation", ".gemini": "gemini-cli Configuration Directory"}}, "fileReferenceInput": {"generateWikiTitle": "Optimize Wiki Content", "wikiTitle": "Select Wiki Title", "noResults": "No matches found", "addFileReference": "Add File Reference (@) • Shortcut: Ctrl+Shift+F", "removeFileReference": "Remove File Reference", "imageLimit": "A maximum of 4 images is supported", "imageOnly": "Only image file types are supported.", "imageSizeLimit": "The image size must not exceed 5MB."}, "fileManager": {"title": "File Manager", "localFiles": "Local Files", "sandboxFiles": "Sandbox Files", "selectDirectory": "Select Directory", "selectDirectoryDescription": "Please select a local directory (such as your user home directory) to start browsing and uploading files.", "filesNotAutoUploaded": "Your files will not be automatically uploaded.", "refresh": "Refresh", "reselectDirectory": "Reselect Local Directory", "rootDirectory": "Root Directory", "goUp": "Go Up", "name": "Name", "size": "Size", "modifiedTime": "Modified Time", "loading": "Loading...", "emptyDirectory": "Empty Directory", "createFolder": "New Folder", "uploadFile": "Upload File", "downloadFile": "Download File", "delete": "Delete", "enterDirectory": "Enter Directory", "uploadToSandbox": "Upload to Sandbox", "uploadFolderToSandbox": "Upload Folder to Sandbox", "batchUpload": "Batch Upload to Sandbox", "batchDownload": "Batch Download", "batchDelete": "<PERSON><PERSON> Delete", "cancelSelection": "Cancel Selection", "folderNamePlaceholder": "Enter folder name...", "confirmCreate": "Confirm Create", "cancelCreate": "Cancel Create", "processingProgress": "Processing Progress", "viewProgress": "View Upload/Download Processing Progress", "taskStatus": "Task Status (Upload/Download)", "noTasks": "No Tasks", "processing": "Processing", "completed": "Completed", "failed": "Failed", "waiting": "Waiting", "uploadSuccess": "Upload Success", "uploadFailed": "Upload Failed", "downloadSuccess": "Download Success", "downloadFailed": "Download Failed", "deleteSuccess": "Delete Success", "deleteFailed": "Delete Failed", "createSuccess": "Create Success", "createFailed": "Create Failed", "batchUploadComplete": "Batch Upload Complete", "batchUploadFailed": "Batch Upload Failed", "batchDownloadComplete": "Batch Download Complete", "batchDownloadFailed": "Batch Download Failed", "batchDeleteComplete": "Batch Delete Complete", "batchDeleteFailed": "Batch Delete Failed", "fileUploadSuccess": "File Upload Success", "fileUploadFailed": "File Upload Failed", "fileDownloadSuccess": "File Downloaded Successfully", "fileDownloadFailed": "File Download Failed, Please Retry", "itemDeleted": "Item Deleted", "deleteRetry": "Delete Failed, Please Retry", "folderCreated": "Folder Created Successfully", "folderCreateFailed": "Folder Creation Failed, Please Retry", "folderNameRequired": "Folder Name Cannot Be Empty", "enterFolderName": "Please Enter Folder Name", "folderUploadSuccess": "Folder Upload Complete", "folderUploadFailed": "Folder Upload Failed, Please Retry", "uploadError": "Error During Upload Process", "createRemoteRootFailed": "Failed to Create Remote Root Directory", "fileTypeNotAllowed": "File Type Not Allowed", "fileTypeNotAllowedMessage": "Only {types} files are supported", "networkError": "Network or Service Error", "fsApiNotSupportedTitle": "Unsupported Browser", "fsApiNotSupportedMessage": "Your browser does not support the File System Access API. Please use the latest version of Chrome, Edge, or Opera.", "successUploaded": "Successfully uploaded {count} items", "failedUploaded": "Failed {count}", "allFailed": "All {count} items failed to upload", "successDownloaded": "Successfully downloaded {count} files", "failedDownloaded": "Failed {count}", "allDownloadFailed": "All {count} files failed to download", "successDeleted": "Successfully deleted {count} items", "failedDeleted": "Failed {count}", "allDeleteFailed": "All {count} items failed to delete", "folderUploadComplete": "Folder Upload Complete", "fileUploadFailedWithTypes": "Upload Failed. Only {types} files are supported", "downloadDirectory": "Download Directory", "directoryDownloadSuccess": "Directory Downloaded Successfully", "directoryDownloadFailed": "Directory Download Failed, Please Retry"}, "chatHistory": {"title": "Chat History", "refreshList": "Refresh session list", "searchPlaceholder": "Search sessions...", "fetchError": "Failed to fetch chat history", "noSessions": "No chat sessions", "save": "Save", "cancel": "Cancel", "edit": "Edit session", "delete": "Delete session", "confirmDelete": "Are you sure you want to delete this session?", "sessionInUse": "Session is in use and cannot be deleted", "deleteSuccess": "Delete successful", "sessionDeleted": "Session deleted", "deleteFailed": "Delete failed", "deleteError": "Error deleting session", "saveSuccess": "Save successful", "titleUpdated": "Session title updated", "saveFailed": "Save failed", "updateError": "Error updating session title", "updateFailed": "Update failed", "previousPage": "Previous", "nextPage": "Next", "contextCleared": "The context has been cleared"}, "chatInput": {"deepResearch": "Deep Research", "deepResearchMode": "Deep Research Mode", "deepResearchModeDescription": "AI will analyze the codebase more deeply and generate more detailed explanations. This will consume more API calls but provide more comprehensive answers.", "noModels": "No models available", "processing": "Processing, please wait...", "askQuestion": "Ask AI about the codebase...", "sendMessage": "Send Message", "optimizePrompt": "Optimize User Input", "clearChatSession": "Clear session context", "clearChatSessionFailed": "Failed to clear chat session", "clearChatSessionSuccess": "Cleared chat session successfully", "sandboxNotReady": "The sandbox is not ready yet", "uploadImage": "Upload Image", "openContainer": "Open Container", "openingContainer": "Opening container...", "containerUnavailable": "Sandbox job or container is not available yet", "containerOpenFailed": "Failed to open the container, please retry later", "containerContextMissing": "Missing wiki or repository context, cannot open container", "containerPopupBlocked": "<PERSON><PERSON> blocked. Please allow opening a new window.", "openContainerTooltip": "Open sandbox container", "openContainerConfirmTitle": "Open the sandbox container?", "openContainerConfirmDesc": "The container console will open in a new window.", "openContainerConfirmAccept": "Open", "openContainerConfirmCancel": "Cancel"}, "ownerTransfer": {"loadUserFail": "Failed to load the user", "parameterVerification": "Parameter verification", "selectUser": "Select User", "successModifyOwner": "Successfully modified the owner of the wiki", "modifyOwnerFailed": "Failed to modify the owner of the wiki", "transferOwner": "Owner Transfer", "userSearchPlaceholder": "User Name/User Code", "noUsers": "No users", "perPage": "Per Page", "items": "Items", "previousPage": "Previous", "nextPage": "Next", "page": "Page {page} of {total}", "transferOwnerConfirm": "Transfer Owner Confirm", "transferOwnerMessage": "Are you sure you want to transfer the owner of the wiki to {user}[{userCode}]?"}, "personInfoDropdown": {"settings": "Settings", "monitor": "Monitor Screen", "operation": "Operation Screen", "sandboxManagement": "Sandbox Management", "operationData": "Today's Operation", "userManagement": "User Management", "logout": "Logout", "appManagement": "App Management"}, "projectDetail": {"wikiAdmin": "Wiki Admin", "wikiUser": "Wiki User", "copySuccess": "Copy Success", "copySuccessMessage": "Text has been copied to the clipboard", "copyFailedMessage": "Please copy manually", "loadFailed": "Load Failed", "loadFailedMessage": "Failed to load Wiki information", "deleteSuccess": "Delete Success", "deleteFailed": "Delete Failed", "deleteSuccessMessage": "User {user} has been deleted", "deleteFailedMessage": "Failed to delete user", "modifySuccess": "Modify Success", "modifyFailed": "Modify Failed", "modifySuccessMessage": "User {user} has been modified", "modifyFailedMessage": "Failed to modify user", "notFoundWikiInfo": "Not found Wiki information", "wikiDetail": "Wiki Detail", "repoName": "Repo Name", "repoBranch": "Repo Branch", "creator": "Creator", "createdTime": "Created Time", "productLine": "Product Line", "solution": "Solution", "copySolution": "Copy Solution", "projectName": "Project Name", "projectUrl": "Poroject Doc Address", "repoUrl": "Repo Url", "copyRepoUrl": "Copy Repo Url", "repoOwner": "Repo Owner", "whalecloudProject": "WhaleCloud Project", "language": "Language", "status": "Status", "completed": "Completed", "processing": "Processing", "failed": "Failed", "pending": "Pending", "product": "Product", "productVersion": "Version", "searchUser": "Search User", "user": "User", "role": "Role", "grantor": "<PERSON><PERSON>", "grantTime": "<PERSON>", "operation": "Operation", "unknown": "Unknown", "modify": "Edit", "delete": "Delete", "deleteConfirm": "Are you sure you want to delete this user?", "noUsers": "No users data", "addUser": "Add User", "projectVersion": "Version", "publishPackage": "Package", "productList": "Products", "productName": "Product Name", "productRepoUrl": "Product Repo", "noData": "No Data", "codeIndexTitle": "Code Repository Index", "codeIndexTip": "Sync will pull the latest code and update the index", "codeIndexRepoDetails": "Repository Progress Details", "syncProgressStatusLoading": "Getting index status...", "syncProgressStatusSyncing": "Index synchronization in progress...", "syncProgressStatusReady": "Index data is ready", "syncProgressStatusFailed": "Index synchronization failed, please retry", "syncProgressStatusError": "Failed to get index status, please try again later", "syncProgressAction": "Resync Index", "syncProgressActionRunning": "Synchronizing...", "syncProgressActionTip": "Sync will pull the latest code, recalculate the index and sync to DocChain.", "syncProgressActionRunningTip": "Synchronization in progress, please wait for the task to complete.", "syncProgressCalculating": "Calculating index data...", "requestFailed": "Request failed", "syncIndexFailed": "Sync index failed", "loadWikiInfoFailed": "Failed to load Wiki information", "loadUsersFailed": "Failed to load user list", "deleteUserFailed": "Failed to delete user", "modifyUserRoleFailed": "Failed to modify user role", "copyFailed": "Co<PERSON> failed", "unknownRepo": "Unknown repository", "unknownUser": "Unknown", "syncTaskQueued": "Sync task queued", "syncTaskInProgress": "Sync in progress", "syncTaskExecuting": "Sync task executing", "noDocChainData": "No DocChain sync data available", "syncCompleted": "Synced", "filesRemaining": "files", "localHeadInconsistent": "Local HEAD inconsistent with DocChain data", "localHeadNotSynced": "Local HEAD not synced to DocChain", "historicDataExists": "Historic data exists in DocChain, not yet linked to local", "noConvertibleFiles": "No convertible files found", "wikiBasicInfo": "Wiki Basic Information", "leftArea": "Left Area - Basic Information", "rightArea": "Right Area - Detailed Information", "codeIndex": "Code Index", "userList": "User List", "searchBoxAndAddButton": "Search box and add button", "userTable": "User table", "addUserModal": "Add user modal"}, "refreshModal": {"title": "Refresh Wiki Settings", "loadingModelConfigurations": "Loading model configurations...", "aiProvider": "AI Provider", "whaleCloud": "WhaleCloud", "selectModel": "Select Model", "forceRefresh": "Force Refresh", "forceRefreshDescription": "Force regenerate the entire Wiki regardless of code changes", "cancel": "Cancel", "confirmRefresh": "Confirm Refresh"}, "roleSelectionModal": {"userAuthorization": "User Authorization", "selectRole": "Select Role", "modifyUserRolesSuccess": "User roles modified successfully", "modifyUserRolesFailed": "Failed to modify user roles", "cancel": "Cancel", "submit": "Submit"}, "sandboxStatusDisplay": {"smartSandboxEnvironment": "Smart Sandbox Environment", "currentSmartSandboxResourceBusy": "Current Smart Sandbox Resource Busy", "preparingSmartSandboxEnvironment": "Preparing Smart Sandbox Environment...", "initializingEnvironmentConfiguration": "Initializing Environment Configuration...", "environmentPreparationTime": "Environment preparation takes 1-2 minutes, please be patient...", "assigningComputeResourcesAndPullingCodeRepository": "Assigning Compute Resources and Pulling Code Repository...", "installingDependenciesAndConfiguringDevelopmentEnvironment": "Installing Dependencies and Configuring Development Environment...", "detectingGeminiCliSession": "Detecting Gemini-CLI Session...", "retry": "Retry", "smartSandboxProvidesIndependentCodeExecutionEnvironment": "Smart Sandbox provides independent code execution environment", "supportSafeCodeAnalysisAndToolCall": "Support safe code analysis and tool call", "firstCreationEnvironmentMayTakeLongTime": "First creation environment may take longer, subsequent use will be faster"}, "searchPage": {"sessionOrWikiInfoNotExist": "Session or Wiki information does not exist.", "sessionNotExist": "Session does not exist.", "wikiInfoNotExist": "Wiki information does not exist.", "sessionNotRelatedToWiki": "Session is not related to Wiki.", "noPermissionToDialogWiki": "No permission to dialog this Wiki, please contact the creator of the Wiki to apply for permission.", "sessionNotBelongToCurrentUser": "Session does not belong to current user, please login and initiate the session.", "shareSuccess": "Share success", "shareSuccessMessage": "Share link has been copied to the clipboard", "shareFailedMessage": "Copy failed, please copy manually", "shareFailed": "Share failed", "shareDialog": "Share Conversation", "shareExpireTime": "Share Expiry", "enterExpireTime": "Enter expiry time (days)", "hours": "hours", "day": "days", "shareExpireHint": "Share link will expire after specified hours (1-8760 hours)", "quickSelect": "Quick Select", "confirmShare": "Confirm Share", "invalidExpireTime": "Please enter a valid expiry time", "researchProgress": "Research Progress", "iteration": "iteration", "initializing": "Initializing...", "errorDetails": "<PERSON><PERSON><PERSON>", "aiThinking": "AI is thinking...", "deepResearch": "Deep Research", "toolCall": "Tool Call", "collapsePanel": "Collapse Panel", "noToolCall": "No Tool Call", "noToolCallDescription": "AI calls tools here, detailed execution process and results will be displayed", "toolExecution": "Tool Execution", "executing": "Executing", "completed": "Completed", "dialogRound": "Dialog Round", "total": "Total", "toolCallParameter": "Parameter", "originalParameter": "Original Parameter", "toolExecutionResult": "Execution Result", "expandPanel": "Expand Panel", "errorDetailsTitle": "<PERSON><PERSON><PERSON>"}, "stepper": {"cancel": "Cancel", "previous": "Previous", "next": "Next", "generateWiki": "Generate Wiki"}, "wikiInfoEditor": {"repositoryMetadata": "Repository Metadata", "repositoryMetadataEmpty": "Repository metadata is empty", "repositoryMetadataException": "Synchronize repository metadata exception", "updateWikiInfo": "Update Wiki Info", "updateWikiInfoSuccess": "Successfully updated wiki information", "editWiki": "Edit Wiki", "productLine": "Product Line", "productLinePlaceholder": "Enter product line name to search", "product": "Product", "productPlaceholder": "Enter product name to search", "productVersion": "Product Version", "productVersionPlaceholder": "Enter product version name to search", "solution": "Solution", "solutionPlaceholder": "Enter solution name to search", "tags": "Tags", "comments": "Comments", "commentsPlaceholder": "Enter comments", "syncRepositoryMetadata": "Sync Repository Metadata", "syncRepositoryMetadataDescription": "Get product line, product, product version, and solution information from whaleDevCloud based on repository address. If no related information is found, please search and fill in manually", "docchainTopic": "<PERSON><PERSON><PERSON><PERSON> (Doc)", "projectKnowledge": "Project Knowledge Base", "searchTopics": "Enter the topic name to search", "cancel": "Cancel", "submit": "Submit", "desc": "Comments", "descPlaceholder": "Enter comments"}, "grantUser": {"loadUsersFailed": "Failed to load users", "userName": "Username", "projectName": "Project Name", "title": "Wiki Authorization", "isPublicVisible": "Public Visibility", "isPublicVisibleDescription": "When enabled, all users can view this Wiki content", "selectUser": "Select User", "searchUser": "Search by username, employee ID or department...", "noUsers": "No users available", "perPage": "Per page", "unit": "items", "lastPage": "Previous", "paging": "Page {page} of {total}", "nextPage": "Next", "selected": "Selected", "searchSelectedUser": "Search selected users...", "noSelectedUsers": "No selected users", "selectUserInLeftTable": "Please select users from the left table", "selectPriv": "Select Permission:", "wikiUser": "Wiki User", "wikiAdmin": "Wiki Admin", "grantSuccess": "Authorization successful", "grantFailed": "Authorization failed", "confirmGrant": "Confirm Authorization"}, "header": {"announcement": "Announcement", "hideAnnouncement": "Hide Announcement", "showAnnouncement": "Show Announcement", "taskProgress": "Task Progress", "helpDocument": "DeepWiki User Guide", "share": "Share", "login": "<PERSON><PERSON>", "user": "User", "toggle": "Toggle Theme", "product": "Product", "project": "Project", "switchToProductMode": "Switch to Product Mode", "switchToProjectMode": "Switch to Project Mode"}, "mermaid": {"diagramDisplay": "Diagram Display", "diagramDisplaying": "Diagram Displaying...", "clickToZoom": "Click to Zoom", "clickToViewFullscreen": "Click to View Fullscreen", "diagramRenderingError": "Diagram Rendering Error", "diagramSyntaxError": "Diagram has syntax errors and cannot be rendered.", "autoFixAttempted": "Auto-fix attempted:", "autoFixSuccess": "Diagram auto-fixed successfully", "fixesApplied": "Applied fixes:"}, "JobsProgressModal": {"title": "Task Progress", "stages": {"init": "Initializing", "download": "Downloading Code", "upload": "Analyzing Repository", "structure": "AI Generating Structure", "pages": "AI Generating Pages", "generate": "Generating Wiki", "completed": "Completed", "resuming": "Resuming", "paused": "Paused"}, "status": {"resuming": "Resuming", "pending_resume": "Pending Resume", "processing": "Processing", "paused": "Paused", "completed": "Completed", "cancelled": "Cancelled", "failed": "Failed", "pending": "Pending"}, "jobTypes": {"generation": "Generation Job", "refresh": "Refresh Job", "sync": "Sync Job", "unknown": "Unknown Job Type"}, "actions": {"retrying": "Retrying...", "retry": "Smart Retry", "deleting": "Deleting...", "deleteTask": "Delete Task", "resuming": "Resuming...", "resumeTask": "Resume Task", "restarting": "Restarting...", "restart": "<PERSON><PERSON>", "cancelling": "Cancelling...", "cancelTask": "Cancel Task", "viewWiki": "View Wiki"}, "tabs": {"processing": "Processing", "queued": "Queued", "failed": "Failed", "cancelled": "Cancelled"}, "details": {"branch": "Branch", "status": "Status", "createdBy": "Created By", "overallProgress": "Overall Progress", "taskStages": "Task Stages", "currentStage": "Current Stage", "processing": "Processing...", "fileProgress": "File Processing Progress", "files": "files", "taskInfo": "Task Information", "taskId": "Task ID", "createdTime": "Created Time", "repoUrl": "Repository URL", "mainRepo": "Primary Repository", "jobType": "Job Type", "subRepos": "Sub Repositories", "startTime": "Start Time", "retryCount": "Retry Count", "statusInfo": "Status Information", "errorInfo": "Error Information", "statusMessage": "Status Message", "stageMessage": "Stage Message", "executionStatus": "Execution Status", "running": "Running", "stopped": "Stopped"}, "emptyStates": {"noProcessingTasks": "No processing tasks currently", "noProcessingTasksDesc": "Tasks will appear here when you create new Wikis", "noQueuedTasks": "No queued tasks currently", "noQueuedTasksDesc": "You can view completed tasks here", "noFailedTasks": "No failed tasks currently", "noFailedTasksDesc": "All tasks have completed successfully", "noCancelledTasks": "No cancelled tasks currently", "noCancelledTasksDesc": "You can restart cancelled tasks here", "selectTask": "Select a task to view details", "selectTaskDesc": "Click on a task card on the left to view wiki details, progress and status"}, "errors": {"stageError": " stage encountered an error, please contact administrator.", "currentStage": "Current"}, "deleteConfirm": {"title": "Delete Task Confirmation", "message": "Are you sure you want to delete this cancelled task and its associated Wiki? This action cannot be undone."}}, "K8SJobManagement": {"createCustomJob": {"fillJobNameAndImage": "Please fill in Job name and container image", "fillAtLeastOneValidVolume": "Please configure at least one valid volume mount", "createCustomJobSuccess": "Custom Job created successfully", "createCustomJobFailed": "Failed to create custom Job", "networkError": "Network error", "statusQuerySuccess": "Status query successful", "statusQueryFailed": "Status query failed", "loadJobListFailed": "Failed to load Job list", "batchQueryStatusSuccess": "Batch query sandbox status completed", "batchQueryStatusFailed": "Batch query failed"}, "fillUserCodeAndGitUrl": "Please fill in user code and Git repository URL", "createSandboxSuccess": "Sandbox created successfully", "createSandboxFailed": "Failed to create sandbox", "networkError": "Network error", "cannotIdentifySandboxParams": "Cannot identify sandbox parameters (user_code/git_url) for this Job", "confirmDeleteSandbox": "Are you sure you want to delete the sandbox?\nUser: ${userCode}\nBranch: ${branch}\nRepository: ${gitUrl}", "deleteSandboxSuccess": "Sandbox deleted successfully", "deleteSandboxFailed": "Failed to delete sandbox", "getJobDetailsFailed": "Failed to get Job details", "getJobMetricsFailed": "Failed to get Job metrics", "confirmCleanupIdleJobs": "Are you sure you want to cleanup all idle Jobs?", "cleanupIdleJobsSuccess": "Successfully cleaned up idle Jobs", "cleanupIdleJobsFailed": "Failed to cleanup idle Jobs", "confirmFastGenerate": "Are you sure you want to regenerate this Wiki in fast mode?", "wikiFastGenerateSuccess": "Wiki fast generation started", "wikiFastGenerateFailed": "Failed to start fast generation", "loadUserQuotasFailed": "Failed to load quotas", "invalidQuota": "Please enter a valid non-negative integer quota", "saveUserQuotasSuccess": "Save successful", "saveUserQuotasFailed": "Save failed", "getUserSandboxesFailed": "Failed to get user sandboxes", "cannotIdentifySandboxGitUrl": "Cannot identify git_url for this sandbox", "actions": {"expandActions": "Expand Actions", "createSandbox": "Create Sandbox", "createCustomJob": "Custom Job", "quickCreateWiki": "Quick Create Wiki", "cleanupIdle": "Cleanup Idle", "problemWikis": "Problem Wikis", "batchCreateJobs": "Batch Create Jobs"}, "requestFailed": "Request failed", "loadProblemWikisFailed": "Failed to load problem wikis", "sandboxCenter": "Sandbox Management Center", "sandboxCenterDescription": "Modern Kubernetes Job & Sandbox Management", "currentEnvironment": "Current Environment: ", "searchUserPlaceholder": "Search users (Employee ID/Name)", "searchWikiPlaceholder": "Search Wiki (ID/Repository URL/Branch)", "tabs": {"jobs": "Jobs", "sandboxUsers": "Sandbox Users", "wikiInfo": "Wiki Info"}, "createCustomJobModal": {"title": "Create Custom Job", "jobName": "Job Name", "containerImage": "Container Image", "basicInfo": "Basic Information", "resourceConfig": "Resource Configuration", "cpuRequest": "CPU Request", "memoryRequest": "Memory Request", "cpuLimit": "CPU Limit", "memoryLimit": "Memory Limit", "volumeMountConfig": "Volume Mount Configuration", "addVolume": "Add Volume", "volumeName": "Volume Name", "hostPath": "Host Path", "containerPath": "Container Path", "readOnly": "Read Only", "delete": "Delete", "envVarConfig": "Environment Variable Configuration", "addEnvVar": "Add Environment Variable", "varName": "Variable Name", "varValue": "Variable Value", "optionalInfo": "Optional Information", "userCode": "User Code", "userName": "User Name", "gitRepoUrl": "Git Repository URL", "branchName": "Branch Name", "namespace": "Namespace", "cancel": "Cancel", "createJob": "Create Job", "creating": "Creating...", "userCodePlaceholder": "User Code (Optional)", "userNamePlaceholder": "User Name (Optional)", "gitRepoUrlPlaceholder": "https://github.com/user/repo.git (Optional)", "branchNamePlaceholder": "main (Optional)", "namespacePlaceholder": "Use system default (Optional)", "currentNamespace": "Current Namespace", "unknownError": "Unknown Error", "requestFailedNoResponse": "Request failed: No response", "stopMetricsMonitoring": "Stop metrics monitoring", "startMetricsMonitoring": "Start monitoring Job {jobName} metrics, refresh every 10 seconds", "gettingJobMetrics": "Getting Job {jobName} metrics...", "jobMetricsSuccess": "Successfully got Job {jobName} metrics:", "jobMetricsFailed": "Failed to get Job metrics:", "jobMetricsNetworkError": "Job metrics network error:", "refreshProblemWikisFailed": "Failed to refresh problem wikis list:", "idleJobsCount": " idle Jobs", "varNamePlaceholder": "VAR_NAME", "varValuePlaceholder": "VAR_VALUE"}, "batchCreateJobsModal": {"title": "Batch Create Jobs", "jobNumber": "Job Number", "jobNumberPlaceholder": "Enter the number of jobs to create", "wctApiKey": "WCT API Key", "wctApiKeyPlaceholder": "Enter WCT API Key", "cancel": "Cancel", "confirm": "Confirm", "creating": "Creating...", "fillAllFields": "Please fill in all fields", "invalidJobNumber": "Please enter a valid job number (positive integer)", "createSuccess": "Batch create jobs successful", "createFailed": "Batch create jobs failed"}, "currentJobs": "Current Jobs"}, "Markdown": {"copyCode": "copy code", "viewFile": "view file"}}, "useChat": {"toolExecutionUnknownError": "Tool execution unknown error", "toolExecutionFailedMessage": "Tool execution failed", "unknownError": "Unknown error", "modelExecutionToolError": "Model execution tool error", "modelQuotaLimit": "Model quota limit", "modelQuotaLimitDescription": "Current model daily quota has been used up, please try again tomorrow or contact the administrator for more quota.", "toolCallParameterError": "Tool call parameter error", "toolCallParameterErrorDescription": "Model tool call parameter is incorrect, please try again later. If the problem persists, please contact the technical support.", "apiForwardError": "API forward error", "apiForwardErrorDescription": "Model API forward exception, please try again later or contact the technical support.", "modelCallTimeout": "Model call timeout", "modelCallTimeoutDescription": "Model call tool timeout, please try again later or contact the technical support.", "modelServiceTimeout": "Model service timeout", "modelServiceTimeoutDescription": "Model service timeout, please try again later or contact the technical support.", "modelServiceError": "Model service error", "modelServiceErrorDescription": "Model service exception, please try again later or contact the technical support.", "apiKeyError": "API key error", "apiKeyErrorDescription": "Invalid API-KEY, please confirm that the API-KEY is correct and has not expired. If the problem persists, please contact the technical support.", "apiCallError": "API call error", "apiCallErrorDescription": "Model API call failed, please check the network connection or try again later.", "modelApiError": "Model API error", "modelApiErrorDescription": "Model API exception, please try again later or contact the technical support."}, "authApp": {"Search": "Search", "appName": "App Name", "appCode": "App Code", "copyAppId": "Copy APP_ID", "copyAppSecret": "Copy APP_SECRET", "comments": "Commnets", "createdTime": "Created Time", "state": "State", "operation": "Operation", "edit": "Edit", "delete": "Delete", "more": "More", "deleteApp": "Delete App", "deleteAppSuccess": "Deleted app successfully", "deleteAppFail": "Failed to delete app", "modifyAppState": "Modify App State", "modifyAppStateSucess": "Modified app state successfully", "modifyAppStateFail": "Failed to modify app state", "copySuccess": "<PERSON><PERSON>d successfully", "copyFail": "Failed to copy", "copyToClipboard": "Copied text to clipboard", "copyManual": "Please copy manually", "appNameOrAppCode": "App Name/App Code", "createApp": "Create App", "noData": "No Data", "total": "total", "records": "records", "recoredPerPage": "/page", "disableApp": "Disable App", "enableApp": "Enable App", "confirmDisableAppDesc": "Are you sure to disable app [{app_name}]", "confirmEnableAppDesc": "Are you sure to enable app [{app_name}]", "tokenManagement": "Token Management", "apiManagement": "API Management", "wikiManagement": "Wiki Management", "appSandbox": "App Sandbox", "bindWiki": "Bind Wiki", "bindWikiSuccess": "Wiki bindings updated successfully", "bindWikiFail": "Failed to update wiki bindings", "wikiName": "Wiki Name", "wikiRepo": "Repository", "wikiBranch": "Branch", "unbindWiki": "Unbind", "unbindWikiSuccess": "Wiki unbound successfully", "unbindWikiFail": "Failed to unbind wiki", "confirmUnbindWiki": "Are you sure you want to unbind this wiki?", "wikiBindingHint": "Only bound wiki code directories will be mounted in the sandbox.", "wikiBindingEmptyHint": "Submitting with no selection removes all bindings.", "boundWikiList": "Bound Wikis", "unboundWikiList": "Available Wikis", "noBoundWiki": "No wiki currently bound", "noAvailableWiki": "No additional wiki available", "noWikiData": "No wiki data", "searchWikiPlaceholder": "Search wiki", "refresh": "Refresh", "selectAll": "Select All", "unselectAll": "Unselect All", "start": "Start", "pause": "Pause", "restart": "<PERSON><PERSON>", "creator": "Creator", "sandboxName": "Sandbox Name", "sandboxStatus": "Sandbox Status", "podIp": "Pod IP", "notCreated": "Not Created", "startSuccess": "Start Success", "startFailed": "Start Failed", "stopSuccess": "Stop Success", "stopFailed": "Stop Failed", "sandboxInfo": "Sandbox Information", "validateParams": "Validate Params", "appNameNotEmpty": "The app name can not be empty", "appCodeNotEmpty": "The app code can not be empty", "createAppSuccess": "Created app successfully", "createAppFail": "Failed to create app", "editApp": "Edit App", "editAppSuccess": "Saved app info successfully", "editAppFail": "Failed to save app info", "appNamePlaceholder": "Please input app name", "appCodePlaceholder": "Please input app code", "commentsPlaceholder": "Please input comments", "cancel": "Cancel", "submit": "Submit", "copyToken": "<PERSON><PERSON>", "type": "Type", "generatedTime": "Created Time", "effectiveTime": "Effective time", "expireTime": "Expiration time", "deleteToken": "Delete Token", "deleteTokenSuccess": "Deleted token successfully", "deleteTokenFail": "Failed to delete token", "modifyTokenState": "Modify token state", "modifyTokenStateSuccess": "Modified token state successfully", "modiToken管理fyTokenStateFail": "Failed to modify token state", "generateToken": "Generate Token", "prePage": "Previous", "nextPage": "Next", "page": "Page {page} of {total}", "confirmDeleteToken": "Confirm to delete token", "generateTokenSuccess": "Generated token successfully", "generateTokenFail": "Failed to generate token", "tokenType": "Token Type", "systemLevel": "System", "userLevel": "User", "systemLevelComments": "User information is not required", "userLevelComments": "Carry the user's personal X-User-Token for identity verification", "expireTimePlaceholder": "Please select the expiration time", "apiName": "Name", "apiCode": "Code", "apiPath": "Path", "apiMethod": "Method", "category": "Category", "unbind": "Unbind", "copyPath": "Copy path", "unbindApi": "Unbind api", "unbindApiSuccess": "Unbound api successfully", "unbindApiFail": "Failed to unbind api", "modifyApiState": "Modify api state", "modifyApiStateSuccess": "Modified api state successfully", "modifyApiStateFail": "Failed to modify api state", "bindApi": "Bind Api", "confirmUnbindApi": "Are you sure to unbind api?", "selectApiPlaceholder": "Please select an api", "bindApiSuccess": "Bound api successfully", "bindApiFail": "Failed to bind api", "confirmDeleteAppDesc": "Are you sure to delete app [{app_name}]?", "disableToken": "Disable Token", "enableToken": "Enable Token", "confirmDisableTokenDesc": "Are you sure to disable <PERSON><PERSON>?", "confirmEnableTokenDesc": "Are you sure to enable Token", "enable": "Enable", "disable": "Disable", "confirmDelteToken": "Are you sure to delete token?", "disableApi": "Disable API", "enableApi": "Enable API", "confirmDisableApi": "Are you sure to disable api?", "confirmEnableApi": "Are you sure to enable api?", "updateToken": "Update token info", "updateTokenSuccess": "Updated token info successfully.", "updateTokenFail": "Failed to update token info.", "createdBy": "Creator"}}