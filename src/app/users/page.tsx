"use client";

import RoleSelectionModal from "@/components/RoleSelectionModal";
import DeleteConfirmModal from "@/components/DeleteConfirmModal";
import { useToast } from "@/contexts/ToastContext";
import { authFetch } from "@/utils/authFetch";
import React, { useEffect, useState } from "react";
import ReactPaginate from "react-paginate";
import { useLanguage } from "@/contexts/LanguageContext";
import { usePageTitle } from "@/utils/pageTitle";

interface UserInfo {
  id: number; // 用户id
  user_name: string; // 用户名称
  user_code: string; // 用户编码
  org: string; // 组织
  dept: string; // 部门
  job: string; // 职位
  state: boolean; // 状态
  roles: string;
}

const Users = () => {
  // 处理通过 URL 参数传递的页面标题，如果没有则使用默认标题
  usePageTitle('DeepWiki 用户管理')
  
  const { addToast } = useToast();

  const [users, setUsers] = useState<UserInfo[]>([]);
  const [selectedUserId, setSelectedUserId] = useState<number>();
  const [isRoleSelectionModalOpen, setIsRoleSelectionModalOpen] =
    useState<boolean>(false);
  const [search, setSearch] = useState<string>("");
  const [departments, setDepartments] = useState<string[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<string>("");
  const [size] = useState<number>(10);
  const [pages, setPages] = useState<number>(0);
  const [page, setPage] = useState<number>(0);
  const { messages } = useLanguage();
  
  // 确认框状态管理
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [confirmAction, setConfirmAction] = useState<{
    userId: number;
    newState: boolean;
    action: 'freeze' | 'activate';
  } | null>(null);

  useEffect(() => {
    fetchDepartments();
  }, []);

  useEffect(() => {
    fetchUsers();
  }, [page]);

  const fetchUsers = async () => {
    const response = await authFetch(
      `/api/users?search=${search}&dept=${selectedDepartment}&page=${page}&size=${size}`
    );
    if (response && response.ok) {
      const data = await response.json();
      setPages(Math.ceil(data.total / size));
      setUsers(data.data);
    } else {
      if (response) {
        if (response.status === 403) {
          const data = await response.json();
          addToast?.({
            type: "error",
            title: "",
            message: data.message ?? messages.user.noPermissionToView,
          });
        }
      }
    }
  };

  const fetchDepartments = async () => {
    const response = await authFetch("/api/departments");
    if (response && response.ok) {
      const data = await response.json();
      setDepartments(data);
    }
  };

  const modifyUserState = async (id: number, state: boolean) => {
    const response = await authFetch("/api/users/state", {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ id, state }),
    });
    if (response && response.ok) {
      fetchUsers();
      addToast?.({ type: "success", title: "", message: messages.user.successModifyUserState });
    } else {
      if (response) {
        const data = await response.json();
        addToast?.({
          type: "error",
          title: "",
          message: data.detail ?? messages.user.modifyUserStateFailed,
        });
      }
    }
  };

  const handleUserStateChange = (userId: number, currentState: boolean, action: 'freeze' | 'activate') => {
    setConfirmAction({
      userId,
      newState: !currentState,
      action
    });
    setShowConfirmModal(true);
  };

  const handleConfirmStateChange = () => {
    if (confirmAction) {
      modifyUserState(confirmAction.userId, confirmAction.newState);
      setShowConfirmModal(false);
      setConfirmAction(null);
    }
  };

  const onEnter = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.code === "Enter") {
      if (page) {
        setPage(0);
      } else {
        fetchUsers();
      }
    }
  };

  return (
    <div className="users-container p-8 flex flex-col gap-y-4">
      <div className="top flex gap-x-4">
        <div className="w-xs">
          <input
            className="input-base"
            placeholder={messages.user.userSearchPlaceholder}
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            onKeyDown={onEnter}
          />
        </div>
        <div className="w-2xs">
          <select
            className="input-base"
            value={selectedDepartment}
            onChange={(e) => setSelectedDepartment(e.target.value)}
          >
            <option value="">{messages.user.selectDepartment}</option>
            {departments.map((department) => (
              <option key={department}>{department}</option>
            ))}
          </select>
        </div>
        <button
          className="btn-primary mr-3 cursor-pointer"
          onClick={() => {
            if (page) {
              setPage(0);
            } else {
              fetchUsers();
            }
          }}
        >
          {messages.user.search}
        </button>
      </div>
      <div className="table-container">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="px-6 py-4 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                {messages.user.username}
              </th>
              <th className="px-6 py-4 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                {messages.user.userCode}
              </th>
              <th className="px-6 py-4 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                {messages.user.department}
              </th>
              <th className="px-6 py-4 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                {messages.user.organization}
              </th>
              <th className="px-6 py-4 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                {messages.user.position}
              </th>
              <th className="px-6 py-4 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                {messages.user.role}
              </th>
              <th className="px-6 py-4 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                {messages.user.state}
              </th>
              <th className="px-6 py-4 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                {messages.user.operation}
              </th>
            </tr>
          </thead>
          <tbody>
            {users.map((user) => (
              <tr
                key={user.id}
                className="border-b border-gray-200"
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="text-sm font-medium text-gray-800">
                    {user.user_name}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="text-sm font-medium text-gray-800">
                    {user.user_code}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="text-sm font-medium text-gray-800">
                    {user.dept}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="text-sm font-medium text-gray-800">
                    {user.org}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="text-sm font-medium text-gray-800">
                    {user.job}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="text-sm font-medium text-gray-800">
                    {user.roles}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-semibold ${
                      user.state
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                    }`}
                  >
                    {user.state ? messages.user.effective : messages.user.invalid}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-4">
                  {user.state ? (
                    <button
                      onClick={() => handleUserStateChange(user.id, user.state, 'freeze')}
                      className="text-red-600 hover:text-red-800 hover:underline transition-colors cursor-pointer"
                    >
                      {messages.user.freeze}
                    </button>
                  ) : (
                    <button
                      onClick={() => handleUserStateChange(user.id, user.state, 'activate')}
                      className="text-green-600 hover:text-green-800 hover:underline transition-colors cursor-pointer"
                    >
                      {messages.user.activate}
                    </button>
                  )}
                  <button
                    onClick={() => {
                      setIsRoleSelectionModalOpen(true);
                      setSelectedUserId(user.id);
                    }}
                    className="text-blue-600 hover:text-blue-800 hover:underline transition-colors cursor-pointer"
                  >
                    {messages.user.grant}
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="pagination-container">
        <ReactPaginate
          forcePage={page}
          pageCount={pages}
          pageRangeDisplayed={5}
          marginPagesDisplayed={2}
          onPageChange={(e) => setPage(e.selected)}
          containerClassName="flex flex-row items-center justify-end gap-1"
          pageClassName=""
          pageLinkClassName="pagination-link"
          previousClassName=""
          previousLinkClassName="pagination-link"
          nextClassName=""
          nextLinkClassName="pagination-link"
          breakClassName=""
          breakLinkClassName="pagination-link"
          activeClassName=""
          activeLinkClassName="pagination-link-active"
          disabledClassName=""
          disabledLinkClassName="pagination-link-disabled"
          previousLabel="‹"
          nextLabel="›"
          breakLabel="..."
        />
      </div>

      {isRoleSelectionModalOpen && (
        <RoleSelectionModal
          user_id={selectedUserId}
          onClose={() => {
            setIsRoleSelectionModalOpen(false);
            fetchUsers();
          }}
        />
      )}

      {/* 用户状态修改确认框 */}
      <DeleteConfirmModal
        isOpen={showConfirmModal}
        onClose={() => {
          setShowConfirmModal(false);
          setConfirmAction(null);
        }}
        onConfirm={handleConfirmStateChange}
        title={confirmAction?.action === 'freeze' ? messages.user.freezeConfirm : messages.user.activateConfirm}
        message={
          confirmAction
            ? `${messages.user.confirm} ${confirmAction.action === 'freeze' ? messages.user.freeze : messages.user.activate} ${users.find(u => u.id === confirmAction.userId)?.user_name}[${users.find(u => u.id === confirmAction.userId)?.user_code}] ?`
            : ''
        }
      />
    </div>
  );
};

export default Users;
