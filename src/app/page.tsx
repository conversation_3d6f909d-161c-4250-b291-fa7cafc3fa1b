'use client';

import React, { useState, useEffect } from 'react';
import CountUp from 'react-countup';
import ProcessedProjects from '@/components/ProcessedProjects';
import { Header } from '@/components';
import { useSettings } from '@/contexts/SettingsContext';
import SettingsModal from '@/components/SettingsModal';
import { JobsProgressModal } from '@/components/JobsProgressModal'; // 导入

import { useLanguage } from '@/contexts/LanguageContext';
import { useToast } from '@/contexts/ToastContext';

import NewWiki from '@/components/NewWiki';
import { authFetch } from '@/utils/authFetch';

// Define the demo mermaid charts outside the component
// const DEMO_FLOW_CHART = `graph TD
//   A[Code Repository] --> B[DeepWiki]
//   B --> C[Architecture Diagrams]
//   B --> D[Component Relationships]
//   B --> E[Data Flow]
//   B --> F[Process Workflows]

//   style A fill:#f9d3a9,stroke:#d86c1f
//   style B fill:#d4a9f9,stroke:#6c1fd8
//   style C fill:#a9f9d3,stroke:#1fd86c
//   style D fill:#a9d3f9,stroke:#1f6cd8
//   style E fill:#f9a9d3,stroke:#d81f6c
//   style F fill:#d3f9a9,stroke:#6cd81f`;

// const DEMO_SEQUENCE_CHART = `sequenceDiagram
//   participant User
//   participant DeepWiki
//   participant GitHub

//   User->>DeepWiki: Enter repository URL
//   DeepWiki->>GitHub: Request repository data
//   GitHub-->>DeepWiki: Return repository data
//   DeepWiki->>DeepWiki: Process and analyze code
//   DeepWiki-->>User: Display wiki with diagrams

//   %% Add a note to make text more visible
// Note over User,GitHub: DeepWiki supports sequence diagrams for visualizing interactions`;

export default function Home() {
  const { messages } = useLanguage();
  // const { projects, isLoading: projectsLoading } = useProcessedProjects();
  const { settings, isSettingsLoaded } = useSettings();
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const { addToast } = useToast();

  // Create a simple translation function
  const t = (key: string, params: Record<string, string | number> = {}): string => {
    // Split the key by dots to access nested properties
    const keys = key.split('.');
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let value: any = messages;

    // Navigate through the nested properties
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        // Return the key if the translation is not found
        return key;
      }
    }

    // If the value is a string, replace parameters
    if (typeof value === 'string') {
      return Object.entries(params).reduce((acc: string, [paramKey, paramValue]) => {
        return acc.replace(`{${paramKey}}`, String(paramValue));
      }, value);
    }

    // Return the key if the value is not a string
    return key;
  };

  // Wiki type state - default to comprehensive view
  const [projectsCount, setProjectsCount] = useState(0);

  // State for configuration modal
  const [isNewWikiOpen, setIsNewWikiOpen] = useState(false);
  const [isJobsProgressModalOpen, setIsJobsProgressModalOpen] = useState(false);
  // 项目刷新触发器
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Listen for auth-error-403 events
  useEffect(() => {
    const handleAuthError = () => {
      addToast({
        type: 'error',
        title: messages.home.authError,
        message: ''
      });
    };

    window.addEventListener('auth-error-403', handleAuthError);

    return () => {
      window.removeEventListener('auth-error-403', handleAuthError);
    };
  }, [addToast, t]);

    // 页面加载时自动获取项目数量
  useEffect(() => {
    // 只在客户端执行
    const fetchProjectsCount = async () => {
      try {
        const res = await authFetch('/api/wiki/count');
        if (res && res.ok) {
          const result = await res.json();
          if (result?.data?.count !== undefined) {
            setProjectsCount(result.data.count);
          }
        }
      } catch (error) {
        // 可选：错误处理
        console.error("查询项目数量失败", error);
        setProjectsCount(0);
      }
    };
    fetchProjectsCount();
  }, []);

  const handleAddNew = () => {
    if (!isSettingsLoaded) return;

    // 缺少必填令牌
    if (!settings.apiKey || !settings.whaleDevCloudToken) {
      addToast({
        type: 'warning',
        title: messages.alert.title,
        message: messages.alert.message,
        action: {
          label: messages.alert.action_label,
          onClick: () => setIsSettingsModalOpen(true),
        },
      });
      return;
    }

    // apikey 无效时不允许创建 wiki
    if (!settings.isApiKeyValid) {
      addToast({
        type: 'warning',
        title: messages.common.error || '错误',
        message: messages?.common?.invalidApiKey || '大模型令牌无效，请前往设置页面正确配置。',
        action: {
          label: messages.alert.action_label,
          onClick: () => setIsSettingsModalOpen(true),
        },
      });
      return;
    }

    setIsNewWikiOpen(true);
  };

  // Render the main page content
  return (
    <div className="flex flex-col min-h-screen bg-[var(--background)] text-[var(--foreground)]">
      <div>
        <Header showProjectModeToggle={true} onOpenSettings={() => setIsSettingsModalOpen(true)} onOpenJobs={() => setIsJobsProgressModalOpen(true)} />
      </div>
      <main className="flex-grow container mx-auto px-4 py-6">
        <div className="text-center">

          {/* 修改为始终显示所有已处理的项目 */}
          <div className="w-full">
            {/* Header section */}
            <div className="flex flex-col items-center w-full max-w-4xl mb-6 mx-auto px-4">
                <div className="text-center w-full">
                  <h2 className="text-[26px] font-normal mb-0 text-[#002C5D] dark:text-[#FFFFFF]">DeepWiki</h2>
                  <p className="text-[14px] font-normal text-[#556A82] dark:text-[#C2D4E5]">
                    {messages.common.deepwikiDescription}
                    <CountUp 
                      end={projectsCount} 
                      duration={2.5}
                      delay={0.5}
                      className="inline text-[24px] font-semibold text-[#2C7FFF] dark:text-[#2C7FFF] px-2"
                    />
                    {messages.common.deepwikiCount}
                  </p>
                </div>
            </div>

            {/* Show all processed projects */}
            <ProcessedProjects
              showHeader={false}
              messages={messages}
              className="w-full"
              onAddNew={handleAddNew}
              refreshTrigger={refreshTrigger}
            />
          </div>
        </div>
      </main>
      
      <footer className="w-full bg-[var(--card-bg)] border-t border-[var(--border-color)] py-4">
        <div className="max-w-6xl mx-auto px-4 flex justify-center">
          <p className="text-[var(--muted)] text-sm font-serif">{messages.footer.copyright}</p>
        </div>
      </footer>

      {isNewWikiOpen &&
        <NewWiki
          onClose={() => setIsNewWikiOpen(false)}
          onSuccess={() => {
            setIsJobsProgressModalOpen(true);
            setIsNewWikiOpen(false);
            // 触发项目列表刷新
            setRefreshTrigger(prev => prev + 1);
          }}
        />
      }

      <SettingsModal isOpen={isSettingsModalOpen} onClose={() => setIsSettingsModalOpen(false)} />
      <JobsProgressModal isOpen={isJobsProgressModalOpen} onClose={() => setIsJobsProgressModalOpen(false)} />
    </div>
  );
}