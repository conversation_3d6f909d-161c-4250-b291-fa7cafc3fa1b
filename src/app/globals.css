@import "tailwindcss";
@custom-variant dark (&:where(.dark, .dark *));
/* 增加规则限制，优先使用类名中的dark主题样式 */

:root {
  --background: #ffffff;
  --foreground: #2A2A2A;
  --shadow-color: rgba(140, 149, 159, 0.15);
  --accent-primary: #033063; /* Blue */
  --accent-secondary: #f6f8fa; /* Subtle Background */
  --border-color: #d0d7de; /* Border */
  --card-bg: #ffffff;
  --highlight: #04356c; /* Blue */
  --muted: #57606a; /* Muted Text */
  --link-color: #14375e; /* Blue */
  --user-message-bg: transparent;
  --ai-message-bg: transparent;
  
  /* 新增变量 */
  --panel-bg: #ffffff;
  --panel-bg-transparent: rgba(255, 255, 255, 0.5);
  --panel-bg-hover: rgba(255, 255, 255, 0.8);
  --tab-bg: rgba(249, 250, 251, 0.5);
  --tab-bg-hover: rgba(255, 255, 255, 0.5);
  --tab-border: #e5e7eb;
  --tab-text: #6b7280;
  --tab-text-hover: #374151;
  --tab-text-active: #2563eb;
  --tab-border-active: #2563eb;
  --badge-blue-bg: #dbeafe;
  --badge-blue-text: #2563eb;
  --badge-teal-bg: #ccfbf1;
  --badge-teal-text: #0d9488;
  --tool-card-bg: rgba(255, 255, 255, 0.6);
  --tool-card-border: rgba(229, 231, 235, 0.4);
  --tool-card-shadow: rgba(0, 0, 0, 0.05);
  --tool-expanded-bg: rgba(249, 250, 251, 0.6);
  --tool-expanded-border: rgba(229, 231, 235, 0.4);
  --tool-blue-bg: rgba(219, 234, 254, 0.5);
  --tool-blue-border: rgba(59, 130, 246, 0.5);
  --tool-teal-bg: rgba(204, 251, 241, 0.5);
  --tool-teal-border: rgba(13, 148, 136, 0.5);
  --tool-blue-text: #1d4ed8;
  --tool-teal-text: #0d9488;
  --tool-status-executing: #3b82f6;
  --tool-status-completed: #10b981;
  --tool-arrow: #9ca3af;
  --tool-param-bg: rgba(255, 255, 255, 0.7);
  --tool-param-border: rgba(229, 231, 235, 0.5);
  --tool-param-text: #374151;
  --tool-result-bg: rgba(255, 255, 255, 0.7);
  --tool-result-border: rgba(229, 231, 235, 0.5);
  --tool-result-text: #374151;
  --collapse-btn-bg: rgba(255, 255, 255, 0.9);
  --collapse-btn-border: rgba(229, 231, 235, 0.5);
  --collapse-btn-hover: #ffffff;
  --collapse-btn-text: #4b5563;
  --collapse-btn-hover-text: #2563eb;
  --empty-state-bg: rgba(249, 250, 251, 0.5);
  --empty-state-border: rgba(229, 231, 235, 0.5);
  --empty-state-text: #6b7280;
  --empty-state-icon: #9ca3af;
  --empty-state-desc: #6b7280;
  --head-text-color: #556A82;
  --login-btn-bg: #2C7FFF;
  --login-btn-text: #FFFFFF;
  --project-text-color: #002C5D;
  --project-text-bg: #EAECF1;
  --project-text-border: #C2CAD6;
}

.dark {
  /* 更典雅的色彩方案 - 深色模式 */
  --background: #0d1117; /* Dark Mode Background */
  --foreground: #FFFFFF; /* Dark Mode Text */
  --shadow-color: rgba(0, 0, 0, 0.3);
  --accent-primary: #58a6ff; /* Lighter blue for dark mode */
  --accent-secondary: #161b22; /* Dark Mode Secondary Bg */
  --border-color: #30363d; /* Dark Mode Border */
  --card-bg: #161b22; /* Dark Mode Card Bg - Changed from #0d1117 */
  --highlight: #1f6feb; /* Dark Mode Focus */
  --muted: #8b949e; /* Dark Mode Muted Text */
  --link-color: #58a6ff; /* Dark Mode Link */
  --user-message-bg: transparent;
  --ai-message-bg: transparent;
  
  /* 深色模式新增变量 */
  --panel-bg: #161b22;
  --panel-bg-transparent: rgba(30, 41, 59, 0.5);
  --panel-bg-hover: rgba(30, 41, 59, 0.8);
  --tab-bg: #161b22;
  --tab-bg-hover: rgba(30, 41, 59, 0.5);
  --tab-border: #374151;
  --tab-text: #9ca3af;
  --tab-text-hover: #d1d5db;
  --tab-text-active: #60a5fa;
  --tab-border-active: #60a5fa;
  --badge-blue-bg: rgba(30, 58, 138, 0.5);
  --badge-blue-text: #93c5fd;
  --badge-teal-bg: rgba(13, 148, 136, 0.5);
  --badge-teal-text: #5eead4;
  --tool-card-bg: rgba(30, 41, 59, 0.4);
  --tool-card-border: rgba(55, 65, 81, 0.4);
  --tool-card-shadow: rgba(0, 0, 0, 0.2);
  --tool-expanded-bg: rgba(30, 41, 59, 0.3);
  --tool-expanded-border: rgba(55, 65, 81, 0.4);
  --tool-blue-bg: rgba(30, 58, 138, 0.3);
  --tool-blue-border: rgba(59, 130, 246, 0.5);
  --tool-teal-bg: rgba(13, 148, 136, 0.3);
  --tool-teal-border: rgba(13, 148, 136, 0.5);
  --tool-blue-text: #93c5fd;
  --tool-teal-text: #5eead4;
  --tool-status-executing: #60a5fa;
  --tool-status-completed: #34d399;
  --tool-arrow: #9ca3af;
  --tool-param-bg: rgba(17, 24, 39, 0.5);
  --tool-param-border: rgba(55, 65, 81, 0.5);
  --tool-param-text: #d1d5db;
  --tool-result-bg: rgba(17, 24, 39, 0.5);
  --tool-result-border: rgba(55, 65, 81, 0.5);
  --tool-result-text: #d1d5db;
  --collapse-btn-bg: rgba(30, 41, 59, 0.9);
  --collapse-btn-border: rgba(55, 65, 81, 0.5);
  --collapse-btn-hover: rgba(30, 41, 59, 1);
  --collapse-btn-text: #d1d5db;
  --collapse-btn-hover-text: #60a5fa;
  --empty-state-bg: rgba(30, 41, 59, 0.5);
  --empty-state-border: rgba(55, 65, 81, 0.5);
  --empty-state-text: #9ca3af;
  --empty-state-icon: #6b7280;
  --empty-state-desc: #6b7280;
  --head-text-color: #C2D4E5;
  --login-btn-bg: #C2D4E5;
  --login-btn-text: #1F2937;
  --project-text-color: #D4E1ED;
  --project-text-bg: #1F293B;
  --project-text-border: transparent;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), sans-serif;
}

/* 更精致的阴影样式 */
.shadow-custom {
  box-shadow: 0 4px 8px -2px var(--shadow-color);
}

/* 纸质纹理背景 */
.paper-texture {
  background-color: var(--card-bg);
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23e0d8c8' fill-opacity='0.07' fill-rule='evenodd'/%3E%3C/svg%3E");
}

/* 深色模式纸质纹理 */
.dark .paper-texture {
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23333333' fill-opacity='0.07' fill-rule='evenodd'/%3E%3C/svg%3E");
}

/* 典雅的按钮样式 */
.btn-elegant {
  background-color: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 0.25rem;
  padding: 0.5rem 1.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-elegant:hover {
  background-color: var(--highlight);
}

.btn-elegant:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.15);
  transition: width 0.3s ease;
}

.btn-elegant:hover:before {
  width: 100%;
}

/* 典雅的输入框样式 */
.input-elegant {
  background-color: transparent;
  border: 1px solid var(--border-color);
  border-radius: 0.25rem;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
}

.input-elegant:focus {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px rgba(138, 124, 113, 0.2);
  outline: none;
}

/* 典雅的卡片样式 */
.card-elegant {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  overflow: hidden;
  transition: all 0.3s ease;
}

.card-elegant:hover {
  box-shadow: 0 4px 12px var(--shadow-color);
}

/* 行限制工具类 */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* 打字指示器动画 */
.typing-indicator {
  display: flex;
  align-items: center;
}

.typing-indicator span {
  height: 6px;
  width: 6px;
  margin: 0 2px;
  background-color: var(--accent-primary);
  border-radius: 50%;
  display: inline-block;
  opacity: 0.4;
}

.typing-indicator span:nth-child(1) {
  animation: typing 1.2s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
  animation: typing 1.2s infinite ease-in-out 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation: typing 1.2s infinite ease-in-out 0.4s;
}

@keyframes typing {
  0% {
    transform: translateY(0);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-4px);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0);
    opacity: 0.4;
  }
}

@keyframes slide-in-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-out-right {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.animate-slide-in-right {
  animation: slide-in-right 0.3s ease-out forwards;
}

.animate-slide-out-right {
  animation: slide-out-right 0.3s ease-in forwards;
}

/* Markdown 内容样式优化 */
.markdown-content {
  font-size: 0.95rem;
  line-height: 1.7;
  color: var(--foreground);
}

.markdown-content p {
  margin-bottom: 1.1rem;
  white-space: pre-wrap;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1.6rem;
  margin-bottom: 1rem;
  font-weight: 600;
  line-height: 1.25;
  color: var(--foreground);
}

.markdown-content h1 {
  font-size: 1.5rem;
}

/* Base Input styles */
.input-base {
  @apply w-full px-3 py-2 text-sm rounded-md bg-[var(--accent-secondary)] text-[var(--foreground)] border border-[var(--border-color)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] focus:border-[var(--accent-primary)] transition-colors;
}

.input-base::placeholder {
  @apply text-[var(--muted)];
}

/* Primary Button */
.btn-primary {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-[var(--accent-primary)] border border-transparent rounded-md shadow-sm hover:bg-[var(--highlight)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[var(--accent-primary)] transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
}

/* Secondary Button */
.btn-secondary {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-[var(--foreground)] bg-[var(--card-bg)] border border-[var(--border-color)] rounded-md shadow-sm hover:bg-[var(--accent-secondary)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[var(--accent-primary)] transition-colors;
}

.markdown-content h2 {
  font-size: 1.3rem;
}

.markdown-content h3 {
  font-size: 1.15rem;
}

.markdown-content ul,
.markdown-content ol {
  margin-bottom: 1.1rem;
  padding-left: 1.5rem;
}

.markdown-content ul {
  list-style-type: disc;
}

.markdown-content ol {
  list-style-type: decimal;
}

.markdown-content li {
  margin-bottom: 0.5rem;
}

.markdown-content code {
  font-family: var(--font-mono), monospace;
  background-color: var(--accent-secondary);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.85em;
}

.markdown-content pre {
  background-color: var(--accent-secondary);
  padding: 1em;
  border-radius: 5px;
  overflow-x: auto;
  margin-bottom: 1rem;
}

.markdown-content pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
}

.markdown-content a {
  color: var(--link-color);
  text-decoration: underline;
}

.markdown-content a:hover {
  text-decoration: none;
}

.markdown-content blockquote {
  border-left: 3px solid var(--accent-primary);
  padding-left: 1rem;
  margin-left: 0;
  color: var(--muted);
}

.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}

.markdown-content table th,
.markdown-content table td {
  border: 1px solid var(--border-color);
  padding: 0.5rem;
}

.markdown-content table th {
  background-color: var(--accent-secondary);
  font-weight: 600;
}

.dark .markdown-content code {
  background-color: var(--accent-secondary);
}

.dark .markdown-content pre {
  background-color: var(--accent-secondary);
}

.dark .markdown-content blockquote {
  color: var(--muted);
}

.dark .markdown-content table th,
.dark .markdown-content table td {
  border-color: var(--border-color);
}

.dark .markdown-content table th {
  background-color: var(--accent-secondary);
}

/* 对话窗口自定义样式 */
.chat-window {
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.85);
  transform-origin: center center;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
}

.dark .chat-window {
  background-color: rgba(30, 28, 26, 0.85);
}

.chat-header {
  background-color: var(--accent-secondary);
}

/* 用户消息气泡样式优化 */
.user-message {
  background-color: var(--user-message-bg);
  padding: 0.5rem 0;
  max-width: 100%;
  font-size: 0.95rem;
  line-height: 1.5;
}

/* AI消息样式优化，使其更像文章 */
.ai-message {
  padding: 0.5rem 0;
  max-width: 100%;
  font-size: 0.95rem;
  line-height: 1.5;
}

/* 用户头像和AI头像容器统一样式 */
.avatar-container {
  width: 32px;
  height: 32px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  margin-top: 0.25rem;
}

/* 用户头像容器样式 */
.user-avatar {
  background-color: var(--accent-primary);
  color: white;
}

/* AI头像容器样式 */
.ai-avatar {
  background-color: var(--accent-secondary);
  color: var(--foreground);
}

/* 改进头像与消息对齐 */
.message-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 12px;
}

/* 发送按钮样式优化 */
.send-button {
  position: absolute;
  right: 8px;
  bottom: 8px;
  z-index: 10;
  width: 32px;
  height: 32px;
  min-width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.send-button:hover:not(:disabled) {
  transform: scale(1.05);
  background-color: var(--highlight);
}

.send-button svg {
  width: 16px;
  height: 16px;
}

/* 源码引用链接样式 */
.file-link {
  display: inline-flex;
  align-items: center;
  background-color: var(--accent-secondary);
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  border: 1px solid var(--border-color);
  color: var(--foreground);
  font-size: 0.875rem;
  margin: 0.25rem 0.5rem 0.25rem 0;
  transition: all 0.2s ease;
}

.file-link:hover {
  background-color: var(--highlight);
  opacity: 0.9;
}

.file-link-icon {
  color: var(--muted);
  margin-right: 0.375rem;
}

.file-link-name {
  font-weight: 500;
  color: var(--foreground);
}

.file-link-line {
  font-size: 0.75rem;
  color: var(--muted);
  margin-left: 0.25rem;
}

/* 修复全屏模式下按钮样式 */
.btn-elegant {
  background-color: var(--accent-primary);
  color: white !important;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  min-width: 28px;
  flex-shrink: 0;
}

/* 确保全屏模式下按钮正确显示 */
.btn-elegant svg {
  color: white !important;
}

/* 修复输入框样式，确保按钮不会被挤到换行 */
textarea.input-elegant {
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
  padding-right: 2.5rem !important; /* 确保右侧有足够空间放置按钮 */
}

/* 改进输入区域样式 */
.input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  border-radius: 0.75rem;
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
  overflow: hidden;
  padding-right: 40px;
}

.input-container:focus-within {
  box-shadow: 0 2px 6px rgba(0,0,0,0.08);
  border-color: var(--accent-primary);
}

/* 发送按钮样式优化 */
.send-button {
  position: absolute;
  right: 8px;
  bottom: 8px;
  z-index: 10;
  width: 32px;
  height: 32px;
  min-width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  background-color: var(--accent-primary);
  color: white;
  cursor: pointer;
}

.send-button:hover:not(:disabled) {
  transform: scale(1.05);
  background-color: var(--highlight);
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);
}

.send-button svg {
  width: 16px;
  height: 16px;
  color: white;
}

/* 模式特定样式，简化处理，移除动画效果 */
.mode-sidebar {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.mode-floating {
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.mode-fullscreen {
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

/* 模式切换按钮组 */
.mode-switcher {
  display: flex;
  background-color: var(--accent-secondary);
  border-radius: 6px;
  padding: 2px;
  margin-right: 4px;
}

.mode-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
}

.mode-button.active {
  background-color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.dark .mode-button.active {
  background-color: var(--accent-primary);
  color: white;
}

.source-link {
  display: inline-flex;
  align-items: center;
  background-color: var(--accent-secondary);
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  color: var(--foreground);
  text-decoration: none;
  border: 1px solid var(--border-color);
  transition: opacity 0.2s;
  margin-right: 0.25rem;
  margin-bottom: 0.25rem;
}

.source-link:hover {
  opacity: 0.8;
}

.source-link-icon {
  margin-right: 0.375rem;
  color: var(--accent-primary);
}

.source-link-text {
  font-weight: 500;
}

.source-link-lines {
  margin-left: 0.25rem;
  font-size: 0.75rem;
  color: var(--muted);
}

/* 修复Ask组件中的硬编码颜色 */
.bg-white\/80 {
  background-color: var(--card-bg);
}

.dark .bg-white\/80, 
html[data-theme='dark'] .bg-white\/80 {
  background-color: var(--card-bg);
}

.bg-gray-900\/80 {
  background-color: var(--card-bg);
}

/* 添加对纯白色背景的处理，解决wiki内容区域在暗色模式下背景仍为白色的问题 */
.dark .bg-white,
html[data-theme='dark'] .bg-white {
  background-color: var(--card-bg);
}

.dark .prose,
html[data-theme='dark'] .prose {
  background-color: var(--card-bg);
  color: var(--foreground);
}

/* 修复Wiki页面内容区域在暗色模式下的背景色问题 */
.dark main,
html[data-theme='dark'] main {
  background-color: var(--background);
}

.dark .container,
html[data-theme='dark'] .container {
  background-color: transparent;
}

/* 确保其他可能的背景色元素在暗色模式下正确显示 */
.dark .overflow-y-auto,
html[data-theme='dark'] .overflow-y-auto {
  background-color: inherit;
}

/* 确保嵌套内容正确继承背景色 */
.dark #wiki-content,
html[data-theme='dark'] #wiki-content {
  background-color: var(--card-bg);
}

/* 修复文本输入区域的颜色 */
.dark .text-gray-800,
html[data-theme='dark'] .text-gray-800 {
  color: var(--foreground);
}

.dark .dark\:text-gray-200,
html[data-theme='dark'] .dark\:text-gray-200 {
  color: var(--foreground);
}

/* 确保 Ask 组件中的响应区域在暗色模式下有正确的背景 */
.dark .bg-transparent,
html[data-theme='dark'] .bg-transparent {
  background-color: transparent;
}

/* --- 下拉菜单夜间模式修复 --- */
.dropdown-menu, .dropdown-content, .select-menu, .select-dropdown, .custom-dropdown {
  background-color: var(--card-bg) !important;
  color: var(--foreground) !important;
  box-shadow: 0 4px 16px var(--shadow-color);
  z-index: 30;
  border: 1px solid var(--border-color);
}
.dark .dropdown-menu, .dark .dropdown-content, .dark .select-menu, .dark .select-dropdown, .dark .custom-dropdown,
html[data-theme='dark'] .dropdown-menu, html[data-theme='dark'] .dropdown-content, html[data-theme='dark'] .select-menu, html[data-theme='dark'] .select-dropdown, html[data-theme='dark'] .custom-dropdown {
  background-color: var(--card-bg) !important;
  color: var(--foreground) !important;
  border: 1px solid var(--border-color);
}

/* --- 强化wiki内容区夜间模式背景色 --- */
#wiki-content, .prose, .markdown-content {
  background-color: var(--card-bg) !important;
}
.dark #wiki-content, .dark .prose, .dark .markdown-content,
html[data-theme='dark'] #wiki-content, html[data-theme='dark'] .prose, html[data-theme='dark'] .markdown-content {
  background-color: var(--card-bg) !important;
  color: var(--foreground) !important;
}

/* --- 移除/覆盖硬编码白色背景 --- */
.bg-white, .bg-gray-50 {
  background-color: var(--card-bg) !important;
}
.dark .bg-white, .dark .bg-gray-50,
html[data-theme='dark'] .bg-white, html[data-theme='dark'] .bg-gray-50 {
  background-color: var(--card-bg) !important;
}

/* 以下为已有规则，无需重复添加 */
.dark .bg-gray-100, 
html[data-theme='dark'] .bg-gray-100 {
  background-color: var(--accent-secondary);
}

.dark .bg-gray-200, 
html[data-theme='dark'] .bg-gray-200 {
  background-color: var(--border-color);
}

.dark .bg-gray-700, 
html[data-theme='dark'] .bg-gray-700 {
  background-color: var(--accent-secondary);
}

.dark .bg-gray-800, 
html[data-theme='dark'] .bg-gray-800 {
  background-color: var(--card-bg);
}

.dark .text-gray-400, 
html[data-theme='dark'] .text-gray-400 {
  color: var(--muted);
}

.dark .text-gray-600, 
html[data-theme='dark'] .text-gray-600 {
  color: var(--muted);
}

.dark .text-blue-400, 
html[data-theme='dark'] .text-blue-400 {
  color: var(--accent-primary);
}

.dark .border-gray-200, 
html[data-theme='dark'] .border-gray-200 {
  border-color: var(--border-color);
}

.dark .border-gray-700, 
html[data-theme='dark'] .border-gray-700 {
  border-color: var(--border-color);
}

.dark .hover\:bg-gray-700\/50:hover, 
html[data-theme='dark'] .hover\:bg-gray-700\/50:hover {
  background-color: var(--accent-primary)/30;
}

.dark .hover\:text-white:hover, 
html[data-theme='dark'] .hover\:text-white:hover {
  color: var(--foreground);
}

/* 隐藏 Edge/IE/Chromium 的密码显示/清除按钮 */
input[type='password']::-ms-reveal {
  display: none;
}
input[type='password']::-ms-clear {
  display: none;
}
input[type='password']::-webkit-credentials-auto-fill-button {
  display: none !important;
}

/* Custom styles for tool panel animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Smooth scale animation */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-scaleIn {
  animation: scaleIn 0.2s ease-out forwards;
}

/* Pulse animation for status indicators */
@keyframes pulse-subtle {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.animate-pulse-subtle {
  animation: pulse-subtle 2s ease-in-out infinite;
}

/* Smooth backdrop blur transition */
.backdrop-blur-transition {
  transition: backdrop-filter 0.3s ease, background-color 0.3s ease;
}

/* Enhanced scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 0px;
  display: none;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #d1d5db, #9ca3af);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #9ca3af, #6b7280);
}

/* Dark mode scrollbar */
.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #4b5563, #374151);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #374151, #1f2937);
}

/* Enhanced glass morphism effect */
.glass-morphism {
  background: rgba(255, 255, 255, 0.75);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-morphism {
  background: rgba(17, 24, 39, 0.75);
  border: 1px solid rgba(107, 114, 128, 0.2);
}

/* Typing indicator animation */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.typing-indicator span {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: currentColor;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.6);
    opacity: 0.3;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Enhanced pulse animation with staggered delays */
.animate-pulse-stagger-1 {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  animation-delay: 0s;
}

.animate-pulse-stagger-2 {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  animation-delay: 0.5s;
}

.animate-pulse-stagger-3 {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  animation-delay: 1s;
}

/* Elegant focus states */
.focus-ring {
  transition: all 0.2s ease;
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.5);
}

/* Subtle hover effects */
.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.dark .hover-lift:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Mermaid diagram styles */
.mermaid {
  text-align: center;
}

/* Prose styles for markdown content */
.prose {
  max-width: none;
}

/* Glass effect for ChatInput component */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .glass-effect {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.css-1u9des2-indicatorSeparator {
  display: none !important; 
}

.dark-react-select__control {
  background-color: transparent !important;
  border-width: 1px !important;
}

.dark-react-select__input {
  color: #ffffff !important;
}

.dark-react-select__single-value {
  color: #ffffff !important;
}

.dark-react-select__control--is-focused {
  border-width: 1px !important;
  border-color: var(--accent-primary) !important;
  box-shadow: none !important;
}

.dark-react-select__menu {
  background-color: var(--background) !important;
}

.dark-react-select__option--is-focused {
  color: var(--background) !important;
}

.react-select__control {
  border-width: 1px !important;
}

.react-select__control--is-focused {
  border-width: 1px !important;
  border-color: var(--accent-primary) !important;
  box-shadow: none !important;
}

/* 分页组件样式 */
.pagination-link {
  display: inline-block;
  padding: 8px 12px;
  text-decoration: none;
  color: var(--foreground);
  background-color: var(--background);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  transition: all 0.2s ease;
  cursor: pointer;
  font-size: 14px;
  line-height: 1;
  min-width: 36px;
  text-align: center;
}

.pagination-link:hover {
  /* background-color: var(--background); */
  /* border-color: var(--accent-primary); */
  color: var(--accent-primary);
}

.pagination-link-active {
  display: inline-block;
  padding: 8px 12px;
  text-decoration: none;
  background-color: var(--head-text-color);
  /* border: 1px solid var(--head-text-color); */
  color: white;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1;
  min-width: 36px;
  text-align: center;
}

.pagination-link-disabled {
  display: inline-block;
  padding: 8px 12px;
  text-decoration: none;
  color: var(--muted);
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  cursor: not-allowed;
  font-size: 14px;
  line-height: 1;
  min-width: 36px;
  text-align: center;
  opacity: 0.6;
}

.pagination-link-disabled:hover {
  background-color: var(--card-bg);
  border-color: var(--border-color);
  color: var(--muted);
}

.fixed-scrollbar-width {
  scrollbar-width: 10px;
}

.fixed-scrollbar-width::-webkit-scrollbar {
  width: 10px;
}