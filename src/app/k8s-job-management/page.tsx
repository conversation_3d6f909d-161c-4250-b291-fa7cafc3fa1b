'use client'

import React, { useState } from 'react'
import { JobManagementComponent } from '@/components/K8SJobManagement'
import { Header } from '@/components'
import SettingsModal from '@/components/SettingsModal'
import { JobsProgressModal } from '@/components/JobsProgressModal'
import { usePageTitle } from '@/utils/pageTitle'

export default function JobManagementPage() {
  // 处理通过 URL 参数传递的页面标题，如果没有则使用默认标题
  usePageTitle('DeepWiki 沙箱管理')
  
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false)
  const [isJobsProgressModalOpen, setIsJobsProgressModalOpen] = useState(false)
  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-b from-white via-slate-50 to-slate-100">
      <Header onOpenSettings={() => setIsSettingsModalOpen(true)} onOpenJobs={() => setIsJobsProgressModalOpen(true)} />
      <SettingsModal isOpen={isSettingsModalOpen} onClose={() => setIsSettingsModalOpen(false)} />
      <JobsProgressModal isOpen={isJobsProgressModalOpen} onClose={() => setIsJobsProgressModalOpen(false)} />
      <div className="pointer-events-none absolute inset-0 [background:repeating-linear-gradient(0deg,rgba(0,0,0,0.02)_0,rgba(0,0,0,0.02)_1px,transparent_1px,transparent_2px),repeating-linear-gradient(90deg,rgba(0,0,0,0.02)_0,rgba(0,0,0,0.02)_1px,transparent_1px,transparent_2px)] [mask-image:radial-gradient(ellipse_at_center,black_60%,transparent_100%)]"></div>
      <div className="pointer-events-none absolute -top-1/3 -left-1/3 w-[130%] h-[130%] bg-[radial-gradient(1000px_600px_at_0%_0%,rgba(255,255,255,0.7),transparent_60%),radial-gradient(800px_500px_at_100%_0%,rgba(146,172,255,0.25),transparent_60%)]"></div>
      <div className="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative">
        <JobManagementComponent />
      </div>
    </div>
  )
} 