/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import React, {
  useCallback,
  useState,
  useEffect,
  useRef,
  useMemo,
} from "react";
import { useParams } from "next/navigation";
import {
  FaBook,
  FaBookOpen,
  FaBitbucket,
  FaDownload,
  FaFileExport,
  FaSync,
  FaUpload,
} from "react-icons/fa";
import Markdown from "@/components/Markdown";
import WikiTreeView from "@/components/WikiTreeView";
import { useLanguage } from "@/contexts/LanguageContext";
import { RepoInfo } from "@/types/repoinfo";
import { ModelSettings } from "@/types/modelConfig";
import { Header } from "@/components";
import { JobsProgressModal } from "@/components/JobsProgressModal";
import RefreshModal from "@/components/RefreshModal";
import FileManager from "@/components/FileManager";
import { useSettings } from "@/contexts/SettingsContext";
import SettingsModal from "@/components/SettingsModal";
import { v4 as uuidv4 } from "uuid";
import ChatInputAdapter from "@/components/ChatInputAdapter";
import { authFetch } from "@/utils/authFetch";
import { useToast } from "@/contexts/ToastContext";
import { useAuth } from "@/contexts/AuthContext";
import ChatHistory from "@/components/ChatHistory";
import { ComponentPrivilegeEnum } from "@/types/privilege";
import { home } from "@/utils/auth";

interface WikiSection {
  id: string;
  title: string;
  pages: string[];
  subsections?: string[];
}

interface WikiPage {
  id: string;
  title: string;
  content: string;
  filePaths: string[];
  importance: "high" | "medium" | "low";
  relatedPages: string[];
  parentId?: string;
  isSection?: boolean;
  children?: string[];
}

interface WikiStructure {
  id: string;
  title: string;
  description: string;
  pages: WikiPage[];
  sections: WikiSection[];
  rootSections: string[];
}

interface Model {
  id: string;
  name: string;
}

interface Provider {
  id: string;
  name: string;
  models: Model[];
  supportsCustomModel?: boolean;
}

// Add CSS styles for wiki with Japanese aesthetic
const wikiStyles = `
  .prose code {
    @apply bg-[var(--background)]/70 px-1.5 py-0.5 rounded font-mono text-xs border border-[var(--border-color)];
  }

  .prose pre {
    @apply bg-[var(--background)]/80 text-[var(--foreground)] rounded-md p-4 overflow-x-auto border border-[var(--border-color)] shadow-sm;
  }

  .prose h1, .prose h2, .prose h3, .prose h4 {
    @apply font-serif text-[var(--foreground)];
  }

  .prose p {
    @apply text-[var(--foreground)] leading-relaxed;
  }

  .prose a {
    @apply text-[var(--accent-primary)] hover:text-[var(--highlight)] transition-colors no-underline border-b border-[var(--border-color)] hover:border-[var(--accent-primary)];
  }

  .prose blockquote {
    @apply border-l-4 border-[var(--accent-primary)]/30 bg-[var(--background)]/30 pl-4 py-1 italic;
  }

  .prose ul, .prose ol {
    @apply text-[var(--foreground)];
  }

  .prose table {
    @apply border-collapse border border-[var(--border-color)];
  }

  .prose th {
    @apply bg-[var(--background)]/70 text-[var(--foreground)] p-2 border border-[var(--border-color)];
  }

  .prose td {
    @apply p-2 border border-[var(--border-color)];
  }

  /* 文件引用链接的特殊样式 */
  .prose a[href*="filePath"] {
    @apply no-underline border-none;
  }

  /* 代码块行号样式优化 */
  .prose pre .react-syntax-highlighter-line-number {
    @apply text-xs text-gray-500 select-none pr-4 text-right;
    min-width: 3em;
  }

  /* 代码块容器样式 */
  .prose .react-syntax-highlighter {
    @apply rounded-b-md shadow-sm;
  }

  /* 行内代码优化 */
  .prose code:not([class*="language-"]) {
    @apply bg-[var(--accent-secondary)]/60 border border-[var(--border-color)]/30 font-medium;
  }

  /* 左侧列滚动条悬浮显示 */
  .left-column-scrollbar {
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* IE and Edge */
  }
  
  .left-column-scrollbar::-webkit-scrollbar {
    display: none !important; /* Chrome, Safari and Opera */
  }
  
  .left-column-scrollbar:hover {
    scrollbar-width: thin !important; /* Firefox */
    -ms-overflow-style: auto !important; /* IE and Edge */
  }
  
  .left-column-scrollbar:hover::-webkit-scrollbar {
    display: block !important; /* Chrome, Safari and Opera */
  }
`;

export default function RepoWikiPage() {
  const params = useParams();

  const id = params.id;

  // Import language context for translations
  const { messages } = useLanguage();
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);

  // State variables
  const [isLoading, setIsLoading] = useState(true);
  const [loadingMessage, setLoadingMessage] = useState<string | undefined>(
    messages.loading?.initializing || "Initializing wiki generation..."
  );
  const [error, setError] = useState<string | null>(null);
  const [wikiStructure, setWikiStructure] = useState<
    WikiStructure | undefined
  >();
  const [currentPage, setCurrentPage] = useState<string | undefined>();
  const [wikiPages, setWikiPages] = useState<Record<string, string>>({});
  const [pagesInProgress, setPagesInProgress] = useState(new Set<string>());
  const [isExporting, setIsExporting] = useState(false);
  const [exportError, setExportError] = useState<string | null>(null);
  const [effectiveRepoInfo, setEffectiveRepoInfo] = useState<RepoInfo>();
  const [isExportDropdownOpen, setIsExportDropdownOpen] = useState(false); // 控制导出下拉菜单的显示
  const exportDropdownRef = useRef<HTMLDivElement>(null); // 导出下拉菜单的引用
  const [currentWikiId, setCurrentWikiId] = useState<string | null>(null); // 保存当前wiki的ID
  const [updatedTime, setUpdatedTime] = useState<string>('');
  const { userInfo } = useAuth(); // keep single declaration
  const [hasWikiAccessPerm, setWikiAccessPerm] = useState(false);
  const { addToast } = useToast();
  const refreshableSections = useMemo(() => {
    if (!wikiStructure?.sections || wikiStructure.sections.length === 0) {
      return [] as { id: string; title: string; pages: string[] }[];
    }
    return wikiStructure.sections
      .filter(
        (section) => Array.isArray(section.pages) && section.pages.length > 0
      )
      .map((section) => ({
        id: section.id,
        title: section.title || section.id,
        pages: section.pages,
      }));
  }, [wikiStructure]);

  // 同步索引相关状态
  const [isSyncingIndex, setIsSyncingIndex] = useState(false);
  const [syncProgress, setSyncProgress] = useState<{
    progress: number;
    processed_files: number;
    total_files: number;
    message?: string;
  } | null>(null);

  // ownerId 状态
  const [ownerId, setOwnerId] = useState<number | undefined>(undefined);

  // 权限
  const { hasRefreshWikiPermission, hasExportWikiPermission } = useAuth();
  const canRefreshWiki =
    ownerId !== undefined
      ? hasRefreshWikiPermission?.(ownerId, ComponentPrivilegeEnum.REFRESH_WIKI)
      : false;
  const canExportWiki = hasExportWikiPermission?.(
    ComponentPrivilegeEnum.EXPORT_WIKI
  );
  const showWikiActions = canRefreshWiki || canExportWiki;

  // 点击外部区域关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        exportDropdownRef.current &&
        !exportDropdownRef.current.contains(event.target as Node)
      ) {
        setIsExportDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Listen for auth-error-403 events
  useEffect(() => {
    const handleAuthError = () => {
      addToast({
        type: "error",
        title: "没有权限执行该操作",
        message: "",
      });
    };

    window.addEventListener("auth-error-403", handleAuthError);

    return () => {
      window.removeEventListener("auth-error-403", handleAuthError);
    };
  }, [addToast]);

  // Model selection state variables - 统一状态管理
  const [selectedProvider, setSelectedProvider] = useState<string>("gemini-cli");
  const [selectedModel, setSelectedModel] = useState<string>("");

  // Wiki type state - default to comprehensive view
  const [isComprehensiveView, setIsComprehensiveView] = useState(true);
  // Create a flag to track if data was loaded from cache to prevent immediate re-save
  const cacheLoadedSuccessfully = useRef(false);

  // Create a flag to ensure the effect only runs once
  const effectRan = React.useRef(false);

  // State for Ask modal
  const [isJobsProgressModalOpen, setIsJobsProgressModalOpen] = useState(false); // 添加任务进度模态框状态
  const [isRefreshModalOpen, setIsRefreshModalOpen] = useState(false); // 添加刷新模态框状态
  const [showFileManager, setShowFileManager] = useState(false);

  // Get settings from context - moved up to be available for all hooks
  const { settings } = useSettings();

  // 获取认证上下文
  const { hasSandboxPermission } = useAuth();

  // Memoize repo info to avoid triggering updates in callbacks

  // Add useEffect to handle scroll reset
  useEffect(() => {
    // Scroll to top when currentPageId changes
    const wikiContent = document.getElementById("wiki-content");
    if (wikiContent) {
      wikiContent.scrollTo({ top: 0, behavior: "smooth" });
    }
  }, [currentPage]);

  // 创建沙盒环境的函数
  const createSandbox = useCallback(async () => {
    if (!hasSandboxPermission() || !userInfo || !hasWikiAccessPerm) {
      console.log("用户没有沙盒权限或未登录");
      return;
    }

    // 新增：检查大模型API Key是否已通过校验
    if (!settings?.isApiKeyValid) {
      return;
    }

    try {
      // 获取仓库URL
      const gitUrl = effectiveRepoInfo?.repoUrl;
      if (!gitUrl) {
        console.error("无法获取仓库URL");
        return;
      }

      console.log("开始创建沙盒环境:", {
        gitUrl,
        branch: effectiveRepoInfo.branch,
        userCode: userInfo.user_code,
      });

      const response = await authFetch("/api/k8s/sandbox/me/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          wiki_id: currentWikiId
        }),
      });

      if (response && response.ok) {
        const data = await response.json();
        if (data.success) {
          console.log("个人沙箱分配成功:", data.data);
          // addToast({
          //   type: 'success',
          //   title: '个人沙箱创建中',
          //   message: '您的开发环境正在创建中'
          // });
        } else {
          console.error("沙箱创建失败:", data.error);
          // 如果是容器数量限制的错误，显示特殊提示
          if (data.error && data.error.includes("容器数量已达上限")) {
            // addToast({
            //   type: 'warning',
            //   title: '个人沙箱创建中',
            //   message: '系统正在清理过期环境，请稍后重试'
            // });
          } else {
            // addToast({
            //   type: 'error',
            //   title: '沙盒创建失败',
            //   message: data.error || '未知错误'
            // });
          }
        }
      } else {
        console.error("沙盒创建请求失败:", response?.status);
        addToast({
          type: "error",
          title: messages.repoPage.snadBoxFailed,
          message: messages.common.requestFail,
        });
      }
    } catch (error) {
      console.error("创建沙盒时发生错误:", error);
      addToast({
        type: "error",
        title: messages.repoPage.snadBoxFailed,
        message:
          error instanceof Error ? error.message : messages.common.requestFail,
      });
    }
  }, [
    hasSandboxPermission,
    userInfo,
    effectiveRepoInfo,
    addToast,
    hasWikiAccessPerm,
    settings?.isApiKeyValid,
    settings?.apiKey,
    messages.common?.error,
    messages.common?.invalidApiKey,
  ]);

  // 在组件挂载时检查并创建沙盒
  useEffect(() => {
    if (hasSandboxPermission() && userInfo && effectiveRepoInfo) {
      // 延迟创建沙盒，避免影响页面加载
      const timer = setTimeout(() => {
        createSandbox();
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [
    hasSandboxPermission,
    userInfo,
    effectiveRepoInfo,
    createSandbox,
    hasWikiAccessPerm,
  ]);

  // Function to export wiki content
  const exportWiki = useCallback(
    async (format: "markdown" | "json") => {
      if (!wikiStructure || Object.keys(wikiPages).length === 0) {
        setExportError("No wiki content to export");
        return;
      }

      try {
        setIsExporting(true);
        setExportError(null);
        setLoadingMessage(`Exporting wiki as ${format} ...`);

        // Prepare the pages for export
        const pagesToExport = wikiStructure.pages.map((page) => {
          // Use the generated content if available, otherwise use an empty string
          const content = wikiPages[page.title] || "Content not generated";
          return {
            ...page,
            content,
          };
        });

        // Get repository URL
        const repoUrl = effectiveRepoInfo?.repoUrl;

        // Make API call to export wiki
        const response = await authFetch(`/api/wiki/export`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            wiki_id: currentWikiId,
            branch: effectiveRepoInfo?.branch,
            repo_url: repoUrl,
            type: effectiveRepoInfo?.type,
            pages: pagesToExport,
            sections: wikiStructure.sections,
            rootSections: wikiStructure.rootSections,
            format,
          }),
        });

        if (response && !response.ok) {
          const errorText = await response
            .text()
            .catch(() => "No error details available");
          throw new Error(
            `Error exporting wiki: ${response.status} - ${errorText}`
          );
        }

        // Get the filename from the Content-Disposition header if available
        const contentDisposition = response?.headers.get("Content-Disposition");
        let filename = `${effectiveRepoInfo?.repo}_wiki.${
          format === "markdown" ? "md" : "json"
        }`;

        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename=(.+)/);
          if (filenameMatch && filenameMatch[1]) {
            filename = filenameMatch[1].replace(/"/g, "");
          }
        }

        // Convert the response to a blob and download it
        const blob = await response?.blob();
        if (!blob) {
          throw new Error("Failed to create blob");
        }
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } catch (err) {
        console.error("Error exporting wiki:", err);
        const errorMessage =
          err instanceof Error ? err.message : "Unknown error during export";
        setExportError(errorMessage);
      } finally {
        setIsExporting(false);
        setLoadingMessage(undefined);
      }
    },
    [wikiStructure, wikiPages, effectiveRepoInfo, currentWikiId]
  );

  // Handle refresh confirmation from modal
  const handleRefreshConfirm = useCallback(
    async (
      modelSettings: ModelSettings,
      options: {
        forceRefresh: boolean;
        refreshPages?: string[];
        rebuildStructure: boolean;
        customInstructions?: string;
      }
    ) => {
      const {
        forceRefresh,
        refreshPages,
        rebuildStructure,
        customInstructions,
      } = options;
      if (!currentWikiId) {
        addToast({
          type: "error",
          title: messages.repoPage.wikiNotFound,
          message: messages.repoPage.cannotRefreshWiki,
        });
        return;
      }

      try {
        // Prepare refresh request using wiki_id only
        const cleanedWhaleDevCloudToken = settings.whaleDevCloudToken.replace(
          /●/g,
          ""
        );
        const refreshRequest = {
          token: cleanedWhaleDevCloudToken,
          wiki_id: currentWikiId,
          model_settings: modelSettings,
          comprehensive: isComprehensiveView,
          force_refresh: forceRefresh,
          refresh_pages: refreshPages,
          rebuild_structure: rebuildStructure,
          custom_instructions: customInstructions,
        };

        // Call refresh API
        const response = await authFetch("/api/wiki/refresh", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(refreshRequest),
        });

        if (response && response.ok) {
          const result = await response.json();

          if (result.status_code === 403) {
            addToast({
              type: "error",
              title: messages.repoPage.tokenIsNotValid,
              message: result.detail,
            });
          }

          if (result.refresh) {
            // 根据模式确定提示语
            let message = "Wiki 刷新任务已在后台运行";
            if (refreshPages && refreshPages.length > 0) {
              message = `已在后台刷新所选的 ${refreshPages.length} 个页面`;
            } else if (forceRefresh) {
              message = "已在后台强制刷新所有页面";
            } else if (rebuildStructure) {
              message = "已在后台重建章节结构并刷新内容";
            }

            addToast({
              type: "success",
              title: messages.repoPage.refreshStarted,
              message,
            });

            // Show the jobs progress modal immediately
            setIsJobsProgressModalOpen(true);
          } else if (result.exists) {
            addToast({
              type: "info",
              title: messages.repoPage.taskAlreadyRunning,
              message: result.message,
            });

            // Still show the progress modal to track existing task
            setIsJobsProgressModalOpen(true);
          }
        } else {
          const errorData = await response?.json().catch(() => ({}));
          throw new Error(errorData.detail || "Failed to start refresh");
        }
      } catch (error) {
        console.error("Error refreshing wiki:", error);
        addToast({
          type: "error",
          title: messages.repoPage.refreshFailed,
          message:
            error instanceof Error
              ? error.message
              : messages.common.requestFail,
        });
      }
    },
    [currentWikiId, isComprehensiveView, addToast, settings.whaleDevCloudToken]
  );

  // Handle refresh wiki - open modal instead of direct refresh
  const handleRefreshWiki = useCallback(() => {
    if (!wikiStructure || !currentWikiId) {
      addToast({
        type: "error",
        title: messages.repoPage.wikiNotFound,
        message: messages.repoPage.cannotRefreshWikiWithNoExistingWiki,
      });
      return;
    }

    setIsRefreshModalOpen(true);
  }, [wikiStructure, currentWikiId, addToast]);

  const handleSyncIndex = useCallback(async () => {
    if (!currentWikiId) {
      addToast({
        type: "error",
        title: "Wiki未找到",
        message: "无法同步索引，未找到有效的Wiki",
      });
      return;
    }

    setIsSyncingIndex(true);
    setSyncProgress({
      progress: 0,
      processed_files: 0,
      total_files: 0,
      message: "正在准备同步...",
    });

    try {
      // 调用同步索引API
      const response = await fetch(`/api/wiki/${currentWikiId}/sync-index`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          strategy: "main_topic", // 默认使用主topic策略
        }),
      });

      if (!response.ok) {
        throw new Error(`同步索引失败: ${response.status}`);
      }

      const result = await response.json();
      const jobId = result.job_id;

      // 轮询任务状态
      const pollInterval = setInterval(async () => {
        try {
          const statusResponse = await fetch(`/api/wiki/jobs/${jobId}`);
          if (statusResponse.ok) {
            const job = await statusResponse.json();

            setSyncProgress({
              progress: job.progress || 0,
              processed_files: job.processed_files || 0,
              total_files: job.total_files || 0,
              message: job.stage_message || job.message,
            });

            if (job.status === "completed") {
              clearInterval(pollInterval);
              setIsSyncingIndex(false);
              setSyncProgress(null);
              addToast({
                type: "success",
                title: "同步完成",
                message: "索引同步已成功完成",
              });
            } else if (job.status === "failed") {
              clearInterval(pollInterval);
              setIsSyncingIndex(false);
              setSyncProgress(null);
              addToast({
                type: "error",
                title: "同步失败",
                message: job.error_message || "索引同步失败",
              });
            }
          }
        } catch (error) {
          console.error("轮询任务状态失败:", error);
        }
      }, 2000);

      // 5分钟后自动停止轮询
      setTimeout(() => {
        clearInterval(pollInterval);
        if (isSyncingIndex) {
          setIsSyncingIndex(false);
          setSyncProgress(null);
        }
      }, 300000);
    } catch (error) {
      console.error("同步索引失败:", error);
      setIsSyncingIndex(false);
      setSyncProgress(null);
      addToast({
        type: "error",
        title: "同步失败",
        message:
          error instanceof Error ? error.message : "同步索引时发生未知错误",
      });
    }
  }, [currentWikiId, addToast, isSyncingIndex]);

  // Start wiki generation when component mounts
  useEffect(() => {
    if (effectRan.current === false) {
      effectRan.current = true; // Set to true immediately to prevent re-entry due to StrictMode

      const loadData = async () => {
        // Try loading from server-side cache first
        setLoadingMessage(
          messages.loading?.fetchingCache || "Checking for cached wiki..."
        );
        try {
          const response = await authFetch(`/api/wiki_detail?id=${id}`);

          if (response && response.ok) {
            const data = await response.json();
            if (data?.priv_status) {
              addToast({
                type: "error",
                title: messages.repoPage.noPermissionToViewWiki,
                message: "",
                duration: 2000,
              });
              setWikiAccessPerm(false);
              setTimeout(() => home(), 2000); // 延迟3秒跳转
            }

            if (data) {
              setWikiAccessPerm(true);
              setIsComprehensiveView(data.wiki_info.comprehensive);
              // setSelectedProvider(data.wiki_info.provider);
              // setSelectedModel(data.wiki_info.model);
              setEffectiveRepoInfo({
                owner: data.wiki_info.repo_owner,
                repo: data.wiki_info.repo_name,
                type: data.wiki_info.repo_type,
                repoUrl: data.wiki_info.repo_url,
                branch: data.wiki_info.branch,
                wiki_id: data.wiki_info.wiki_id,
                token: null,
                localPath: null,
                subRepos: [],
              });
              setOwnerId(data.wiki_info.owner_id);
              setCurrentWikiId(data.wiki_info.wiki_id);
              setUpdatedTime(data.wiki_info.updated_time);
              setWikiStructure(data.wiki_structure);
              setWikiPages(data.wiki_pages);
              setCurrentPage(
                data.wiki_structure.pages.length > 0
                  ? data.wiki_structure.pages[0].title
                  : undefined
              );
              setIsLoading(false);
              setLoadingMessage(undefined);
              cacheLoadedSuccessfully.current = true;

              return;
            } else {
              setIsLoading(false);
              setError(
                messages.repoPage?.wikiNotFound ||
                  "No wiki cache found for this repository."
              );
            }
          } else {
            setWikiAccessPerm(false);
            // Log error and show error message
            console.error(
              "Error fetching wiki cache from server:",
              response?.status,
              await response?.text()
            );
            setIsLoading(false);
            setError(
              messages.repoPage?.wikiNotFound ||
                "No wiki cache found for this repository."
            );
          }
        } catch (error) {
          console.error("Error loading from server cache:", error);
          setIsLoading(false);
          setError(
            error instanceof Error ? error.message : "An unknown error occurred"
          );
        }
      };

      loadData();
    } else {
      console.log("Skipping duplicate repository fetch/cache check");
    }
  }, [
    effectiveRepoInfo,
    messages.repoPage?.wikiNotFound,
    messages.loading?.fetchingCache,
    isComprehensiveView,
    addToast,
  ]);

  const handlePageSelect = (pageId: string) => {
    if (currentPage != pageId) {
      setCurrentPage(pageId);
    }
  };

  // 新增：模型相关状态
  const [providers, setProviders] = useState<Provider[]>([]);
  const [availableModels, setAvailableModels] = useState<Model[]>([]);
  const [modelType, setModelType] = useState<"whalecloud" | "gemini-cli">(
    "gemini-cli"
  );
  const [deepResearch, setDeepResearch] = useState(false);
  const [isModelsLoaded, setIsModelsLoaded] = useState(false);

  // 新增：获取模型数据的函数
  const fetchModels = useCallback(async () => {
    if (!hasWikiAccessPerm) {
      return;
    }
    try {
      const response = await authFetch("/api/config/models");
      if (response && response.ok) {
        const data = await response.json();
        setProviders(data.providers);

        // 使用配置文件中的默认provider
        const defaultProvider = data.defaultProvider || "gemini-cli";
        console.log(defaultProvider);
        const defaultProviderConfig = data.providers.find(
          (p: Provider) => p.id === defaultProvider
        );
        if (defaultProviderConfig) {
          // setSelectedProvider(defaultProvider);
          setAvailableModels(defaultProviderConfig.models);
          if (defaultProviderConfig.models.length > 0) {
            setSelectedModel(defaultProviderConfig.models[0].id);
          }
        }
      }
    } catch (err) {
      console.error("Failed to fetch models:", err);
    } finally {
      setIsModelsLoaded(true);
    }
  }, [hasWikiAccessPerm]);

  // 新增：在组件挂载时获取模型数据
  useEffect(() => {
    fetchModels();
  }, [fetchModels, hasWikiAccessPerm]);

  const handleInitialQuestionSubmit = (
    question: string,
    modelType: "whalecloud" | "gemini-cli",
    selectedProvider?: string,
    selectedModel?: string,
    fileReferences?: unknown[],
    commandParams?: { operation: string | null; param: string | null },
    images?: Array<string>
  ) => {
    // 新增：检查大模型API Key是否已通过校验
    if (!settings?.isApiKeyValid) {
      addToast({
        type: "error",
        title: messages.common?.error || "Error",
        message:
          messages.common?.invalidApiKey ||
          "Large Model Token invalid. Please configure it in Settings.",
      });
      return;
    }

    const sessionId = uuidv4();

    console.log("=== handleInitialQuestionSubmit 开始 ===", {
      modelType,
      selectedProvider,
      selectedModel,
      sessionId,
      fileReferences,
      fileReferencesLength: fileReferences?.length || 0,
      commandParams,
    });

    // 存储模型和提供商信息到localStorage
    const modelKey = `model_${sessionId}`;
    const providerKey = `provider_${sessionId}`;

    console.log("=== 存储到localStorage ===", {
      modelKey,
      providerKey,
      selectedModel,
    });

    // 存储仓库和配置参数到localStorage
    const paramsKey = `params_${sessionId}`;
    const params = {
      repoUrl: effectiveRepoInfo?.repoUrl,
      question: question,
      owner: effectiveRepoInfo?.owner,
      repo: effectiveRepoInfo?.repo,
      type: effectiveRepoInfo?.type,
      branch: effectiveRepoInfo?.branch,
      provider: modelType,
      model: selectedModel,
      deepResearch: deepResearch,
      token: effectiveRepoInfo?.token || "",
      wikiId: currentWikiId, // 优先使用wiki_id参数用于API调用
      fileReferences: fileReferences || [], // 添加文件引用参数
      commandParams: commandParams || {},
      wikiInfoId: id,
      images: images,
    };

    localStorage.setItem(paramsKey, JSON.stringify(params));

    console.log("最终存储到localStorage的完整数据:", {
      question: question,
      model: selectedModel,
      provider: modelType,
      params: params,
      sessionId: sessionId,
      fileReferences: fileReferences || [],
    });

    // 验证localStorage中的数据
    const storedParams = localStorage.getItem(paramsKey);

    console.log("=== 验证localStorage存储结果 ===", {
      storedParams: storedParams ? JSON.parse(storedParams) : null,
    });

    // 只保留sessionId在URL中
    const url = `/search/${sessionId}`;
    console.log("即将打开URL:", url);
    window.open(url, "_blank");

    console.log("=== handleInitialQuestionSubmit 结束 ===");
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50 dark:bg-gray-900">
      <style>{wikiStyles}</style>
      <Header
        onOpenSettings={() => setIsSettingsModalOpen(true)}
        onOpenJobs={() => setIsJobsProgressModalOpen(true)}
      />

      <main className="flex-1 w-full mx-auto py-6 pb-0 overflow-y-auto">
        {isLoading ? (
          <div className="flex flex-col items-center justify-center p-8 bg-[var(--card-bg)] rounded-lg shadow-custom card-elegant max-w-screen-xl mx-auto">
            <div className="relative mb-6">
              <div className="absolute -inset-4 bg-[var(--accent-primary)]/10 rounded-full blur-md animate-pulse"></div>
              <div className="relative flex items-center justify-center">
                <div className="w-3 h-3 bg-[var(--accent-primary)]/70 rounded-full animate-pulse"></div>
                <div className="w-3 h-3 bg-[var(--accent-primary)]/70 rounded-full animate-pulse delay-75 mx-2"></div>
                <div className="w-3 h-3 bg-[var(--accent-primary)]/70 rounded-full animate-pulse delay-150"></div>
              </div>
            </div>
            <p className="text-[var(--foreground)] text-center mb-3 font-serif">
              {loadingMessage || messages.common?.loading || "Loading..."}
              {isExporting &&
                (messages.loading?.preparingDownload ||
                  " Please wait while we prepare your download...")}
            </p>

            {/* Progress bar for page generation */}
            {wikiStructure && (
              <div className="w-full max-w-md mt-3">
                <div className="bg-[var(--background)]/50 rounded-full h-2 mb-3 overflow-hidden border border-[var(--border-color)]">
                  <div
                    className="bg-[var(--accent-primary)] h-2 rounded-full transition-all duration-300 ease-in-out"
                    style={{
                      width: `${Math.max(
                        5,
                        (100 *
                          (wikiStructure.pages.length - pagesInProgress.size)) /
                          wikiStructure.pages.length
                      )}%`,
                    }}
                  />
                </div>
                <p className="text-xs text-[var(--muted)] text-center">
                  {messages.repoPage?.pagesCompleted
                    ? messages.repoPage.pagesCompleted
                        .replace(
                          "{completed}",
                          (
                            wikiStructure.pages.length - pagesInProgress.size
                          ).toString()
                        )
                        .replace(
                          "{total}",
                          wikiStructure.pages.length.toString()
                        )
                    : `${
                        wikiStructure.pages.length - pagesInProgress.size
                      } of ${wikiStructure.pages.length} pages completed`}
                </p>

                {/* Show list of in-progress pages */}
                {pagesInProgress.size > 0 && (
                  <div className="mt-4 text-xs">
                    <p className="text-[var(--muted)] mb-2">
                      {messages.repoPage?.currentlyProcessing ||
                        "Currently processing:"}
                    </p>
                    <ul className="text-[var(--foreground)] space-y-1">
                      {Array.from(pagesInProgress)
                        .slice(0, 3)
                        .map((pageId) => {
                          const page = wikiStructure.pages.find(
                            (p) => p.id === pageId
                          );
                          return page ? (
                            <li
                              key={pageId}
                              className="truncate border-l-2 border-[var(--accent-primary)]/30 pl-2"
                            >
                              {page.title}
                            </li>
                          ) : null;
                        })}
                      {pagesInProgress.size > 3 && (
                        <li className="text-[var(--muted)]">
                          {messages.repoPage?.andMorePages
                            ? messages.repoPage.andMorePages.replace(
                                "{count}",
                                (pagesInProgress.size - 3).toString()
                              )
                            : `...and ${pagesInProgress.size - 3} more`}
                        </li>
                      )}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </div>
        ) : error ? (
          <div className="flex flex-col items-center w-full">
            {/* 友好的提示信息 */}
            <div className="bg-[var(--card-bg)] rounded-lg p-8 mb-6 shadow-sm max-w-screen-xl mx-auto w-full text-center">
              <div className="flex justify-center mb-4">
                <div className="relative">
                  <div className="absolute -inset-2 bg-[var(--accent-primary)]/10 rounded-full blur-md"></div>
                  <FaBook className="text-3xl text-[var(--accent-primary)] relative z-10" />
                </div>
              </div>
              <h3 className="text-xl font-bold text-[var(--foreground)] mb-2 font-serif">
                {messages.repoPage?.generatingWiki || "你的wiki页面正在生成哦~"}
              </h3>
              <div className="text-[var(--muted)] mb-6 max-w-2xl mx-auto">
                {messages.repoPage?.wikiGenerationInProgress ||
                  "请耐心等候，这通常需要数十分钟...可以在右上角查看进度~"}
                <br />
                {messages.repoPage?.wikiGenerationInProgressWithStyle ? (
                  <div
                    dangerouslySetInnerHTML={{
                      __html:
                        messages.repoPage.wikiGenerationInProgressWithStyle,
                    }}
                  />
                ) : (
                  <>
                    等待期间，
                    <span className="font-bold text-green-600">
                      您可先用下方输入框提问，体验代码问答功能
                    </span>
                    。Wiki 生成后即可使用全部功能
                  </>
                )}
              </div>
              <div className="flex justify-center mb-6">
                <div className="w-12 h-12 border-4 border-[var(--accent-primary)] border-t-transparent rounded-full animate-spin"></div>
              </div>
            </div>

            {/* AI问答模块 */}
            <div className="max-w-screen-xl mx-auto w-full">
              <ChatInputAdapter
                wikiInfoId={currentWikiId || undefined}
                wikiStructure={wikiStructure}
                onSubmit={handleInitialQuestionSubmit}
                providers={providers}
                availableModels={availableModels}
                selectedProvider={selectedProvider}
                setSelectedProvider={setSelectedProvider}
                selectedModel={selectedModel}
                setSelectedModel={setSelectedModel}
                modelType={modelType}
                setModelType={setModelType}
                deepResearch={deepResearch}
                setDeepResearch={setDeepResearch}
                useFixedPosition={false}
              />
            </div>
          </div>
        ) : wikiStructure ? (
          <div className="container mx-auto grid grid-cols-1 lg:grid-cols-[minmax(250px,280px)_1fr] xl:grid-cols-[minmax(280px,320px)_1fr_minmax(250px,280px)] gap-6 max-h-[calc(100vh-120px)] min-w-0">
            {/* Left Column: Wiki Navigation */}
            <div className="max-h-[calc(100vh-120px)] bg-[var(--card-bg)] rounded-lg shadow-sm border border-[var(--border-color)] p-5 min-w-0 flex flex-col">
              <h3 className="text-lg font-bold text-[var(--foreground)] mb-3 font-serif">
                {wikiStructure.title}
              </h3>
              <p className="text-[var(--muted)] text-sm mb-5 leading-relaxed">
                {wikiStructure.description}
              </p>

              <div className="text-xs text-[var(--muted)] mb-5 flex items-center">
                {
                  <>
                    {<FaBitbucket className="mr-2" />}
                    <a
                      href={
                        effectiveRepoInfo?.repoUrl?.endsWith(".git")
                          ? effectiveRepoInfo.repoUrl.slice(0, -4)
                          : effectiveRepoInfo?.repoUrl ?? ""
                      }
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:text-[var(--accent-primary)] transition-colors border-b border-[var(--border-color)] hover:border-[var(--accent-primary)]"
                    >
                      {effectiveRepoInfo?.owner}/{effectiveRepoInfo?.repo}/
                      {effectiveRepoInfo?.branch}
                    </a>
                  </>
                }
              </div>

              <h4 className="text-md font-semibold text-[var(--foreground)] mb-3 font-serif">
                {messages.repoPage?.pages || "Pages"}
              </h4>
              <div className="flex-1 overflow-y-auto pr-1 left-column-scrollbar">
                <WikiTreeView
                  wikiStructure={wikiStructure}
                  currentPageTitle={currentPage}
                  onPageSelect={handlePageSelect}
                  messages={messages.repoPage}
                />
              </div>
            </div>

            {/* Center Column: Wiki Content */}
            <div
              id="wiki-content"
              className="max-h-[calc(100vh-120px)] bg-[var(--card-bg)] rounded-lg shadow-sm border border-[var(--border-color)] p-6 lg:p-8 overflow-y-auto min-w-0"
              style={{ paddingBottom: 188 }}
            >
              {currentPage && wikiPages[currentPage] ? (
                <div className="prose prose-sm md:prose-base lg:prose-lg max-w-none">
                  <Markdown
                    content={wikiPages[currentPage]}
                    branch={effectiveRepoInfo?.branch}
                    repo_url={effectiveRepoInfo?.repoUrl || ""}
                  />
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center p-8 text-[var(--muted)] h-full">
                  <div className="relative mb-4">
                    <div className="absolute -inset-2 bg-[var(--accent-primary)]/5 rounded-full blur-md"></div>
                    <FaBookOpen className="text-4xl relative z-10" />
                  </div>
                  <p className="font-serif">
                    {messages.repoPage?.selectPagePrompt ||
                      "Select a page from the navigation to view its content"}
                  </p>
                </div>
              )}
            </div>

            {/* Right Column: Actions and Table of Contents */}
            <aside className="hidden xl:block w-full flex-shrink-0 min-w-0">
              <div className="space-y-4">
                {/* Wiki Actions Section */}
                {/* 权限控制：只有有刷新或导出权限时才渲染 Wiki Actions */}
                {showWikiActions && (
                  <div className="p-4 bg-[var(--card-bg)] rounded-lg shadow-sm border border-[var(--border-color)] space-y-3">
                    <h3 className="font-semibold text-sm">
                      {messages.repoPage?.wikiActions || "Wiki Actions"}
                    </h3>

                    {/* 刷新按钮 */}
                    {canRefreshWiki && (
                      <button
                        onClick={handleRefreshWiki}
                        disabled={isLoading}
                        className="w-full flex items-center justify-center text-xs px-3 py-2 bg-[var(--background)] text-[var(--foreground)] rounded-md hover:bg-[var(--background)]/80 disabled:opacity-50 disabled:cursor-not-allowed border border-[var(--border-color)] transition-colors hover:cursor-pointer"
                      >
                        <FaSync
                          className={`mr-2 ${isLoading ? "animate-spin" : ""}`}
                        />
                        {messages.repoPage?.refreshWiki || "Refresh Wiki"}
                      </button>
                    )}

                    {/* 同步索引按钮 */}
                    {/* {canRefreshWiki && (
                      <button
                        onClick={handleSyncIndex}
                        disabled={isSyncingIndex}
                        className="w-full flex items-center justify-center text-xs px-3 py-2 bg-[var(--accent-primary)] text-white rounded-md hover:bg-[var(--accent-primary)]/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors hover:cursor-pointer"
                      >
                        <FaUpload
                          className={`mr-2 ${
                            isSyncingIndex ? "animate-spin" : ""
                          }`}
                        />
                        {isSyncingIndex ? "同步中..." : "同步索引"}
                      </button>
                    )} */}

                    {/* 索引进度显示 */}
                    {/* {syncProgress && (
                      <div className="w-full">
                        <div className="flex justify-between text-xs text-[var(--muted)] mb-1">
                          <span>索引进度</span>
                          <span>{Math.round(syncProgress.progress)}%</span>
                        </div>
                        <div className="w-full bg-[var(--border-color)] rounded-full h-2">
                          <div
                            className="bg-[var(--accent-primary)] h-2 rounded-full transition-all duration-300"
                            style={{ width: `${syncProgress.progress}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-[var(--muted)] mt-1">
                          {syncProgress.processed_files || 0} /{" "}
                          {syncProgress.total_files || 0} 文件
                        </div>
                        {syncProgress.message && (
                          <div className="text-xs text-[var(--muted)] mt-1">
                            {syncProgress.message}
                          </div>
                        )}
                      </div>
                    )} */}

                    {/* 导出按钮/下拉，仅有导出权限时显示 */}
                    {canExportWiki && Object.keys(wikiPages).length > 0 && (
                      <div className="relative" ref={exportDropdownRef}>
                        <button
                          onClick={() =>
                            setIsExportDropdownOpen(!isExportDropdownOpen)
                          }
                          className="w-full flex items-center justify-center text-xs px-3 py-2 bg-[var(--background)] text-[var(--foreground)] rounded-md hover:bg-[var(--background)]/80 disabled:opacity-50 disabled:cursor-not-allowed border border-[var(--border-color)] transition-colors"
                        >
                          <FaFileExport className="mr-2" />
                          {messages.repoPage?.exportWiki || "Export"}
                        </button>
                        <div
                          className={`absolute right-0 mt-1 w-full bg-[var(--card-bg)] rounded-md shadow-lg border border-[var(--border-color)] z-10 ${
                            isExportDropdownOpen ? "block" : "hidden"
                          }`}
                        >
                          <button
                            onClick={() => {
                              exportWiki("markdown");
                              setIsExportDropdownOpen(false);
                            }}
                            disabled={isExporting}
                            className="w-full text-left px-4 py-2 text-xs text-[var(--foreground)] hover:bg-[var(--background)]/50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                          >
                            <FaDownload className="mr-2" />
                            {messages.repoPage?.exportAsMarkdown ||
                              "Export as Markdown"}
                          </button>
                          <button
                            onClick={() => {
                              exportWiki("json");
                              setIsExportDropdownOpen(false);
                            }}
                            disabled={isExporting}
                            className="w-full text-left px-4 py-2 text-xs text-[var(--foreground)] hover:bg-[var(--background)]/50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                          >
                            <FaFileExport className="mr-2" />
                            {messages.repoPage?.exportAsJson ||
                              "Export as JSON"}
                          </button>
                        </div>
                      </div>
                    )}
                    {exportError && (
                      <div className="text-xs text-red-500 p-2 bg-red-500/10 rounded-md">
                        {exportError}
                      </div>
                    )}

                    {/* 更新时间信息 - 放在卡片底部 */}
                    {updatedTime && (
                      <div className="pt-2 mt-3 border-t border-[var(--border-color)]">
                        <div className="text-xs text-[var(--muted)] flex items-center gap-2">
                          <span className="opacity-75">
                            {messages.repoPage?.lastUpdated || '更新时间:'}
                          </span>
                          <time
                            dateTime={new Date(updatedTime).toISOString()}
                            title={new Date(updatedTime).toLocaleString()}
                            className="font-mono text-[var(--foreground)] opacity-90 hover:opacity-100 transition-opacity"
                          >
                            {new Date(updatedTime).toLocaleString()}
                          </time>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Chat History Section */}
                <ChatHistory
                  currentWikiId={currentWikiId}
                  userInfo={userInfo}
                />
              </div>
            </aside>
          </div>
        ) : null}
      </main>

      {/* ChatInputAdapter - 在正常状态下显示，使用固定定位 */}
      {!error && (
        <ChatInputAdapter
          wikiInfoId={currentWikiId || undefined}
          wikiStructure={wikiStructure}
          onSubmit={handleInitialQuestionSubmit}
          providers={providers}
          availableModels={availableModels}
          selectedProvider={selectedProvider}
          setSelectedProvider={setSelectedProvider}
          selectedModel={selectedModel}
          setSelectedModel={setSelectedModel}
          modelType={modelType}
          setModelType={setModelType}
          deepResearch={deepResearch}
          setDeepResearch={setDeepResearch}
          useFixedPosition={true}
          repoUrl={effectiveRepoInfo?.repoUrl || ""}
          branch={effectiveRepoInfo?.branch || "main"}
          userCode={userInfo?.user_code || ""}
          onFileManagerOpen={() => setShowFileManager(true)}
        />
      )}

      <SettingsModal
        isOpen={isSettingsModalOpen}
        onClose={() => setIsSettingsModalOpen(false)}
      />
      <JobsProgressModal
        isOpen={isJobsProgressModalOpen}
        onClose={() => setIsJobsProgressModalOpen(false)}
      />
      <RefreshModal
        isOpen={isRefreshModalOpen}
        onClose={() => setIsRefreshModalOpen(false)}
        onConfirm={handleRefreshConfirm}
        currentModel={selectedModel}
        apiKey={settings.apiKey || ""}
        sections={refreshableSections}
      />

      <FileManager
        isOpen={showFileManager}
        onClose={() => setShowFileManager(false)}
        repoUrl={effectiveRepoInfo?.repoUrl || ""}
        branch={effectiveRepoInfo?.branch || "main"}
        userCode={userInfo?.user_code || ""}
      />
    </div>
  );
}
