'use client';

import React, { useState } from 'react';
import ProcessedProjects from '@/components/ProcessedProjects';
import { useLanguage } from '@/contexts/LanguageContext';
import { Header } from '@/components';
import SettingsModal from '@/components/SettingsModal';

export default function ProcessedProjectsPage() {
  const { messages } = useLanguage();
  
  // State for settings modal
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);

  // Function to handle opening the settings modal
  const handleOpenSettings = () => {
    setIsSettingsModalOpen(true);
  };


  return (
    <div className="flex flex-col min-h-screen bg-[var(--background)]">
      <Header onOpenSettings={handleOpenSettings} />

      <main className="flex-grow container mx-auto px-4 py-8">
        <h1 className="text-4xl font-bold text-center mb-2 text-[var(--foreground)]">{messages.projects?.existingProjects || 'Existing Projects'}</h1>
        <p className="text-md text-center text-[var(--muted)] mb-8">{messages.projects?.browseProjects || 'Browse and manage your generated wikis'}</p>

        <ProcessedProjects showHeader={false} />
        
      </main>

      <SettingsModal isOpen={isSettingsModalOpen} onClose={() => setIsSettingsModalOpen(false)} />
    </div>
  );
}