"use client";

import AppApi from "@/components/AppApi";
import AppEditor from "@/components/AppEditor";
import AppToken from "@/components/AppToken";
import Popconfirm from "@/components/Popconfirm";
import AppSandbox from "@/components/AppSandbox";
import AppWiki from "@/components/AppWiki";
import { useLanguage } from "@/contexts/LanguageContext";
import { useToast } from "@/contexts/ToastContext";
import { authFetch } from "@/utils/authFetch";
import { usePageTitle } from "@/utils/pageTitle";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  PaginationState,
  useReactTable,
} from "@tanstack/react-table";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { getSandboxStatusDescription, getSandboxStatusStyle } from "@/components/k8s/status";
import { Fa<PERSON>he<PERSON>, FaRegCopy } from "react-icons/fa";
import { ImCancelCircle } from "react-icons/im";
import { RxTokens } from "react-icons/rx";
import { VscSymbolInterface } from "react-icons/vsc";
import { HiOutlineBookOpen } from "react-icons/hi";
import ReactPaginate from "react-paginate";

interface App {
  id: number;
  app_name: string;
  app_code: string;
  comments: string;
  state: boolean;
  created_date: string;
}

const AuthApp = () => {
  // 处理通过 URL 参数传递的页面标题，如果没有则使用默认标题
  usePageTitle('DeepWiki 应用管理')
  
  const { addToast } = useToast();

  const [apps, setApps] = useState<App[]>([]);
  const [pages, setPages] = useState<number>(0);
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const pageSizeOptions = [10, 20, 50];
  const [isAppEditorOpen, setIsAppEditorOpen] = useState<boolean>(false);
  const [total, setTotal] = useState<number>(0);
  const [selectedApp, setSelectedApp] = useState<App>();
  const [search, setSearch] = useState<string>("");
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [dropdownPos, setDropdownPos] = useState<Record<string, number>>({
    x: 0,
    y: 0,
  });
  const [showTokenManagement, setShowTokenManagement] =
    useState<boolean>(false);
  const [showApiManagement, setShowApiManagement] = useState<boolean>(false);
  const [showSandbox, setShowSandbox] = useState<boolean>(false);
  const [showWikiManagement, setShowWikiManagement] = useState<boolean>(false);
  const [sandboxStatus, setSandboxStatus] = useState<Record<number, string>>({});
  const [sandboxStatusLoading, setSandboxStatusLoading] = useState<Record<number, boolean>>({});
  const { messages, language } = useLanguage();
  const [isPopupShow, setIsPopupShow] = useState<boolean>(false);

  useEffect(() => {
    if (!showDropdown) return;

    const handleDocumentClick = () => {
      if (!isPopupShow) {
        setShowDropdown(false);
      }
    };

    document.addEventListener("click", handleDocumentClick);

    return () => {
      document.removeEventListener("click", handleDocumentClick);
    };
  }, [showDropdown, isPopupShow]);

  const formatSensitiveData = (origin: string) => {
    return origin.slice(0, 4) + "****" + origin.slice(-4);
  };

  const fetchApps = useCallback(async () => {
    const response = await authFetch(
      `/api/apps?search=${search}&page=${pagination.pageIndex}&size=${pagination.pageSize}`
    );

    if (response && response.ok) {
      const json = await response.json();
      if (json) {
        setPages(Math.ceil(json.total / pagination.pageSize));
        setTotal(json.total);
        const list: App[] = json.data || [];
        setApps(list);
        
        // 立即设置加载状态
        const loadingMap: Record<number, boolean> = {};
        list.forEach(app => {
          loadingMap[app.id] = true;
        });
        setSandboxStatusLoading(prev => ({ ...prev, ...loadingMap }));
        
        // 并发加载沙盒状态（仅对当前页数据）
        try {
          const results = await Promise.all(
            list.map(async (a: App) => {
              try {
                const r = await authFetch(`/api/k8s/sandbox/app/${a.id}/status`);
                if (r && r.ok) {
                  const jd = await r.json();
                  const st: string = (jd && jd.success && jd.data && jd.data.status) || "";
                  return [a.id, st] as [number, string];
                }
              } catch {}
              return [a.id, ""] as [number, string];
            })
          );
          const map: Record<number, string> = {};
          results.forEach(([id, st]) => (map[id] = st));
          setSandboxStatus(prev => ({ ...prev, ...map }));
          
          // 清除加载状态
          const clearLoading: Record<number, boolean> = {};
          list.forEach(app => {
            clearLoading[app.id] = false;
          });
          setSandboxStatusLoading(prev => ({ ...prev, ...clearLoading }));
        } catch {
          // 即使出错也要清除加载状态
          const clearLoading: Record<number, boolean> = {};
          list.forEach(app => {
            clearLoading[app.id] = false;
          });
          setSandboxStatusLoading(prev => ({ ...prev, ...clearLoading }));
        }
      }
    }
  }, [search, pagination, authFetch, setSandboxStatus, setSandboxStatusLoading]);

  useEffect(() => {
    fetchApps();
  }, [pagination]);

  const deleteApp = useCallback(async (id: number) => {
    const response = await authFetch(`/api/apps/${id}`, {
      method: "DELETE",
    });

    if (response && response.ok) {
      addToast({
        type: "success",
        title: messages.authApp.deleteApp,
        message: messages.authApp.deleteAppSuccess,
      });
      fetchApps();
    } else {
      addToast({
        type: "error",
        title: messages.authApp.deleteApp,
        message: messages.authApp.deleteAppFail,
      });
    }
  }, [addToast, messages, fetchApps]);

  const handleCopy = useCallback(async (text?: string) => {
    if (!text) return;

    const showToast = (type: "success" | "error", message: string) => {
      addToast({
        type,
        title:
          type === "success"
            ? messages.authApp.copySuccess
            : messages.authApp.copyFail,
        message,
      });
    };

    try {
      // 优先使用 Clipboard API
      if (window.isSecureContext && navigator.clipboard) {
        await navigator.clipboard.writeText(text);
        showToast("success", messages.authApp.copyToClipboard);
        return;
      }

      // 降级方案
      const textarea = document.createElement("textarea");
      textarea.value = text;
      textarea.style.position = "fixed";
      textarea.style.opacity = "0";
      document.body.appendChild(textarea);
      textarea.select();

      try {
        const successful = document.execCommand("copy");
        if (!successful) throw new Error("execCommand failed");
        showToast("success", messages.authApp.copyToClipboard);
      } finally {
        document.body.removeChild(textarea);
      }
    } catch (error) {
      console.error(messages.authApp.copyFail, error);
      showToast(
        "error",
        `${messages.authApp.copyFail}: ${
          error instanceof Error ? error.message : messages.authApp.copyManual
        }`
      );
    }
  }, [addToast, messages]);

  const columns: ColumnDef<App>[] = useMemo(
    () => [
      {
        accessorKey: "app_name",
        header: messages.authApp.appName,
        size: 80,
        cell: (info) => (
          <div className="text-sm font-medium text-gray-800 overflow-hidden whitespace-nowrap text-ellipsis">
            {info.getValue() as string}
          </div>
        ),
      },
      {
        accessorKey: "sandbox_status",
        header: messages.authApp.appSandbox,
        size: 80,
        cell: (info) => {
          const app = info.row.original as App;
          const isLoading = sandboxStatusLoading[app.id];
          const st = (sandboxStatus[app.id] || "").toUpperCase();
          
          if (isLoading) {
            return (
              <span className="inline-flex items-center gap-1 text-xs text-gray-500">
                <div className="w-3 h-3 border border-gray-300 border-t-transparent rounded-full animate-spin"></div>
                {messages.common.loading}
              </span>
            );
          }
          
          const badge = getSandboxStatusStyle(st || "NOT_CREATED");
          const label = st ? getSandboxStatusDescription(st, language) : "-";
          return (
            <span className={badge}>{label}</span>
          );
        },
      },
      {
        accessorKey: "app_code",
        header: messages.authApp.appCode,
        size: 100,
        cell: (info) => (
          <div className="text-sm font-medium text-gray-800 overflow-hidden whitespace-nowrap text-ellipsis">
            {info.getValue() as string}
          </div>
        ),
      },
      {
        accessorKey: "app_id",
        header: "APP_ID",
        size: 100,
        cell: (info) => (
          <div className="flex items-center">
            <div
              className="text-sm font-medium text-gray-800 overflow-hidden whitespace-nowrap text-ellipsis"
              title={info.getValue() as string}
            >
              {info.getValue() as string}
            </div>
            <button
              className="ml-1 p-1 rounded hover:bg-[var(--hover-bg)] transition-colors"
              title={messages.authApp.copyAppId}
              onClick={() => handleCopy(info.getValue() as string)}
              style={{ lineHeight: 0 }}
            >
              <FaRegCopy
                size={16}
                className="text-[var(--muted)] hover:text-[var(--primary)]"
              />
            </button>
          </div>
        ),
      },
      {
        accessorKey: "app_secret",
        header: "APP_SECRET",
        size: 100,
        cell: (info) => (
          <div className="flex items-center">
            <div className="text-sm font-medium text-gray-800 w-[90px] overflow-hidden whitespace-nowrap text-ellipsis">
              {formatSensitiveData(info.getValue() as string)}
            </div>
            <button
              className="ml-1 p-1 rounded hover:bg-[var(--hover-bg)] transition-colors"
              title={messages.authApp.copyAppSecret}
              onClick={() => handleCopy(info.getValue() as string)}
              style={{ lineHeight: 0 }}
            >
              <FaRegCopy
                size={16}
                className="text-[var(--muted)] hover:text-[var(--primary)]"
              />
            </button>
          </div>
        ),
      },
      {
        accessorKey: "created_by",
        header: messages.authApp.createdBy,
        size: 100,
        cell: (info) => (
          <div className="text-sm font-medium text-gray-800 overflow-hidden whitespace-nowrap text-ellipsis" title={info.getValue() as string}>
            {info.getValue() as string}
          </div>
        ),
      },
      {
        accessorKey: "created_date",
        header: messages.authApp.createdTime,
        size: 100,
        cell: (info) => (
          <div className="text-sm font-medium text-gray-800">
            {info.getValue() as string}
          </div>
        ),
      },
      {
        accessorKey: "state",
        header: messages.authApp.state,
        size: 70,
        cell: (info) => (
          <div
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-semibold ${
              info.getValue()
                ? "bg-green-100 text-green-800"
                : "bg-red-100 text-red-800"
            }`}
          >
            {info.getValue()
              ? messages.authApp.enable
              : messages.authApp.disable}
          </div>
        ),
      },
      {
        accessorKey: "operation",
        header: messages.authApp.operation,
        size: 100,
        cell: (info) => (
          <div className="flex items-center gap-x-2 relative">
            <div
              className="text-blue-500 text-sm cursor-pointer"
              onClick={() => {
                setSelectedApp(info.row.original);
                setIsAppEditorOpen(true);
              }}
            >
              {messages.authApp.edit}
            </div>
            <Popconfirm
              title={messages.authApp.deleteApp}
              description={messages.authApp.confirmDeleteAppDesc.replace(
                "{app_name}",
                info.row.original.app_name
              )}
              onConfirm={() => {
                deleteApp(info.row.original.id);
              }}
              onCancel={() => {}}
            >
              <div className="text-red-500 text-sm cursor-pointer">
                {messages.authApp.delete}
              </div>
            </Popconfirm>

            <div
              className="text-blue-500 text-sm cursor-pointer"
              onClick={(e) => {
                setSelectedApp(info.row.original);
                setDropdownPos({ x: e.pageX, y: e.pageY });
                setShowDropdown(true);
              }}
            >
              {messages.authApp.more}
            </div>
          </div>
        ),
      },
    ],
    [sandboxStatus, sandboxStatusLoading, messages, language, deleteApp, handleCopy]
  );

  const table = useReactTable({
    data: apps,
    columns: columns,
    getCoreRowModel: getCoreRowModel(),
  });

  const modifyAppState = async () => {
    if (!selectedApp) {
      return;
    }
    const response = await authFetch(`/api/apps/${selectedApp.id}/state`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ state: !selectedApp.state }),
    });

    if (response && response.ok) {
      addToast({
        type: "success",
        title: messages.authApp.modifyAppState,
        message: messages.authApp.modifyAppStateSucess,
      });
      fetchApps();
    } else {
      addToast({
        type: "error",
        title: messages.authApp.modifyAppState,
        message: messages.authApp.modifyAppStateFail,
      });
    }
  };

  return (
    <div className="w-full p-8 flex flex-col gap-y-4 relative">
      {/* top */}
      <div className="w-full flex justify-between items-center">
        <div className="flex gap-x-4">
          <input
            type="text"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-xs px-3 py-2 text-sm rounded-md bg-transparent text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)] border border-[var(--border-color)]"
            placeholder={messages.authApp.appNameOrAppCode}
          />
          <button
            className="btn-primary mr-3 cursor-pointer"
            onClick={() => {
              if (pagination.pageIndex !== 0) {
                setPagination((pre) => {
                  return { ...pre, pageIndex: 0 };
                });
              } else {
                fetchApps();
              }
            }}
          >
            {messages.user.search}
          </button>
        </div>
        <div>
          <button
            className="px-4 py-2 text-sm font-medium rounded-md border border-transparent bg-[var(--accent-primary)]/90 text-white hover:bg-[var(--accent-primary)] transition-colors disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
            onClick={() => {
              setSelectedApp(undefined);
              setIsAppEditorOpen(true);
            }}
          >
            {messages.authApp.createApp}
          </button>
        </div>
      </div>
      {/* table */}
      <div className="table-container w-full ">
        <table className="w-full table-fixed">
          <thead className="border-b border-[var(--border-color)]">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id} className="border-b border-gray-200">
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    className="px-6 py-4 text-left text-xs font-bold text-gray-500 uppercase tracking-wider"
                    style={{ width: header.getSize() }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody className="w-full">
            {table.getRowModel().rows.length > 0 ? (
              table.getRowModel().rows.map((row) => (
                <tr
                  key={row.original.id}
                  className={`border-b border-[var(--border-color)]`}
                >
                  {row.getVisibleCells().map((cell) => (
                    <td
                      key={cell.id}
                      className="px-6 py-4"
                      style={{ width: cell.column.getSize() }}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </td>
                  ))}
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={table.getAllColumns().length}
                  className="px-4 py-16 text-center"
                >
                  <div className="flex flex-col items-center justify-center text-[var(--muted)]">
                    {/* <div className="w-10 h-10 rounded-full bg-[var(--accent-secondary)] flex items-center justify-center">
                      
                    </div> */}
                    <p className="text-sm font-medium">
                      {messages.authApp.noData}
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* pagination */}
      <div className="w-full flex items-center gap-3 justify-end">
        <div>{`${messages.authApp.total} ${total} ${messages.authApp.records}`}</div>
        <ReactPaginate
          forcePage={pagination.pageIndex}
          pageCount={pages}
          pageRangeDisplayed={3}
          marginPagesDisplayed={2}
          onPageChange={(e) => {
            setPagination((pre) => {
              return { ...pre, pageIndex: e.selected };
            });
          }}
          containerClassName="inline-flex gap-1"
          pageClassName=""
          pageLinkClassName="pagination-link"
          previousClassName=""
          previousLinkClassName="pagination-link"
          nextClassName=""
          nextLinkClassName="pagination-link"
          breakClassName=""
          breakLinkClassName="pagination-link"
          activeClassName=""
          activeLinkClassName="pagination-link-active"
          disabledClassName=""
          disabledLinkClassName="pagination-link-disabled"
          previousLabel="‹"
          nextLabel="›"
          breakLabel="..."
        />
        <div>
          <select
            className="px-2 py-1.5 border border-[var(--border-color)] rounded-sm outline-0 text-sm"
            value={pagination.pageSize}
            onChange={(e) => {
              setPagination({
                pageSize: Number(e.target.value),
                pageIndex: 0,
              });
            }}
          >
            {pageSizeOptions.map((item) => (
              <option key={item} value={item}>
                {`${item} ${messages.authApp.recoredPerPage}`}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* 应用信息编辑 */}
      {isAppEditorOpen && (
        <AppEditor
          exist={selectedApp}
          onClose={() => setIsAppEditorOpen(false)}
          onFinish={() => {
            fetchApps();
            setIsAppEditorOpen(false);
          }}
        />
      )}

      {/* token管理 */}
      {showTokenManagement && selectedApp && (
        <AppToken
          app_primary_key={selectedApp.id}
          onClose={() => setShowTokenManagement(false)}
        />
      )}

      {/* 接口管理 */}
      {showApiManagement && selectedApp && (
        <AppApi
          app_primary_key={selectedApp.id}
          onClose={() => setShowApiManagement(false)}
        />
      )}

      {showSandbox && selectedApp && (
        <AppSandbox
          app_id={selectedApp.id}
          app_code={selectedApp.app_code}
          app_name={selectedApp.app_name}
          onClose={() => {
            setShowSandbox(false);
            fetchApps();
          }}
        />
      )}

      {showWikiManagement && selectedApp && (
        <AppWiki
          app_primary_key={selectedApp.id}
          onClose={() => setShowWikiManagement(false)}
        />
      )}

      {/* 修改应用状态确认框
      {selectedApp && showModifyStateConfirm && (
        <DeleteConfirmModal
          isOpen={showModifyStateConfirm}
          onClose={() => {
            setShowModifyStateConfirm(false);
          }}
          onConfirm={() => {
            if (selectedApp) {
              modifyAppState();
            }
          }}
          title={
            selectedApp.state
              ? messages.authApp.disableApp
              : messages.authApp.enableApp
          }
          message={
            selectedApp.state
              ? messages.authApp.confirmDisableAppDesc.replace(
                  "{app_name}",
                  selectedApp.app_name
                )
              : messages.authApp.confirmEnableAppDesc.replace(
                  "{app_name}",
                  selectedApp.app_name
                )
          }
        />
      )} */}

      {/* 更多菜单 */}
      {selectedApp && showDropdown && (
        <div
          className="dropdown-container absolute bg-[var(--card-bg)] border border-[var(--border-color)] rounded-lg shadow-lg z-[9999] min-w-[100px] flex flex-col justify-center py-2 text-left"
          style={{
            top: `${dropdownPos?.y}px`,
            left: `calc(${dropdownPos?.x}px - 100px)`,
          }}
        >
          {selectedApp.state ? (
            <Popconfirm
              title={messages.authApp.disableApp}
              description={messages.authApp.confirmDisableAppDesc.replace(
                "{app_name}",
                selectedApp.app_name
              )}
              onConfirm={() => {
                modifyAppState();
                setIsPopupShow(false);
                setShowDropdown(false);
              }}
              onCancel={() => {
                setIsPopupShow(false);
                setShowDropdown(false);
              }}
            >
              <button
                className="flex items-center justify-start gap-2 w-full px-3 py-1 text-sm text-[var(--foreground)] hover:bg-[var(--accent-primary)]/10 dark:hover:bg-[var(--accent-primary)]/20 transition-colors cursor-pointer"
                title={messages.authApp.disable}
                onClick={() => setIsPopupShow(true)}
              >
                <ImCancelCircle className="h-4 w-4 text-[var(--muted)]" />
                {messages.authApp.disable}
              </button>
            </Popconfirm>
          ) : (
            <Popconfirm
              title={messages.authApp.enableApp}
              description={messages.authApp.confirmEnableAppDesc.replace(
                "{app_name}",
                selectedApp?.app_name
              )}
              onConfirm={() => {
                modifyAppState();
                setIsPopupShow(false);
                setShowDropdown(false);
              }}
              onCancel={() => {
                setIsPopupShow(false);
                setShowDropdown(false);
              }}
            >
              <button
                className="flex items-center justify-start gap-2 w-full px-3 py-1 text-sm text-[var(--foreground)] hover:bg-[var(--accent-primary)]/10 dark:hover:bg-[var(--accent-primary)]/20 transition-colors cursor-pointer"
                title={messages.authApp.enable}
                onClick={() => setIsPopupShow(true)}
              >
                <FaCheck className="h-4 w-4 text-[var(--muted)]" />
                {messages.authApp.enable}
              </button>
            </Popconfirm>
          )}
          <button
            className="flex items-center justify-start gap-2 w-full px-3 py-1 text-sm text-[var(--foreground)] hover:bg-[var(--accent-primary)]/10 dark:hover:bg-[var(--accent-primary)]/20 transition-colors cursor-pointer"
            title={messages.authApp.tokenManagement}
            onClick={() => setShowTokenManagement(true)}
          >
            <RxTokens className="h-4 w-4 text-[var(--muted)]" />
            Token
          </button>
          <button
            className="flex items-center justify-start gap-2 w-full px-3 py-1 text-sm text-[var(--foreground)] hover:bg-[var(--accent-primary)]/10 dark:hover:bg-[var(--accent-primary)]/20 transition-colors cursor-pointer"
            title={messages.authApp.apiManagement}
            onClick={() => setShowApiManagement(true)}
          >
            <VscSymbolInterface className="h-4 w-4 text-[var(--muted)]" />
            API
          </button>
          <button
            className="flex items-center justify-start gap-2 w-full px-3 py-1 text-sm text-[var(--foreground)] hover:bg-[var(--accent-primary)]/10 dark:hover:bg-[var(--accent-primary)]/20 transition-colors cursor-pointer"
            title={messages.authApp.wikiManagement}
            onClick={() => {
              setShowWikiManagement(true);
              setShowDropdown(false);
            }}
          >
            <HiOutlineBookOpen className="h-4 w-4 text-[var(--muted)]" />
            {messages.authApp.wikiManagement}
          </button>
          <button
            className="flex items-center justify-start gap-2 w-full px-3 py-1 text-sm text-[var(--foreground)] hover:bg-[var(--accent-primary)]/10 dark:hover:bg-[var(--accent-primary)]/20 transition-colors cursor-pointer"
            title={messages.authApp.appSandbox}
            onClick={() => setShowSandbox(true)}
          >
            <span className="inline-block w-4 h-4 rounded-full bg-[var(--accent-primary)]/70" />
            {messages.authApp.appSandbox}
          </button>
        </div>
      )}
    </div>
  );
};

export default AuthApp;
