"use client";

import React, { useEffect, useState, useRef } from "react";
import { useRouter } from 'next/navigation';
import { useLanguage } from "@/contexts/LanguageContext";

export default function TokenPage() {   
    const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
    const [message, setMessage] = useState('');
    const { messages: t } = useLanguage();
    const router = useRouter();
    const hasCalledRef = useRef(false); // 防止重复调用

    useEffect(() => {
        // 防止重复调用
        if (hasCalledRef.current) {
            return;
        }
        hasCalledRef.current = true;

        // Only access window.location in the browser
        if (typeof window === 'undefined') {
            setStatus('error');
            setMessage(t.token.clientError);
            return;
        }

        const code = new URLSearchParams(window.location.search).get('code');
        
        if (!code) {
            setStatus('error');
            setMessage(t.token.noCode);
            return;
        }

        const fetchToken = async () => {
            try {
            
                const params = new URLSearchParams();
                params.set('code', code);
                const url = '/api/auth/getToken' + '?' + params.toString();

                const response = await fetch(url, {
                  method: 'GET',
                });
                
                if (!response.ok) {
                    throw new Error(`SSO 服务器返回错误: ${response.status}`);
                }
                
                // 成功获取 token
                setStatus('success');
                setMessage(t.token.processing);
                
                // 触发自定义事件通知 AuthProvider 重新检查认证状态
                window.dispatchEvent(new CustomEvent('authTokenReceived'));
                
                // 等待认证状态更新后再跳转
                // 使用更长的延迟确保认证检查完成
                setTimeout(async () => {
                    // 再次检查认证状态，确保已登录
                    try {
                        const authCheckResponse = await fetch('/api/auth/isLogged');
                        if (authCheckResponse.ok) {
                            const authData = await authCheckResponse.json();
                            if (authData.is_login) {
                                console.log('认证状态确认成功，跳转到首页');
                                router.push('/');
                            } else {
                                console.warn('认证状态检查失败，延长等待时间');
                                // 如果还没有登录成功，再等待一段时间
                                setTimeout(() => {
                                    router.push('/');
                                }, 100);
                            }
                        } else {
                            console.warn('认证状态检查请求失败，直接跳转');
                            router.push('/');
                        }
                    } catch (error) {
                        console.error('认证状态检查出错，直接跳转:', error);
                        router.push('/');
                    }
                }, 100);
                
            } catch (error) {
                console.error('获取令牌时出错:', error);
                setStatus('error');
                setMessage(t.token.networkError);
            }
        };
        
        fetchToken();
    }, []); // 空依赖数组，确保只执行一次

    // 渲染不同状态的UI
    if (status === 'loading') {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p>{t.token.login}</p>
                </div>
            </div>
        );
    }

    if (status === 'error') {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <div className="text-red-600 text-xl mb-4">❌</div>
                    <p className="text-red-600 mb-4">{message}</p>
                    <button 
                        onClick={() => router.push('/')}
                        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                    >
                        {t.token.returnHome}
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
                <div className="text-green-600 text-xl mb-4">✅</div>
                <p className="text-green-600">{message}</p>
            </div>
        </div>
    );
}