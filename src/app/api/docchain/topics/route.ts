import { NextResponse } from 'next/server';

interface TopicInfo {
  id: number;
  name: string;
  state: string;
  create_date: string;
  state_date: string;
  extra: Record<string, unknown>;
  prompt?: string;
  introduce?: string;
}

export async function GET(request: Request) {
  try {

    // 1. 获取参数
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'default';

    let url;

    // 2. 按 type 切换配置,获取DocChain配置
    let docchainBaseUrl: string;
    let docchainApiKey: string;

    if (type === 'lab') {
      docchainBaseUrl = process.env.LAB_DOCCHAIN_BASE_URL || 'http://localhost:7000';
      docchainApiKey = process.env.LAB_DOCCHAIN_API_KEY || '';
      url = `${docchainBaseUrl}/v1/topic/list`;

    } else {
      docchainBaseUrl = process.env.DOCCHAIN_BASE_URL || 'http://localhost:7000';
      docchainApiKey = process.env.DOCCHAIN_API_KEY || '';
      // 构建请求URL
      url = `${docchainBaseUrl}/llmdoc/v1/topic/list`;
    }

    console.log(`topiclisturl:${url}`)

    // 构建请求头
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (docchainApiKey) {
      headers['X-Api-Key'] = docchainApiKey;
    }

    // 发送请求到DocChain服务
    const response = await fetch(url, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      throw new Error(`DocChain API responded with status: ${response.status}`);
    }

    const data = await response.json();

    // 转换数据格式
    const topics: TopicInfo[] = Array.isArray(data) ? data.map((topic: Record<string, unknown>) => ({
      id: Number(topic.id),
      name: String(topic.name),
      state: String(topic.state),
      create_date: String(topic.create_date),
      state_date: String(topic.state_date),
      extra: (topic.extra as Record<string, unknown>) || {},
      prompt: topic.prompt ? String(topic.prompt) : undefined,
      introduce: topic.introduce ? String(topic.introduce) : undefined,
    })) : [];

    return NextResponse.json({
      success: true,
      data: topics,
    });

  } catch (error) {
    console.error('Error fetching DocChain topics:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        data: [],
      },
      { status: 500 }
    );
  }
} 