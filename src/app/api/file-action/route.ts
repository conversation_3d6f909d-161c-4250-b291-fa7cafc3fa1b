import { NextRequest, NextResponse } from "next/server";  
import { getServerInternalUrl } from '@/utils/config';  
import { routerAuthFetch } from "@/utils/routerAuthFetch";

const TARGET_SERVER_BASE_URL = getServerInternalUrl();  
  
export async function GET(request: NextRequest) {  
    try {  
      // 从URL查询参数中获取数据，而不是从请求体
      const { searchParams } = new URL(request.url);
      const params = {
        filePath: searchParams.get('filePath'),
        lines: searchParams.get('lines'),
        owner: searchParams.get('owner'),
        repo: searchParams.get('repo'),
        action: searchParams.get('action'),
        branch: searchParams.get('branch')
      };
        
      const response = await routerAuthFetch(request, `${TARGET_SERVER_BASE_URL}/api/file-action`, {
        method: 'POST',  
        body: JSON.stringify(params),  
      });  
        
      if (!response.ok) {  
        return NextResponse.json(  
          { error: `Backend server returned ${response.status}` },  
          { status: response.status }  
        );  
      }  
        
      const data = await response.json();  
        
      // 如果后端返回了要打开的URL，直接重定向  
      if (data.success && data.action === "open_url" && data.url) {  
        return NextResponse.redirect(data.url);  
      }  
        
      return NextResponse.json(data);  
    } catch (error) {  
      console.error('Error forwarding request to backend:', error);  
      return NextResponse.json(  
        { error: 'Internal Server Error' },  
        { status: 500 }  
      );  
    }  
  }
