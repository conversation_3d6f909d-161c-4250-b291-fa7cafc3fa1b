/* eslint-disable @typescript-eslint/no-unused-vars */
'use client';

import React, { useCallback, useState, useMemo, useEffect, useRef } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import { FaBook, FaBookOpen, FaGithub, FaGitlab, FaBitbucket, FaDownload, FaFileExport, FaFolder, FaUpload } from 'react-icons/fa';
import Markdown from '@/components/Markdown';
import ModelSelectionModal from '@/components/ModelSelectionModal';
import WikiTreeView from '@/components/WikiTreeView';
import { useLanguage } from '@/contexts/LanguageContext';
import { RepoInfo, SubRepoInfo } from '@/types/repoinfo';
import { ModelSettings } from '@/types/modelConfig';
import getRepoUrl from '@/utils/getRepoUrl';
import { Header } from '@/components';
import { JobsProgressModal } from '@/components/JobsProgressModal'; // 导入JobsProgressModal
import RefreshModal from '@/components/RefreshModal'; // 导入RefreshModal
import FileManager from '@/components/FileManager';
import { useSettings } from '@/contexts/SettingsContext';
import SettingsModal from '@/components/SettingsModal';
import { v4 as uuidv4 } from 'uuid';
import ChatInputAdapter from '@/components/ChatInputAdapter';
import { authFetch } from '@/utils/authFetch';
import { useToast } from '@/contexts/ToastContext';
import { useAuth } from '@/contexts/AuthContext';
import ChatHistory from '@/components/ChatHistory';
import { ComponentPrivilegeEnum } from '@/types/privilege';
import { home } from '@/utils/auth';


interface WikiSection {
  id: string;
  title: string;
  pages: string[];
  subsections?: string[];
}

interface WikiPage {
  id: string;
  title: string;
  content: string;
  filePaths: string[];
  importance: 'high' | 'medium' | 'low';
  relatedPages: string[];
  parentId?: string;
  isSection?: boolean;
  children?: string[];
}

interface WikiStructure {
  id: string;
  title: string;
  description: string;
  pages: WikiPage[];
  sections: WikiSection[];
  rootSections: string[];
}


interface Model {
  id: string;
  name: string;
}

interface Provider {
  id: string;
  name: string;
  models: Model[];
  supportsCustomModel?: boolean;
}





// Add CSS styles for wiki with Japanese aesthetic
const wikiStyles = `
  .prose code {
    @apply bg-[var(--background)]/70 px-1.5 py-0.5 rounded font-mono text-xs border border-[var(--border-color)];
  }

  .prose pre {
    @apply bg-[var(--background)]/80 text-[var(--foreground)] rounded-md p-4 overflow-x-auto border border-[var(--border-color)] shadow-sm;
  }

  .prose h1, .prose h2, .prose h3, .prose h4 {
    @apply font-serif text-[var(--foreground)];
  }

  .prose p {
    @apply text-[var(--foreground)] leading-relaxed;
  }

  .prose a {
    @apply text-[var(--accent-primary)] hover:text-[var(--highlight)] transition-colors no-underline border-b border-[var(--border-color)] hover:border-[var(--accent-primary)];
  }

  .prose blockquote {
    @apply border-l-4 border-[var(--accent-primary)]/30 bg-[var(--background)]/30 pl-4 py-1 italic;
  }

  .prose ul, .prose ol {
    @apply text-[var(--foreground)];
  }

  .prose table {
    @apply border-collapse border border-[var(--border-color)];
  }

  .prose th {
    @apply bg-[var(--background)]/70 text-[var(--foreground)] p-2 border border-[var(--border-color)];
  }

  .prose td {
    @apply p-2 border border-[var(--border-color)];
  }

  /* 文件引用链接的特殊样式 */
  .prose a[href*="filePath"] {
    @apply no-underline border-none;
  }

  /* 代码块行号样式优化 */
  .prose pre .react-syntax-highlighter-line-number {
    @apply text-xs text-gray-500 select-none pr-4 text-right;
    min-width: 3em;
  }

  /* 代码块容器样式 */
  .prose .react-syntax-highlighter {
    @apply rounded-b-md shadow-sm;
  }

  /* 行内代码优化 */
  .prose code:not([class*="language-"]) {
    @apply bg-[var(--accent-secondary)]/60 border border-[var(--border-color)]/30 font-medium;
  }

  /* 左侧列滚动条悬浮显示 */
  .left-column-scrollbar {
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* IE and Edge */
  }
  
  .left-column-scrollbar::-webkit-scrollbar {
    display: none !important; /* Chrome, Safari and Opera */
  }
  
  .left-column-scrollbar:hover {
    scrollbar-width: thin !important; /* Firefox */
    -ms-overflow-style: auto !important; /* IE and Edge */
  }
  
  .left-column-scrollbar:hover::-webkit-scrollbar {
    display: block !important; /* Chrome, Safari and Opera */
  }
`;

// Helper function to generate cache key for localStorage
const getCacheKey = (owner: string, repo: string, repoType: string, language: string, isComprehensive: boolean = true): string => {
  return `deepwiki_cache_${repoType}_${owner}_${repo}_${language}_${isComprehensive ? 'comprehensive' : 'concise'}`;
};

export default function RepoWikiPage() {

  // Get route parameters and search params
  const params = useParams();
  const searchParams = useSearchParams();

  // Extract owner and repo from route params
  const owner = params.owner as string;
  const repo = params.repo as string;

  // Extract tokens from search params
  const token = searchParams.get('token') || '';
  const repoType = searchParams.get('type') || 'github';
  const branch = searchParams.get('branch') || 'main';
  const localPath = searchParams.get('local_path') ? decodeURIComponent(searchParams.get('local_path') || '') : undefined;
  const repoUrl = searchParams.get('repo_url') ? decodeURIComponent(searchParams.get('repo_url') || '') : undefined;
  const providerParam = searchParams.get('provider') || '';
  const modelParam = searchParams.get('model') || '';
  const isCustomModelParam = searchParams.get('is_custom_model') === 'true';
  const customModelParam = searchParams.get('custom_model') || '';
  const language = searchParams.get('language') || 'en';

  const wiki_id = searchParams.get('wiki_id') as string;

  // 解析 sub_repos 为数组
  const subReposParam = searchParams.get('sub_repos');
  const subRepos: SubRepoInfo[] = useMemo(() => {
    if (subReposParam) {
      try {
        return JSON.parse(decodeURIComponent(subReposParam));
      } catch {
        return [];
      }
    }
    return [];
  }, [subReposParam]);

  // Import language context for translations
  const { messages } = useLanguage();
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);

  // Initialize repo info
  const repoInfo = useMemo<RepoInfo>(() => ({
    owner,
    repo,
    type: repoType,
    token: token || null,
    localPath: localPath || null,
    repoUrl: repoUrl || null,
    branch: branch,
    subRepos: subRepos,
    wiki_id: wiki_id
  }), [owner, repo, repoType, localPath, repoUrl, token, subRepos, branch, wiki_id]);

  // State variables
  const [isLoading, setIsLoading] = useState(true);
  const [loadingMessage, setLoadingMessage] = useState<string | undefined>(
    messages.loading?.initializing || 'Initializing wiki generation...'
  );
  const [error, setError] = useState<string | null>(null);
  const [wikiStructure, setWikiStructure] = useState<WikiStructure | undefined>();
  const [currentPageId, setCurrentPageId] = useState<string | undefined>();
  const [generatedPages, setGeneratedPages] = useState<Record<string, WikiPage>>({});
  const [pagesInProgress, setPagesInProgress] = useState(new Set<string>());
  const [isExporting, setIsExporting] = useState(false);
  const [exportError, setExportError] = useState<string | null>(null);
  const [requestInProgress, setRequestInProgress] = useState(false);
  const [currentToken, setCurrentToken] = useState(token); // Track current effective token
  const [effectiveRepoInfo, setEffectiveRepoInfo] = useState(repoInfo); // Track effective repo info with cached data
  const [isExportDropdownOpen, setIsExportDropdownOpen] = useState(false); // 控制导出下拉菜单的显示
  const exportDropdownRef = useRef<HTMLDivElement>(null); // 导出下拉菜单的引用
  const [currentWikiId, setCurrentWikiId] = useState<string | null>(null); // 保存当前wiki的ID
  const { userInfo } = useAuth(); // keep single declaration
  const [hasWikiAccessPerm, setWikiAccessPerm] = useState(false);
  const { addToast } = useToast();
  const refreshableSections = useMemo(() => {
    if (!wikiStructure?.sections || wikiStructure.sections.length === 0) {
      return [] as { id: string; title: string; pages: string[] }[];
    }
    return wikiStructure.sections
      .filter((section) => Array.isArray(section.pages) && section.pages.length > 0)
      .map((section) => ({
        id: section.id,
        title: section.title || section.id,
        pages: section.pages,
      }));
  }, [wikiStructure]);
  

  // ownerId 状态
  const [ownerId, setOwnerId] = useState<number | undefined>(undefined);

  // 权限
  const { hasRefreshWikiPermission, hasExportWikiPermission } = useAuth();
  const canRefreshWiki = ownerId !== undefined
    ? hasRefreshWikiPermission?.(ownerId, ComponentPrivilegeEnum.REFRESH_WIKI)
    : false;
  const canExportWiki = hasExportWikiPermission?.(ComponentPrivilegeEnum.EXPORT_WIKI);
  const showWikiActions = canRefreshWiki || canExportWiki;
  
  // 点击外部区域关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (exportDropdownRef.current && !exportDropdownRef.current.contains(event.target as Node)) {
        setIsExportDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);


    // Listen for auth-error-403 events
    useEffect(() => {
      const handleAuthError = () => {
        addToast({
          type: 'error',
          title: '没有权限执行该操作',
          message: ''
        });
      };
  
      window.addEventListener('auth-error-403', handleAuthError);
  
      return () => {
        window.removeEventListener('auth-error-403', handleAuthError);
      };
    }, [addToast]);

  // Model selection state variables - 统一状态管理
  const [selectedProvider, setSelectedProvider] = useState<string>(() => {
    // 优先使用URL参数，如果没有则使用空字符串
    return providerParam || '';
  });
  const [selectedModel, setSelectedModel] = useState<string>(() => {
    // 优先使用URL参数，如果没有则使用空字符串  
    return modelParam || '';
  });
  const [isCustomSelectedModel, setIsCustomSelectedModel] = useState(isCustomModelParam);
  const [customSelectedModel, setCustomSelectedModel] = useState(customModelParam);
  const [showModelOptions, setShowModelOptions] = useState(false); // Controls whether to show model options
  const excludedDirs = searchParams.get('excluded_dirs') || '';
  const excludedFiles = searchParams.get('excluded_files') || '';
  const [modelExcludedDirs, setModelExcludedDirs] = useState(excludedDirs);
  const [modelExcludedFiles, setModelExcludedFiles] = useState(excludedFiles);

  // Wiki type state - default to comprehensive view
  const isComprehensiveParam = searchParams.get('comprehensive') !== 'false';
  const [isComprehensiveView, setIsComprehensiveView] = useState(isComprehensiveParam);
  // Using useRef for activeContentRequests to maintain a single instance across renders
  // This map tracks which pages are currently being processed to prevent duplicate requests
  // Note: In a multi-threaded environment, additional synchronization would be needed,
  // but in React's single-threaded model, this is safe as long as we set the flag before any async operations
  const activeContentRequests = useRef(new Map<string, boolean>()).current;
  const [structureRequestInProgress, setStructureRequestInProgress] = useState(false);
  // Create a flag to track if data was loaded from cache to prevent immediate re-save
  const cacheLoadedSuccessfully = useRef(false);

  // Create a flag to ensure the effect only runs once
  const effectRan = React.useRef(false);
  // State for Ask modal
  const [isJobsProgressModalOpen, setIsJobsProgressModalOpen] = useState(false); // 添加任务进度模态框状态
  const [isRefreshModalOpen, setIsRefreshModalOpen] = useState(false); // 添加刷新模态框状态
  const [showFileManager, setShowFileManager] = useState(false);

  // Get settings from context - moved up to be available for all hooks
  const { settings } = useSettings();

  // 获取认证上下文
  const { hasSandboxPermission } = useAuth();

  // Memoize repo info to avoid triggering updates in callbacks

  // Add useEffect to handle scroll reset
  useEffect(() => {
    // Scroll to top when currentPageId changes
    const wikiContent = document.getElementById('wiki-content');
    if (wikiContent) {
      wikiContent.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, [currentPageId]);

  // 创建沙盒环境的函数
  const createSandbox = useCallback(async () => {
    if (!hasSandboxPermission() || !userInfo || ! hasWikiAccessPerm) {
      console.log('用户没有沙盒权限或未登录');
      return;
    }

    // 新增：检查大模型API Key是否已通过校验
    if (!settings?.isApiKeyValid) {
      return;
    }

    try {
      // 获取仓库URL
      const gitUrl = effectiveRepoInfo.repoUrl || getRepoUrl(effectiveRepoInfo);
      if (!gitUrl) {
        console.error('无法获取仓库URL');
        return;
      }

      console.log('开始创建沙盒环境:', {
        gitUrl,
        branch: effectiveRepoInfo.branch,
        userCode: userInfo.user_code
      });

      const response = await authFetch('/api/k8s/sandbox/me/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          git_url: gitUrl,
          branch: effectiveRepoInfo.branch,
          wct_api_key: settings.apiKey,
        }),
      });

      if (response && response.ok) {
        const data = await response.json();
        if (data.success) {
          console.log('个人沙箱分配成功:', data.data);
          // addToast({
          //   type: 'success',
          //   title: '个人沙箱创建中',
          //   message: '您的开发环境正在创建中'
          // });
        } else {
          console.error('沙箱创建失败:', data.error);
          // 如果是容器数量限制的错误，显示特殊提示
          if (data.error && data.error.includes('容器数量已达上限')) {
            // addToast({
            //   type: 'warning',
            //   title: '个人沙箱创建中',
            //   message: '系统正在清理过期环境，请稍后重试'
            // });
          } else {
            // addToast({
            //   type: 'error',
            //   title: '沙盒创建失败',
            //   message: data.error || '未知错误'
            // });
          }
        }
      } else {
        console.error('沙盒创建请求失败:', response?.status);
        addToast({
          type: 'error',
          title: messages.repoPage.snadBoxFailed,
          message: messages.common.requestFail
        });
      }
    } catch (error) {
      console.error('创建沙盒时发生错误:', error);
      addToast({
        type: 'error',
        title: messages.repoPage.snadBoxFailed,
        message: error instanceof Error ? error.message : messages.common.requestFail
      });
    }
  }, [hasSandboxPermission, userInfo, effectiveRepoInfo, addToast, hasWikiAccessPerm, settings?.isApiKeyValid, settings?.apiKey, messages.common?.error, messages.common?.invalidApiKey]);

  // 在组件挂载时检查并创建沙盒
  useEffect(() => {
    if (hasSandboxPermission() && userInfo && effectiveRepoInfo) {
      // 延迟创建沙盒，避免影响页面加载
      const timer = setTimeout(() => {
        createSandbox();
      }, 2000);
      
      return () => clearTimeout(timer);
    }
  }, [hasSandboxPermission, userInfo, effectiveRepoInfo, createSandbox, hasWikiAccessPerm]);

  // Function to export wiki content
  const exportWiki = useCallback(async (format: 'markdown' | 'json') => {
    if (!wikiStructure || Object.keys(generatedPages).length === 0) {
      setExportError('No wiki content to export');
      return;
    }

    try {
      setIsExporting(true);
      setExportError(null);
      setLoadingMessage(`${language === 'ja' ? 'Wikiを' : 'Exporting wiki as '} ${format} ${language === 'ja' ? 'としてエクスポート中...' : '...'}`);

      // Prepare the pages for export
      const pagesToExport = wikiStructure.pages.map(page => {
        // Use the generated content if available, otherwise use an empty string
        const content = generatedPages[page.id]?.content || 'Content not generated';
        return {
          ...page,
          content
        };
      });

      // Get repository URL
      const repoUrl = effectiveRepoInfo.repoUrl || getRepoUrl(effectiveRepoInfo);

      // Make API call to export wiki
      const response = await authFetch(`/api/wiki/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          wiki_id: currentWikiId,
          branch: branch,
          repo_url: repoUrl,
          type: effectiveRepoInfo.type,
          pages: pagesToExport,
          sections: wikiStructure.sections,
          rootSections: wikiStructure.rootSections,
          format
        })
      });

      if (response && !response.ok) {
        const errorText = await response.text().catch(() => 'No error details available');
        throw new Error(`Error exporting wiki: ${response.status} - ${errorText}`);
      }

      // Get the filename from the Content-Disposition header if available
      const contentDisposition = response?.headers.get('Content-Disposition');
      let filename = `${effectiveRepoInfo.repo}_wiki.${format === 'markdown' ? 'md' : 'json'}`;

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename=(.+)/);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/"/g, '');
        }
      }

      // Convert the response to a blob and download it
      const blob = await response?.blob();
      if (!blob) {
        throw new Error('Failed to create blob');
      }
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

    } catch (err) {
      console.error('Error exporting wiki:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error during export';
      setExportError(errorMessage);
    } finally {
      setIsExporting(false);
      setLoadingMessage(undefined);
    }
  }, [wikiStructure, generatedPages, effectiveRepoInfo, language, currentWikiId, branch]);

  // Handle refresh confirmation from modal
  const handleRefreshConfirm = useCallback(async (
    modelSettings: ModelSettings,
    options: {
      forceRefresh: boolean;
      refreshPages?: string[];
      rebuildStructure: boolean;
      customInstructions?: string;
    },
  ) => {
    const { forceRefresh, refreshPages, rebuildStructure, customInstructions } = options;
    if (!currentWikiId) {
      addToast({
        type: 'error',
        title: messages.repoPage.wikiNotFound,
        message: messages.repoPage.cannotRefreshWiki
      });
      return;
    }

    try {
      // Prepare refresh request using wiki_id only
      const cleanedWhaleDevCloudToken = settings.whaleDevCloudToken.replace(/●/g, '');
      const refreshRequest = {
        token: cleanedWhaleDevCloudToken,
        wiki_id: currentWikiId,
        model_settings: modelSettings,
        comprehensive: isComprehensiveView,
        force_refresh: forceRefresh,
        refresh_pages: refreshPages,
        rebuild_structure: rebuildStructure,
        custom_instructions: customInstructions,
      };

      // Call refresh API
      const response = await authFetch('/api/wiki/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(refreshRequest)
      });

      if (response && response.ok) {
        const result = await response.json();

        if (result.status_code === 403) {
          addToast({
            type: 'error',
            title: messages.repoPage.tokenIsNotValid,
            message: result.detail
          });
        }
        
        if (result.refresh) {
          // 根据模式确定提示语
          let message = 'Wiki 刷新任务已在后台运行';
          if (refreshPages && refreshPages.length > 0) {
            message = `已在后台刷新所选的 ${refreshPages.length} 个页面`;
          } else if (forceRefresh) {
            message = '已在后台强制刷新所有页面';
          } else if (rebuildStructure) {
            message = '已在后台重建章节结构并刷新内容';
          }

          addToast({
            type: 'success',
            title: messages.repoPage.refreshStarted,
            message,
          });
          
          // Show the jobs progress modal immediately
          setIsJobsProgressModalOpen(true);
        } else if (result.exists) {
          addToast({
            type: 'info',
            title: messages.repoPage.taskAlreadyRunning,
            message: result.message
          });
          
          // Still show the progress modal to track existing task
          setIsJobsProgressModalOpen(true);
        }
      } else {
        const errorData = await response?.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to start refresh');
      }
    } catch (error) {
      console.error('Error refreshing wiki:', error);
      addToast({
        type: 'error',
        title: messages.repoPage.refreshFailed,
        message: error instanceof Error ? error.message : messages.common.requestFail
      });
    }
  }, [currentWikiId, isComprehensiveView, addToast, settings.whaleDevCloudToken]);

  // Handle refresh wiki - open modal instead of direct refresh
  const handleRefreshWiki = useCallback(() => {
    if (!wikiStructure || !currentWikiId) {
      addToast({
        type: 'error',
        title: messages.repoPage.wikiNotFound,
        message: messages.repoPage.cannotRefreshWikiWithNoExistingWiki
      });
      return;
    }

    setIsRefreshModalOpen(true);
  }, [wikiStructure, currentWikiId, addToast]);


  const isMountedRef = useRef(true);
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);


  const confirmRefresh = useCallback(async (newToken?: string) => {
    setShowModelOptions(false);
    setLoadingMessage(messages.loading?.clearingCache || 'Clearing server cache...');
    setIsLoading(true); // Show loading indicator immediately

    try {
      // 只发送删除接口需要的参数
      const params = new URLSearchParams({
        owner: effectiveRepoInfo.owner,
        repo: effectiveRepoInfo.repo,
        branch: effectiveRepoInfo.branch,
        repo_type: effectiveRepoInfo.type,
        language: language,
      });

      const response = await authFetch(`/api/wiki_cache?${params.toString()}`, {
        method: 'DELETE',
        headers: {
          'Accept': 'application/json',
        }
      });

      if (response) {
        if (response.ok) {
          console.log('Server-side wiki cache cleared successfully.');
          // Optionally, show a success message for cache clearing if desired
          // setLoadingMessage('Cache cleared. Refreshing wiki...');
        } else {
          const errorText = await response.text();
          console.warn(`Failed to clear server-side wiki cache (status: ${response.status}): ${errorText}. Proceeding with refresh anyway.`);
          // Optionally, inform the user about the cache clear failure but that refresh will still attempt
          // setError(\`Cache clear failed: ${errorText}. Trying to refresh...\`);
          if(response.status == 401) {
            setIsLoading(false);
            setLoadingMessage(undefined);
            setError('Failed to validate the authorization code');
            console.error('Failed to validate the authorization code')
            return;
          }
        }
      }
    } catch (err) {
      console.warn('Error calling DELETE /api/wiki_cache:', err);
      setIsLoading(false);
      // Optionally, inform the user about the cache clear error
      // setError(\`Error clearing cache: ${err instanceof Error ? err.message : String(err)}. Trying to refresh...\`);
      throw err;
    }

    // Update token if provided
    if (newToken) {
      // Update current token state
      setCurrentToken(newToken);
      // Update the URL parameters to include the new token
      const currentUrl = new URL(window.location.href);
      currentUrl.searchParams.set('token', newToken);
      window.history.replaceState({}, '', currentUrl.toString());
    }

    // Proceed with the rest of the refresh logic
    console.log('Refreshing wiki. Server cache will be overwritten upon new generation if not cleared.');

    // Clear the localStorage cache (if any remnants or if it was used before this change)
    const localStorageCacheKey = getCacheKey(effectiveRepoInfo.owner, effectiveRepoInfo.repo, effectiveRepoInfo.type, language, isComprehensiveView);
    localStorage.removeItem(localStorageCacheKey);

    // Reset cache loaded flag
    cacheLoadedSuccessfully.current = false;
    effectRan.current = false; // Allow the main data loading useEffect to run again

    // Reset all state
    setWikiStructure(undefined);
    setCurrentPageId(undefined);
    setGeneratedPages({});
    setPagesInProgress(new Set());
    setError(null);
    setIsLoading(true); // Set loading state for refresh
    setLoadingMessage(messages.loading?.initializing || 'Initializing wiki generation...');

    // Clear any in-progress requests for page content
    activeContentRequests.clear();
    // Reset flags related to request processing if they are component-wide
    setStructureRequestInProgress(false); // Assuming this flag should be reset
    setRequestInProgress(false); // Assuming this flag should be reset

    // Explicitly trigger the data loading process again by re-invoking what the main useEffect does.
    // This will first attempt to load from (now hopefully non-existent or soon-to-be-overwritten) server cache,
    // then proceed to fetchRepositoryStructure if needed.
    // To ensure fetchRepositoryStructure is called if cache is somehow still there or to force a full refresh:
    // One option is to directly call fetchRepositoryStructure() if force refresh means bypassing cache check.
    // For now, we rely on the standard loadData flow initiated by resetting effectRan and dependencies.
    // This will re-trigger the main data loading useEffect.
    // No direct call to fetchRepositoryStructure here, let the useEffect handle it based on effectRan.current = false.
  }, [effectiveRepoInfo, language, messages.loading, activeContentRequests, isComprehensiveView]);

  // Start wiki generation when component mounts
  useEffect(() => {
    if (effectRan.current === false) {
      effectRan.current = true; // Set to true immediately to prevent re-entry due to StrictMode

      const loadData = async () => {
        // Try loading from server-side cache first
        setLoadingMessage(messages.loading?.fetchingCache || 'Checking for cached wiki...');
        try {
          const params = new URLSearchParams({
            owner: effectiveRepoInfo.owner,
            repo: effectiveRepoInfo.repo,
            repo_type: effectiveRepoInfo.type,
            branch: effectiveRepoInfo.branch,
            wiki_id: effectiveRepoInfo.wiki_id,
            language: language,
            comprehensive: isComprehensiveView.toString(),
          });
          const response = await authFetch(`/api/wiki_cache?${params.toString()}`);

          if (response && response.ok) {
            const cachedData = await response.json(); // Returns null if no cache
            if (cachedData?.priv_status) {
              addToast({
                type: 'error',
                title: messages.repoPage.noPermissionToViewWiki,
                message: '',
                duration: 2000,
              });
              setWikiAccessPerm(false);
              setTimeout(() => home(), 2000); // 延迟3秒跳转
            }

            if (cachedData) {
              setWikiAccessPerm(true);
              console.log('Using server-cached wiki data');

              setSelectedProvider(cachedData.provider);
              setSelectedModel(cachedData.model);
              // Update repoInfo
              setEffectiveRepoInfo(cachedData.repo);
              // 保存 ownerId 到 state
              if (cachedData && cachedData.ownerId) {
                setOwnerId(cachedData.ownerId);
              }
              // Save wiki_id for refresh functionality
              if (cachedData.wiki_id) {
                setCurrentWikiId(cachedData.wiki_id);
                console.log('Saved wiki_id:', cachedData.wiki_id);
              }

              // Ensure the cached structure has sections and rootSections
              const cachedStructure = {
                ...cachedData.wiki_structure,
                sections: cachedData.wiki_structure.sections || [],
                rootSections: cachedData.wiki_structure.rootSections || []
              };

              // If sections or rootSections are missing, create intelligent ones based on page titles
              if (!cachedStructure.sections.length || !cachedStructure.rootSections.length) {
                const pages = cachedStructure.pages;
                const sections: WikiSection[] = [];
                const rootSections: string[] = [];

                // Group pages by common prefixes or categories
                const pageClusters = new Map<string, WikiPage[]>();

                // Define common categories that might appear in page titles
                const categories = [
                  { id: 'overview', title: 'Overview', keywords: ['overview', 'introduction', 'about'] },
                  { id: 'architecture', title: 'Architecture', keywords: ['architecture', 'structure', 'design', 'system'] },
                  { id: 'features', title: 'Core Features', keywords: ['feature', 'functionality', 'core'] },
                  { id: 'components', title: 'Components', keywords: ['component', 'module', 'widget'] },
                  { id: 'api', title: 'API', keywords: ['api', 'endpoint', 'service', 'server'] },
                  { id: 'data', title: 'Data Flow', keywords: ['data', 'flow', 'pipeline', 'storage'] },
                  { id: 'models', title: 'Models', keywords: ['model', 'ai', 'ml', 'integration'] },
                  { id: 'ui', title: 'User Interface', keywords: ['ui', 'interface', 'frontend', 'page'] },
                  { id: 'setup', title: 'Setup & Configuration', keywords: ['setup', 'config', 'installation', 'deploy'] }
                ];

                // Initialize clusters with empty arrays
                categories.forEach(category => {
                  pageClusters.set(category.id, []);
                });

                // Add an "Other" category for pages that don't match any category
                pageClusters.set('other', []);

                // Assign pages to categories based on title keywords
                pages.forEach((page: WikiPage) => {
                  const title = page.title.toLowerCase();
                  let assigned = false;

                  // Try to find a matching category
                  for (const category of categories) {
                    if (category.keywords.some(keyword => title.includes(keyword))) {
                      pageClusters.get(category.id)?.push(page);
                      assigned = true;
                      break;
                    }
                  }

                  // If no category matched, put in "Other"
                  if (!assigned) {
                    pageClusters.get('other')?.push(page);
                  }
                });

                // Create sections for non-empty categories
                for (const [categoryId, categoryPages] of pageClusters.entries()) {
                  if (categoryPages.length > 0) {
                    const category = categories.find(c => c.id === categoryId) ||
                                    { id: categoryId, title: categoryId === 'other' ? 'Other' : categoryId.charAt(0).toUpperCase() + categoryId.slice(1) };

                    const sectionId = `section-${categoryId}`;
                    sections.push({
                      id: sectionId,
                      title: category.title,
                      pages: categoryPages.map((p: WikiPage) => p.id)
                    });
                    rootSections.push(sectionId);

                    // Update page parentId
                    categoryPages.forEach((page: WikiPage) => {
                      page.parentId = sectionId;
                    });
                  }
                }

                // If we still have no sections (unlikely), fall back to importance-based grouping
                if (sections.length === 0) {
                  const highImportancePages = pages.filter((p: WikiPage) => p.importance === 'high').map((p: WikiPage) => p.id);
                  const mediumImportancePages = pages.filter((p: WikiPage) => p.importance === 'medium').map((p: WikiPage) => p.id);
                  const lowImportancePages = pages.filter((p: WikiPage) => p.importance === 'low').map((p: WikiPage) => p.id);

                  if (highImportancePages.length > 0) {
                    sections.push({
                      id: 'section-high',
                      title: 'Core Components',
                      pages: highImportancePages
                    });
                    rootSections.push('section-high');
                  }

                  if (mediumImportancePages.length > 0) {
                    sections.push({
                      id: 'section-medium',
                      title: 'Key Features',
                      pages: mediumImportancePages
                    });
                    rootSections.push('section-medium');
                  }

                  if (lowImportancePages.length > 0) {
                    sections.push({
                      id: 'section-low',
                      title: 'Additional Information',
                      pages: lowImportancePages
                    });
                    rootSections.push('section-low');
                  }
                }

                cachedStructure.sections = sections;
                cachedStructure.rootSections = rootSections;
              }

              setWikiStructure(cachedStructure);
              setGeneratedPages(cachedData.generated_pages);
              setCurrentPageId(cachedStructure.pages.length > 0 ? cachedStructure.pages[0].id : undefined);
              setIsLoading(false);
              setLoadingMessage(undefined);
              cacheLoadedSuccessfully.current = true;
              return; // Exit if cache is successfully loaded
            } else {
              console.log('No valid wiki data in server cache or cache is empty.');
              setIsLoading(false);
              setError(messages.repoPage?.wikiNotFound || "No wiki cache found for this repository.");
            }
          } else {
             setWikiAccessPerm(false);
            // Log error and show error message
            console.error('Error fetching wiki cache from server:', response?.status, await response?.text());
            setIsLoading(false);
            setError(messages.repoPage?.wikiNotFound || "No wiki cache found for this repository.");
          }
        } catch (error) {
          console.error('Error loading from server cache:', error);
          setIsLoading(false);
          setError(error instanceof Error ? error.message : "An unknown error occurred");
        }
      };

      loadData();

    } else {
      console.log('Skipping duplicate repository fetch/cache check');
    }
  }, [effectiveRepoInfo, language, messages.repoPage?.wikiNotFound, messages.loading?.fetchingCache, isComprehensiveView, addToast]);

  // Save wiki to server-side cache when generation is complete
  useEffect(() => {
    const saveCache = async () => {
      if (!isLoading &&
          !error &&
          wikiStructure &&
          Object.keys(generatedPages).length > 0 &&
          Object.keys(generatedPages).length >= wikiStructure.pages.length &&
          !cacheLoadedSuccessfully.current) {

        const allPagesHaveContent = wikiStructure.pages.every(page =>
          generatedPages[page.id] && generatedPages[page.id].content && generatedPages[page.id].content !== 'Loading...');

        if (allPagesHaveContent) {
          console.log('Attempting to save wiki data to server cache via Next.js proxy');

          try {
            // Make sure wikiStructure has sections and rootSections
            const structureToCache = {
              ...wikiStructure,
              sections: wikiStructure.sections || [],
              rootSections: wikiStructure.rootSections || []
            };
            const dataToCache = {
              repo: effectiveRepoInfo.repo,
              owner: effectiveRepoInfo.owner,
              branch: effectiveRepoInfo.branch,
              language: language,
              comprehensive: isComprehensiveView,
              wiki_structure: structureToCache,
              generated_pages: generatedPages,
              provider: selectedProvider,
              model: selectedModel
            };
            const response = await authFetch(`/api/wiki_cache`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(dataToCache),
            });

            if (response && response.ok) {
              console.log('Wiki data successfully saved to server cache');
            } else {
              console.error('Error saving wiki data to server cache:', response?.status, await response?.text());
            }
          } catch (error) {
            console.error('Error saving to server cache:', error);
          }
        }
      }
    };

    saveCache();
  }, [isLoading, error, wikiStructure, generatedPages, effectiveRepoInfo, selectedProvider, selectedModel, language, isComprehensiveView, settings.apiKey]);

  const handlePageSelect = (pageId: string) => {
    if (currentPageId != pageId) {
      setCurrentPageId(pageId)
    }
  };
  
  // 新增：模型相关状态
  const [providers, setProviders] = useState<Provider[]>([]);
  const [availableModels, setAvailableModels] = useState<Model[]>([]);
  const [modelType, setModelType] = useState<'whalecloud' | 'gemini-cli'>('gemini-cli');
  const [deepResearch, setDeepResearch] = useState(false);
  const [isModelsLoaded, setIsModelsLoaded] = useState(false);

  // 新增：获取模型数据的函数
  const fetchModels = useCallback(async () => {
    if(!hasWikiAccessPerm) {
      return;
    }
    try {
      const response = await authFetch('/api/config/models');
      if (response && response.ok) {
        const data = await response.json();
        setProviders(data.providers);
        
        // 使用配置文件中的默认provider
        const defaultProvider = data.defaultProvider || 'gemini-cli';
        const defaultProviderConfig = data.providers.find((p: Provider) => p.id === defaultProvider);
        if (defaultProviderConfig) {
          setSelectedProvider(defaultProvider);
          setAvailableModels(defaultProviderConfig.models);
          if (defaultProviderConfig.models.length > 0) {
            setSelectedModel(defaultProviderConfig.models[0].id);
          }
        }
      }
    } catch (err) {
      console.error('Failed to fetch models:', err);
    } finally {
      setIsModelsLoaded(true);
    }
  }, [hasWikiAccessPerm]);

  // 新增：在组件挂载时获取模型数据
  useEffect(() => {
    fetchModels();
  }, [fetchModels, hasWikiAccessPerm]);

  const handleInitialQuestionSubmit = (question: string, modelType: 'whalecloud' | 'gemini-cli', selectedProvider?: string, selectedModel?: string, fileReferences?: unknown[]) => {
    // 新增：检查大模型API Key是否已通过校验
    if (!settings?.isApiKeyValid) {
      addToast({
        type: 'error',
        title: messages.common?.error || 'Error',
        message: messages.common?.invalidApiKey || 'Large Model Token invalid. Please configure it in Settings.'
      });
      return;
    }

    const sessionId = uuidv4();
    
    console.log('=== handleInitialQuestionSubmit 开始 ===', {
      modelType,
      selectedProvider,
      selectedModel,
      sessionId,
      fileReferences,
      fileReferencesLength: fileReferences?.length || 0
    });
    
    // 存储模型和提供商信息到localStorage
    const modelKey = `model_${sessionId}`;
    const providerKey = `provider_${sessionId}`;
    
    console.log('=== 存储到localStorage ===', {
      modelKey,
      providerKey,
      selectedModel,
    });
  
    // 存储仓库和配置参数到localStorage
    const paramsKey = `params_${sessionId}`;
    const params = {
      repoUrl: effectiveRepoInfo.repoUrl,
      question: question,
      owner: effectiveRepoInfo.owner,
      repo: effectiveRepoInfo.repo,
      type: effectiveRepoInfo.type,
      branch: effectiveRepoInfo.branch,
      provider: modelType,
      model: selectedModel,
      deepResearch: deepResearch,
      token: effectiveRepoInfo.token || '',
      wikiId: currentWikiId, // 优先使用wiki_id参数用于API调用
      fileReferences: fileReferences || [] // 添加文件引用参数
    };
    localStorage.setItem(paramsKey, JSON.stringify(params));
    
    console.log('最终存储到localStorage的完整数据:', {
      question: question,
      model: selectedModel,
      provider: modelType,
      params: params,
      sessionId: sessionId,
      fileReferences: fileReferences || []
    });
    
    // 验证localStorage中的数据
    const storedParams = localStorage.getItem(paramsKey);
    
    console.log('=== 验证localStorage存储结果 ===', {
      storedParams: storedParams ? JSON.parse(storedParams) : null
    });
    
    // 只保留sessionId在URL中
    const url = `/search/${sessionId}`;
    console.log('即将打开URL:', url);
    window.open(url, '_blank');
    
    console.log('=== handleInitialQuestionSubmit 结束 ===');
  };

  // Function to save wiki content as markdown
  const saveAsMarkdown = () => {
    // ... existing code ...
  };


  return (
    <div className="flex flex-col h-screen bg-gray-50 dark:bg-gray-900">
      <style>{wikiStyles}</style>
      <Header onOpenSettings={() => setIsSettingsModalOpen(true)} onOpenJobs={() => setIsJobsProgressModalOpen(true)} />

      <main className="flex-1 w-full mx-auto py-6 pb-0 overflow-y-auto">
        {isLoading ? (
          <div className="flex flex-col items-center justify-center p-8 bg-[var(--card-bg)] rounded-lg shadow-custom card-elegant max-w-screen-xl mx-auto">
            <div className="relative mb-6">
              <div className="absolute -inset-4 bg-[var(--accent-primary)]/10 rounded-full blur-md animate-pulse"></div>
              <div className="relative flex items-center justify-center">
                <div className="w-3 h-3 bg-[var(--accent-primary)]/70 rounded-full animate-pulse"></div>
                <div className="w-3 h-3 bg-[var(--accent-primary)]/70 rounded-full animate-pulse delay-75 mx-2"></div>
                <div className="w-3 h-3 bg-[var(--accent-primary)]/70 rounded-full animate-pulse delay-150"></div>
              </div>
            </div>
            <p className="text-[var(--foreground)] text-center mb-3 font-serif">
              {loadingMessage || messages.common?.loading || 'Loading...'}
              {isExporting && (messages.loading?.preparingDownload || ' Please wait while we prepare your download...')}
            </p>

            {/* Progress bar for page generation */}
            {wikiStructure && (
              <div className="w-full max-w-md mt-3">
                <div className="bg-[var(--background)]/50 rounded-full h-2 mb-3 overflow-hidden border border-[var(--border-color)]">
                  <div
                    className="bg-[var(--accent-primary)] h-2 rounded-full transition-all duration-300 ease-in-out"
                    style={{
                      width: `${Math.max(5, 100 * (wikiStructure.pages.length - pagesInProgress.size) / wikiStructure.pages.length)}%`
                    }}
                  />
                </div>
                <p className="text-xs text-[var(--muted)] text-center">
                  {language === 'ja'
                    ? `${wikiStructure.pages.length}ページ中${wikiStructure.pages.length - pagesInProgress.size}ページ完了`
                    : messages.repoPage?.pagesCompleted
                        ? messages.repoPage.pagesCompleted
                            .replace('{completed}', (wikiStructure.pages.length - pagesInProgress.size).toString())
                            .replace('{total}', wikiStructure.pages.length.toString())
                        : `${wikiStructure.pages.length - pagesInProgress.size} of ${wikiStructure.pages.length} pages completed`}
                </p>

                {/* Show list of in-progress pages */}
                {pagesInProgress.size > 0 && (
                  <div className="mt-4 text-xs">
                    <p className="text-[var(--muted)] mb-2">
                      {messages.repoPage?.currentlyProcessing || 'Currently processing:'}
                    </p>
                    <ul className="text-[var(--foreground)] space-y-1">
                      {Array.from(pagesInProgress).slice(0, 3).map(pageId => {
                        const page = wikiStructure.pages.find(p => p.id === pageId);
                        return page ? <li key={pageId} className="truncate border-l-2 border-[var(--accent-primary)]/30 pl-2">{page.title}</li> : null;
                      })}
                      {pagesInProgress.size > 3 && (
                        <li className="text-[var(--muted)]">
                          {language === 'ja'
                            ? `...他に${pagesInProgress.size - 3}ページ`
                            : messages.repoPage?.andMorePages
                                ? messages.repoPage.andMorePages.replace('{count}', (pagesInProgress.size - 3).toString())
                                : `...and ${pagesInProgress.size - 3} more`}
                        </li>
                      )}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </div>
        ) : error ? (
          <div className="flex flex-col items-center w-full">
            {/* 友好的提示信息 */}
            <div className="bg-[var(--card-bg)] rounded-lg p-8 mb-6 shadow-sm max-w-screen-xl mx-auto w-full text-center">
              <div className="flex justify-center mb-4">
                <div className="relative">
                  <div className="absolute -inset-2 bg-[var(--accent-primary)]/10 rounded-full blur-md"></div>
                  <FaBook className="text-3xl text-[var(--accent-primary)] relative z-10" />
                </div>
              </div>
              <h3 className="text-xl font-bold text-[var(--foreground)] mb-2 font-serif">
                {messages.repoPage?.generatingWiki || '你的wiki页面正在生成哦~'}
              </h3>
              <div className="text-[var(--muted)] mb-6 max-w-2xl mx-auto">
                {messages.repoPage?.wikiGenerationInProgress || '请耐心等候，这通常需要数十分钟...可以在右上角查看进度~'}
                <br />
                {messages.repoPage?.wikiGenerationInProgressWithStyle ? (
                  <div dangerouslySetInnerHTML={{ __html: messages.repoPage.wikiGenerationInProgressWithStyle }} />
                ) : (
                  <>
                    等待期间，<span className="font-bold text-green-600">您可先用下方输入框提问，体验代码问答功能</span>。Wiki 生成后即可使用全部功能
                  </>
                )}
              </div>
              <div className="flex justify-center mb-6">
                <div className="w-12 h-12 border-4 border-[var(--accent-primary)] border-t-transparent rounded-full animate-spin"></div>
              </div>
            </div>

            {/* AI问答模块 */}
            <div className="max-w-screen-xl mx-auto w-full">
              <ChatInputAdapter 
                wikiInfoId={currentWikiId || undefined}
                wikiStructure={wikiStructure}
                onSubmit={handleInitialQuestionSubmit} 
                providers={providers}
                availableModels={availableModels}
                selectedProvider={selectedProvider}
                setSelectedProvider={setSelectedProvider}
                selectedModel={selectedModel}
                setSelectedModel={setSelectedModel}
                modelType={modelType}
                setModelType={setModelType}
                deepResearch={deepResearch}
                setDeepResearch={setDeepResearch}
                useFixedPosition={false}
              />
            </div>
          </div>
        ) : wikiStructure ? (
          <div className="container mx-auto grid grid-cols-1 lg:grid-cols-[minmax(250px,280px)_1fr] xl:grid-cols-[minmax(280px,320px)_1fr_minmax(250px,280px)] gap-6 max-h-[calc(100vh-120px)] min-w-0">
            {/* Left Column: Wiki Navigation */}
            <div className="max-h-[calc(100vh-120px)] bg-[var(--card-bg)] rounded-lg shadow-sm border border-[var(--border-color)] p-5 min-w-0 flex flex-col">
              <h3 className="text-lg font-bold text-[var(--foreground)] mb-3 font-serif">{wikiStructure.title}</h3>
              <p className="text-[var(--muted)] text-sm mb-5 leading-relaxed">{wikiStructure.description}</p>
              
              <div className="text-xs text-[var(--muted)] mb-5 flex items-center">
                {effectiveRepoInfo.type === 'local' ? (
                  <div className="flex items-center">
                    <FaFolder className="mr-2" />
                    <span className="break-all">{effectiveRepoInfo.localPath}</span>
                  </div>
                ) : (
                  <>
                    {effectiveRepoInfo.type === 'github' ? (
                      <FaGithub className="mr-2" />
                    ) : effectiveRepoInfo.type === 'gitlab' ? (
                      <FaGitlab className="mr-2" />
                    ) : (
                      <FaBitbucket className="mr-2" />
                    )}
                    <a
                      href={
                        effectiveRepoInfo.repoUrl?.endsWith('.git')
                          ? effectiveRepoInfo.repoUrl.slice(0, -4)
                          : effectiveRepoInfo.repoUrl ?? ''
                      }
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:text-[var(--accent-primary)] transition-colors border-b border-[var(--border-color)] hover:border-[var(--accent-primary)]"
                    >
                      {effectiveRepoInfo.owner}/{effectiveRepoInfo.repo}/{effectiveRepoInfo.branch}
                    </a>
                  </>
                )}
              </div>

              <h4 className="text-md font-semibold text-[var(--foreground)] mb-3 font-serif">
                {messages.repoPage?.pages || 'Pages'}
              </h4>
              <div className="flex-1 overflow-y-auto pr-1 left-column-scrollbar">
                <WikiTreeView
                  wikiStructure={wikiStructure}
                  currentPageId={currentPageId}
                  onPageSelect={handlePageSelect}
                  messages={messages.repoPage}
                />
              </div>
            </div>

            {/* Center Column: Wiki Content */}
            <div id="wiki-content" className="max-h-[calc(100vh-120px)] bg-[var(--card-bg)] rounded-lg shadow-sm border border-[var(--border-color)] p-6 lg:p-8 overflow-y-auto min-w-0" style={{ paddingBottom: 188 }}>
              {currentPageId && generatedPages[currentPageId] ? (
                <div className="prose prose-sm md:prose-base lg:prose-lg max-w-none">
                  <Markdown content={generatedPages[currentPageId].content} branch={effectiveRepoInfo.branch} repo_url={effectiveRepoInfo.repoUrl || ''} />
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center p-8 text-[var(--muted)] h-full">
                  <div className="relative mb-4">
                    <div className="absolute -inset-2 bg-[var(--accent-primary)]/5 rounded-full blur-md"></div>
                    <FaBookOpen className="text-4xl relative z-10" />
                  </div>
                  <p className="font-serif">
                    {messages.repoPage?.selectPagePrompt || 'Select a page from the navigation to view its content'}
                  </p>
                </div>
              )}
            </div>

            {/* Right Column: Actions and Table of Contents */}
            <aside className="hidden xl:block w-full flex-shrink-0 min-w-0">
              <div className="space-y-4">

                {/* Wiki Actions Section */}
                {/* 权限控制：只有有刷新或导出权限时才渲染 Wiki Actions */}
                {showWikiActions && (
                  <div className="p-4 bg-[var(--card-bg)] rounded-lg shadow-sm border border-[var(--border-color)] space-y-3">
                    <h3 className="font-semibold text-sm">{messages.repoPage?.wikiActions || 'Wiki Actions'}</h3>
                    
                    {/* 刷新按钮 */}
                    {canRefreshWiki && (
                      <button
                        onClick={handleRefreshWiki}
                        disabled={isLoading}
                        className="w-full flex items-center justify-center text-xs px-3 py-2 bg-[var(--background)] text-[var(--foreground)] rounded-md hover:bg-[var(--background)]/80 disabled:opacity-50 disabled:cursor-not-allowed border border-[var(--border-color)] transition-colors hover:cursor-pointer"
                      >
                        <FaUpload className={`mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                        {messages.repoPage?.refreshWiki || 'Refresh Wiki'}
                      </button>
                    )}

                    {/* 导出按钮/下拉，仅有导出权限时显示 */}
                    {canExportWiki && Object.keys(generatedPages).length > 0 && (
                      <div className="relative" ref={exportDropdownRef}>
                        <button
                          onClick={() => setIsExportDropdownOpen(!isExportDropdownOpen)}
                          className="w-full flex items-center justify-center text-xs px-3 py-2 bg-[var(--background)] text-[var(--foreground)] rounded-md hover:bg-[var(--background)]/80 disabled:opacity-50 disabled:cursor-not-allowed border border-[var(--border-color)] transition-colors"
                        >
                          <FaFileExport className="mr-2" />
                          {messages.repoPage?.exportWiki || 'Export'}
                        </button>
                        <div className={`absolute right-0 mt-1 w-full bg-[var(--card-bg)] rounded-md shadow-lg border border-[var(--border-color)] z-10 ${isExportDropdownOpen ? 'block' : 'hidden'}`}>
                          <button
                            onClick={() => {
                              exportWiki('markdown');
                              setIsExportDropdownOpen(false);
                            }}
                            disabled={isExporting}
                            className="w-full text-left px-4 py-2 text-xs text-[var(--foreground)] hover:bg-[var(--background)]/50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                          >
                            <FaDownload className="mr-2" />
                            {messages.repoPage?.exportAsMarkdown || 'Export as Markdown'}
                          </button>
                          <button
                            onClick={() => {
                              exportWiki('json');
                              setIsExportDropdownOpen(false);
                            }}
                            disabled={isExporting}
                            className="w-full text-left px-4 py-2 text-xs text-[var(--foreground)] hover:bg-[var(--background)]/50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                          >
                            <FaFileExport className="mr-2" />
                            {messages.repoPage?.exportAsJson || 'Export as JSON'}
                          </button>
                        </div>
                      </div>
                    )}
                    {exportError && (
                      <div className="text-xs text-red-500 p-2 bg-red-500/10 rounded-md">
                        {exportError}
                      </div>
                    )}
                  </div>
                )}
                
                {/* Document Structure Section - Commented out */}
                {/* {wikiStructure && currentPageId && generatedPages[currentPageId] && (
                  <div className="p-4 bg-[var(--card-bg)] rounded-lg shadow-sm border border-[var(--border-color)]">
                    <h3 className="font-semibold text-sm mb-3">Document Structure</h3>
                    <div className="max-h-[calc(100vh-330px)] overflow-y-auto pr-1">
                      <TableOfContents content={generatedPages[currentPageId].content} />
                    </div>
                  </div>
                )} */}

                                {/* Chat History Section */}
                <ChatHistory currentWikiId={currentWikiId} userInfo={userInfo} />
              </div>
            </aside>
          </div>
        ) : null}
      </main>

      {/* ChatInputAdapter - 在正常状态下显示，使用固定定位 */}
      {!error && (
        <ChatInputAdapter 
          wikiInfoId={currentWikiId || undefined}
          wikiStructure={wikiStructure}
          onSubmit={handleInitialQuestionSubmit} 
          providers={providers}
          availableModels={availableModels}
          selectedProvider={selectedProvider}
          setSelectedProvider={setSelectedProvider}
          selectedModel={selectedModel}
          setSelectedModel={setSelectedModel}
          modelType={modelType}
          setModelType={setModelType}
          deepResearch={deepResearch}
          setDeepResearch={setDeepResearch}
          useFixedPosition={true}
          repoUrl={effectiveRepoInfo?.repoUrl || ''}
          branch={effectiveRepoInfo?.branch || 'main'}
          userCode={userInfo?.user_code || ''}
          onFileManagerOpen={() => setShowFileManager(true)}
        />
      )}

      {/* Model Selection Modal */}
      {/* {isModelSelectionModalOpen && (
        <ModelSelectionModal
          isOpen={isModelSelectionModalOpen}
          onClose={() => setIsModelSelectionModalOpen(false)}
          provider={selectedProvider}
          setProvider={setSelectedProvider}
          model={selectedModel}
          setModel={setSelectedModel}
          isCustomModel={isCustomSelectedModel}
          setIsCustomModel={setIsCustomSelectedModel}
          customModel={customSelectedModel}
          setCustomModel={setCustomSelectedModel}
          isComprehensiveView={isComprehensiveView}
          setIsComprehensiveView={setIsComprehensiveView}
          showFileFilters={true}
          excludedDirs={modelExcludedDirs}
          setExcludedDirs={setModelExcludedDirs}
          excludedFiles={modelExcludedFiles}
          setExcludedFiles={setModelExcludedFiles}
          onApply={confirmRefresh}
          showWikiType={true}
          showTokenInput={effectiveRepoInfo.type !== 'local' && !currentToken} // Show token input if not local and no current token
          repositoryType={effectiveRepoInfo.type as 'whaleDevCloud' | 'gitlab' | 'bitbucket' | 'github'}
        />
      )} */}
      <SettingsModal isOpen={isSettingsModalOpen} onClose={() => setIsSettingsModalOpen(false)} />
      <JobsProgressModal isOpen={isJobsProgressModalOpen} onClose={() => setIsJobsProgressModalOpen(false)} />
      <RefreshModal 
        isOpen={isRefreshModalOpen}
        onClose={() => setIsRefreshModalOpen(false)}
        onConfirm={handleRefreshConfirm}
        currentModel={selectedModel}
        apiKey={settings.apiKey || ''}
        sections={refreshableSections}
      />

      <FileManager
        isOpen={showFileManager}
        onClose={() => setShowFileManager(false)}
        repoUrl={effectiveRepoInfo?.repoUrl || ''}
        branch={effectiveRepoInfo?.branch || 'main'}
        userCode={userInfo?.user_code || ''}
      />
    </div>
  );
}
