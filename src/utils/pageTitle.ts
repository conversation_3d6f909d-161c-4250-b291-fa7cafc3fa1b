import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';

/**
 * 自定义 Hook：处理页面标题设置
 * 优先使用 sessionStorage 传递的标题，否则使用默认标题
 * @param defaultTitle 可选的默认标题
 */
export function usePageTitle(defaultTitle?: string) {
  const searchParams = useSearchParams();
  
  useEffect(() => {
    const finalTitle = defaultTitle;
    
    // // 优先检查 sessionStorage 中的标题（方案三）
    // try {
    //   const sessionTitle = sessionStorage.getItem('nextPageTitle');
    //   if (sessionTitle) {
    //     finalTitle = sessionTitle;
    //     // 读取后清除存储，避免影响其他页面
    //     sessionStorage.removeItem('nextPageTitle');
    //   }
    // } catch (error) {
    //   console.warn('Failed to read title from sessionStorage:', error);
    // }
    
    // // 备用：检查 URL 参数中的直接标题（向后兼容）
    // if (!finalTitle || finalTitle === defaultTitle) {
    //   const pageTitle = searchParams.get('pageTitle');
    //   if (pageTitle) {
    //     try {
    //       finalTitle = decodeURIComponent(pageTitle);
    //     } catch (error) {
    //       console.warn('Failed to decode page title from URL:', error);
    //     }
    //   }
    // }
    
    // 设置最终标题
    if (finalTitle) {
      document.title = finalTitle;
    }
  }, [searchParams, defaultTitle]);
}
