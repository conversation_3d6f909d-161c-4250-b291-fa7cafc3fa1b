/**
 * 统一的配置管理
 * 用于管理前端应用的所有环境变量和配置项
 */

/**
 * 获取服务端内部通信地址
 * 用于 Next.js API 路由与后端服务通信
 * 在容器化部署时，这通常是 localhost 或内网地址
 */
export const getServerInternalUrl = (): string => {
  return process.env.SERVER_BASE_URL || 'http://localhost:8001';
};

/**
 * 获取客户端访问的服务器地址
 * 用于浏览器直接访问的地址（如 WebSocket 连接）
 * 在生产环境中，这应该是公网可访问的地址或网关地址
 */
export const getServerPublicUrl = (): string => {
  return process.env.NEXT_PUBLIC_SERVER_BASE_URL || 'http://localhost:8001';
};

export const getSsoBaseUrl = (): string => {
  return 'https://ssodr.iwhalecloud.com:40083/login/v2/auth';
};

export const getDxpBaseUrl = (): string => {
  return process.env.NEXT_PUBLIC_DXP_BASE_URL || 'https://da.iwhalecloud.com/davinci/WhaleCloud/publicForm/54b150e73c874732868db678d06168d6';
};
/**
 * 获取 WebSocket 连接 URL
 * 自动将 HTTP/HTTPS 协议转换为 WS/WSS
 */
export const getWebSocketUrl = (): string => {
  const baseUrl = getServerPublicUrl();
  const wsBaseUrl = baseUrl.replace(/^http/, 'ws');
  return `${wsBaseUrl}/ws/chat`;
};

/**
 * 配置常量
 */
export const CONFIG = {
  // 默认超时时间
  WS_TIMEOUT: 5000,
  
  // API 端点
  ENDPOINTS: {
    // 前端API路由（代理到后端）
    CHAT_STREAM: '/api/chat/stream',
    WIKI_PROJECTS: '/api/wiki/projects',
    WIKI_CACHE: '/api/wiki_cache',
    AUTH_STATUS: '/api/auth/status',
    AUTH_VALIDATE: '/api/auth/validate',
    MODELS_CONFIG: '/api/config/models',
    LANG_CONFIG: '/api/lang/config',
    LOCAL_REPO_STRUCTURE: '/api/local_repo/structure',
    EXPORT_WIKI: '/api/export/wiki',
    AUTH_TOKEN: '/api/auth/token',
    FILE_IMAGE: '/api/file/image',
    
    // 后端直接端点
    BACKEND: {
      // 聊天相关
      CHAT_COMPLETIONS_STREAM: { path: '/chat/completions/stream', method: 'POST' },
      WS_CHAT: { path: '/ws/chat', method: 'WebSocket' },
      
      // 认证相关
      AUTH_STATUS: { path: '/auth/status', method: 'GET' },
      AUTH_VALIDATE: { path: '/auth/validate', method: 'POST' },
      AUTH_LOGOUT: { path: '/auth/logout', method: 'GET' },
      AUTH_GET_TOKEN: { path: '/auth/getToken', method: 'GET' },
      AUTH_IS_LOGGED: { path: '/auth/isLogged', method: 'GET' },
      
      // Wiki和项目相关
      WIKI_CACHE_GET: { path: '/api/wiki_cache', method: 'GET' },
      WIKI_CACHE_POST: { path: '/api/wiki_cache', method: 'POST' },
      WIKI_CACHE_DELETE: { path: '/api/wiki_cache', method: 'DELETE' },
      PROCESSED_PROJECTS: { path: '/api/processed_projects', method: 'GET' },
      EXPORT_WIKI: { path: '/api/export/wiki', method: 'POST' },
      
      // 模型和配置相关
      MODELS_CONFIG: { path: '/api/models/config', method: 'GET' },
      MODELS_TEST_CONNECTION: { path: '/api/models/testConnection', method: 'POST' },
      LANG_CONFIG: { path: '/api/lang/config', method: 'GET' },
      CONFIG_SETTINGS: { path: '/api/config/settings', method: 'POST' },
      
      // 仓库相关
      LOCAL_REPO_STRUCTURE: { path: '/api/local_repo/structure', method: 'GET' },
      REPO_BRANCHES: { path: '/api/repo/branches', method: 'POST' },
      WHALE_DEV_CLOUD_STRUCTURE: { path: '/api/whaleDevCloud/structure', method: 'GET' },
      WHALE_DEV_CLOUD_VERIFY_TOKEN: { path: '/api/whaleDevCloud/verifyToken', method: 'POST' },
      
      // 文件操作
      FILE_ACTION: { path: '/api/file-action', method: 'POST' },
      
      // 健康检查和系统
      HEALTH: { path: '/health', method: 'GET' },
      ROOT: { path: '/', method: 'GET' },
    },
  },
} as const; 