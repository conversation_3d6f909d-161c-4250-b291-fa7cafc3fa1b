// 智能沙箱状态码到中文描述的映射
export const SANDBOX_STATUS_MAP: Record<string, string> = {
  NOT_CREATED: '智能沙箱未创建，正在为您分配环境...',
  CREATING: '智能沙箱已分配，环境正在启动中...',
  INITIALIZING: '智能沙箱正在初始化（检测 Gemini-CLI 会话），请耐心等待...',
  READY: '智能沙箱已就绪，可以开始使用',
  FAILED: '智能沙箱创建失败，请稍后重试或联系管理员',
  QUERY_FAILED: '智能沙箱状态查询失败，请检查网络或稍后重试',
  PENDING: '智能沙箱任务排队中，请稍等...',
  RUNNING: '智能沙箱正在运行中...',
  STOPPING: '智能沙箱正在停止...',
  STOPPED: '智能沙箱已停止',
  ERROR: '智能沙箱运行异常',
  TIMEOUT: '智能沙箱操作超时',
  QUOTA_EXCEEDED: '个人沙箱配额已满，请前往个人沙箱管理页面释放资源后再试',
  TOTAL_CAPACITY_FULL: '系统总资源已满，正在重试或请稍后再试',
  SYSTEM_BUSY: '当前智能沙箱资源繁忙，排队等候中',
  UNKNOWN: '智能沙箱状态未知'
};

export const SANDBOX_STATUS_MAP_EN: Record<string, string> = {
  NOT_CREATED: 'Sandbox not created, allocating environment...',
  CREATING: 'Sandbox allocated, environment is starting...',
  INITIALIZING: 'Sandbox is initializing (detecting Gemini-CLI session), please wait...',
  READY: 'Sandbox is ready, can start using',
  FAILED: 'Sandbox creation failed, please try again later or contact administrator',
  QUERY_FAILED: 'Sandbox status query failed, please check network or try again later',
  PENDING: 'Sandbox task is pending, please wait...',
  RUNNING: 'Sandbox is running...',
  STOPPING: 'Sandbox is stopping...',
  STOPPED: 'Sandbox is stopped',
  ERROR: 'Sandbox running error',
  TIMEOUT: 'Sandbox operation timeout',
  QUOTA_EXCEEDED: 'Personal sandbox quota is full, please go to the personal sandbox management page to release resources and try again',
  TOTAL_CAPACITY_FULL: 'System total resources are full, please try again later or contact administrator',
  SYSTEM_BUSY: 'Current sandbox resource is busy, waiting in queue',
  UNKNOWN: 'Sandbox status unknown'
};

// 获取状态描述（不区分大小写）
export const getSandboxStatusDescription = (status: string, lang?: string): string => {
  if (!status && lang === 'en') return 'Sandbox status unknown';
  if (!status && lang === 'zh') return '状态未知';
  
  const normalizedStatus = status.toString().trim().toUpperCase();
  return lang === 'en' ? SANDBOX_STATUS_MAP_EN[normalizedStatus] || `Sandbox Status: ${status}` : SANDBOX_STATUS_MAP[normalizedStatus] || `沙箱状态: ${status}`;
}; 