/**
 * 路径映射工具
 * 用于主web容器路径和沙箱容器路径的相互转换
 */

export interface PathMappings {
  realCodePath: string;      // 主web容器中的代码目录路径
  realDocsPath: string;      // 主web容器中的文档目录路径
  virtualCodePath: string;   // 沙箱容器中的代码目录路径
  virtualDocsPath: string;   // 沙箱容器中的文档目录路径
}

/**
 * 获取项目的路径映射配置
 * @param owner 仓库所有者
 * @param repoName 仓库名称
 * @param branch 分支名称
 * @param userCode 用户代码
 * @returns 路径映射配置
 */
export function getPathMappings(
  owner: string,
  repoName: string,
  branch: string,
  userCode: string
): PathMappings {
  const projectKey = `${repoName}-${branch}`;
  
  return {
    realCodePath: `/root/.adalflow/repos/${owner}/${projectKey}`,
    realDocsPath: `/root/.adalflow/workspace/${owner}/${projectKey}/${userCode}`,
    virtualCodePath: '/data/workspace/code',
    virtualDocsPath: '/data/workspace/docs',
  };
}

/**
 * 将实际路径转换为虚拟路径（用于显示）
 * @param realPath 实际路径
 * @param mappings 路径映射配置
 * @returns 虚拟路径
 */
export function realToVirtualPath(realPath: string, mappings: PathMappings): string {
  // 标准化路径，移除尾部斜杠
  const normalizedRealPath = realPath.replace(/\/$/, '');
  const normalizedCodePath = mappings.realCodePath.replace(/\/$/, '');
  const normalizedDocsPath = mappings.realDocsPath.replace(/\/$/, '');

  if (normalizedRealPath.startsWith(normalizedCodePath)) {
    const relativePath = normalizedRealPath.substring(normalizedCodePath.length);
    if (relativePath === '') {
      return mappings.virtualCodePath;
    }
    return mappings.virtualCodePath + relativePath;
  }

  if (normalizedRealPath.startsWith(normalizedDocsPath)) {
    const relativePath = normalizedRealPath.substring(normalizedDocsPath.length);
    if (relativePath === '') {
      return mappings.virtualDocsPath;
    }
    return mappings.virtualDocsPath + relativePath;
  }

  // 如果不匹配任何映射，返回原路径
  return realPath;
}

/**
 * 将虚拟路径转换为实际路径（用于文件操作）
 * @param virtualPath 虚拟路径
 * @param mappings 路径映射配置
 * @returns 实际路径
 */
export function virtualToRealPath(virtualPath: string, mappings: PathMappings): string {
  // 标准化路径
  const normalizedVirtualPath = virtualPath.replace(/\/$/, '');
  const normalizedVirtualCodePath = mappings.virtualCodePath.replace(/\/$/, '');
  const normalizedVirtualDocsPath = mappings.virtualDocsPath.replace(/\/$/, '');

  if (normalizedVirtualPath.startsWith(normalizedVirtualCodePath)) {
    const relativePath = normalizedVirtualPath.substring(normalizedVirtualCodePath.length);
    if (relativePath === '') {
      return mappings.realCodePath;
    }
    return mappings.realCodePath + relativePath;
  }

  if (normalizedVirtualPath.startsWith(normalizedVirtualDocsPath)) {
    const relativePath = normalizedVirtualPath.substring(normalizedVirtualDocsPath.length);
    if (relativePath === '') {
      return mappings.realDocsPath;
    }
    return mappings.realDocsPath + relativePath;
  }

  // 如果不匹配任何映射，返回原路径
  return virtualPath;
}

/**
 * 检查虚拟路径是否属于代码目录
 * @param virtualPath 虚拟路径
 * @returns 是否属于代码目录
 */
export function isCodePath(virtualPath: string): boolean {
  return virtualPath.startsWith('/data/workspace/code');
}

/**
 * 检查虚拟路径是否属于文档目录
 * @param virtualPath 虚拟路径
 * @returns 是否属于文档目录
 */
export function isDocsPath(virtualPath: string): boolean {
  return virtualPath.startsWith('/data/workspace/docs');
}

/**
 * 获取路径的类型
 * @param virtualPath 虚拟路径
 * @returns 路径类型
 */
export function getPathType(virtualPath: string): 'code' | 'docs' | 'unknown' {
  if (isCodePath(virtualPath)) {
    return 'code';
  }
  if (isDocsPath(virtualPath)) {
    return 'docs';
  }
  return 'unknown';
}

/**
 * 标准化路径，确保使用正斜杠
 * @param path 路径字符串
 * @returns 标准化的路径
 */
export function normalizePath(path: string): string {
  return path.replace(/\\/g, '/');
}

/**
 * 获取文件的相对路径（相对于项目根目录）
 * @param virtualPath 虚拟路径
 * @returns 相对路径
 */
export function getRelativePath(virtualPath: string): string {
  if (isCodePath(virtualPath)) {
    return virtualPath.replace('/data/workspace/code', '').replace(/^\//, '');
  }
  if (isDocsPath(virtualPath)) {
    return virtualPath.replace('/data/workspace/docs', '').replace(/^\//, '');
  }
  return virtualPath;
}

/**
 * 构建完整的虚拟路径
 * @param pathType 路径类型
 * @param relativePath 相对路径
 * @returns 完整的虚拟路径
 */
export function buildVirtualPath(pathType: 'code' | 'docs', relativePath: string): string {
  const basePath = pathType === 'code' ? '/data/workspace/code' : '/data/workspace/docs';
  const cleanRelativePath = relativePath.replace(/^\//, '');
  
  if (!cleanRelativePath) {
    return basePath;
  }
  
  return `${basePath}/${cleanRelativePath}`;
}

/**
 * 检查路径是否为目录（以斜杠结尾）
 * @param path 路径字符串
 * @returns 是否为目录
 */
export function isDirectory(path: string): boolean {
  return path.endsWith('/');
}

/**
 * 获取文件名（包含扩展名）
 * @param path 路径字符串
 * @returns 文件名
 */
export function getFileName(path: string): string {
  const parts = path.split('/');
  return parts[parts.length - 1] || '';
}

/**
 * 获取文件扩展名
 * @param path 路径字符串
 * @returns 文件扩展名（包含点号）
 */
export function getFileExtension(path: string): string {
  const fileName = getFileName(path);
  const lastDotIndex = fileName.lastIndexOf('.');
  return lastDotIndex > 0 ? fileName.substring(lastDotIndex) : '';
}

/**
 * 获取父目录路径
 * @param path 路径字符串
 * @returns 父目录路径
 */
export function getParentPath(path: string): string {
  const normalizedPath = path.replace(/\/$/, '');
  const lastSlashIndex = normalizedPath.lastIndexOf('/');
  return lastSlashIndex > 0 ? normalizedPath.substring(0, lastSlashIndex) : '/';
} 