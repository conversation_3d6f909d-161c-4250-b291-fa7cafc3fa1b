import { authFetch } from './authFetch';
import { Model, Provider, ModelConfig } from '@/types/modelConfig';

// 全局缓存变量
let cachedModelConfig: ModelConfig | null = null;
let configPromise: Promise<ModelConfig> | null = null;
let lastFetchTime: number = 0;
const CACHE_DURATION = 60 * 60 * 1000; // 60分钟缓存

/**
 * 获取模型配置（带缓存）
 * @returns Promise<ModelConfig>
 */
export const fetchModelConfig = async (): Promise<ModelConfig> => {
  const now = Date.now();
  
  // 如果缓存存在且未过期，直接返回
  if (cachedModelConfig && (now - lastFetchTime) < CACHE_DURATION) {
    return cachedModelConfig;
  }
  
  // 如果正在请求中，等待现有请求
  if (configPromise) {
    return configPromise;
  }
  
  // 创建新的请求
  configPromise = (async () => {
    try {
      const response = await authFetch('/api/config/models');
      
      if (!response || !response.ok) {
        throw new Error(`Error fetching model configurations: ${response?.status}`);
      }
      
      const data = await response.json();
      cachedModelConfig = data;
      lastFetchTime = now;
      return data;
    } catch (err) {
      // 清除失败的请求缓存
      configPromise = null;
      throw err;
    }
  })();
  
  return configPromise;
};

/**
 * 清除缓存
 */
export const clearModelConfigCache = (): void => {
  cachedModelConfig = null;
  configPromise = null;
  lastFetchTime = 0;
};

/**
 * 获取指定provider的可用模型
 * @param providerId provider ID
 * @returns Promise<Model[]>
 */
export const getProviderModels = async (providerId: string): Promise<Model[]> => {
  const config = await fetchModelConfig();
  const provider = config.providers.find(p => p.id === providerId);
  return provider?.models || [];
};

/**
 * 获取所有providers
 * @returns Promise<Provider[]>
 */
export const getAllProviders = async (): Promise<Provider[]> => {
  const config = await fetchModelConfig();
  return config.providers;
};

/**
 * 获取默认provider
 * @returns Promise<string>
 */
export const getDefaultProvider = async (): Promise<string> => {
  const config = await fetchModelConfig();
  return config.defaultProvider;
};

/**
 * 检查provider是否存在
 * @param providerId provider ID
 * @returns Promise<boolean>
 */
export const isProviderValid = async (providerId: string): Promise<boolean> => {
  const config = await fetchModelConfig();
  return config.providers.some(p => p.id === providerId);
};

/**
 * 获取provider配置
 * @param providerId provider ID
 * @returns Promise<Provider | null>
 */
export const getProviderConfig = async (providerId: string): Promise<Provider | null> => {
  const config = await fetchModelConfig();
  return config.providers.find(p => p.id === providerId) || null;
}; 