/**
 * @ 符号解析工具
 * 用于检测和解析用户输入中的 @ 符号文件引用
 */

export interface AtCompletionInfo {
  isAtCompletion: boolean;
  pattern: string;
  startIndex: number;
  endIndex: number;
  beforeAt: string;
  afterAt: string;
}

/**
 * 解析文本中的 @ 符号和文件路径
 * @param text 输入文本
 * @param cursorPosition 光标位置
 * @returns 解析结果
 */
export function parseAtCompletion(text: string, cursorPosition: number): AtCompletionInfo {
  const defaultResult: AtCompletionInfo = {
    isAtCompletion: false,
    pattern: '',
    startIndex: -1,
    endIndex: -1,
    beforeAt: '',
    afterAt: '',
  };

  if (!text || cursorPosition < 0 || cursorPosition > text.length) {
    return defaultResult;
  }

  // 在光标位置之前查找最近的 @ 符号
  let atIndex = -1;
  for (let i = cursorPosition - 1; i >= 0; i--) {
    if (text[i] === '@') {
      // 检查 @ 符号前是否为空格或文本开头
      if (i === 0 || /\s/.test(text[i - 1])) {
        atIndex = i;
        break;
      }
    } else if (/\s/.test(text[i])) {
      // 遇到空格时停止搜索
      break;
    }
  }

  if (atIndex === -1) {
    return defaultResult;
  }

  // 从 @ 符号后开始查找路径结束位置
  let endIndex = cursorPosition;
  for (let i = atIndex + 1; i < text.length; i++) {
    const char = text[i];
    // 允许路径字符：字母、数字、/、-、_、.
    if (/\s/.test(char)) {
      endIndex = i;
      break;
    }
    if (i === text.length - 1) {
      endIndex = text.length;
      break;
    }
  }

  // 提取路径模式
  const pattern = text.substring(atIndex + 1, endIndex);
  
  // 检查光标是否在 @ 符号和路径结束之间
  if (cursorPosition < atIndex || cursorPosition > endIndex) {
    return defaultResult;
  }

  return {
    isAtCompletion: true,
    pattern,
    startIndex: atIndex,
    endIndex,
    beforeAt: text.substring(0, atIndex),
    afterAt: text.substring(endIndex),
  };
}

/**
 * 替换文本中的 @ 路径
 * @param text 原始文本
 * @param completionInfo 解析信息
 * @param newPath 新路径
 * @returns 替换后的文本
 */
export function replaceAtPath(
  text: string,
  completionInfo: AtCompletionInfo,
  newPath: string
): string {
  if (!completionInfo.isAtCompletion) {
    return text;
  }

  const { beforeAt, afterAt } = completionInfo;
  
  // 构建新文本：@ 符号前的内容 + @ + 新路径 + @ 符号后的内容
  return beforeAt + '@' + newPath + afterAt;
}

/**
 * 获取光标在替换后的新位置
 * @param completionInfo 解析信息
 * @param newPath 新路径
 * @returns 新的光标位置
 */
export function getNewCursorPosition(
  completionInfo: AtCompletionInfo,
  newPath: string
): number {
  if (!completionInfo.isAtCompletion) {
    return 0;
  }

  // 光标位置：@ 符号前的内容长度 + @ 符号长度 + 新路径长度
  return completionInfo.beforeAt.length + 1 + newPath.length;
}

/**
 * 检查文本是否包含有效的 @ 文件引用
 * @param text 文本内容
 * @returns 是否包含 @ 引用
 */
export function hasAtReference(text: string): boolean {
  const atRegex = /(?:^|\s)@[\w\/\-\._]+/g;
  return atRegex.test(text);
}

/**
 * 提取文本中所有的 @ 文件引用
 * @param text 文本内容
 * @returns 文件引用列表
 */
export function extractAtReferences(text: string): string[] {
  const atRegex = /(?:^|\s)@([\w\/\-\._]+)/g;
  const references: string[] = [];
  let match;

  while ((match = atRegex.exec(text)) !== null) {
    references.push(match[1]);
  }

  return references;
}

/**
 * 文件引用信息接口
 */
export interface FileReferenceInfo {
  path: string;
  startIndex: number;
  endIndex: number;
  isDirectory: boolean;
}

/**
 * 解析文本中的所有文件引用（不包括正在输入的@模式）
 * @param text 文本内容
 * @param excludeActiveCompletion 是否排除当前活跃的@完成
 * @param activeCompletion 当前活跃的@完成信息
 * @returns 文件引用信息列表
 */
export function parseFileReferences(
  text: string, 
  excludeActiveCompletion: boolean = false,
  activeCompletion?: AtCompletionInfo | null
): FileReferenceInfo[] {
  const references: FileReferenceInfo[] = [];
  // 匹配完整的文件引用：@后跟随非空格字符，并以空格或文本末尾结束
  const atRegex = /(?:^|\s)@([\w\/\-\._]+)(?=\s|$)/g;
  let match;

  while ((match = atRegex.exec(text)) !== null) {
    const fullMatch = match[0];
    const path = match[1];
    const actualStartIndex = match.index + (fullMatch.startsWith(' ') ? 1 : 0);
    const startIndex = actualStartIndex;
    const endIndex = startIndex + path.length + 1; // +1 for @
    
    // 如果需要排除活跃的完成，并且当前匹配与活跃完成重叠，则跳过
    if (excludeActiveCompletion && activeCompletion && activeCompletion.isAtCompletion) {
      const overlapStart = Math.max(startIndex, activeCompletion.startIndex);
      const overlapEnd = Math.min(endIndex, activeCompletion.endIndex);
      if (overlapStart < overlapEnd) {
        continue;
      }
    }
    
    references.push({
      path,
      startIndex,
      endIndex,
      isDirectory: path.endsWith('/')
    });
  }

  return references;
}

/**
 * 删除指定位置的文件引用
 * @param text 原始文本
 * @param startIndex 引用开始位置
 * @param endIndex 引用结束位置
 * @returns 删除引用后的文本
 */
export function removeFileReference(text: string, startIndex: number, endIndex: number): string {
  // 检查是否需要删除前后的空格
  let adjustedStart = startIndex;
  let adjustedEnd = endIndex;
  
  // 如果引用前面有空格，一起删除
  if (adjustedStart > 0 && text[adjustedStart - 1] === ' ') {
    adjustedStart -= 1;
  }
  
  // 如果引用后面有空格，一起删除（但保留一个空格以避免单词连接）
  if (adjustedEnd < text.length && text[adjustedEnd] === ' ') {
    adjustedEnd += 1;
  }
  
  const result = text.substring(0, adjustedStart) + text.substring(adjustedEnd);
  
  // 如果删除后出现了双空格，清理为单空格
  return result.replace(/\s{2,}/g, ' ').trim();
}

/**
 * 验证路径是否有效
 * @param path 文件路径
 * @returns 是否有效
 */
export function isValidPath(path: string): boolean {
  if (!path || path.trim().length === 0) {
    return false;
  }

  // 检查是否包含无效字符
  const invalidChars = /[<>:"|?*]/;
  if (invalidChars.test(path)) {
    return false;
  }

  // 检查是否以有效字符开头
  if (!/^[a-zA-Z0-9\/\-\._]/.test(path)) {
    return false;
  }

  return true;
} 