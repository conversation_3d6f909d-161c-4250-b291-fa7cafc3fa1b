/**
 * Server-Sent Events (SSE) client for chat completions
 * This replaces the WebSocket client with SSE implementation
 */

import { authFetch } from "./authFetch";
import { getServerPublicUrl } from "./config";

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface ChatCompletionRequest {
  repo_url: string;
  type: string;
  messages: Array<{ id?: string; role: 'user' | 'assistant' | 'system'; content: string }>;
  provider: string;
  model: string;
  language: string;
  token?: string;
  session_id: string;
  branch: string;
  filePath?: string;
  existing_topic_id?: string;
  existing_topic_id_code?: string;
  existing_topic_id_doc?: string;
  caller?: number;
  file_references?: Array<{ path: string; isDirectory: boolean; name?: string }>;
  wiki_id: string;
  command_params?: { operation: string | null, param: string | null };
  images?: Array<string>;
}

/**
 * Creates an SSE connection for chat completions
 * @param request The chat completion request
 * @param onMessage Callback for received messages
 * @param onError Callback for errors
 * @param onClose Callback for when the connection closes
 * @param apiKey API key for authentication
 * @param whaleDevCloudToken Whale dev cloud token for authentication
 * @returns An object with a close method to terminate the SSE connection
 */
export const createChatSSE = (
  body: ChatCompletionRequest,
  onMessage: (message: string, event?: string, id?:string) => void,
  onError: (error: Event) => void,
  onClose: () => void,
  apiKey?: string | null,
  whaleDevCloudToken?: string | null
): { close: () => void } => {
  const sseUrl = `${getServerPublicUrl()}/api/chat/stream`;
  
  // Prepare the request body for the POST request
  const requestOptions: RequestInit = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream',
      'Cache-Control': 'no-cache',
    },
    body: JSON.stringify(body),
  };

  // Add authentication headers if provided
  if (apiKey) {
    requestOptions.headers = {
      ...requestOptions.headers,
      'X-API-Key': apiKey,
    };
  }
  
  if (whaleDevCloudToken) {
    requestOptions.headers = {
      ...requestOptions.headers,
      'X-Whale-Dev-Cloud-Token': whaleDevCloudToken,
    };
  }

  let eventSource: EventSource | null = null;
  let abortController: AbortController | null = null;
  let isClosed = false;

  const cleanup = () => {
    if (isClosed) return;
    isClosed = true;
    
    if (eventSource) {
      eventSource.close();
      eventSource = null;
    }
    
    if (abortController) {
      abortController.abort();
      abortController = null;
    }
  };

  // Start the SSE connection
  const initSSE = async () => {
    try {
      abortController = new AbortController();
      
      // Make the initial POST request to start the SSE stream
      const response = await authFetch(sseUrl, {
        ...requestOptions,
        signal: abortController.signal,
      });

      if (!response || !response.ok) {
        throw new Error(`HTTP error! status: ${response?.status}`);
      }

      // Check if the response is actually an SSE stream
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('text/event-stream')) {
        // If not SSE, try to read the response as text for error information
        const errorText = await response.text();
        throw new Error(`Expected SSE stream, got ${contentType}. Response: ${errorText}`);
      }

      // Get the SSE URL from the response (if the server returns it)
      // Or use the response itself as an SSE stream
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error('No response body reader available');
      }

      // Read the stream
      const readStream = async () => {
        try {
          let currentEvent: string | undefined = undefined;
          let id: string | undefined = undefined;
          
          while (!isClosed) {
            const { done, value } = await reader.read();
            
            if (done) {
              cleanup();
              onClose();
              break;
            }

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\n');

            for (const line of lines) {
              if (line.startsWith('event: ')) {
                currentEvent = line.slice(7).trim();
              } else if (line.startsWith('data: ')) {
                const data = line.slice(6).trim();
                if (data === '[DONE]') {
                  cleanup();
                  onClose();
                  return;
                }
                if (data) {
                  onMessage(data, currentEvent, id);
                  // Reset currentEvent after processing
                  currentEvent = undefined;
                }
              } else if (line.startsWith('id: ')) {
                id = line.slice(4).trim();
              } else if (line.trim() && !line.startsWith(':') && !line.startsWith('id:') && !line.startsWith('retry:')) {
                // Handle plain text streaming (non-SSE format)
                // This handles cases where the backend streams plain text without SSE formatting
                const data = line.trim();
                if (data === '[DONE]') {
                  cleanup();
                  onClose();
                  return;
                }
                if (data) {
                  onMessage(data, currentEvent, id);
                  // Reset currentEvent after processing
                  currentEvent = undefined;
                  id = undefined;
                }
              }
            }
          }
        } catch (error) {
          if (!isClosed) {
            console.error('SSE read error:', error);
            onError(error as Event);
            cleanup();
          }
        }
      };

      readStream();

    } catch (error) {
      if (!isClosed) {
        console.error('SSE connection error:', error);
        onError(error as Event);
        cleanup();
      }
    }
  };

  // Start the connection
  initSSE();

  // Return an object with a close method
  return {
    close: cleanup,
  };
};

/**
 * Closes an SSE connection
 * @param sseConnection The SSE connection object to close
 */
export const closeSSE = (sseConnection: { close: () => void } | null): void => {
  if (sseConnection) {
    sseConnection.close();
  }
};
