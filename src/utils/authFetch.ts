import { login } from "./auth";

export async function authFetch(url: string, options: RequestInit = {}) {
  const headers = new Headers(options.headers || {});
  const response = await fetch(url, {
      ...options,
      headers,
      credentials: 'include',
    });
  if (response.status === 401) {
    login();
    return;
  } else if (response.status === 403) {
    window.dispatchEvent(new CustomEvent('auth-error-403', { detail: response.statusText }));
  }
  return response;
  }