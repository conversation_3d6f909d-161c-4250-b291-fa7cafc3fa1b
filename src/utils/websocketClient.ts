/**
 * WebSocket client for chat completions
 * This replaces the HTTP streaming endpoint with a WebSocket connection
 */

// Get the server base URL from environment or use default
// For client-side WebSocket connections, use NEXT_PUBLIC_SERVER_BASE_URL
// This should be the externally accessible URL (gateway/public URL)
const getServerBaseUrl = (): string => {
  // In browser environment, use the public URL
  if (typeof window !== 'undefined') {
    return process.env.NEXT_PUBLIC_SERVER_BASE_URL || 'http://localhost:8001';
  }
  // On server side (unlikely for WebSocket), fallback to localhost
  return process.env.SERVER_BASE_URL || 'http://localhost:8001';
};

// Convert HTTP URL to WebSocket URL
export const getWebSocketUrl = (apiKey?: string | null, whaleDevCloudToken?: string | null): string => {
  const baseUrl = getServerBaseUrl();
  // Replace http:// with ws:// or https:// with wss://
  const wsBaseUrl = baseUrl.replace(/^http/, 'ws');
  let url = `${wsBaseUrl}/ws/chat`;

  const params: string[] = [];
  // 过滤掉apiKey和whaleDevCloudToken中的●符号
  const cleanedApiKey = apiKey?.replace(/●/g, '') || '';
  const cleanedWhaleDevCloudToken = whaleDevCloudToken?.replace(/●/g, '') || '';
  
  if (cleanedApiKey) {
    params.push(`apiKey=${encodeURIComponent(cleanedApiKey)}`);
  }
  if (cleanedWhaleDevCloudToken) {
    params.push(`whaleDevCloudToken=${encodeURIComponent(cleanedWhaleDevCloudToken)}`);
  }
  if (params.length > 0) {
    url += `?${params.join('&')}`;
  }

  return url;
};

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface ChatCompletionRequest {
  repo_url: string;
  type: string;
  branch: string;
  messages: Array<{ role: 'user' | 'assistant'; content: string }>;
  provider: string;
  model: string;
  language: string;
  token?: string;
  session_id: string; // Add session_id to the request
}

/**
 * Creates a WebSocket connection for chat completions
 * @param request The chat completion request
 * @param onMessage Callback for received messages
 * @param onError Callback for errors
 * @param onClose Callback for when the connection closes
 * @returns The WebSocket connection
 */
export const createChatWebSocket = (
  body: ChatCompletionRequest,
  onMessage: (message: string) => void,
  onError: (error: Event) => void,
  onClose: () => void,
  apiKey?: string | null,
  whaleDevCloudToken?: string | null
): WebSocket => {
  // Create WebSocket connection
  const wsUrl = new URL(getWebSocketUrl(apiKey, whaleDevCloudToken));
  const ws = new WebSocket(wsUrl.toString());
  
  // Set up event handlers
  ws.onopen = () => {
    console.log('WebSocket connection established');
    // Send the request body once the connection is open
    ws.send(JSON.stringify(body));
  };
  
  ws.onmessage = (event) => {
    // Call the message handler with the received text
    onMessage(event.data);
  };
  
  ws.onerror = (error) => {
    console.error('WebSocket error:', error);
    onError(error);
  };
  
  ws.onclose = () => {
    console.log('WebSocket connection closed');
    onClose();
  };
  
  return ws;
};

/**
 * Closes a WebSocket connection
 * @param ws The WebSocket connection to close
 */
export const closeWebSocket = (ws: WebSocket | null): void => {
  if (ws && ws.readyState === WebSocket.OPEN) {
    ws.close();
  }
};
