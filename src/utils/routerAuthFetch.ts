import { headers } from "next/headers";
import { NextRequest } from "next/server";

export async function routerAuthFetch(request: NextRequest, url: string, options: RequestInit = {}) {
  // 1. 创建默认头部
  const defaultHeaders = new Headers({
    'Content-Type': 'application/json'
  });

  
  // 2. 从NextRequest中只提取Cookie头部
  const cookie = request.headers.get('cookie');
  const requestHeaders = new Headers();
  if (cookie) {
    requestHeaders.set('cookie', cookie);
  }

  // 3. 将传入的options.headers转换为Headers对象
  const inputHeaders = new Headers(options.headers || {});

  // 合并优先级: 传入headers > request headers > 默认headers
  const mergedHeaders = new Headers([
    ...defaultHeaders,
    ...requestHeaders,
    ...inputHeaders
  ]);

  console.log(headers);
  console.log(options);
  
  return fetch(url, {
    ...options,
    headers: mergedHeaders,
  });
}