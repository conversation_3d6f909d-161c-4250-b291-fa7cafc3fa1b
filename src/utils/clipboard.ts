/**
 * 通用的剪贴板复制功能
 * 支持现代浏览器的 Clipboard API 和降级到传统方法
 * Universal clipboard copy functionality with fallback support
 * 
 * @param text 要复制的文本 / Text to copy
 * @returns Promise<boolean> 复制是否成功 / Whether copy was successful
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  // 方法 1: 使用现代 Clipboard API (HTTPS 环境)
  // Method 1: Modern Clipboard API (requires HTTPS)
  if (navigator.clipboard && navigator.clipboard.writeText) {
    try {
      await navigator.clipboard.writeText(text);
      return true;
    } catch (err) {
      console.warn('Clipboard API failed, trying fallback method:', err);
      // 如果失败,继续尝试降级方法
      // If fails, try fallback method
    }
  }

  // 方法 2: 降级方法 - 使用 execCommand (适用于较旧浏览器或非 HTTPS 环境)
  // Method 2: Fallback - using execCommand (for older browsers or non-HTTPS)
  try {
    // 创建临时 textarea 元素
    // Create temporary textarea element
    const textarea = document.createElement('textarea');
    textarea.value = text;
    
    // 设置样式使其不可见
    // Style to make it invisible
    textarea.style.position = 'fixed';
    textarea.style.top = '0';
    textarea.style.left = '0';
    textarea.style.width = '2em';
    textarea.style.height = '2em';
    textarea.style.padding = '0';
    textarea.style.border = 'none';
    textarea.style.outline = 'none';
    textarea.style.boxShadow = 'none';
    textarea.style.background = 'transparent';
    textarea.style.opacity = '0';
    textarea.style.pointerEvents = 'none';
    
    // 添加到 DOM
    // Append to DOM
    document.body.appendChild(textarea);
    
    // 选中文本
    // Select text
    textarea.focus();
    textarea.select();
    
    // iOS 兼容处理
    // iOS compatibility
    const range = document.createRange();
    range.selectNodeContents(textarea);
    const selection = window.getSelection();
    if (selection) {
      selection.removeAllRanges();
      selection.addRange(range);
    }
    textarea.setSelectionRange(0, textarea.value.length);
    
    // 执行复制命令
    // Execute copy command
    const successful = document.execCommand('copy');
    
    // 清理: 移除临时元素
    // Cleanup: remove temporary element
    document.body.removeChild(textarea);
    
    return successful;
  } catch (err) {
    console.error('All copy methods failed:', err);
    return false;
  }
}

/**
 * 检查是否支持剪贴板功能
 * Check if clipboard functionality is supported
 * 
 * @returns boolean 是否支持 / Whether supported
 */
export function isClipboardSupported(): boolean {
  return !!(
    (navigator.clipboard && navigator.clipboard.writeText) ||
    document.queryCommandSupported?.('copy')
  );
}
