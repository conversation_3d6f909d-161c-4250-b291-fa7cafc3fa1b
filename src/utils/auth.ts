const appKey = process.env.NEXT_PUBLIC_SSO_APP_KEY || process.env.SSO_APP_KEY || 'eaccec38af18ed371dea';
const defaultSsoBaseUrl = 'https://ssodr.iwhalecloud.com:40083/login/v2/auth/';
const ssoBaseUrl = process.env.NEXT_PUBLIC_SSO_BASE_URL || process.env.SSO_BASE_URL || defaultSsoBaseUrl;
const fallbackOrigin = 'http://localhost:3000';
const redirectPath = '/auth/token';

const getClientOrigin = (): string => {
  if (typeof window === 'undefined' || !window.location) {
    return fallbackOrigin;
  }

  try {
    return new URL(window.location.href).origin;
  } catch (error) {
    console.error('Failed to read client origin', error);
    return fallbackOrigin;
  }
};

const buildRedirectUrl = (): string => {
  try {
    return new URL(redirectPath, getClientOrigin()).toString();
  } catch (error) {
    console.error('Failed to construct redirect URL', error);
    return `${fallbackOrigin}${redirectPath}`;
  }
};

const navigateTo = (target: string) => {
  if (typeof window === 'undefined') {
    return;
  }

  window.location.assign(target);
};

const buildSsoUrl = (path: 'login' | 'logout'): string => {
  const normalizedBase = ssoBaseUrl.endsWith('/') ? ssoBaseUrl : `${ssoBaseUrl}/`;

  try {
    const url = new URL(path, normalizedBase);
    url.searchParams.set('appKey', appKey);
    url.searchParams.set('redirectUrl', buildRedirectUrl());
    return url.toString();
  } catch (error) {
    console.error('Failed to construct SSO URL', error);
    return normalizedBase;
  }
};

export function logout() {
  navigateTo(buildSsoUrl('logout'));
}

export function login() {
  navigateTo(buildSsoUrl('login'));
}

export function home() {
  navigateTo('/');
}
