'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import { authFetch } from '@/utils/authFetch';
import { useLanguage } from '@/contexts/LanguageContext';

export interface FileSearchResult {
  real_path: string;
  virtual_path: string;
  is_directory: boolean;
}

export interface FileSearchSuggestion {
  label: string;
  value: string;
  description?: string;
  isDirectory: boolean;
}

export enum FileSearchStatus {
  IDLE = 'idle',
  SEARCHING = 'searching',
  READY = 'ready',
  ERROR = 'error',
}

interface FileSearchState {
  status: FileSearchStatus;
  suggestions: FileSearchSuggestion[];
  isLoading: boolean;
  pattern: string;
  error?: string;
}

interface UseFileSearchProps {
  repoUrl: string;
  branch: string;
  userCode: string;
  enabled: boolean;
}

export const useFileSearch = ({
  repoUrl,
  branch,
  userCode,
  enabled,
}: UseFileSearchProps) => {
  const { messages } = useLanguage();
  const [state, setState] = useState<FileSearchState>({
    status: FileSearchStatus.IDLE,
    suggestions: [],
    isLoading: false,
    pattern: '',
  });

  const searchController = useRef<AbortController | null>(null);
  const debounceTimer = useRef<NodeJS.Timeout | null>(null);

  // 清理函数
  const cleanup = useCallback(() => {
    if (searchController.current) {
      searchController.current.abort();
      searchController.current = null;
    }
    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
      debounceTimer.current = null;
    }
  }, []);

  // 重置状态
  const reset = useCallback(() => {
    cleanup();
    setState({
      status: FileSearchStatus.IDLE,
      suggestions: [],
      isLoading: false,
      pattern: '',
    });
  }, [cleanup]);

  // 执行搜索
  const performSearch = useCallback(async (pattern: string) => {
    if (!enabled || !repoUrl || !userCode) {
      return;
    }

    // 清理之前的请求
    cleanup();

    // 创建新的控制器
    const controller = new AbortController();
    searchController.current = controller;

    setState(prev => ({
      ...prev,
      status: FileSearchStatus.SEARCHING,
      isLoading: true,
      pattern,
      error: undefined,
    }));

    try {
      const searchParams = new URLSearchParams({
        repo_url: repoUrl,
        branch: branch,
        pattern: pattern,
        user_code: userCode,
      });

      const response = await authFetch(`/api/file/search?${searchParams}`, {
        signal: controller.signal,
      });

      if (!response || !response.ok) {
        throw new Error(`搜索失败: ${response?.statusText || '网络错误'}`);
      }

      const data = await response.json();

      if (controller.signal.aborted) {
        return;
      }

      if (data.success) {
        const suggestions: FileSearchSuggestion[] = data.data
          .filter((result: FileSearchResult) => {
            // 过滤掉 .gemini 目录
            return !result.virtual_path.startsWith('.gemini/');
          })
          .map((result: FileSearchResult) => {
            // 为根目录提供特殊描述
            let description = result.is_directory ? (messages.components?.fileSearchSuggestions?.directory || 'Directory') : (messages.components?.fileSearchSuggestions?.file || 'File');
            if (result.is_directory) {
              const pathParts = result.virtual_path.split('/');
              const rootDir = pathParts[0];
              const rootDescriptions = messages.components?.fileSearchSuggestions?.rootDescriptions;
              // 只有在根目录（即没有子路径）时才做判断
              if (rootDescriptions && pathParts.length === 2) {
                switch (rootDir) {
                  case 'code':
                    description = rootDescriptions.code || 'Source Code';
                    break;
                  case 'i-doc':
                    description = rootDescriptions['i-doc'] || 'AI Generated Documentation';
                    break;
                  case 'o-doc':
                    description = rootDescriptions['o-doc'] || 'Product Documentation';
                    break;
                  case 'workspace':
                    description = rootDescriptions.workspace || 'User Personalized Documentation';
                    break;
                  case '.gemini':
                    description = rootDescriptions['.gemini'] || 'gemini-cli Configuration Directory';
                    break;
                }
              }
            }
            
            return {
              label: result.virtual_path,
              value: result.virtual_path,
              description,
              isDirectory: result.is_directory,
            };
          });

        setState(prev => ({
          ...prev,
          status: FileSearchStatus.READY,
          suggestions,
          isLoading: false,
        }));
      } else {
        throw new Error(data.message || '搜索失败');
      }
    } catch (error) {
      if (controller.signal.aborted) {
        return;
      }

      console.error('文件搜索错误:', error);
      setState(prev => ({
        ...prev,
        status: FileSearchStatus.ERROR,
        suggestions: [],
        isLoading: false,
        error: error instanceof Error ? error.message : '搜索失败',
      }));
    }
  }, [enabled, repoUrl, branch, userCode, cleanup, messages.components?.fileSearchSuggestions?.directory, messages.components?.fileSearchSuggestions?.file, messages.components?.fileSearchSuggestions?.rootDescriptions]);

  // 防抖搜索
  const debouncedSearch = useCallback((pattern: string) => {
    // 清理之前的防抖定时器
    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }

    // 如果模式为空，清空建议
    if (!pattern.trim()) {
      // 取消正在进行的请求
      cleanup();
      setState(prev => ({
        ...prev,
        status: FileSearchStatus.IDLE,
        suggestions: [],
        isLoading: false,
        pattern: '',
      }));
      return;
    }

    // 设置新的防抖定时器
    debounceTimer.current = setTimeout(() => {
      performSearch(pattern);
    }, 200); // 200ms 防抖，减少检查次数
  }, [performSearch, cleanup]);

  // 搜索函数
  const search = useCallback((pattern: string) => {
    if (!enabled) {
      return;
    }

    debouncedSearch(pattern);
  }, [enabled, debouncedSearch]);

  // 读取文件内容
  const readFile = useCallback(async (virtualPath: string): Promise<string | null> => {
    if (!repoUrl || !userCode) {
      return null;
    }

    try {
      const searchParams = new URLSearchParams({
        repo_url: repoUrl,
        branch: branch,
        virtual_path: virtualPath,
        user_code: userCode,
      });

      const response = await authFetch(`/api/file/read?${searchParams}`);

      if (!response || !response.ok) {
        throw new Error(`读取文件失败: ${response?.statusText || '网络错误'}`);
      }

      const data = await response.json();

      if (data.success) {
        return data.data.content;
      } else {
        throw new Error(data.message || '读取文件失败');
      }
    } catch (error) {
      console.error('读取文件错误:', error);
      return null;
    }
  }, [repoUrl, branch, userCode]);

  // 清理副作用
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  // 当依赖项变化时重置
  useEffect(() => {
    reset();
  }, [repoUrl, branch, userCode, reset]);

  return {
    ...state,
    search,
    reset,
    readFile,
  };
}; 