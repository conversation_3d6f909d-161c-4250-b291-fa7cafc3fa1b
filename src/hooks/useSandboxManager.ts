import { useState, useCallback, useRef, useEffect } from 'react';
import { authFetch } from '@/utils/authFetch';
import RepoInfo from '@/types/repoinfo';
import { useSettings } from '@/contexts/SettingsContext';
import { useLanguage } from '@/contexts/LanguageContext';

// 沙箱状态类型定义
interface SandboxState {
  status: string;
  isPolling: boolean;
  isReady: boolean;
  error: string | null;
}

// 轮询配置
const POLLING_CONFIG = {
  interval: 2000, // 轮询间隔2秒
  maxRetries: 30, // 最大重试次数（1分钟）
  errorRetryInterval: 5000, // 错误时重试间隔5秒
};

// 标准化状态码，确保不区分大小写
const normalizeStatus = (status: string): string => {
  return status?.toString().trim().toUpperCase() || '';
};

// 检查状态是否匹配（不区分大小写）
const isStatusMatch = (status1: string, status2: string): boolean => {
  const normalized1 = normalizeStatus(status1);
  const normalized2 = normalizeStatus(status2);
  const matches = normalized1 === normalized2;
  
  if (status1 && status2) {
    console.log(`状态比对: "${status1}" -> "${normalized1}" vs "${status2}" -> "${normalized2}" = ${matches}`);
  }
  
  return matches;
};

// 最终状态（不需要继续轮询的状态）
const FINAL_STATUSES = ['READY', 'FAILED'];
const isFinalStatus = (status: string): boolean => {
  const normalizedStatus = normalizeStatus(status);
  return FINAL_STATUSES.includes(normalizedStatus);
};

/**
 * 沙箱管理Hook
 * 负责沙箱状态的查询、创建和轮询
 * 仅在gemini-cli模式下工作
 */
export const useSandboxManager = (repoInfo: RepoInfo | null, modelType: 'whalecloud' | 'gemini-cli') => {
  const [sandboxState, setSandboxState] = useState<SandboxState>({
    status: '',
    isPolling: false,
    isReady: false,
    error: null
  });
  
  const pollingTimerRef = useRef<NodeJS.Timeout | null>(null);
  const isPollingRef = useRef<boolean>(false);
  const retryCountRef = useRef<number>(0);

  const { settings } = useSettings();
  const { messages } = useLanguage();

  /**
   * 查询沙箱状态
   * @returns 沙箱状态字符串
   */
  const querySandboxStatus = useCallback(async (): Promise<string> => {
    // 如果大模型Key无效，则不发起后端请求
    if (!settings?.isApiKeyValid) {
      console.warn('跳过沙箱状态查询：API Key 无效');
      throw new Error(messages.common?.invalidApiKey || 'Large Model Token invalid. Please configure it in Settings.');
    }

    if (!repoInfo?.wiki_id) {
      console.error('沙盒状态查询失败: 缺少wiki_id', { wiki_id: repoInfo?.wiki_id });
      throw new Error('缺少wiki_id');
    }

    console.log('开始查询沙箱状态:', { wiki_id: repoInfo.wiki_id });
    
    const statusResp = await authFetch(
      `/api/k8s/sandbox/me/status/new?wiki_id=${repoInfo.wiki_id}`
    );
    
    console.log('沙盒状态查询响应:', { status: statusResp?.status, ok: statusResp?.ok });
    
    if (!statusResp || !statusResp.ok) {
      console.error('沙盒状态查询HTTP失败:', statusResp?.status);
      throw new Error(messages.common?.requestFail || '沙盒状态查询失败');
    }
    
    const statusData = await statusResp.json();
    console.log('沙盒状态查询数据:', statusData);
    
    if (!statusData.success) {
      console.error('沙盒状态查询业务失败:', statusData.error);
      throw new Error(statusData.error || '沙盒状态查询失败');
    }
    
    const rawStatus = statusData.data.status;
    const status = rawStatus?.toString().trim().toUpperCase() || 'QUERY_FAILED';
    console.log('原始状态:', rawStatus, '-> 标准化状态:', status);
    
    return status;
  }, [repoInfo, settings?.isApiKeyValid, messages.common?.invalidApiKey]);

  /**
   * 创建沙箱
   */
  const createSandbox = useCallback(async (): Promise<void> => {
    // 如果大模型Key无效，则不发起后端请求
    if (!settings?.isApiKeyValid) {
      console.warn('跳过沙箱创建：API Key 无效');
      throw new Error(messages.common?.invalidApiKey || 'Large Model Token invalid. Please configure it in Settings.');
    }

    if (!repoInfo?.wiki_id) {
      console.error('沙盒创建失败: 缺少wiki_id', { wiki_id: repoInfo?.wiki_id });
      throw new Error('缺少仓库信息');
    }

    console.log('开始创建沙盒:', { wiki_id: repoInfo.wiki_id });

    const createResp = await authFetch('/api/k8s/sandbox/me/create', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        wiki_id: repoInfo.wiki_id
      })
    });

    console.log('沙盒创建响应:', { status: createResp?.status, ok: createResp?.ok });

    if (!createResp || !createResp.ok) {
      console.error('沙盒创建HTTP失败:', createResp?.status);
      throw new Error('沙箱创建失败');
    }

    const createData = await createResp.json();
    console.log('沙盒创建数据:', createData);
    
    if (!createData.success) {
      console.error('沙盒创建业务失败:', createData.error);
      throw new Error(createData.error || '沙箱创建失败');
    }
    
    console.log('沙盒创建成功');
  }, [repoInfo, settings?.isApiKeyValid]);

  /**
   * 处理沙箱状态变化
   * @param status 新的沙箱状态
   */
  const handleSandboxStatusChange = useCallback((status: string) => {
    console.log('处理沙盒状态变化:', status);
    
    // 根据状态决定是否继续轮询并一次性更新所有状态
    if (isStatusMatch(status, 'READY')) {
      console.log('沙盒已就绪，停止轮询并设置就绪状态');
      setSandboxState(prev => ({
        ...prev,
        status: normalizeStatus(status),
        isReady: true,
        isPolling: false,
        error: null
      }));
      if (pollingTimerRef.current) {
        clearTimeout(pollingTimerRef.current);
        pollingTimerRef.current = null;
      }
      isPollingRef.current = false;
      retryCountRef.current = 0; // 重置重试计数
      
      // 添加调试日志确认状态设置
      console.log('沙盒状态已设置为就绪:', {
        status: normalizeStatus(status),
        isReady: true,
        isPolling: false
      });
    } else if (isStatusMatch(status, 'FAILED')) {
      console.log('沙盒创建失败，停止轮询');
      setSandboxState(prev => ({
        ...prev,
        status: normalizeStatus(status),
        isReady: false,
        isPolling: false,
        error: '沙箱创建失败'
      }));
      if (pollingTimerRef.current) {
        clearTimeout(pollingTimerRef.current);
        pollingTimerRef.current = null;
      }
      isPollingRef.current = false;
      retryCountRef.current = 0; // 重置重试计数
    } else {
      // 其他状态，只更新状态和清除错误，保持当前的轮询状态
      setSandboxState(prev => ({
        ...prev,
        status: normalizeStatus(status),
        error: null
      }));
    }
  }, []);

  /**
   * 轮询沙箱状态
   */
  const pollSandboxStatus = useCallback(async () => {
    if (isPollingRef.current) return;

    // 如果大模型Key无效，不进入轮询
    if (!settings?.isApiKeyValid) {
      console.warn('跳过沙箱状态轮询：API Key 无效');
      setSandboxState(prev => ({
        ...prev,
        isPolling: false,
        error: messages.common?.invalidApiKey || 'Large Model Token invalid. Please configure it in Settings.'
      }));
      return;
    }
    
    // 检查重试次数限制
    if (retryCountRef.current >= POLLING_CONFIG.maxRetries) {
      console.warn('沙箱状态轮询达到最大重试次数，停止轮询');
      setSandboxState(prev => ({
        ...prev,
        status: 'QUERY_FAILED',
        isPolling: false,
        error: '轮询超时，请刷新页面重试'
      }));
      isPollingRef.current = false;
      return;
    }
    
    try {
      isPollingRef.current = true;
      console.log(`开始第${retryCountRef.current + 1}次沙盒状态查询`);
      const status = await querySandboxStatus();
      
      // 成功获取状态，重置错误计数
      retryCountRef.current = 0;
      
      console.log('轮询获得沙盒状态:', {
        原始状态: status,
        标准化状态: normalizeStatus(status),
        是否为READY: isStatusMatch(status, 'READY'),
        是否为NOT_CREATED: isStatusMatch(status, 'NOT_CREATED'),
        是否为最终状态: isFinalStatus(status)
      });
      
      if (isStatusMatch(status, 'NOT_CREATED')) {
        // 沙箱不存在，创建新的沙箱
        setSandboxState(prev => ({
          ...prev,
          status: 'CREATING',
          isPolling: true
        }));
        
        console.log('沙箱不存在，开始创建...');
        await createSandbox();
        console.log('沙箱创建请求已发送，继续轮询状态...');
        
        // 创建后继续轮询
        pollingTimerRef.current = setTimeout(pollSandboxStatus, POLLING_CONFIG.interval);
      } else {
        // 处理其他状态
        console.log('调用handleSandboxStatusChange处理状态:', status);
        handleSandboxStatusChange(status);
        
        // 如果状态不是最终状态，继续轮询
        if (!isFinalStatus(status)) {
          console.log('状态不是最终状态，继续轮询');
          pollingTimerRef.current = setTimeout(pollSandboxStatus, POLLING_CONFIG.interval);
        } else {
          console.log('状态是最终状态，停止轮询');
        }
      }
    } catch (error) {
      console.error('沙箱状态轮询失败:', error);
      retryCountRef.current++;
      
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      setSandboxState(prev => ({
        ...prev,
        status: 'QUERY_FAILED',
        isPolling: true, // 保持轮询状态，因为会继续重试
        error: `${errorMessage}`
      }));
      
      // 网络错误时使用更长的重试间隔
      const retryInterval = retryCountRef.current <= 3 ? POLLING_CONFIG.interval : POLLING_CONFIG.errorRetryInterval;
      console.log(`将在${retryInterval}ms后进行第${retryCountRef.current + 1}次重试`);
      pollingTimerRef.current = setTimeout(pollSandboxStatus, retryInterval);
    } finally {
      isPollingRef.current = false;
    }
  }, [querySandboxStatus, createSandbox, handleSandboxStatusChange, settings?.isApiKeyValid, messages.common?.invalidApiKey]);

  /**
   * 启动沙箱轮询
   */
  const startSandboxPolling = useCallback(() => {
    if (pollingTimerRef.current) {
      clearTimeout(pollingTimerRef.current);
    }

    // 如果大模型Key无效，不启动轮询
    if (!settings?.isApiKeyValid) {
      console.warn('未启动沙箱轮询：API Key 无效');
      setSandboxState(prev => ({
        ...prev,
        isPolling: false,
        error: messages.common?.invalidApiKey || 'Large Model Token invalid. Please configure it in Settings.'
      }));
      return;
    }

    // 重置重试计数
    retryCountRef.current = 0;
    setSandboxState(prev => ({
      ...prev,
      isPolling: true,
      error: null
    }));
    pollSandboxStatus();
  }, [pollSandboxStatus, settings?.isApiKeyValid, messages.common?.invalidApiKey]);

  /**
   * 停止沙箱轮询
   */
  const stopSandboxPolling = useCallback(() => {
    if (pollingTimerRef.current) {
      clearTimeout(pollingTimerRef.current);
      pollingTimerRef.current = null;
    }
    isPollingRef.current = false;
    retryCountRef.current = 0; // 重置重试计数
    setSandboxState(prev => ({
      ...prev,
      isPolling: false
    }));
  }, []);

  /**
   * 初始化沙箱轮询（仅在gemini-cli模式下）
   */
  useEffect(() => {
    console.log('useSandboxManager useEffect被触发:', {
      modelType,
      repoUrl: repoInfo?.repoUrl,
      branch: repoInfo?.branch,
      repoInfo: repoInfo ? 'exists' : 'null'
    });
    
    if (modelType !== 'gemini-cli') {
      console.log('不是gemini-cli模式，停止轮询');
      stopSandboxPolling();
      setSandboxState({
        status: '',
        isPolling: false,
        isReady: false,
        error: null
      });
      return;
    }
    
    if (!repoInfo?.repoUrl || !repoInfo?.branch) {
      console.log('仓库信息不完整，停止轮询', { 
        repoUrl: repoInfo?.repoUrl, 
        branch: repoInfo?.branch,
        repoInfoExists: !!repoInfo
      });
      // 如果不是gemini-cli模式，停止轮询并重置状态
      stopSandboxPolling();
      setSandboxState({
        status: '',
        isPolling: false,
        isReady: false,
        error: null
      });
      return;
    }

    // 如果大模型Key无效，停止轮询并提示
    if (!settings?.isApiKeyValid) {
      console.log('API Key 无效，停止沙箱轮询');
      stopSandboxPolling();
      setSandboxState({
        status: '',
        isPolling: false,
        isReady: false,
        error: messages.common?.invalidApiKey || 'Large Model Token invalid. Please configure it in Settings.'
      });
      return;
    }
    
    console.log('条件满足，启动沙盒轮询');
    // 启动轮询
    startSandboxPolling();

    // 清理函数
    return () => {
      console.log('useSandboxManager清理，停止轮询');
      stopSandboxPolling();
    };
  }, [modelType, repoInfo, repoInfo?.repoUrl, repoInfo?.branch, startSandboxPolling, stopSandboxPolling, settings?.isApiKeyValid, messages.common?.invalidApiKey]);

  /**
   * 手动检查沙盒状态（紧急修复用）
   */
  const manualCheckSandboxStatus = useCallback(async () => {
    console.log('手动检查沙盒状态');
    try {
      const status = await querySandboxStatus();
      handleSandboxStatusChange(status);
      
      if (isStatusMatch(status, 'READY')) {
        console.log('手动检查发现沙盒已就绪');
        setSandboxState(prev => ({
          ...prev,
          isReady: true,
          isPolling: false,
          status: normalizeStatus(status),
          error: null
        }));
      }
      
      return status;
    } catch (error) {
      console.error('手动检查沙盒状态失败:', error);
      throw error;
    }
  }, [querySandboxStatus, handleSandboxStatusChange]);

  /**
   * 强制设置沙盒为就绪状态（紧急修复用）
   */
  const forceSandboxReady = useCallback(() => {
    console.log('强制设置沙盒为就绪状态');
    setSandboxState(prev => ({
      ...prev,
      status: 'READY',
      isPolling: false,
      isReady: true,
      error: null
    }));
    if (pollingTimerRef.current) {
      clearTimeout(pollingTimerRef.current);
      pollingTimerRef.current = null;
    }
    isPollingRef.current = false;
    retryCountRef.current = 0;
  }, []);

  return {
    sandboxStatus: sandboxState.status,
    sandboxPolling: sandboxState.isPolling,
    sandboxReady: sandboxState.isReady,
    sandboxError: sandboxState.error,
    startSandboxPolling,
    stopSandboxPolling,
    // 紧急修复方法
    manualCheckSandboxStatus,
    forceSandboxReady
  };
}; 