'use client';

import { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import RepoInfo from '@/types/repoinfo';
import { createChatSSE, closeSSE, ChatCompletionRequest } from '@/utils/sseClient';
import getRepoUrl from '@/utils/getRepoUrl';
import { useSettings } from '@/contexts/SettingsContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { authFetch } from '@/utils/authFetch';
import { useSandboxManager } from './useSandboxManager';
import { getSandboxStatusDescription } from '@/utils/sandboxStatusMap';
import { general_chat_mistake_content, general_chat_mistake_title } from '@/const/chat';
// import { extractAtReferences } from '@/utils/atCompletionParser';

// 工具调用相关接口
interface ToolCall {
  id: string;
  type: string;
  function: {
    name: string;
    arguments: string;
  };
}

export interface ToolExecution {
  id: string;
  name: string;
  arguments: string;
  status: 'executing' | 'completed' | 'failed';
  result?: string;
  timestamp: number;
  conversationId: string;
  msgId?: string; // 关联到具体的消息ID（历史记录用）
}

// 新增：工具调用分组
export interface ToolExecutionGroup {
  conversationId: string;
  timestamp: number;
  userQuestion?: string; // 关联的用户问题
  tools: ToolExecution[];
  stats: {
    total: number;
    executing: number;
    completed: number;
    failed: number;
  };
}

// 工具信息的类型定义
interface ToolInfo {
  type: 'tool_call' | 'tool_execution_status' | 'tool_error';
  tool_calls?: ToolCall[];
  status?: string;
  error?: {
    message?: string;
    code?: string;
    type?: string;
  };
  timestamp?: number;
}

// 沙盒状态信息类型及类型守卫
interface SandboxStatusInfo {
  type: 'sandbox_status';
  status: string;
  message?: string;
  can_retry?: boolean;
  timestamp?: number;
}

const isSandboxStatusInfo = (value: unknown): value is SandboxStatusInfo => {
  if (!value || typeof value !== 'object') return false;
  const obj = value as Record<string, unknown>;
  return obj.type === 'sandbox_status' && typeof obj.status === 'string';
};

export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  toolCalls?: ToolCall[];
  toolExecutions?: ToolExecution[];
  createdAt?: number; // ms timestamp when message was created
  error?: string;
  provider?: 'gemini-cli' | 'whalecloud';
  file_references?: string; // JSON string from backend
  command_params?: string | { operation: string | null, param: string | null };
  msg_data?: string;
}

interface Model {
  id: string;
  name:string;
}

interface Provider {
  id: string;
  name: string;
  models: Model[];
  supportsCustomModel?: boolean;
}

interface ResearchStage {
  title: string;
  content: string;
  iteration: number;
  type: 'plan' | 'update' | 'conclusion';
}

interface FileReference {
  id: string;
  path: string;
  isDirectory: boolean;
  name: string;
}

interface UseChatProps {
  repoInfo: RepoInfo;
  sessionId: string;
  initialProvider?: string; // 替代modelType的简单provider参数
  initialModel?: string; // 初始模型参数
}

export const isClearSessionContext = (msg: Message) => {
  if (msg && msg.role === 'system' && msg.msg_data) {
    try {
      const msg_data = JSON.parse(msg.msg_data);
      return !!msg_data.clear_session_context;
    } catch (error) {
      console.error(error);
      return false;
    }
  }
  return false;
}

export const useChat = ({ repoInfo, sessionId, initialProvider, initialModel }: UseChatProps) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [question, setQuestion] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [deepResearch, setDeepResearch] = useState(false);
  const [modelType, setModelType] = useState<'whalecloud' | 'gemini-cli'>(() => {
    if (initialProvider === 'gemini-cli') return 'gemini-cli';
    return 'whalecloud';
  });
  const [selectedProvider, setSelectedProvider] = useState<string>(initialProvider || '');
  const [selectedModel, setSelectedModel] = useState<string>(initialModel || '');
  const [providers, setProviders] = useState<Provider[]>([]);
  const [availableModels, setAvailableModels] = useState<Model[]>([]);
  const [isModelsLoaded, setIsModelsLoaded] = useState(false);
  const [isInitialSetupComplete, setIsInitialSetupComplete] = useState(false);
  
  // State for Deep Research
  const [researchStages, setResearchStages] = useState<ResearchStage[]>([]);
  const [researchIteration, setResearchIteration] = useState(0);
  const [researchComplete, setResearchComplete] = useState(false);
  const [researchProgress, setResearchProgress] = useState({ current: 0, total: 5 });
  
  // State for Tool Calls
  const [toolExecutions, setToolExecutions] = useState<ToolExecution[]>([]);
  const [currentToolCalls, setCurrentToolCalls] = useState<ToolCall[]>([]);
  
  // 对话轮次状态管理
  const [currentConversationId, setCurrentConversationId] = useState<string>('');
  // 对话轮次与用户问题的映射
  const [conversationQuestionMap, setConversationQuestionMap] = useState<Map<string, string>>(new Map());
  
  console.log('useChat初始化，传入参数:', { 
    repoInfo: repoInfo ? { repoUrl: repoInfo.repoUrl, branch: repoInfo.branch } : null, 
    modelType, 
    initialProvider, 
    initialModel 
  });
  
  // 沙盒状态管理
  const {
    sandboxStatus,
    sandboxPolling,
    sandboxReady,
    sandboxError,
    startSandboxPolling,
    stopSandboxPolling,
    manualCheckSandboxStatus,
    forceSandboxReady
  } = useSandboxManager(repoInfo, modelType);
  
  console.log('useChat沙盒状态:', { 
    sandboxStatus, 
    sandboxPolling, 
    sandboxReady, 
    sandboxError 
  });
  
  const sseConnectionRef = useRef<{ close: () => void } | null>(null);
  // 记录被沙箱未就绪阻止的待发送问题
  const pendingQuestionRef = useRef<string | null>(null);
  const { settings } = useSettings();
  const { language, messages: i18nmessages } = useLanguage();
  const conversationHistoryRef = useRef<Message[]>([]);
  const currentMessageIdRef = useRef<string | null>(null);
  const sandboxStatusMessageIdRef = useRef<string | null>(null);

  useEffect(() => {
    conversationHistoryRef.current = messages;
  }, [messages]);

  // 解析工具调用和状态信息
  // const parseToolMessage = (message: string): { isToolMessage: boolean; toolInfo?: ToolInfo; content?: string } => {
  //   if (message.startsWith('TOOL_CALL:')) {
  //     try {
  //       const toolInfo = JSON.parse(message.substring(10));
  //       console.log('Parsed tool call:', toolInfo);
  //       return { isToolMessage: true, toolInfo };
  //     } catch (e) {
  //       console.error('Failed to parse tool call:', e, 'Message:', message);
  //       return { isToolMessage: false, content: message };
  //     }
  //   } else if (message.startsWith('TOOL_STATUS:')) {
  //     try {
  //       const statusInfo = JSON.parse(message.substring(12));
  //       console.log('Parsed tool status:', statusInfo);
  //       return { isToolMessage: true, toolInfo: statusInfo };
  //     } catch (e) {
  //       console.error('Failed to parse tool status:', e, 'Message:', message);
  //       return { isToolMessage: false, content: message };
  //     }
  //   } else if (message.startsWith('TOOL_ERROR:')) {
  //     try {
  //       const errorInfo = JSON.parse(message.substring(11));
  //       console.log('Parsed tool error:', errorInfo);
  //       return { isToolMessage: true, toolInfo: errorInfo };
  //     } catch (e) {
  //       console.error('Failed to parse tool error:', e, 'Message:', message);
  //       return { isToolMessage: false, content: message };
  //     }
  //   }
  //   return { isToolMessage: false, content: message };
  // };

  // 处理工具调用信息
  const handleToolInfo = (toolInfo: ToolInfo, forcedConversationId?: string) => {
    if (toolInfo.type === 'tool_call') {
      const newToolCalls = toolInfo.tool_calls || [];
      setCurrentToolCalls(prev => [...prev, ...newToolCalls]);
      
      // 优先使用传入的conversationId，其次使用状态中的
      const targetConversationId = forcedConversationId || currentConversationId;
      
      if (!targetConversationId) {
        console.warn('No conversationId available for tool execution, using fallback');
      }
      
      // 创建新的工具执行记录
      const newExecutions: ToolExecution[] = newToolCalls.map((call: ToolCall) => ({
        id: call.id,
        name: call.function.name,
        arguments: call.function.arguments,
        status: 'executing' as const,
        timestamp: toolInfo.timestamp ? toolInfo.timestamp * 1000 : Date.now(), // 转换为毫秒
        conversationId: targetConversationId || `conv_fallback_${Date.now()}` // 关联到当前对话轮次
      }));
      
      setToolExecutions(prev => [...prev, ...newExecutions]);
      console.log('Added tool executions with conversationId:', targetConversationId, newExecutions);
    } else if (toolInfo.type === 'tool_execution_status') {
      const status = toolInfo.status;
      
      // 更新最近添加的工具执行状态（按时间戳）
      setToolExecutions(prev => {
        const newExecutions = [...prev];
        if (newExecutions.length > 0) {
          // 找到最近的执行中的工具
          for (let i = newExecutions.length - 1; i >= 0; i--) {
            if (newExecutions[i].status === 'executing') {
              newExecutions[i].status = status === 'completed' ? 'completed' : 
                                       status === 'failed' ? 'failed' : 'executing';
              console.log(`Updated tool ${newExecutions[i].id} status to ${newExecutions[i].status}`);
            } else if (newExecutions[i].status === 'completed') {
              break;
            }
          }
        }
        return newExecutions;
      });
    } else if (toolInfo.type === 'tool_error') {
      const errorInfo = toolInfo.error;
      
      // 如果errorInfo不存在，使用默认错误信息
      if (!errorInfo) {
        const defaultErrorMessage = i18nmessages.useChat.toolExecutionUnknownError;
        setMessages(prev => [...prev, { 
          id: `chatcmpl-${crypto.randomUUID ? crypto.randomUUID() : Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)}`,
          role: 'assistant', 
          content: `⚠️ **${i18nmessages.useChat.toolExecutionFailedMessage}**\n\n${defaultErrorMessage}`,
          createdAt: Date.now(),
          error: toolInfo.error?.message,
          provider: selectedProvider as 'gemini-cli' | 'whalecloud'
        }]);
        
        // 创建一个新的失败工具记录
        const errorToolExecution: ToolExecution = {
          id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name: '工具调用',
          arguments: JSON.stringify({ error: 'Unknown tool error' }),
          status: 'failed',
          result: defaultErrorMessage,
          timestamp: toolInfo.timestamp ? toolInfo.timestamp * 1000 : Date.now(),
          conversationId: forcedConversationId || currentConversationId || `conv_fallback_${Date.now()}`
        };
        setToolExecutions(prev => [...prev, errorToolExecution]);
        return;
      }
      
      // 解析错误信息，提供更友好的提示
      let userFriendlyMessage = errorInfo.message || i18nmessages.useChat.unknownError;
      let errorTitle = i18nmessages.useChat.modelExecutionToolError;
      
      // 检查错误码和类型，提供特定的用户友好消息
      if (errorInfo.code?.includes('429') || userFriendlyMessage.includes('429') || userFriendlyMessage.includes('rate limit') || userFriendlyMessage.includes('quota')) {
        errorTitle = '🚫 ' + i18nmessages.useChat.modelQuotaLimit;
        userFriendlyMessage = i18nmessages.useChat.modelQuotaLimitDescription;
      }
      else if (errorInfo.code?.includes('400') || userFriendlyMessage.includes('400') || userFriendlyMessage.includes('bad request')) {
        // 检查是否是 GenerateContentRequest 相关的错误
        if (userFriendlyMessage.includes('GenerateContentRequest') && userFriendlyMessage.includes('function_response.name')) {
          errorTitle = '🔧 ' + i18nmessages.useChat.toolCallParameterError;
          userFriendlyMessage = i18nmessages.useChat.toolCallParameterErrorDescription;
        } else if (userFriendlyMessage.includes('api_forward_request_error')) {
          errorTitle = '⚠️ ' + i18nmessages.useChat.apiForwardError;
          userFriendlyMessage = i18nmessages.useChat.apiForwardErrorDescription;
        } else if (userFriendlyMessage.includes('工具执行超时')) {
          errorTitle = '⚠️ ' + i18nmessages.useChat.modelCallTimeout;
          userFriendlyMessage = i18nmessages.useChat.modelCallTimeoutDescription;
        } else if (userFriendlyMessage.includes('超时')) {
          errorTitle = '⚠️ ' + i18nmessages.useChat.modelServiceTimeout;
          userFriendlyMessage = i18nmessages.useChat.modelServiceTimeoutDescription;
        } else {
          errorTitle = '⚠️ ' + i18nmessages.useChat.modelServiceError;
          userFriendlyMessage = i18nmessages.useChat.modelServiceErrorDescription;
        }
      } else if (errorInfo.code?.includes('401')) {
        errorTitle = i18nmessages.useChat.apiKeyError;
        userFriendlyMessage = i18nmessages.useChat.apiKeyErrorDescription;
      }
       else if (userFriendlyMessage.includes('API错误') || userFriendlyMessage.includes('GenerateContentRequest')) {
        errorTitle = '🔧 ' + i18nmessages.useChat.apiCallError;
        userFriendlyMessage = i18nmessages.useChat.apiCallErrorDescription;
      }
      
      // 添加错误消息到聊天界面（而不是工具面板）
      setMessages(prev => [...prev, { 
        id: `chatcmpl-${crypto.randomUUID ? crypto.randomUUID() : Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)}`,
        role: 'assistant', 
        content: `⚠️ **${errorTitle}**\n\n${userFriendlyMessage}`,
        createdAt: Date.now(),
        error: toolInfo.error?.message,
        provider: selectedProvider as 'gemini-cli' | 'whalecloud'
      }]);
      
      console.log('Added error message to chat:', { title: errorTitle, message: userFriendlyMessage });
      
      // 处理工具执行失败：先尝试将执行中的工具标记为失败，如果没有则创建新的失败记录
      setToolExecutions(prev => {
        const newExecutions = [...prev];
        let foundExecutingTool = false;
        
        // 尝试找到最近的执行中工具并标记为失败
        for (let i = newExecutions.length - 1; i >= 0; i--) {
          if (newExecutions[i].status === 'executing') {
            newExecutions[i].status = 'failed';
            newExecutions[i].result = userFriendlyMessage;
            console.log(`Marked tool ${newExecutions[i].id} as failed due to error`);
            foundExecutingTool = true;
            break;
          }
        }
        
        // 如果没有找到执行中的工具，创建一个新的失败工具记录
        if (!foundExecutingTool) {
          // 根据错误类型确定工具名称
          let toolName = '工具调用';
          if (userFriendlyMessage.includes('GenerateContentRequest') && userFriendlyMessage.includes('function_response.name')) {
            toolName = '工具参数错误';
          } else if (userFriendlyMessage.includes('api_forward_request_error')) {
            toolName = 'API转发错误';
          } else if (errorInfo.code === '400') {
            toolName = 'API错误';
          }
          
          const errorToolExecution: ToolExecution = {
            id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            name: toolName,
            arguments: JSON.stringify({ 
              error_type: errorInfo.type || 'unknown',
              error_code: errorInfo.code || 0,
              original_message: errorInfo.message || '未知错误'
            }),
            status: 'failed',
            result: userFriendlyMessage,
            timestamp: toolInfo.timestamp ? toolInfo.timestamp * 1000 : Date.now(),
            conversationId: forcedConversationId || currentConversationId || `conv_fallback_${Date.now()}`
          };
          newExecutions.push(errorToolExecution);
          console.log('Created new failed tool execution:', errorToolExecution);
        }
        
        return newExecutions;
      });
    }
  };

  const checkIfResearchComplete = (content: string): boolean => {
    if (content.includes('## Final Conclusion')) return true;
    if ((content.includes('## Conclusion') || content.includes('## Summary')) && !content.includes('Next Steps')) return true;
    if (content.includes('This concludes our research') || content.includes('This completes our investigation')) return true;
    return false;
  };

  const extractResearchStage = (content: string, iteration: number): ResearchStage | null => {
    let title: string;
    let type: 'plan' | 'update' | 'conclusion' = 'update';

    if (content.includes('## Final Conclusion')) {
      title = 'Final Conclusion';
      type = 'conclusion';
    } else if (iteration === 1 && content.includes('## Research Plan')) {
      title = 'Research Plan';
      type = 'plan';
    } else if (content.includes('## Research Update')) {
      title = `Research Update ${iteration}`;
    } else {
      title = `Research Iteration ${iteration}`;
    }

    if (content.trim()) {
      return { title, content, iteration, type };
    }
    return null;
  };

  // 检查是否为API错误信息
  const isApiErrorMessage = (message: string): boolean => {
    // 检查是否为JSON格式的API错误
    try {
      const parsed = JSON.parse(message);
      if (parsed.error && parsed.status_code) {
        return true;
      }
    } catch {
      // 不是JSON，继续其他检查
    }
    
    // 检查是否包含WCT API错误特征（API错误(400): {...}）
    if (message.includes('API错误(') && (message.includes('400') || message.includes('429'))) {
      return true;
    }
    
    // 检查是否包含API错误特征
    if (message.includes('API错误') && message.includes('error')) {
      return true;
    }
    
    // 检查是否包含Gemini API错误特征
    if (message.includes('GenerateContentRequest') && message.includes('function_response.name')) {
      return true;
    }
    
    // 检查是否包含api_forward_request_error特征
    if (message.includes('api_forward_request_error')) {
      return true;
    }
    
    // 检查是否包含429或400错误码
    // if (message.includes('429') || message.includes('400')) {
    //   return true;
    // }
    
    return false;
  };

  // 解析并处理API错误，返回用户友好的错误信息
  const parseApiError = (message: string): { title: string; content: string } => {
    let errorTitle = i18nmessages.useChat.modelApiError;
    let errorContent = i18nmessages.useChat.modelApiErrorDescription;
    
    // 检查429错误码
    if (message.includes('429') || message.toLowerCase().includes('rate limit') || message.toLowerCase().includes('quota')) {
      errorTitle = '🚫 ' + i18nmessages.useChat.modelQuotaLimit;
      errorContent = i18nmessages.useChat.modelQuotaLimitDescription;
    }
    // 检查400错误码和具体的错误类型
    else if (message.includes('400') || message.toLowerCase().includes('bad request')) {
      // 检查是否是 GenerateContentRequest 相关的错误
      if (message.includes('GenerateContentRequest') && message.includes('function_response.name')) {
        errorTitle = '🔧 ' + i18nmessages.useChat.toolCallParameterError;
        errorContent = i18nmessages.useChat.toolCallParameterErrorDescription;
      } else if (message.includes('api_forward_request_error')) {
        errorTitle = '⚠️ ' + i18nmessages.useChat.apiForwardError;
        errorContent = i18nmessages.useChat.apiForwardErrorDescription;
      } else if (message.includes('工具执行超时')) {
        errorTitle = '⚠️ ' + i18nmessages.useChat.modelCallTimeout;
        errorContent = i18nmessages.useChat.modelCallTimeoutDescription;
      } else if (message.includes('超时')) {
        errorTitle = '⚠️ ' + i18nmessages.useChat.modelServiceTimeout;
        errorContent = i18nmessages.useChat.modelServiceTimeoutDescription;
      }
      else {
        errorTitle = '⚠️ ' + i18nmessages.useChat.modelServiceError;
        errorContent = i18nmessages.useChat.modelServiceErrorDescription;
      }
    }
    else if (message.includes('401')){
      errorTitle = i18nmessages.useChat.apiKeyError;
      errorContent = i18nmessages.useChat.apiKeyErrorDescription;
    }
    // 其他API错误
    else if (message.includes('GenerateContentRequest') || message.includes('API错误')) {
      errorTitle = '🔧 ' + i18nmessages.useChat.apiCallError;
      errorContent = i18nmessages.useChat.apiCallErrorDescription;
    }
    
    return { title: errorTitle, content: errorContent };
  };

  // 解析消息中的进度信息
  const parseProgressInfo = (message: string) => {
    const progressRegex = /<!-- PROGRESS_INFO: (.*?) -->/;
    const match = message.match(progressRegex);
    
    if (match && match[1]) {
      try {
        const progressInfo = JSON.parse(match[1]);
        if (progressInfo.type === 'research_progress') {
          setResearchProgress({ 
            current: progressInfo.current, 
            total: progressInfo.total 
          });
          
          // 如果是最终迭代，标记为完成
          if (progressInfo.is_final) {
            setResearchComplete(true);
          }
          
          // 返回去除进度信息后的消息
          return message.replace(match[0], '');
        }
      } catch (e) {
        console.error('Error parsing progress info:', e);
      }
    }
    
        return message;
   };

   // 接收后端SSE推送的沙盒状态，并以加载风格展示/更新
   const renderSandboxStatusText = (info: SandboxStatusInfo): string => {
     const status = info?.status || 'UNKNOWN';
     const msg = info?.message || '';
     const desc = getSandboxStatusDescription(status, language);
          if (status === 'READY') return `✅ ${desc}`;
      if (status === 'CREATING' || status === 'INITIALIZING' || status === 'RECREATING' || status === 'PENDING' || status === 'RUNNING') {
        return `⏳ ${desc}${msg ? `\n\n${msg}` : ''}`;
      }
      if (status === 'CREATE_FAILED' || status === 'RECREATE_FAILED' || status === 'URL_FAILED' || status === 'QUERY_FAILED' || status === 'FAILED' || status === 'ERROR' || status === 'TIMEOUT' || status === 'QUOTA_EXCEEDED' || status === 'TOTAL_CAPACITY_FULL' || status === 'SYSTEM_BUSY') {
        return `⚠️ ${desc}${msg ? `\n\n${msg}` : ''}`;
      }
      return `⏳ ${desc}${msg ? `\n\n${msg}` : ''}`;
   };

     const upsertSandboxStatusMessage = useCallback((info: SandboxStatusInfo) => {
    const text = renderSandboxStatusText(info);
    setMessages(prev => {
      const id = sandboxStatusMessageIdRef.current;
      if (id) {
        return prev.map(m => (m.id === id ? { ...m, content: text } : m));
      }
      const newId = `sandbox-${Date.now()}`;
      sandboxStatusMessageIdRef.current = newId;
      return [...prev, { id: newId, role: 'assistant', content: text, createdAt: Date.now() }];
    });
    if (info?.status === 'READY') {
      try { forceSandboxReady(); } catch {}
      const id = sandboxStatusMessageIdRef.current;
      if (id) {
        setTimeout(() => {
          setMessages(prev => prev.filter(m => m.id !== id));
          sandboxStatusMessageIdRef.current = null;
        }, 1500);
      }
    }
  }, [setMessages, forceSandboxReady]);

  // 处理文件引用，读取文件内容
  // const processFileReferences = useCallback(async (messageContent: string): Promise<string> => {
  //   const fileReferences = extractAtReferences(messageContent);
    
  //   if (fileReferences.length === 0) {
  //     return messageContent;
  //   }

  //   let processedContent = messageContent;
  //   const fileContents: string[] = [];

  //   for (const filePath of fileReferences) {
  //     try {
  //       // 这个功能暂时禁用，因为我们需要在 ChatInput 组件级别处理文件引用
  //       // 而不是在发送到后端之前处理
  //       continue;
  //     } catch (error) {
  //       console.error(`读取文件 ${filePath} 失败:`, error);
  //     }
  //   }

  //   // 将文件内容添加到消息末尾
  //   if (fileContents.length > 0) {
  //     processedContent += '\n\n以下是引用的文件内容：\n' + fileContents.join('\n');
  //   }

  //   return processedContent;
  // }, [repoInfo]);
   
   const handleSubmit = useCallback(async (currentQuestion: string, fileReferences?: FileReference[], commandParams?: { operation: string | null, param: string | null }, images?: Array<string>) => {
    if (!currentQuestion.trim() || isLoading) return;
    
    // 创建新的对话轮次
    const newConversationId = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    setCurrentConversationId(newConversationId);
    
    // 在流式处理中使用闭包捕获这个ID
    const conversationIdForThisRound = newConversationId;

    // 保存用户问题和conversationId的映射
    setConversationQuestionMap(prev => {
      const newMap = new Map(prev);
      newMap.set(conversationIdForThisRound, currentQuestion);
      return newMap;
    });
    
    console.log('handleSubmit被调用，当前状态:', {
      modelType,
      sandboxReady,
      sandboxStatus,
      sandboxPolling,
      sandboxError,
      currentQuestion: currentQuestion.length > 50 ? currentQuestion.substring(0, 50) + '...' : currentQuestion,
      newConversationId,
      conversationIdForThisRound
    });
    
          // gemini-cli模式下不再阻止发送消息，沙盒状态由后端SSE推送
      if (modelType === 'gemini-cli') {
        console.log('发送消息（不阻塞），沙盒状态由后端SSE推送处理:', {
          sandboxReady,
          sandboxStatus,
          sandboxPolling,
          sandboxError,
          modelType
        });
      }
    
    setQuestion('');
    
    // 清空工具调用状态
    setCurrentToolCalls([]);

    // 如果开启了深度研究模式
    if (deepResearch) {
      // 1. 重置研究状态
      setResearchStages([]);
      setResearchComplete(false);
      setResearchIteration(0);
      setResearchProgress({ current: 0, total: 5 });

      // 2. 将清理后的用户问题添加到UI，并准备后端消息
      const cleanQuestion = currentQuestion.replace(/(\[DEEP RESEARCH\]\s*)+/g, '').trim();
      const userMessage: Message = { id: `chatcmpl-${crypto.randomUUID ? crypto.randomUUID() : Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)}`, role: 'user', content: cleanQuestion, createdAt: Date.now(), file_references: JSON.stringify(fileReferences || []), command_params: commandParams ? JSON.stringify(commandParams) : undefined };
      setMessages(prev => [...prev, userMessage]);

      // 3. 准备发送到后端的消息 (包含历史记录和带标记的新问题)
      const taggedUserMessage: Message = { id: `chatcmpl-${crypto.randomUUID ? crypto.randomUUID() : Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)}`, role: 'user', content: `[DEEP RESEARCH] ${cleanQuestion}` };
      const messagesForBackend = [...messages, taggedUserMessage];
      
      // 4. 更新会话历史记录引用，为后续的continue做准备
      conversationHistoryRef.current = messagesForBackend;
      
      setIsLoading(true);

      // 新增：发起SSE前检查API Key有效性
      if (!settings?.isApiKeyValid) {
        setIsLoading(false);
        setMessages(prev => [...prev, { id: '', role: 'assistant', content: (i18nmessages.common?.invalidApiKey || 'Large Model Token invalid. Please configure it in Settings.'), createdAt: Date.now() }]);
        return;
      }

      // 过滤掉清空上下文前的消息
      const clear_index = messagesForBackend.findIndex(item => isClearSessionContext(item));
      const finalMessages = clear_index === -1 ? messagesForBackend : messagesForBackend.slice(clear_index + 1);

      const requestBody: ChatCompletionRequest = {
        messages: finalMessages.map(msg => ({ id: msg.id, role: msg.role, content: msg.content })),
        repo_url: repoInfo.repoUrl || getRepoUrl(repoInfo),
        branch: repoInfo.branch,
        type: repoInfo.type,
        provider: selectedProvider,
        model: selectedModel,
        language: language,
        session_id: sessionId,
        token: repoInfo.token ?? undefined,
        file_references: fileReferences || [],
        caller: 1,
        wiki_id: repoInfo.wiki_id,
      };

      closeSSE(sseConnectionRef.current);
      let fullResponse = '';
      
      sseConnectionRef.current = createChatSSE(
          requestBody,
                    (message: string, event?: string, id?: string) => { // onMessage (接收研究计划)
                                // 如果id不为空，则更新消息的id,防止报错后前后端id不一致
          if (id) {
            setMessages(prev => {
              const updatedMessages = [...prev];
              if(updatedMessages.length > 0) {
                updatedMessages[updatedMessages.length - 1].id = id;
              }
              return updatedMessages;
            });
          }
               // 根据event类型处理工具调用消息
               if (event === 'tool_call' || event === 'tool_execution_status' || event === 'tool_error') {
                 try {
                   let jsonData = message;
                   // 如果消息带有前缀，需要先去除前缀
                   if (event === 'tool_call' && message.startsWith('TOOL_CALL:')) {
                     jsonData = message.substring(10);
                   } else if (event === 'tool_execution_status' && message.startsWith('TOOL_STATUS:')) {
                     jsonData = message.substring(12);
                   } else if (event === 'tool_error' && message.startsWith('TOOL_ERROR:')) {
                     jsonData = message.substring(11);
                   }
                   const toolInfo = JSON.parse(jsonData);
                   toolInfo.type = event; // 设置工具信息类型
                   // 关键：传入当前轮次的conversationId
                   handleToolInfo(toolInfo, conversationIdForThisRound);
                 } catch (e) {
                   console.error(`Failed to parse ${event} message:`, e, 'Message:', message);
                 }
                 return; // 工具调用消息不添加到聊天中
               }
               
                              // 处理data事件，解析content字段
               let content = message;
               let error = false;
               if (event === 'data') {
                 try {
                   const dataObj = JSON.parse(message);
                   content = dataObj.content;
                   if ('error' in dataObj) {
                    error = dataObj.error;
                   }
                 } catch (e) {
                   // 如果解析失败，保持原始消息
                   console.warn('Failed to parse data message as JSON, using raw message:', e);
                 }
               }

               // 优先处理沙盒状态消息
               try {
                 const maybeStatus = JSON.parse(content) as unknown;
                 if (isSandboxStatusInfo(maybeStatus)) {
                   upsertSandboxStatusMessage(maybeStatus);
                   return;
                 }
               } catch {}
 
                if (id && !currentMessageIdRef.current) {
                 // 存储从后端接收到的消息ID
                 currentMessageIdRef.current = id;
                 console.log('Received message ID from backend:', id);
               }
               
               // 检查是否为API错误消息
               if (error || isApiErrorMessage(content)) {
                 console.log('Detected API error in research:', content);
                 const { title, content: errorContent } = parseApiError(content);
                 setMessages(prev => [...prev, { 
                   id:"",
                   role: 'assistant', 
                   content: `**${title}**\n\n${errorContent}`,
                   createdAt: Date.now(),
                   error: content
                 }]);
                 return; // 不继续处理研究内容
               }
               
               const processedMessage = parseProgressInfo(content);
               if (processedMessage) {
                 fullResponse += processedMessage;
                 const stage = extractResearchStage(fullResponse, 1);
                 if (stage) {
                     setResearchStages([stage]);
                 }
               }
          },
          (error: Event) => {
              console.error('SSE error when research:', error);
              setMessages(prev => [...prev, { id: prev[-1].id || `chatcmpl-${crypto.randomUUID ? crypto.randomUUID() : Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)}`, role: 'assistant', content: `**${general_chat_mistake_title}**\n\n${general_chat_mistake_content}`, createdAt: Date.now(), error: error.toString() }]);
              setIsLoading(false);
          },
          () => { // onClose (研究计划接收完毕)
              const isComplete = checkIfResearchComplete(fullResponse);
              setResearchComplete(isComplete);
              
              // 使用研究计划更新会话历史
              // 使用从后端接收到的ID（如果有的话），否则生成一个新的ID
              const messageId = currentMessageIdRef.current || `chatcmpl-${crypto.randomUUID ? crypto.randomUUID() : Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)}`;
              conversationHistoryRef.current = [...messagesForBackend, { id: messageId, role: 'assistant', content: fullResponse }];

              if (!isComplete) {
                  setResearchIteration(1); // 触发useEffect以调用continueResearch
                  setResearchProgress({ current: 1, total: 5 });
                  setIsLoading(false); // 允许下一次迭代触发
              } else {
                  setIsLoading(false);
                  setResearchProgress({ current: 5, total: 5 });
                  // 如果一步就完成，则直接将结果添加到主聊天界面
                  // 使用从后端接收到的ID（如果有的话），否则生成一个新的ID
                  const messageId = currentMessageIdRef.current || `chatcmpl-${crypto.randomUUID ? crypto.randomUUID() : Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)}`;
                  setMessages(prev => [...prev, { id: messageId, role: 'assistant', content: fullResponse, createdAt: Date.now() }]);
                  // 重置currentMessageIdRef
                  currentMessageIdRef.current = null;
              }
               closeSSE(sseConnectionRef.current);
          },
          settings.apiKey,
          settings.whaleDevCloudToken
      );

    } else {
      // --- 普通聊天逻辑 ---
      const userMessage: Message = { id: `chatcmpl-${crypto.randomUUID ? crypto.randomUUID() : Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)}`, role: 'user', content: currentQuestion, createdAt: Date.now(), file_references: JSON.stringify(fileReferences || []), command_params: commandParams ? JSON.stringify(commandParams) : undefined };
      if (images) {
        userMessage["msg_data"] = JSON.stringify({ images });
      }
      setMessages(prev => [...prev, userMessage]);
      const newMessages = [...messages, userMessage];

      setIsLoading(true); // 设置加载状态

      // 新增：发起SSE前检查API Key有效性（普通对话）
      if (!settings?.isApiKeyValid) {
        setMessages(prev => [...prev, { id: '', role: 'assistant', content: (i18nmessages.common?.invalidApiKey || 'Large Model Token invalid. Please configure it in Settings.'), createdAt: Date.now() }]);
        setIsLoading(false);
        return;
      }

      // 过滤掉清空上下文前的消息
      const clear_index = newMessages.findIndex(item => isClearSessionContext(item));
      const finalMessages = clear_index === -1 ? newMessages : newMessages.slice(clear_index + 1);

      const requestBody: ChatCompletionRequest = {
        messages: finalMessages.slice(-10).map(msg => ({ id:msg.id, role: msg.role, content: msg.content })),
        repo_url: repoInfo.repoUrl || getRepoUrl(repoInfo),
        type: repoInfo.type,
        branch: repoInfo.branch,
        provider: selectedProvider,
        model: selectedModel,
        language: language,
        session_id: sessionId,
        token: repoInfo.token ?? undefined,
        file_references: fileReferences || [],
        wiki_id: repoInfo.wiki_id,
        command_params: commandParams,
        images: images
      };
      
      closeSSE(sseConnectionRef.current);
      
      let fullResponse = '';
      const assistantMessage: Message = { id: `chatcmpl-${crypto.randomUUID ? crypto.randomUUID() : Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)}`, role: 'assistant', content: '', provider: (selectedProvider || initialProvider) as 'gemini-cli' | 'whalecloud' };
      setMessages(prev => [...prev, assistantMessage]);
      sseConnectionRef.current = createChatSSE(
        requestBody,
        (message: string, event?: string, id?: string) => {
          
          // 如果id不为空，则更新消息的id,防止报错后前后端id不一致
          if (id) {
            setMessages(prev => {
              const updatedMessages = [...prev];
              if(updatedMessages.length > 0) {
                updatedMessages[updatedMessages.length - 1].id = id;
              }
              return updatedMessages;
            });
          }

          // 根据event类型处理工具调用消息
          if (event === 'tool_call' || event === 'tool_execution_status' || event === 'tool_error') {
            try {
              let jsonData = message;
              // 如果消息带有前缀，需要先去除前缀
              if (event === 'tool_call' && message.startsWith('TOOL_CALL:')) {
                jsonData = message.substring(10);
              } else if (event === 'tool_execution_status' && message.startsWith('TOOL_STATUS:')) {
                jsonData = message.substring(12);
              } else if (event === 'tool_error' && message.startsWith('TOOL_ERROR:')) {
                jsonData = message.substring(11);
              }
              
              const toolInfo = JSON.parse(jsonData);
              toolInfo.type = event; // 设置工具信息类型
              // 传入当前轮次的conversationId
              handleToolInfo(toolInfo, conversationIdForThisRound);
              return; // 工具调用消息不添加到聊天中
            } catch (e) {
              console.error(`Failed to parse ${event} message:`, e);
              console.error('Message length:', message.length);
              console.error('First 500 chars:', message);
              
              // 容错处理：只处理 tool_call 情况，提取工具名称和参数
              if (event === 'tool_call') {
                try {
                  // 尝试提取工具ID、名称和参数
                  const toolIdMatch = message.match(/"id":\s*"([^"]+)"/);
                  const toolNameMatch = message.match(/"name":\s*"([^"]+)"/);
                  const toolArgsMatch = message.match(/"arguments":\s*"([^"]+)"/);
                  
                  if (toolNameMatch) {
                    // 构建工具调用信息，直接使用提取到的 arguments（即使被截断）
                    const fallbackToolInfo = {
                      type: 'tool_call' as const,
                      tool_calls: [{
                        id: toolIdMatch ? toolIdMatch[1] : 'fallback-tool-call',
                        type: 'function',
                        function: {
                          name: toolNameMatch[1],
                          arguments: toolArgsMatch ? toolArgsMatch[1] : '{}'
                        }
                      }]
                    };
                    
                    console.log('Using fallback tool info due to JSON parse failure:', fallbackToolInfo);
                    handleToolInfo(fallbackToolInfo, conversationIdForThisRound);
                  }
                } catch (fallbackError) {
                  console.error('Fallback tool info extraction failed:', fallbackError, message);
                }
              }
              
              return; // 确保不泄露到会话
            }
          }
          
          // 检测并过滤工具调用内容（防止泄漏到会话）
          if (!event || (event !== 'data' && event !== 'sandbox_status')) {
            // 如果消息包含工具调用的明显特征，直接忽略
            if (message.includes('"tool_calls"') || 
                message.includes('"function"') || 
                message.includes('"arguments"') ||
                message.includes('"timestamp"') ||
                message.includes('"status"') ||
                message.includes('"executing"') ||
                message.includes('"completed"') ||
                message.includes('"failed"')) {
              console.log('Filtered tool call content from leaking to chat:', message);
              return; // 忽略工具调用内容
            }
          }
          
          // 处理data事件，解析content字段
          let content = message;
          let error = false;
          if (event === 'data') {
            try {
              const dataObj = JSON.parse(message);
              content = dataObj.content;
              if ('error' in dataObj) {
                error = dataObj.error;
               }
            } catch (e) {
              // 如果解析失败，保持原始消息
              console.warn('Failed to parse data message as JSON, using raw message:', e);
            }
          }
          
          // 优先处理沙盒状态消息
          try {
            const maybeStatus = JSON.parse(content) as unknown;
            if (isSandboxStatusInfo(maybeStatus)) {
              upsertSandboxStatusMessage(maybeStatus);
              return;
            }
          } catch {}

          // 检查是否为API错误消息
          if (error || isApiErrorMessage(content)) {
            console.log('Detected API error in chat:', content);
            const { title, content: errorContent } = parseApiError(content);
            setMessages(prev => {
              const updatedMessages = [...prev];
              if(updatedMessages.length > 0) {
                updatedMessages[updatedMessages.length - 1].content = `**${title}**\n\n${errorContent}`;
                updatedMessages[updatedMessages.length - 1].provider = (selectedProvider || initialProvider) as 'gemini-cli' | 'whalecloud';
                updatedMessages[updatedMessages.length - 1].createdAt = Date.now();
                updatedMessages[updatedMessages.length - 1].error = content;
              }
              return updatedMessages;
            });
            return; // 不继续处理，避免显示原始错误信息
          }
          
          const processedMessage = parseProgressInfo(content);
          if (processedMessage) { // 只有非空消息才添加到聊天中
            fullResponse += processedMessage;
            setMessages(prev => {
                const updatedMessages = [...prev];
                if(updatedMessages.length > 0) {
                  updatedMessages[updatedMessages.length - 1].content = fullResponse;
                  updatedMessages[updatedMessages.length - 1].id = id ? id : '';
                  updatedMessages[updatedMessages.length - 1].provider = (selectedProvider || initialProvider) as 'gemini-cli' | 'whalecloud';
                }
                return updatedMessages;
            });
          }
        },
        (error: Event) => {
          console.error('SSE error when chat:', error);
          setMessages(prev => [...prev.slice(0, -1), { id: prev[-1].id || `chatcmpl-${crypto.randomUUID ? crypto.randomUUID() : Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)}`, role: 'assistant', content: `**${general_chat_mistake_title}**\n\n${general_chat_mistake_content}`, createdAt: Date.now(), error: error.toString(), provider: selectedProvider as 'gemini-cli' | 'whalecloud' }]);
          setIsLoading(false);
        },
        () => {
          // SSE closed: set assistant message end time as createdAt for display
          setMessages(prev => {
            const updated = [...prev];
            if (updated.length > 0) {
              const last = updated[updated.length - 1];
              if (last.role === 'assistant' && !last.createdAt) {
                last.createdAt = Date.now();
                last.provider = (selectedProvider || initialProvider) as 'gemini-cli' | 'whalecloud';
              }
            }
            return updated;
          });
          setIsLoading(false);
        },
        settings.apiKey,
        settings.whaleDevCloudToken
      );
    }
  }, [isLoading, modelType, sandboxReady, sandboxStatus, sandboxPolling, sandboxError, messages, repoInfo, sessionId, selectedProvider, selectedModel, language, settings, deepResearch, upsertSandboxStatusMessage]);

  // 沙盒就绪后，自动发送之前被阻止的问题
  useEffect(() => {
    if (modelType === 'gemini-cli' 
        && sandboxReady 
        && pendingQuestionRef.current 
        && !isLoading) {
      const pending = pendingQuestionRef.current;
      pendingQuestionRef.current = null;
      console.log('检测到沙盒就绪，自动发送待处理问题:', pending);
      // 清空输入框并触发发送
      setQuestion('');
      // 直接调用提交逻辑
      handleSubmit(pending);
    }
  }, [modelType, sandboxReady, isLoading, handleSubmit]);

  const continueResearch = useCallback(async () => {
    if (researchComplete || isLoading) return;

    setIsLoading(true);
    const newIteration = researchIteration + 1;
    setResearchIteration(newIteration);
    setResearchProgress(prev => ({ current: newIteration, total: prev.total }));

    // 后端需要"Continue"指令来触发下一步
    const continueMessage: Message = { id: `chatcmpl-${crypto.randomUUID ? crypto.randomUUID() : Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)}`, role: 'user', content: '[DEEP RESEARCH] Continue the research' };
    const messagesForBackend = [...conversationHistoryRef.current, continueMessage];

    const requestBody: ChatCompletionRequest = {
      messages: messagesForBackend.map(msg => ({ id: msg.id, role: msg.role, content: msg.content })),
      repo_url: repoInfo.repoUrl || getRepoUrl(repoInfo),
      branch: repoInfo.branch,
      type: repoInfo.type,
      provider: selectedProvider,
      model: selectedModel,
      language: language,
      session_id: sessionId,
      token: repoInfo.token ?? undefined,
      file_references: [],
      wiki_id: repoInfo.wiki_id,
    };

    closeSSE(sseConnectionRef.current);
    
    let fullResponse = '';
    
    // 新增：发起SSE前检查API Key有效性（继续研究）
    if (!settings?.isApiKeyValid) {
      setMessages(prev => [...prev, { id: '', role: 'assistant', content: (i18nmessages.common?.invalidApiKey || 'Large Model Token invalid. Please configure it in Settings.'), createdAt: Date.now() }]);
      setIsLoading(false);
      return;
    }

    sseConnectionRef.current = createChatSSE(
      requestBody,
      (message: string, event?: string, id?: string) => { // onMessage
        // 如果id不为空，则更新消息的id,防止报错后前后端id不一致
        if (id) {
          setMessages(prev => {
            const updatedMessages = [...prev];
            if(updatedMessages.length > 0) {
              updatedMessages[updatedMessages.length - 1].id = id;
            }
            return updatedMessages;
          });
        }
        
        // 处理data事件，解析content字段
        let content = message;
        let error = false;
        if (event === 'data') {
          try {
            const dataObj = JSON.parse(message);
            content = dataObj.content;
            if ('error' in dataObj) {
              error = dataObj.error;
            }
          } catch (e) {
            // 如果解析失败，保持原始消息
            console.warn('Failed to parse data message as JSON, using raw message:', e);
          }
        }
        
        // 优先处理沙盒状态消息
        try {
          const maybeStatus = JSON.parse(content) as unknown;
          if (isSandboxStatusInfo(maybeStatus)) {
            upsertSandboxStatusMessage(maybeStatus);
            return;
          }
        } catch {}

        // 检查是否为API错误消息
        if (error || isApiErrorMessage(content)) {
          console.log('Detected API error in continue research:', content);
          const { title, content: errorContent } = parseApiError(content);
          setMessages(prev => [...prev, {
            id: '',
            role: 'assistant', 
            content: `**${title}**\n\n${errorContent}`,
            createdAt: Date.now(),
            error: content
          }]);
          return; // 不继续处理研究内容
        }

        if(id) {
          currentMessageIdRef.current = id
        }
        
        const processedMessage = parseProgressInfo(content);
        if (processedMessage.trim()) {
          fullResponse += processedMessage;

          const stage = extractResearchStage(fullResponse, newIteration);
          if (stage) {
            setResearchStages(prev => {
              const existingIndex = prev.findIndex(s => s.iteration === newIteration);
              if (existingIndex > -1) {
                const newStages = [...prev];
                newStages[existingIndex] = stage;
                return newStages;
              }
              return [...prev, stage];
            });
          }
        }
      },
      (error: Event) => { // onError
        console.error('SSE error when continue research:', error);
        setMessages(prev => [...prev, { id: `chatcmpl-${crypto.randomUUID ? crypto.randomUUID() : Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)}`, role: 'assistant', content: `**${general_chat_mistake_title}**\n\n${general_chat_mistake_content}`, createdAt: Date.now(), error: error.toString(), provider: selectedProvider as 'gemini-cli' | 'whalecloud' }]);
        setIsLoading(false);
        setResearchComplete(true);
      },
      () => { // onClose
        const isComplete = checkIfResearchComplete(fullResponse) || newIteration >= 5;
        setResearchComplete(isComplete);
        
        if (isComplete) {
          setIsLoading(false);
          setResearchProgress(prev => ({ current: prev.total, total: prev.total }));
          
          // 研究完成，将最终结果合并到主聊天界面，并清理历史
          // 使用从后端接收到的ID（如果有的话），否则生成一个新的ID
          const messageId = currentMessageIdRef.current || `chatcmpl-${crypto.randomUUID ? crypto.randomUUID() : Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)}`;
          setMessages(prev => {
            const lastUserMessageIndex = prev.map(m => m.role === 'user').lastIndexOf(true);
            
            if (lastUserMessageIndex === -1) {
              const finalMessages: Message[] = [...prev, { id: messageId, role: 'assistant', content: fullResponse, createdAt: Date.now(), provider: selectedProvider as 'gemini-cli' | 'whalecloud' }];
              conversationHistoryRef.current = finalMessages;
              return finalMessages;
            }

            const historyBefore = prev.slice(0, lastUserMessageIndex);
            
            // 复制并清理用户问题
            const userQuestionMessage: Message = { ...prev[lastUserMessageIndex] };
            userQuestionMessage.content = userQuestionMessage.content.replace(/(\[DEEP RESEARCH\]\s*)+/g, '').trim();

            const finalMessages: Message[] = [...historyBefore, userQuestionMessage, { id: messageId, role: 'assistant', content: fullResponse, createdAt: Date.now(), provider: selectedProvider as 'gemini-cli' | 'whalecloud' }];
            
            // 同步更新内部历史记录
            conversationHistoryRef.current = finalMessages;
            
            return finalMessages;
          });
          // 重置currentMessageIdRef
          currentMessageIdRef.current = null;

        } else {
          // 研究未完成，更新历史记录以进行下一次迭代
          // 使用从后端接收到的ID（如果有的话），否则生成一个新的ID
          const messageId = currentMessageIdRef.current || `chatcmpl-${crypto.randomUUID ? crypto.randomUUID() : Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)}`;
          const lastContinueMessageIndex = messagesForBackend.map(m=>m.content.includes('Continue the research')).lastIndexOf(true);
          const historyForNextIteration = messagesForBackend.slice(0, lastContinueMessageIndex);
          conversationHistoryRef.current = [...historyForNextIteration, { id: messageId, role: 'assistant', content: fullResponse }];
          // 重置currentMessageIdRef
          currentMessageIdRef.current = null;
          setIsLoading(false); // 允许下一次迭代触发
        }
      },
      settings.apiKey,
      settings.whaleDevCloudToken
    );
  }, [researchComplete, isLoading, researchIteration, repoInfo, selectedProvider, selectedModel, language, sessionId, settings, upsertSandboxStatusMessage]);

  useEffect(() => {
    if (deepResearch && !isLoading && !researchComplete && researchIteration > 0) {
      const timer = setTimeout(() => {
        continueResearch();
      }, 1000); // Delay before continuing
      return () => clearTimeout(timer);
    }
  }, [deepResearch, isLoading, researchComplete, researchIteration, continueResearch]);

  // 新增：当收到研究计划后自动继续研究
  useEffect(() => {
    // 当研究阶段非空且只有一个阶段(研究计划)时，自动继续研究
    if (deepResearch && !isLoading && researchStages.length === 1 && !researchComplete) {
      const timer = setTimeout(() => {
        continueResearch();
      }, 2000); // 给用户一点时间查看研究计划
      return () => clearTimeout(timer);
    }
  }, [deepResearch, isLoading, researchStages, researchComplete, continueResearch]);

  useEffect(() => {
    const fetchModels = async () => {
      try {
        const response = await authFetch('/api/config/models');
        if (response && response.ok) {
          const data = await response.json();
          setProviders(data.providers);
        }
      } catch (err) {
        console.error('Failed to fetch models:', err);
      } finally {
        setIsModelsLoaded(true);
      }
    };
    fetchModels();
  }, []);

  useEffect(() => {
    if (!isModelsLoaded || providers.length === 0 || isInitialSetupComplete) {
      return;
    }

    console.log('useChat初始化模型设置', { initialProvider, initialModel, providersCount: providers.length });

    // 如果有initialProvider，直接使用（SearchPage会处理具体的设置）
    if (initialProvider) {
      const provider = providers.find(p => p.id === initialProvider);
      if (provider) {
        // 设置modelType以匹配initialProvider
        setModelType(initialProvider as 'whalecloud' | 'gemini-cli');
        
        // 只设置availableModels，不改变selectedProvider和selectedModel
        // 这些由SearchPage统一管理
        if (initialProvider !== 'gemini-cli') {
          setAvailableModels(provider.models);
        }
        
        console.log('useChat检测到initialProvider，设置modelType为:', initialProvider);
        setIsInitialSetupComplete(true);
        return;
      }
    }

    // 如果没有initialProvider，使用默认逻辑（首次访问的情况）
    console.log('useChat使用默认初始化逻辑');
    const defaultProvider = providers.find(p => p.id === 'gemini-cli') || providers[0];
    if (defaultProvider) {
      setSelectedProvider(defaultProvider.id);
      setModelType(defaultProvider.id as 'whalecloud' | 'gemini-cli');
      
      if (defaultProvider.id === 'gemini-cli') {
        setSelectedModel('gemini-2.5-flash');
      } else {
        setAvailableModels(defaultProvider.models);
        setSelectedModel(defaultProvider.models[0]?.id || '');
      }
    }

    setIsInitialSetupComplete(true);
  }, [isModelsLoaded, providers, initialProvider, initialModel, isInitialSetupComplete]);

  useEffect(() => {
    // This effect handles model selection when the user manually changes the provider.
    if (isInitialSetupComplete) {
      const providerConfig = providers.find((p) => p.id === selectedProvider);
      if (providerConfig) {
        // 更新modelType以匹配provider
        if (selectedProvider === 'gemini-cli') {
          setModelType('gemini-cli');
          // gemini-cli使用固定的模型选项，不从providers中获取
          // if (selectedModel !== 'gemini-2.5-flash' && selectedModel !== 'gemini-2.5-pro') {
          //   setSelectedModel('gemini-2.5-flash');
          // }
        } else {
          setModelType('whalecloud');
          setAvailableModels(providerConfig.models);
          if (!providerConfig.models.some(m => m.id === selectedModel)) {
            setSelectedModel(providerConfig.models[0]?.id || '');
          }
        }
      }
    }
  }, [selectedProvider, isInitialSetupComplete, providers, selectedModel, setModelType]);

    // 从消息历史中查找用户问题（用于历史记录）
    const findUserQuestionFromMessages = useCallback((msgId: string, messages: Message[]): string | undefined => {
      const messageIndex = messages.findIndex(msg => msg.id === msgId);
      if (messageIndex === -1) return undefined;
      
      // 向前查找最近的用户消息
      for (let i = messageIndex - 1; i >= 0; i--) {
        if (messages[i].role === 'user') {
          return messages[i].content?.replace(/(\[DEEP RESEARCH\]\s*)+/g, '').trim();
        }
      }
      return undefined;
    }, []);

  // 生成分组后的工具调用列表
  const groupedToolExecutions = useMemo(() => {
    const groups = new Map<string, ToolExecutionGroup>();
    
    toolExecutions.forEach(tool => {
      if (tool.status === 'failed') {
        return;
      }
      let groupKey: string;
      let userQuestion: string | undefined;
      
      if (tool.msgId) {
        // 历史记录：基于 msgId 分组
        groupKey = `history_${tool.msgId}`;
        // 从消息历史中查找用户问题
        userQuestion = findUserQuestionFromMessages(tool.msgId, messages);
      } else {
        // 新会话：基于现有的 conversationId 分组
        groupKey = tool.conversationId;
        // 从映射关系中查找用户问题
        userQuestion = conversationQuestionMap.get(tool.conversationId);
      }
      
      if (!groups.has(groupKey)) {
        groups.set(groupKey, {
          conversationId: groupKey,
          timestamp: tool.timestamp,
          userQuestion: userQuestion,
          tools: [],
          stats: { total: 0, executing: 0, completed: 0, failed: 0 }
        });
      }
      
      const group = groups.get(groupKey)!;
      group.tools.push(tool);
      
      // 更新统计
      group.stats.total++;
      if (tool.status === 'executing') group.stats.executing++;
      else if (tool.status === 'completed') group.stats.completed++;
      else if (tool.status === 'failed') group.stats.failed++;
    });
    
    // 转换为数组并排序
    return Array.from(groups.values())
      .sort((a, b) => a.timestamp - b.timestamp);
  }, [toolExecutions, conversationQuestionMap, messages, findUserQuestionFromMessages]);

  useEffect(() => {
    return () => {
      if (sseConnectionRef.current) {
        closeSSE(sseConnectionRef.current);
      }
    };
  }, []);



  const clearConversation = useCallback(() => {
    setMessages([]);
    setQuestion('');
    setResearchStages([]);
    setResearchComplete(false);
    setResearchIteration(0);
    // 注意：不清除 toolExecutions，保持工具调用历史
    setCurrentToolCalls([]);
    // 清理对话轮次
    setCurrentConversationId('');
    // 清理用户问题映射
    setConversationQuestionMap(new Map());
  }, []);

  return {
    messages,
    setMessages, // Export setMessages
    question,
    setQuestion,
    isLoading,
    deepResearch,
    setDeepResearch,
    modelType,
    setModelType,
    selectedProvider,
    setSelectedProvider,
    selectedModel,
    setSelectedModel,
    providers,
    availableModels,
    handleSubmit,
    clearConversation,
    researchStages,
    researchProgress, // 导出进度状态
    isModelsLoaded,
    isInitialSetupComplete,
    // 导出工具调用相关状态
    toolExecutions,
    setToolExecutions, // 导出setToolExecutions函数
    currentToolCalls,
    // 新增：导出分组后的工具调用列表
    groupedToolExecutions,
    // 导出沙盒状态
    sandboxStatus,
    sandboxPolling,
    sandboxReady,
    sandboxError,
    // 导出沙盒控制方法
    startSandboxPolling,
    stopSandboxPolling,
    // 紧急修复方法
    manualCheckSandboxStatus,
    forceSandboxReady
  };
}; 

export default useChat;
