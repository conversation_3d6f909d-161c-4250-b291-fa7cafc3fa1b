'use client';

import { authFetch } from '@/utils/authFetch';
import React, { createContext, useContext, useState, useEffect, useRef, ReactNode } from 'react';
import { logout as LogoutUtil, login as LoginUtil } from '@/utils/auth';

interface AuthContextType {
  userInfo: UserInfo | null;
  isLogged: boolean;
  userRoles: UserRole[] | null;
  componentPrivileges: ComponentPrivilege[] | null;
  setUserInfo: (userInfo: UserInfo | null) => void;
  setIsLogged: (isLogged: boolean) => void;
  checkAuthentication: () => Promise<void>;
  logout: () => Promise<void>;
  login: () => void;
  hasAdminPermission: () => boolean;
  hasSandboxPermission: () => boolean;
  hasSuperAdminPermission: () => boolean;
  hasGrantPermission: (roleCode: string) => boolean;
  hasDeletePermission: (createdBy: number, privCode: string) => boolean;
  hasEditPermission: (createdBy: number) => boolean;
  hasOwnerTransferPermission: (ownerId: number) => boolean;
  hasAddWikiPermission: (privCode: string) => boolean;
  hasRefreshWikiPermission: (ownerId: number, privCode: string) => boolean;
  hasExportWikiPermission: (privCode: string) => boolean;
  hasAccessPermission: (roleCode: string) => boolean;
  isAuthLoading: boolean;
}
interface UserRole {
  user_role_id: number;
  user_id: number;
  role_id: number;
  role_name: string;
  role_code: string;
  role_comments: string;
}

interface UserInfo {
  id: number,
  wcp_user_id: string;
  user_code: string;
  user_name: string;
  email: string;
  phone: string;
  dept: string;
  org: string;
  job: string;
  job_id: string;
  dept_id: string;
  org_id: string;
}

interface ComponentPrivilege {
  id: number;
  priv_type: string;
  priv_code: string;
  priv_name: string;
  priv_el?: string | null;
  state: number;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [isLogged, setIsLogged] = useState<boolean>(false);
  const [userRoles, setUserRoles] = useState<UserRole[] | null>(null);
  const [componentPrivileges, setComponentPrivileges] = useState<ComponentPrivilege[] | null>(null);
  
  // 添加防抖相关状态
  const [isChecking, setIsChecking] = useState<boolean>(false);
  const [isAuthLoading, setIsAuthLoading] = useState<boolean>(true); // 初始为加载状态
  const checkTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 防抖的认证检查函数
  const debouncedCheckAuthentication = () => {
    // 清除之前的定时器
    if (checkTimeoutRef.current) {
      clearTimeout(checkTimeoutRef.current);
    }
    
    // 如果正在检查中，跳过
    if (isChecking) {
      console.log('Authentication check already in progress, skipping...');
      return;
    }

    // 设置新的定时器
    checkTimeoutRef.current = setTimeout(() => {
      checkAuthentication();
    }, 200); // 300ms 防抖延迟
  };

  // 检查用户是否已认证的函数
  const checkAuthentication = async () => {
    // 防止重复检查
    if (isChecking) {
      console.log('Authentication check already in progress, skipping...');
      return;
    }

    setIsChecking(true);
    
    try {
      const isLoggedResponse = await authFetch('/api/auth/isLogged', {
        // 添加超时防止挂起
        signal: AbortSignal.timeout(5000)
      });

      if (!isLoggedResponse || !isLoggedResponse.ok) {
        console.error('Backend auth check failed:', isLoggedResponse?.status, isLoggedResponse?.statusText);
        setIsLogged(false);
        setUserInfo(null);
        return;
      }

      const data = await isLoggedResponse?.json();

      if (data.is_login) {
        setIsLogged(true);
        setUserInfo(data.user_info);
        if (data.user_roles) {
          setUserRoles(data.user_roles);
        }
        if (data.component_privileges) {
          setComponentPrivileges(data.component_privileges);
        }
      } else {
        setIsLogged(false);
        setUserInfo(null);
        setUserRoles(null);
        setComponentPrivileges(null);
        // 只有在不是token页面时才跳转，避免循环
        if (!window.location.pathname.includes('/auth/token')) {
          login();
        }
      }
    } catch (error) {
      console.error('Authentication check error:', error);
      // 如果后端不可用，暂时跳过认证检查
      setIsLogged(false);
      setUserInfo(null);
      setUserRoles(null);
      setComponentPrivileges(null);
    } finally {
      setIsChecking(false);
      setIsAuthLoading(false); // 认证检查完成，设置加载状态为 false
    }
  };

  // 登出函数
  const logout = async () => {
    try {
      const res = await authFetch('/api/auth/logout');
      if (res && res.ok) {
        setIsLogged(false);
        setUserInfo(null);
        setUserRoles(null);
        setComponentPrivileges(null);
        setIsAuthLoading(false); // 登出后设置加载状态为 false
        LogoutUtil();
      }
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const login = () => {
    LoginUtil();
  };

    // 判断是否为超级管理员
  const isSuperAdmin = (): boolean => {
    return !!(userRoles && userRoles.some(role => role.role_code === 'super_admin'));
  };

  // 检查是否具有管理员权限（超级管理员或wiki管理员）
  const hasAdminPermission = () => {
    if (!userRoles || userRoles.length === 0) return false;
    // 统一调用 isSuperAdmin
    if (isSuperAdmin())
      return true;
    return userRoles.some(role => role.role_code === 'repo_owner');
  };

  const hasSuperAdminPermission = () => {
    if (!userRoles || userRoles.length === 0) return false;
    return userRoles.some(role =>
      role.role_code === 'super_admin'
    );
  };

  // 检查是否具有创建沙盒的权限（super_admin、repo_owner、ai_qa）
  const hasSandboxPermission = () => {
    if (!userRoles || userRoles.length === 0) return false;
    return userRoles.some(role => 
      role.role_code === 'super_admin' || 
      role.role_code === 'repo_owner' || 
      role.role_code === 'ai_qa'
    );
  };

  const hasGrantPermission = (roleCode: string) => {
    if (isSuperAdmin()) {
      return true;
    }
    // 是否具备授权角色
    return !!(roleCode && (roleCode === 'wiki_grant'));
  };

    /**
   * 判断是否有生成wiki的权限
   * 超级管理员直接返回 true
   * 否则需本人创建且拥有对应组件权限
   */
  const hasAddWikiPermission = (privCode: string) => {
    if (isSuperAdmin()) {
      return true;
    }
    return hasComponentPrivilege(privCode);
  };

  /**
   * 判断是否有刷新wiki的权限
   * 超级管理员直接返回 true
   * 否则需有组件权限点且有管理员权限
   */
  const hasRefreshWikiPermission = (ownerId: number, privCode: string) => {
    if (isSuperAdmin()) {
      return true;
    }
    return !!(userInfo?.id === ownerId && hasComponentPrivilege(privCode));
  };

  /**
   * 判断是否有导出wiki的权限
   * 超级管理员直接返回 true
   * 否则需有组件权限点且有管理员权限
   */
  const hasExportWikiPermission = (privCode: string) => {
    if (isSuperAdmin()) {
      return true;
    }
    return hasComponentPrivilege(privCode);
  };

  /**
   * 判断是否有删除权限
   * 超级管理员直接返回 true
   * 否则需本人创建且拥有对应组件权限
   */
  const hasDeletePermission = (ownerId: number, privCode: string) => {
    if (isSuperAdmin()) {
      return true;
    }
    return !!(userInfo?.id === ownerId && hasComponentPrivilege(privCode));
  };

  const hasAccessPermission = (roleCode: string) => {
    if (isSuperAdmin()) {
      return true;
    }
    // 是否具备授权角色
    return !!(roleCode && (roleCode === 'wiki_grant' || roleCode === 'wiki_access'));
  };

  /**
   * 校验组件权限
   * @param privCode 权限编码
   * @returns 是否有权限
   */
  const hasComponentPrivilege = (privCode: string): boolean => {
    if (!privCode) return false;
    return !!(componentPrivileges && componentPrivileges.some(priv => priv.priv_code === privCode));
  };

  /**
   * 校验是否有编辑wiki信息的权限
   * @param createdBy 仓库创建人
   * @returns 是否有权限
   */
  const hasEditPermission = (ownerId: number): boolean => {
    if (isSuperAdmin()) {
      return true;
    }
    
    return !!(userInfo?.id === ownerId && userRoles?.some(role => role.role_code === 'repo_owner'));
  }

  /**
   * 校验是否有授权转移地权限
   * @param createdBy 仓库拥有者
   * @returns 是否有权限
   */
  const hasOwnerTransferPermission = (ownerId: number): boolean => {
    if (isSuperAdmin()) {
      return true;
    }
    
    return !!(userInfo?.id === ownerId);
  }

  useEffect(() => {
    console.log('AuthProvider useEffect triggered - about to call checkAuthentication');
    // 使用防抖版本的认证检查
    debouncedCheckAuthentication();
  }, []);

  // 监听页面焦点变化和自定义认证事件
  useEffect(() => {
    // const handleFocus = () => {
    //   console.log('Page gained focus, rechecking authentication...');
    //   checkAuthentication();
    // };

    // const handleVisibilityChange = () => {
    //   if (!document.hidden) {
    //     console.log('Page became visible, rechecking authentication...');
    //     checkAuthentication();
    //   }
    // };

    const handleAuthTokenReceived = () => {
      console.log('Auth token received event, rechecking authentication...');
      // 重置加载状态，准备重新检查认证
      setIsAuthLoading(true);
      debouncedCheckAuthentication();
    };

    // 监听窗口焦点事件
    // window.addEventListener('focus', handleFocus);
    // 监听页面可见性变化
    // document.addEventListener('visibilitychange', handleVisibilityChange);
    // 监听自定义认证事件
    window.addEventListener('authTokenReceived', handleAuthTokenReceived);

    return () => {
      // window.removeEventListener('focus', handleFocus);
      // document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('authTokenReceived', handleAuthTokenReceived);
      // 清理定时器
      if (checkTimeoutRef.current) {
        clearTimeout(checkTimeoutRef.current);
      }
    };
  }, []);

  const value: AuthContextType = {
    userInfo,
    isLogged,
    isAuthLoading,
    userRoles,
    componentPrivileges,
    setUserInfo,
    setIsLogged,
    checkAuthentication,
    logout,
    login,
    hasAdminPermission,
    hasSandboxPermission,
    hasGrantPermission,
    hasDeletePermission,
    hasAddWikiPermission,
    hasRefreshWikiPermission,
    hasExportWikiPermission,
    hasAccessPermission,
    hasEditPermission,
    hasOwnerTransferPermission,
    hasSuperAdminPermission,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}; 