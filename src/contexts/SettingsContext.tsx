'use client';

import { authFetch } from '@/utils/authFetch';
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './AuthContext';

interface Settings {
  apiKey: string;
  defaultWikiModel: string;
  defaultChatModel: string;
  whaleDevCloudToken: string;
  // 校验状态字段（基于空值检查：空=无效，非空=有效）
  isApiKeyValid: boolean;
  isWhaleDevCloudTokenValid: boolean;
  // 公告显示状态
  showAnnouncement: boolean;
  // 项目/产品模式状态
  isProjectMode: boolean;
}

interface SettingsContextType {
  settings: Settings;
  setApiKey: (apiKey: string, isValid?: boolean) => Promise<void>;
  setWhaleDevCloudToken: (token: string, isValid?: boolean) => Promise<void>;
  isSettingsLoaded: boolean;
  // 刷新校验状态（基于空值检查）
  refreshValidationStatus: () => Promise<void>;
  // 更新全局状态中的API Key和Token
  updateGlobalApiKeyAndToken: (apiKey: string, whaleDevCloudToken: string) => void;
  // 更新全局状态中的模型配置
  updateGlobalModelConfig: (defaultWikiModel: string, defaultChatModel: string) => void;
  // 设置公告显示状态
  setShowAnnouncement: (show: boolean) => void;
  // 设置项目/产品模式状态
  setIsProjectMode: (isProject: boolean) => void;
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export const SettingsProvider = ({ children }: { children: ReactNode }) => {
  const { isLogged } = useAuth();
  
  // 获取默认值的辅助函数
  const getDefaultShowAnnouncement = () => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('showAnnouncement');
      return saved !== null ? saved === 'true' : true; // 默认显示公告
    }
    return true;
  };

  let defaultWikiModel = 'gemini-2.5-flash';
  let defaultChatModel = 'gemini-2.5-flash';
  if (typeof window !== 'undefined') {
    defaultWikiModel = localStorage.getItem('defaultWikiModel') || 'gemini-2.5-flash';
    defaultChatModel = localStorage.getItem('defaultChatModel') || 'gemini-2.5-flash';
  }
  const [settings, setSettingsState] = useState<Settings>({
    apiKey: '',
    defaultWikiModel: defaultWikiModel,
    defaultChatModel: defaultChatModel,
    whaleDevCloudToken: '',
    isApiKeyValid: false, // 初始为false，等待从数据库加载
    isWhaleDevCloudTokenValid: false,
    showAnnouncement: getDefaultShowAnnouncement(), // 从localStorage读取或使用默认值
    isProjectMode: false,
  });
  const [isSettingsLoaded, setIsSettingsLoaded] = useState(false);
  
  // 刷新所有校验状态
  const refreshValidationStatus = async () => {
    // 从数据库查出的配置都是有效的，只需要检查是否为空
    setSettingsState(prev => ({
      ...prev,
      isApiKeyValid: !!prev.apiKey,
      isWhaleDevCloudTokenValid: !!prev.whaleDevCloudToken
    }));
  };

  // 监听用户登录状态，在登录后初始化设置
  useEffect(() => {
    const initializeSettings = async () => {
      if (!isLogged) {
        return;
      }

      try {
        const response = await authFetch('/api/config/settings');
        if (response && response.ok) {
          const data = await response.json();
          // 解码API Key和Token
          const apiKey = data.ai_api_key ? atob(data.ai_api_key) : '';
          const whaleDevCloudToken = data.dev_cloud_token ? atob(data.dev_cloud_token) : '';
          
          // 更新全局状态
          setSettingsState(prev => ({
            ...prev,
            apiKey: apiKey,
            whaleDevCloudToken: whaleDevCloudToken,
            isApiKeyValid: !!apiKey,
            isWhaleDevCloudTokenValid: !!whaleDevCloudToken,
          }));
        }
      } catch (error) {
        console.error('初始化配置失败:', error);
      } finally {
        setIsSettingsLoaded(true);
      }
    };
    
    initializeSettings();
  }, [isLogged]);

  // 单独保存API Key
  const setApiKey = async (apiKey: string, isValid?: boolean) => {
    const cleanedApiKey = apiKey?.replace(/●/g, '') || '';
    
    setSettingsState(prev => ({ 
      ...prev, 
      apiKey: cleanedApiKey,
      isApiKeyValid: isValid ?? !!cleanedApiKey,
    }));
    
    const response = await authFetch('/api/config/settings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ai_api_key: btoa(cleanedApiKey),
        // 不传递dev_cloud_token，让后端保持现有值
      }),
    });
    
    if (!response || !response.ok) {
      const errorData = response ? await response.json() : { detail: '保存API Key失败' };
      throw new Error(errorData.detail || '保存API Key失败');
    }
  };

  // 单独保存WhaleDevCloud Token
  const setWhaleDevCloudToken = async (token: string, isValid?: boolean) => {
    const cleanedToken = token?.replace(/●/g, '') || '';
    
    setSettingsState(prev => ({ 
      ...prev, 
      whaleDevCloudToken: cleanedToken,
      isWhaleDevCloudTokenValid: isValid ?? !!cleanedToken,
    }));
    
    const response = await authFetch('/api/config/settings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        // 不传递ai_api_key，让后端保持现有值
        dev_cloud_token: btoa(cleanedToken),
      }),
    });
    
    if (!response || !response.ok) {
      const errorData = response ? await response.json() : { detail: '保存WhaleDevCloud Token失败' };
      throw new Error(errorData.detail || '保存WhaleDevCloud Token失败');
    }
  };

  // 更新全局状态中的API Key和Token
  const updateGlobalApiKeyAndToken = (apiKey: string, whaleDevCloudToken: string) => {
    setSettingsState(prev => ({
      ...prev,
      apiKey: apiKey,
      whaleDevCloudToken: whaleDevCloudToken,
      isApiKeyValid: !!apiKey,
      isWhaleDevCloudTokenValid: !!whaleDevCloudToken,
    }));
  };

  // 更新全局状态中的模型配置
  const updateGlobalModelConfig = (defaultWikiModel: string, defaultChatModel: string) => {
    setSettingsState(prev => ({
      ...prev,
      defaultWikiModel: defaultWikiModel,
      defaultChatModel: defaultChatModel,
    }));
    
    // 保存到localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('defaultWikiModel', defaultWikiModel);
      localStorage.setItem('defaultChatModel', defaultChatModel);
    }
  };

  // 设置公告显示状态
  const setShowAnnouncement = (show: boolean) => {
    setSettingsState(prev => ({
      ...prev,
      showAnnouncement: show,
    }));
    
    // 持久化到localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('showAnnouncement', show.toString());
    }
  };

  // 设置项目/产品模式状态
  const setIsProjectMode = (isProject: boolean) => {
    setSettingsState(prev => ({
      ...prev,
      isProjectMode: isProject,
    }));
  };

  const value: SettingsContextType = {
    settings,
    setApiKey,
    setWhaleDevCloudToken,
    isSettingsLoaded,
    refreshValidationStatus,
    updateGlobalApiKeyAndToken,
    updateGlobalModelConfig,
    setShowAnnouncement,
    setIsProjectMode,
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
};

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};
