export interface Tag {
  id: number;
  name: string;
  type: number; // 1: 系统标签, 2: 用户标签
  color: string;
  comments?: string;
  module_type?: number;
  state?: number;
  created_by?: number;
  created_date?: string;
  update_by?: number;
  update_date?: string;
}

export interface TagCreateRequest {
  name: string;
  type: number;
  color: string;
  comments?: string;
  module_type?: number;
  state?: number;
}

export interface TagUpdateRequest {
  name: string;
  type: number;
  color: string;
  comments?: string;
  module_type?: number;
  state?: number;
}

export interface TagListResponse {
  code: string;
  message: string;
  data: Tag[];
  total: number;
}

export interface TagResponse {
  code: string;
  message: string;
  data: Tag;
}

export interface TagDeleteResponse {
  code: string;
  message: string;
}

export const TAG_TYPE = {
  SYSTEM: 1,
  USER: 2
} as const;

export const TAG_TYPE_LABELS = {
  [TAG_TYPE.SYSTEM]: '系统标签',
  [TAG_TYPE.USER]: '用户标签'
} as const;
