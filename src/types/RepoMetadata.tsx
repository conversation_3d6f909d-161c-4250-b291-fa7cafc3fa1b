export interface RepoMetadata {
  dc_repo_id: string | number | null; // 研发云仓库ID
  dc_project_id: string | number | null; // 研发云项目ID
  branch_version_id: string | number | null; // 分支版本ID
  branch_version_name: string | null; // 分支版本名称
  product_version_id: string | number | null; // 产品版本ID
  product_version_code: string | null; // 产品版本编码
  product_name: string | null; // 产品名称
  product_id: string | number | null; // 产品ID
  product_line_id: string | number | null; // 产品线ID
  product_line_name: string | null; // 产品线名称
  solutions: Solution[];
}

export interface Solution {
  release_pkg_id: string | number | null; // 发布包ID
  release_pkg_code: string | null; // 发布包编码
  solution_name: string | null; // 解决方案名称
  solution_id: string | number | null; // 解决方案ID
}

export interface RepoMetadataResponse {
  repositoryId: string | number | null;
  projectId: string | number | null;
  apiBranchVersionDto: ApiBranchVersionDto | null;
  productLineDto: ProductLineDto | null;
  apiDistributionDtoList: ApiDistributionDto[] | null;
  apiProductVersionDto: ApiProductVersionDto | null;
}

export interface ApiBranchVersionDto {
  branchName: string | null;
  branchVersionId: string | number | null;
  cmoCode: string | null;
  devUserCode: string | null;
  isMain: string | null;
  mainBranchId: string | number | null;
  productVersionId: string | number | null;
}

export interface ProductLineDto {
  productLineId: string | number | null;
  productLineName: string | null;
}

export interface ApiProductVersionDto {
  classType: string | null;
  productDto: ProductDto | null;
  productVersionCode: string | null;
  productVersionId: string | number | null;
}

export interface ProductDto {
  classType: string | null;
  productId: string | number | null;
  productName: string | null;
}

export interface ApiDistributionDto {
  classType: string | null;
  productDto: ProductDto | null;
  productVersionCode: string | null;
  productVersionId: string | number | null;
}

export const defaultRepoMetadata = () => {
  const repo_metadata: RepoMetadata = {
    dc_repo_id: null,
    dc_project_id: null,
    branch_version_id: null,
    branch_version_name: null,
    product_version_id: null,
    product_version_code: null,
    product_name: null,
    product_id: null,
    product_line_id: null,
    product_line_name: null,
    solutions: [],
  };
  return repo_metadata;
};
