export interface SubRepoInfo {
  url: string;
  branch: string;
  owner?: string;
  name?: string;
  type?: string;
  is_main?: boolean;
}
export interface RepoInfo {
    owner: string;
    repo: string;
    type: string;
    token: string | null;
    localPath: string | null;
    repoUrl: string | null;
    branch: string;
    subRepos: SubRepoInfo[];
    wiki_id: string;
}

export default RepoInfo;
