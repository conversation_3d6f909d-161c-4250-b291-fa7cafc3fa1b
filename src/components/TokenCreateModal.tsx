import React, { useState } from 'react';
import { FaTimes } from 'react-icons/fa';
import { useLanguage } from '@/contexts/LanguageContext';
import { useToast } from '@/contexts/ToastContext';
import { authFetch } from '@/utils/authFetch';

interface TokenCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (tokenData?: { name: string; token: string }) => void;
}

const TokenCreateModal: React.FC<TokenCreateModalProps> = ({ isOpen, onClose, onSuccess }) => {
  const { messages: t } = useLanguage();
  const { addToast } = useToast();
  
  // 获取本地时间的 ISO 字符串格式（解决时区问题）
  const getLocalISOString = () => {
    const now = new Date();
    // 获取本地时间偏移量并调整
    return new Date(now.getTime() - now.getTimezoneOffset() * 60000)
      .toISOString()
      .slice(0, 16);
  };
  
  const [tokenName, setTokenName] = useState('');
  const [effectiveTime, setEffectiveTime] = useState(() => getLocalISOString());
  const [expireTime, setExpireTime] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  const handleSubmit = async () => {
    if (!tokenName.trim()) {
      addToast?.({
        type: 'warning',
        title: '',
        message: t.settings.enterTokenName || '请输入Token名称'
      });
      return;
    }

    // 校验时间逻辑
    if (effectiveTime && expireTime) {
      const effectiveDate = new Date(effectiveTime);
      const expireDate = new Date(expireTime);
      
      // 检查失效时间是否在生效时间之后
      if (expireDate <= effectiveDate) {
        addToast?.({
          type: 'warning',
          title: '',
          message: t.settings.expireTimeMustBeAfterEffectiveTime || '失效时间必须晚于生效时间'
        });
        return;
      }
    } 
    if (expireTime) {
      // 只设置了失效时间，检查是否在未来
      const expireDate = new Date(expireTime);
      const now = new Date();
      
      if (expireDate <= now) {
        addToast?.({
          type: 'warning',
          title: '',
          message: t.settings.expireTimeMustBeFuture || '失效时间必须是未来时间'
        });
        return;
      }
    }

    setIsCreating(true);
    
    try {
      const response = await authFetch('/api/user_token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: tokenName.trim(),
          effective_at: effectiveTime || null,
          use_type: '',
          expires_at: expireTime || null,
        }),
      });

      // 检查响应是否存在
      if (!response) {
        throw new Error('网络请求失败');
      }

      // 处理成功响应
      if (response.ok) {
        const data = await response.json();
        const token = data || '';
        
        if (!token) {
          throw new Error('未获取到Token内容');
        }

        // 成功创建Token
        addToast?.({
          type: 'success',
          title: '',
          message: t.settings.tokenCreated || 'Token创建成功'
        });
        
        onSuccess({ name: tokenName.trim(), token });
        onClose();
        
        // Reset form
        setTokenName('');
        setEffectiveTime(getLocalISOString());
        setExpireTime('');
        return;
      }

      // 处理HTTP错误响应
      let errorMessage = '创建Token失败';
      try {
        const errorData = await response.json();
        console.error('创建Token失败，后端返回:', errorData);
        errorMessage = errorData.detail || errorMessage;
      } catch (parseError) {
        console.error('无法解析错误响应:', parseError);
      }

      // 显示错误提示
      addToast?.({
        type: 'error',
        title: '',
        message: errorMessage
      });

      return;

    } catch (error) {
      console.error('创建Token过程中发生错误:', error);
      addToast?.({
        type: 'error',
        title: '',
        message: t.settings.tokenCreateFailed || '创建Token失败'
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleClose = () => {
    if (!isCreating) {
      onClose();
      // Reset form
      setTokenName('');
      setEffectiveTime(getLocalISOString());
      setExpireTime('');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-[var(--card-bg)] rounded-lg shadow-2xl border border-[var(--border-color)] w-full max-w-md">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-[var(--foreground)]">
              {t.settings.createToken || '创建Token'}
            </h3>
            <button
              onClick={handleClose}
              disabled={isCreating}
              className="text-[var(--muted)] hover:text-[var(--foreground)] transition-colors disabled:opacity-50"
            >
              <FaTimes size={16} />
            </button>
          </div>

          {/* Form */}
          <div className="space-y-4">
            {/* Token名称 */}
            <div>
              <label className="block text-sm font-medium text-[var(--foreground)] mb-2">
                {t.settings.tokenName || 'Token名称'} <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={tokenName}
                onChange={(e) => setTokenName(e.target.value)}
                placeholder={t.settings.enterTokenName || '请输入Token名称'}
                className="w-full px-3 py-2 text-sm rounded-md bg-transparent text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)] border border-[var(--border-color)]"
                disabled={isCreating}
              />
            </div>

            {/* 生效时间 */}
            <div>
              <label className="block text-sm font-medium text-[var(--foreground)] mb-2">
                {t.settings.effectiveTime || '生效时间'}
              </label>
              <input
                type="datetime-local"
                value={effectiveTime}
                onChange={(e) => setEffectiveTime(e.target.value)}
                className="w-full px-3 py-2 text-sm rounded-md bg-transparent text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)] border border-[var(--border-color)]"
                disabled={isCreating}
                min={getLocalISOString()}
              />
            </div>

            {/* 过期时间 */}
            <div>
              <label className="block text-sm font-medium text-[var(--foreground)] mb-2">
                {t.settings.expireTime || '过期时间'}
              </label>
              <input
                type="datetime-local"
                value={expireTime}
                onChange={(e) => setExpireTime(e.target.value)}
                className="w-full px-3 py-2 text-sm rounded-md bg-transparent text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)] border border-[var(--border-color)]"
                disabled={isCreating}
                min={effectiveTime || getLocalISOString()}
              />
            </div>
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 mt-6">
            <button
              onClick={handleClose}
              disabled={isCreating}
              className="px-4 py-2 text-sm font-medium rounded-md border border-[var(--border-color)] text-[var(--accent-primary)] hover:bg-[var(--accent-primary)]/10 transition-colors disabled:opacity-50"
            >
              {t.common.cancel || '取消'}
            </button>
            <button
              onClick={handleSubmit}
              disabled={isCreating}
              className="px-4 py-2 text-sm font-medium rounded-md border border-transparent bg-[var(--accent-primary)]/90 text-white hover:bg-[var(--accent-primary)] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isCreating ? (
                <>
                  <span className="inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
                  {t.tagManagement.creating || '创建中...'}
                </>
              ) : (
                t.k8s.createSandbox.create || '创建'
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TokenCreateModal;
