'use client';

import React, { useRef, useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { FaAppStore, FaSignOutAlt } from 'react-icons/fa';
import { HiCog } from 'react-icons/hi';
import { MdOutlineMonitorHeart } from 'react-icons/md';
import { HiPresentationChartBar } from 'react-icons/hi2';
import { HiOutlineUsers } from 'react-icons/hi';
import { TbBrandCodesandbox } from 'react-icons/tb';
import { authFetch } from '@/utils/authFetch';
import { useLanguage } from '@/contexts/LanguageContext';
import { IoIosToday } from 'react-icons/io';

interface PersonInfoDropdownProps {
  isOpen: boolean;
  onClose: () => void;
  triggerRef?: React.RefObject<HTMLButtonElement | null>;
  onOpenSettings?: () => void;
  triggerPosition?: { top: number; left: number; width: number; height: number };
}

interface GlobalConfig {
  monitor_url?: string;
  operation_url?: string;
  operation_data_url?: string;
}

export default function PersonInfoDropdown({ isOpen, onClose, triggerRef, onOpenSettings, triggerPosition }: PersonInfoDropdownProps) {
  const { logout, hasSuperAdminPermission, isLogged } = useAuth();
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [globalConfig, setGlobalConfig] = useState<GlobalConfig | null>(null);
  const { messages } = useLanguage();

  const handleLogout = async () => {
    await logout();
    onClose();
  };

  const handleSettings = () => {
    onOpenSettings?.();
    onClose();
  };

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      
      // 检查点击是否在下拉框外部
      if (dropdownRef.current && !dropdownRef.current.contains(target)) {
        // 如果有触发器引用，也要检查是否点击在触发器上
        if (triggerRef?.current && triggerRef.current.contains(target)) {
          return; // 点击在触发器上，不关闭
        }
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleClickOutside as EventListener);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside as EventListener);
    };
  }, [isOpen, onClose, triggerRef]);

  useEffect(() => {
    const fetchGlobalConfig = async () => {
      const response = await authFetch('/api/config/global');
      console.log(response);
      if (response && response.status === 200) {
        const data = await response.json();
        setGlobalConfig(data);
      }
    };
    if(isLogged) {
      fetchGlobalConfig();
    }
  }, [isLogged]);

  if (!isOpen) {
    return null;
  }

  // 计算下拉框位置
  const getDropdownStyle = () => {
    if (triggerPosition) {
      return {
        position: 'fixed' as const,
        top: triggerPosition.top + triggerPosition.height + 8, // 按钮下方8px
        left: triggerPosition.left, // 与按钮左边缘对齐
        zIndex: 50,
      };
    }
    return {
      position: 'absolute' as const,
      right: 0,
      top: '100%',
      marginTop: 8,
      zIndex: 50,
    };
  };

  return (
    <div 
      ref={dropdownRef}
      className="min-w-40 bg-[var(--card-bg)] rounded-lg shadow-2xl border border-[var(--border-color)]"
      style={getDropdownStyle()}
    >
      {/* Actions Section */}
      <div className="py-2">
        {onOpenSettings && (
          <button
            onClick={handleSettings}
            className="w-full flex items-center px-4 py-2 text-sm text-[var(--foreground)] hover:bg-[var(--accent-primary)]/10 dark:hover:bg-[var(--accent-primary)]/20 transition-colors cursor-pointer"
          >
            <HiCog className="mr-3 text-[var(--muted)] w-4 h-4" />
            {messages.components.personInfoDropdown.settings}
          </button>
        )}
        {hasSuperAdminPermission() && (
          <div>
            <button
              onClick={() => window.open(globalConfig?.monitor_url, '_blank')}
              className="w-full flex items-center px-4 py-2 text-sm text-[var(--foreground)] hover:bg-[var(--accent-primary)]/10 dark:hover:bg-[var(--accent-primary)]/20 transition-colors cursor-pointer"
            >
              <MdOutlineMonitorHeart className="mr-3 text-[var(--muted)] w-4 h-4" />
              {messages.components.personInfoDropdown.monitor}
            </button>
            <button
              onClick={() => window.open(globalConfig?.operation_url, '_blank')}
              className="w-full flex items-center px-4 py-2 text-sm text-[var(--foreground)] hover:bg-[var(--accent-primary)]/20 transition-colors cursor-pointer"
            >
              <HiPresentationChartBar className="mr-3 text-[var(--muted)] w-4 h-4" />
              {messages.components.personInfoDropdown.operation}
            </button>
            <button
              onClick={() => window.open(globalConfig?.operation_data_url, '_blank')}
              className="w-full flex items-center px-4 py-2 text-sm text-[var(--foreground)] hover:bg-[var(--accent-primary)]/20 transition-colors cursor-pointer"
            >
              <IoIosToday className="mr-3 text-[var(--muted)] w-4 h-4" />
              {messages.components.personInfoDropdown.operationData}
            </button>
            <button
              onClick={() => window.open('/k8s-job-management', '_blank')}
              className="w-full flex items-center px-4 py-2 text-sm text-[var(--foreground)] hover:bg-[var(--accent-primary)]/20 transition-colors cursor-pointer"
            >
              <TbBrandCodesandbox className="mr-3 text-[var(--muted)] w-4 h-4" />
              {messages.components.personInfoDropdown.sandboxManagement}
            </button>
            <button
              onClick={() => window.open('/users', '_blank')}
              className="w-full flex items-center px-4 py-2 text-sm text-[var(--foreground)] hover:bg-[var(--accent-primary)]/20 transition-colors cursor-pointer"
            >
              <HiOutlineUsers className="mr-3 text-[var(--muted)] w-4 h-4" />
              {messages.components.personInfoDropdown.userManagement}
            </button>
            <button
              onClick={() => window.open('/auth/app', '_blank')}
              className="w-full flex items-center px-4 py-2 text-sm text-[var(--foreground)] hover:bg-[var(--accent-primary)]/20 transition-colors cursor-pointer"
            >
              <FaAppStore className="mr-3 text-[var(--muted)] w-4 h-4" />
              {messages.components.personInfoDropdown.appManagement}
            </button>
          </div>
        )}
        <button
          onClick={handleLogout}
          className="w-full flex items-center px-4 py-2 text-sm text-[var(--foreground)] hover:bg-[var(--accent-primary)]/10 dark:hover:bg-[var(--accent-primary)]/20 transition-colors cursor-pointer"
        >
          <FaSignOutAlt className="mr-3 text-[var(--muted)] w-4 h-4" />
          {messages.components.personInfoDropdown.logout}
        </button>
      </div>
    </div>
  );
}
