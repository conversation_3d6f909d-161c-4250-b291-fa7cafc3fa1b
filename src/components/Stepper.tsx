import { useLanguage } from "@/contexts/LanguageContext";
import React, { useState } from "react";

interface StepperProps {
  initStep?: number;
  titles: string[];
  onFinish?: () => void;
  onClose: () => void;
  children: React.ReactNode[];
}

const Stepper = (props: StepperProps) => {
  const { initStep = 0, titles, onFinish, onClose, children } = props;
  const [currentStep, setCurrentStep] = useState(initStep);
  const steps = React.Children.toArray(children);
  const {messages} = useLanguage();

  const tailColorClass = (index: number) => {
    if (index < currentStep) {
      return "bg-[var(--accent-primary)]";
    }
    return "bg-gray-300";
  };

  const indicatorColorClass = (index: number) => {
    if (index === currentStep) {
      return "bg-[var(--accent-primary)] border-[var(--accent-primary)] text-white";
    } else if (index > currentStep) {
      return "bg-white border-gray-300 bg-gray-200 text-gray-300";
    } else {
      return "bg-white border-[var(--accent-primary)] text-[var(--accent-primary)]";
    }
  };

  const titleColorClass = (index: number) => {
    if (index > currentStep) {
      return "text-gray-300";
    }
    return "text-[var(--accent-primary)]";
  };

  return (
    <div className="w-full h-full flex flex-col gap-y-6">
      <div className="w-full flex items-center px-16">
        {titles.map((title, index) => {
          return (
            <div
              key={title}
              className={`flex justify-start relative ${
                index == steps.length - 1 ? "" : "flex-auto w-1/4"
              }`}
            >
              <div className="flex flex-col justify-center items-center gap-y-3">
                {index < steps.length - 1 && (
                  <div
                    className={`absolute left-15 w-[calc(100%-(var(--spacing)*10))] top-3 h-px ml-2 ${tailColorClass(
                      index
                    )}`}
                  ></div>
                )}
                <div className={`indicator w-6 h-6 border rounded-full text-sm flex justify-center items-center ${indicatorColorClass(index)}`}>
                  {index + 1}
                </div>
                <div className={`text-sm w-24 text-center ${titleColorClass(index)}`}>
                  {title}
                </div>
              </div>
            </div>
          );
        })}
      </div>
      <div className="w-full h-[50vh] overflow-y-auto" style={{ scrollbarWidth: 'none' }}>
        {steps.map((child, idx) => (
          <div
            key={idx}
            className={`w-full ${idx === currentStep ? "block" : "hidden"}`}
          >
            {child}
          </div>
        ))}
      </div>
      <div className="w-full flex justify-end items-end gap-x-2">
        <div>
          {currentStep == 0 && (
            <button
              className="px-4 py-2 text-sm font-medium rounded-md border border-[var(--border-color)] text-[var(--accent-primary)] hover:bg-[var(--accent-primary)]/10 transition-colors cursor-pointer"
              onClick={onClose}
            >
              {messages.components.stepper.cancel}
            </button>
          )}
          {currentStep > 0 && (
            <button
              className="px-4 py-2 text-sm font-medium rounded-md border border-[var(--border-color)] text-[var(--accent-primary)] hover:bg-[var(--accent-primary)]/10 transition-colors cursor-pointer"
              onClick={() => setCurrentStep((pre) => (pre > 0 ? pre - 1 : pre))}
            >
              {messages.components.stepper.previous}
            </button>
          )}
        </div>
        <div>
          {currentStep < steps.length - 1 && (
            <button
              className="px-4 py-2 text-sm font-medium rounded-md border border-transparent bg-[var(--accent-primary)]/90 text-white hover:bg-[var(--accent-primary)] transition-colors cursor-pointer"
              onClick={() =>
                setCurrentStep((pre) =>
                  pre < steps.length - 1 ? pre + 1 : pre
                )
              }
            >
              {messages.components.stepper.next}
            </button>
          )}
          {currentStep == steps.length - 1 && (
            <button
              className="px-4 py-2 text-sm font-medium rounded-md border border-transparent bg-[var(--accent-primary)]/90 text-white hover:bg-[var(--accent-primary)] transition-colors disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
              onClick={() => {
                if (onFinish) {
                  onFinish();
                }
              }}
            >
              {messages.components.stepper.generateWiki}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default Stepper;
