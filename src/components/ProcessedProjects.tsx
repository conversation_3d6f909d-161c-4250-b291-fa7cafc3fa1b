'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import {FaTimes, FaPlus, FaAlignJustify, FaEdit, FaLock, FaExclamationTriangle} from 'react-icons/fa';
import { AiOutlineSetting, AiOutlineUserSwitch } from 'react-icons/ai';
import ReactPaginate from 'react-paginate';
import { authFetch } from '@/utils/authFetch';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/contexts/ToastContext';
import { useSettings } from '@/contexts/SettingsContext';
import GrantUser from "@/components/GrantUser";
import ProjectDetail from "@/components/ProjectDetail";
import { ComponentPrivilegeEnum } from '@/types/privilege';
import {getDxpBaseUrl} from "@/utils/config";
import WikiInfoEditor from './WikiInfoEditor';
import { Tag } from '@/types/tag';
import OwnerTransfer from './OwnerTransfer';

interface Release {
  release_pkg_code: string;
  solution_name: string;
}
interface DevCloudInfo {
  product_line_name: string;
  product_name: string;
  product_version_code: string;
  releases: Release[];
}

// Interface should match the structure from the API
interface ProcessedProject {
  id: number;
  wiki_id: string;
  owner: string;
  repo: string;
  name: string;
  repo_type: string;
  submittedAt: number;
  language: string;
  branch?: string; // 新增分支字段，建议为可选
  role_code?: string;
  userName: string;
  userCode: string;
  createdBy: number;
  visibility?: number;
  dev_cloud: DevCloudInfo; 
  created_time: string;
  updated_time: string;
  tags?: Tag[];
  ownerId: number;
  project_name?: string;
  pm_name?: string;
}

interface ProcessedProjectsProps {
  showHeader?: boolean;
  className?: string;
  messages?: Record<string, Record<string, string>>; // Translation messages with proper typing
  onAddNew?: () => void; // 新增 prop
  refreshTrigger?: number; // 项目刷新触发器
}

export default function ProcessedProjects({
  showHeader = true,
  className = "",
  messages,
  onAddNew,
  refreshTrigger
}: ProcessedProjectsProps) {
  const [projects, setProjects] = useState<ProcessedProject[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  // 防抖搜索状态
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [viewMode,] = useState<'card' | 'list'>('card');
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [selectedProject, setSelectedProject] = useState<ProcessedProject | null>(null);
  const [showDropdown, setShowDropdown] = useState<string | null>(null);
  // 分页状态
  const [currentPage, setCurrentPage] = useState(0); // react-paginate 使用 0-based 索引
  const [itemsPerPage] = useState(20);
  // 后端分页状态
  const [totalItems, setTotalItems] = useState(0); // 后端返回的总条目数
  const [totalPages, setTotalPages] = useState(0); // 总页数
  // 登录状态检查（是否调用登陆接口，保证在在检查过登陆状态后采取调用查询接口）
  const [isLoginChecked, setIsLoginChecked] = useState(false);

  const { hasGrantPermission, hasDeletePermission, hasEditPermission, hasOwnerTransferPermission, hasAccessPermission, hasAddWikiPermission, hasSuperAdminPermission, userInfo } = useAuth();
  const { addToast } = useToast();
  const { settings } = useSettings();
  const [isLogin, setLogin]  = useState(false);
  const [showDetail, setShowDetail] = useState(false);
  const [showEditor, setShowEditor] = useState<boolean>(false);
  const [selectedWikiId, setSelectedWikiId] = useState<string | null>(null);
  const [showOwnerTransfer, setShowOwnerTransfer] = useState<boolean>(false);
  // 删除弹窗对应的 Wiki 信息
  const [pendingDeleteProject, setPendingDeleteProject] = useState<ProcessedProject | null>(null);
  // 删除弹窗输入的 Wiki 名称
  const [deleteConfirmInput, setDeleteConfirmInput] = useState<string>('');
  // 删除操作进行中的状态
  const [isDeletingWiki, setIsDeletingWiki] = useState<boolean>(false);
  // 删除二次确认阅读状态
  const [isDeleteAcknowledged, setIsDeleteAcknowledged] = useState<boolean>(false);
  

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showDropdown && !(event.target as Element).closest('.dropdown-container')) {
        setShowDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showDropdown]);

  const t = useCallback((key: string) => {
    // Default messages fallback
    const defaultMessages = {
      title: 'Processed Wiki Projects',
      searchPlaceholder: 'Search projects by name, owner, or repository...',
      noProjects: 'No projects found in the server cache. The cache might be empty or the server encountered an issue.',
      noSearchResults: 'No projects match your search criteria.',
      processedOn: 'Processed on:',
      loadingProjects: 'Loading projects...',
      errorLoading: 'Error loading projects:',
      backToHome: 'Back to Home',
      createNewWiki: 'Create New Wiki', // 新增
      deleteDialogTitlePrefix: 'Delete ',
      deleteDialogWarningTitle: 'Unexpected issues may occur if you ignore this warning!',
      deleteDialogWarningDescriptionPrefix: 'This will permanently delete ',
      deleteDialogWarningDescriptionSuffix: ', including its code and documents. This action cannot be undone.',
      deleteDialogUnderstand: 'I have read and understand these effects',
      deleteDialogInstructionPrefix: 'To confirm, type "',
      deleteDialogInstructionSuffix: '" in the box below',
      deleteDialogConfirm: 'Delete this wiki',
      deleteDialogProcessing: 'Deleting...'
    };

    if (messages?.projects?.[key]) {
      return messages.projects[key];
    }
    return defaultMessages[key as keyof typeof defaultMessages] || key;
  }, [messages]);

  const clearSearch = () => {
    setSearchQuery('');
  };

  // 检查登录状态（只在组件初始化时调用一次）
  const checkLoginStatus = async () => {
    try {
      const isLoggedResponse = await authFetch('/api/auth/isLogged', {
        signal: AbortSignal.timeout(5000)
      });
      
      if (isLoggedResponse && isLoggedResponse.ok) {
        const data = await isLoggedResponse.json();
        setLogin(data && data.is_login);
      } else {
        setLogin(false);
      }
    } catch (error) {
      console.error("Failed to check login status:", error);
      setLogin(false);
    } finally {
      setIsLoginChecked(true);
    }
  };

  // 防抖处理搜索查询
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500); // 500ms防抖延迟

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // 组件初始化时检查登录状态（只执行一次）
  useEffect(() => {
    checkLoginStatus();
  }, []);


  // 提取fetchProjects函数，供删除逻辑复用
  const fetchProjects = useCallback(async (page = 1, keyword = '') => {
    // 等待登录状态检查完成
    if (!isLoginChecked) {
      return;
    }

    setIsLoading(true);
    setError(null);
    
    try {
      // 直接使用已检查的登录状态，无需重复调用isLogged接口
      const endpoint = isLogin 
        ? '/api/wiki/projects-with-role'
        : '/api/wiki/projects';
      
      // 构建分页和搜索参数
      const params = new URLSearchParams({
        page: page.toString(),
        page_size: itemsPerPage.toString(),
        ...(keyword && { keyword }),
        view_type: settings.isProjectMode ? '2' : '1' // 项目模式为2，产品模式为1
      });
      
      const projectResponse = await authFetch(`${endpoint}?${params}`);

      if (projectResponse) {
        if (!projectResponse.ok) {
          throw new Error(`Failed to fetch projects: ${projectResponse.statusText}`);
        }
        const result = await projectResponse.json();
        if (result.code !== 200) {
          throw new Error(result.message);
        }
        
        const wikis = result.data.wikis as ProcessedProject[];
        setProjects(wikis);
        setTotalItems(result.data.total);
        setTotalPages(Math.ceil(result.data.total / itemsPerPage));
      }

    } catch (e: unknown) {
      console.error("Failed to load projects from API:", e);
      const message = e instanceof Error ? e.message : t('unknownError');
      setError(message);
      setProjects([]);
      setTotalItems(0);
      setTotalPages(0);
    } finally {
      setIsLoading(false);
    }
  }, [isLoginChecked, isLogin, itemsPerPage, settings.isProjectMode, t]);

  // 新增项目后跳转到第一页显示
  useEffect(() => {
    if (refreshTrigger && refreshTrigger > 0) {
      setCurrentPage(0);
    }
  }, [refreshTrigger]);

  useEffect(() => {
    const apiPage = currentPage + 1;
    fetchProjects(apiPage, debouncedSearchQuery);
  }, [currentPage, debouncedSearchQuery, itemsPerPage, isLoginChecked, settings.isProjectMode, fetchProjects]);

  // 处理分页切换
  const handlePageChange = (selectedItem: { selected: number }) => {
    const newPage = selectedItem.selected;
    setCurrentPage(newPage);
  };

  // 处理搜索（重置到第一页）
  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    // 搜索时重置到第一页，防抖后会自动触发API调用
    if (currentPage !== 0) {
      setCurrentPage(0);
    }
  };

  // 关闭删除弹窗并重置输入
  const closeDeleteModal = () => {
    setPendingDeleteProject(null);
    setDeleteConfirmInput('');
    setIsDeleteAcknowledged(false);
  };

  // 打开删除弹窗并记录当前 Wiki
  const openDeleteModal = (project: ProcessedProject) => {
    setShowDropdown(null);
    setPendingDeleteProject(project);
    setDeleteConfirmInput('');
    setIsDeleteAcknowledged(false);
  };

  // 获取弹窗中展示的 Wiki 名称
  const getWikiDisplayName = useCallback((project: ProcessedProject | null) => {
    if (!project) {
      return '';
    }

    if (settings.isProjectMode) {
      if (project.project_name && project.project_name.trim()) {
        return project.project_name;
      }
      if (project.name && project.name.trim()) {
        return project.name;
      }
    }

    if (project.name && project.name.trim()) {
      return project.name;
    }

    const branchSegment = project.branch ? `/${project.branch}` : '';
    return `${project.owner}/${project.repo}${branchSegment}`;
  }, [settings.isProjectMode]);

  // 执行具体的 Wiki 删除逻辑
  const handleDelete = async (project: ProcessedProject | null) => {
    if (!project) {
      return;
    }
    setIsDeletingWiki(true);
    try {
      const wikiInfoResponse = await authFetch(`/api/wiki/info/${project.wiki_id}/basic`);
      if (wikiInfoResponse && wikiInfoResponse.ok) {
        const result = await wikiInfoResponse.json();
        if (result && result.data.status === 'pending') {
          addToast({
            type: 'error',
            title: t('deletePending'),
            message: ''
          });
          setIsDeletingWiki(false);
          return;
        }
      }
      const response = await authFetch(`/api/wiki/project/${project.wiki_id}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
      });
      if (response && !response.ok) {
        const errorBody = await response.json().catch(() => ({ error: response.statusText }));
        throw new Error(errorBody.error || response.statusText);
      }
      
      // 删除成功后统一跳转到第一页，简化逻辑
      setCurrentPage(0);
      await fetchProjects(1, debouncedSearchQuery);
      closeDeleteModal();
    } catch (e: unknown) {
      console.error('Failed to delete project:', e);
      addToast({
        type: 'error',
        title: t('failedToDelete'),
        message: ''
      });
    }
    setIsDeletingWiki(false);
  };

  const handleAuthClick = async (project: ProcessedProject) => {
    setShowDropdown(null);
    setSelectedProject(project);
    setShowAuthModal(true);
  };

  const handleDropdownToggle = (projectId: string) => {
    setShowDropdown(showDropdown === projectId ? null : projectId);
  };

  const handleApplicationClick = async(project: ProcessedProject)=>{
    if(!userInfo) {
      addToast({
            type: 'warning',
            title: t('cannotApply'),
            message: ''
          });
      return;
    }
    if(hasSuperAdminPermission() || hasGrantPermission(project.role_code as string) || hasAccessPermission(project.role_code as string)) {
      return;
    }

    const applicationCode = userInfo?.user_code;
    const approveCode = project.userCode;
    const wikiId = project.wiki_id;
    const branch = project.branch;
    const repo = project.repo;
    const owner = project.owner;

    const url = `${getDxpBaseUrl()}?name=${repo}&branch=${branch}&project=${owner}&reqstaff=${applicationCode}&wikistaff=${approveCode}&wikiId=${wikiId}`;
    window.open(url,'_blank')
  };

  const getWikiRole = (roleCode: string) => {

    if(isLogin && (hasSuperAdminPermission() || hasGrantPermission(roleCode) || hasAccessPermission(roleCode))) {
             return (
        <span className="text-xs font-normal px-2 py-1 text-[#000000] dark:text-[#D4E1ED] rounded-full border border-[var(--border-color)]">
        <svg width="10px" height="17px" viewBox="0 0 11 11" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
            <g id="页面-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
              <g id="DeepWiki首页浅色" transform="translate(-1173, -398)" fill="currentColor" fillRule="nonzero">
              <g id="编组-6" transform="translate(715, 272)">
                  <g id="编组-5" transform="translate(450, 118)">
                      <g id="编组" transform="translate(8, 8)">
                          <path d="M9.71244342,6.01244016 L0.666948904,6.01244016 C0.298042792,6.01244016 0,5.71439737 0,5.34549126 C0,4.97658514 0.298042792,4.67854235 0.666948904,4.67854235 L9.71244342,4.67854235 C10.0813495,4.67854235 10.3793923,4.97658514 10.3793923,5.34549126 C10.3793923,5.71439737 10.0813495,6.01244016 9.71244342,6.01244016 Z" id="路径"></path>
                          <path d="M5.32725437,10.647735 C5.15634872,10.647735 4.98544306,10.5831244 4.85622171,10.4518188 C4.59569479,10.1912919 4.59569479,9.77028039 4.85622171,9.50766926 L9.03298922,5.3246491 L4.85622171,1.13954473 C4.59569479,0.879017814 4.59569479,0.455922102 4.85622171,0.195395187 C5.11674862,-0.0651317289 5.53984434,-0.0651317289 5.80037125,0.195395187 L10.4481714,4.85153222 C10.7086983,5.11205914 10.7086983,5.53307064 10.4481714,5.79359755 L5.80037125,10.4518188 C5.66906569,10.5831244 5.49816003,10.647735 5.32725437,10.647735 L5.32725437,10.647735 Z" id="路径"></path>
                      </g>
                  </g>
              </g>
            </g>
          </g>
        </svg>
        </span>);
    }
         return (
       <span className="flex items-center gap-1 px-2 py-1 bg-[#F6F6F6] dark:bg-[#1F293B] text-[#000000] dark:text-[#D4E1ED] rounded-full border border-[var(--border-color)]">
         <span className="text-xs font-normal text-[#2C7FFF]">{t('applyPermission')}</span>
         <svg width="10px" height="11px" viewBox="0 0 11 11" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
           <g id="页面-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
             <g id="DeepWiki首页浅色" transform="translate(-1173, -398)" fill="currentColor" fillRule="nonzero">
             <g id="编组-6" transform="translate(715, 272)">
                 <g id="编组-5" transform="translate(450, 118)">
                     <g id="编组" transform="translate(8, 8)">
                         <path d="M9.71244342,6.01244016 L0.666948904,6.01244016 C0.298042792,6.01244016 0,5.71439737 0,5.34549126 C0,4.97658514 0.298042792,4.67854235 0.666948904,4.67854235 L9.71244342,4.67854235 C10.0813495,4.67854235 10.3793923,4.97658514 10.3793923,5.34549126 C10.3793923,5.71439737 10.0813495,6.01244016 9.71244342,6.01244016 Z" id="路径"></path>
                         <path d="M5.32725437,10.647735 C5.15634872,10.647735 4.98544306,10.5831244 4.85622171,10.4518188 C4.59569479,10.1912919 4.59569479,9.77028039 4.85622171,9.50766926 L9.03298922,5.3246491 L4.85622171,1.13954473 C4.59569479,0.879017814 4.59569479,0.455922102 4.85622171,0.195395187 C5.11674862,-0.0651317289 5.53984434,-0.0651317289 5.80037125,0.195395187 L10.4481714,4.85153222 C10.7086983,5.11205914 10.7086983,5.53307064 10.4481714,5.79359755 L5.80037125,10.4518188 C5.66906569,10.5831244 5.49816003,10.647735 5.32725437,10.647735 L5.32725437,10.647735 Z" id="路径"></path>
                     </g>
                 </g>
             </g>
           </g>
         </g>
       </svg>
       </span>);

  };

  const getWikiRoleColor =(roleCode: string) => {
    if(isLogin && (hasSuperAdminPermission() || hasGrantPermission(roleCode) || hasAccessPermission(roleCode))) {
      return 'text-[#079320] text-[0.875rem]';
    }
    return 'text-[0.875rem] text-[#3357FF]';
  };

  const formatDateTimeLocale = (dateString: string): string => {
    const date = new Date(dateString);
    
    if (isNaN(date.getTime())) {
      throw new Error('Invalid date string');
    }
    
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      // hour: '2-digit',
      // minute: '2-digit',
      // second: '2-digit',
      hour12: false
    }).replace(/\//g, '-');
  }

  const pendingDeleteName = getWikiDisplayName(pendingDeleteProject);
  const deleteTargetName = pendingDeleteName || pendingDeleteProject?.name || '';

  return (
    <div className={`${className}`}>
      {showHeader && (
        <header className="mb-6">
          <div className="flex items-center justify-between">
            <h1 className="text-3xl font-bold text-[var(--accent-primary)]">{t('title')}</h1>
            <Link href="/" className="text-[var(--accent-primary)] hover:underline">
              {t('backToHome')}
            </Link>
          </div>
        </header>
      )}

      {/* Search Bar and View Toggle */}
      <div className="mb-10 flex flex-col sm:flex-row gap-4">
        {/* Search Bar */}
        <div className="relative flex-1 flex justify-center">
          <div className="relative w-2/3">
            {/* 搜索图标 */}
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" className="text-[#D0D7DE] dark:text-[#30405D]" viewBox="0 0 18 18" version="1.1" fill="currentColor">
                <g id="页面-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                  <g id="DeepWiki首页浅色" transform="translate(-546, -201)" fill="currentColor" fillRule="nonzero">
                    <g id="编组-8" transform="translate(535, 187)">
                   <g id="编组" transform="translate(11, 14)">
                    <path d="M17.6347885,15.8877138 L14.5132763,12.7635014 C16.8139064,9.62308731 16.5438794,5.18924392 13.7058956,2.35126013 C10.5708821,-0.783753375 5.48627363,-0.783753375 2.35126013,2.35126013 C-0.783753375,5.48627363 -0.783753375,10.5708821 2.35126013,13.7058956 C5.18924392,16.5438794 9.62308731,16.8139064 12.7635014,14.5132763 L15.8877138,17.6374887 C16.3710621,18.1208371 17.1514401,18.1208371 17.6347885,17.6374887 C18.1181368,17.1514401 18.1181368,16.3710621 17.6347885,15.8877138 Z M3.66089109,12.3935644 C1.24954995,9.98222322 1.24954995,6.07223222 3.66089109,3.66089109 C6.07223222,1.24954995 9.98222322,1.24954995 12.3935644,3.66089109 C14.8049055,6.07223222 14.8049055,9.98222322 12.3935644,12.3935644 C9.98492349,14.8049055 6.07493249,14.8049055 3.66089109,12.3935644 Z" id="形状"/>
                  </g>
                  </g>
                </g>
              </g>
            </svg>
            </div>
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
              placeholder={t('searchPlaceholder')}
              className="input-japanese block w-full pl-10 pr-12 py-2.5 border border-[var(--border-color)] rounded-md bg-[var(--background)] text-[var(--foreground)] placeholder:text-sm placeholder:font-normal placeholder:text-[#023063] dark:placeholder:text-[#94A3B8] focus:outline-none focus:border-[var(--accent-primary)] focus:ring-1 focus:ring-[var(--accent-primary)]"
            />
            {searchQuery && (
              <button
                onClick={clearSearch}
                className="absolute inset-y-0 right-0 flex items-center pr-3 text-[var(--muted)] hover:text-[var(--foreground)] transition-colors"
              >
                <FaTimes className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>
      </div>

      {isLoading && <p className="text-[var(--muted)]">{t('loadingProjects')}</p>}
      {error && <p className="text-[var(--highlight)]">{t('errorLoading')} {error}</p>}

      {!isLoading && !error && (
        <div className={viewMode === 'card' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4' : 'space-y-3'}>
          {!settings.isProjectMode && onAddNew && hasAddWikiPermission(ComponentPrivilegeEnum.CREATE_WIKI) && (
            viewMode === 'card' ? (
              <div className="relative card-elegant p-5 flex items-center justify-center cursor-pointer" onClick={onAddNew}>
                <div className="text-center">
                  <FaPlus className="text-4xl text-[var(--project-text-color)] mb-2 mx-auto" />
                  <h3 className="text-lg font-semibold text-[var(--project-text-color)]">{t('createNewWiki')}</h3>
                </div>
              </div>
            ) : (
              <div className="p-3 border border-[var(--border-color)] rounded-lg bg-[var(--card-bg)] hover:bg-[var(--background)] transition-colors cursor-pointer" onClick={onAddNew}>
                <div className="flex items-center justify-center">
                  <FaPlus className="text-xl text-[var(--accent-primary)] mr-2" />
                  <h3 className="text-base font-medium text-[var(--link-color)]">{t('createNewWiki')}</h3>
                </div>
              </div>
            )
          )}
          {projects.map((project) => (
            viewMode === 'card' ? (
              <div key={project.id} className="relative card-elegant">
                <div className="absolute top-2 right-2 flex items-center gap-1 z-10">
                  {isLogin && <button
                    type="button"
                    onClick={() => handleDropdownToggle(project.wiki_id)}
                    className="p-1 rounded-full text-[#000000] dark:text-[#D4E1ED] hover:text-[var(--foreground)] hover:bg-[var(--accent-secondary)] transition-colors"
                    title="More options"
                  >
                    <svg width="16px" height="16px" viewBox="0 0 15 3" version="1.1" xmlns="http://www.w3.org/2000/svg">
                      <title>更多</title>
                        <g id="页面-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                          <g id="DeepWiki首页浅色" transform="translate(-1682, -291)" fill="currentColor" fillRule="nonzero">
                            <g id="编组-6" transform="translate(1220, 272)">
                              <g id="更多" transform="translate(462, 19)">
                              <rect id="矩形" x="0" y="0" width="3" height="3"></rect>
                              <rect id="矩形备份-2" x="6" y="0" width="3" height="3"></rect>
                              <rect id="矩形备份-3" x="12" y="0" width="3" height="3"></rect>
                              </g>
                            </g>
                          </g>
                        </g>
                      </svg>
                  </button>}
                </div>

                {/* Dropdown Menu */}
                {showDropdown === project.wiki_id && (
                  <div className="dropdown-container absolute top-2 right-2 bg-[var(--card-bg)] border border-[var(--border-color)] rounded-lg shadow-lg z-[9999] min-w-[100px] flex flex-col justify-center py-2 text-left">
                    {(hasGrantPermission(project?.role_code as string)) && <button
                      onClick={() => handleAuthClick(project)}
                      className="flex items-center justify-start gap-2 w-full px-3 py-1 text-sm text-[var(--foreground)] hover:bg-[var(--accent-primary)]/10 dark:hover:bg-[var(--accent-primary)]/20 transition-colors cursor-pointer"
                      title={t('grant')}
                    >
                      <AiOutlineSetting className="h-4 w-4 text-[var(--muted)]" />
                      {t('grant')}
                    </button>}

                    {hasOwnerTransferPermission(project?.ownerId) && <button
                      onClick={() => {
                        setSelectedWikiId(project.wiki_id);
                        setShowOwnerTransfer(true);
                        setShowDropdown(null);
                      }}
                      className="flex items-center justify-start gap-2 w-full px-3 py-1 text-sm text-[var(--foreground)] hover:bg-[var(--accent-primary)]/10 dark:hover:bg-[var(--accent-primary)]/20 transition-colors cursor-pointer"
                      title={t('transferWikiOwner')}
                    >
                      <AiOutlineUserSwitch className="h-4 w-4 text-[var(--muted)]" />
                      {t('transferWikiOwner')}
                    </button>}
                    

                    <button
                        type="button"
                        onClick={() => {
                          setSelectedWikiId(project.wiki_id);
                          setShowDetail(true);
                          setShowDropdown(null);
                        }}
                        className="flex items-center justify-start gap-2 w-full px-3 py-1 text-sm text-[var(--danger)] hover:bg-[var(--accent-primary)]/10 dark:hover:bg-[var(--accent-primary)]/20 transition-colors cursor-pointer"
                        title={t('queryWikDetail')}
                      >
                        <FaAlignJustify className="h-4 w-4 text-[var(--muted)]" />
                        {t('queryWikDetail')}
                      </button>

                    {hasEditPermission(project.ownerId) && (
                      <button
                        type="button"
                        onClick={() => {
                          setSelectedWikiId(project.wiki_id);
                          setShowEditor(true);
                          setShowDropdown(null);
                        }}
                        className="flex items-center justify-start gap-2 w-full px-3 py-1 text-sm text-[var(--danger)] hover:bg-[var(--accent-primary)]/10 dark:hover:bg-[var(--accent-primary)]/20 transition-colors cursor-pointer"
                        title={t('modify')}
                      >
                        <FaEdit className="h-4 w-4 text-[var(--muted)]" />
                        {t('modify')}
                      </button>
                    )}

                    {((hasDeletePermission(project?.ownerId, ComponentPrivilegeEnum.DELETE_WIKI))) && (
                      <button
                        type="button"
                        onClick={() => openDeleteModal(project)}
                        className="flex items-center justify-start gap-2 w-full px-3 py-1 text-sm text-[var(--danger)] hover:bg-[var(--accent-primary)]/10 dark:hover:bg-[var(--accent-primary)]/20 transition-colors cursor-pointer"
                        title={t('delete')}
                      >
                        <FaTimes className="h-4 w-4 text-[var(--muted)]" />
                        {t('delete')}
                      </button>
                    )}

                  </div>

                )}

                 {showDetail && selectedWikiId===project.wiki_id && (
                    <ProjectDetail isProjectMode={settings.isProjectMode} wiki_id={selectedWikiId} role_code={project?.role_code} onClose={() => setShowDetail(false)}
                    />
                )}

                {showEditor && selectedWikiId===project.wiki_id && (
                    <WikiInfoEditor
                      wiki_id={selectedWikiId}
                      onClose={() => setShowEditor(false)}
                      onFinish={() => {
                        setShowEditor(false);
                        fetchProjects((currentPage + 1), debouncedSearchQuery);
                      }}
                    />
                )}

                {showOwnerTransfer && selectedWikiId===project.wiki_id && (
                  <OwnerTransfer 
                    wiki_id={selectedWikiId}
                    onClose={() => setShowOwnerTransfer(false)}
                    onFinish={() => {
                      setShowOwnerTransfer(false);
                      fetchProjects((currentPage + 1), debouncedSearchQuery);
                    }}
                  />
                )}

                <Link
                    href={(hasAccessPermission(project?.role_code as string)) ? `/wiki/${project.id}` : '#'}
                    className="block"
                    prefetch={false}
                    onClick={(e) => {
                      if (!(hasAccessPermission(project?.role_code as string))) {
                        e.preventDefault();
                        addToast({
                          type: 'warning',
                          title: t('cannotAccess'),
                          message: isLogin ? t('pleaseApplyPermission'): t('pleaseLogin')
                        });
                      }
                    }}
                  >
                {/* 第一部分：标题和省略号按钮 */}
                <div className="pt-[9px] pl-[13px] pr-8 pb-[11px]">
                    <h3 className="text-sm font-semibold text-[var(--project-text-color)] text-left truncate" title={settings.isProjectMode ? project.project_name : `${project.owner}/${project.repo}/${project.branch}`}>
                      {settings.isProjectMode ? project.project_name : `${project.owner}/${project.repo}/${project.branch}`}
                    </h3>
                </div>
                
                {/* 分割线 - 占据整个卡片宽度 */}
                <div className="w-full h-px bg-[var(--border-color)]"></div>
                
                {/* 第二部分：信息展示区域 */}
                <div className="pt-[11px] px-4 pb-4 flex-grow">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-x-10 gap-y-2">
                    {!settings.isProjectMode && <div className="flex justify-between items-center min-w-0">
                      <span className="text-xs font-semibold text-[#8EA5BF] dark:text-[#748798] flex-shrink-0">{t('productLine')}</span>
                      <span className="text-xs font-normal text-[#8EA5BF] dark:text-[#748798] truncate ml-2" title={project.dev_cloud?.product_line_name || '未知'}>{project.dev_cloud?.product_line_name || '未知'}</span>
                    </div>}
                    {!settings.isProjectMode && <div className="flex justify-between items-center min-w-0">
                      <span className="text-xs font-semibold text-[#8EA5BF] dark:text-[#748798] flex-shrink-0">{t('productName')}</span>
                      <span className="text-xs font-normal text-[#8EA5BF] dark:text-[#748798] truncate ml-2" title={project.dev_cloud?.product_name || '未知'}>{project.dev_cloud?.product_name || '未知'}</span>
                    </div>}
                    <div className="flex justify-between items-center min-w-0">
                      <span className="text-xs font-semibold text-[#8EA5BF] dark:text-[#748798] flex-shrink-0">{settings.isProjectMode ? t('projectVersion') : t('productVersion')}</span>
                      <span className="text-xs font-normal text-[#8EA5BF] dark:text-[#748798] truncate ml-2" title={settings.isProjectMode ? project.dev_cloud.releases[0]?.release_pkg_code :project.dev_cloud?.product_version_code || '未知'}>{settings.isProjectMode ? project.dev_cloud.releases[0]?.release_pkg_code : project.dev_cloud?.product_version_code || '未知'}</span>
                    </div>
                    {settings.isProjectMode && <div className="flex justify-between items-center min-w-0">
                      <span className="text-xs font-semibold text-[#8EA5BF] dark:text-[#748798] flex-shrink-0">{t('solution')}</span>
                      <span className="text-xs font-normal text-[#8EA5BF] dark:text-[#748798] truncate ml-2" title={(project?.dev_cloud?.releases ?? []).map(item => item.solution_name).join(", ")}>{(project?.dev_cloud?.releases ?? []).map(item => item.solution_name).join(", ")}</span>
                    </div>}
                    {settings.isProjectMode && <div className="flex justify-between items-center min-w-0">
                      <span className="text-xs font-semibold text-[#8EA5BF] dark:text-[#748798] flex-shrink-0">{t('projectManager')}</span>
                      <span className="text-xs font-normal text-[#8EA5BF] dark:text-[#748798] truncate ml-2" title={project.pm_name || '未知'}>{project.pm_name || '未知'}</span>
                    </div>}
                    <div className="flex justify-between items-center min-w-0">
                      <span className="text-xs font-semibold text-[#8EA5BF] dark:text-[#748798] flex-shrink-0">{t('updatedTime')}</span>
                      <span className="text-xs font-normal text-[#8EA5BF] dark:text-[#748798] truncate ml-2" title={project.updated_time}>{formatDateTimeLocale(project.updated_time)}</span>
                    </div>
                    <div className="flex justify-between items-center min-w-0">
                      <span className="text-xs font-semibold text-[#8EA5BF] dark:text-[#748798] flex-shrink-0">{t('owner')}</span>
                      <span className="text-xs font-normal text-[#8EA5BF] dark:text-[#748798] truncate ml-2" title={project.userName + project.userCode}>{project.userName ? `${project.userName}[${project.userCode}]` : '未知'}</span>
                    </div>
                    <div className="flex justify-between items-center min-w-0">
                      <span className="text-xs font-semibold text-[#8EA5BF] dark:text-[#748798] flex-shrink-0">{t('createdTime')}</span>
                      <span className="text-xs font-normal text-[#8EA5BF] dark:text-[#748798] truncate ml-2" title={project.created_time}>{formatDateTimeLocale(project.created_time)}</span>
                    </div>
                  </div>
                </div>
                
                {/* 第三部分：底部信息 */}
                <div className="px-[13px] pb-[15px] flex items-center justify-between">
                 <div className="flex items-center gap-2 text-xs">
                       {project.tags && project.tags.length > 0 ? (
                        <>
                          {project.tags.slice(0, 3).map((tag) => (
                            <span
                              key={tag.name}
                              className="text-xs font-normal px-2 py-1 border border-[var(--border-color)] max-w-[90px] truncate inline-block"
                              style={{
                                backgroundColor: tag.color + '20', // 添加透明度
                                color: tag.color,
                                borderColor: tag.color + '40'
                              }}
                              title={tag.comments || tag.name}
                            >
                              {tag.name}
                            </span>
                          ))}
                          {project.tags.length > 3 && (
                            <span
                              className="text-xs font-normal px-2 py-1 rounded-full border border-[var(--border-color)] bg-[#EAECF1] dark:bg-[#1F293B] text-[var(--project-text-color)]"
                              title={project.tags.slice(3).map(tag => tag.name).join(', ')}
                            >
                              +{project.tags.length - 3}
                            </span>
                          )}
                        </>
                      ) : null
                     }
                   </div>
                  <button onClick={(e) => {
                    // 已经登录，并且具备访问权限
                    if(isLogin && (hasSuperAdminPermission() || hasGrantPermission(project?.role_code as string) || hasAccessPermission(project?.role_code as string))) {

                    } else {
                      e.stopPropagation();
                      handleApplicationClick(project);
                    }
                  }}
                      className={`flex items-center justify-start gap-1 ${getWikiRoleColor(project?.role_code as string)} transition-colors cursor-pointer text-xs`}>
                    {getWikiRole(project?.role_code as string)}
                  </button>
                </div>
                </Link>
              </div>
            ) : (
              <div key={project.id} className="relative p-3 border border-[var(--border-color)] rounded-lg bg-[var(--card-bg)] hover:bg-[var(--background)] transition-colors">
                <button
                  type="button"
                  onClick={() => openDeleteModal(project)}
                  className="absolute top-2 right-2 text-[var(--muted)] hover:text-[var(--foreground)]"
                  title="Delete project"
                >
                  <FaTimes className="h-4 w-4" />
                </button>
                <Link
                  href={`/${project.owner}/${project.repo}?type=${project.repo_type}&language=${project.language}${project.branch ? `&branch=${encodeURIComponent(project.branch)}` : ''}`}
                  className="flex items-center justify-between"
                >
                  <div className="flex-1 min-w-0">
                    <h3 className="text-base font-medium text-[var(--link-color)] hover:underline truncate">
                      {project.name}
                    </h3>
                    <p className="text-xs text-[var(--muted)] mt-1">
                      {t('processedOn')} {new Date(project.submittedAt).toLocaleDateString()} • {project.repo_type} • {project.language}
                      {project.branch ? ` • ${project.branch}` : ''}
                    </p>
                  </div>
                  <div className="flex gap-2 ml-4">
                    <span className="px-2 py-1 text-xs bg-[var(--accent-primary)]/10 text-[var(--accent-primary)] rounded border border-[var(--accent-primary)]/20">
                      {project.repo_type}
                    </span>
                    {project.branch && (
                      <span className="px-2 py-1 text-xs bg-[var(--accent-primary)]/20 text-[var(--accent-primary)] rounded border border-[var(--accent-primary)]/10">
                        {project.branch}
                      </span>
                    )}
                  </div>
                </Link>
              </div>
            )
          ))}
        </div>
      )}

      {/* 分页组件 - 只在有数据时显示 */}
      {!isLoading && !error && totalPages > 1 && (
        <div className="mt-6 flex justify-end">
          <ReactPaginate
            pageCount={totalPages}
            pageRangeDisplayed={5}
            marginPagesDisplayed={2}
            onPageChange={handlePageChange}
            forcePage={currentPage}
            containerClassName="flex flex-row items-center gap-1"
            pageClassName=""
            pageLinkClassName="pagination-link"
            previousClassName=""
            previousLinkClassName="pagination-link"
            nextClassName=""
            nextLinkClassName="pagination-link"
            breakClassName=""
            breakLinkClassName="pagination-link"
            activeClassName=""
            activeLinkClassName="pagination-link-active"
            disabledClassName=""
            disabledLinkClassName="pagination-link-disabled"
            previousLabel="‹"
            nextLabel="›"
            breakLabel="..."
          />
        </div>
      )}

      {!isLoading && !error && searchQuery && totalItems === 0 && (
        <p className="text-[var(--muted)]">{t('noSearchResults')}</p>
      )}

      {!isLoading && !error && projects.length === 0 && !searchQuery && (
        <p className="text-[var(--muted)]">{t('noProjects')}</p>
      )}

      {/* Authorization Modal */}
      {showAuthModal && selectedProject && <GrantUser wiki_id={selectedProject.id} project_name={selectedProject.owner + '/' + selectedProject.repo}
                                                      onClose={()=>setShowAuthModal(false)} visibility={selectedProject?.visibility}
                                                      onGrantSuccess={(visibility: number)=>{
                                                        setProjects(prev => prev.map(p => p.id === selectedProject?.id ? { ...p, visibility: visibility } : p));
                                                      }}
      />
      }

      {/* 删除 Wiki 确认弹窗 */}
      {pendingDeleteProject && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
          <div className="w-full max-w-xl rounded-xl border border-[var(--border-color)] bg-[var(--card-bg)] shadow-2xl">
            <div className="flex items-center justify-between border-b border-[var(--border-color)] px-6 py-4">
              <div className="flex-1 min-w-0 pr-4">
                <h3
                  className="truncate text-lg font-semibold text-[var(--foreground)] text-left"
                  title={`${t('deleteDialogTitlePrefix')}${deleteTargetName}`}
                >
                  {`${t('deleteDialogTitlePrefix')}${deleteTargetName}`}
                </h3>
              </div>
              <button
                type="button"
                onClick={closeDeleteModal}
                className="p-2 text-[var(--muted)] hover:text-[var(--foreground)] hover:bg-[var(--accent-secondary)] rounded-full transition-colors"
              >
                <FaTimes className="h-4 w-4" />
              </button>
            </div>
            <div className="px-6 py-6">
              <div className="flex flex-col items-center gap-3 text-center">
                <div className="flex h-12 w-12 items-center justify-center rounded-full border border-[var(--border-color)] bg-[var(--accent-secondary)]">
                  <FaLock className="h-5 w-5 text-[var(--muted)]" />
                </div>
                <h4
                  className="max-w-full truncate text-xl font-semibold text-[var(--foreground)]"
                  title={deleteTargetName}
                >
                  {deleteTargetName}
                </h4>
              </div>

              {/* 删除弹窗警告阶段内容 */}
              {!isDeleteAcknowledged ? (
                <>
                  <div className="mt-6 rounded-lg border border-yellow-200 bg-yellow-50 px-4 py-3 text-left dark:border-yellow-600/60 dark:bg-yellow-400/10">
                    <div className="flex items-start gap-3">
                      <FaExclamationTriangle className="mt-0.5 h-5 w-5 text-yellow-500" />
                      <div className="space-y-2">
                        <p className="text-sm font-semibold text-yellow-800 dark:text-yellow-200">
                          {t('deleteDialogWarningTitle')}
                        </p>
                        <p className="text-sm leading-6 text-[var(--foreground)]">
                          {t('deleteDialogWarningDescriptionPrefix')}
                          <span className="font-semibold">{deleteTargetName}</span>
                          {t('deleteDialogWarningDescriptionSuffix')}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="mt-6">
                    <button
                      type="button"
                      onClick={() => setIsDeleteAcknowledged(true)}
                      className="w-full rounded-md bg-[var(--accent-secondary)] px-4 py-2 text-sm font-semibold text-[var(--foreground)] hover:bg-[var(--accent-primary)]/10 transition-colors"
                    >
                      {t('deleteDialogUnderstand')}
                    </button>
                  </div>
                </>
              ) : (
                <>
                  {/* 删除弹窗确认输入阶段内容 */}
                  <p className="mt-6 text-sm text-[var(--foreground)]">
                    {t('deleteDialogInstructionPrefix')}
                    <span className="font-semibold">{deleteTargetName}</span>
                    {t('deleteDialogInstructionSuffix')}
                  </p>
                  <input
                    type="text"
                    value={deleteConfirmInput}
                    onChange={(event) => setDeleteConfirmInput(event.target.value)}
                    placeholder={deleteTargetName}
                    className="mt-4 w-full rounded-md border border-[var(--border-color)] bg-[var(--background)] px-4 py-2 text-[var(--foreground)] focus:border-[#dc2626] focus:outline-none focus:ring-1 focus:ring-[#dc2626]"
                  />
                  <button
                    type="button"
                    onClick={() => handleDelete(pendingDeleteProject)}
                    className="mt-5 w-full rounded-md bg-[#dc2626] px-4 py-2 text-sm font-semibold text-white transition-colors hover:bg-[#b91c1c] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#dc2626] disabled:cursor-not-allowed disabled:opacity-60 dark:bg-[#b91c1c] dark:hover:bg-[#dc2626]"
                    disabled={isDeletingWiki || deleteConfirmInput.trim() !== deleteTargetName}
                  >
                    {isDeletingWiki ? t('deleteDialogProcessing') : t('deleteDialogConfirm')}
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
