import React from 'react';
import { getSandboxStatusDescription } from '@/utils/sandboxStatusMap';
import Header from './Header';
import { useLanguage } from '@/contexts/LanguageContext';

// 沙盒状态比对辅助函数
const isStatusMatch = (status1: string, status2: string): boolean => {
  return status1?.toString().trim().toUpperCase() === status2?.toString().trim().toUpperCase();
};

interface SandboxStatusDisplayProps {
  sandboxStatus: string;
  sandboxPolling: boolean;
  sandboxError: string | null;
  isShareMode: boolean;
  onShare: () => void;
  onRetry?: () => void;
  onOpenSettings?: () => void;
}

/**
 * 沙箱状态显示组件
 * 当沙箱正在初始化或出现错误时显示
 */
const SandboxStatusDisplay: React.FC<SandboxStatusDisplayProps> = ({
  sandboxStatus,
  sandboxPolling,
  sandboxError,
  isShareMode,
  onShare,
  onRetry,
  onOpenSettings
}) => {
  const { messages, language } = useLanguage();
  // 只有在明确不需要显示时才返回null
  // 当前逻辑：只要调用了这个组件，就应该显示某种状态
  // 这个检查主要是为了兼容性，实际上SearchPage的逻辑已经控制了何时显示这个组件

  // 判断是否显示重试按钮
  const showRetryButton = sandboxError && onRetry && (
    isStatusMatch(sandboxStatus, 'FAILED') || 
    isStatusMatch(sandboxStatus, 'QUERY_FAILED') || 
    sandboxError.includes('轮询超时')
  );

  // 获取状态对应的图标
  const getStatusIcon = (status: string, isPolling: boolean) => {
    if (sandboxError && (isStatusMatch(status, 'FAILED') || isStatusMatch(status, 'QUERY_FAILED'))) {
      return (
        <div className="w-16 h-16 mx-auto mb-6 text-red-500">
          <svg className="w-full h-full" viewBox="0 0 24 24" fill="none" stroke="currentColor" aria-hidden="true">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
      );
    }
    
    if (isPolling || isStatusMatch(status, 'CREATING') || isStatusMatch(status, 'INITIALIZING') || (!status && !isPolling)) {
      return (
        <div className="relative w-16 h-16 mx-auto mb-6">
          <div className="absolute inset-0 rounded-full border-4 border-blue-200 dark:border-blue-800"></div>
          <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-blue-500 animate-spin"></div>
          <div className="absolute inset-2 rounded-full bg-blue-50 dark:bg-blue-900/20 flex items-center justify-center">
            <svg className="w-6 h-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
        </div>
      );
    }

    return (
      <div className="w-16 h-16 mx-auto mb-6 text-gray-400">
        <svg className="w-full h-full" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
        </svg>
      </div>
    );
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-gray-200">
      {!isShareMode && <Header onShare={onShare} showJobs={false} onOpenSettings={onOpenSettings} />}
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="max-w-md w-full text-center">
          {/* 状态图标 */}
          {getStatusIcon(sandboxStatus, sandboxPolling)}
          
          {/* 主标题 */}
          <h3 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
            {messages.components.sandboxStatusDisplay.smartSandboxEnvironment}
          </h3>
          
          {/* 状态描述 */}
          <div className="mb-6">
            <p className="text-lg text-gray-700 dark:text-gray-300 mb-2">
              {sandboxError ? messages.components.sandboxStatusDisplay.currentSmartSandboxResourceBusy : (
                !sandboxStatus && !sandboxPolling 
                  ? messages.components.sandboxStatusDisplay.preparingSmartSandboxEnvironment 
                  : getSandboxStatusDescription(sandboxStatus, language)
              )}
            </p>
            
            {/* 错误详情 */}
            {sandboxError && (
              <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <p className="text-sm text-red-700 dark:text-red-300">
                  {sandboxError}
                </p>
              </div>
            )}
            
            {/* 进度提示 */}
            {(sandboxPolling || (!sandboxStatus && !sandboxError)) && !sandboxError && (
              <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  {!sandboxStatus && !sandboxPolling 
                    ? messages.components.sandboxStatusDisplay.initializingEnvironmentConfiguration 
                    : messages.components.sandboxStatusDisplay.environmentPreparationTime 
                  }
                </p>
                {isStatusMatch(sandboxStatus, 'CREATING') && (
                  <p className="text-xs text-blue-600 dark:text-blue-400 mt-2">
                    {messages.components.sandboxStatusDisplay.assigningComputeResourcesAndPullingCodeRepository}
                  </p>
                )}
                {isStatusMatch(sandboxStatus, 'INITIALIZING') && (
                  <>
                    <p className="text-xs text-blue-600 dark:text-blue-400 mt-2">
                      {messages.components.sandboxStatusDisplay.installingDependenciesAndConfiguringDevelopmentEnvironment}
                    </p>
                    <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                      {messages.components.sandboxStatusDisplay.detectingGeminiCliSession}
                    </p>
                  </>
                )}
              </div>
            )}
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-center gap-3">
            {showRetryButton && (
              <button 
                onClick={onRetry} 
                className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                {messages.components.sandboxStatusDisplay.retry}
              </button>
            )}
          </div>
          
          {/* 帮助提示 */}
          <div className="mt-8 text-xs text-gray-500 dark:text-gray-400 space-y-1">
            <p>{messages.components.sandboxStatusDisplay.smartSandboxProvidesIndependentCodeExecutionEnvironment}</p>
            <p>{messages.components.sandboxStatusDisplay.supportSafeCodeAnalysisAndToolCall}</p>
            {sandboxPolling && (
              <p className="text-blue-600 dark:text-blue-400">{messages.components.sandboxStatusDisplay.firstCreationEnvironmentMayTakeLongTime}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SandboxStatusDisplay; 