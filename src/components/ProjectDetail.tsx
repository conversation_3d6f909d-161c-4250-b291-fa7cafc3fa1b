'use client';

import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { FaTimes, FaPlus, FaSearch, FaEdit, FaTrash, FaCheck, FaRegCopy, FaSync, FaChevronDown } from 'react-icons/fa';
import { authFetch } from '@/utils/authFetch';
import { useToast } from '@/contexts/ToastContext';
import GrantUser from "@/components/GrantUser";
import {useAuth} from "@/contexts/AuthContext";
import { Tag } from '@/types/tag';
import Popconfirm from './Popconfirm';
import { useLanguage } from '@/contexts/LanguageContext';
import { useSettings } from '@/contexts/SettingsContext';

interface WikiInfo {
  id: number;
  wiki_id: string;
  repo_url: string;
  branch: string;
  repo_owner: string;
  repo_name: string;
  repo_type: string;
  provider: string;
  model: string;
  language: string;
  comprehensive: boolean;
  created_time: string;
  updated_time: string;
  created_by: number;
  status: string;
  user_name?: string;
  user_code?: string;
  tags: Tag[];
  dev_cloud: DevCloud | null;
  visibility: number;
  pm_name?: string;
  products?: Product[];
}

interface Product {
  productName: string;
  repo_url: string;
}

interface DevCloud {
  product_id: number | null;
  product_line_id: number | null;
  product_line_name: string | null;
  product_name: string | null;
  product_version_code: string | null;
  product_version_id: number | null;
  releases: Release[] | null;
}

interface Release {
  release_pkg_code: string | null;
  release_pkg_id: number | null;
  solution_id: number | null;
  solution_name: string | null;
}

interface RepoSyncStat {
  repo_id: number;
  repo_url: string;
  branch: string;
  pending_files: number;
  total_files: number;
  indexed_files?: number;
  docchain_total_files?: number;
  expected_files?: number;
  commit_status?: string;
  job_status?: string;
  job_pending_files?: number;
  job_total_files?: number;
  job_message?: string;
  namespace?: string;
  head_commit?: string | null;
  last_sync_commit?: string | null;
  status_breakdown?: Record<string, number>;
}

interface WikiSyncSummaryState {
  pending_files: number;
  total_files: number;
  indexed_files: number;
  unexpected_files?: number;
  repositories: RepoSyncStat[];
  status?: string;
  job_id?: string;
  stage_message?: string;
}

interface SyncProgressState {
  progress: number;
  processed_files: number;
  total_files: number;
  message: string;
}

const ACTIVE_SYNC_STATUSES = new Set(['pending', 'running', 'processing', 'queued', 'syncing', 'refreshing']);
const TERMINAL_SYNC_STATUSES = new Set(['completed', 'failed', 'error', 'idle']);

const normalizeCount = (value: unknown): number => {
  if (typeof value === 'number' && Number.isFinite(value)) {
    return value;
  }
  if (typeof value === 'string') {
    const parsed = Number(value);
    return Number.isFinite(parsed) ? parsed : 0;
  }
  return 0;
};

// interface WikiTag {
//   wiki_tag_id: number;
//   wiki_id: number;
//   tag_id: number;
//   tag_name: string;
//   tag_type: number;
//   tag_color: string;
//   tag_comments?: string;
//   tag_module_type: number;
//   tag_state: number;
//   created_by: number;
//   created_date: string;
//   update_by?: number;
//   update_date?: string;
// }

interface UserInfo {
  user_id: number;
  user_name: string;
  user_code: string;
  role_code: string;
  created_date: string;
  creator_info: {
    creator_name: string;
    creator_code: string;
  };
}

interface ProjectDetailProps {
  wiki_id: string;
  role_code?: string;
  onClose?: () => void;
  isProjectMode?: boolean;
}

export default function ProjectDetail({ wiki_id, role_code, onClose, isProjectMode }: ProjectDetailProps) {
  const { addToast } = useToast();
  const [wikiInfo, setWikiInfo] = useState<WikiInfo | null>(null);
  const [users, setUsers] = useState<UserInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [editingUserId, setEditingUserId] = useState<number | null>(null);
  const [editingRoleCode, setEditingRoleCode] = useState<string>('');
  const { hasGrantPermission, hasAccessPermission, userInfo, hasSuperAdminPermission } = useAuth();
  const {messages, language} = useLanguage();
  const {settings} = useSettings();
  
  const scrollRef = useRef<HTMLDivElement>(null);
  const [scrollbarWidth, setScrollbarWidth] = useState(0);
  const [renderTime, setRenderTime] = useState<Date>();

  const [showCodeIndexDetails, setShowCodeIndexDetails] = useState(false);
  const [syncSummary, setSyncSummary] = useState<WikiSyncSummaryState | null>(null);
  const [syncProgress, setSyncProgress] = useState<SyncProgressState | null>(null);
  const [isSyncSummaryLoading, setIsSyncSummaryLoading] = useState(false);
  const [isSyncingIndex, setIsSyncingIndex] = useState(false);
  const syncSummaryRef = useRef<WikiSyncSummaryState | null>(null);
  const syncPollTimerRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const isMountedRef = useRef(true);
  const [repoDetailsOpen, setRepoDetailsOpen] = useState(false);

  const stopSyncPolling = useCallback(() => {
    if (syncPollTimerRef.current) {
      clearInterval(syncPollTimerRef.current);
      syncPollTimerRef.current = null;
    }
  }, []);

  const fetchSyncSummary = useCallback(async (options?: { silent?: boolean }) => {
    const silent = options?.silent ?? false;
    if (!wiki_id) {
      return;
    }

    if (!silent && isMountedRef.current) {
      setIsSyncSummaryLoading(true);
    }

    try {
      const response = await authFetch(`/api/wiki/${wiki_id}/index-progress`);
      if (!response) {
        return;
      }
      if (!response.ok) {
        throw new Error(`Failed to fetch sync summary: ${response.status}`);
      }
      const data = await response.json();
      const repoSync = data?.repo_sync;
      const summary: WikiSyncSummaryState = repoSync
        ? {
            pending_files: repoSync.pending_files ?? 0,
            total_files: repoSync.total_files ?? 0,
            indexed_files:
              repoSync.indexed_files ?? Math.max((repoSync.total_files ?? 0) - (repoSync.pending_files ?? 0), 0),
            unexpected_files: repoSync.unexpected_files ?? 0,
            repositories: Array.isArray(repoSync.repositories) ? repoSync.repositories : [],
            status: repoSync.status,
          }
        : {
            pending_files: data?.pending_sync_files ?? 0,
            total_files: data?.total_sync_files ?? 0,
            indexed_files:
              data?.indexed_sync_files ?? Math.max((data?.total_sync_files ?? 0) - (data?.pending_sync_files ?? 0), 0),
            repositories: [],
            status: data?.status,
          };

      summary.status = data?.status ?? summary.status ?? syncSummaryRef.current?.status ?? 'idle';
      summary.job_id = data?.job_id ?? syncSummaryRef.current?.job_id;
      summary.stage_message = data?.stage_message ?? syncSummaryRef.current?.stage_message;

      const normalizedStatus = (summary.status || '').toLowerCase();
      const isActive = ACTIVE_SYNC_STATUSES.has(normalizedStatus);

     if (isMountedRef.current) {
        setSyncSummary(summary);
        syncSummaryRef.current = summary;

        if (isActive) {
          const processed = data?.processed_files ?? Math.max(summary.total_files - summary.pending_files, summary.indexed_files ?? 0);
          const total = data?.total_files ?? summary.total_files ?? processed;
          setSyncProgress({
            progress: data?.progress ?? (total ? Math.round((processed / total) * 100) : 0),
            processed_files: processed,
            total_files: total,
            message: data?.stage_message ?? summary.stage_message ?? '',
          });
        } else {
          setSyncProgress(null);
        }

        if (TERMINAL_SYNC_STATUSES.has(normalizedStatus)) {
          setIsSyncingIndex(false);
        } else if (isActive) {
          setIsSyncingIndex(true);
        } else {
          setIsSyncingIndex(false);
        }

        if (isActive) {
          if (!syncPollTimerRef.current) {
            syncPollTimerRef.current = setInterval(() => {
              void fetchSyncSummary({ silent: true });
            }, 4000);
          }
        } else if (syncPollTimerRef.current) {
          stopSyncPolling();
        }
      }
    } catch (error) {
      if (!silent) {
        console.error('Failed to fetch sync summary:', error);
      }
    } finally {
      if (!silent && isMountedRef.current) {
        setIsSyncSummaryLoading(false);
      }
    }
  }, [wiki_id]);

  const startSyncPolling = useCallback(() => {
    if (syncPollTimerRef.current) {
      return;
    }
    void fetchSyncSummary({ silent: true });
  }, [fetchSyncSummary]);

  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
      stopSyncPolling();
    };
  }, [stopSyncPolling]);

  useEffect(() => {
    if (scrollRef.current) {
      const width = scrollRef.current.offsetWidth - scrollRef.current.clientWidth;
      setScrollbarWidth(width);
    }
  }, [renderTime]);

  const canSyncIndex = useMemo(() => {
    return hasSuperAdminPermission() || hasGrantPermission(typeof role_code === 'string' ? role_code : '');
  }, [hasGrantPermission, hasSuperAdminPermission, role_code]);

  useEffect(() => {
    if (!wiki_id) {
      setSyncSummary(null);
      setSyncProgress(null);
      setIsSyncingIndex(false);
      return;
    }
    setIsSyncSummaryLoading(true);
    setSyncProgress(null);
    const timer = window.setTimeout(() => {
      if (!isMountedRef.current) {
        return;
      }
      void fetchSyncSummary({ silent: false });
    }, 120);

    return () => {
      window.clearTimeout(timer);
    };
  }, [wiki_id, fetchSyncSummary]);

  useEffect(() => {
    if (isSyncingIndex) {
      startSyncPolling();
    } else {
      stopSyncPolling();
    }
  }, [isSyncingIndex, startSyncPolling, stopSyncPolling]);

  const handleSyncIndex = useCallback(async () => {
    if (!wiki_id) {
      return;
    }
    if (!canSyncIndex) {
      return;
    }

    setIsSyncingIndex(true);
    setSyncProgress({
      progress: 0,
      processed_files: 0,
      total_files: 0,
      message: messages.components.projectDetail.syncProgressStatusLoading,
    });
    stopSyncPolling();

    try {
      const response = await authFetch(`/api/wiki/${wiki_id}/index-progress`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ strategy: 'main_topic' }),
      });

      if (!response) {
        throw new Error(messages.components.projectDetail.requestFailed);
      }
      if (!response.ok) {
        const text = await response.text();
        throw new Error(text || `HTTP ${response.status}`);
      }

      const result = await response.json();
      setSyncSummary((prev) => {
        const next: WikiSyncSummaryState = {
          pending_files: prev?.pending_files ?? 0,
          total_files: prev?.total_files ?? 0,
          indexed_files: prev?.indexed_files ?? 0,
          unexpected_files: prev?.unexpected_files,
          repositories: prev?.repositories ?? [],
          status: 'pending',
          job_id: result?.job_id ?? prev?.job_id,
          stage_message: result?.message || messages.components.projectDetail.syncProgressStatusSyncing,
        };
        syncSummaryRef.current = next;
        return next;
      });

      startSyncPolling();
      void fetchSyncSummary({ silent: true });
    } catch (error) {
      console.error(messages.components.projectDetail.syncIndexFailed, error);
      addToast({
        type: 'error',
        title: messages.components.projectDetail.syncProgressStatusFailed,
        message: error instanceof Error ? error.message : messages.common?.unknownError || 'Unknown error',
      });
      setIsSyncingIndex(false);
      setSyncProgress(null);
    }
  }, [wiki_id, canSyncIndex, addToast, messages, stopSyncPolling, startSyncPolling, fetchSyncSummary]);


  // Role options
  const roleOptions = [
    { label: messages.components.projectDetail.wikiAdmin, value: 'wiki_grant' },
    { label: messages.components.projectDetail.wikiUser, value: 'wiki_access' }
  ];

  // Copy repository address
  const handleCopy = async (text?: string) => {
    if (!text) return;

    const showToast = (type: 'success' | 'error', message: string) => {
      addToast({
        type,
        title: type === 'success' ? messages.components.projectDetail.copySuccess : messages.components.projectDetail.copyFailed,
        message
      });
    };

    try {
      // Prefer Clipboard API
      if (window.isSecureContext && navigator.clipboard) {
        await navigator.clipboard.writeText(text);
        showToast('success', messages.components.projectDetail.copySuccessMessage);
        return;
      }

      // Fallback solution
      const textarea = document.createElement('textarea');
      textarea.value = text;
      textarea.style.position = 'fixed';
      textarea.style.opacity = '0';
      document.body.appendChild(textarea);
      textarea.select();

      try {
        const successful = document.execCommand('copy');
        if (!successful) throw new Error('execCommand failed');
        showToast('success', messages.components.projectDetail.copySuccessMessage);
      } finally {
        document.body.removeChild(textarea);
      }
    } catch (error) {
      console.error(messages.components.projectDetail.copyFailed, error);
      showToast('error', `${messages.components.projectDetail.copyFailed}: ${error instanceof Error ? error.message : messages.components.projectDetail.copyFailedMessage}`);
    }
  };

  // Load wiki information
  const loadWikiInfo = async () => {
    try {
      const response = await authFetch(`/api/wiki/info/${wiki_id}/nocheck`);
      if (response && response.ok) {
        const result = await response.json();
        if (result.code !== 200) {
          throw new Error(result.message);
        }
        setWikiInfo(result.data);
        setTimeout(() => setRenderTime(new Date()), 100);
        setLoading(false);
        void loadUsers(result.data);
      } else {
        addToast({
          type: 'error',
          title: messages.components.projectDetail.loadFailed,
          message: messages.components.projectDetail.loadFailedMessage
        });
        setLoading(false);
      }
    } catch (error) {
      console.error(messages.components.projectDetail.loadWikiInfoFailed, error);
      addToast({
        type: 'error',
        title: messages.components.projectDetail.loadFailed,
        message: messages.components.projectDetail.loadFailedMessage
      });
      setLoading(false);
    }
  };

  // Load user list
  const loadUsers = async (wikiInfo: WikiInfo) => {
    try {
      const response = await authFetch(`/api/wiki/grant/users/${wikiInfo?.id}`);
      if (response && response.ok) {
        const data = await response.json();
        setUsers(data);
      } else {
        addToast({
          type: 'error',
          title: messages.components.projectDetail.loadFailed,
          message: messages.components.projectDetail.loadUsersFailed
        });
      }
    } catch (error) {
      console.error(messages.components.projectDetail.loadUsersFailed, error);
      addToast({
        type: 'error',
        title: messages.components.projectDetail.loadFailed,
        message: messages.components.projectDetail.loadUsersFailed
      });
    }
  };

  // Delete user
  const handleDeleteUser = async (user: UserInfo) => {
    try {
      const response = await authFetch('/api/wiki/grant', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          wiki_id: wikiInfo?.id,
          user_id: user.user_id,
          role_id: user.role_code === 'wiki_access' ? 6: 5
        })
      });

      if (response && response.ok) {
        addToast({
          type: 'success',
          title: messages.components.projectDetail.deleteSuccess,
          message: messages.components.projectDetail.deleteSuccessMessage.replace('{user}', user.user_name)
        });
        if (wikiInfo) {
          loadUsers(wikiInfo); // Reload user list
        }
      } else {
        addToast({
          type: 'error',
          title: messages.components.projectDetail.deleteFailed,
          message: messages.components.projectDetail.deleteFailedMessage
        });
      }
    } catch (error) {
      console.error(messages.components.projectDetail.deleteUserFailed, error);
      addToast({
        type: 'error',
        title: messages.components.projectDetail.deleteFailed,
        message: messages.components.projectDetail.deleteFailedMessage
      });
    }
  };

  // Start editing user role
  const handleStartEdit = (user: UserInfo) => {
    setEditingUserId(user.user_id);
    setEditingRoleCode(user.role_code);
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setEditingUserId(null);
    setEditingRoleCode('');
  };

  // Confirm user role modification
  const handleConfirmEdit = async (user: UserInfo) => {
    try {

      const response = await authFetch('/api/wiki/grant', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          wiki_id: wikiInfo?.id,
          users: [editingUserId],
          role_id: editingRoleCode === 'wiki_access' ? 6: 5,
        }),
      });

      if (response && response.ok) {
        addToast({
          type: 'success',
          title: messages.components.projectDetail.modifySuccess,
          message: messages.components.projectDetail.modifySuccessMessage.replace('{user}', user.user_name)
        });
        setEditingUserId(null);
        setEditingRoleCode('');
        if (wikiInfo) {
          loadUsers(wikiInfo); // Reload user list
        }
      } else {
        addToast({
          type: 'error',
          title: messages.components.projectDetail.modifyFailed,
          message: messages.components.projectDetail.modifyFailedMessage
        });
      }
    } catch (error) {
      console.error(messages.components.projectDetail.modifyUserRoleFailed, error);
      addToast({
        type: 'error',
        title: messages.components.projectDetail.modifyFailed,
        message: messages.components.projectDetail.modifyFailedMessage
      });
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh');
  };

  // Get role name
  const getRoleName = (roleCode: string) => {
    return roleCode=== 'wiki_grant' ? messages.components.projectDetail.wikiAdmin : messages.components.projectDetail.wikiUser;
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await loadWikiInfo();
    };

    loadData();
  }, [wiki_id]);

  const solutions = useMemo(() => {
    return (wikiInfo?.dev_cloud?.releases ?? []).map(item => item.solution_name).join(", ");
  }, [wikiInfo]);

  const repoStatusDetails = useMemo(() => {
    return (syncSummary?.repositories || []).map((repo) => {
      const namespaceLabel = repo.namespace || `${repo.repo_url ?? ''}${repo.branch ? `#${repo.branch}` : ''}`;
      const jobStatus = (repo.job_status || '').toLowerCase();
      const commitStatus = (repo.commit_status || 'unknown').toLowerCase();
      const headCommit = repo.head_commit ? repo.head_commit.slice(0, 8) : undefined;
      const jobPendingRaw = normalizeCount(repo.job_pending_files);
      const cachedPending = normalizeCount(repo.pending_files);
      const hasActiveJob = jobStatus && ACTIVE_SYNC_STATUSES.has(jobStatus);

      let pendingCount = cachedPending;
      if (hasActiveJob) {
        pendingCount = jobPendingRaw;
      }
      if (!Number.isFinite(pendingCount) || pendingCount < 0) {
        pendingCount = 0;
      }
      const jobTotalRaw = normalizeCount(repo.job_total_files);
      const cachedTotal = Math.max(
        normalizeCount(repo.total_files),
        normalizeCount(repo.expected_files),
        normalizeCount(repo.docchain_total_files)
      );
      let totalCount = cachedTotal;
      if (!totalCount) {
        totalCount = jobTotalRaw;
      } else if (jobTotalRaw && jobTotalRaw > totalCount) {
        totalCount = jobTotalRaw;
      }
      const indexedCandidate = Math.max(
        normalizeCount(repo.indexed_files),
        totalCount > 0 ? totalCount - pendingCount : 0,
        0
      );
      if (!totalCount && (indexedCandidate || pendingCount)) {
        totalCount = indexedCandidate + pendingCount;
      }
      if (totalCount && pendingCount > totalCount) {
        totalCount = pendingCount;
      }
      const indexedCount = totalCount ? Math.min(indexedCandidate, totalCount) : indexedCandidate;
      const jobTotal = jobTotalRaw || totalCount;
      const jobCompleted = jobTotal ? Math.max(jobTotal - pendingCount, 0) : indexedCount;

      let statusText = '';
      let statusClass = 'text-[var(--muted)]';
      if (hasActiveJob) {
        switch (jobStatus) {
          case 'queued':
          case 'pending':
            if (jobTotal > 0) {
              statusText = pendingCount > 0
                ? `${messages.components.projectDetail.syncTaskQueued}，将处理 ${jobTotal} ${messages.components.projectDetail.filesRemaining}（待处理 ${pendingCount}）`
                : `${messages.components.projectDetail.syncTaskQueued}，将处理 ${jobTotal} ${messages.components.projectDetail.filesRemaining}`;
            } else {
              statusText = pendingCount > 0
                ? `${messages.components.projectDetail.syncTaskQueued}，待处理 ${pendingCount} ${messages.components.projectDetail.filesRemaining}`
                : messages.components.projectDetail.syncTaskQueued;
            }
            statusClass = 'text-sky-500';
            break;
          case 'running':
          case 'processing':
            if (jobTotal > 0) {
              statusText = pendingCount > 0
                ? `${messages.components.projectDetail.syncTaskInProgress}，进度 ${jobCompleted}/${jobTotal}，剩余 ${pendingCount} ${messages.components.projectDetail.filesRemaining}`
                : `${messages.components.projectDetail.syncTaskInProgress}，进度 ${jobCompleted}/${jobTotal}`;
            } else {
              statusText = pendingCount > 0
                ? `${messages.components.projectDetail.syncTaskInProgress}，剩余 ${pendingCount} ${messages.components.projectDetail.filesRemaining}`
                : messages.components.projectDetail.syncTaskInProgress;
            }
            statusClass = 'text-blue-500';
            break;
          default:
            statusText = messages.components.projectDetail.syncTaskExecuting;
            statusClass = 'text-blue-500';
        }
      } else if (!totalCount) {
        statusText = messages.components.projectDetail.noDocChainData;
      } else if (pendingCount > 0) {
        statusText = `${messages.components.projectDetail.syncCompleted} ${indexedCount}/${totalCount}，剩余 ${pendingCount} ${messages.components.projectDetail.filesRemaining}`;
        statusClass = 'text-amber-500';
      } else {
        statusText = `${messages.components.projectDetail.syncCompleted} ${indexedCount}/${totalCount}`;
        statusClass = 'text-emerald-500';
      }

      let commitNote: string | undefined;
      if (!hasActiveJob) {
        switch (commitStatus) {
          case 'diverged':
            commitNote = messages.components.projectDetail.localHeadInconsistent;
            break;
          case 'head-only':
            commitNote = messages.components.projectDetail.localHeadNotSynced;
            break;
          case 'historic':
            commitNote = messages.components.projectDetail.historicDataExists;
            break;
          default:
            break;
        }
      }

      const jobMessage = repo.job_message;
      const suppressedJobMessages = new Set([
        'No files to convert',
        messages.components.projectDetail.noConvertibleFiles,
      ]);
      if (!commitNote && jobMessage && jobMessage !== statusText && !suppressedJobMessages.has(jobMessage)) {
        commitNote = jobMessage;
      }

      return {
        key: `${repo.repo_url ?? ''}-${repo.branch ?? ''}`,
        label: namespaceLabel || messages.components.projectDetail.unknownRepo,
        commitStatus,
        statusText,
        statusClass,
        headCommit,
        commitNote,
        pendingCount,
        totalCount,
        indexedCount,
        statusSummary: `${indexedCount}/${totalCount}`,
      };
    });
  }, [syncSummary?.repositories]);

  // 统一的进度计算逻辑
  const aggregatedPending = syncSummary?.pending_files ?? 0;
  const totalSyncFiles = syncSummary?.total_files ?? 0;
  const indexedSyncFilesRaw = syncSummary?.indexed_files ?? Math.max(totalSyncFiles - aggregatedPending, 0);
  
  // 确保总数不为0，如果为0则使用已索引文件数作为总数
  const adjustedTotal = totalSyncFiles > 0 ? totalSyncFiles : Math.max(indexedSyncFilesRaw, 0);
  
  // 确保已完成文件数不超过总数
  const completedSyncFiles = Math.max(Math.min(indexedSyncFilesRaw, adjustedTotal), 0);
  
  // 计算完成百分比，确保一致性
  const completionPercent = adjustedTotal > 0
    ? Math.round((completedSyncFiles / adjustedTotal) * 100)
    : (indexedSyncFilesRaw > 0 ? 100 : 0);
  
  // 统一的进度显示数据
  const progressDisplay = {
    completed: completedSyncFiles,
    total: adjustedTotal,
    percentage: completionPercent,
    pending: aggregatedPending
  };

  // 进度一致性检查函数
  const validateProgressConsistency = () => {
    const expectedPercentage = progressDisplay.total > 0 
      ? Math.round((progressDisplay.completed / progressDisplay.total) * 100)
      : (progressDisplay.completed > 0 ? 100 : 0);
    
    if (progressDisplay.percentage !== expectedPercentage) {
      console.warn('进度显示不一致:', {
        displayed: progressDisplay.percentage,
        expected: expectedPercentage,
        completed: progressDisplay.completed,
        total: progressDisplay.total
      });
    }
    
    return expectedPercentage;
  };

  // 确保进度一致性
  const consistentPercentage = validateProgressConsistency();
  const normalizedSyncStatus = (syncSummary?.status || '').toLowerCase();
  const isSyncJobActive = isSyncingIndex || ACTIVE_SYNC_STATUSES.has(normalizedSyncStatus);
  const isSyncCalculating = (!syncSummary && isSyncSummaryLoading) || normalizedSyncStatus === 'refreshing';
  const progressBarWidth = isSyncCalculating ? '100%' : `${Math.min(Math.max(consistentPercentage, 0), 100)}%`;
  const progressBarExtraClasses = isSyncCalculating ? 'animate-pulse opacity-60' : '';
  const statusIndicatorClass = isSyncJobActive
    ? 'bg-[var(--accent-primary)] animate-pulse'
    : normalizedSyncStatus === 'failed'
      ? 'bg-red-500'
      : 'bg-[var(--muted)]';
  const activeStageMessage = isSyncJobActive ? (syncSummary?.stage_message || syncProgress?.message || '') : '';
  const displayStageMessage = activeStageMessage === 'refreshing'
    ? messages.components.projectDetail.syncProgressCalculating
    : activeStageMessage;
  const syncStatusText = (() => {
    if (isSyncSummaryLoading && !syncSummary) {
      return messages.components.projectDetail.syncProgressStatusLoading;
    }
    if (isSyncCalculating) {
      return messages.components.projectDetail.syncProgressCalculating;
    }
    if (normalizedSyncStatus === 'refreshing') {
      return messages.components.projectDetail.syncProgressCalculating;
    }
    if (isSyncJobActive) {
      return activeStageMessage || messages.components.projectDetail.syncProgressStatusSyncing;
    }
    if (normalizedSyncStatus === 'failed') {
      return messages.components.projectDetail.syncProgressStatusFailed;
    }
    if (normalizedSyncStatus === 'error') {
      return messages.components.projectDetail.syncProgressStatusError;
    }
    return messages.components.projectDetail.syncProgressStatusReady;
  })();

  const syncActionLabel = isSyncJobActive
    ? messages.components.projectDetail.syncProgressActionRunning
    : messages.components.projectDetail.syncProgressAction;

  const syncActionDisabled = !canSyncIndex || isSyncJobActive || isSyncSummaryLoading;

  const syncActionTip = isSyncJobActive
    ? messages.components.projectDetail.syncProgressActionRunningTip
    : messages.components.projectDetail.syncProgressActionTip;

  const CodeIndexDetailModal = () => (
    <div className="fixed inset-0 bg-black/60 bg-opacity-50 flex items-center justify-center z-50 p-4 backdrop-blur-sm">
      <div className="bg-[var(--card-bg)] rounded-lg shadow-2xl border border-[var(--border-color)] w-full max-w-3xl max-h-[80vh] flex flex-col">
        <div className="p-4 border-b border-[var(--border-color)] flex-shrink-0">
          <div className="flex items-center justify-between gap-3">
            <div className="flex items-center gap-3 min-w-0">
              <h3 className="text-lg font-semibold text-[var(--foreground)] whitespace-nowrap">
                {messages.components.projectDetail.codeIndexTitle}
              </h3>
              <span className="text-xs text-[var(--muted)] truncate">
                {messages.components.projectDetail.codeIndexTip}
              </span>
            </div>
            <div className="flex items-center gap-3">
              <span className="font-mono text-sm text-[var(--foreground)]">
                {progressDisplay.total > 0 ? `${progressDisplay.completed}/${progressDisplay.total}` : `${progressDisplay.completed}/0`}
              </span>
              <button
                onClick={handleSyncIndex}
                disabled={syncActionDisabled}
                className="p-2 border border-[var(--border-color)] rounded-full bg-[var(--background)] text-[var(--foreground)] hover:bg-[var(--background)]/80 disabled:opacity-60 disabled:cursor-not-allowed transition-colors"
                title={syncActionTip}
                aria-label={syncActionLabel}
              >
                <FaSync className={`${isSyncJobActive ? 'animate-spin' : ''}`} size={12} />
              </button>
              <button
                onClick={() => setShowCodeIndexDetails(false)}
                className="text-[var(--muted)] hover:text-[var(--foreground)] transition-colors"
              >
                <FaTimes size={16} />
              </button>
            </div>
          </div>
        </div>

        <div className="p-4 overflow-y-auto">
          <div className="border border-[var(--border-color)] rounded-lg bg-[var(--card-bg)] p-5 space-y-4">
            <div>
              <div className="w-full bg-[var(--border-color)] rounded-full h-2 overflow-hidden">
                <div
                  className={`bg-[var(--accent-primary)] h-full transition-all duration-300 ${progressBarExtraClasses}`}
                  style={{ width: progressBarWidth }}
                />
              </div>
              <div className="mt-2 flex items-center justify-between text-xs text-[var(--muted)]">
                <div className="flex items-center gap-2">
                  <span className={`inline-flex h-2 w-2 rounded-full ${statusIndicatorClass}`} />
                  <span>{syncStatusText}</span>
                </div>
                <div className="font-mono text-[var(--foreground)]">
                  {progressDisplay.completed}/{progressDisplay.total} ({consistentPercentage}%)
                </div>
              </div>
              {displayStageMessage && displayStageMessage !== syncStatusText && (
                <div className="mt-1 text-xs text-[var(--muted)]">{displayStageMessage}</div>
              )}
            </div>

            <div className="border-t border-[var(--border-color)] pt-3">
              <button
                className="w-full flex items-center justify-between text-sm text-[var(--foreground)]"
                onClick={() => setRepoDetailsOpen((prev) => !prev)}
                type="button"
              >
                <span>{messages.components.projectDetail.codeIndexRepoDetails}</span>
                <FaChevronDown
                  className={`transition-transform text-[var(--muted)] ${repoDetailsOpen ? 'rotate-180' : ''}`}
                  size={12}
                />
              </button>
              {repoDetailsOpen && (
                <div className="mt-3 space-y-3">
                  {repoStatusDetails.length > 0 ? (
                    repoStatusDetails.map((detail) => {
                      const repoProgressPercent =
                        detail.totalCount > 0 ? Math.round((detail.indexedCount / detail.totalCount) * 100) : detail.indexedCount > 0 ? 100 : 0;

                      const progressBarColorClass = detail.statusClass
                        .replace('text-[var(--muted)]', 'bg-[var(--muted)]')
                        .replace('text-', 'bg-');

                      return (
                        <div
                          key={detail.key}
                          className="border border-[var(--border-color)] rounded-md p-3 bg-[var(--background)]/60 space-y-2 text-xs"
                        >
                          <div className="flex items-center justify-between gap-4">
                            <span className="font-medium text-sm text-[var(--foreground)] truncate" title={detail.label}>
                              {detail.label}
                            </span>
                            {detail.headCommit && (
                              <span className="font-mono text-[var(--muted)] flex-shrink-0">HEAD: {detail.headCommit}</span>
                            )}
                          </div>

                          <div className="flex items-center gap-3">
                            <div className="w-full bg-[var(--border-color)] rounded-full h-1.5 overflow-hidden">
                              <div
                                className={`h-1.5 rounded-full transition-all duration-300 ${progressBarColorClass}`}
                                style={{ width: `${repoProgressPercent}%` }}
                              />
                            </div>
                            <span className="font-mono text-[var(--muted)] flex-shrink-0">
                              {detail.indexedCount}/{detail.totalCount}
                            </span>
                          </div>

                          <div className={`pt-1 ${detail.statusClass}`} title={detail.statusText}>
                            {detail.statusText}
                          </div>

                          {detail.commitNote && detail.commitNote !== detail.statusText && (
                            <div
                              className="text-[var(--muted)] pt-1 border-t border-[var(--border-color)] border-dashed"
                              title={detail.commitNote}
                            >
                              {detail.commitNote}
                            </div>
                          )}
                        </div>
                      );
                    })
                  ) : (
                    <div className="text-xs text-[var(--muted)]">{messages.components.projectDetail.noData}</div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
 
   if (loading) {
     return (
       <div className="fixed inset-0 bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-[var(--card-bg)] rounded-lg p-6 w-96">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]"></div>
            <span className="ml-2 text-[var(--muted)]">{messages.common.loading}</span>
          </div>
        </div>
      </div>
    );
  }

  if (!wikiInfo) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-[var(--card-bg)] rounded-lg p-6 w-96">
          <div className="text-center text-[var(--muted)]">
            {messages.components.projectDetail.notFoundWikiInfo}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/60 bg-opacity-50 flex items-center justify-center z-50 p-4 backdrop-blur-sm">
      <div className="bg-[var(--card-bg)] rounded-lg shadow-2xl border border-[var(--border-color)] w-full max-w-5xl max-h-[95vh] overflow-y-auto">
        {/* {messages.components.projectDetail.wikiBasicInfo} */}
        <div className="p-6 border-b border-[var(--border-color)]">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-semibold text-[var(--foreground)]">{messages.components.projectDetail.wikiDetail}</h3>
            {onClose && (
              <button
                onClick={onClose}
                className="text-[var(--muted)] hover:text-[var(--foreground)] transition-colors"
              >
                <FaTimes size={16} />
              </button>
            )}
          </div>
          <div className="grid grid-cols-2 gap-12 text-sm mb-2">
          {/* {messages.components.projectDetail.leftArea} */}
          <div className="space-y-4">
            <div className="flex mb-1.5 items-center h-6">
              <span className={`${language === 'en' ? 'w-30' : 'w-24'} flex-shrink-0 text-left font-semibold text-[var(--project-text-color)] `}>{settings.isProjectMode ? messages.components.projectDetail.projectName : messages.components.projectDetail.repoName}:</span>
              <span className="text-[var(--foreground)] font-medium">{wikiInfo.repo_name}</span>
            </div>
            {
              !settings.isProjectMode && (
                <div className="flex mb-1.5 items-center h-6">
                <span className={`${language === 'en' ? 'w-30' : 'w-24'} flex-shrink-0 text-left font-semibold text-[var(--project-text-color)] `}>{messages.components.projectDetail.repoBranch}:</span>
                <span className="text-[var(--foreground)]">{wikiInfo.branch}</span>
              </div>
              )
            }
            {/*<div className="flex mb-1.5">*/}
            {/*  <span className="text-[var(--muted)] w-24 flex-shrink-0 text-left">仓库类型:</span>*/}
            {/*  <span className="text-[var(--foreground)]">{wikiInfo.repo_type}</span>*/}
            {/*</div>*/}
            <div className="flex mb-1.5 items-center h-6">
              <span className={`${language === 'en' ? 'w-30' : 'w-24'} flex-shrink-0 text-left font-semibold text-[var(--project-text-color)] `}>{messages.components.projectDetail.creator}:</span>
              <span className="text-[var(--foreground)]">
                {wikiInfo.user_name && wikiInfo.user_code 
                  ? `${wikiInfo.user_name}[${wikiInfo.user_code}]`
                  : wikiInfo.user_name || messages.components.projectDetail.unknownUser
                }
              </span>
            </div>
            <div className="flex mb-1.5 items-center h-6">
              <span className={`${language === 'en' ? 'w-30' : 'w-24'} flex-shrink-0 text-left font-semibold text-[var(--project-text-color)] `}>{messages.components.projectDetail.createdTime}:</span>
              <span className="text-[var(--foreground)]">{formatDate(wikiInfo.created_time)}</span>
            </div>
            {!isProjectMode && <div className="flex mb-1.5 items-center h-6">
              <span className={`${language === 'en' ? 'w-30' : 'w-24'} flex-shrink-0 text-left font-semibold text-[var(--project-text-color)] `}>{messages.components.projectDetail.productLine}:</span>
              <span className="text-[var(--foreground)]">{wikiInfo.dev_cloud?.product_line_name}</span>
            </div>}
            <div className="flex mb-1.5 items-center h-6">
              <span className={`${language === 'en' ? 'w-30' : 'w-24'} flex-shrink-0 text-left font-semibold text-[var(--project-text-color)] `}>{messages.components.projectDetail.solution}:</span>
              <span className="text-[var(--foreground)] text-ellipsis whitespace-nowrap overflow-hidden" title={solutions}>{solutions}</span>
              {solutions && <button
                className="ml-1 p-1 rounded hover:bg-[var(--hover-bg)] transition-colors"
                title={messages.components.projectDetail.copySolution}
                onClick={() => handleCopy(solutions)}
                style={{ lineHeight: 0 }}
              >
                <FaRegCopy size={16} className="text-[var(--muted)] hover:text-[var(--primary)]" />
              </button>}
            </div>
            <div className="flex items-center h-6">
              <span className={`${language === 'en' ? 'w-30' : 'w-24'} flex-shrink-0 text-left font-semibold text-[var(--project-text-color)] `}>{messages.components.projectDetail.codeIndexTitle}:</span>
              <div className="flex items-center gap-2 w-full">
                <div className="w-full bg-[var(--border-color)] rounded-full h-2 overflow-hidden" title={syncStatusText}>
                  <div
                    className={`bg-[var(--accent-primary)] h-full transition-all duration-300 ${progressBarExtraClasses}`}
                    style={{ width: progressBarWidth }}
                  />
                </div>
                <span className="text-xs text-[var(--muted)] flex-shrink-0">
                  {consistentPercentage}%
                </span>
                <button
                  onClick={handleSyncIndex}
                  disabled={syncActionDisabled}
                  className="p-1.5 border border-transparent rounded-full hover:bg-[var(--hover-bg)] disabled:opacity-60 disabled:cursor-not-allowed transition-colors"
                  title={syncActionTip}
                >
                  <FaSync className={`${isSyncJobActive ? 'animate-spin' : ''}`} size={12} />
                </button>
                <button
                  onClick={() => setShowCodeIndexDetails(true)}
                  className="p-1.5 border border-transparent rounded-full hover:bg-[var(--hover-bg)] transition-colors"
                  title={messages.components.projectDetail.details || '详情'}
                >
                  <FaChevronDown size={12} />
                </button>
              </div>
            </div>
            {isProjectMode && <div className="flex mb-1.5 items-center h-6">
              <span className={`${language === 'en' ? 'w-30' : 'w-24'} flex-shrink-0 text-left font-semibold text-[var(--project-text-color)] `}>{messages.projects.projectManager}:</span>
              <span className="text-[var(--foreground)]">{wikiInfo.pm_name}</span>
            </div>}
          </div>

          {/* {messages.components.projectDetail.rightArea} */}
          <div className="space-y-4">
            <div className="flex mb-1.5 items-center h-6">
              <span className="w-24 flex-shrink-0 text-left font-semibold text-[var(--project-text-color)] ">{isProjectMode ? messages.components.projectDetail.projectUrl : messages.components.projectDetail.repoUrl}:</span>
              <span className="text-[var(--foreground)] truncate max-w-[200px] flex items-center gap-2" title={wikiInfo.repo_url}>
                <span className="truncate">{wikiInfo.repo_url}</span>
                <button
                  className="ml-1 p-1 rounded hover:bg-[var(--hover-bg)] transition-colors"
                  title={messages.components.projectDetail.copyRepoUrl}
                  onClick={() => handleCopy(wikiInfo.repo_url)}
                  style={{ lineHeight: 0 }}
                >
                  <FaRegCopy size={16} className="text-[var(--muted)] hover:text-[var(--primary)]" />
                </button>
              </span>
            </div>
            <div className="flex mb-1.5 items-center h-6">
              <span className="w-24 flex-shrink-0 text-left font-semibold text-[var(--project-text-color)] ">{isProjectMode ? messages.components.projectDetail.whalecloudProject : messages.components.projectDetail.repoOwner}:</span>
              <span className="text-[var(--foreground)]">{wikiInfo.repo_owner}</span>
            </div>
            {/*<div className="flex mb-1.5">*/}
            {/*  <span className="text-[var(--muted)] w-24 flex-shrink-0 text-left">创建人ID:</span>*/}
            {/*  <span className="text-[var(--foreground)]">{wikiInfo.created_by}</span>*/}
            {/*</div>*/}
            <div className="flex mb-1.5 items-center h-6">
              <span className="w-24 flex-shrink-0 text-left font-semibold text-[var(--project-text-color)] ">{messages.components.projectDetail.language}:</span>
              <span className="text-[var(--foreground)]">{wikiInfo.language}</span>
            </div>
            <div className="flex mb-1.5 items-center h-6">
              <span className="w-24 flex-shrink-0 text-left font-semibold text-[var(--project-text-color)] ">{messages.components.projectDetail.status}:</span>
              <span className={`px-2 py-1 rounded text-xs ${
                wikiInfo.status === 'completed' ? 'bg-green-100 text-green-800' :
                wikiInfo.status === 'pending' ? 'bg-blue-100 text-blue-800' :
                wikiInfo.status === 'failed' ? 'bg-red-100 text-red-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {wikiInfo.status === 'completed' ? messages.components.projectDetail.completed :
                 wikiInfo.status === 'pending' ? messages.components.projectDetail.processing :
                 wikiInfo.status === 'failed' ? messages.components.projectDetail.failed : messages.components.projectDetail.pending}
              </span>
            </div>
            {!isProjectMode && <div className="flex mb-1.5 items-center h-6">
              <span className="w-24 flex-shrink-0 text-left font-semibold text-[var(--project-text-color)] ">{messages.components.projectDetail.product}:</span>
              <span className="text-[var(--foreground)]">{wikiInfo.dev_cloud?.product_name}</span>
            </div>}
            <div className="flex mb-1.5 items-center h-6">
              <span className="w-24 flex-shrink-0 text-left font-semibold text-[var(--project-text-color)] ">{isProjectMode ? messages.components.projectDetail.publishPackage : messages.components.projectDetail.productVersion}:</span>
              <span className="text-[var(--foreground)]">{isProjectMode ? wikiInfo?.dev_cloud?.releases?.[0]?.release_pkg_code : wikiInfo.dev_cloud?.product_version_code}</span>
            </div>
          </div>
        </div>

        {/* Include products */}
        {isProjectMode && <div className='products-container text-start flex flex-col gap-y-2'>
          <div className='font-bold'>
            {messages.components.projectDetail.productList}:
          </div>
          <div>
            <div className='border-1 border-[var(--border-color)]' style={{ paddingRight: `${scrollbarWidth}px` }}>
              <table className="w-full text-sm border-[var(--border-color)] border-r-0 rounded-md" style={{ width: "calc(100% + 1px)" }}>
                <thead className="bg-[var(--card-bg)]">
                  <tr className="border-[var(--border-color)]">
                    <th className="text-left py-3 px-4 text-[var(--muted)] font-medium border-r border-[var(--border-color)] w-[30%]">{messages.components.projectDetail.productName}</th>
                    <th className="text-left py-3 px-4 text-[var(--muted)] font-medium w-[70%]">{messages.components.projectDetail.productRepoUrl}</th>
                  </tr>
                </thead>
              </table>
            </div>
            <div className='max-h-60 overflow-y-auto border-r-1 border-[var(--border-color)] border-b-1' ref={scrollRef}>
              <table className="w-full text-sm border-l-1 border-[var(--border-color)] rounded-md">
                <tbody>
                  {(wikiInfo.products && wikiInfo.products.length > 0) ? wikiInfo.products?.map(product => 
                    <tr key={product.productName} className="border-b border-[var(--border-color)] hover:bg-[var(--hover-bg)] last:border-b-0">
                      <td className="py-3 px-4 border-r border-[var(--border-color)] w-[30%]">
                          <div className="text-left font-medium text-[var(--foreground)]">{product.productName}</div>
                      </td>
                      <td className="py-3 px-4 w-[70%]">
                          <div className="text-left font-medium text-[var(--foreground)]">{product.repo_url}</div>
                      </td>
                    </tr>) : <tr className='py-3 px-4 border-r border-[var(--border-color)]'>
                      <td colSpan={2} className="py-3 px-4 border-r border-[var(--border-color)] text-center">{messages.components.projectDetail.noData}</td>
                    </tr>}
                </tbody>
              </table>
            </div>
          </div>
        </div>}

        {/* Tag area */}
        {(hasSuperAdminPermission() || hasAccessPermission(typeof role_code === "string" ? role_code :'')) && (
        <div className="pb-4">
          {/* <div className="flex items-center gap-2 mb-3">
            <span className="text-sm font-medium text-[var(--muted)]">标签管理:</span>
          </div> */}
          {/* <WikiTagManager
            wiki_id={Number(wikiInfo.id)}
            wiki_tags={wikiInfo.tags}
          /> */}
               {/* Current tag display */}
      {wikiInfo.tags.length > 0 && (
        <div className="mt-3">
          <div className="flex flex-wrap gap-2">
            {wikiInfo.tags.map((tag) => (
              <div key={tag.id} style={{ maxWidth: '160px' }}>
              <span
                className="inline-flex items-center gap-2 px-3 py-0.5 text-sm border"
                style={{
                  backgroundColor: `${tag.color}20`,
                  borderColor: tag.color,
                  color: tag.color
                }}
              >
                <span 
                  className="truncate max-w-[100px]"
                  title={tag.name}
                >
                  {tag.name}
                </span>
              </span>
              </div>
            ))}
          </div>
        </div>
      )}
        </div>
        )}
        </div>

        {/* {messages.components.projectDetail.userList} */}
        <div className="p-6">
          {/* {messages.components.projectDetail.searchBoxAndAddButton} */}
          <div className="flex items-center justify-between mb-4">
            {(hasSuperAdminPermission() || hasGrantPermission(typeof role_code === "string" ? role_code :'')) && <button
              onClick={() => {
                setShowAddUserModal(true);
              }}
              className="flex items-center gap-1 px-3 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors"
            >
              <FaPlus size={12} />
              {messages.components.projectDetail.addUser}
            </button>}
            
            <div className="relative w-64">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[var(--muted)]" size={14} />
              <input
                type="text"
                placeholder={messages.components.projectDetail.searchUser}
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                className="w-full pl-10 pr-3 py-2 text-sm bg-[var(--input-bg)] border border-[var(--border-color)] rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent"
              />
            </div>
          </div>

          {/* {messages.components.projectDetail.userTable} */}
          <div className="max-h-80 overflow-y-auto">
            <table className="w-full text-sm border border-[var(--border-color)] rounded-md">
              <thead className="bg-[var(--card-bg)] sticky top-0">
                <tr className="border-b border-[var(--border-color)]">
                    <th className="text-left py-3 px-4 text-[var(--muted)] font-medium border-r border-[var(--border-color)]">{messages.components.projectDetail.user}</th>
                  <th className="text-left py-3 px-4 text-[var(--muted)] font-medium border-r border-[var(--border-color)]">{messages.components.projectDetail.role}</th>
                  <th className="text-left py-3 px-4 text-[var(--muted)] font-medium border-r border-[var(--border-color)]">{messages.components.projectDetail.grantor}</th>
                  <th className="text-left py-3 px-4 text-[var(--muted)] font-medium border-r border-[var(--border-color)]">{messages.components.projectDetail.grantTime}</th>
                  <th className="text-left py-3 px-4 text-[var(--muted)] font-medium">{messages.components.projectDetail.operation}</th>
                </tr>
              </thead>
              <tbody>
                {users
                  .filter(user => 
                    user.user_name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
                    user.user_code.toLowerCase().includes(searchKeyword.toLowerCase())
                  )
                  .map((user) => (
                  <tr key={user.user_id} className="border-b border-[var(--border-color)] hover:bg-[var(--hover-bg)]">
                    <td className="py-3 px-4 border-r border-[var(--border-color)]">
                      <div>
                        <div className="text-left font-medium text-[var(--foreground)]">{user.user_name}</div>
                        <div className="text-left text-xs text-[var(--muted)]">{user.user_code}</div>
                      </div>
                    </td>
                    <td className="text-left py-3 px-4 border-r border-[var(--border-color)]">
                      {editingUserId === user.user_id ? (
                        <div className="flex items-center gap-2">
                          <select
                            value={editingRoleCode}
                            onChange={(e) => setEditingRoleCode(e.target.value)}
                            className="px-2 py-1 border border-[var(--border-color)] rounded bg-[var(--input-bg)] text-[var(--foreground)]"
                          >
                            {roleOptions.map((role) => (
                              <option key={role.value} value={role.value}>
                                {role.label}
                              </option>
                            ))}
                          </select>
                          <button
                            onClick={() => handleConfirmEdit(user)}
                            className="p-1 text-green-600 hover:text-green-700 transition-colors"
                            title={messages.user.confirm}
                          >
                            <FaCheck size={12} />
                          </button>
                          <button
                            onClick={handleCancelEdit}
                            className="p-1 text-red-600 hover:text-red-700 transition-colors"
                            title={messages.common.cancel}
                          >
                            <FaTimes size={12} />
                          </button>
                        </div>
                      ) : (
                        <span className="px-2 py-1 bg-[var(--primary-light)] text-[var(--primary)] rounded">
                          {getRoleName(user.role_code)}
                        </span>
                      )}
                    </td>
                    <td className="py-3 px-4 border-r border-[var(--border-color)]">
                      <div>
                        <div className="text-left font-medium text-[var(--foreground)]">{user.creator_info?.creator_name || messages.components.projectDetail.unknown}</div>
                        <div className="text-left text-[var(--muted)]">{user.creator_info?.creator_code || ''}</div>
                      </div>
                    </td>
                    <td className="text-left py-3 px-4 border-r border-[var(--border-color)] text-[var(--muted)]">
                      {formatDate(user.created_date)}
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex gap-1">
                        {(hasSuperAdminPermission()
                            || ((wikiInfo.created_by !== user?.user_id) && hasGrantPermission(typeof role_code === "string" ? role_code :'') && userInfo?.user_code === user.creator_info?.creator_code)) &&  (<>
                          <button
                              onClick={() => handleStartEdit(user)}
                              className="p-1 text-[var(--muted)] hover:text-[var(--primary)] transition-colors"
                              title={messages.components.projectDetail.modify}
                              hidden={editingUserId === user.user_id}
                          >
                            <FaEdit size={12}/>
                          </button>
                          <Popconfirm
                            title={messages.components.projectDetail.deleteConfirm}
                            onConfirm={() => handleDeleteUser(user)}
                            onCancel={() => {}}
                          >
                            <button
                              // onClick={() => handleDeleteUser(user)}
                              className="p-1 text-[var(--muted)] hover:text-red-500 transition-colors"
                              title={messages.components.projectDetail.delete}
                              hidden={editingUserId === user.user_id}
                            >
                            <FaTrash size={12}/>
                          </button>
                          </Popconfirm>
                        </>)}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            
            {users.filter(user => 
              user.user_name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
              user.user_code.toLowerCase().includes(searchKeyword.toLowerCase())
            ).length === 0 && (
              <div className="text-center py-8 text-[var(--muted)]">
                {messages.components.projectDetail.noUsers}
              </div>
            )}
          </div>
        </div>
        {/* {messages.components.projectDetail.addUserModal} */}
        {showAddUserModal && <GrantUser wiki_id={Number(wikiInfo.id)} project_name={`${wikiInfo.repo_owner}/${wikiInfo.repo_name}`} filter='T'
                                        onClose={()=> {
                                          setShowAddUserModal(false);
                                          loadUsers(wikiInfo);
                                        }}/>}


      </div>
      {showCodeIndexDetails && <CodeIndexDetailModal />}
    </div>
  );
}
