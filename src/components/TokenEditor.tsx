import { useToast } from "@/contexts/ToastContext";

import React, { useEffect, useState } from "react";
import { FaTimes } from "react-icons/fa";
import CustomRadio from "./CustomRadio";
import { authFetch } from "@/utils/authFetch";
import { useLanguage } from "@/contexts/LanguageContext";
// 已移除绑定用户相关依赖

// 已去除绑定用户功能

interface Token {
  id: number;
  token: string;
  token_type: 1 | 2;
  state: boolean;
  created_date: string;
  expires_at: string;
  effective_at: string;
  // 兼容历史数据的字段（界面不再使用绑定用户）
  users?: unknown[];
}

interface TokenEditProps {
  app_primary_key: number;
  data?: Token;
  onClose: () => void;
  onFinish: () => void;
}

const TokenEditor = (props: TokenEditProps) => {
  const { app_primary_key, data, onClose, onFinish } = props;

  const { addToast } = useToast();
  const { messages } = useLanguage();
  // Token类型：1-用户级，2-系统级（界面不再支持绑定用户）
  const [tokenType, setTokenType] = useState<number>(1);

  const [expires, setExpires] = useState<string>("");
  const [effective, setEffective] = useState<string>("");
  // 已移除selectedUsers等状态

  useEffect(() => {
    if (data) {
      setTokenType(data.token_type);
      setEffective(data.effective_at ?? "");
      setExpires(data.expires_at ?? "");
      // 不再处理绑定用户
    }
  }, [data]);

  // 提交时不再校验/提交绑定用户
  const onSubmit = async () => {
    if (data) {
      updateToken();
    } else {
      addToken();
    }
  };

  const addToken = async () => {
    const response = await authFetch(`/api/apps/${app_primary_key}/tokens`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        app_primary_key: app_primary_key,
        token_type: tokenType,
        effective_at: effective || null,
        expires_at: expires || null,
      }),
    });

    if (response && response.ok) {
      addToast({
        type: "success",
        title: messages.authApp.generateToken,
        message: messages.authApp.generateTokenSuccess,
      });
      onFinish();
    } else {
      addToast({
        type: "error",
        title: messages.authApp.generateToken,
        message: messages.authApp.generateTokenFail,
      });
    }
  };

  const updateToken = async () => {
    if (data) {
      const response = await authFetch(
        `/api/apps/${app_primary_key}/tokens/${data.id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            token_id: data.id,
            app_primary_key: app_primary_key,
            token_type: tokenType,
            effective_at: effective || null,
            expires_at: expires || null,
          }),
        }
      );

      if (response && response.ok) {
        addToast({
          type: "success",
          title: messages.authApp.updateToken,
          message: messages.authApp.updateTokenSuccess,
        });
        onFinish();
      } else {
        addToast({
          type: "error",
          title: messages.authApp.updateToken,
          message: messages.authApp.updateTokenFail,
        });
      }
    }
  };

  // 已移除用户加载与选择的处理函数

  return (
    <div className="fixed inset-0 bg-black/60 bg-opacity-100 flex items-center justify-center z-50 p-4 backdrop-blur-sm text-start">
      <div className="bg-[var(--card-bg)] rounded-lg shadow-2xl border border-[var(--border-color)] w-full max-w-2xl max-h-[95vh] overflow-hidden">
        <div className="p-6 border-b border-[var(--border-color)] flex flex-col gap-y-6">
          {/* modal header */}
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-[var(--foreground)]">
              {messages.authApp.generateToken}
            </h3>
            {onClose && (
              <button
                onClick={onClose}
                className="text-[var(--muted)] hover:text-[var(--foreground)] transition-colors"
              >
                <FaTimes size={16} />
              </button>
            )}
          </div>

          {/* modal body */}
          <div
            className="w-full flex flex-col gap-y-4 overflow-y-auto"
            style={{ scrollbarWidth: "none" }}
          >
            <div>
              <label className="block text-sm font-medium text-black mb-2 dark:text-white">
                {messages.authApp.tokenType}
              </label>
              <div className="flex gap-3">
                <CustomRadio
                  selected={tokenType === 1}
                  onClick={() => {
                    setTokenType(1);
                  }}
                >
                  <div className="flex items-center">
                    <div className="text-left">
                      <div className="font-medium text-sm">
                        {messages.authApp.userLevel}
                      </div>
                      <div className="text-xs opacity-80">
                        {messages.authApp.userLevelComments}
                      </div>
                    </div>
                  </div>
                </CustomRadio>

                <CustomRadio
                  selected={tokenType === 2}
                  onClick={() => {
                    setTokenType(2);
                  }}
                >
                  <div className="flex items-center">
                    <div className="text-left">
                      <div className="font-medium text-sm">
                        {messages.authApp.systemLevel}
                      </div>
                      <div className="text-xs opacity-80">
                        {messages.authApp.systemLevelComments}
                      </div>
                    </div>
                  </div>
                </CustomRadio>
              </div>
            </div>

            {/* 绑定用户功能已移除 */}

            <div>
              <label className="block text-sm font-medium text-black mb-2 dark:text-white">
                {messages.authApp.effectiveTime}
              </label>
              <div>
                <div>
                  <input
                    type="datetime-local"
                    className="w-full px-3 py-2 text-sm rounded-md bg-transparent text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)] border border-[var(--border-color)]"
                    value={effective}
                    onChange={(e) => setEffective(e.target.value)}
                  />
                </div>
              </div>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-black mb-2 dark:text-white">
                {messages.authApp.expireTime}
              </label>
              <div>
                <div>
                  <input
                    type="datetime-local"
                    className="w-full px-3 py-2 text-sm rounded-md bg-transparent text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)] border border-[var(--border-color)]"
                    value={expires}
                    onChange={(e) => setExpires(e.target.value)}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* modal footer */}
          <div className="flex justify-end items-center">
            <div className="flex items-center gap-x-2">
              <button
                className="px-4 py-2 text-sm font-medium rounded-md border border-[var(--border-color)] text-[var(--accent-primary)] hover:bg-[var(--accent-primary)]/10 transition-colors cursor-pointer"
                onClick={onClose}
              >
                {messages.authApp.cancel}
              </button>
              <button
                onClick={onSubmit}
                className="px-4 py-2 text-sm font-medium rounded-md border border-transparent bg-[var(--accent-primary)]/90 text-white hover:bg-[var(--accent-primary)] transition-colors disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
              >
                {messages.authApp.submit}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TokenEditor;
