import { useToast } from "@/contexts/ToastContext";
import {
  RepoMetadata,
  defaultRepoMetadata,
  Solution,
  RepoMetadataResponse,
} from "@/types/RepoMetadata";
import { Tag } from "@/types/tag";
import { authFetch } from "@/utils/authFetch";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { FaQuestionCircle, FaTimes } from "react-icons/fa";
import { SingleValue, MultiValue } from "react-select";
import AsyncSelect from "react-select/async";
import WikiTagManager from "./WikiTagManager";
import { useTheme } from "next-themes";
import { useLanguage } from "@/contexts/LanguageContext";
import { useSettings } from "@/contexts/SettingsContext";

interface WikiInfo {
  id: number;
  wiki_id: string;
  repo_url: string;
  branch: string;
  repo_owner: string;
  repo_name: string;
  repo_type: string;
  provider: string;
  model: string;
  language: string;
  comprehensive: boolean;
  created_time: string;
  updated_time: string;
  created_by: number;
  status: string;
  user_name?: string;
  user_code?: string;
  comments: string | null;
  tags: Tag[];
  dev_cloud: DevCloud | null;
  visibility: number;
  topic_id?: string;
}

interface DevCloud {
  product_id: number | null;
  product_line_id: number | null;
  product_line_name: string | null;
  product_name: string | null;
  product_version_code: string | null;
  product_version_id: number | null;
  releases: Release[] | null;
}

interface Release {
  release_pkg_code: string | null;
  release_pkg_id: number | null;
  solution_id: number | null;
  solution_name: string | null;
}

interface SelectOption {
  label: string | null;
  value: string | number | null;
}

interface ProductLineOption {
  productLineId: number;
  productLineName: string;
}

interface ProductOption {
  productId: number;
  productName: string;
  classType: string;
}

interface ProductVersionOption {
  productVersionId: number;
  productVersionCode: string;
  classType: string;
  productDto: null;
}

interface SolutionOption {
  productId: number;
  productName: string;
  classType: string;
}

interface DocChainTopic {
  id: number;
  name: string;
  state: string;
}

interface WikiInfoEditorProps {
  wiki_id: string;
  onClose: () => void;
  onFinish: () => void;
}

const WikiInfoEditor = (props: WikiInfoEditorProps) => {
  const { wiki_id, onClose, onFinish } = props;

  const debounceTimeout = useRef<ReturnType<typeof setTimeout> | null>(null);
  const latestInputValue = useRef<string>("");

  const { theme } = useTheme();
  const { addToast } = useToast();

  const { settings } = useSettings();

  const [wikiInfo, setWikiInfo] = useState<WikiInfo | null>(null);
  const [repoMetadata, setRepoMetadata] = useState<RepoMetadata | null>(null);

  const [comments, setComments] = useState<string>("");

  const [selectedProductLine, setSelectedProductLine] =
    useState<SelectOption | null>();
  const [selectedProduct, setSelectedProduct] = useState<SelectOption | null>();
  const [selectedProductVersion, setSelectedProductVersion] =
    useState<SelectOption | null>();
  const [selectedSolutions, setSelectedSolutions] = useState<SelectOption[]>(
    []
  );
  
  // DocChain Topic 相关状态
  const [selectedDocChainTopic, setSelectedDocChainTopic] = useState<SelectOption | null>(null);
  const [availableDocTopics, setAvailableDocTopics] = useState<DocChainTopic[]>([]);
  const [isLoadingTopics, setIsLoadingTopics] = useState(false);
  
  const { messages } = useLanguage();

  useEffect(() => {
    const loadWikiInfo = async () => {
      try {
        const response = await authFetch(`/api/wiki/info/${wiki_id}`);
        if (response && response.ok) {
          const result = await response.json();
          if (result.code !== 200) {
            throw new Error(result.message);
          }
          setWikiInfo(result.data);
        }
      } catch (error) {
        console.error("加载Wiki信息失败:", error);
      }
    };

    loadWikiInfo();
  }, [wiki_id]);

  useEffect(() => {
    if (wikiInfo) {
      setComments(wikiInfo.comments ?? "");
      // 处理 topic_id 字段
      if (wikiInfo.topic_id) {
        // 从已加载的 topics 中找到对应的 topic
        const topic = availableDocTopics.find(t => t.id.toString() === wikiInfo.topic_id);
        if (topic) {
          setSelectedDocChainTopic({
            value: topic.id.toString(),
            label: topic.name,
          });
        } else {
          // 如果 topics 还没加载完成，先设置一个临时值
          setSelectedDocChainTopic({
            value: wikiInfo.topic_id,
            label: `Topic ${wikiInfo.topic_id}`,
          });
        }
      } else {
        setSelectedDocChainTopic(null);
      }
      if (wikiInfo.dev_cloud) {
        const data = wikiInfo.dev_cloud;
        const repo_metadata: RepoMetadata = defaultRepoMetadata();
        if (data.product_line_id && data.product_line_name) {
          repo_metadata.product_line_id = data.product_line_id ?? null;
          repo_metadata.product_line_name = data.product_line_name ?? null;
        }
        if (data.product_version_id && data.product_version_code) {
          repo_metadata.product_version_id = data.product_version_id ?? null;
          repo_metadata.product_version_code =
            data.product_version_code ?? null;
        }
        if (data.product_id && data.product_name) {
          repo_metadata.product_id = data.product_id ?? null;
          repo_metadata.product_name = data.product_name ?? null;
        }
        if (data.releases) {
          for (const release of data.releases) {
            const solution: Solution = {
              release_pkg_id: release.release_pkg_id,
              release_pkg_code: release.release_pkg_code,
              solution_name: release.solution_name ?? null,
              solution_id: release.solution_id ?? null,
            };
            repo_metadata.solutions.push(solution);
          }
        }
        setRepoMetadata(repo_metadata);
      }
    }
  }, [wikiInfo, availableDocTopics]);

  useEffect(() => {
    if (repoMetadata) {
      if (repoMetadata.product_line_id) {
        setSelectedProductLine({
          value: repoMetadata.product_line_id,
          label: repoMetadata.product_line_name,
        });
      } else {
        setSelectedProductLine(null);
      }
      if (repoMetadata.product_id) {
        setSelectedProduct({
          value: repoMetadata.product_id,
          label: repoMetadata.product_name,
        });
      } else {
        setSelectedProduct(null);
      }
      if (repoMetadata.product_version_id) {
        setSelectedProductVersion({
          value: repoMetadata.product_version_id,
          label: repoMetadata.product_version_code,
        });
      } else {
        setSelectedProductVersion(null);
      }
      if (repoMetadata.solutions) {
        setSelectedSolutions(
          repoMetadata.solutions.map((solution) => {
            return {
              value: solution.solution_id,
              label: solution.solution_name,
            };
          })
        );
      } else {
        setSelectedSolutions([]);
      }
    }
  }, [repoMetadata, availableDocTopics]);

  // 加载 DocChain Topics
  useEffect(() => {
    const loadTopics = async () => {
      setIsLoadingTopics(true);
      try {
        const response = await authFetch("/api/docchain/topics?type=lab");
        if (response) {
          if (response.ok) {
            const data = await response.json();
            if (data.success) {
              setAvailableDocTopics(data.data || []);
            }
          } else {
            const data = await response.json();
            console.error(
              `Failed to load DocChain topics: ${data.error}, status: ${response.status}`
            );
          }
        }
      } catch (error) {
        console.error("Failed to load DocChain topics:", error);
      }
      setIsLoadingTopics(false);
    };

    loadTopics();
  }, []);

  // 当 topics 加载完成后，更新已选择的 topic 的标签
  useEffect(() => {
    if (wikiInfo?.topic_id && availableDocTopics.length > 0 && selectedDocChainTopic?.value === wikiInfo.topic_id) {
      const topic = availableDocTopics.find(t => t.id.toString() === wikiInfo.topic_id);
      if (topic && selectedDocChainTopic.label === `Topic ${wikiInfo.topic_id}`) {
        setSelectedDocChainTopic({
          value: topic.id.toString(),
          label: topic.name,
        });
      }
    }
  }, [availableDocTopics, wikiInfo?.topic_id, selectedDocChainTopic]);

  // 当 topics 加载完成后，触发 AsyncSelect 的默认选项加载
  const [defaultOptions, setDefaultOptions] = useState<SelectOption[]>([]);
  
  useEffect(() => {
    if (availableDocTopics.length > 0) {
      const options = availableDocTopics.slice(0, 100).map((topic) => ({
        label: topic.name,
        value: topic.id.toString(),
      }));
      setDefaultOptions(options);
    }
  }, [availableDocTopics]);

  const syncRepoMetadata = () => {
    if (wikiInfo && wikiInfo.repo_url) {
      const params = new URLSearchParams({ repo_url: wikiInfo.repo_url });
      authFetch(`/api/zcm/repo/metadata?${params.toString()}`)
        .then((response) => response?.json())
        .then((data: RepoMetadataResponse) => {
          if (data) {
            const repo_metadata: RepoMetadata = defaultRepoMetadata();
            repo_metadata.dc_repo_id = data.repositoryId;
            repo_metadata.dc_project_id = data.projectId;
            if (
              !data.apiBranchVersionDto &&
              !data.apiDistributionDtoList &&
              !data.apiProductVersionDto &&
              !data.productLineDto
            ) {
              addToast({
                type: "warning",
                title: messages.components.wikiInfoEditor.repositoryMetadata,
                message: messages.components.wikiInfoEditor.repositoryMetadataEmpty,
              });
              return;
            }
            if (data.apiBranchVersionDto) {
              repo_metadata.branch_version_id =
                data.apiBranchVersionDto?.branchVersionId ?? null;
              repo_metadata.branch_version_name =
                data.apiBranchVersionDto?.branchName ?? null;
            }
            if (data.productLineDto) {
              repo_metadata.product_line_id =
                data.productLineDto?.productLineId ?? null;
              repo_metadata.product_line_name =
                data.productLineDto?.productLineName ?? null;
            }
            if (
              data.apiProductVersionDto &&
              data.apiProductVersionDto.classType === "V"
            ) {
              repo_metadata.product_version_id =
                data.apiProductVersionDto.productVersionId;
              repo_metadata.product_version_code =
                data.apiProductVersionDto.productVersionCode;
              if (
                data.apiProductVersionDto.productDto &&
                data.apiProductVersionDto.productDto.classType === "P"
              ) {
                repo_metadata.product_id =
                  data.apiProductVersionDto.productDto.productId;
                repo_metadata.product_name =
                  data.apiProductVersionDto.productDto.productName;
              }
            }
            if (
              data.apiProductVersionDto &&
              data.apiProductVersionDto.classType === "D"
            ) {
              const solution: Solution = {
                release_pkg_id: data.apiProductVersionDto.productVersionId,
                release_pkg_code: data.apiProductVersionDto.productVersionCode,
                solution_name: null,
                solution_id: null,
              };
              if (
                data.apiProductVersionDto.productDto &&
                data.apiProductVersionDto.productDto.classType === "D"
              ) {
                solution.solution_name =
                  data.apiProductVersionDto.productDto.productName;
                solution.solution_id =
                  data.apiProductVersionDto.productDto.productId;
              }
              repo_metadata.solutions.push(solution);
            }
            if (data.apiDistributionDtoList) {
              for (const apiDistributionDto of data.apiDistributionDtoList) {
                if (apiDistributionDto.classType === "D") {
                  const solution_id =
                    apiDistributionDto.productDto?.productId ?? null;
                  if (
                    solution_id &&
                    !repo_metadata.solutions.some(
                      (item) => item.solution_id === solution_id
                    )
                  ) {
                    const solution: Solution = {
                      release_pkg_id: apiDistributionDto.productVersionId,
                      release_pkg_code: apiDistributionDto.productVersionCode,
                      solution_name:
                        apiDistributionDto.productDto?.productName ?? null,
                      solution_id: solution_id,
                    };
                    repo_metadata.solutions.push(solution);
                  }
                }
              }
            }
            setRepoMetadata(repo_metadata);
          } else {
            addToast({
              type: "warning",
              title: messages.components.wikiInfoEditor.repositoryMetadata,
              message: messages.components.wikiInfoEditor.repositoryMetadataEmpty,
            });
          }
        })
        .catch((error) => {
          console.error(error);
          addToast({
            type: "error",
            title: messages.components.wikiInfoEditor.repositoryMetadata,
            message: messages.components.wikiInfoEditor.repositoryMetadataException,
          });
        });
    }
  };

  const onSubmit = async () => {
    const data = {
      repo_metadata: repoMetadata,
      comments: comments,
      topic_id: selectedDocChainTopic?.value || "",
    };
    const response = await authFetch(`/api/wiki/info/${wiki_id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    if (response && response.ok) {
      addToast({
        type: "success",
        title: messages.components.wikiInfoEditor.updateWikiInfo,
        message: messages.components.wikiInfoEditor.updateWikiInfoSuccess,
      });
      onFinish();
    }
  };

  const loadProductLines = useCallback(
    (inputValue: string, callback: (options: SelectOption[]) => void) => {
      latestInputValue.current = inputValue;
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }

      debounceTimeout.current = setTimeout(() => {
        fetch(`/api/zcm/product-lines?search=${latestInputValue.current}`).then(
          (response) => {
            if (response && response.ok) {
              response.json().then((data: ProductLineOption[]) => {
                callback(
                  data.map((item: ProductLineOption) => {
                    return {
                      label: item.productLineName,
                      value: item.productLineId,
                    };
                  })
                );
              });
            }
          }
        );
      }, 1000);
    },
    []
  );

  const onProductLineChange = (option: SingleValue<SelectOption>) => {
    setRepoMetadata((pre: RepoMetadata | null) => {
      const newData = pre ? { ...pre } : defaultRepoMetadata();
      newData.product_line_id = option?.value ?? null;
      newData.product_line_name = option?.label ?? null;
      return newData;
    });
  };

  const loadProducts = useCallback(
    (inputValue: string, callback: (options: SelectOption[]) => void) => {
      latestInputValue.current = inputValue;
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }

      debounceTimeout.current = setTimeout(() => {
        fetch(`/api/zcm/products?search=${inputValue}`).then((response) => {
          if (response && response.ok) {
            response.json().then((data: ProductOption[]) => {
              callback(
                data.map((item: ProductOption) => {
                  return { label: item.productName, value: item.productId };
                })
              );
            });
          }
        });
      }, 1000);
    },
    []
  );

  const onProductChange = (option: SingleValue<SelectOption>) => {
    setRepoMetadata((pre: RepoMetadata | null) => {
      const newData = pre ? { ...pre } : defaultRepoMetadata();
      newData.product_id = option?.value ?? null;
      newData.product_name = option?.label ?? null;
      return newData;
    });
  };

  const loadProductVersions = useCallback(
    (inputValue: string, callback: (options: SelectOption[]) => void) => {
      latestInputValue.current = inputValue;
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }

      debounceTimeout.current = setTimeout(() => {
        fetch(`/api/zcm/product-versions?search=${inputValue}`).then(
          (response) => {
            if (response && response.ok) {
              response.json().then((data: ProductVersionOption[]) => {
                callback(
                  data.map((item: ProductVersionOption) => {
                    return {
                      label: item.productVersionCode,
                      value: item.productVersionId,
                    };
                  })
                );
              });
            }
          }
        );
      }, 1000);
    },
    []
  );

  const onProductVersionChange = (option: SingleValue<SelectOption>) => {
    setRepoMetadata((pre: RepoMetadata | null) => {
      const newData = pre ? { ...pre } : defaultRepoMetadata();
      newData.product_version_id = option?.value ?? null;
      newData.product_version_code = option?.label ?? null;
      return newData;
    });
  };

  const loadSolutions = useCallback(
    (inputValue: string, callback: (options: SelectOption[]) => void) => {
      latestInputValue.current = inputValue;
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }

      debounceTimeout.current = setTimeout(() => {
        fetch(`/api/zcm/solutions?search=${inputValue}`).then((response) => {
          if (response && response.ok) {
            response.json().then((data) => {
              callback(
                data.map((item: SolutionOption) => {
                  return { value: item.productId, label: item.productName };
                })
              );
            });
          }
        });
      }, 1000);
    },
    []
  );

  const onSolutionsChange = (options: MultiValue<SelectOption>) => {
    setRepoMetadata((pre: RepoMetadata | null) => {
      const newData = pre ? { ...pre } : defaultRepoMetadata();
      const solutions = newData.solutions;
      const newSolutions: Solution[] = options.map((option: SelectOption) => {
        const oldSolution = solutions.find(
          (solution) => solution.solution_id === option.value
        );
        if (oldSolution) {
          return { ...oldSolution };
        } else {
          return {
            release_pkg_id: null,
            release_pkg_code: null,
            solution_id: option.value,
            solution_name: option.label,
          };
        }
      });
      newData.solutions = newSolutions;
      return newData;
    });
  };

  const loadDocChainTopics = useCallback(
    (inputValue: string, callback: (options: SelectOption[]) => void) => {
      // 前端过滤逻辑
      let filteredTopics = availableDocTopics;
      
      if (inputValue.trim()) {
        // 有搜索内容时进行过滤
        const query = inputValue.toLowerCase();
        filteredTopics = availableDocTopics.filter((topic) => {
          return (
            topic.name.toLowerCase().includes(query) ||
            topic.id.toString().includes(query) ||
            topic.state.toLowerCase().includes(query)
          );
        });
      } else {
        // 没有搜索内容时，只显示前100个
        filteredTopics = availableDocTopics.slice(0, 100);
      }
      const options = filteredTopics.map((topic) => ({
        label: topic.name,
        value: topic.id.toString(),
      }));

      callback(options);
    },
    [availableDocTopics]
  );

  const onDocChainTopicChange = (option: SingleValue<SelectOption>) => {
    setSelectedDocChainTopic(option);
  };

  return (
    <div className="fixed inset-0 bg-black/60 bg-opacity-50 flex items-center justify-center z-50 p-4 backdrop-blur-sm text-start">
      <div className="bg-[var(--card-bg)] rounded-lg shadow-2xl border border-[var(--border-color)] w-full max-w-2xl max-h-[95vh] overflow-hidden">
        <div className="p-6 border-b border-[var(--border-color)] flex flex-col gap-y-6">
          {/* modal header */}
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-[var(--foreground)]">
              {messages.components.wikiInfoEditor.editWiki}
            </h3>
            {onClose && (
              <button
                onClick={onClose}
                className="text-[var(--muted)] hover:text-[var(--foreground)] transition-colors"
              >
                <FaTimes size={16} />
              </button>
            )}
          </div>

          {/* modal body */}
          <div
            className="w-full flex flex-col gap-y-4 max-h-[60vh] overflow-y-auto"
            style={{ scrollbarWidth: "none" }}
          >
            {
              !settings.isProjectMode && (
                <>
                  <div>
              <label className="block text-sm font-medium text-black mb-2 dark:text-white">
                {messages.components.wikiInfoEditor.productLine}
              </label>
              <AsyncSelect
                classNamePrefix={`${theme === 'dark' ? 'dark-react-select' : 'react-select'}`}
                isClearable
                value={selectedProductLine}
                loadOptions={loadProductLines}
                onChange={onProductLineChange}
                placeholder={messages.components.wikiInfoEditor.productLinePlaceholder}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-black mb-2 dark:text-white">
                {messages.components.wikiInfoEditor.product}
              </label>
              <AsyncSelect
                classNamePrefix={`${theme === 'dark' ? 'dark-react-select' : 'react-select'}`}
                isClearable
                value={selectedProduct}
                loadOptions={loadProducts}
                onChange={onProductChange}
                placeholder={messages.components.wikiInfoEditor.productPlaceholder}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-black mb-2 dark:text-white">
                {messages.components.wikiInfoEditor.productVersion}
              </label>
              <AsyncSelect
                classNamePrefix={`${theme === 'dark' ? 'dark-react-select' : 'react-select'}`}
                isClearable
                value={selectedProductVersion}
                loadOptions={loadProductVersions}
                onChange={onProductVersionChange}
                placeholder={messages.components.wikiInfoEditor.productVersionPlaceholder}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-black mb-2 dark:text-white">
                {messages.components.wikiInfoEditor.solution}
              </label>
              <AsyncSelect
                classNamePrefix={`${theme === 'dark' ? 'dark-react-select' : 'react-select'}`}
                isClearable
                value={selectedSolutions}
                loadOptions={loadSolutions}
                isMulti
                onChange={onSolutionsChange}
                placeholder={messages.components.wikiInfoEditor.solutionPlaceholder}
              />
            </div>
                </>
              )
            }
            <div>
              <label className="block text-sm font-medium mb-2 text-black dark:text-white">
                {messages.components.wikiInfoEditor.tags}
              </label>
              <div>
                <WikiTagManager 
                  wiki_id={wikiInfo?.id ?? 0}
                  wiki_tags={wikiInfo?.tags ?? []}
                  visibility={wikiInfo?.visibility ?? 0}
                />
              </div>
            </div>

            <div>
              <label className="flex items-center gap-2 block text-sm font-medium text-black mb-2 dark:text-white">
                {settings.isProjectMode ? messages.components.wikiInfoEditor.projectKnowledge : messages.components.wikiInfoEditor.docchainTopic || "DocChain Topic"}
                <span
                  className="inline-flex items-center cursor-pointer"
                  title={settings.isProjectMode ? messages.form.projectDocchainTips : messages.form?.docchainInfo}
                >
                  <svg
                    className="w-4 h-4 text-[var(--muted)] hover:text-[var(--accent-primary)] transition-colors"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 16h-1v-4h-1m1-4h.01M12 2a10 10 0 100 20 10 10 0 000-20z"
                    />
                  </svg>
                </span>
              </label>
              {isLoadingTopics ? (
                <div className="text-sm text-[var(--muted)] py-2">
                  {messages.common?.loading || "Loading topics..."}
                </div>
              ) : (
                <AsyncSelect
                  classNamePrefix={`${theme === 'dark' ? 'dark-react-select' : 'react-select'}`}
                  isClearable
                  value={selectedDocChainTopic}
                  loadOptions={loadDocChainTopics}
                  defaultOptions={defaultOptions}
                  onChange={onDocChainTopicChange}
                  placeholder={messages.components.wikiInfoEditor.searchTopics || "Search topics..."}
                />
              )}
            </div>


            <div>
              <label className="block text-sm font-medium mb-2 text-black dark:text-white">
                {settings.isProjectMode ?  messages.components.wikiInfoEditor.desc : messages.components.wikiInfoEditor.comments}
              </label>
              <textarea
                rows={3}
                maxLength={255}
                className="input-japanese block w-full px-3 py-2 text-sm rounded-md bg-transparent text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)] border border-[var(--border-color)] resize-none"
                placeholder={settings.isProjectMode ? messages.components.wikiInfoEditor.descPlaceholder : messages.components.wikiInfoEditor.commentsPlaceholder}
                value={comments}
                onChange={(e) => setComments(e.target.value)}
              />
            </div>
          </div>

          {/* modal footer */}
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              {!settings.isProjectMode && (
                <>
                <div className="flex items-center gap-x-2">
                <button
                  className="px-4 py-2 text-sm font-medium rounded-md border border-[var(--border-color)] text-[var(--accent-primary)] hover:bg-[var(--accent-primary)]/10 transition-colors cursor-pointer"
                  onClick={syncRepoMetadata}
                >
                  {messages.components.wikiInfoEditor.syncRepositoryMetadata}
                </button>
                <FaQuestionCircle
                  className="text-[var(--accent-primary)]"
                  title={messages.components.wikiInfoEditor.syncRepositoryMetadataDescription}
                />
              </div>
                </>
              )}
            </div>
            <div className="flex items-center gap-x-2">
              <button
                className="px-4 py-2 text-sm font-medium rounded-md border border-[var(--border-color)] text-[var(--accent-primary)] hover:bg-[var(--accent-primary)]/10 transition-colors cursor-pointer"
                onClick={onClose}
              >
                {messages.components.wikiInfoEditor.cancel}
              </button>
              <button
                className="px-4 py-2 text-sm font-medium rounded-md border border-transparent bg-[var(--accent-primary)]/90 text-white hover:bg-[var(--accent-primary)] transition-colors disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
                onClick={onSubmit}
              >
                {messages.components.wikiInfoEditor.submit}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WikiInfoEditor;
