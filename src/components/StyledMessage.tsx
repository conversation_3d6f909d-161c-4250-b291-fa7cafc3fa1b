import React from 'react';

interface StyledMessageProps {
  message: string;
  className?: string;
}

export const StyledMessage: React.FC<StyledMessageProps> = ({ message, className = '' }) => {
  // 解析消息中的样式标记
  const parts = message.split(/(<span[^>]*>.*?<\/span>)/);
  
  return (
    <div className={className}>
      {parts.map((part, index) => {
        if (part.startsWith('<span') && part.endsWith('</span>')) {
          // 提取span标签的内容和类名
          const classMatch = part.match(/class="([^"]*)"/);
          const contentMatch = part.match(/<span[^>]*>(.*?)<\/span>/);
          
          if (classMatch && contentMatch) {
            const classes = classMatch[1];
            const content = contentMatch[1];
            
            return (
              <span key={index} className={classes}>
                {content}
              </span>
            );
          }
        }
        return part;
      })}
    </div>
  );
};

