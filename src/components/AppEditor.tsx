"use client";

import { useLanguage } from "@/contexts/LanguageContext";
import { useToast } from "@/contexts/ToastContext";
import { authFetch } from "@/utils/authFetch";
import React, { useEffect, useState } from "react";
import { FaTimes } from "react-icons/fa";

interface AppEditorProps {
  onClose: () => void;
  onFinish: () => void;
  exist?: App;
}

interface App {
  id: number;
  app_name: string;
  app_code: string;
  comments?: string;
}

const AuthAppModal = (props: AppEditorProps) => {
  const { onClose, onFinish, exist } = props;

  const { addToast } = useToast();

  const [appCode, setAppCode] = useState<string>("");
  const [appName, setAppName] = useState<string>("");
  const [comments, setComments] = useState<string>("");
  const { messages } = useLanguage();

  useEffect(() => {
    if (exist) {
      setAppName(exist.app_name);
      setAppCode(exist.app_code);
      setComments(exist.comments ?? "");
    }
  }, [exist]);

  const onSubmit = () => {
    if (!appName) {
      addToast({
        type: "warning",
        title: messages.authApp.validateParams,
        message: messages.authApp.appNameNotEmpty,
      });
      return;
    }

    if (!appCode) {
      addToast({
        type: "warning",
        title: messages.authApp.validateParams,
        message: messages.authApp.appCodeNotEmpty,
      });
      return;
    }

    if (exist) {
      updateApp();
    } else {
      createApp();
    }
  };

  const createApp = async () => {
    const response = await authFetch(`/api/apps`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        app_name: appName,
        app_code: appCode,
        comments: comments,
      }),
    });

    if (response && response.ok) {
      addToast({
        type: "success",
        title: messages.authApp.createApp,
        message: messages.authApp.createAppSuccess,
      });
      onFinish();
    } else {
      const data = await response?.json();
      addToast({
        type: "error",
        title: messages.authApp.createApp,
        message:
          data && data.detail ? data.detail : messages.authApp.createAppFail,
      });
    }
  };

  const updateApp = async () => {
    const response = await authFetch(`/api/apps/${exist?.id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        app_name: appName,
        app_code: appCode,
        comments: comments,
      }),
    });

    if (response && response.ok) {
      addToast({
        type: "success",
        title: messages.authApp.editApp,
        message: messages.authApp.editAppSuccess,
      });
      onFinish();
    } else {
      const data = await response?.json();
      addToast({
        type: "error",
        title: messages.authApp.editApp,
        message:
          data && data.detail ? data.detail : messages.authApp.editAppFail,
      });
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 bg-opacity-50 flex items-center justify-center z-50 p-4 backdrop-blur-sm text-start">
      <div className="bg-[var(--card-bg)] rounded-lg shadow-2xl border border-[var(--border-color)] w-full max-w-2xl max-h-[95vh] overflow-hidden">
        <div className="border-b border-[var(--border-color)] flex flex-col gap-y-6">
          {/* modal header */}
          <div className="flex items-center justify-between px-6 pt-6">
            <h3 className="text-lg font-semibold text-[var(--foreground)]">
              {exist
                ? messages.authApp.editApp
                : messages.authApp.createApp}
            </h3>
            {onClose && (
              <button
                onClick={onClose}
                className="text-[var(--muted)] hover:text-[var(--foreground)] transition-colors"
              >
                <FaTimes size={16} />
              </button>
            )}
          </div>

          {/* modal body */}
          <div className="w-full max-h-[60vh] flex flex-col gap-y-5 px-6">
            <div>
              <label className="block text-sm font-medium text-black mb-2 dark:text-white">
                {messages.authApp.appName}
              </label>
              <input
                type="text"
                className="input-japanese block w-full px-3 py-2 text-sm rounded-md bg-transparent text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)] border border-[var(--border-color)]"
                placeholder={messages.authApp.appNamePlaceholder}
                value={appName}
                onChange={(e) => setAppName(e.target.value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-black mb-2 dark:text-white">
                {messages.authApp.appCode}
              </label>
              <input
                type="text"
                className="input-japanese block w-full px-3 py-2 text-sm rounded-md bg-transparent text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)] border border-[var(--border-color)]"
                placeholder={messages.authApp.appCodePlaceholder}
                value={appCode}
                onChange={(e) => setAppCode(e.target.value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 text-black dark:text-white">
                {messages.authApp.comments}
              </label>
              <textarea
                rows={3}
                maxLength={255}
                className="input-japanese block w-full px-3 py-2 text-sm rounded-md bg-transparent text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)] border border-[var(--border-color)] resize-none"
                placeholder={messages.authApp.commentsPlaceholder}
                value={comments}
                onChange={(e) => setComments(e.target.value)}
              />
            </div>
          </div>

          {/* modal footer */}
          <div className="flex justify-end items-center px-6 py-3 border-t border-[var(--border-color)]">
            <div className="flex items-center gap-x-2">
              <button
                className="px-4 py-2 text-sm font-medium rounded-md border border-[var(--border-color)] text-[var(--accent-primary)] hover:bg-[var(--accent-primary)]/10 transition-colors cursor-pointer"
                onClick={onClose}
              >
                {messages.authApp.cancel}
              </button>
              <button
                className="px-4 py-2 text-sm font-medium rounded-md border border-transparent bg-[var(--accent-primary)]/90 text-white hover:bg-[var(--accent-primary)] transition-colors disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
                onClick={onSubmit}
              >
                {messages.authApp.submit}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthAppModal;
