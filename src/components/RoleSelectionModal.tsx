"use client";

import { useLanguage } from "@/contexts/LanguageContext";
import { useToast } from "@/contexts/ToastContext";
import { getRoleMap } from "@/utils/role";
import React, { useEffect, useState } from "react";
import { FaTimes } from "react-icons/fa";

interface RoleSelectionModalProps {
  user_id?: number;
  onClose: () => void;
}

interface Role {
  id: number;
  role_name: string;
  role_code: string;
  comments: string;
}

const RoleSelectionModal = (props: RoleSelectionModalProps) => {
  const { user_id, onClose } = props;

  const { addToast } = useToast();
  const { messages, language } = useLanguage();

  const [roles, setRoles] = useState<Role[]>([]);
  const [selectedRole, setSelectedRole] = useState<number | undefined>();

  useEffect(() => {
    fetchRoles();
    fetchUserRoles();
  }, []);

  const fetchRoles = async () => {
    const response = await fetch("/api/roles");
    if (response && response.ok) {
      const data: Role[] = await response.json();
      setRoles(data.filter(item => item.id !== 3));
    }
  };

  const fetchUserRoles = async () => {
    const response = await fetch(`/api/users/${user_id}/roles`);
    if (response && response.ok) {
      const data = await response.json();
      const roles = [...new Set<number>(data.map((role: Role) => role.id))];
      if (roles) {
        setSelectedRole(roles[0]);
      }
    }
  };

  const modifyUserRoles = async () => {
    const response = await fetch(`/api/users/${user_id}/roles`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        id: user_id,
        role_ids: selectedRole ? [selectedRole] : [],
      }),
    });
    if (response && response.ok) {
      addToast?.({ type: "success", title: "", message: messages.components.roleSelectionModal.modifyUserRolesSuccess });
      onClose();
    } else {
      if (response) {
        const data = await response.json();
        addToast?.({
          type: "error",
          title: "",
          message: data.detail ?? messages.components.roleSelectionModal.modifyUserRolesFailed,
        });
      }
    }
  };

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.value) {
      const value = Number(e.target.value);
      setSelectedRole(value);
    } else {
      setSelectedRole(undefined);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm">
      <div className="relative w-full max-w-3xl bg-[var(--card-bg)] rounded-xl shadow-2xl border border-[var(--border-color)]">
        <div className="flex items-center justify-between p-5 border-b border-[var(--border-color)]">
          <h3 className="text-xl font-semibold text-[var(--foreground)]">
            {messages.components.roleSelectionModal.userAuthorization}
          </h3>
          <button
            onClick={onClose}
            className="p-2 rounded-full text-[var(--muted)] hover:bg-[var(--accent-secondary)] hover:text-[var(--foreground)] transition-colors"
          >
            <FaTimes />
          </button>
        </div>
        <div className="flex p-8 py-12">
          <div className="w-full">
            <label className="block text-sm font-medium text-[var(--muted)] mb-4">
              {messages.components.roleSelectionModal.selectRole}
            </label>
            <div className="flex flex-wrap gap-x-8 gap-y-4">
              {roles.map((role) => (
                <label key={role.id} className="flex items-center gap-x-1.5 cursor-pointer" title={role.comments}>
                  <input
                    type="radio"
                    value={role.id}
                    checked={selectedRole === role.id}
                    onChange={onChange}
                    name="myRadioGroup"
                  />
                  <div>{getRoleMap(role.id, language)}</div>
                </label>
              ))}
            </div>
          </div>
        </div>
        <div className="flex justify-end p-5 border-t border-[var(--border-color)] bg-[var(--accent-secondary)] rounded-b-xl">
          <button
            onClick={onClose}
            className="btn-secondary mr-3 cursor-pointer"
          >
            {messages.components.roleSelectionModal.cancel}
          </button>
          <button
            onClick={modifyUserRoles}
            className="btn-primary mr-3 cursor-pointer"
          >
            {messages.components.roleSelectionModal.submit}
          </button>
        </div>
      </div>
    </div>
  );
};

export default RoleSelectionModal;
