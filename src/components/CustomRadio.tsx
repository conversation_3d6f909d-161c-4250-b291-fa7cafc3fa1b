import React from "react";

interface CustomRadioProps {
  selected?: boolean;
  onClick: () => void;
  children: React.ReactNode;
}

const CustomRadio = (props: CustomRadioProps) => {
  const { selected = false, onClick, children } = props;
  return (
    <button
      type="button"
      onClick={onClick}
      className={`flex-1 flex items-center justify-between p-2 rounded-md border transition-colors cursor-pointer ${
        selected
          ? "bg-[var(--accent-primary)]/10 border-[var(--accent-primary)]/30 text-[var(--accent-primary)]"
          : "bg-[var(--background)]/50 border-[var(--border-color)] text-[var(--foreground)] hover:bg-[var(--background)]"
      }`}
    >
      {children}
      {selected ? (
        <div className="ml-2 h-4 w-4 rounded-full bg-[var(--accent-primary)]/20 flex items-center justify-center">
          <div className="h-2 w-2 rounded-full bg-[var(--accent-primary)]"></div>
        </div>
      ) : (
        <div className="ml-2 h-4 w-4 rounded-full border border-[var(--accent-primary)]/20 flex items-center justify-center"></div>
      )}
    </button>
  );
};

export default CustomRadio;
