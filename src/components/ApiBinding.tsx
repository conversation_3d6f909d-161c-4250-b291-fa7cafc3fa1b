import { useToast } from "@/contexts/ToastContext";

import React, { useEffect, useState } from "react";
import { FaTimes } from "react-icons/fa";
import CustomRadio from "./CustomRadio";
import { authFetch } from "@/utils/authFetch";
import { useLanguage } from "@/contexts/LanguageContext";

interface ApiBindingProps {
  app_primary_key: number;
  onClose: () => void;
  onFinish: () => void;
}

interface Api {
  id: number;
  api_name: string;
  api_path: string;
  api_method: string;
  comments: string;
}

const ApiBinding = (props: ApiBindingProps) => {
  const { app_primary_key, onClose, onFinish } = props;

  const { addToast } = useToast();

  const [selectedApiList, setSelectedApiList] = useState<number[]>([]);

  const [apiList, setApiList] = useState<Api[]>([]);

  const { messages } = useLanguage();

  useEffect(() => {
    fetchUnboundApiList();
  }, []);

  const fetchUnboundApiList = async () => {
    const response = await authFetch(
      `/api/apps/${app_primary_key}/api/unbound`
    );
    if (response && response.ok) {
      const data = await response.json();
      setApiList(data);
    }
  };

  const onSubmit = async () => {
    if (!selectedApiList.length) {
      addToast({
        type: "warning",
        title: messages.authApp.bindApi,
        message: messages.authApp.selectApiPlaceholder,
      });
      return;
    }
    const response = await authFetch(`/api/apps/${app_primary_key}/api`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        api_list: selectedApiList,
      }),
    });

    if (response && response.ok) {
      addToast({
        type: "success",
        title: messages.authApp.bindApi,
        message: messages.authApp.bindApiSuccess,
      });
      onFinish();
    } else {
      addToast({
        type: "error",
        title: messages.authApp.bindApi,
        message: messages.authApp.bindApiFail,
      });
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 bg-opacity-100 flex items-center justify-center z-50 p-4 backdrop-blur-sm text-start">
      <div className="bg-[var(--card-bg)] rounded-lg shadow-2xl border border-[var(--border-color)] w-full max-w-xl max-h-[95vh] overflow-hidden">
        <div className="p-6 border-b border-[var(--border-color)] flex flex-col gap-y-6">
          {/* modal header */}
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-[var(--foreground)]">
              {messages.authApp.bindApi}
            </h3>
            {onClose && (
              <button
                onClick={onClose}
                className="text-[var(--muted)] hover:text-[var(--foreground)] transition-colors"
              >
                <FaTimes size={16} />
              </button>
            )}
          </div>

          {/* modal body */}
          <div className="w-full flex flex-col gap-y-4 max-h-[50vh] overflow-y-auto">
            <div className="flex flex-col gap-2">
              {!apiList.length && (
                <div className="flex justify-center items-center h-20">
                  {messages.authApp.noData}
                </div>
              )}
              {apiList.map((api) => (
                <div key={api.id} className="flex items-center space-x-4">
                  <CustomRadio
                    selected={selectedApiList.includes(api.id)}
                    onClick={() => {
                      if (selectedApiList.includes(api.id)) {
                        setSelectedApiList((pre) =>
                          pre.filter((item) => item !== api.id)
                        );
                      } else {
                        setSelectedApiList((pre) => [...pre, api.id]);
                      }
                    }}
                  >
                    <div className="flex items-center">
                      <div className="text-left">
                        <div className="font-medium text-sm mb-1">
                          {api.api_name}
                        </div>
                        <div className="text-xs opacity-80">
                          {`${api.api_path} [${api.api_method}]`}
                        </div>
                      </div>
                    </div>
                  </CustomRadio>
                </div>
              ))}
            </div>
          </div>

          {/* modal footer */}
          <div className="flex justify-end items-center">
            <div className="flex items-center gap-x-2">
              <button
                className="px-4 py-2 text-sm font-medium rounded-md border border-[var(--border-color)] text-[var(--accent-primary)] hover:bg-[var(--accent-primary)]/10 transition-colors cursor-pointer"
                onClick={onClose}
              >
                {messages.authApp.cancel}
              </button>
              <button
                onClick={onSubmit}
                className="px-4 py-2 text-sm font-medium rounded-md border border-transparent bg-[var(--accent-primary)]/90 text-white hover:bg-[var(--accent-primary)] transition-colors disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
              >
                {messages.authApp.submit}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApiBinding;
