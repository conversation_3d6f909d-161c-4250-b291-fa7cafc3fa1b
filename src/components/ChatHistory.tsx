import React, { useState, useEffect, useCallback } from 'react';
import { authFetch } from '@/utils/authFetch';
import { useToast } from '@/contexts/ToastContext';
import Popconfirm from './Popconfirm';
import { useLanguage } from '@/contexts/LanguageContext';

interface ChatSession {
  id: string;
  title: string;
}

interface ChatHistoryProps {
  currentWikiId: string | null;
  userInfo: { id: number } | null;
}

const ChatHistory: React.FC<ChatHistoryProps> = React.memo(function ChatHistory({ currentWikiId, userInfo }) {
  const { messages: t } = useLanguage()
  // Chat History States
  const [chatSearchQuery, setChatSearchQuery] = useState('');
  const [currentChatPage, setCurrentChatPage] = useState(1);
  const [editingChatId, setEditingChatId] = useState<string | null>(null);
  const [editingChatTitle, setEditingChatTitle] = useState('');
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [totalChatSessions, setTotalChatSessions] = useState(0);
  const [chatLoading, setChatLoading] = useState(false);
  const [chatError, setChatError] = useState<string | null>(null);
  const chatPageSize = 10;
  
  // BroadcastChannel 用于监听会话状态
  const [activeSessions, setActiveSessions] = useState<Set<string>>(new Set());
  
  // 防抖搜索功能
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(chatSearchQuery);

  const { addToast } = useToast();

  // 获取会话历史的函数
  const fetchChatSessions = useCallback(async (pageNum?: number) => {
    if (!currentWikiId || !userInfo?.id) return;
    
    try {
      setChatLoading(true);
      setChatError(null);
      
      const pageToFetch = pageNum !== undefined ? pageNum : currentChatPage;
      
      const params = new URLSearchParams({
        wiki_id: currentWikiId,
        pageNum: pageToFetch.toString(),
        pageSize: chatPageSize.toString(),
      });
      
      // 添加搜索参数
      if (debouncedSearchQuery.trim()) {
        params.append('search', debouncedSearchQuery.trim());
      }
      
      const response = await authFetch(`/api/chat/session?${params.toString()}`);
      if (response && !response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response?.json();
      setChatSessions(data.sessions || []);
      setTotalChatSessions(data.total || 0);
    } catch (err) {
      console.error("Failed to fetch chat sessions:", err);
      setChatError(t.components?.chatHistory?.fetchError || 'Failed to fetch chat history');
      setChatSessions([]);
    } finally {
      setChatLoading(false);
    }
  }, [currentWikiId, currentChatPage, debouncedSearchQuery, userInfo?.id]);

  // 获取会话历史
  useEffect(() => {
    fetchChatSessions();
  }, [fetchChatSessions]);

  // 防抖处理搜索查询
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(chatSearchQuery);
    }, 500);

    return () => clearTimeout(timer);
  }, [chatSearchQuery]);

  // 初始化 BroadcastChannel 监听
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const channel = new BroadcastChannel('chat-session-status');
      
      const handleMessage = (event: MessageEvent) => {
        const { type, sessionId } = event.data;
        
        setActiveSessions(prev => {
          const newSet = new Set(prev);
          if (type === 'SESSION_OPEN') {
            newSet.add(sessionId);
          } else if (type === 'SESSION_CLOSE') {
            newSet.delete(sessionId);
          }
          return newSet;
        });
      };
      
      channel.addEventListener('message', handleMessage);
      
      return () => {
        channel.removeEventListener('message', handleMessage);
        channel.close();
      };
    }
  }, []);

  // Reset page when search query changes
  useEffect(() => {
    setCurrentChatPage(1);
  }, [debouncedSearchQuery]);



  // Chat History Logic
  const totalChatPages = Math.ceil(totalChatSessions / chatPageSize);

  const handleEditChat = (chat: { id: string; title: string }) => {
    setEditingChatId(chat.id);
    setEditingChatTitle(chat.title);
  };

  const handleDeleteChat = async (chatId: string) => {
      try {
        const response = await authFetch(`/api/chat/session/delete?chat_sid=${encodeURIComponent(chatId)}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        if (response && response.ok) {
          const result = await response.json();
          if (result.success) {
            addToast({
              type: 'success',
              title: t.components?.chatHistory?.deleteSuccess || 'Delete successful',
              message: t.components.chatHistory.sessionDeleted || 'Session deleted'
            });
            
            // 删除成功后，重置到第一页并重新查询数据
            setCurrentChatPage(1);
            // 重新获取第一页数据，保持当前的搜索条件
            await fetchChatSessions(1);
          } else {
            throw new Error(result.message || (t.components?.chatHistory?.deleteFailed || 'Delete failed'));
          }
        } else {
          const errorData = await response?.json().catch(() => ({}));
          throw new Error(errorData.detail || `HTTP error! status: ${response?.status}`);
        }
      } catch (error) {
        console.error('Error deleting chat session:', error);
        addToast({
          type: 'error',
          title: t.components?.chatHistory?.deleteFailed || 'Delete failed',
          message: error instanceof Error ? error.message : (t.components?.chatHistory?.deleteError || 'Error deleting session')
        });
      }
  };

  const handleSaveChatTitle = async () => {
    if (editingChatId && editingChatTitle.trim()) {
      try {
        const response = await authFetch('/api/chat/session/update_title', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            chat_sid: editingChatId,
            title: editingChatTitle.trim()
          })
        });

        if (response && response.ok) {
          const result = await response.json();
          if (result.success) {
            addToast({
              type: 'success',
              title: t.components?.chatHistory?.saveSuccess || 'Save successful',
              message: t.components?.chatHistory?.titleUpdated || 'Session title updated'
            });
            
            setChatSessions(prev => 
              prev.map(chat => 
                chat.id === editingChatId 
                  ? { ...chat, title: editingChatTitle.trim() }
                  : chat
              )
            );
            
            setEditingChatId(null);
            setEditingChatTitle('');
          } else {
            throw new Error(result.message || (t.components?.chatHistory?.updateFailed || 'Update failed'));
          }
        } else {
          const errorData = await response?.json().catch(() => ({}));
          throw new Error(errorData.detail || `HTTP error! status: ${response?.status}`);
        }
      } catch (error) {
        console.error('Error updating chat session title:', error);
        addToast({
          type: 'error',
          title: t.components?.chatHistory?.saveFailed || 'Save failed',
          message: error instanceof Error ? error.message : (t.components?.chatHistory?.updateError || 'Error updating session title')
        });
      }
    }
  };

  const handleCancelEdit = () => {
    setEditingChatId(null);
    setEditingChatTitle('');
  };

  return (
    <div className="p-4 bg-[var(--card-bg)] rounded-lg shadow-sm border border-[var(--border-color)]">
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-semibold text-sm">{t.components?.chatHistory?.title || 'Chat History'}</h3>
        <button
          onClick={() => {
            // 重置状态
            setCurrentChatPage(1);
            setChatSearchQuery('');
            setDebouncedSearchQuery('');
            // 强制触发重新获取第一页数据
            fetchChatSessions(1);
          }}
          className="p-1 text-[var(--muted)] hover:text-[var(--danger)] hover:bg-[var(--accent-primary)]/10 rounded transition-colors"
          title={t.components?.chatHistory?.refreshList || 'Refresh session list'}
          disabled={chatLoading}
        >
          {chatLoading ? (
            <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          ) : (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          )}
        </button>
      </div>
      
      {/* Search Box */}
      <div className="mb-4">
        <input
          type="text"
          placeholder={t.components?.chatHistory?.searchPlaceholder || 'Search sessions...'}
          className="w-full px-3 py-2 text-sm border border-[var(--border-color)] rounded-md bg-[var(--background)] text-[var(--foreground)] placeholder:text-[var(--muted)] focus:outline-none focus:border-[var(--accent-primary)] focus:ring-1 focus:ring-[var(--accent-primary)]"
          value={chatSearchQuery}
          onChange={(e) => setChatSearchQuery(e.target.value)}
        />
      </div>

      {/* Chat History List */}
      <div className="max-h-[calc(100vh-450px)] overflow-y-auto">
        {chatLoading ? (
          <div className="flex justify-center items-center h-20">
            <div className="w-5 h-5 border-2 border-[var(--accent-primary)] border-t-transparent rounded-full animate-spin"></div>
          </div>
        ) : chatError ? (
          <div className="text-center py-4 text-[var(--danger)]">
            {chatError}
          </div>
        ) : chatSessions.length === 0 ? (
          <div className="text-center py-4 text-[var(--muted)]">
            {t.components?.chatHistory?.noSessions || 'No chat sessions'}
          </div>
        ) : (
          chatSessions.map((chat) => (
            <div key={chat.id} className="flex items-center justify-between p-2 mb-2 bg-[var(--background)] rounded-md border border-[var(--border-color)] hover:bg-[var(--accent-secondary)]/30 transition-colors">
              <div className="flex items-center flex-1 min-w-0 cursor-pointer" onClick={() => {
                      const url = `/search/${chat.id}`;
                      window.open(url, '_blank');
                    }}>
                <div className="flex-shrink-0 mr-3">
                  <div className="w-8 h-8 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-[var(--foreground)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  {editingChatId === chat.id ? (
                    <input
                      type="text"
                      value={editingChatTitle}
                      onChange={(e) => setEditingChatTitle(e.target.value)}
                      className="w-full px-2 py-1 text-sm border border-[var(--border-color)] rounded bg-[var(--background)] text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)]"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          handleSaveChatTitle();
                        } else if (e.key === 'Escape') {
                          handleCancelEdit();
                        }
                      }}
                      onClick={(e) => e.stopPropagation()}
                      onMouseDown={(e) => e.stopPropagation()}
                      autoFocus
                    />
                  ) : (
                    <div 
                      className="text-sm font-medium text-[var(--foreground)] truncate cursor-pointer"
                      title={chat.title}
                    >
                      {chat.title}
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-2 ml-3">
                {editingChatId === chat.id ? (
                  <>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSaveChatTitle();
                      }}
                      className="p-1.5 text-[var(--accent-primary)] hover:bg-[var(--accent-primary)]/10 rounded transition-colors"
                      title={t.components?.chatHistory?.save || 'Save'}
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCancelEdit();
                      }}
                      className="p-1.5 text-[var(--muted)] hover:bg-[var(--muted)]/10 rounded transition-colors"
                      title={t.components?.chatHistory?.cancel || 'Cancel'}
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </>
                ) : (
                  <>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditChat(chat);
                      }}
                      className="p-1.5 text-[var(--muted)] hover:text-[var(--danger)] hover:bg-[var(--accent-primary)]/10 rounded transition-colors"
                      title={t.components?.chatHistory?.edit || 'Edit session'}
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>
                    <Popconfirm
                      title={t.components?.chatHistory?.confirmDelete || 'Are you sure you want to delete this session?'}
                      onConfirm={() => handleDeleteChat(chat.id)}
                      onCancel={() => {}}
                    >
                    <button
                      // onClick={(e) => {
                      //   e.stopPropagation();
                      //   handleDeleteChat(chat.id);
                      // }}
                      disabled={activeSessions.has(chat.id)}
                      className={`p-1.5 rounded transition-colors ${
                        activeSessions.has(chat.id)
                          ? 'text-[var(--muted)]/50 cursor-not-allowed'
                          : 'text-[var(--muted)] hover:text-[var(--danger)] hover:bg-[var(--accent-primary)]/10'
                      }`}
                      title={activeSessions.has(chat.id) ? (t.components?.chatHistory?.sessionInUse || 'Session is in use and cannot be deleted') : (t.components?.chatHistory?.delete || 'Delete session')}
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                    </Popconfirm>
                  </>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      {/* Pagination */}
      {totalChatPages > 1 && (
        <div className="flex justify-center items-center gap-2 mt-4 pt-3 border-t border-[var(--border-color)]">
          <button
            onClick={() => setCurrentChatPage(Math.max(1, currentChatPage - 1))}
            disabled={currentChatPage === 1}
            className="px-3 py-1 text-sm text-[var(--muted)] hover:text-[var(--foreground)] disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {t.components?.chatHistory?.previousPage || 'Previous'}
          </button>
          <span className="text-sm text-[var(--muted)]">
            {currentChatPage} / {totalChatPages}
          </span>
          <button
            onClick={() => setCurrentChatPage(Math.min(totalChatPages, currentChatPage + 1))}
            disabled={currentChatPage === totalChatPages}
            className="px-3 py-1 text-sm text-[var(--muted)] hover:text-[var(--foreground)] disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {t.components?.chatHistory?.nextPage || 'Next'}
          </button>
        </div>
      )}
    </div>
  );
});

export default ChatHistory; 