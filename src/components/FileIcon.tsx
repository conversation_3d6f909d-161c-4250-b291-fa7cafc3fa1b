'use client';

import React from 'react';
// 导入 Font Awesome 图标
import {
  FaFolderOpen,    // 文件夹（非实心，线框风格）
  FaFile,           // 默认文件
  FaFileAlt,        // 通用文本文件
  FaFileCode,       // 代码文件
  FaFileImage,      // 图片文件
  FaFileArchive,    // 压缩文件
  FaFileCsv,        // CSV 文件
  FaJs,             // JavaScript 文件
  FaPython,         // Python 文件
  FaJava,           // Java 文件
  FaCss3Alt,        // CSS 文件
  FaHtml5,          // HTML 文件
  FaDocker,         // Dockerfile
  FaDatabase,       // 数据库/SQL
  FaMarkdown,       // Markdown 文件
  FaFilePdf,        // PDF 文件
  FaFileWord,       // Word 文档
  FaFileExcel,      // Excel 文档
  FaFilePowerpoint, // PowerPoint 文档
  FaFileAudio,      // 音频文件
  FaFileVideo,      // 视频文件
} from 'react-icons/fa';

// 导入 Simple Icons (用于特定技术或品牌图标，如 TypeScript, React, JSON, Git等)
import {
  SiTypescript,     // TypeScript
  SiReact,          // React (用于 JSX/TSX)
  SiJson,           // JSON
  SiYarn,           // Yarn (用于 yarn.lock)
  SiNpm,            // NPM (用于 package-lock.json)
  SiWebpack,        // Webpack
  SiGit,            // Git (用于 .gitignore)
  SiGo,             // Go 语言
  SiPhp,            // PHP 语言
  SiRuby,           // Ruby 语言
} from 'react-icons/si';


interface FileIconProps {
  fileName: string;
  isDirectory?: boolean;
  className?: string;
}

const FileIcon: React.FC<FileIconProps> = ({ fileName, isDirectory = false, className = "w-4 h-4" }) => {
  if (isDirectory) {
    // 目录图标
    return <FaFolderOpen className={`${className} text-blue-400`} />;
  }

  // 统一使用 basename（兼容路径传入的情况）
  const basename = (fileName.split(/[/\\]/).pop() || fileName);
  const ext = basename.includes('.') ? basename.split('.').pop()?.toLowerCase() : undefined;
  const fileNameLower = basename.toLowerCase();

  // 定义图标配置的类型
  type IconConfig = {
    icon: React.ElementType; // React 组件类型
    color: string;           // Tailwind CSS 颜色类
  };

  /**
   * 根据文件扩展名或特定文件名获取对应的图标和颜色配置。
   * 优先级：特定文件名 > 文件扩展名 > 默认图标。
   */
  const getIconConfig = (extension: string | undefined, name: string): IconConfig => {
    // 1. 特殊文件名处理 (优先级最高)
    if (name === 'dockerfile' || name === 'dockerfile.txt') {
      return { icon: FaDocker, color: 'text-blue-600' };
    }
    if (name === 'package.json') {
      return { icon: SiJson, color: 'text-purple-500' };
    }
    if (name === 'package-lock.json') {
      return { icon: SiNpm, color: 'text-red-600' };
    }
    if (name === 'yarn.lock') {
      return { icon: SiYarn, color: 'text-cyan-500' };
    }
    if (name === '.gitignore') {
      return { icon: SiGit, color: 'text-orange-600' };
    }
    if (name.startsWith('readme') && extension === 'md') {
      return { icon: FaMarkdown, color: 'text-blue-600' };
    }
    if (name.startsWith('readme')) { // 通用的 readme 文件
      return { icon: FaFileAlt, color: 'text-blue-600' };
    }
    if (name.startsWith('license')) {
      return { icon: FaFileAlt, color: 'text-yellow-600' };
    }
    if (name.startsWith('webpack.config')) {
      return { icon: SiWebpack, color: 'text-blue-400' };
    }
    if (name === 'makefile' || name === 'Makefile') {
      return { icon: FaFileCode, color: 'text-gray-700' };
    }

    // 2. 文件扩展名处理
    switch (extension) {
      // 文档类型
      case 'md':
        return { icon: FaMarkdown, color: 'text-blue-600' };
      case 'txt':
      case 'log':
        return { icon: FaFileAlt, color: 'text-gray-500' };
      case 'pdf':
        return { icon: FaFilePdf, color: 'text-red-600' };
      case 'doc':
      case 'docx':
        return { icon: FaFileWord, color: 'text-blue-700' };
      case 'xls':
      case 'xlsx':
        return { icon: FaFileExcel, color: 'text-green-700' };
      case 'ppt':
      case 'pptx':
        return { icon: FaFilePowerpoint, color: 'text-orange-700' };

      // 配置文件 / 数据文件
      case 'yml':
      case 'yaml':
        return { icon: FaFileCode, color: 'text-red-500' };
      case 'json':
        return { icon: SiJson, color: 'text-purple-500' };
      case 'properties':
      case 'conf':
      case 'cfg':
        return { icon: FaFileCode, color: 'text-green-600' };
      case 'csv':
        return { icon: FaFileCsv, color: 'text-green-500' };
      case 'sql':
        return { icon: FaDatabase, color: 'text-blue-600' };

      // 编程语言
      case 'js':
        return { icon: FaJs, color: 'text-yellow-500' };
      case 'ts':
        return { icon: SiTypescript, color: 'text-blue-500' };
      case 'jsx':
      case 'tsx':
        return { icon: SiReact, color: 'text-cyan-500' };
      case 'java':
        return { icon: FaJava, color: 'text-orange-500' };
      case 'py':
        return { icon: FaPython, color: 'text-green-500' };
      case 'go':
        return { icon: SiGo, color: 'text-cyan-600' };
      case 'php':
        return { icon: SiPhp, color: 'text-purple-600' };
      case 'rb':
        return { icon: SiRuby, color: 'text-red-700' };
      case 'c':
      case 'cpp':
      case 'h':
      case 'hpp':
        return { icon: FaFileCode, color: 'text-blue-800' }; // C/C++ 通用图标

      // 样式文件
      case 'css':
        return { icon: FaCss3Alt, color: 'text-blue-500' };
      case 'scss':
      case 'sass':
        return { icon: FaCss3Alt, color: 'text-pink-600' };
      case 'less':
        return { icon: FaCss3Alt, color: 'text-blue-400' };

      // 网页文件
      case 'html':
      case 'htm':
        return { icon: FaHtml5, color: 'text-orange-600' };
      case 'xml':
        return { icon: FaFileCode, color: 'text-red-500' };

      // 图片文件
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'bmp':
      case 'tiff':
        return { icon: FaFileImage, color: 'text-purple-500' };
      case 'svg':
        return { icon: FaFileImage, color: 'text-red-500' };

      // 音频/视频文件
      case 'mp3':
      case 'wav':
      case 'ogg':
        return { icon: FaFileAudio, color: 'text-green-500' };
      case 'mp4':
      case 'avi':
      case 'mov':
        return { icon: FaFileVideo, color: 'text-indigo-500' };

      // 压缩文件
      case 'zip':
      case 'rar':
      case '7z':
      case 'tar':
      case 'gz':
      case 'tgz':
      case 'bz2':
        return { icon: FaFileArchive, color: 'text-yellow-600' };

      // 可执行文件/脚本
      case 'exe':
      case 'bat':
      case 'sh':
        return { icon: FaFileCode, color: 'text-gray-700' };

      default:
        // 3. 默认文件图标
        return { icon: FaFile, color: 'text-gray-600' };
    }
  };

  const { icon: IconComponent, color } = getIconConfig(ext, fileNameLower);

  return (
    <IconComponent className={`${className} ${color}`} />
  );
};

export default FileIcon;
