'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useToast } from '@/contexts/ToastContext';
import { authFetch } from '@/utils/authFetch';

// File System Access API 类型声明
declare global {
  interface Window {
    showDirectoryPicker?: () => Promise<FileSystemDirectoryHandle>;
  }

  interface FileSystemHandle {
    readonly kind: 'file' | 'directory';
    readonly name: string;
  }

  interface FileSystemFileHandle extends FileSystemHandle {
    readonly kind: 'file';
    getFile(): Promise<File>;
    createWritable(): Promise<FileSystemWritableFileStream>;
  }

  interface FileSystemDirectoryHandle extends FileSystemHandle {
    readonly kind: 'directory';
    getDirectoryHandle(name: string): Promise<FileSystemDirectoryHandle>;
    getFileHandle(name: string, options?: { create?: boolean }): Promise<FileSystemFileHandle>;
    values(): AsyncIterableIterator<FileSystemHandle>;
  }

  interface FileSystemWritableFileStream {
    write(data: Blob | ArrayBuffer | ArrayBufferView | string): Promise<void>;
    close(): Promise<void>;
  }
}
import {
  XMarkIcon as X,
  FolderOpenIcon as FolderOpen,
  DocumentIcon as File,
  ArrowUpTrayIcon as Upload,
  ArrowDownTrayIcon as Download,
  PlusIcon as Plus,
  TrashIcon as Trash2,
  ArrowPathIcon as RefreshCw,
  HomeIcon as Home
} from '@heroicons/react/24/outline';
import FileIcon from './FileIcon';

// 默认允许上传的扩展名（作为后备）
const DEFAULT_ALLOWED_EXTENSIONS = new Set([
  'md', 'txt', 'csv', 'yml', 'yaml', 'properties', 'json', 'png', 'svg', 'jpg',
  'sql', 'java', 'js', 'ts', 'jsx', 'tsx', 'css', 'scss', 'sass', 'less',
  'html', 'htm', 'conf', 'cfg'
]);

const DEFAULT_ACCEPT_ATTR = '.md,.txt,.csv,.yml,.yaml,.properties,.json,.png,.svg,.jpg,.sql,.java,.js,.ts,.jsx,.tsx,.css,.scss,.sass,.less,.html,.htm,.conf,.cfg';

// 动态获取的允许扩展名状态
let dynamicAllowedExtensions = DEFAULT_ALLOWED_EXTENSIONS;

const isAllowedUpload = (fileName: string): boolean => {
  const parts = fileName.split('.');
  const ext = parts.length > 1 ? parts.pop()!.toLowerCase() : '';
  return !!ext && dynamicAllowedExtensions.has(ext);
};

interface FileItem {
  name: string;
  type: 'file' | 'directory';
  path: string;
  size?: number;
  modified?: string;
  mime_type?: string;
}

interface DirectoryData {
  path: string;
  items: FileItem[];
  parent?: string | null;
}

interface FileManagerProps {
  isOpen: boolean;
  onClose: () => void;
  repoUrl: string;
  branch: string;
  userCode: string;
}

interface ContextMenuProps {
  x: number;
  y: number;
  items: Array<{
    label: string;
    icon: React.ReactNode;
    onClick: () => void;
    disabled?: boolean;
  }>;
  onClose: () => void;
}

const ContextMenu: React.FC<ContextMenuProps> = ({ x, y, items, onClose }) => {
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  return (
    <div
      ref={menuRef}
      className="fixed z-50 bg-white/95 backdrop-blur-sm border border-gray-200/50 rounded-lg shadow-xl py-1 min-w-[160px]"
      style={{ left: x, top: y }}
    >
      {items.map((item, index) => (
        <button
          key={index}
          onClick={() => {
            item.onClick();
            onClose();
          }}
          disabled={item.disabled}
          className={`w-full px-3 py-2 text-left text-sm flex items-center gap-2 transition-colors ${
            item.disabled
              ? 'text-gray-400 cursor-not-allowed'
              : 'text-gray-700 hover:bg-blue-50 hover:text-blue-700'
          }`}
        >
          {item.icon}
          {item.label}
        </button>
      ))}
    </div>
  );
};

interface FilePanelProps {
  title: string;
  currentPath: string;
  directoryData: DirectoryData | null;
  isLoading: boolean;
  onNavigate: (path: string) => void;
  onRefresh: () => void;
  onContextMenu: (event: React.MouseEvent, item: FileItem | null) => void;
  // 创建文件夹相关 props
  isCreatingFolder?: boolean;
  newFolderName?: string;
  onNewFolderNameChange?: (name: string) => void;
  onConfirmCreateFolder?: () => void;
  onCancelCreateFolder?: () => void;
  newFolderInputRef?: React.RefObject<HTMLInputElement | null>;
  onReselectDirectory?: () => void; // 本地面板：重新选择目录
  // 多选相关 props
  selectedItems?: Set<string>;
  onItemSelect?: (item: FileItem, isSelected: boolean, isMultiSelect: boolean, isShiftSelect: boolean) => void;
  onSelectAll?: () => void;
  onClearSelection?: () => void;
  isRemote?: boolean; // 是否为远程面板
  fileListRef?: React.RefObject<HTMLDivElement | null>; // 文件列表容器的引用
  canViewGlobalDocs?: boolean; // 是否可查看全局文档
  messages?: {
    components?: {
      fileManager?: {
        [key: string]: string;
      };
    };
  }; // 国际化消息
}

const FilePanel: React.FC<FilePanelProps> = ({
  title,
  currentPath,
  directoryData,
  isLoading,
  onNavigate,
  onRefresh,
  onContextMenu,
  isCreatingFolder = false,
  newFolderName = '',
  onNewFolderNameChange,
  onConfirmCreateFolder,
  onCancelCreateFolder,
  newFolderInputRef,
  onReselectDirectory,
  selectedItems = new Set(),
  onItemSelect,
  onSelectAll,
  onClearSelection,
  isRemote = false,  
  fileListRef,
  canViewGlobalDocs = false,
  messages,
}) => {
  const getFileIcon = (item: FileItem) => {
    return <FileIcon fileName={item.name} isDirectory={item.type === 'directory'} className="w-4 h-4" />;
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '';
    if (bytes < 1024) return `${bytes}B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)}MB`;
  };

  const formatDate = (dateStr?: string) => {
    if (!dateStr) return '';
    try {
      return new Date(dateStr).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return '';
    }
  };

  const renderBreadcrumb = () => {
    if (!currentPath) {
      return (
        <div className="flex items-center gap-1 text-sm text-gray-600">
          <Home className="w-4 h-4" />
          <span>{messages?.components?.fileManager?.rootDirectory || 'Root Directory'}</span>
        </div>
      );
    }

    const parts = currentPath.split('/').filter(Boolean);

    return (
      <div className="flex items-center gap-1 text-sm text-gray-600">
        <button
          onClick={() => onNavigate('')}
          className="flex items-center gap-1 hover:text-blue-600 transition-colors"
        >
          <Home className="w-4 h-4" />
        </button>
        {parts.map((part, index) => {
          const pathUpToThis = parts.slice(0, index + 1).join('/') + '/';
          const isLast = index === parts.length - 1;

          return (
            <React.Fragment key={index}>
              <span className="text-gray-400">/</span>
              {isLast ? (
                <span className="font-medium text-gray-800">{part}</span>
              ) : (
                <button
                  onClick={() => onNavigate(pathUpToThis)}
                  className="hover:text-blue-600 transition-colors"
                >
                  {part}
                </button>
              )}
            </React.Fragment>
          );
        })}
      </div>
    );
  };

  return (
    <div className="flex-1 flex flex-col bg-white/60 backdrop-blur-sm border border-gray-200/50 rounded-lg overflow-hidden">
      {/* 标题栏 */}
      <div className="px-4 py-3 border-b border-gray-200/50 bg-gray-100/50">
        <div className="flex items-center justify-between gap-2">
          <h3 className="font-medium text-gray-800">{title}</h3>
          <div className="flex items-center gap-2">
            {onReselectDirectory && (
              <button
                onClick={onReselectDirectory}
                className="p-1 hover:bg-gray-200/50 rounded transition-colors"
                title={messages?.components?.fileManager?.reselectDirectory || 'Reselect Local Directory'}
                aria-label={messages?.components?.fileManager?.reselectDirectory || 'Reselect Local Directory'}
              >
                <FolderOpen className="w-4 h-4 text-gray-700" />
              </button>
            )}
          <button
            onClick={onRefresh}
            disabled={isLoading}
            className="p-1 hover:bg-gray-200/50 rounded transition-colors"
              title={messages?.components?.fileManager?.refresh || 'Refresh'}
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
          </div>
        </div>
      </div>

      {/* 导航栏 */}
      <div className="px-4 py-2 border-b border-gray-200/30 bg-gray-50/30">
        <div className="flex items-center justify-between">
          {renderBreadcrumb()}
          {directoryData?.parent !== undefined && (
            <button
              onClick={() => onNavigate(directoryData.parent || '')}
              className="text-xs text-blue-600 hover:text-blue-800 transition-colors"
            >
              {messages?.components?.fileManager?.goUp || 'Go Up'}
            </button>
          )}
        </div>
      </div>

      {/* Column Headers */}
      <div className="px-4 py-2 border-b border-gray-200/50 flex text-xs font-medium text-gray-500 bg-gray-50/30">
        <div className="flex-1 min-w-0">{messages?.components?.fileManager?.name || 'Name'}</div>
        <div className="w-24 text-right">{messages?.components?.fileManager?.size || 'Size'}</div>
        <div className="w-40 text-right">{messages?.components?.fileManager?.modifiedTime || 'Modified Time'}</div>
      </div>

      {/* 文件列表 */}
      <div
        ref={fileListRef}
        className="flex-1 overflow-auto focus:outline-none"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.ctrlKey || e.metaKey) {
            if (e.key === 'a') {
              e.preventDefault();
              onSelectAll?.();
            }
          } else if (e.key === 'Escape') {
            onClearSelection?.();
          }
        }}
        onContextMenu={(e) => {
          e.preventDefault();
          onContextMenu(e, null);
        }}
        onMouseDown={(e) => {
          // 当鼠标按下时，确保容器获得焦点以接收键盘事件
          if (e.target === e.currentTarget) {
            e.currentTarget.focus();
          }
        }}
      >
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="flex items-center gap-2 text-gray-500">
              <RefreshCw className="w-4 h-4 animate-spin" />
              {messages?.components?.fileManager?.loading || 'Loading...'}
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {/* 创建文件夹输入框 - 始终显示在顶部 */}
            {isCreatingFolder && (
              <div className="px-4 py-2 bg-blue-50/30 border-l-4 border-blue-500 flex items-center gap-3">
                <FolderOpen className="w-4 h-4 text-blue-500" />
                <input
                  ref={newFolderInputRef}
                  type="text"
                  value={newFolderName}
                  onChange={(e) => onNewFolderNameChange?.(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      onConfirmCreateFolder?.();
                    } else if (e.key === 'Escape') {
                      onCancelCreateFolder?.();
                    }
                  }}
                  placeholder={messages?.components?.fileManager?.folderNamePlaceholder || 'Enter folder name...'}
                  className="flex-1 px-2 py-1 text-sm border border-blue-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white"
                />
                <button
                  onClick={onConfirmCreateFolder}
                  className="p-1 text-green-600 hover:text-green-800 hover:bg-green-100 rounded transition-colors"
                  title={messages?.components?.fileManager?.confirmCreate || 'Confirm Create'}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                  </svg>
                </button>
                <button
                  onClick={onCancelCreateFolder}
                  className="p-1 text-red-600 hover:text-red-800 hover:bg-red-100 rounded transition-colors"
                  title={messages?.components?.fileManager?.cancelCreate || 'Cancel Create'}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}

            {/* 文件列表或空目录提示 */}
            {directoryData?.items.length === 0 ? (
              <div className="flex items-center justify-center h-32 text-gray-500">
                {messages?.components?.fileManager?.emptyDirectory || 'Empty Directory'}
              </div>
            ) : (
              directoryData?.items
                .filter(item => {
                  // 如果是远程目录且用户没有全局文档权限，过滤掉 g-doc
                  if (isRemote && item.name === 'g-doc' && !canViewGlobalDocs) {
                    return false;
                  }
                  return true;
                })
                .map((item) => {
                const isSelected = selectedItems.has(item.path);
                return (
                <div
                  key={item.path}
                    className={`px-4 py-2 cursor-pointer transition-colors group flex items-center text-sm ${
                      isSelected 
                        ? 'bg-blue-100 border-l-4 border-blue-500' 
                        : 'hover:bg-blue-50/50'
                    }`}
                    onClick={(e) => {
                      if (onItemSelect) {
                        const isMultiSelect = e.ctrlKey || e.metaKey;
                        const isShiftSelect = e.shiftKey;
                        onItemSelect(item, !isSelected, isMultiSelect, isShiftSelect);
                      } else if (item.type === 'directory') {
                      onNavigate(item.path);
                    }
                  }}
                  onDoubleClick={() => {
                    if (item.type === 'directory') {
                      onNavigate(item.path);
                    }
                  }}
                  onContextMenu={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onContextMenu(e, item);
                  }}
                >
                  <div className="flex-1 flex items-center gap-3 min-w-0">
                    {getFileIcon(item)}
                    <span className="font-medium text-gray-800 truncate">{item.name}</span>
                  </div>
                  <div className="w-24 text-right text-gray-500">
                    {item.type === 'file' ? formatFileSize(item.size) : ''}
                  </div>
                  <div className="w-40 text-right text-gray-500">
                    {formatDate(item.modified)}
                  </div>
                </div>
                );
              })
            )}
          </div>
        )}
      </div>
    </div>
  );
};

const FileManager: React.FC<FileManagerProps> = ({
  isOpen,
  onClose,
  repoUrl,
  branch,
  userCode
}) => {
  const { messages } = useLanguage();
  const { addToast } = useToast();
  const [localPath, setLocalPath] = useState('');
  const [remotePath, setRemotePath] = useState('');
  const [localData, setLocalData] = useState<DirectoryData | null>(null);
  const [remoteData, setRemoteData] = useState<DirectoryData | null>(null);
  const [isLocalLoading, setIsLocalLoading] = useState(false);
  const [isRemoteLoading, setIsRemoteLoading] = useState(false);
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    item: FileItem | null;
    isRemote: boolean;
  } | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [localDirHandle, setLocalDirHandle] = useState<FileSystemDirectoryHandle | null>(null);

  // 创建文件夹状态
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const newFolderInputRef = useRef<HTMLInputElement>(null);

  // 传输任务进度
  type TransferTask = {
    id: string;
    kind: 'upload' | 'download';
    name: string;
    path: string;
    status: 'queued' | 'processing' | 'completed' | 'failed';
    message?: string;
  };
  const [tasks, setTasks] = useState<TransferTask[]>([]);
  const [isProgressOpen, setIsProgressOpen] = useState(false);
  const progressPanelRef = useRef<HTMLDivElement>(null);
  const progressToggleRef = useRef<HTMLButtonElement>(null);

  // 多选状态
  const [selectedLocalItems, setSelectedLocalItems] = useState<Set<string>>(new Set());
  const [selectedRemoteItems, setSelectedRemoteItems] = useState<Set<string>>(new Set());
  const [lastSelectedIndex, setLastSelectedIndex] = useState<number>(-1);

  // 允许的文件扩展名状态
  const [allowedExtensions, setAllowedExtensions] = useState<Set<string>>(DEFAULT_ALLOWED_EXTENSIONS);
  const [acceptAttr, setAcceptAttr] = useState<string>(DEFAULT_ACCEPT_ATTR);
  // i-doc/o-doc 上传/新建/删除权限（页面级缓存）
  const [docUploadAllowed, setDocUploadAllowed] = useState<boolean | null>(null);
  // g-doc 查看权限（页面级缓存）
  const [canViewGlobalDocs, setCanViewGlobalDocs] = useState<boolean>(false);

  // 远程根目录判定
  const isRemoteRoot = remotePath === '' || remotePath === '/';

  const addTask = useCallback((task: TransferTask) => {
    setTasks(prev => [task, ...prev].slice(0, 200));
  }, []);

  const updateTask = useCallback((id: string, updates: Partial<TransferTask>) => {
    setTasks(prev => prev.map(t => (t.id === id ? { ...t, ...updates } : t)));
  }, []);

  // 获取允许的文件扩展名
  const fetchAllowedExtensions = useCallback(async () => {
    try {
      const response = await authFetch('/api/file/allowed-extensions');
      if (response && response.ok) {
        const result = await response.json();
        if (result.success) {
          const extensions = new Set<string>(result.data.allowed_extensions);
          setAllowedExtensions(extensions);
          setAcceptAttr(result.data.accept_attr);
          // 更新全局变量
          dynamicAllowedExtensions = extensions;
        }
      }
    } catch (error) {
      console.error('获取允许的文件扩展名失败:', error);
      // 使用默认值
    }
  }, []);

  // 查询并缓存是否可在 i-doc/o-doc 下上传/新建/删除
  const fetchDocUploadPermission = useCallback(async () => {
    try {
      const params = new URLSearchParams({ repo_url: repoUrl, branch });
      const res = await authFetch(`/api/file/doc-upload-permission?${params}`);
      if (res && res.ok) {
        const j = await res.json();
        setDocUploadAllowed(!!j?.data?.allowed);
      } else {
        setDocUploadAllowed(false);
      }
    } catch {
      setDocUploadAllowed(false);
    }
  }, [repoUrl, branch]);

  // 查询并缓存是否可查看全局文档目录
  const fetchGlobalDocsPermission = useCallback(async () => {
    try {
      const res = await authFetch('/api/file/doc-upload-global-docs-permission');
      if (res && res.ok) {
        const j = await res.json();
        setCanViewGlobalDocs(!!j?.data?.canView);
      } else {
        setCanViewGlobalDocs(false);
      }
    } catch {
      setCanViewGlobalDocs(false);
    }
  }, []);

  // 多选处理函数
  const handleItemSelect = useCallback((item: FileItem, isSelected: boolean, isMultiSelect: boolean, isRemote: boolean, isShiftSelect: boolean = false) => {
    const setSelectedItems = isRemote ? setSelectedRemoteItems : setSelectedLocalItems;
    // const selectedItems = isRemote ? selectedRemoteItems : selectedLocalItems; // 暂时注释掉未使用的变量
    const items = isRemote ? remoteData?.items : localData?.items;

    if (!items) return;

    const currentIndex = items.findIndex(i => i.path === item.path);

    if (isMultiSelect) {
      // Ctrl/Cmd 多选 - 切换当前项的选择状态
      setSelectedItems(prev => {
        const newSet = new Set(prev);
        if (isSelected) {
          newSet.add(item.path);
        } else {
          newSet.delete(item.path);
        }
        return newSet;
      });
    } else if (isShiftSelect && lastSelectedIndex >= 0) {
      // Shift 范围选择
      const startIndex = Math.min(lastSelectedIndex, currentIndex);
      const endIndex = Math.max(lastSelectedIndex, currentIndex);
      setSelectedItems(prev => {
        const newSet = new Set(prev);
        for (let i = startIndex; i <= endIndex; i++) {
          if (items[i]) {
            newSet.add(items[i].path);
          }
        }
        return newSet;
      });
    } else {
      // 单选 - 清空其他选择，只选择当前项
      setSelectedItems(() => {
        const newSet = new Set<string>();
        if (isSelected) {
          newSet.add(item.path);
        }
        return newSet;
      });
    }

    setLastSelectedIndex(currentIndex);
  }, [remoteData, localData, lastSelectedIndex]);

  const handleSelectAll = useCallback((isRemote: boolean) => {
    const items = isRemote ? remoteData?.items : localData?.items;
    if (!items) return;

    const setSelectedItems = isRemote ? setSelectedRemoteItems : setSelectedLocalItems;
    setSelectedItems(new Set(items.map(item => item.path)));
  }, [remoteData, localData]);

  const handleClearSelection = useCallback((isRemote: boolean) => {
    const setSelectedItems = isRemote ? setSelectedRemoteItems : setSelectedLocalItems;
    setSelectedItems(new Set());
    setLastSelectedIndex(-1);
  }, []);


  // 点击外部时关闭处理进度
  useEffect(() => {
    if (!isProgressOpen) return;
    const handleClickOutside = (e: MouseEvent) => {
      const panel = progressPanelRef.current;
      const toggle = progressToggleRef.current;
      const target = e.target as Node;
      if (panel && !panel.contains(target) && toggle && !toggle.contains(target)) {
        setIsProgressOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside, true);
    return () => document.removeEventListener('mousedown', handleClickOutside, true);
  }, [isProgressOpen]);

  // 开始创建文件夹
  const startCreatingFolder = useCallback(() => {
    setIsCreatingFolder(true);
    setNewFolderName('');
    // 聚焦到输入框
    setTimeout(() => {
      newFolderInputRef.current?.focus();
    }, 100);
  }, []);

  // 取消创建文件夹
  const cancelCreatingFolder = useCallback(() => {
    setIsCreatingFolder(false);
    setNewFolderName('');
  }, []);

  // 当远程目录路径变化时，自动取消创建文件夹占位
  useEffect(() => {
    if (isCreatingFolder) {
      setIsCreatingFolder(false);
      setNewFolderName('');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [remotePath]);

  // 当目录变化时清空选择
  useEffect(() => {
    setSelectedLocalItems(new Set());
    setSelectedRemoteItems(new Set());
    setLastSelectedIndex(-1);
  }, [localPath, remotePath]);

  // 文件列表容器的引用，用于聚焦
  const localFileListRef = useRef<HTMLDivElement>(null);
  const remoteFileListRef = useRef<HTMLDivElement>(null);


  // API调用函数
  const fetchRemoteDirectory = useCallback(async (path: string, options?: { showLoading?: boolean }) => {
    if (!isOpen) return;
    const showLoading = options?.showLoading !== false;
    if (showLoading) setIsRemoteLoading(true);
    try {
      const params = new URLSearchParams({
        repo_url: repoUrl,
        branch,
        user_code: userCode,
        path
      });

      const response = await authFetch(`/api/file/directory?${params}`);
      if (response && response.ok) {
        const result = await response.json();
        if (result.success) {
          setRemoteData(result.data);
        } else {
          console.error('获取远程目录失败:', result);
          // 显示空目录，不再硬编码固定目录
          setRemoteData({ path, items: [], parent: null });
        }
      } else {
        console.error('API请求失败:', response?.status);
        setRemoteData({ path, items: [], parent: null });
      }
    } catch (error) {
      console.error('获取远程目录失败:', error);
      setRemoteData({ path, items: [], parent: null });
    } finally {
      if (showLoading) setIsRemoteLoading(false);
    }
  }, [isOpen, repoUrl, branch, userCode]);

  // Read real local file system
  const fetchLocalDirectory = useCallback(async (rootHandle: FileSystemDirectoryHandle, relativePath: string, options?: { showLoading?: boolean }) => {
    const showLoading = options?.showLoading !== false;
    if (showLoading) setIsLocalLoading(true);
    setLocalData(null);

    try {
        let currentHandle = rootHandle;
        const parts = relativePath.split('/').filter(Boolean);
        for (const part of parts) {
            currentHandle = await currentHandle.getDirectoryHandle(part);
        }

        const items: FileItem[] = [];
        for await (const entry of currentHandle.values()) {
            const isDirectory = entry.kind === 'directory';
            let file;
            if (!isDirectory && entry.kind === 'file') {
                try {
                    file = await (entry as FileSystemFileHandle).getFile();
                } catch (e) {
                    console.warn(`Could not get file for ${entry.name}`, e);
                    continue; // Skip files we can't access
                }
            }

            items.push({
                name: entry.name,
                type: isDirectory ? 'directory' : 'file',
                path: relativePath ? `${relativePath}${entry.name}${isDirectory ? '/' : ''}` : `${entry.name}${isDirectory ? '/' : ''}`,
                size: file?.size,
                modified: file ? new Date(file.lastModified).toISOString() : undefined,
            });
        }

        let parent: string | null = null;
        if (parts.length > 1) {
            parent = parts.slice(0, -1).join('/') + '/';
        } else if (parts.length === 1) {
            parent = '';
        }

        setLocalData({
            path: relativePath,
            items: items.sort((a, b) => {
                if (a.type === 'directory' && b.type === 'file') return -1;
                if (a.type === 'file' && b.type === 'directory') return 1;
                return a.name.localeCompare(b.name);
            }),
            parent,
        });
    } catch (error) {
        console.error('Error reading local directory:', error);
        setLocalData({ path: relativePath, items: [], parent: null });
    } finally {
        if (showLoading) setIsLocalLoading(false);
    }
  }, []);

  const handleSelectLocalDirectory = useCallback(async () => {
    try {
      if (!window.showDirectoryPicker) {
        addToast({
          type: 'error',
          title: messages?.components?.fileManager?.fsApiNotSupportedTitle || 'Unsupported Browser',
          message: messages?.components?.fileManager?.fsApiNotSupportedMessage || 'Your browser does not support the File System Access API. Please use the latest version of Chrome, Edge, or Opera.'
        });
        return;
      }
      const handle = await window.showDirectoryPicker();
      setLocalDirHandle(handle);
      setLocalPath('');
      fetchLocalDirectory(handle, '');
    } catch (err) {
      console.info('Directory selection cancelled:', err);
    }
  }, [fetchLocalDirectory, addToast, messages?.components?.fileManager?.fsApiNotSupportedMessage, messages?.components?.fileManager?.fsApiNotSupportedTitle]);

  // 初始化加载（只在文件管理器打开时执行一次）
  useEffect(() => {
    if (isOpen) {
      // 获取允许的文件扩展名（只需要获取一次）
      fetchAllowedExtensions();
      // 查询一次权限并缓存
      fetchDocUploadPermission();
      // 查询全局文档权限
      fetchGlobalDocsPermission();
      // 按当前 remotePath 刷新远程目录
      fetchRemoteDirectory(remotePath);
      // 如果本地目录已选择，静默刷新当前本地路径；否则保持未选择状态
      if (localDirHandle) {
        fetchLocalDirectory(localDirHandle, localPath, { showLoading: false });
      }
    }
  }, [isOpen, fetchRemoteDirectory, localDirHandle, fetchLocalDirectory, fetchAllowedExtensions]);
  // 将权限查询加入依赖



  // 当本地路径变化时，只刷新本地目录，不影响远程目录
  useEffect(() => {
    if (isOpen && localDirHandle) {
      fetchLocalDirectory(localDirHandle, localPath, { showLoading: false });
    }
  }, [localPath, localDirHandle, fetchLocalDirectory, isOpen]);

  // 文件上传
  const uploadFile = useCallback(async (file: File, targetPath: string, refresh: boolean = true) => {
    // 远程根目录下禁止上传
    if (targetPath === '' || targetPath === '/') {
      addToast({
        type: 'error',
        title: messages?.components?.fileManager?.uploadNotAllowed || 'Upload Not Allowed',
        message: messages?.components?.fileManager?.uploadNotAllowedAtRoot || 'Uploading at root directory is forbidden.'
      });
      return false;
    }
    // i-doc/o-doc 权限校验（前端兜底）
    const norm = (targetPath || '').replace(/^\/+/, '');
    const isInProjectDocs = norm.startsWith('i-doc') || norm.startsWith('o-doc');
    if (isInProjectDocs && docUploadAllowed === false) {
      addToast({ type: 'error', title: messages?.components?.fileManager?.uploadNotAllowed || 'Upload Not Allowed', message: '当前用户无权在 i-doc/o-doc 下上传' });
      return false;
    }
    const taskId = `upload-${Date.now()}-${file.name}-${Math.random().toString(36).slice(2,8)}`;
    addTask({ id: taskId, kind: 'upload', name: file.name, path: targetPath || '/', status: 'processing' });
    try {
      if (!isAllowedUpload(file.name)) {
        updateTask(taskId, { status: 'failed', message: messages?.components?.fileManager?.fileTypeNotAllowed || 'File Type Not Allowed' });
        const allowedTypesText = Array.from(allowedExtensions).join('、');
        addToast({
          type: 'error',
          title: messages?.components?.fileManager?.fileTypeNotAllowed || 'File Type Not Allowed',
          message: `${file.name} ${messages?.components?.fileManager?.fileTypeNotAllowedMessage || 'Only {types} files are supported'}. ${(messages?.components?.fileManager?.fileTypeNotAllowedMessage || 'Only {types} files are supported').replace('{types}', allowedTypesText)}`
        });
        return false;
      }
      const formData = new FormData();
      formData.append('file', file);

      // 如果是根目录，直接拦截（上方已经拦截，这里兜底）
      const uploadPath = targetPath === '' ? '/' : targetPath;

      const params = new URLSearchParams({
        repo_url: repoUrl,
        branch,
        user_code: userCode,
        path: uploadPath
      });

      const response = await authFetch(`/api/file/upload?${params}`, {
        method: 'POST',
        body: formData
      });

      if (response && response.ok) {
        const result = await response.json();
        if (result.success) {
          // 可选刷新远程目录
          if (refresh) {
            fetchRemoteDirectory(remotePath, { showLoading: false });
          }
          updateTask(taskId, { status: 'completed' });
          return true;
        }
      }
      updateTask(taskId, { status: 'failed', message: messages?.components?.fileManager?.uploadFailed || 'Upload Failed' });
      addToast({
        type: 'error',
        title: messages?.components?.fileManager?.uploadFailed || 'Upload Failed',
        message: `${file.name} ${messages?.components?.fileManager?.uploadFailed || 'Upload Failed'}`
      });
      return false;
    } catch (error) {
      console.error('文件上传失败:', error);
      updateTask(taskId, { status: 'failed', message: messages?.components?.fileManager?.networkError || 'Network or Service Error' });
      addToast({
        type: 'error',
        title: messages?.components?.fileManager?.uploadFailed || 'Upload Failed',
        message: `${file.name} ${messages?.components?.fileManager?.uploadFailed || 'Upload Failed'}. ${messages?.components?.fileManager?.networkError || 'Network or Service Error'}`
      });
      return false;
    }
  }, [repoUrl, branch, userCode, remotePath, fetchRemoteDirectory, addTask, updateTask, addToast, allowedExtensions, messages?.components?.fileManager]);

  const handleUploadLocalFile = useCallback(async (item: FileItem): Promise<boolean> => {
    if (!localDirHandle) return false;
    try {
        let dirHandle = localDirHandle;
        const pathParts = item.path.split('/');
        const fileName = pathParts.pop();

        for (const part of pathParts) {
            if (part) {
                dirHandle = await dirHandle.getDirectoryHandle(part);
            }
        }

        if (!fileName) return false;

        // 先检查文件名
        if (!isAllowedUpload(fileName)) {
          const allowedTypesText = Array.from(allowedExtensions).join('、');
          addToast({
            type: 'error',
            title: messages?.components?.fileManager?.fileTypeNotAllowed || 'File Type Not Allowed',
            message: `${fileName} ${(messages?.components?.fileManager?.fileUploadFailedWithTypes || 'Upload Failed. Only {types} files are supported').replace('{types}', allowedTypesText)}`
          });
          return false;
        }

        const fileHandle = await dirHandle.getFileHandle(fileName);
        const file = await fileHandle.getFile();

        // 再次检查文件对象（避免重复toast，只在真正需要时显示）
        if (!isAllowedUpload(file.name)) {
          // 如果文件名和文件对象名不同，才显示toast
          if (fileName !== file.name) {
            const allowedTypesText = Array.from(allowedExtensions).join('、');
          addToast({
            type: 'error',
              title: messages?.components?.fileManager?.fileTypeNotAllowed || 'File Type Not Allowed',
              message: `${file.name} ${(messages?.components?.fileManager?.fileUploadFailedWithTypes || 'Upload Failed. Only {types} files are supported').replace('{types}', allowedTypesText)}`
          });
          }
          return false;
        }

        const success = await uploadFile(file, remotePath);
        return success;

    } catch (error) {
        console.error('Error handling local file upload:', error);
        return false;
    }
  }, [localDirHandle, remotePath, uploadFile, addToast, allowedExtensions, messages?.components?.fileManager]);



  // 文件下载
  const downloadFile = useCallback(async (filePath: string, fileName: string) => {
    const taskId = `download-${Date.now()}-${fileName}-${Math.random().toString(36).slice(2,8)}`;
    addTask({ id: taskId, kind: 'download', name: fileName, path: filePath, status: 'processing' });
    try {
      const requestBody = {
        repo_url: repoUrl,
        branch,
        user_code: userCode,
        file_path: filePath
      };

      const response = await authFetch(`/api/file/download`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (response && response.ok) {
        const blob = await response.blob();

        // 如果有本地目录句柄，尝试下载到本地目录
        if (localDirHandle) {
          try {
            // 获取当前本地目录的句柄
            let currentDirHandle = localDirHandle;
            const pathParts = localPath.split('/').filter(Boolean);

            // 导航到当前路径
            for (const part of pathParts) {
              currentDirHandle = await currentDirHandle.getDirectoryHandle(part);
            }

            // 在当前目录创建文件
            const fileHandle = await currentDirHandle.getFileHandle(fileName, { create: true });
            const writable = await fileHandle.createWritable();
            await writable.write(blob);
            await writable.close();

            // 刷新本地目录显示
            fetchLocalDirectory(localDirHandle, localPath);
            updateTask(taskId, { status: 'completed' });
            return true;
          } catch (fsError) {
            console.warn('保存到本地目录失败，使用浏览器下载:', fsError);
            // 如果文件系统API失败，回退到传统下载方式
          }
        }

        // 回退到传统下载方式
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        updateTask(taskId, { status: 'completed' });
        return true;
      }
      updateTask(taskId, { status: 'failed', message: messages?.components?.fileManager?.downloadFailed || 'Download Failed' });
      return false;
    } catch (error) {
      console.error('文件下载失败:', error);
      updateTask(taskId, { status: 'failed', message: messages?.components?.fileManager?.networkError || 'Network or Service Error' });
      return false;
    }
  }, [repoUrl, branch, userCode, localDirHandle, localPath, fetchLocalDirectory, addTask, updateTask, messages?.components?.fileManager?.downloadFailed, messages?.components?.fileManager?.networkError]);


  // 目录下载
  const downloadDirectory = useCallback(async (dirPath: string, dirName: string) => {
    const taskId = `download-dir-${Date.now()}-${dirName}-${Math.random().toString(36).slice(2,8)}`;
    addTask({ id: taskId, kind: 'download', name: dirName + '.zip', path: dirPath, status: 'processing' });
    try {
      const requestBody = {
        repo_url: repoUrl,
        branch,
        user_code: userCode,
        dir_path: dirPath
      };

      const response = await authFetch(`/api/file/download-directory`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (response && response.ok) {
        const blob = await response.blob();

        // 如果有本地目录句柄，尝试下载到本地目录
        if (localDirHandle) {
          try {
            // 获取当前本地目录的句柄
            let currentDirHandle = localDirHandle;
            const pathParts = localPath.split('/').filter(Boolean);

            // 导航到当前路径
            for (const part of pathParts) {
              currentDirHandle = await currentDirHandle.getDirectoryHandle(part);
            }

            // 在当前目录创建zip文件
            const fileHandle = await currentDirHandle.getFileHandle(`${dirName}.zip`, { create: true });
            const writable = await fileHandle.createWritable();
            await writable.write(blob);
            await writable.close();

            // 刷新本地目录显示
            fetchLocalDirectory(localDirHandle, localPath);
            updateTask(taskId, { status: 'completed' });
            return true;
          } catch (fsError) {
            console.warn('保存到本地目录失败，使用浏览器下载:', fsError);
            // 如果文件系统API失败，回退到传统下载方式
          }
        }

        // 回退到传统下载方式
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${dirName}.zip`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        updateTask(taskId, { status: 'completed' });
        return true;
      }
      updateTask(taskId, { status: 'failed', message: messages?.components?.fileManager?.downloadFailed || 'Download Failed' });
      return false;
    } catch (error) {
      console.error('目录下载失败:', error);
      updateTask(taskId, { status: 'failed', message: messages?.components?.fileManager?.networkError || 'Network or Service Error' });
      return false;
    }
  }, [repoUrl, branch, userCode, localDirHandle, localPath, fetchLocalDirectory, addTask, updateTask, messages?.components?.fileManager?.downloadFailed, messages?.components?.fileManager?.networkError]);

  // 批量下载远程文件
  const handleBatchDownloadRemoteFiles = useCallback(async () => {
    if (selectedRemoteItems.size === 0) return;

    const items = remoteData?.items.filter(item => selectedRemoteItems.has(item.path)) || [];
    let successCount = 0;
    let failCount = 0;

    for (const item of items) {
      if (item.type === 'file') {
        const success = await downloadFile(item.path, item.name);
        if (success) {
          successCount++;
        } else {
          failCount++;
        }
      } else if (item.type === 'directory') {
        const success = await downloadDirectory(item.path, item.name);
        if (success) {
          successCount++;
        } else {
          failCount++;
        }
      }
    }

    if (successCount > 0) {
      addToast({
        type: 'success',
        title: messages?.components?.fileManager?.batchDownloadComplete || 'Batch Download Complete',
        message: `${(messages?.components?.fileManager?.successDownloaded || 'Successfully downloaded {count} files').replace('{count}', successCount.toString())}. ${failCount > 0 ? `${(messages?.components?.fileManager?.failedDownloaded || 'Failed {count}').replace('{count}', failCount.toString())}` : ''}`
      });
    } else if (failCount > 0) {
      addToast({
        type: 'error',
        title: messages?.components?.fileManager?.batchDownloadFailed || 'Batch Download Failed',
        message: (messages?.components?.fileManager?.allDownloadFailed || 'All {count} files failed to download').replace('{count}', failCount.toString())
      });
    }

    // 清空选择
    setSelectedRemoteItems(new Set());
  }, [selectedRemoteItems, remoteData, downloadFile, downloadDirectory, addToast, messages?.components?.fileManager?.allDownloadFailed, messages?.components?.fileManager?.batchDownloadComplete, messages?.components?.fileManager?.batchDownloadFailed, messages?.components?.fileManager?.failedDownloaded, messages?.components?.fileManager?.successDownloaded]);

  // 创建目录
  const createDirectory = useCallback(async (path: string, name: string) => {
    // 远程根目录禁止创建
    if (path === '' || path === '/') {
      addToast({
        type: 'error',
        title: messages?.components?.fileManager?.createNotAllowed || 'Create Not Allowed',
        message: messages?.components?.fileManager?.createNotAllowedAtRoot || 'Creating folders at root directory is forbidden.'
      });
      return false;
    }
    try {
      // i-doc/o-doc 权限校验（前端兜底）
      const norm = (path || '').replace(/^\/+/, '');
      const isInProjectDocs = norm.startsWith('i-doc') || norm.startsWith('o-doc');
      if (isInProjectDocs && docUploadAllowed === false) {
        addToast({ type: 'error', title: messages?.components?.fileManager?.createNotAllowed || 'Create Not Allowed', message: '当前用户无权在 i-doc/o-doc 下新建目录' });
        return false;
      }
      // 规范化父路径：根目录为'/'，其他目录以'/'结尾
      let normalizedPath = path;
      if (!normalizedPath || normalizedPath === '') {
        normalizedPath = '/';
      } else if (!normalizedPath.endsWith('/')) {
        normalizedPath = normalizedPath + '/';
      }

      const params = new URLSearchParams({
        repo_url: repoUrl,
        branch,
        user_code: userCode,
        path: normalizedPath,
        name
      });

      const response = await authFetch(`/api/file/create-directory?${params}`, {
        method: 'POST'
      });

      if (response && response.ok) {
        const result = await response.json();
        if (result.success) {
          fetchRemoteDirectory(remotePath);
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error('创建目录失败:', error);
      return false;
    }
  }, [repoUrl, branch, userCode, remotePath, fetchRemoteDirectory]);

  // 确认创建文件夹
  const confirmCreatingFolder = useCallback(async () => {
    if (!newFolderName.trim()) {
      addToast({
        type: 'error',
        title: messages?.components?.fileManager?.folderNameRequired || 'Folder Name Cannot Be Empty',
        message: messages?.components?.fileManager?.enterFolderName || 'Please Enter Folder Name'
      });
      return;
    }

    const success = await createDirectory(remotePath, newFolderName.trim());
    if (success) {
      addToast({
        type: 'success',
        title: messages?.components?.fileManager?.createSuccess || 'Create Success',
        message: messages?.components?.fileManager?.folderCreated || 'Folder Created Successfully'
      });
      setIsCreatingFolder(false);
      setNewFolderName('');
    } else {
      addToast({
        type: 'error',
        title: messages?.components?.fileManager?.createFailed || 'Create Failed',
        message: messages?.components?.fileManager?.folderCreateFailed || 'Folder Creation Failed, Please Retry'
      });
    }
  }, [newFolderName, remotePath, createDirectory, addToast, messages?.components?.fileManager?.createFailed, messages?.components?.fileManager?.createSuccess, messages?.components?.fileManager?.enterFolderName, messages?.components?.fileManager?.folderCreateFailed, messages?.components?.fileManager?.folderCreated, messages?.components?.fileManager?.folderNameRequired]);

  // 递归上传本地目录至远程当前目录下（会在远程创建同名根文件夹）
  const handleUploadLocalDirectory = useCallback(async (item: FileItem): Promise<boolean> => {
    if (!localDirHandle) return false;
    if (item.type !== 'directory') return false;

    try {
      // 获取该本地目录的句柄
      let dirHandle = localDirHandle;
      const pathParts = item.path.split('/').filter(Boolean);
      for (const part of pathParts) {
        dirHandle = await dirHandle.getDirectoryHandle(part);
      }

      const rootFolderName = item.name;

      // 在远程当前目录下创建同名根文件夹
      const created = await createDirectory(remotePath, rootFolderName);
      if (!created) {
        addToast({
          type: 'error',
          title: messages?.components?.fileManager?.uploadFailed || 'Upload Failed',
          message: messages?.components?.fileManager?.createRemoteRootFailed || 'Failed to Create Remote Root Directory'
        });
        return false;
      }

      // 规范化远程根目录路径（作为上传目标父目录）
      const baseRemote = (() => {
        let base = remotePath || '';
        if (base && !base.endsWith('/')) base = base + '/';
        return `${base}${rootFolderName}/`;
      })();

      // 递归遍历并上传（批量期间不触发刷新）
      const uploadDirRecursive = async (currentDir: FileSystemDirectoryHandle, relativePrefix: string): Promise<void> => {
        for await (const entry of currentDir.values()) {
          if (entry.kind === 'directory') {
            const subPrefix = relativePrefix ? `${relativePrefix}${entry.name}/` : `${entry.name}/`;
            // 继续深入
            const subDir = await currentDir.getDirectoryHandle(entry.name);
            await uploadDirRecursive(subDir, subPrefix);
          } else if (entry.kind === 'file') {
            try {
              const fileHandle = await currentDir.getFileHandle(entry.name);
              const file = await fileHandle.getFile();
              if (!isAllowedUpload(file.name)) {
                // 跳过不合规文件
                continue;
              }
              // 计算目标远程目录（父目录）
              const targetRemoteDir = relativePrefix ? `${baseRemote}${relativePrefix}` : baseRemote;
              await uploadFile(file, targetRemoteDir, false);
            } catch (e) {
              console.warn('跳过无法读取的文件: ', entry.name, e);
              continue;
            }
          }
        }
      };

      await uploadDirRecursive(dirHandle, '');

      // 批量完成后刷新远程目录（静默刷新，避免loading闪烁）
      await fetchRemoteDirectory(remotePath, { showLoading: false });

      addToast({
        type: 'success',
        title: messages?.components?.fileManager?.uploadSuccess || 'Upload Success',
        message: `${rootFolderName} ${messages?.components?.fileManager?.folderUploadComplete || 'Folder Upload Complete'}`
      });
      return true;
    } catch (error) {
      console.error('上传本地文件夹失败:', error);
      addToast({
        type: 'error',
        title: messages?.components?.fileManager?.uploadFailedTitle || 'Upload Fail',
        message: messages?.components?.fileManager?.uploadError || 'Error During Upload Process'
      });
      return false;
    }
  }, [localDirHandle, remotePath, createDirectory, uploadFile, fetchRemoteDirectory, addToast, messages?.components?.fileManager]);

  // 批量上传本地文件和文件夹
  const handleBatchUploadLocalItems = useCallback(async () => {
    if (!localDirHandle || selectedLocalItems.size === 0) return;

    const items = localData?.items.filter(item => selectedLocalItems.has(item.path)) || [];
    let successCount = 0;
    let failCount = 0;

    for (const item of items) {
      if (item.type === 'file') {
        const success = await handleUploadLocalFile(item);
        if (success) {
          successCount++;
        } else {
          failCount++;
        }
      } else if (item.type === 'directory') {
        const success = await handleUploadLocalDirectory(item);
        if (success) {
          successCount++;
        } else {
          failCount++;
        }
      }
    }

    if (successCount > 0) {
      addToast({
        type: 'success',
        title: messages?.components?.fileManager?.batchUploadComplete || 'Batch Upload Complete',
        message: `${(messages?.components?.fileManager?.successUploaded || 'Successfully uploaded {count} items').replace('{count}', successCount.toString())}. ${failCount > 0 ? `${(messages?.components?.fileManager?.failedUploaded || 'Failed {count}').replace('{count}', failCount.toString())}` : ''}`
      });
    } else if (failCount > 0) {
      addToast({
        type: 'error',
        title: messages?.components?.fileManager?.batchUploadFailed || 'Batch Upload Failed',
        message: (messages?.components?.fileManager?.allFailed || 'All {count} items failed to upload').replace('{count}', failCount.toString())
      });
    }

    // 清空选择
    setSelectedLocalItems(new Set());
  }, [localDirHandle, selectedLocalItems, localData, handleUploadLocalFile, handleUploadLocalDirectory, addToast, messages?.components?.fileManager?.allFailed, messages?.components?.fileManager?.batchUploadComplete, messages?.components?.fileManager?.batchUploadFailed, messages?.components?.fileManager?.failedUploaded, messages?.components?.fileManager?.successUploaded]);

  // 删除文件/目录
  const deleteItem = useCallback(async (itemPath: string) => {
    // 根目录禁止删除任何项（包括多选）
    if (remotePath === '' || remotePath === '/') {
      addToast({
        type: 'error',
        title: messages?.components?.fileManager?.deleteNotAllowed || 'Delete Not Allowed',
        message: messages?.components?.fileManager?.deleteNotAllowedAtRoot || 'Deleting items at root directory is forbidden.'
      });
      return false;
    }
    // i-doc/o-doc 权限兜底：无权则前端直接拦截
    try {
      const norm = (itemPath || '').replace(/^\/+/, '');
      const isInProjectDocs = norm.startsWith('i-doc') || norm.startsWith('o-doc');
      if (isInProjectDocs && docUploadAllowed === false) {
        addToast({
          type: 'error',
          title: messages?.components?.fileManager?.deleteNotAllowed || 'Delete Not Allowed',
          message: '当前用户无权在 i-doc/o-doc 下删除'
        });
        return false;
      }
    } catch {}
    try {
      const params = new URLSearchParams({
        repo_url: repoUrl,
        branch,
        user_code: userCode,
        item_path: itemPath
      });

      const response = await authFetch(`/api/file/delete?${params}`, {
        method: 'DELETE'
      });

      if (response && response.ok) {
        const result = await response.json();
        if (result.success) {
          fetchRemoteDirectory(remotePath);
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error('删除失败:', error);
      return false;
    }
  }, [repoUrl, branch, userCode, remotePath, fetchRemoteDirectory]);

  // 右键菜单处理
  const handleContextMenu = useCallback((event: React.MouseEvent, item: FileItem | null, isRemote: boolean) => {
    setContextMenu({
      x: event.clientX,
      y: event.clientY,
      item,
      isRemote
    });
  }, []);

  const getContextMenuItems = () => {
    if (!contextMenu) return [];

    const { item, isRemote } = contextMenu;
    const items = [];
    const selectedItems = isRemote ? selectedRemoteItems : selectedLocalItems;
    const hasSelection = selectedItems.size > 0;

    if (isRemote) {
      // 远程文件操作
      if (hasSelection && selectedItems.size > 1) {
        // 多选时显示批量操作菜单
        const selectedFiles = remoteData?.items.filter(i => selectedItems.has(i.path) && i.type === 'file') || [];
        const selectedDirs = remoteData?.items.filter(i => selectedItems.has(i.path) && i.type === 'directory') || [];

        if (selectedFiles.length > 0 || selectedDirs.length > 0) {
          const typeText = selectedFiles.length > 0 && selectedDirs.length > 0
            ? `${selectedFiles.length}${messages?.components?.fileManager?.file || 'files'}${selectedDirs.length}${messages?.components?.fileManager?.directory || 'directories'}`
            : selectedFiles.length > 0
            ? `${selectedFiles.length}${messages?.components?.fileManager?.file || 'files'}`
            : `${selectedDirs.length}${messages?.components?.fileManager?.directory || 'directories'}`;

          items.push({
            label: `${messages?.components?.fileManager?.batchDownload || 'Batch Download'} (${typeText})`,
            icon: <Download className="w-4 h-4" />,
            onClick: handleBatchDownloadRemoteFiles
          });
        }

        const normForBatch = (remotePath || '').replace(/^\/+/, '');
        const isInProjectDocsForBatch = normForBatch.startsWith('i-doc') || normForBatch.startsWith('o-doc');
        const denyDocOpsBatch = isInProjectDocsForBatch && docUploadAllowed === false;
        items.push({
          label: messages?.components?.fileManager?.batchDelete || 'Batch Delete',
          icon: <Trash2 className="w-4 h-4" />,
          onClick: async () => {
            let successCount = 0;
            let failCount = 0;

            for (const dir of selectedDirs) {
              const success = await deleteItem(dir.path);
              if (success) {
                successCount++;
              } else {
                failCount++;
              }
            }

            // 对于文件，直接调用deleteItem
            for (const file of selectedFiles) {
              const success = await deleteItem(file.path);
              if (success) {
                successCount++;
              } else {
                failCount++;
              }
            }

            if (successCount > 0) {
              addToast({
                type: 'success',
                title: messages?.components?.fileManager?.batchDeleteComplete || 'Batch Delete Complete',
                message: `${(messages?.components?.fileManager?.successDeleted || 'Successfully deleted {count} items').replace('{count}', successCount.toString())}. ${failCount > 0 ? `${(messages?.components?.fileManager?.failedDeleted || 'Failed {count}').replace('{count}', failCount.toString())}` : ''}`
              });
              fetchRemoteDirectory(remotePath);
            } else if (failCount > 0) {
              addToast({
                type: 'error',
                title: messages?.components?.fileManager?.batchDeleteFailed || 'Batch Delete Failed',
                message: (messages?.components?.fileManager?.allDeleteFailed || 'All {count} items failed to delete').replace('{count}', failCount.toString())
              });
            }
          },
          disabled: isRemoteRoot || denyDocOpsBatch
        });

        items.push({
          label: messages?.components?.fileManager?.cancelSelection || 'Cancel Selection',
          icon: <X className="w-4 h-4" />,
          onClick: () => setSelectedRemoteItems(new Set())
        });
      } else if (item) {
        // 单选时显示单个项目操作菜单
        if (item.type === 'file') {
          items.push({
            label: messages?.components?.fileManager?.downloadFile || 'Download File',
            icon: <Download className="w-4 h-4" />,
            onClick: async () => {
              const success = await downloadFile(item.path, item.name);
              if (success) {
                addToast({
                  type: 'success',
                  title: messages?.components?.fileManager?.downloadSuccess || 'Download Success',
                  message: messages?.components?.fileManager?.fileDownloadSuccess || 'File Downloaded Successfully'
                });
              } else {
                addToast({
                  type: 'error',
                  title: messages?.components?.fileManager?.downloadFailed || 'Download Failed',
                  message: messages?.components?.fileManager?.fileDownloadFailed || 'File Download Failed, Please Retry'
                });
              }
            }
          });
        }
        if (item.type === 'directory') {
          items.push({
            label: messages?.components?.fileManager?.downloadDirectory || 'Download Directory',
            icon: <Download className="w-4 h-4" />,
            onClick: async () => {
              const success = await downloadDirectory(item.path, item.name);
              if (success) {
                addToast({
                  type: 'success',
                  title: messages?.components?.fileManager?.downloadSuccess || 'Download Success',
                  message: messages?.components?.fileManager?.directoryDownloadSuccess || 'Directory Downloaded Successfully'
                });
              } else {
                addToast({
                  type: 'error',
                  title: messages?.components?.fileManager?.downloadFailed || 'Download Failed',
                  message: messages?.components?.fileManager?.directoryDownloadFailed || 'Directory Download Failed, Please Retry'
                });
              }
            }
          });
        }
        const normForSingle = (remotePath || '').replace(/^\/+/, '');
        const isInProjectDocsForSingle = normForSingle.startsWith('i-doc') || normForSingle.startsWith('o-doc');
        const denyDocOps = isInProjectDocsForSingle && docUploadAllowed === false;
        items.push({
          label: messages?.components?.fileManager?.delete || 'Delete',
          icon: <Trash2 className="w-4 h-4" />,
          onClick: async () => {
            const success = await deleteItem(item.path);
            if (success) {
              addToast({
                type: 'success',
                title: messages?.components?.fileManager?.deleteSuccess || 'Delete Success',
                message: messages?.components?.fileManager?.itemDeleted || 'Item Deleted'
              });
            } else {
              addToast({
                type: 'error',
                title: messages?.components?.fileManager?.deleteFailed || 'Delete Failed',
                message: messages?.components?.fileManager?.deleteFailedMessage || 'Delete Failed, Please Retry'
              });
            }
          },
          disabled: isRemoteRoot || denyDocOps
        });
      } else {
        // 远程空白区域右键（根目录禁用上传/新建）
        const norm = (remotePath || '').replace(/^\/+/, '');
        const isInProjectDocs = norm.startsWith('i-doc') || norm.startsWith('o-doc');
        const denyDocOps = isInProjectDocs && docUploadAllowed === false;
        items.push({
          label: messages?.components?.fileManager?.uploadFile || 'Upload File',
          icon: <Upload className="w-4 h-4" />,
          onClick: () => fileInputRef.current?.click(),
          disabled: isRemoteRoot || denyDocOps
        });
        items.push({
          label: messages?.components?.fileManager?.createFolder || 'New Folder',
          icon: <Plus className="w-4 h-4" />,
          onClick: () => {
            startCreatingFolder();
          },
          disabled: isRemoteRoot || denyDocOps
        });
      }
    } else {
      // 本地文件操作
      if (hasSelection && selectedItems.size > 1) {
        // 多选时显示批量操作菜单
        const selectedFiles = localData?.items.filter(i => selectedItems.has(i.path) && i.type === 'file') || [];
        const selectedDirs = localData?.items.filter(i => selectedItems.has(i.path) && i.type === 'directory') || [];

        if (selectedFiles.length > 0 || selectedDirs.length > 0) {
          const typeText = selectedFiles.length > 0 && selectedDirs.length > 0
            ? `${selectedFiles.length}${messages?.components?.fileManager?.file || 'files'}${selectedDirs.length}${messages?.components?.fileManager?.directory || 'directories'}`
            : selectedFiles.length > 0
            ? `${selectedFiles.length}${messages?.components?.fileManager?.file || 'files'}`
            : `${selectedDirs.length}${messages?.components?.fileManager?.directory || 'directories'}`;

          items.push({
            label: `${messages?.components?.fileManager?.batchUpload || 'Batch Upload to Sandbox'} (${typeText})`,
            icon: <Upload className="w-4 h-4" />,
            onClick: handleBatchUploadLocalItems
          });
        }

        items.push({
          label: messages?.components?.fileManager?.cancelSelection || 'Cancel Selection',
          icon: <X className="w-4 h-4" />,
          onClick: () => setSelectedLocalItems(new Set())
        });
      } else if (item) {
        // 单选时显示单个项目操作菜单
        if (item.type === 'file') {
          items.push({
            label: messages?.components?.fileManager?.uploadToSandbox || 'Upload to Sandbox',
            icon: <Upload className="w-4 h-4" />,
            onClick: async () => {
              const success = await handleUploadLocalFile(item);
              if (success) {
                addToast({
                  type: 'success',
                  title: messages?.components?.fileManager?.uploadSuccess || 'Upload Success',
                  message: messages?.components?.fileManager?.fileUploadSuccess || 'File Upload Success'
                });
              }
            },
          });
        }
        if (item.type === 'directory') {
          items.push({
            label: messages?.components?.fileManager?.enterDirectory || 'Enter Directory',
            icon: <FolderOpen className="w-4 h-4" />,
            onClick: () => {
              if (localDirHandle) {
                setLocalPath(item.path);
                fetchLocalDirectory(localDirHandle, item.path);
              }
            }
          });
          items.push({
            label: messages?.components?.fileManager?.uploadFolderToSandbox || 'Upload Folder to Sandbox',
            icon: <Upload className="w-4 h-4" />,
            onClick: async () => {
              const success = await handleUploadLocalDirectory(item);
              if (!success) {
                addToast({
                  type: 'error',
                  title: messages?.components?.fileManager?.uploadFailed || 'Upload Failed',
                  message: messages?.components?.fileManager?.folderUploadFailed || 'Folder Upload Failed, Please Retry'
                });
              }
            }
          });
        }
      } else {
        // 本地空白区域右键
        items.push({
          label: messages?.components?.fileManager?.refresh || 'Refresh',
          icon: <RefreshCw className="w-4 h-4" />,
          onClick: () => {
            if (localDirHandle) {
              fetchLocalDirectory(localDirHandle, localPath);
            }
          }
        });
      }
    }

    return items;
  };

  if (!isOpen) return null;

  return (
    <>
      {/* 遮罩层 */}
      <div className="fixed inset-0 bg-black/30 backdrop-blur-sm z-40" onClick={onClose} />

      {/* 文件管理器窗口 - 确保完全居中 */}
      <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
        <div className="w-full h-full max-w-[1200px] max-h-[800px] bg-gray-50/90 backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-200/50 flex flex-col">
        {/* 标题栏 */}
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200/50 bg-gray-100/90 rounded-t-2xl flex-shrink-0">
          <div className="flex items-center gap-3">
            <div className="flex gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            </div>
            <h2 className="text-lg font-semibold text-gray-800">{messages?.components?.fileManager?.title || 'File Manager'}</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-200/50 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* 主要内容区域 */}
        <div className="flex-1 flex gap-4 p-6 min-h-0">
          {/* 本地文件面板 */}
          {!localDirHandle ? (
            <div className="flex-1 flex flex-col items-center justify-center bg-white/60 backdrop-blur-sm border border-gray-200/50 rounded-lg">
                <h3 className="text-lg font-medium text-gray-800 mb-4">{messages?.components?.fileManager?.localFiles || 'Local Files'}</h3>
                <p className="mb-4 text-gray-600 text-center px-4">{messages?.components?.fileManager?.selectDirectoryDescription || 'Please select a local directory (such as your user home directory) to start browsing and uploading files.'}</p>
                <button
                    onClick={handleSelectLocalDirectory}
                    className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors shadow-sm"
                >
                    {messages?.components?.fileManager?.selectDirectory || 'Select Directory'}
                </button>
                <p className="text-xs text-gray-400 mt-4">{messages?.components?.fileManager?.filesNotAutoUploaded || 'Your files will not be automatically uploaded.'}</p>
            </div>
          ) : (
            <FilePanel
              title={messages?.components?.fileManager?.localFiles || 'Local Files'}
              currentPath={localPath}
              directoryData={localData}
              isLoading={isLocalLoading}
              onNavigate={(path) => {
                if (localDirHandle) {
                  setLocalPath(path);
                  fetchLocalDirectory(localDirHandle, path);
                }
              }}
              onRefresh={() => {
                if (localDirHandle) {
                  fetchLocalDirectory(localDirHandle, localPath, { showLoading: false });
                }
              }}
              onContextMenu={(e, item) => handleContextMenu(e, item, false)}
              onReselectDirectory={handleSelectLocalDirectory}
              selectedItems={selectedLocalItems}
              onItemSelect={(item, isSelected, isMultiSelect, isShiftSelect) =>
                handleItemSelect(item, isSelected, isMultiSelect, false, isShiftSelect)
              }
              onSelectAll={() => handleSelectAll(false)}
              onClearSelection={() => handleClearSelection(false)}
              isRemote={false}
              fileListRef={localFileListRef}
              messages={messages}
            />
          )}

          {/* 远程文件面板 */}
          <FilePanel
            title={messages?.components?.fileManager?.sandboxFiles || 'Sandbox Files'}
            currentPath={remotePath}
            directoryData={remoteData}
            isLoading={isRemoteLoading}
            onNavigate={(path) => {
              setRemotePath(path);
              fetchRemoteDirectory(path);
            }}
            onRefresh={() => fetchRemoteDirectory(remotePath, { showLoading: false })}
            onContextMenu={(e, item) => handleContextMenu(e, item, true)}
            isCreatingFolder={isCreatingFolder}
            newFolderName={newFolderName}
            onNewFolderNameChange={setNewFolderName}
            onConfirmCreateFolder={confirmCreatingFolder}
            onCancelCreateFolder={cancelCreatingFolder}
            newFolderInputRef={newFolderInputRef}
            selectedItems={selectedRemoteItems}
            onItemSelect={(item, isSelected, isMultiSelect, isShiftSelect) =>
              handleItemSelect(item, isSelected, isMultiSelect, true, isShiftSelect)
            }
            onSelectAll={() => handleSelectAll(true)}
            onClearSelection={() => handleClearSelection(true)}
            isRemote={true}
            fileListRef={remoteFileListRef}
            canViewGlobalDocs={canViewGlobalDocs}
            messages={messages}
          />
        </div>
        {/* Footer - 处理进度 */}
        <div className="px-6 py-3 border-t border-gray-200/50 bg-gray-100/60 rounded-b-2xl flex items-center justify-between select-none">
          <button
            ref={progressToggleRef}
            className="text-xs text-gray-700 hover:text-blue-700 transition-colors cursor-pointer"
            onClick={() => setIsProgressOpen(v => !v)}
            title={messages?.components?.fileManager?.viewProgress || 'View Upload/Download Processing Progress'}
          >
            {messages?.components?.fileManager?.processingProgress || 'Processing Progress'} {isProgressOpen ? '▲' : '▼'}
          </button>
        </div>
        {/* 处理进度折叠面板 */}
        {isProgressOpen && (
          <div ref={progressPanelRef} className="absolute left-6 right-6 bottom-16 bg-white/95 backdrop-blur-sm border border-gray-200/70 rounded-xl shadow-xl overflow-hidden select-none cursor-default">
            <div className="px-4 py-2 text-xs text-gray-600 border-b border-gray-100">{messages?.components?.fileManager?.taskStatus || 'Task Status (Upload/Download)'}</div>
            <div className="max-h-48 overflow-auto divide-y divide-gray-100">
              {tasks.length === 0 ? (
                <div className="px-4 py-3 text-sm text-gray-400">{messages?.components?.fileManager?.noTasks || 'No Tasks'}</div>
              ) : (
                tasks.map(t => (
                  <div key={t.id} className="px-4 py-2 text-sm flex items-center gap-3">
                    <span className="flex items-center justify-center w-4 h-4">
                      {t.kind === 'upload' ? (
                        <Upload className="w-4 h-4 text-gray-500" />
                      ) : (
                        <Download className="w-4 h-4 text-gray-500" />
                      )}
                    </span>
                    <span className="font-medium text-gray-800 truncate max-w-[30%]" title={t.name}>{t.name}</span>
                    <span className="text-gray-500 truncate flex-1" title={t.path}>{t.path}</span>
                    <span className={`text-xs px-2 py-0.5 rounded-full ${
                      t.status === 'processing' ? 'bg-blue-100 text-blue-700' :
                      t.status === 'completed' ? 'bg-green-100 text-green-700' :
                      t.status === 'failed' ? 'bg-red-100 text-red-700' : 'bg-gray-100 text-gray-600'
                    }`}>
                      {t.status === 'processing' ? (messages?.components?.fileManager?.processing || 'Processing') : t.status === 'completed' ? (messages?.components?.fileManager?.completed || 'Completed') : t.status === 'failed' ? (messages?.components?.fileManager?.failed || 'Failed') : (messages?.components?.fileManager?.waiting || 'Waiting')}
                    </span>
                    {t.message && <span className="text-xs text-gray-400">{t.message}</span>}
                  </div>
                ))
              )}
            </div>
          </div>
        )}
        </div>
      </div>

      {/* 右键菜单 */}
      {contextMenu && (
        <ContextMenu
          x={contextMenu.x}
          y={contextMenu.y}
          items={getContextMenuItems()}
          onClose={() => setContextMenu(null)}
        />
      )}

      {/* 隐藏的文件上传输入 */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={acceptAttr}
        className="hidden"
        onChange={(e) => {
          const files = e.target.files;
          if (files) {
            Array.from(files).forEach(file => {
              uploadFile(file, remotePath);
            });
          }
          e.target.value = '';
        }}
      />
    </>
  );
};

export default FileManager;
