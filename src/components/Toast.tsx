'use client';

import React, { useEffect, useState } from 'react';
import { FaExclamationTriangle, FaTimes, FaCog, FaCheckCircle } from 'react-icons/fa';

export interface ToastProps {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  duration?: number;
  onDismiss: (id: string) => void;
  action?: {
    label: string;
    onClick: () => void;
  };
}

const icons: { [key in ToastProps['type']]: React.ReactNode } = {
  warning: <FaExclamationTriangle className="h-6 w-6 text-yellow-500" />,
  info: <FaCog className="h-6 w-6 text-blue-500" />,
  error: <FaExclamationTriangle className="h-6 w-6 text-red-500" />,
  success: <FaCheckCircle className="h-6 w-6 text-green-500" />,
};

const Toast: React.FC<ToastProps> = ({ id, type, title, message, duration = 5000, onDismiss, action }) => {
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsExiting(true);
      setTimeout(() => onDismiss(id), 300); // Allow time for exit animation
    }, duration);

    return () => clearTimeout(timer);
  }, [id, duration, onDismiss]);

  const handleDismiss = () => {
    setIsExiting(true);
    setTimeout(() => onDismiss(id), 300);
  };

  return (
    <div
      className={`relative w-full max-w-sm overflow-hidden rounded-xl bg-gray-50/80 dark:bg-black/50 backdrop-blur-lg border border-gray-200 dark:border-white/10 shadow-lg
                  transform transition-all duration-300 ease-in-out animate-slide-in-right
                  ${isExiting ? 'animate-slide-out-right' : ''}`}
    >
      <div className="p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0 pt-0.5">{icons[type]}</div>
          <div className="ml-3 flex-1">
            <p className="text-sm font-medium text-gray-900 dark:text-white">{title}</p>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-300">{message}</p>
            {action && (
              <div className="mt-3">
                <button
                  onClick={() => {
                    action.onClick();
                    handleDismiss();
                  }}
                  className="text-sm font-semibold text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300"
                >
                  {action.label}
                </button>
              </div>
            )}
          </div>
          <div className="ml-4 flex flex-shrink-0">
            <button
              onClick={handleDismiss}
              className="inline-flex rounded-md bg-transparent text-gray-400 hover:text-gray-500 dark:hover:text-gray-200 focus:outline-none"
            >
              <span className="sr-only">Close</span>
              <FaTimes className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Toast; 