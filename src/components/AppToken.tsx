import { useToast } from "@/contexts/ToastContext";
import { authFetch } from "@/utils/authFetch";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  PaginationState,
  useReactTable,
} from "@tanstack/react-table";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { FaRegCopy, FaTimes } from "react-icons/fa";
import TokenEditor from "./TokenEditor";
import { useLanguage } from "@/contexts/LanguageContext";
import Popconfirm from "./Popconfirm";

interface Token {
  id: number;
  token: string;
  token_type: 1 | 2;
  state: boolean;
  created_date: string;
  expires_at: string;
  effective_at: string;
  users: User[];
}

interface User {
  id: number;
  user_name: string;
  user_code: string;
}

interface AppTokenProps {
  app_primary_key: number;
  onClose: () => void;
}

const AppToken = (props: AppTokenProps) => {
  const { app_primary_key, onClose } = props;

  const { addToast } = useToast();

  const [tokens, setTokens] = useState<Token[]>([]);
  const [pages, setPages] = useState<number>(0);
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const pageSizeOptions = [10, 20, 50];
  const [showTokenEditor, setShowTokenEditor] = useState<boolean>(false);
  const { messages } = useLanguage();
  const [selectedToken, setSelectedToken] = useState<Token>();

  // 说明：根据需求将“用户级(2)”与“系统级(1)”在页面展示时对调
  // 因此当后端返回 token_type=1（原系统级）时，页面显示为“用户级”；
  // 当 token_type=2（原用户级）时，页面显示为“系统级”。
  const TOKEN_TYPE_MAP = {
    1: messages.authApp.userLevel,
    2: messages.authApp.systemLevel,
  };

  const fetchTokens = useCallback(async () => {
    const response = await authFetch(
      `/api/apps/${app_primary_key}/tokens?page=${pagination.pageIndex}&size=${pagination.pageSize}`
    );
    if (response && response.ok) {
      const json = await response.json();
      setPages(Math.ceil(json.total / pagination.pageSize));
      setTokens(json.data);
    }
  }, [app_primary_key, pagination.pageIndex, pagination.pageSize]);

  useEffect(() => {
    fetchTokens();
  }, [fetchTokens]);

  const formatToken = (origin: string) => {
    return origin.slice(0, 4) + "****" + origin.slice(-4);
  };

  // 先解构文案，便于依赖收敛
  const authTexts = messages.authApp;
  const copyTokenText = authTexts.copyToken;
  const typeText = authTexts.type;
  const stateText = authTexts.state;
  const effectiveTimeText = authTexts.effectiveTime;
  const expireTimeText = authTexts.expireTime;
  const operationText = authTexts.operation;
  const editText = authTexts.edit;
  const disableTokenText = authTexts.disableToken;
  const confirmDisableTokenDescText = authTexts.confirmDisableTokenDesc;
  const enableTokenText = authTexts.enableToken;
  const confirmEnableTokenDescText = authTexts.confirmEnableTokenDesc;
  const disableText = authTexts.disable;
  const enableText = authTexts.enable;
  const deleteLabelText = authTexts.delete;
  const deleteTokenText = authTexts.deleteToken;
  const confirmDeleteTokenText = authTexts.confirmDelteToken;

  const handleCopy = useCallback(
    async (text?: string) => {
      if (!text) return;

      const showToast = (type: "success" | "error", message: string) => {
        addToast({
          type,
          title: type === "success" ? authTexts.copySuccess : authTexts.copyFail,
          message,
        });
      };

      try {
        // 优先使用 Clipboard API
        if (window.isSecureContext && navigator.clipboard) {
          await navigator.clipboard.writeText(text);
          showToast("success", authTexts.copyToClipboard);
          return;
        }

        // 降级方案
        const textarea = document.createElement("textarea");
        textarea.value = text;
        textarea.style.position = "fixed";
        textarea.style.opacity = "0";
        document.body.appendChild(textarea);
        textarea.select();

        try {
          const successful = document.execCommand("copy");
          if (!successful) throw new Error("execCommand failed");
          showToast("success", authTexts.copyToClipboard);
        } finally {
          document.body.removeChild(textarea);
        }
      } catch (error) {
        console.error(authTexts.copyFail, error);
        showToast(
          "error",
          `messages.authApp.copyFail: ${
            error instanceof Error ? error.message : authTexts.copyManual
          }`
        );
      }
    },
    [addToast, authTexts]
  );

  const deleteToken = useCallback(
    async (id: number) => {
      const response = await authFetch(
        `/api/apps/${app_primary_key}/tokens/${id}`,
        { method: "DELETE" }
      );
      if (response && response.ok) {
        addToast({
          type: "success",
          title: deleteTokenText,
          message: authTexts.deleteTokenSuccess,
        });
        fetchTokens();
      } else {
        addToast({
          type: "error",
          title: deleteTokenText,
          message: authTexts.deleteTokenFail,
        });
      }
    },
    [addToast, app_primary_key, deleteTokenText, authTexts, fetchTokens]
  );

  const modifyTokenState = useCallback(
    async (id: number, state: boolean) => {
      const response = await authFetch(
        `/api/apps/${app_primary_key}/tokens/${id}/state`,
        {
          method: "PATCH",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ state: state }),
        }
      );
      if (response && response.ok) {
        addToast({
          type: "success",
          title: authTexts.modifyTokenState,
          message: authTexts.modifyTokenStateSuccess,
        });
        fetchTokens();
      } else {
        addToast({
          type: "error",
          title: authTexts.modifyTokenState,
          message: authTexts.modifyTokenStateFail,
        });
      }
    },
    [addToast, app_primary_key, authTexts, fetchTokens]
  );

  const columns: ColumnDef<Token>[] = useMemo(
    () => [
      {
        accessorKey: "token",
        header: "Token",
        cell: (info) => (
          <div className="flex items-center">
            <div className="text-sm w-[110px] overflow-hidden whitespace-nowrap text-ellipsis">
              {formatToken(info.getValue() as string)}
            </div>
            <button
              className="ml-1 p-1 rounded hover:bg-[var(--hover-bg)] transition-colors"
              title={copyTokenText}
              onClick={() => handleCopy(info.getValue() as string)}
              style={{ lineHeight: 0 }}
            >
              <FaRegCopy
                size={16}
                className="text-[var(--muted)] hover:text-[var(--primary)]"
              />
            </button>
          </div>
        ),
        size: 120,
      },
      {
        accessorKey: "token_type",
        header: typeText,
        cell: (info) => (
          <div className="text-sm">
            {TOKEN_TYPE_MAP[info.getValue() as 1 | 2]}
          </div>
        ),
        size: 60,
      },
      {
        accessorKey: "state",
        header: stateText,
        cell: (info) => (
          <div
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-semibold ${
              info.getValue()
                ? "bg-green-100 text-green-800"
                : "bg-red-100 text-red-800"
            }`}
          >
            {info.getValue() == 1 ? enableText : disableText}
          </div>
        ),
        size: 60,
      },
      {
        accessorKey: "effective_at",
        header: effectiveTimeText,
        cell: (info) => (
          <div className="text-sm">{info.getValue() as string}</div>
        ),
        size: 120,
      },
      {
        accessorKey: "expires_at",
        header: expireTimeText,
        cell: (info) => (
          <div className="text-sm">{(info.getValue() as string) || ""}</div>
        ),
        size: 120,
      },
      // 已移除绑定用户列（用户级token也不再绑定用户）
      {
        accessorKey: "operation",
        header: operationText,
        size: 100,
        cell: (info) => (
          <div className="flex items-center gap-x-2 relative">
            <div
              className="text-blue-500 text-sm cursor-pointer"
              onClick={() => {
                setSelectedToken(info.row.original);
                setShowTokenEditor(true);
              }}
            >
              {editText}
            </div>
            {info.row.original.state ? (
              <Popconfirm
                title={disableTokenText}
                description={confirmDisableTokenDescText}
                onConfirm={() => {
                  modifyTokenState(
                    info.row.original.id,
                    !info.row.original.state
                  );
                }}
                onCancel={() => {}}
              >
                <div className="text-amber-500 text-sm cursor-pointer">
                  {disableText}
                </div>
              </Popconfirm>
            ) : (
              <Popconfirm
                title={enableTokenText}
                description={confirmEnableTokenDescText}
                onConfirm={() => {
                  modifyTokenState(
                    info.row.original.id,
                    !info.row.original.state
                  );
                }}
                onCancel={() => {}}
              >
                <div className="text-green-500 text-sm cursor-pointer">
                  {enableText}
                </div>
              </Popconfirm>
            )}
            <Popconfirm
              title={deleteTokenText}
              description={confirmDeleteTokenText}
              onConfirm={() => {
                deleteToken(info.row.original.id);
              }}
              onCancel={() => {}}
            >
              <div className="text-red-500 text-sm cursor-pointer">
                {deleteLabelText}
              </div>
            </Popconfirm>
          </div>
        ),
      },
    ],
    [
      TOKEN_TYPE_MAP,
      copyTokenText,
      typeText,
      stateText,
      effectiveTimeText,
      expireTimeText,
      operationText,
      editText,
      disableTokenText,
      confirmDisableTokenDescText,
      enableTokenText,
      confirmEnableTokenDescText,
      disableText,
      enableText,
      deleteLabelText,
      deleteTokenText,
      confirmDeleteTokenText,
      handleCopy,
      deleteToken,
      modifyTokenState,
    ]
  );

  const table = useReactTable({
    data: tokens,
    columns: columns,
    pageCount: pages,
    state: {
      pagination: pagination,
    },
    manualPagination: true,
    getCoreRowModel: getCoreRowModel(),
    onPaginationChange: setPagination,
  });

  // handleCopy/deleteToken/modifyTokenState 已上移并改为 useCallback

  return (
    <div className="fixed inset-0 bg-black/60 bg-opacity-50 flex items-center justify-center z-50 p-4 backdrop-blur-sm text-start">
      <div className="bg-[var(--card-bg)] rounded-lg shadow-2xl border border-[var(--border-color)] w-full max-w-6xl max-h-[95vh] overflow-hidden">
        <div className="border-b border-[var(--border-color)] flex flex-col gap-y-6">
          {/* modal header */}
          <div className="flex items-center justify-between px-6 pt-6">
            <h3 className="text-lg font-semibold text-[var(--foreground)]">
              {messages.authApp.tokenManagement}
            </h3>
            {onClose && (
              <button
                onClick={onClose}
                className="text-[var(--muted)] hover:text-[var(--foreground)] transition-colors"
              >
                <FaTimes size={16} />
              </button>
            )}
          </div>

          {/* modal body */}
          <div className="w-full max-h-[70vh] flex flex-col gap-y-5 px-6 pb-8">
            <div className="basis-auto flex justify-end">
              <button
                className="px-4 py-2 text-sm font-medium rounded-md border border-transparent bg-[var(--accent-primary)]/90 text-white hover:bg-[var(--accent-primary)] transition-colors disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
                onClick={() => setShowTokenEditor(true)}
              >
                {messages.authApp.generateToken}
              </button>
            </div>

            {/* 表格 */}
            <div className="border border-[var(--border-color)] rounded-md flex-1 flex flex-col overflow-hidden">
              <div className="basis-auto">
                <table className="w-full h-full table-fixed">
                  <thead className="border-b border-[var(--border-color)] bg-[var(--accent-secondary)]">
                    {table.getHeaderGroups().map((headerGroup) => (
                      <tr key={headerGroup.id}>
                        {headerGroup.headers.map((header) => (
                          <th
                            key={header.id}
                            className="px-4 py-2 text-left text-sm font-medium text-[var(--foreground)]"
                            style={{ width: header.getSize() }}
                          >
                            {header.isPlaceholder
                              ? null
                              : flexRender(
                                  header.column.columnDef.header,
                                  header.getContext()
                                )}
                          </th>
                        ))}
                      </tr>
                    ))}
                  </thead>
                </table>
              </div>
              <div className="flex-1 overflow-auto">
                <table className="w-full table-fixed">
                  <tbody>
                    {table.getRowModel().rows.length > 0 ? (
                      table.getRowModel().rows.map((row) => (
                        <tr
                          key={row.original.id}
                          className={`border-b border-[var(--border-color)]`}
                        >
                          {row.getVisibleCells().map((cell) => (
                            <td
                              key={cell.id}
                              className="px-4 py-2"
                              style={{ width: cell.column.getSize() }}
                            >
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext()
                              )}
                            </td>
                          ))}
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td
                          colSpan={table.getAllColumns().length}
                          className="px-4 py-20 text-center"
                        >
                          <div className="flex flex-col items-center justify-center text-[var(--muted)]">
                            <p className="text-sm font-medium">
                              {messages.authApp.noData}
                            </p>
                          </div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="flex flex-col items-end justify-between gap-2 basis-auto">
              <div className="flex items-center gap-2">
                {/* 页面大小选择器 */}
                <div className="flex items-center gap-2 text-sm text-[var(--muted)]">
                  <select
                    value={pagination.pageSize}
                    onChange={(e) => {
                      setPagination((prev) => ({
                        ...prev,
                        pageSize: Number(e.target.value),
                        pageIndex: 0, // 切换页面大小时重置到第一页
                      }));
                    }}
                    className="px-2 py-1 text-xs border border-[var(--border-color)] rounded bg-[var(--background)] text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)]"
                  >
                    {pageSizeOptions.map((size) => (
                      <option key={size} value={size}>
                        {size}
                      </option>
                    ))}
                  </select>
                  <span>{messages.authApp.recoredPerPage}</span>
                </div>
                <button
                  onClick={() => table.previousPage()}
                  disabled={!table.getCanPreviousPage()}
                  className="px-3 py-1 text-sm border border-[var(--border-color)] rounded-md bg-[var(--background)] text-[var(--foreground)] hover:bg-[var(--accent-secondary)] disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
                >
                  {messages.authApp.prePage}
                </button>

                <span className="text-sm text-[var(--muted)]">
                  {messages.authApp.page
                    .replace(
                      "{page}",
                      (table.getState().pagination.pageIndex + 1).toString()
                    )
                    .replace("{total}", table.getPageCount().toString())}
                </span>

                <button
                  onClick={() => table.nextPage()}
                  disabled={!table.getCanNextPage()}
                  className="px-3 py-1 text-sm border border-[var(--border-color)] rounded-md bg-[var(--background)] text-[var(--foreground)] hover:bg-[var(--accent-secondary)] disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
                >
                  {messages.authApp.nextPage}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {showTokenEditor && (
        <TokenEditor
          app_primary_key={app_primary_key}
          data={selectedToken}
          onClose={() => setShowTokenEditor(false)}
          onFinish={() => {
            fetchTokens();
            setShowTokenEditor(false);
          }}
        />
      )}
    </div>
  );
};

export default AppToken;
