import React, { useEffect, useState } from "react";
import { FaTimes, FaSearch } from "react-icons/fa";
import { useToast } from "@/contexts/ToastContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { authFetch } from "@/utils/authFetch";
import CustomRadio from "./CustomRadio";

interface WikiBindingProps {
  app_primary_key: number;
  onClose: () => void;
  onFinish: () => void;
}

interface WikiOption {
  id: number;
  wiki_id: string;
  name?: string;
  description?: string;
  repo_owner?: string;
  repo_name?: string;
  branch?: string;
  is_bound?: boolean;
}

const WikiBinding = ({ app_primary_key, onClose, onFinish }: WikiBindingProps) => {
  const { addToast } = useToast();
  const { messages } = useLanguage();

  const [options, setOptions] = useState<WikiOption[]>([]);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [search, setSearch] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [initialized, setInitialized] = useState<boolean>(false);

  const fetchOptions = async (keyword?: string) => {
    setLoading(true);
    try {
      const query = keyword ? `?search=${encodeURIComponent(keyword)}` : "";
      const response = await authFetch(`/api/apps/${app_primary_key}/wikis/options${query}`);
      if (response && response.ok) {
        const data = await response.json();
        setOptions(data || []);
        if (!initialized) {
          const bound = (data || [])
            .filter((item: WikiOption) => item.is_bound)
            .map((item: WikiOption) => item.id);
          setSelectedIds(bound);
          setInitialized(true);
        }
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOptions();
  }, []);

  const toggleSelect = (id: number) => {
    setSelectedIds((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    const allIds = options.map((option) => option.id);
    setSelectedIds(allIds);
  };

  const handleUnselectAll = () => {
    setSelectedIds([]);
  };

  const isAllSelected = options.length > 0 && selectedIds.length === options.length;
  // const isPartiallySelected = selectedIds.length > 0 && selectedIds.length < options.length;

  const handleSubmit = async () => {
    const response = await authFetch(`/api/apps/${app_primary_key}/wikis`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ wiki_list: selectedIds }),
    });
    if (response && response.ok) {
      addToast({
        type: "success",
        title: messages.authApp.bindWiki,
        message: messages.authApp.bindWikiSuccess,
      });
      onFinish();
    } else {
      addToast({
        type: "error",
        title: messages.authApp.bindWiki,
        message: messages.authApp.bindWikiFail,
      });
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 bg-opacity-100 flex items-center justify-center z-[110] p-4 backdrop-blur-sm text-start">
      <div className="bg-[var(--card-bg)] rounded-lg shadow-2xl border border-[var(--border-color)] w-full max-w-3xl max-h-[95vh] overflow-hidden">
        <div className="p-6 border-b border-[var(--border-color)] flex flex-col gap-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-[var(--foreground)]">
              {messages.authApp.bindWiki}
            </h3>
            <button
              onClick={onClose}
              className="text-[var(--muted)] hover:text-[var(--foreground)] transition-colors"
            >
              <FaTimes size={16} />
            </button>
          </div>

          <div className="flex gap-2">
            <div className="flex-1 flex items-center gap-2">
              <div className="relative flex-1">
                <span className="absolute inset-y-0 left-3 flex items-center text-[var(--muted)]">
                  <FaSearch size={14} />
                </span>
                <input
                  type="text"
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      fetchOptions(search);
                    }
                  }}
                  placeholder={messages.authApp.searchWikiPlaceholder}
                  className="w-full pl-9 pr-3 py-2 text-sm rounded-md bg-transparent text-[var(--foreground)] border border-[var(--border-color)] focus:outline-none focus:border-[var(--accent-primary)]"
                />
              </div>
              <button
                className="px-3 py-2 text-sm font-medium rounded-md border border-[var(--border-color)] text-[var(--accent-primary)] hover:bg-[var(--accent-primary)]/10 transition-colors cursor-pointer"
                onClick={() => fetchOptions(search)}
              >
                {messages.user.search}
              </button>
            </div>
            <div className="text-xs text-[var(--muted)] self-center">
              {messages.authApp.wikiBindingEmptyHint}
            </div>
          </div>

          {options.length > 0 && (
            <div className="flex justify-between items-center">
              <div className="text-sm text-[var(--muted)]">
                {messages.authApp.selectAll}: {selectedIds.length}/{options.length}
              </div>
              <div className="flex gap-2">
                <button
                  className="px-3 py-1.5 text-sm font-medium rounded-md border border-[var(--border-color)] text-[var(--accent-primary)] hover:bg-[var(--accent-primary)]/10 transition-colors cursor-pointer"
                  onClick={isAllSelected ? handleUnselectAll : handleSelectAll}
                >
                  {isAllSelected ? messages.authApp.unselectAll : messages.authApp.selectAll}
                </button>
              </div>
            </div>
          )}

          <div className="w-full flex flex-col gap-y-4 max-h-[50vh] overflow-y-auto">
            {loading ? (
              <div className="flex justify-center items-center h-32 text-[var(--muted)]">
                {messages.common.loading}
              </div>
            ) : options.length ? (
              <>
                {(() => {
                  const currentSelected = new Set(selectedIds);
                  const boundList = options.filter((item) => currentSelected.has(item.id));
                  const unboundList = options.filter((item) => !currentSelected.has(item.id));

                  return (
                    <>
                      <div>
                        <div className="text-xs font-medium text-[var(--muted)] mb-2 uppercase tracking-wide">
                          {messages.authApp.boundWikiList}
                        </div>
                        {boundList.length ? (
                          <div className="flex flex-col gap-y-2">
                            {boundList.map((wiki) => (
                              <CustomRadio
                                key={`bound-${wiki.id}`}
                                selected={true}
                                onClick={() => toggleSelect(wiki.id)}
                              >
                                <div className="flex flex-col items-start">
                                  <span className="font-medium text-sm text-[var(--foreground)]">
                                    {wiki.name || wiki.repo_name || wiki.wiki_id}
                                  </span>
                                  <span className="text-xs text-[var(--muted)]">
                                    {wiki.repo_owner ? `${wiki.repo_owner}/${wiki.repo_name}` : wiki.repo_name || "-"}
                                    {wiki.branch ? ` · ${wiki.branch}` : ""}
                                  </span>
                                </div>
                              </CustomRadio>
                            ))}
                          </div>
                        ) : (
                          <div className="text-xs text-[var(--muted)]">
                            {messages.authApp.noBoundWiki}
                          </div>
                        )}
                      </div>

                      <div>
                        <div className="text-xs font-medium text-[var(--muted)] mb-2 uppercase tracking-wide">
                          {messages.authApp.unboundWikiList}
                        </div>
                        {unboundList.length ? (
                          <div className="flex flex-col gap-y-2">
                            {unboundList.map((wiki) => (
                              <CustomRadio
                                key={`unbound-${wiki.id}`}
                                selected={false}
                                onClick={() => toggleSelect(wiki.id)}
                              >
                                <div className="flex flex-col items-start">
                                  <span className="font-medium text-sm text-[var(--foreground)]">
                                    {wiki.name || wiki.repo_name || wiki.wiki_id}
                                  </span>
                                  <span className="text-xs text-[var(--muted)]">
                                    {wiki.repo_owner ? `${wiki.repo_owner}/${wiki.repo_name}` : wiki.repo_name || "-"}
                                    {wiki.branch ? ` · ${wiki.branch}` : ""}
                                  </span>
                                </div>
                              </CustomRadio>
                            ))}
                          </div>
                        ) : (
                          <div className="text-xs text-[var(--muted)]">
                            {messages.authApp.noAvailableWiki}
                          </div>
                        )}
                      </div>
                    </>
                  );
                })()}
              </>
            ) : (
              <div className="flex justify-center items-center h-32 text-[var(--muted)]">
                {messages.authApp.noWikiData}
              </div>
            )}
          </div>

          <div className="flex justify-end items-center gap-2">
            <button
              className="px-4 py-2 text-sm font-medium rounded-md border border-[var(--border-color)] text-[var(--accent-primary)] hover:bg-[var(--accent-primary)]/10 transition-colors cursor-pointer"
              onClick={onClose}
            >
              {messages.authApp.cancel}
            </button>
            <button
              className="px-4 py-2 text-sm font-medium rounded-md border border-transparent bg-[var(--accent-primary)]/90 text-white hover:bg-[var(--accent-primary)] transition-colors cursor-pointer"
              onClick={handleSubmit}
            >
              {messages.authApp.submit}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WikiBinding;
