'use client';

import React, { useState } from 'react';
import ChatInput from './ChatInput';
import { WikiStructure } from '@/types/wiki/wikistructure';

interface Model {
  id: string;
  name: string;
}

interface Provider {
  id: string;
  name: string;
  models: Model[];
  supportsCustomModel?: boolean;
}

interface ChatInputAdapterProps {
  wikiStructure?: WikiStructure;
  wikiInfoId?: string;
  onSubmit: (question: string, modelType: 'whalecloud' | 'gemini-cli', selectedProvider?: string, selectedModel?: string, fileReferences?: unknown[], commandParams?: { operation: string | null, param: string | null }, images?: Array<string>) => void;
  isLoading?: boolean;
  // 新增：模型相关 props
  providers: Provider[];
  availableModels: Model[];
  selectedProvider: string;
  setSelectedProvider: (provider: string) => void;
  selectedModel: string;
  setSelectedModel: (model: string) => void;
  modelType: 'whalecloud' | 'gemini-cli';
  setModelType: (type: 'whalecloud' | 'gemini-cli') => void;
  deepResearch: boolean;
  setDeepResearch: (deepResearch: boolean) => void;
  // 新增：控制定位的 prop
  useFixedPosition?: boolean;
  // 新增：文件搜索相关 props
  repoUrl?: string;
  branch?: string;
  userCode?: string;
  onFileManagerOpen?: () => void;
}

const ChatInputAdapter: React.FC<ChatInputAdapterProps> = ({ 
  wikiStructure,
  wikiInfoId,
  onSubmit, 
  isLoading = false,
  providers,
  availableModels,
  selectedProvider,
  setSelectedProvider,
  selectedModel,
  setSelectedModel,
  modelType,
  setModelType,
  deepResearch,
  setDeepResearch,
  useFixedPosition = true,
  repoUrl,
  branch,
  userCode,
  onFileManagerOpen,
}) => {
  const [question, setQuestion] = useState('');

  const handleSubmit = (finalQuestion: string, fileReferences?: unknown[], commandParams?: { operation: string | null, param: string | null }, images?: Array<string>) => {
    onSubmit(finalQuestion, modelType, selectedProvider, selectedModel, fileReferences, commandParams, images);
    setQuestion('');
  };

  const clearConversation = () => {
    setQuestion('');
  };

  return (
    <div className={`${useFixedPosition ? 'fixed bottom-4' : 'relative mt-4'} left-1/2 -translate-x-1/2 w-full max-w-3xl px-4 z-10`}>
      <ChatInput
        wikiInfoId={wikiInfoId}
        wikiStructure={wikiStructure}
        question={question}
        setQuestion={setQuestion}
        handleSubmit={handleSubmit}
        isLoading={isLoading}
        clearConversation={clearConversation}
        deepResearch={deepResearch}
        setDeepResearch={setDeepResearch}
        modelType={modelType}
        setModelType={setModelType}
        selectedProvider={selectedProvider}
        setSelectedProvider={setSelectedProvider}
        selectedModel={selectedModel}
        setSelectedModel={setSelectedModel}
        providers={providers}
        availableModels={availableModels}
        repoUrl={repoUrl}
        branch={branch}
        userCode={userCode}
        onFileManagerOpen={onFileManagerOpen}
      />
    </div>
  );
};

export default ChatInputAdapter; 
