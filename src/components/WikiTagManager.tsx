'use client';

import React, { useState, useEffect, useRef, ReactNode } from 'react';
import { FaTimes, FaPlus, FaSearch } from 'react-icons/fa';
import { authFetch } from '@/utils/authFetch';
import { useToast } from '@/contexts/ToastContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { Tag, TagCreateRequest, TAG_TYPE } from '@/types/tag';
import ReactDOM from 'react-dom';

export interface WikiTag {
  wiki_tag_id: number;
  wiki_id: number;
  tag_id: number;
  tag_name: string;
  tag_type: number;
  tag_color: string;
  tag_comments?: string;
  tag_module_type: number;
  tag_state: number;
  created_by: number;
  created_date: string;
  update_by?: number;
  update_date?: string;
}

interface WikiTagManagerProps {
  wiki_id: number;
  wiki_tags: Tag[];
  visibility: number;
}

interface Rect {
  x: number;
  y: number;
  height: number;
}

const PRESET_COLORS = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
  '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
  '#F8C471', '#82E0AA', '#F1948A', '#E74C3C', '#D7BDE2',
  '#F9E79F', '#D5A6BD', '#A9CCE3', '#FAD7A0', '#D2B4DE'
];

const TopLayerPortal = ({ rect, onClose, children }: { rect: Rect, onClose: () => void, children?: ReactNode }) => {
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClick(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        onClose();
      }
    }
    document.addEventListener("mousedown", handleClick);
    return () => document.removeEventListener("mousedown", handleClick);
  }, [onClose]);

  const { x, y, height } = rect;
  return ReactDOM.createPortal(
    <div
      ref={dropdownRef}
      style={{
        position: "absolute",
        left: x,
        top: y + height,
        zIndex: 100,
        pointerEvents: "auto",
      }}
    >
      {children}
    </div>,
    document.body
  );
}

export default function WikiTagManager({ wiki_id, wiki_tags }: WikiTagManagerProps) {
  const [wikiTags, setWikiTags] = useState<Tag[]>(wiki_tags);
  const { addToast } = useToast();
  const { messages } = useLanguage();
  const [allTags, setAllTags] = useState<Tag[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showDropdown, setShowDropdown] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState<'left' | 'right'>('left');
  const [newTagForm, setNewTagForm] = useState<TagCreateRequest>({
    name: '',
    type: TAG_TYPE.USER,
    color: '#4ECDC4',
    module_type: 1,
    state: 1
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const colorPickerRef = useRef<HTMLDivElement>(null);
  const colorButtonRef = useRef<HTMLButtonElement>(null);
  const tagDropdownRef = useRef<HTMLDivElement>(null);

  const [rect, setRect] = useState<Rect>();

  useEffect(() => {
    setWikiTags(wiki_tags);
  }, [wiki_tags]);

  useEffect(() => {
    if (showDropdown && tagDropdownRef.current) {
      const domRect = tagDropdownRef.current.getBoundingClientRect();
      setRect({
        x: domRect.left + window.scrollX,
        y: domRect.top + window.scrollY,
        height: domRect.height,
      });
    }
  }, [showDropdown]);

  // 计算下拉框位置
  const calculateDropdownPosition = () => {
    if (tagDropdownRef.current) {
      const rect = tagDropdownRef.current.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const dropdownWidth = 320; // w-80 = 320px
      const safeMargin = 20; // 安全边距
      
      // 查找最近的滚动容器（通常是modal）
      let parentContainer = tagDropdownRef.current.parentElement;
      let containerRect = null;
      
      while (parentContainer && !containerRect) {
        const computedStyle = window.getComputedStyle(parentContainer);
        if (computedStyle.overflow === 'auto' || computedStyle.overflow === 'scroll' || 
            computedStyle.overflowY === 'auto' || computedStyle.overflowY === 'scroll') {
          containerRect = parentContainer.getBoundingClientRect();
          break;
        }
        parentContainer = parentContainer.parentElement;
      }
      
      // 计算右侧和左侧可用空间
      const rightSpace = viewportWidth - rect.right;
      const leftSpace = rect.left;
      
      // 如果有容器边界，考虑容器边界
      if (containerRect) {
        const containerRightSpace = containerRect.right - rect.right;
        const containerLeftSpace = rect.left - containerRect.left;
        
        // 优先显示在右侧，但如果空间不足则显示在左侧
        if (containerRightSpace >= dropdownWidth + safeMargin) {
          setDropdownPosition('left');
        } else if (containerLeftSpace >= dropdownWidth + safeMargin) {
          setDropdownPosition('right');
        } else {
          // 如果两侧空间都不足，选择空间较大的一侧
          setDropdownPosition(containerLeftSpace > containerRightSpace ? 'right' : 'left');
        }
      } else {
        // 如果没有找到容器，使用视口边界
        if (rightSpace >= dropdownWidth + safeMargin) {
          setDropdownPosition('left');
        } else if (leftSpace >= dropdownWidth + safeMargin) {
          setDropdownPosition('right');
        } else {
          setDropdownPosition(leftSpace > rightSpace ? 'right' : 'left');
        }
      }
    }
  };

  // 加载所有标签和wiki标签
  const loadTags = async () => {
    try {
      const allTagsResponse = await authFetch('/api/tags/tag');

      if (allTagsResponse?.ok) {
        const allTagsData = await allTagsResponse.json();
        setAllTags(allTagsData.data.filter((tag: Tag) => tag.state === 1) || []);
      }

    } catch (error) {
      console.error('加载标签失败:', error);
      addToast({
        type: 'error',
        title: messages.common?.error || 'Error',
        message: messages.tagManagement?.loadTagsError || 'Error occurred while loading tags'
      });
    } finally {
      setLoading(false);
    }
  };

  const loadWikiTags = async () => {
    try {
      const wikiTagsResponse = await authFetch(`/api/tags/wiki/${wiki_id}`);

      if (wikiTagsResponse?.ok) {
        const wikiTagsData = await wikiTagsResponse.json();
        setWikiTags(wikiTagsData.data || []);
      }
    } catch (error) {
      console.error('加载标签失败:', error);
      addToast({
        type: 'error',
        title: messages.common?.error || 'Error',
        message: messages.tagManagement?.loadTagsError || 'Error occurred while loading tags'
      });
    } finally {
      setLoading(false);
    }
  };
  

  // 添加标签到wiki
  const addTagToWiki = async (tagId: number) => {
    try {
      const response = await authFetch('/api/tags/wiki', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          wiki_id: wiki_id,
          tag_ids: [tagId]
        })
      });

      if (response?.ok) {
        addToast({
          type: 'success',
          title: messages.common?.success || 'Success',
          message: messages.tagManagement?.tagAddedSuccess || 'Tag added to Wiki successfully'
        });
        loadWikiTags(); // 重新加载数据
        setShowDropdown(false); // 关闭下拉框
      } else {
        const errorData = await response?.json();
        addToast({
          type: 'error',
          title: messages.common?.error || 'Error',
          message: errorData?.detail || (messages.tagManagement?.addTagError || 'Error occurred while adding tag')
        });
      }
    } catch (error) {
      console.error('添加标签失败:', error);
      addToast({
        type: 'error',
        title: messages.common?.error || 'Error',
        message: messages.tagManagement?.addTagError || 'Error occurred while adding tag'
      });
    }
  };

  // 从wiki移除标签
  const removeTagFromWiki = async (wikiTagId: number) => {
    try {
      const response = await authFetch('/api/tags/wiki', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          wiki_id: wiki_id,
          tag_id: wikiTagId
        })
      });

      if (response?.ok) {
        addToast({
          type: 'success',
          title: messages.common?.success || 'Success',
          message: messages.tagManagement?.tagRemovedSuccess || 'Tag removed from Wiki successfully'
        });
        loadWikiTags(); // 重新加载数据
      } else {
        addToast({
          type: 'error',
          title: messages.common?.error || 'Error',
          message: messages.tagManagement?.removeTagError || 'Error occurred while removing tag'
        });
      }
    } catch (error) {
      console.error('移除标签失败:', error);
      addToast({
        type: 'error',
        title: messages.common?.error || 'Error',
        message: messages.tagManagement?.removeTagError || 'Error occurred while removing tag'
      });
    }
  };

  // 创建新标签
  const createNewTag = async () => {
    if (!newTagForm.name.trim()) {
      addToast({
        type: 'error',
        title: messages.common?.error || 'Error',
        message: messages.tagManagement?.tagNameRequired || 'Tag name is required'
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await authFetch('/api/tags/tag', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newTagForm)
      });

             if (response?.ok) {
         addToast({
           type: 'success',
           title: messages.common?.success || 'Success',
           message: messages.tagManagement?.tagCreatedSuccess || 'Tag created successfully'
         });
         setShowCreateForm(false);
         setNewTagForm({
           name: '',
           type: TAG_TYPE.USER,
           color: '#4ECDC4',
           module_type: 1,
           state: 1
         });
         loadTags(); // 重新加载数据
       } else {
        const errorData = await response?.json();
        addToast({
          type: 'error',
          title: messages.common?.error || 'Error',
          message: errorData?.detail || (messages.tagManagement?.createTagError || 'Error occurred while creating tag')
        });
      }
    } catch (error) {
      console.error('创建标签失败:', error);
      addToast({
        type: 'error',
        title: messages.common?.error || 'Error',
        message: messages.tagManagement?.createTagError || 'Error occurred while creating tag'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 过滤可添加的标签（排除已添加的）
  const availableTags = allTags.filter(tag => 
    !wikiTags.some(wt => wt.id === tag.id) &&
    tag.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // 点击外部关闭下拉框和颜色选择器
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // if (tagDropdownRef.current && !tagDropdownRef.current.contains(event.target as Node)) {
      //   setShowDropdown(false);
      // }
      if (colorPickerRef.current && !colorPickerRef.current.contains(event.target as Node) &&
          colorButtonRef.current && !colorButtonRef.current.contains(event.target as Node)) {
        setShowColorPicker(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    loadTags();
  }, [wiki_id]);

  // 处理下拉框显示
  const handleDropdownToggle = () => {
    if (!showDropdown) {
      calculateDropdownPosition();
    }
    setShowDropdown(true);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-sm text-gray-600">{messages.tagManagement?.loading || 'Loading...'}</span>
      </div>
    );
  }

  return (
    <div className="relative" ref={dropdownRef}>
      {/* 当前标签显示 */}
      {wikiTags.length > 0 && (
        <div className="mt-3">
          <div className="flex flex-wrap gap-2 items-start">
            <div className="flex flex-wrap gap-2 flex-1">
              {wikiTags.map((tag) => (
                <div key={tag.id} style={{ maxWidth: '140px' }}>
                  <span
                    className="inline-flex items-center gap-1 px-3 py-1 text-sm border"
                    style={{
                      backgroundColor: `${tag.color}20`,
                      borderColor: tag.color,
                      color: tag.color
                    }}
                  >
                    <span 
                      className="truncate max-w-[80px]"
                      title={tag.name}
                    >
                      {tag.name}
                    </span>
                    {tag.module_type !== 2 && <button
                      onClick={() => removeTagFromWiki(tag.id)}
                      className="p-0.5 text-current hover:bg-current hover:bg-opacity-20 rounded-full transition-colors flex-shrink-0"
                      title={messages.tagManagement?.removeTagTitle || 'Remove Tag'}
                    >
                      <FaTimes size={10} />
                    </button>}
                  </span>
                </div>
              ))}
              
              {/* 标签选择器触发器 */}
              <div className="relative" ref={tagDropdownRef}>
                <button
                  onClick={handleDropdownToggle}
                  className="inline-flex items-center justify-center w-8 h-8 border border-[var(--border-color)] rounded-full text-[var(--muted)] hover:text-[var(--foreground)] hover:bg-[var(--accent-secondary)] transition-colors focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] focus:border-transparent"
                  title={messages.tagManagement?.addTag || 'Add Tag'}
                >
                  <FaPlus size={12} />
                </button>
                
                {/* 标签下拉框 */}
                {showDropdown && rect && (
                  <TopLayerPortal rect={rect} onClose={() => setShowDropdown(false)}>
                    <div 
                      className={`absolute top-full mt-1 w-80 bg-[var(--background)] border border-[var(--border-color)] rounded-md shadow-lg z-50 ${
                        dropdownPosition === 'left' ? 'left-0' : 'right-0'
                      }`}
                    >
                      {!showCreateForm ? (
                        <>
                          {/* 搜索框 */}
                          <div className="p-3 border-b border-[var(--border-color)]">
                            <div className="relative">
                              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[var(--muted)]" size={14} />
                              <input
                                type="text"
                                placeholder={messages.tagManagement?.searchAndAddTag || 'Search and add tags'}
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full pl-10 pr-3 py-2 text-sm bg-[var(--accent-secondary)] border border-[var(--border-color)] rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] focus:border-transparent text-[var(--foreground)] placeholder-[var(--muted)]"
                              />
                            </div>
                          </div>

                          {/* 标签列表 */}
                          <div className="max-h-48 overflow-y-auto">
                            {availableTags.length === 0 ? (
                              <div className="text-center py-8 text-[var(--muted)]">
                                {searchTerm ? (messages.tagManagement?.noMatchingTags || 'No matching tags found') : (messages.tagManagement?.noAvailableTags || 'No available tags')}
                              </div>
                            ) : (
                              availableTags.map((tag) => (
                                <div
                                  key={tag.id}
                                  className="flex items-center justify-between p-2 hover:bg-[var(--accent-secondary)] transition-colors cursor-pointer"
                                  onClick={() => {
                                    addTagToWiki(tag.id);
                                  }}
                                >
                                  <div className="flex items-center gap-3">
                                    <div
                                      className="w-4 h-4 rounded-full"
                                      style={{ backgroundColor: tag.color }}
                                    />
                                    <span 
                                      className="text-[var(--foreground)] truncate text-sm w-[120px] text-left"
                                      title={tag.name}
                                    >
                                      {tag.name}
                                    </span>
                                    <span className="text-xs text-[var(--muted)] px-2 py-0.5 bg-[var(--accent-secondary)] rounded flex-shrink-0">
                                      {tag.type === TAG_TYPE.SYSTEM ? messages.tagManagement?.systemTag || 'System Tag' : messages.tagManagement?.userTag || 'User Tag'}
                                    </span>
                                  </div>
                                  <FaPlus size={12} className="text-[var(--muted)]" />
                                </div>
                              ))
                            )}
                            
                            {/* 创建新标签选项 */}
                            <div className="border-t border-[var(--border-color)]">
                              <div
                                className="flex items-center justify-between p-3 hover:bg-[var(--accent-primary)]/10 transition-colors cursor-pointer"
                                onClick={() => setShowCreateForm(true)}
                              >
                                <div className="flex items-center gap-3">
                                  <FaPlus size={12} className="text-[var(--accent-primary)]" />
                                  <span className="text-[var(--accent-primary)] font-medium">{messages.tagManagement?.createNewTag || 'Create New Tag'}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </>
                      ) : (
                        /* 创建标签表单 */
                        <div className="p-4">
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="text-sm font-medium text-[var(--foreground)]">{messages.tagManagement?.createNewTag || 'Create New Tag'}</h4>
                            <button
                              onClick={() => setShowCreateForm(false)}
                              className="text-[var(--muted)] hover:text-[var(--foreground)]"
                            >
                              <FaTimes size={14} />
                            </button>
                          </div>
                          <div className="space-y-3">
                            {/* 标签名称 */}
                            <div>
                              <label className="block text-xs font-medium text-[var(--foreground)] mb-1">
                                {messages.tagManagement?.tagName || 'Tag Name'} *
                              </label>
                              <input
                                type="text"
                                value={newTagForm.name}
                                onChange={(e) => setNewTagForm(prev => ({ ...prev, name: e.target.value }))}
                                className="w-full px-3 py-2 text-sm bg-[var(--background)] border border-[var(--border-color)] rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] focus:border-transparent text-[var(--foreground)] placeholder-[var(--muted)]"
                                placeholder={messages.tagManagement?.tagNamePlaceholder || 'Enter tag name'}
                                maxLength={60}
                                required
                              />
                            </div>

                            {/* 标签颜色 */}
                            <div className="relative">
                              <label className="block text-xs font-medium text-[var(--foreground)] mb-1">
                                {messages.tagManagement?.tagColor || 'Tag Color'}
                              </label>
                              <div className="flex items-center gap-3">
                                <button
                                  ref={colorButtonRef}
                                  onClick={() => setShowColorPicker(!showColorPicker)}
                                  className="w-8 h-8 rounded border border-[var(--border-color)]"
                                  style={{ backgroundColor: newTagForm.color }}
                                />
                                <input
                                  type="text"
                                  disabled={true}
                                  value={newTagForm.color}
                                  onChange={(e) => setNewTagForm(prev => ({ ...prev, color: e.target.value }))}
                                  className="flex-1 px-3 py-2 text-sm bg-[var(--background)] border border-[var(--border-color)] rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] focus:border-transparent text-[var(--foreground)]"
                                  placeholder={messages.tagManagement?.tagColorPlaceholder || '#FF0000'}
                                />
                              </div>
                              
                              {/* 颜色选择器 */}
                              {showColorPicker && (
                                <div
                                  ref={colorPickerRef}
                                  className="absolute z-10 mt-2 p-3 bg-[var(--background)] border border-[var(--border-color)] rounded-lg shadow-lg grid grid-cols-6 gap-2"
                                >
                                  {PRESET_COLORS.map((color) => (
                                    <button
                                      key={color}
                                      onClick={() => {
                                        setNewTagForm(prev => ({ ...prev, color }));
                                        setShowColorPicker(false);
                                      }}
                                      className="w-6 h-6 rounded border border-[var(--border-color)] hover:scale-110 transition-transform"
                                      style={{ backgroundColor: color }}
                                    />
                                  ))}
                                </div>
                              )}
                            </div>

                            {/* 操作按钮 */}
                            <div className="flex gap-2 pt-2">
                              <button
                                onClick={() => setShowCreateForm(false)}
                                className="flex-1 px-3 py-2 text-sm border border-[var(--border-color)] text-[var(--foreground)] rounded-md hover:bg-[var(--accent-secondary)] transition-colors"
                              >
                                {messages.common?.cancel || 'Cancel'}
                              </button>
                              <button
                                onClick={createNewTag}
                                disabled={isSubmitting || !newTagForm.name.trim()}
                                className="flex-1 px-3 py-2 text-sm bg-[var(--accent-primary)] text-white rounded-md hover:bg-[var(--accent-primary)]/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                {isSubmitting ? (messages.tagManagement?.creating || 'Creating...') : (messages.tagManagement?.createNewTag || 'Create New Tag')}
                              </button>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </TopLayerPortal>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 如果没有标签，显示添加按钮 */}
      {wikiTags.length === 0 && (
        <div className="mt-3">
          <div className="relative" ref={tagDropdownRef}>
            <button
              onClick={handleDropdownToggle}
              className="inline-flex items-center gap-2 px-3 py-2 border border-[var(--border-color)] rounded-md text-[var(--muted)] hover:text-[var(--foreground)] hover:bg-[var(--accent-secondary)] transition-colors focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] focus:border-transparent"
            >
              <FaPlus size={12} />
              <span>{messages.tagManagement?.addTag || 'Add Tag'}</span>
            </button>
            
            {/* 标签下拉框 */}
            {showDropdown && rect && (
              <TopLayerPortal rect={rect} onClose={() => setShowDropdown(false)}>
                <div 
                  className={`absolute top-full mt-1 w-80 bg-[var(--background)] border border-[var(--border-color)] rounded-md shadow-lg z-50 ${
                    dropdownPosition === 'left' ? 'left-0' : 'right-0'
                  }`}
                >
                  {!showCreateForm ? (
                    <>
                      {/* 搜索框 */}
                      <div className="p-3 border-b border-[var(--border-color)]">
                        <div className="relative">
                          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[var(--muted)]" size={14} />
                          <input
                            type="text"
                            placeholder={messages.tagManagement?.searchAndAddTag || 'Search and add tags'}
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="w-full pl-10 pr-3 py-2 text-sm bg-[var(--accent-secondary)] border border-[var(--border-color)] rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] focus:border-transparent text-[var(--foreground)] placeholder-[var(--muted)]"
                          />
                        </div>
                      </div>

                      {/* 标签列表 */}
                      <div className="max-h-48 overflow-y-auto">
                        {availableTags.length === 0 ? (
                          <div className="text-center py-8 text-[var(--muted)]">
                            {searchTerm ? (messages.tagManagement?.noMatchingTags || 'No matching tags found') : (messages.tagManagement?.noAvailableTags || 'No available tags')}
                          </div>
                        ) : (
                          availableTags.map((tag) => (
                            <div
                              key={tag.id}
                              className="flex items-center justify-between p-2 hover:bg-[var(--accent-secondary)] transition-colors cursor-pointer"
                              onClick={() => addTagToWiki(tag.id)}
                            >
                              <div className="flex items-center gap-3">
                                <div
                                  className="w-4 h-4 rounded-full"
                                  style={{ backgroundColor: tag.color }}
                                />
                                <span 
                                  className="text-[var(--foreground)] truncate text-sm w-[120px] text-left"
                                  title={tag.name}
                                >
                                  {tag.name}
                                </span>
                                <span className="text-xs text-[var(--muted)] px-2 py-0.5 bg-[var(--accent-secondary)] rounded flex-shrink-0">
                                  {tag.type === TAG_TYPE.SYSTEM ? messages.tagManagement?.systemTag || 'System Tag' : messages.tagManagement?.userTag || 'User Tag'}
                                </span>
                              </div>
                              <FaPlus size={12} className="text-[var(--muted)]" />
                            </div>
                          ))
                        )}
                        
                        {/* 创建新标签选项 */}
                        <div className="border-t border-[var(--border-color)]">
                          <div
                            className="flex items-center justify-between p-3 hover:bg-[var(--accent-primary)]/10 transition-colors cursor-pointer"
                            onClick={() => setShowCreateForm(true)}
                          >
                            <div className="flex items-center gap-3">
                              <FaPlus size={12} className="text-[var(--accent-primary)]" />
                              <span className="text-[var(--accent-primary)] font-medium">{messages.tagManagement?.createNewTag || 'Create New Tag'}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </>
                  ) : (
                    /* 创建标签表单 */
                    <div className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="text-sm font-medium text-[var(--foreground)]">{messages.tagManagement?.createNewTag || 'Create New Tag'}</h4>
                        <button
                          onClick={() => setShowCreateForm(false)}
                          className="text-[var(--muted)] hover:text-[var(--foreground)]"
                        >
                          <FaTimes size={14} />
                        </button>
                      </div>
                      <div className="space-y-3">
                        {/* 标签名称 */}
                        <div>
                          <label className="block text-xs font-medium text-[var(--foreground)] mb-1">
                            {messages.tagManagement?.tagName || 'Tag Name'} *
                          </label>
                          <input
                            type="text"
                            value={newTagForm.name}
                            onChange={(e) => setNewTagForm(prev => ({ ...prev, name: e.target.value }))}
                            className="w-full px-3 py-2 text-sm bg-[var(--background)] border border-[var(--border-color)] rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] focus:border-transparent text-[var(--foreground)] placeholder-[var(--muted)]"
                            placeholder={messages.tagManagement?.tagNamePlaceholder || 'Enter tag name'}
                            maxLength={60}
                            required
                          />
                        </div>

                        {/* 标签颜色 */}
                        <div className="relative">
                          <label className="block text-xs font-medium text-[var(--foreground)] mb-1">
                            {messages.tagManagement?.tagColor || 'Tag Color'}
                          </label>
                          <div className="flex items-center gap-3">
                            <button
                              ref={colorButtonRef}
                              onClick={() => setShowColorPicker(!showColorPicker)}
                              className="w-8 h-8 rounded border border-[var(--border-color)]"
                              style={{ backgroundColor: newTagForm.color }}
                            />
                            <input
                              type="text"
                              disabled={true}
                              value={newTagForm.color}
                              onChange={(e) => setNewTagForm(prev => ({ ...prev, color: e.target.value }))}
                              className="w-full px-3 py-2 text-sm bg-[var(--background)] border border-[var(--border-color)] rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] focus:border-transparent text-[var(--foreground)]"
                              placeholder={messages.tagManagement?.tagColorPlaceholder || '#FF0000'}
                            />
                          </div>
                          
                          {/* 颜色选择器 */}
                          {showColorPicker && (
                            <div
                              ref={colorPickerRef}
                              className="absolute z-10 mt-2 p-3 bg-[var(--background)] border border-[var(--border-color)] rounded-lg shadow-lg grid grid-cols-6 gap-2"
                            >
                              {PRESET_COLORS.map((color) => (
                                <button
                                  key={color}
                                  onClick={() => {
                                    setNewTagForm(prev => ({ ...prev, color }));
                                    setShowColorPicker(false);
                                  }}
                                  className="w-6 h-6 rounded border border-[var(--border-color)] hover:scale-110 transition-transform"
                                  style={{ backgroundColor: color }}
                                />
                              ))}
                            </div>
                          )}
                        </div>

                        {/* 操作按钮 */}
                        <div className="flex gap-2 pt-2">
                          <button
                            onClick={() => setShowCreateForm(false)}
                            className="flex-1 px-3 py-2 text-sm border border-[var(--border-color)] text-[var(--foreground)] rounded-md hover:bg-[var(--accent-secondary)] transition-colors"
                          >
                            {messages.common?.cancel || 'Cancel'}
                          </button>
                          <button
                            onClick={createNewTag}
                            disabled={isSubmitting || !newTagForm.name.trim()}
                            className="flex-1 px-3 py-2 text-sm bg-[var(--accent-primary)] text-white rounded-md hover:bg-[var(--accent-primary)]/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {isSubmitting ? (messages.tagManagement?.creating || 'Creating...') : (messages.tagManagement?.createNewTag || 'Create New Tag')}
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </TopLayerPortal>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
