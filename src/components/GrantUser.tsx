'use client';

import React, {useState, useEffect, useMemo} from 'react';
import { FaTimes, FaAngleLeft, FaAngleRight, FaAngleUp, FaAngleDown, FaSearch } from 'react-icons/fa';
import { 
  useReactTable, 
  getCoreRowModel, 
  getPaginationRowModel, 
  getFilteredRowModel,
  getSortedRowModel,
  flexRender,
  RowSelectionState,
  PaginationState,
  Table,
  Row
} from '@tanstack/react-table';
import { authFetch } from '@/utils/authFetch';
import { useToast } from '@/contexts/ToastContext';
import { useLanguage } from '@/contexts/LanguageContext';

interface GrantUserProps {
  wiki_id: number;
  project_name: string;
  onClose?: () => void;
  filter?: string;
  visibility?: number;
  onGrantSuccess?: (visibility: number) => void;
}

interface GrantsUser {
  id: string,
  user_name: string,
  user_code: string,
  org?: string,
}

export default function GrantUser({ wiki_id, project_name, onClose, filter = 'F', visibility, onGrantSuccess }: GrantUserProps) {
  const { addToast } = useToast();
  const { messages: t } = useLanguage();
  const [selectedPermission, setSelectedPermission] = useState<string>('6'); // 默认选择查看权限
  
  // 数据状态
  const [allUsers, setAllUsers] = useState<GrantsUser[]>([]);
  const [rightUsers, setRightUsers] = useState<GrantsUser[]>([]);
  
  // 分页状态
  const [leftPagination, setLeftPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  
  const [rightPagination, setRightPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  // 页面大小选项
  const pageSizeOptions = [10, 20, 50];
  
  // 行选择状态 - 使用 Set 来管理跨页选择
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  
  // 右侧表格选择状态
  const [rightTableSelection, setRightTableSelection] = useState<RowSelectionState>({});
  
  // 使用 React Table 内置的全局搜索功能
  const [leftGlobalFilter, setLeftGlobalFilter] = useState('');
  const [rightGlobalFilter, setRightGlobalFilter] = useState('');

  const [isPublicVisible, setIsPublicVisible] = useState(visibility === 1 ? true : false);

  const initVisibility = visibility === 1 ? true : false;
  
  // 检查是否有任何变化（用户选择或全员可见状态变化）
  const hasChanges = useMemo(() => {
    return rightUsers.length > 0 || isPublicVisible !== initVisibility;
  }, [rightUsers.length, isPublicVisible, initVisibility]);


  useEffect(() => {
    const loadData = async () => {
      await loadUsers(wiki_id);
    };

    loadData();
  }, [wiki_id]);

  // 数据加载函数 - 一次性加载所有数据
  const loadUsers = async (project_id?: number) => {
    try {
      const params = new URLSearchParams({
        wiki_id: project_id?.toString() || wiki_id.toString() || '',
        filter: filter
      });

      const response = await authFetch(`/api/wiki/grant/users?${params.toString()}`);
      if (response && response.ok) {
        const data = await response.json();
        setAllUsers(data.users || []);
      }
    } catch (error) {
      console.error('加载用户失败:', error);
      addToast({
        type: 'error',
        title: t.components.grantUser.loadUsersFailed,
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  // 获取部门选项（去重）- 暂时注释掉，因为部门筛选被禁用了
  // const getOrgOptions = useMemo(() => {
  //   const orgs = [...new Set(allUsers.map(user => user.org).filter(Boolean))];
  //   return orgs.map(org => ({ value: org, label: org }));
  // }, [allUsers]);

    // 获取所有已选择的用户
  const selectedUsers = useMemo(() => {
    return allUsers.filter(user => rowSelection[user.id]);
  }, [allUsers, rowSelection]);

  // 获取右侧表格已选择的用户
  const rightSelectedUsers = useMemo(() => {
    return rightUsers.filter(user => rightTableSelection[user.id]);
  }, [rightUsers, rightTableSelection]);

  // 当右侧表格数据变化时，自动调整分页状态
  useEffect(() => {
    if (rightUsers.length > 0) {
      const maxPageIndex = Math.ceil(rightUsers.length / 10) - 1;
      if (rightPagination.pageIndex > maxPageIndex) {
        setRightPagination(prev => ({
          ...prev,
          pageIndex: Math.max(0, maxPageIndex)
        }));
      }
    } else {
      // 如果没有数据，重置到第一页
      setRightPagination({ pageIndex: 0, pageSize: 10 });
    }
  }, [rightUsers.length]);

  // 批量选中逻辑
  const handleBatchSelect = () => {
    if (Object.keys(rowSelection).length > 0) {
      setRightUsers(prev => [...prev, ...selectedUsers]);
      setAllUsers(prev => prev.filter(user => !rowSelection[user.id]));
      setRowSelection({});
    }
  };

  // 批量取消逻辑
  const handleBatchDeselect = () => {
    if (Object.keys(rightTableSelection).length > 0) {
      // 将右侧选中的用户移回左侧
      setAllUsers(prev => [...prev, ...rightSelectedUsers]);
      setRightUsers(prev => prev.filter(user => !rightTableSelection[user.id]));
      setRightTableSelection({});
    }
  };

  // 重置选择
  const resetSelections = () => {
    setRowSelection({});
    setRightTableSelection({});
    setRightUsers([]);
    setSelectedPermission('6');
    setLeftGlobalFilter('');
    setRightGlobalFilter('');
    setLeftPagination({ pageIndex: 0, pageSize: 10 });
    setRightPagination({ pageIndex: 0, pageSize: 10 });
    onClose?.();
  };

  // 定义左侧表格列
  const leftColumns = useMemo(() => [
    {
      id: 'select',
      header: ({ table }: { table: Table<GrantsUser> }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={table.getToggleAllPageRowsSelectedHandler()}
          className="rounded border-[var(--border-color)] text-[var(--accent-primary)] focus:ring-[var(--accent-primary)]"
        />
      ),
      cell: ({ row }: { row: Row<GrantsUser> }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={row.getToggleSelectedHandler()}
          className="rounded border-[var(--border-color)] text-[var(--accent-primary)] focus:ring-[var(--accent-primary)]"
        />
      ),
      enableSorting: false,
      size: 50,
    },
    {
      accessorKey: 'user_name',
      header: t.components.grantUser.userName,
      cell: ({ getValue }: { getValue: () => string }) => (
        <span className="text-sm text-[var(--foreground)]">{getValue()}</span>
      ),
    },
    {
      accessorKey: 'user_code',
      header: t.common.employeeId,
      cell: ({ getValue }: { getValue: () => string }) => (
        <span className="text-sm text-[var(--foreground)]">{getValue()}</span>
      ),
    },
    {
      accessorKey: 'org',
      header: t.common.department,
      cell: ({ getValue }: { getValue: () => string }) => (
        <span className="text-sm text-[var(--muted)]">{getValue() || '-'}</span>
      ),
    },
  ], []);

  // 定义右侧表格列
  const rightColumns = useMemo(() => [
    {
      id: 'select',
      header: ({ table }: { table: Table<GrantsUser> }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={table.getToggleAllPageRowsSelectedHandler()}
          className="rounded border-[var(--border-color)] text-[var(--accent-primary)] focus:ring-[var(--accent-primary)]"
        />
      ),
      cell: ({ row }: { row: Row<GrantsUser> }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={row.getToggleSelectedHandler()}
          className="rounded border-[var(--border-color)] text-[var(--accent-primary)] focus:ring-[var(--accent-primary)]"
        />
      ),
      enableSorting: false,
      size: 50,
    },
    {
      accessorKey: 'user_name',
      header: t.components.grantUser.userName,
      cell: ({ getValue }: { getValue: () => string }) => (
        <span className="text-sm text-[var(--foreground)]">{getValue()}</span>
      ),
    },
    {
      accessorKey: 'user_code',
      header: t.common.employeeId,
      cell: ({ getValue }: { getValue: () => string }) => (
        <span className="text-sm text-[var(--foreground)]">{getValue()}</span>
      ),
    },
    {
      accessorKey: 'org',
      header: t.common.department,
      cell: ({ getValue }: { getValue: () => string }) => (
        <span className="text-sm text-[var(--muted)]">{getValue() || '-'}</span>
      ),
    },
  ], []);

  // 创建左侧表格实例
  const leftTable = useReactTable({
    data: allUsers,
    columns: leftColumns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    
    // 全局搜索配置
    globalFilterFn: 'includesString',
    onGlobalFilterChange: setLeftGlobalFilter,
    
    // 行选择配置
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    
    // 状态
    state: {
      rowSelection,
      pagination: leftPagination,
      globalFilter: leftGlobalFilter,
    },
    
    // 事件处理
    onPaginationChange: setLeftPagination,
    
    // 获取行ID
    getRowId: (row) => row.id,
  });

  // 创建右侧表格实例
  const rightTable = useReactTable({
    data: rightUsers,
    columns: rightColumns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    
    // 全局搜索配置
    globalFilterFn: 'includesString',
    onGlobalFilterChange: setRightGlobalFilter,
    
    // 行选择配置
    enableRowSelection: true,
    onRowSelectionChange: setRightTableSelection,
    
    // 状态
    state: {
      rowSelection: rightTableSelection,
      pagination: rightPagination, // 使用独立的分页状态
      globalFilter: rightGlobalFilter,
    },
    
    // 事件处理
    onPaginationChange: setRightPagination,
    
    // 获取行ID
    getRowId: (row) => row.id,
  });

  return (
    <div className="fixed inset-0 bg-black/60 bg-opacity-50 flex items-center justify-center z-50 backdrop-blur-sm p-4">
      <div className="bg-[var(--card-bg)] border border-[var(--border-color)] rounded-lg p-4 lg:p-6 w-full max-w-7xl min-w-[320px] max-h-[90vh] min-h-[600px] flex flex-col">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-[var(--foreground)]">{t.components.grantUser.title}</h3>
          <button
            onClick={() => { onClose?.() }}
            className="text-[var(--muted)] hover:text-[var(--foreground)] transition-colors"
          >
            <FaTimes className="h-4 w-4" />
          </button>
        </div>

        <div className="mb-4 text-left">
          <p className="text-sm text-[var(--muted)] mb-2">{t.components.grantUser.projectName}：</p>
          <p className="text-[var(--foreground)] font-medium">{project_name}</p>
        </div>

        <div className="mb-4">
              <div className="flex items-center gap-6">
                <div className="text-left">
                  <span className="text-sm text-[var(--foreground)]">{t.components.grantUser.isPublicVisible}</span>
                  <p className="text-xs text-[var(--muted)] mt-1">{t.components.grantUser.isPublicVisibleDescription}</p>
                </div>
                <button
                  type="button"
                  onClick={() => setIsPublicVisible(!isPublicVisible)}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full cursor-pointer transition-colors ${
                    isPublicVisible ? 'bg-[var(--accent-primary)]' : 'bg-[var(--border-color)]'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      isPublicVisible ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
          </div>

        <p className="text-sm text-[var(--muted)] mb-2 text-left">{t.components.grantUser.selectUser}：</p>
        {/* 主要内容区域 */}
        <div className="flex flex-col lg:flex-row gap-4 mb-6 flex-1 overflow-auto">
          {/* 左侧用户表格 */}
          <div className="w-full lg:flex-1">
            {/* 搜索和筛选区域 */}
            <div className="flex flex-col sm:flex-row gap-3 mb-3">
              {/* <div className="flex-1">
                <select
                  value={selectedOrgFilter}
                  onChange={(e) => setSelectedOrgFilter(e.target.value)}
                  className="w-full p-2 border border-[var(--border-color)] rounded-md bg-[var(--background)] text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)]"
                >
                  <option value="">全部部门</option>
                  {getOrgOptions.map(org => (
                    <option key={org.value} value={org.label}>{org.label}</option>
                  ))}
                </select>
              </div> */}
              <div className="w-full sm:flex-1 relative">
                <input
                  type="text"
                  value={leftGlobalFilter ?? ''}
                  onChange={(e) => setLeftGlobalFilter(e.target.value)}
                  placeholder={t.components.grantUser.searchUser}
                  className="w-full p-2 pl-8 border border-[var(--border-color)] rounded-md bg-[var(--background)] text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)]"
                />
                <FaSearch className="absolute left-2 top-1/2 transform -translate-y-1/2 text-[var(--muted)] h-4 w-4" />
              </div>
            </div>

            {/* 左侧表格 */}
            <div className="border border-[var(--border-color)] rounded-md overflow-hidden h-[448px]">
              <table className="w-full">
                <thead className="bg-[var(--accent-secondary)]">
                  {leftTable.getHeaderGroups().map(headerGroup => (
                    <tr key={headerGroup.id}>
                      {headerGroup.headers.map(header => (
                        <th key={header.id} className="px-4 py-2 text-left text-sm font-medium text-[var(--foreground)]">
                          {header.isPlaceholder ? null : (
                            flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )
                          )}
                        </th>
                      ))}
                    </tr>
                  ))}
                </thead>
                <tbody>
                  {leftTable.getRowModel().rows.length > 0 ? 
                  leftTable.getRowModel().rows.map(row => (
                    <tr key={row.id} className="border-t border-[var(--border-color)] hover:bg-[var(--accent-secondary)]">
                      {row.getVisibleCells().map(cell => (
                        <td key={cell.id} className="px-4 py-2">
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </td>
                      ))}
                    </tr>
                  )) : 
                  (
                    <tr>
                      <td colSpan={rightTable.getAllColumns().length} className="px-4 py-16 text-center">
                        <div className="flex flex-col items-center justify-center text-[var(--muted)]">
                          <div className="w-16 h-16 mb-3 rounded-full bg-[var(--accent-secondary)] flex items-center justify-center">
                            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                          </div>
                          <p className="text-sm font-medium">{t.components.grantUser.noUsers}</p>
                        </div>
                      </td>
                    </tr>
                  )
                }
                </tbody>
              </table>
            </div>

            {/* React Table 内置分页控件 */}
            {allUsers.length > 0 && <div className="mt-3 flex flex-col sm:flex-row items-center justify-between sm:justify-end gap-2">
              <div className="flex items-center gap-2">
                {/* 页面大小选择器 */}
                <div className="flex items-center gap-2 text-sm text-[var(--muted)]">
                  <span>{t.components.grantUser.perPage}</span>
                  <select
                    value={leftPagination.pageSize}
                    onChange={(e) => {
                      setLeftPagination(prev => ({
                        ...prev,
                        pageSize: Number(e.target.value),
                        pageIndex: 0 // 切换页面大小时重置到第一页
                      }));
                    }}
                    className="px-2 py-1 text-xs border border-[var(--border-color)] rounded bg-[var(--background)] text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)]"
                  >
                    {pageSizeOptions.map(size => (
                      <option key={size} value={size}>{size}</option>
                    ))}
                  </select>
                  <span>{t.components.grantUser.unit}</span>
                </div>
                <button
                  onClick={() => leftTable.previousPage()}
                  disabled={!leftTable.getCanPreviousPage()}
                  className="px-3 py-1 text-sm border border-[var(--border-color)] rounded-md bg-[var(--background)] text-[var(--foreground)] hover:bg-[var(--accent-secondary)] disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {t.components.grantUser.lastPage}
                </button>
                
                <span className="text-sm text-[var(--muted)]">
                  {t.components.grantUser.paging.replace('{page}', leftTable.getState().pagination.pageIndex + 1).replace('{total}', leftTable.getPageCount())}
                </span>
                
                <button
                  onClick={() => leftTable.nextPage()}
                  disabled={!leftTable.getCanNextPage()}
                  className="px-3 py-1 text-sm border border-[var(--border-color)] rounded-md bg-[var(--background)] text-[var(--foreground)] hover:bg-[var(--accent-secondary)] disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {t.components.grantUser.nextPage}
                </button>
              </div>
            </div>}
          </div>

          {/* 中间操作按钮 */}
          <div className="flex flex-row lg:flex-col gap-3 justify-center lg:px-4 order-2 lg:order-none">
            <div className="flex flex-col gap-2">
              <button
                onClick={handleBatchSelect}
                disabled={Object.keys(rowSelection).length === 0}
                className="px-4 py-2 text-sm bg-[var(--accent-primary)] text-white rounded-md hover:bg-[var(--accent-primary)]/80 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {/* 小屏幕显示下箭头，大屏幕显示右箭头 */}
                <FaAngleDown className="h-4 w-4 lg:hidden" />
                <FaAngleRight className="h-4 w-4 hidden lg:block" />
                {t.components.grantUser.selected}
              </button>
              {/* <button
                onClick={handleSelectAll}
                disabled={filteredUsers.length === 0}
                className="px-4 py-2 text-sm bg-[var(--accent-primary)] text-white rounded-md hover:bg-[var(--accent-primary)]/80 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                <FaCheckDouble className="h-3 w-3" />
                全部选中
              </button> */}
            </div>
            
            <div className="flex flex-col gap-2">
              <button
                onClick={handleBatchDeselect}
                disabled={Object.keys(rightTableSelection).length === 0}
                className="px-4 py-2 text-sm bg-[var(--accent-secondary)] text-[var(--foreground)] rounded-md hover:bg-[var(--accent-secondary)]/80 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {/* 小屏幕显示上箭头，大屏幕显示左箭头 */}
                <FaAngleUp className="h-4 w-4 lg:hidden" />
                <FaAngleLeft className="h-4 w-4 hidden lg:block" />
                {t.common.cancel}
              </button>
              {/* <button
                onClick={ }
                disabled={rightUsers.length === 0}
                className="px-4 py-2 text-sm bg-[var(--accent-secondary)] text-[var(--foreground)] rounded-md hover:bg-[var(--accent-secondary)]/80 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                <FaTimesCircle className="h-3 w-3" />
                全部取消
              </button> */}
            </div>
          </div>

          {/* 右侧已选用户表格 */}
          <div className="w-full lg:flex-1 order-3 lg:order-none">

            {/* 右侧表格搜索 */}
            <div className="mb-3">
              <div className="relative">
                <input
                  type="text"
                  value={rightGlobalFilter ?? ''}
                  onChange={(e) => setRightGlobalFilter(e.target.value)}
                  placeholder={t.components.grantUser.searchSelectedUser}
                  className="w-full p-2 pl-8 border border-[var(--border-color)] rounded-md bg-[var(--background)] text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)]"
                />
                <FaSearch className="absolute left-2 top-1/2 transform -translate-y-1/2 text-[var(--muted)] h-4 w-4" />
              </div>
            </div>

            {/* 右侧表格 */}
            <div className="border border-[var(--border-color)] rounded-md overflow-hidden h-[448px]">
              <table className="w-full">
                <thead className="bg-[var(--accent-secondary)]">
                  {rightTable.getHeaderGroups().map(headerGroup => (
                    <tr key={headerGroup.id}>
                      {headerGroup.headers.map(header => (
                        <th key={header.id} className="px-4 py-2 text-left text-sm font-medium text-[var(--foreground)]">
                          {header.isPlaceholder ? null : (
                            flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )
                          )}
                        </th>
                      ))}
                    </tr>
                  ))}
                </thead>
                <tbody>
                  {rightTable.getRowModel().rows.length > 0 ? (
                    rightTable.getRowModel().rows.map(row => (
                      <tr key={row.id} className="border-t border-[var(--border-color)] hover:bg-[var(--accent-secondary)]">
                        {row.getVisibleCells().map(cell => (
                          <td key={cell.id} className="px-4 py-2">
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </td>
                        ))}
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={rightTable.getAllColumns().length} className="px-4 py-16 text-center">
                        <div className="flex flex-col items-center justify-center text-[var(--muted)]">
                          <div className="w-16 h-16 mb-3 rounded-full bg-[var(--accent-secondary)] flex items-center justify-center">
                            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                          </div>
                          <p className="text-sm font-medium">{t.components.grantUser.noSelectedUsers}</p>
                          <p className="text-xs mt-1">{t.components.grantUser.selectUserInLeftTable}</p>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {/* 右侧表格分页控件 */}
            {rightUsers.length > 0 && (
              <div className="mt-3 flex flex-col sm:flex-row items-center justify-between sm:justify-end gap-2">
                <div className="flex items-center gap-2">
                  {/* 页面大小选择器 */}
                  <div className="flex items-center gap-2 text-sm text-[var(--muted)]">
                    <span>{t.components.grantUser.perPage}</span>
                    <select
                      value={rightPagination.pageSize}
                      onChange={(e) => {
                        setRightPagination(prev => ({
                          ...prev,
                          pageSize: Number(e.target.value),
                          pageIndex: 0 // 切换页面大小时重置到第一页
                        }));
                      }}
                      className="px-2 py-1 text-xs border border-[var(--border-color)] rounded bg-[var(--background)] text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)]"
                    >
                      {pageSizeOptions.map(size => (
                        <option key={size} value={size}>{size}</option>
                      ))}
                    </select>
                    <span>{t.components.grantUser.unit}</span>
                  </div>
                  <button
                    onClick={() => rightTable.previousPage()}
                    disabled={!rightTable.getCanPreviousPage()}
                    className="px-3 py-1 text-sm border border-[var(--border-color)] rounded-md bg-[var(--background)] text-[var(--foreground)] hover:bg-[var(--accent-secondary)] disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {t.components.grantUser.lastPage}
                  </button>
                  
                  <span className="text-sm text-[var(--muted)]">
                    {t.components.grantUser.paging.replace('{page}', rightTable.getState().pagination.pageIndex + 1).replace('{total}', rightTable.getPageCount())}
                  </span>
                  
                  <button
                    onClick={() => rightTable.nextPage()}
                    disabled={!rightTable.getCanNextPage()}
                    className="px-3 py-1 text-sm border border-[var(--border-color)] rounded-md bg-[var(--background)] text-[var(--foreground)] hover:bg-[var(--accent-secondary)] disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {t.components.grantUser.nextPage}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="mt-auto">
          <div className="mb-6 text-left">
            <label className="block text-sm text-[var(--muted)] mb-2">{t.components.grantUser.selectPriv}</label>
            <div className="relative w-full sm:w-auto">
              <select
                value={selectedPermission}
                onChange={(e) => setSelectedPermission(e.target.value)}
                className="w-full p-2 pr-8 border border-[var(--border-color)] rounded-md bg-[var(--background)] text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)] appearance-none"
              >
                <option value="6">{t.components.grantUser.wikiUser}</option>
                <option value="5">{t.components.grantUser.wikiAdmin}</option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <svg className="w-4 h-4 text-[var(--muted)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-end gap-2">
            <button
              onClick={resetSelections}
              className="px-4 py-2 text-sm text-[var(--muted)] hover:text-[var(--foreground)] transition-colors order-2 sm:order-none"
            >
              {t.common.cancel}
            </button>
            <button
              onClick={async () => {
                if (rightUsers.length > 0 || hasChanges) {
                  try {
                    const response = await authFetch('/api/wiki/grant', {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({
                        wiki_id: wiki_id,
                        users: rightUsers.map(user => user.id),
                        role_id: selectedPermission,
                        visibility: isPublicVisible,
                      }),
                    });

                    if (response && response.ok) {
                      addToast({
                        type: 'success',
                        title: t.components.grantUser.grantSuccess,
                        message: ''
                      });
                      onGrantSuccess?.(isPublicVisible ? 1 : 2);
                    } else {
                      addToast({
                        type: 'error',
                        title: t.components.grantUser.grantFailed,
                        message: ''
                      });
                    }
                  } catch (error) {
                    addToast({
                      type: 'error',
                      title: t.components.grantUser.grantFailed,
                      message: error instanceof Error ? error.message : 'Unknown error'
                    });
                  }
                  resetSelections();
                  onClose?.();
                }
              }}
              disabled={!hasChanges}
              className={`px-4 py-2 text-sm rounded-md transition-colors ${
                hasChanges
                  ? 'bg-[var(--accent-primary)] text-white hover:bg-[var(--accent-primary)]/80' 
                  : 'bg-[var(--border-color)] text-[var(--muted)] cursor-not-allowed'
              }`}
            >
              {t.components.grantUser.confirmGrant}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}