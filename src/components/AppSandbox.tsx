"use client";

import React, { useEffect, useMemo, useState, useRef } from "react";
import { useToast } from "@/contexts/ToastContext";
import { authFetch } from "@/utils/authFetch";
import { getSandboxStatusDescription, getSandboxStatusStyle } from "@/components/k8s/status";
import { FaTimes, FaPlay, FaPause, FaRedo } from "react-icons/fa";
import { useLanguage } from "@/contexts/LanguageContext";

interface AppSandboxProps {
  app_id: number;
  app_name: string;
  app_code: string;
  onClose: () => void;
}

type SandboxStatus =
  | "NOT_CREATED"
  | "CREATING"
  | "INITIALIZING"
  | "READY"
  | "FAILED"
  | "QUERY_FAILED";

interface SandboxStateResp {
  status: SandboxStatus | string;
  job_name?: string;
  pod_ip?: string;
  message?: string;
  creation_time?: string;
  namespace?: string;
  environment?: string;
  creator_id?: string;
  creator_code?: string;
  creator_name?: string;
}

// 状态样式与文案由 k8s/status 提供

const AppSandbox = (props: AppSandboxProps) => {
  const { app_id, app_code, app_name, onClose } = props;
  const { addToast } = useToast();
  const { messages, language } = useLanguage();

  const [loading, setLoading] = useState<boolean>(false);
  const [statusLoading, setStatusLoading] = useState<boolean>(false);
  const [status, setStatus] = useState<SandboxStateResp | null>(null);
  const [isOperationInProgress, setIsOperationInProgress] = useState<boolean>(false);
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const refreshStatus = async () => {
    setStatusLoading(true);
    try {
      const resp = await authFetch(`/api/k8s/sandbox/app/${app_id}/status`);
      if (resp && resp.ok) {
        const json = await resp.json();
        if (json && json.success) {
          const newStatus = json.data as SandboxStateResp;
          setStatus(newStatus);
          
          // 如果沙箱已经就绪，停止定时刷新并启用按钮
          if (newStatus.status === "READY") {
            setIsOperationInProgress(false);
            stopAutoRefresh();
          }
        } else {
          setStatus({ status: "QUERY_FAILED", message: json?.error });
        }
      } else {
        setStatus({ status: "QUERY_FAILED", message: "Request failed" });
      }
    } catch (e: unknown) {
      const message = e instanceof Error ? e.message : "";
      setStatus({ status: "QUERY_FAILED", message });
    } finally {
      setStatusLoading(false);
    }
  };

  const startAutoRefresh = () => {
    if (refreshIntervalRef.current) {
      clearInterval(refreshIntervalRef.current);
    }
    refreshIntervalRef.current = setInterval(refreshStatus, 3000); // 每3秒刷新一次
  };

  const stopAutoRefresh = () => {
    if (refreshIntervalRef.current) {
      clearInterval(refreshIntervalRef.current);
      refreshIntervalRef.current = null;
    }
  };

  const startSandbox = async () => {
    setLoading(true);
    setIsOperationInProgress(true);
    try {
      const resp = await authFetch(`/api/k8s/sandbox/app/${app_id}`, {
        method: "POST",
      });
      if (!resp) {
        addToast({ type: "error", title: messages.authApp.appSandbox, message: messages.authApp.startFailed });
        setIsOperationInProgress(false);
        return;
      }
      const response = resp as Response;
      const ok = response.ok;
      const json = ok ? await response.json() : undefined;
      if (ok && json && (json as { success?: boolean; error?: string }).success) {
        addToast({ type: "success", title: messages.authApp.appSandbox, message: messages.authApp.startSuccess });
        await refreshStatus();
        startAutoRefresh(); // 开始定时刷新
      } else {
        addToast({ type: "error", title: messages.authApp.appSandbox, message: (json as { error?: string } | undefined)?.error || messages.authApp.startFailed });
        setIsOperationInProgress(false);
      }
    } catch (e: unknown) {
      const message = e instanceof Error ? e.message : messages.authApp.startFailed;
      addToast({ type: "error", title: messages.authApp.appSandbox, message });
      setIsOperationInProgress(false);
    } finally {
      setLoading(false);
    }
  };

  const stopSandbox = async () => {
    setLoading(true);
    try {
      const resp = await authFetch(`/api/k8s/sandbox/app/${app_id}`, { method: "DELETE" });
      if (!resp) {
        addToast({ type: "error", title: messages.authApp.appSandbox, message: messages.authApp.stopFailed });
        return;
      }
      const response = resp as Response;
      const ok = response.ok;
      const json = ok ? await response.json() : undefined;
      if (ok && json && (json as { success?: boolean; error?: string }).success) {
        addToast({ type: "success", title: messages.authApp.appSandbox, message: messages.authApp.stopSuccess });
        stopAutoRefresh(); // 停止定时刷新
        setIsOperationInProgress(false);
        await refreshStatus();
      } else {
        addToast({ type: "error", title: messages.authApp.appSandbox, message: (json as { error?: string } | undefined)?.error || messages.authApp.stopFailed });
      }
    } catch (e: unknown) {
      const message = e instanceof Error ? e.message : messages.authApp.stopFailed;
      addToast({ type: "error", title: messages.authApp.appSandbox, message });
    } finally {
      setLoading(false);
    }
  };

  const restartSandbox = async () => {
    setLoading(true);
    setIsOperationInProgress(true);
    try {
      // 先停止
      const stopResp = await authFetch(`/api/k8s/sandbox/app/${app_id}`, { method: "DELETE" });
      if (stopResp && stopResp.ok) {
        const stopJson = await stopResp.json();
        if (stopJson && stopJson.success) {
          // 停止成功后启动
          const startResp = await authFetch(`/api/k8s/sandbox/app/${app_id}`, { method: "POST" });
          if (startResp && startResp.ok) {
            const startJson = await startResp.json();
            if (startJson && startJson.success) {
              addToast({ type: "success", title: messages.authApp.appSandbox, message: messages.authApp.restart + " " + messages.authApp.startSuccess });
              await refreshStatus();
              startAutoRefresh(); // 开始定时刷新
            } else {
              addToast({ type: "error", title: messages.authApp.appSandbox, message: messages.authApp.startFailed });
              setIsOperationInProgress(false);
            }
          } else {
            addToast({ type: "error", title: messages.authApp.appSandbox, message: messages.authApp.startFailed });
            setIsOperationInProgress(false);
          }
        } else {
          addToast({ type: "error", title: messages.authApp.appSandbox, message: messages.authApp.stopFailed });
          setIsOperationInProgress(false);
        }
      } else {
        addToast({ type: "error", title: messages.authApp.appSandbox, message: messages.authApp.stopFailed });
        setIsOperationInProgress(false);
      }
    } catch (e: unknown) {
      const message = e instanceof Error ? e.message : messages.authApp.startFailed;
      addToast({ type: "error", title: messages.authApp.appSandbox, message });
      setIsOperationInProgress(false);
    } finally {
      setLoading(false);
    }
  };

  const isRunning = useMemo(() => (status?.status || "").toUpperCase() === "READY", [status]);

  useEffect(() => {
    refreshStatus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 清理定时器
  useEffect(() => {
    return () => {
      stopAutoRefresh();
    };
  }, []);

  return (
    <div className="fixed inset-0 bg-black/60 bg-opacity-100 flex items-center justify-center z-50 p-4 backdrop-blur-sm text-start">
      <div className="bg-[var(--card-bg)] rounded-lg shadow-2xl border border-[var(--border-color)] w-full max-w-4xl max-h-[95vh] overflow-hidden flex flex-col">
        <div className="p-6 border-b border-[var(--border-color)] flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-indigo-500 text-white grid place-items-center">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-[var(--foreground)] select-none">
              {messages.authApp.appSandbox}
            </h3>
          </div>
          <button
            onClick={onClose}
            className="text-[var(--muted)] hover:text-[var(--foreground)] transition-colors"
          >
            <FaTimes size={16} />
          </button>
        </div>

        <div className="p-6 flex-1 overflow-y-auto">
          {/* 沙箱信息卡片 */}
          <div className="border border-[var(--border-color)] rounded-lg overflow-hidden mb-6">
            <div className="p-4">
              <div className="grid grid-cols-2 gap-4 text-sm select-none">
                <div className="space-y-3">
                  <div>
                    <div className="text-[var(--muted)] text-xs mb-1">{messages.authApp.sandboxStatus}</div>
                    <div>
                      {(() => {
                        const st = (status?.status || "").toUpperCase();
                        if (st === "QUERY_FAILED") return <span className="text-red-500">查询失败</span>;
                        if (!st || st === "NOT_CREATED") return <span className="text-[var(--muted)]">{messages.authApp.notCreated}</span>;
                        const tip = getSandboxStatusDescription(st, language);
                        const badge = getSandboxStatusStyle(st);
                        return <span className={badge} title={tip}>{tip}</span>;
                      })()}
                    </div>
                  </div>
                  <div>
                    <div className="text-[var(--muted)] text-xs mb-1">{messages.authApp.appName}</div>
                    <div className="text-[var(--foreground)] truncate" title={app_name}>{app_name}</div>
                  </div>
                  <div>
                    <div className="text-[var(--muted)] text-xs mb-1">{messages.authApp.appCode}</div>
                    <div className="text-[var(--foreground)] truncate" title={app_code}>{app_code}</div>
                  </div>
                </div>
                <div className="space-y-3">
                  <div>
                    <div className="text-[var(--muted)] text-xs mb-1">{messages.authApp.sandboxName}</div>
                    <div className="text-[var(--foreground)] truncate" title={status?.job_name}>{status?.job_name || "-"}</div>
                  </div>
                  <div>
                    <div className="text-[var(--muted)] text-xs mb-1">{messages.authApp.podIp}</div>
                    <div className="text-[var(--foreground)]">{status?.pod_ip || "-"}</div>
                  </div>
                  <div>
                    <div className="text-[var(--muted)] text-xs mb-1">{messages.authApp.creator}</div>
                    <div className="text-[var(--foreground)]">
                      {(() => {
                        const name = status?.creator_name || "";
                        const code = status?.creator_code || status?.creator_id || "-";
                        return name ? `${name}(${code})` : code;
                      })()}
                    </div>
                  </div>
                  <div>
                    <div className="text-[var(--muted)] text-xs mb-1">{messages.authApp.createdTime}</div>
                    <div className="text-[var(--foreground)]">
                      {status?.creation_time ? new Date(status.creation_time).toLocaleString() : "-"}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end items-center gap-2 select-none">
            <button
              disabled={statusLoading}
              onClick={refreshStatus}
              className="px-3 py-1.5 text-sm font-medium rounded-md border border-[var(--border-color)] text-[var(--accent-primary)] hover:bg-[var(--accent-primary)]/10 transition-colors cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {messages.common.refresh}
            </button>
            {!isRunning ? (
              <button
                disabled={loading || isOperationInProgress}
                onClick={startSandbox}
                className="px-4 py-2 text-sm font-medium rounded-md border border-transparent bg-[var(--accent-primary)]/90 text-white hover:bg-[var(--accent-primary)] transition-colors cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FaPlay className="inline mr-1" size={12} />
                {messages.authApp.start}
              </button>
            ) : (
              <button
                disabled={loading || isOperationInProgress}
                onClick={stopSandbox}
                className="px-4 py-2 text-sm font-medium rounded-md border border-transparent bg-amber-600 text-white hover:bg-amber-700 transition-colors cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FaPause className="inline mr-1" size={12} />
                {messages.authApp.pause}
              </button>
            )}
            <button
              disabled={loading || isOperationInProgress}
              onClick={restartSandbox}
              className="px-4 py-2 text-sm font-medium rounded-md border border-transparent bg-gray-700 text-white hover:bg-gray-800 transition-colors cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <FaRedo className="inline mr-1" size={12} />
              {messages.authApp.restart}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppSandbox;


