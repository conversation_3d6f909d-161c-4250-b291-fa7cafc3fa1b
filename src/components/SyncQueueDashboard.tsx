'use client'

import React, { useCallback, useEffect, useMemo, useState } from 'react'
import type { JSX } from 'react'
import { useLanguage } from '@/contexts/LanguageContext'
import { authFetch } from '@/utils/authFetch'

interface QueueItem {
  queue_key?: string
  job_id?: string
  wiki_id?: string
  repo_id?: number
  repo_url?: string
  branch?: string
  namespace?: string | null
  strategy?: string
  enqueued_at?: string
  started_at?: string
}

interface ActiveJob {
  job_id?: string
  wiki_id?: string
  status?: string
  created_at?: string
  updated_at?: string
}

interface QueueMetrics {
  global_limit?: number
  configured_global_limit?: number
  instance_limit?: number
  local_limit?: number
  thread_pool_limit?: number
  active_slots?: number
  available_slots?: number
  available_slots_global?: number
  slot_ttl_seconds?: number
  job_ttl_seconds?: number
  pending_count?: number
  running_count?: number
  active_job_count?: number
  redis_status?: string
}

interface RedisKeys {
  global_slot_key?: string
  waiting_queue_key?: string
  pending_list_key?: string
  pending_meta_key?: string
  running_meta_key?: string
  job_registry_key?: string
  wiki_active_key?: string
  task_store_prefix?: string
  latest_job_key?: string
  summary_cache_key?: string
  summary_refresh_key?: string
}

interface QueueSnapshot {
  pending: QueueItem[]
  running: QueueItem[]
  active_jobs: ActiveJob[]
  metrics?: QueueMetrics
  redis_keys?: RedisKeys
}

const POLL_INTERVAL = 5000

const REDIS_KEY_LABELS: { key: keyof RedisKeys; label: string }[] = [
  { key: 'global_slot_key', label: '全局槽位计数' },
  { key: 'waiting_queue_key', label: '等待队列列表' },
  { key: 'pending_list_key', label: '排队快照列表' },
  { key: 'pending_meta_key', label: '排队任务元数据' },
  { key: 'running_meta_key', label: '运行任务元数据' },
  { key: 'job_registry_key', label: '作业注册表' },
  { key: 'wiki_active_key', label: 'Wiki 活跃映射' },
  { key: 'task_store_prefix', label: '任务持久化前缀' },
  { key: 'latest_job_key', label: '最新作业键格式' },
  { key: 'summary_cache_key', label: '同步摘要缓存键格式' },
  { key: 'summary_refresh_key', label: '摘要刷新锁键格式' },
]

type SummaryStats = {
  pendingCount: number
  runningCount: number
  jobCount: number
  activeSlots: number
  availableLocalSlots: number
  availableGlobalSlots: number
  configuredGlobalLimit: number
  effectiveGlobalLimit: number
  instanceLimit: number
  localLimit: number
  threadLimit: number
}

// 顶部概览卡片的配置结构，方便统一渲染风格
type OverviewCard = {
  key: string
  label: string
  hint: string
  valueText: string
  ratio: number
  containerClass: string
  borderClass: string
  progressColor: string
  icon: JSX.Element
  footer?: string
}

export function SyncQueueDashboard(): JSX.Element {
  // 语言上下文，用于读取多语言文案
  const { messages } = useLanguage()
  // 控制刷新状态
  const [loading, setLoading] = useState(false)
  // 用于展示错误信息
  const [error, setError] = useState<string | null>(null)
  // 用于缓存后端返回的队列快照
  const [snapshot, setSnapshot] = useState<QueueSnapshot>({ pending: [], running: [], active_jobs: [], metrics: {}, redis_keys: {} })
  // 记录最近一次刷新完成的时间
  const [lastUpdated, setLastUpdated] = useState<string>('')
  // 控制 Redis 详情显示
  const [showRedisDetails, setShowRedisDetails] = useState(false)

  // 拉取控制台数据的异步方法
  const fetchSnapshot = useCallback(async () => {
    setLoading(true)
    setError(null)
    try {
      const response = await authFetch('/api/wiki/sync-overview', { cache: 'no-store' })
      if (!response?.ok) {
        throw new Error(`请求失败，状态码：${response?.status}`)
      }
      const data = (await response.json()) as QueueSnapshot
      setSnapshot({
        pending: Array.isArray(data?.pending) ? data.pending : [],
        running: Array.isArray(data?.running) ? data.running : [],
        active_jobs: Array.isArray(data?.active_jobs) ? data.active_jobs : [],
        metrics: data?.metrics || {},
        redis_keys: data?.redis_keys || {},
      })
      setLastUpdated(new Date().toLocaleString())
    } catch (err) {
      console.error('加载同步队列失败', err)
      setError((err as Error).message)
    } finally {
      setLoading(false)
    }
  }, [])

  // 组件挂载时立即拉取一次数据，并建立轮询
  useEffect(() => {
    let timer: NodeJS.Timeout | null = null
    fetchSnapshot()
    timer = setInterval(fetchSnapshot, POLL_INTERVAL)
    return () => {
      if (timer) {
        clearInterval(timer)
      }
    }
  }, [fetchSnapshot])

  // 计算关键指标，方便在页面顶部快速查看
  const summary = useMemo<SummaryStats>(
    () => {
      const metrics = snapshot.metrics ?? {}
      const pendingCount = metrics.pending_count ?? snapshot.pending.length
      const runningCount = metrics.running_count ?? snapshot.running.length
      const jobCount = metrics.active_job_count ?? snapshot.active_jobs.length
      const activeSlots = metrics.active_slots ?? 0
      const availableLocalSlots = metrics.available_slots ?? 0
      const availableGlobalSlots = metrics.available_slots_global ?? availableLocalSlots
      const configuredGlobalLimit = metrics.configured_global_limit ?? metrics.global_limit ?? activeSlots + availableGlobalSlots
      const effectiveGlobalLimit = metrics.global_limit ?? configuredGlobalLimit
      const instanceLimit = metrics.instance_limit ?? metrics.local_limit ?? metrics.thread_pool_limit ?? configuredGlobalLimit
      const localLimit = metrics.local_limit ?? Math.min(instanceLimit, effectiveGlobalLimit)
      const threadLimit = metrics.thread_pool_limit ?? 0
      return {
        pendingCount,
        runningCount,
        jobCount,
        activeSlots,
        availableLocalSlots,
        availableGlobalSlots,
        configuredGlobalLimit,
        effectiveGlobalLimit,
        instanceLimit,
        localLimit,
        threadLimit,
      }
    },
    [snapshot],
  )

  // 顶部概览卡片的配置清单，便于统一样式与动画
  const overviewCards = useMemo<OverviewCard[]>(() => {
    const safeConfiguredLimit = summary.configuredGlobalLimit || 1
    const safeBase = safeConfiguredLimit === 0 ? 1 : safeConfiguredLimit
    const safeSlotsBase = summary.configuredGlobalLimit && summary.configuredGlobalLimit > 0 ? summary.configuredGlobalLimit : 1
    return [
      {
        key: 'pending',
        label: '排队任务',
        hint: '等待调度',
        valueText: `${summary.pendingCount}`,
        ratio: Math.min(100, (summary.pendingCount / safeBase) * 100),
        containerClass: 'bg-white/80 dark:bg-gray-800/80',
        borderClass: 'border-gray-200 dark:border-gray-700',
        progressColor: 'bg-blue-500',
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10m-9 4h8m-9 4h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2h-3M5 5H4a2 2 0 00-2 2v12a2 2 0 002 2h1" />
          </svg>
        ),
      },
      {
        key: 'running',
        label: '运行中',
        hint: '实时处理',
        valueText: `${summary.runningCount}`,
        ratio: Math.min(100, (summary.runningCount / safeBase) * 100),
        containerClass: 'bg-white/80 dark:bg-gray-800/80',
        borderClass: 'border-gray-200 dark:border-gray-700',
        progressColor: 'bg-green-500',
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        ),
      },
      {
        key: 'jobs',
        label: '活跃作业',
        hint: '实时追踪',
        valueText: `${summary.jobCount}`,
        ratio: Math.min(100, (summary.jobCount / safeBase) * 100),
        containerClass: 'bg-white/80 dark:bg-gray-800/80',
        borderClass: 'border-gray-200 dark:border-gray-700',
        progressColor: 'bg-purple-500',
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
        ),
      },
      {
        key: 'slots',
        label: '全局槽位',
        hint: '活跃/上限',
        valueText: `${summary.activeSlots}/${summary.configuredGlobalLimit || '-'}`,
        ratio: Math.min(100, (summary.activeSlots / safeSlotsBase) * 100),
        containerClass: 'bg-white/80 dark:bg-gray-800/80',
        borderClass: 'border-gray-200 dark:border-gray-700',
        progressColor: 'bg-amber-500',
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-amber-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V9m-7-7l7 7" />
          </svg>
        ),
        footer: `全局可用：${summary.availableGlobalSlots} ｜ 实例可用：${summary.availableLocalSlots}`,
      },
    ]
  }, [summary])

  return (
    <section className="relative space-y-6 overflow-hidden rounded-2xl border border-gray-200 bg-white/80 dark:border-gray-700 dark:bg-gray-800/80 backdrop-blur-xl p-6 shadow-lg transition-all hover:shadow-xl">
      <header className="relative z-10 space-y-5">
        <div className="flex flex-col gap-4 rounded-xl border border-gray-200 bg-white/60 dark:border-gray-700 dark:bg-gray-800/60 p-4 sm:flex-row sm:items-center sm:justify-between sm:p-5 backdrop-blur-sm">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              {messages?.syncQueue?.title || '同步任务监控'}
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {messages?.syncQueue?.description || '实时查看同步任务的排队与运行状态。'}
            </p>
          </div>
          <button
            type="button"
            onClick={fetchSnapshot}
            disabled={loading}
            className="flex items-center justify-center gap-2 rounded-lg bg-blue-500 px-5 py-2.5 text-sm font-semibold text-white shadow-md transition-all hover:bg-blue-600 hover:shadow-lg disabled:cursor-not-allowed disabled:opacity-60"
          >
            {loading ? (
              <>
                <span className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></span>
                刷新中...
              </>
            ) : (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                手动刷新
              </>
            )}
          </button>
        </div>
        
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {overviewCards.map((card) => (
            <div
              key={card.key}
              className={`group relative overflow-hidden rounded-xl border ${card.borderClass} ${card.containerClass} p-5 shadow-sm transition-all hover:-translate-y-0.5 hover:shadow-md backdrop-blur-sm`}
            >
              <div className="flex items-start justify-between">
                <div>
                  <p className="text-xs uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    {card.hint}
                  </p>
                  <p className="mt-2 text-2xl font-bold text-gray-900 dark:text-white">
                    {card.valueText}
                  </p>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">{card.label}</p>
                </div>
                <div className="rounded-full bg-gray-100 dark:bg-gray-700 p-2 shadow-inner">
                  {card.icon}
                </div>
              </div>
              <div className="mt-4 h-1.5 w-full overflow-hidden rounded-full bg-gray-100 dark:bg-gray-700">
                <div
                  className={`h-full rounded-full ${card.progressColor}`}
                  style={{ width: `${card.ratio}%` }}
                ></div>
              </div>
              {card.footer && (
                <p className="mt-3 text-xs text-gray-500 dark:text-gray-400">
                  {card.footer}
                </p>
              )}
            </div>
          ))}
        </div>
        <div className="flex flex-wrap items-center justify-between gap-3 pt-2">
          <p className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
            <span className={`h-2.5 w-2.5 rounded-full ${loading ? 'bg-blue-500 animate-pulse' : 'bg-green-500'}`}></span>
            {loading ? '正在刷新数据...' : `最后更新：${lastUpdated || '暂无'}`}
          </p>
          {error && (
            <div className="flex items-center gap-2 rounded-full border border-red-500/30 bg-red-500/10 px-3 py-1.5 text-sm text-red-500">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              加载失败：{error}
            </div>
          )}
        </div>
      </header>

      <div className="grid gap-6 lg:grid-cols-2">
        <div className="relative overflow-hidden rounded-xl border border-gray-200 bg-white/80 dark:border-gray-700 dark:bg-gray-800/80 p-5 shadow-md transition-all hover:shadow-lg backdrop-blur-sm">
          <div className="relative z-10">
            <div className="mb-4 flex items-center justify-between">
              <h3 className="flex items-center gap-2 text-lg font-semibold text-gray-900 dark:text-white">
                <span className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/50 text-blue-500 dark:text-blue-400">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                </span>
                排队任务
              </h3>
              <span className="rounded-full border border-blue-200 bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800 dark:border-blue-800/30 dark:bg-blue-900/30 dark:text-blue-300">
                {snapshot.pending.length}
              </span>
            </div>
            {snapshot.pending.length === 0 ? (
              <div className="flex flex-col items-center justify-center gap-3 rounded-xl border border-dashed border-gray-200 bg-gray-50 py-10 text-center dark:border-gray-700 dark:bg-gray-800/50">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <p className="text-sm text-gray-500 dark:text-gray-400">当前没有排队任务</p>
              </div>
            ) : (
              <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800">
                <table className="min-w-full text-xs sm:text-sm">
                  <thead className="bg-gray-50 text-gray-500 dark:bg-gray-700/50 dark:text-gray-400">
                    <tr>
                      <th className="px-4 py-3 text-left text-[0.65rem] font-semibold uppercase tracking-widest">Job ID</th>
                      <th className="px-4 py-3 text-left text-[0.65rem] font-semibold uppercase tracking-widest">Wiki</th>
                      <th className="px-4 py-3 text-left text-[0.65rem] font-semibold uppercase tracking-widest">仓库</th>
                      <th className="px-4 py-3 text-left text-[0.65rem] font-semibold uppercase tracking-widest">入队时间</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                    {snapshot.pending.map((item, index) => (
                      <tr key={item.queue_key || index} className="odd:bg-white dark:odd:bg-gray-800/50 transition-colors hover:bg-gray-50 dark:hover:bg-gray-700/50">
                        <td className="px-4 py-3 font-mono text-xs text-gray-900 dark:text-gray-200">{item.job_id}</td>
                        <td className="px-4 py-3 text-xs text-gray-900 dark:text-gray-200">{item.wiki_id || '-'}</td>
                        <td className="px-4 py-3 text-xs text-gray-900 dark:text-gray-200">
                          <span className="inline-flex max-w-[180px] break-all font-mono">{item.repo_url || '-'}</span>
                        </td>
                        <td className="px-4 py-3 text-xs text-gray-500 dark:text-gray-400">{item.enqueued_at ? new Date(item.enqueued_at).toLocaleString() : '-'}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>

        <div className="relative overflow-hidden rounded-xl border border-gray-200 bg-white/80 dark:border-gray-700 dark:bg-gray-800/80 p-5 shadow-md transition-all hover:shadow-lg backdrop-blur-sm">
          <div className="relative z-10">
            <div className="mb-4 flex items-center justify-between">
              <h3 className="flex items-center gap-2 text-lg font-semibold text-gray-900 dark:text-white">
                <span className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/50 text-green-500 dark:text-green-400">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </span>
                运行中任务
              </h3>
              <span className="rounded-full border border-green-200 bg-green-100 px-3 py-1 text-xs font-medium text-green-800 dark:border-green-800/30 dark:bg-green-900/30 dark:text-green-300">
                {snapshot.running.length}
              </span>
            </div>
            {snapshot.running.length === 0 ? (
              <div className="flex flex-col items-center justify-center gap-3 rounded-xl border border-dashed border-gray-200 bg-gray-50 py-10 text-center dark:border-gray-700 dark:bg-gray-800/50">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-sm text-gray-500 dark:text-gray-400">当前没有运行中的任务</p>
              </div>
            ) : (
              <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800">
                <table className="min-w-full text-xs sm:text-sm">
                  <thead className="bg-gray-50 text-gray-500 dark:bg-gray-700/50 dark:text-gray-400">
                    <tr>
                      <th className="px-4 py-3 text-left text-[0.65rem] font-semibold uppercase tracking-widest">Job ID</th>
                      <th className="px-4 py-3 text-left text-[0.65rem] font-semibold uppercase tracking-widest">Wiki</th>
                      <th className="px-4 py-3 text-left text-[0.65rem] font-semibold uppercase tracking-widest">仓库</th>
                      <th className="px-4 py-3 text-left text-[0.65rem] font-semibold uppercase tracking-widest">开始时间</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                    {snapshot.running.map((item, index) => (
                      <tr key={item.queue_key || index} className="odd:bg-white dark:odd:bg-gray-800/50 transition-colors hover:bg-gray-50 dark:hover:bg-gray-700/50">
                        <td className="px-4 py-3 font-mono text-xs text-gray-900 dark:text-gray-200">{item.job_id}</td>
                        <td className="px-4 py-3 text-xs text-gray-900 dark:text-gray-200">{item.wiki_id || '-'}</td>
                        <td className="px-4 py-3 text-xs text-gray-900 dark:text-gray-200">
                          <span className="inline-flex max-w-[180px] break-all font-mono">{item.repo_url || '-'}</span>
                        </td>
                        <td className="px-4 py-3 text-xs text-gray-500 dark:text-gray-400">{item.started_at ? new Date(item.started_at).toLocaleString() : '-'}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="relative overflow-hidden rounded-xl border border-gray-200 bg-white/80 dark:border-gray-700 dark:bg-gray-800/80 p-5 shadow-md transition-all hover:shadow-lg backdrop-blur-sm">
        <div className="relative z-10">
          <div className="mb-4 flex items-center justify-between">
            <h3 className="flex items-center gap-2 text-lg font-semibold text-gray-900 dark:text-white">
              <span className="flex h-8 w-8 items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900/50 text-purple-500 dark:text-purple-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </span>
              活跃作业
            </h3>
            <span className="rounded-full border border-purple-200 bg-purple-100 px-3 py-1 text-xs font-medium text-purple-800 dark:border-purple-800/30 dark:bg-purple-900/30 dark:text-purple-300">
              {snapshot.active_jobs.length}
            </span>
          </div>
          {snapshot.active_jobs.length === 0 ? (
            <div className="flex flex-col items-center justify-center gap-3 rounded-xl border border-dashed border-gray-200 bg-gray-50 py-10 text-center dark:border-gray-700 dark:bg-gray-800/50">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
              <p className="text-sm text-gray-500 dark:text-gray-400">当前没有活跃作业</p>
            </div>
          ) : (
            <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800">
              <table className="min-w-full text-xs sm:text-sm">
                <thead className="bg-gray-50 text-gray-500 dark:bg-gray-700/50 dark:text-gray-400">
                  <tr>
                    <th className="px-4 py-3 text-left text-[0.65rem] font-semibold uppercase tracking-widest">Job ID</th>
                    <th className="px-4 py-3 text-left text-[0.65rem] font-semibold uppercase tracking-widest">Wiki</th>
                    <th className="px-4 py-3 text-left text-[0.65rem] font-semibold uppercase tracking-widest">状态</th>
                    <th className="px-4 py-3 text-left text-[0.65rem] font-semibold uppercase tracking-widest">创建时间</th>
                    <th className="px-4 py-3 text-left text-[0.65rem] font-semibold uppercase tracking-widest">更新时间</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  {snapshot.active_jobs.map((job, index) => (
                    <tr key={job.job_id || index} className="odd:bg-white dark:odd:bg-gray-800/50 transition-colors hover:bg-gray-50 dark:hover:bg-gray-700/50">
                      <td className="px-4 py-3 font-mono text-xs text-gray-900 dark:text-gray-200">{job.job_id}</td>
                      <td className="px-4 py-3 text-xs text-gray-900 dark:text-gray-200">{job.wiki_id || '-'}</td>
                      <td className="px-4 py-3 text-xs">
                        <span className={`inline-flex items-center gap-1 rounded-full px-2 py-1 text-xs font-medium ${
                          job.status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                          job.status === 'failed' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                          job.status === 'running' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                          job.status === 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                          'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                        }`}>
                          <span className="h-2 w-2 rounded-full bg-current" />
                          {job.status || '-'}
                        </span>
                      </td>
                      <td className="px-4 py-3 text-xs text-gray-500 dark:text-gray-400">{job.created_at ? new Date(job.created_at).toLocaleString() : '-'}</td>
                      <td className="px-4 py-3 text-xs text-gray-500 dark:text-gray-400">{job.updated_at ? new Date(job.updated_at).toLocaleString() : '-'}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      <div className="relative overflow-hidden rounded-xl border border-gray-200 bg-white/80 dark:border-gray-700 dark:bg-gray-800/80 p-5 shadow-md transition-all hover:shadow-lg backdrop-blur-sm">
        <button
          type="button"
          onClick={() => setShowRedisDetails((prev) => !prev)}
          className="group flex w-full items-center justify-between rounded-xl border border-gray-200 bg-white/60 px-5 py-4 text-left font-medium text-gray-900 transition-all hover:bg-white dark:border-gray-700 dark:bg-gray-800/60 dark:text-white hover:shadow-sm dark:hover:bg-gray-800"
        >
          <div className="flex items-center gap-3">
            <span className="flex h-10 w-10 items-center justify-center rounded-full bg-amber-100 text-amber-500 dark:bg-amber-900/50 dark:text-amber-400">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
              </svg>
            </span>
            <div className="flex flex-col">
              <span>Redis 运行详情</span>
              <span className="text-xs text-gray-500 dark:text-gray-400">查看缓存槽位、键结构与连接状态</span>
            </div>
          </div>
          <span className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
            <span className={`inline-flex items-center gap-1 rounded-full border px-2 py-1 text-xs ${
              snapshot.metrics?.redis_status === 'connected'
                ? 'border-green-500/40 bg-green-500/10 text-green-500'
                : 'border-yellow-500/40 bg-yellow-500/10 text-yellow-500'
            }`}>
              <span className="h-2 w-2 rounded-full bg-current" />
              {snapshot.metrics?.redis_status === 'connected' ? '在线' : '本地回退'}
            </span>
            {showRedisDetails ? (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                </svg>
                收起
              </>
            ) : (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
                展开
              </>
            )}
          </span>
        </button>
        {showRedisDetails && (
          <div className="mt-6 space-y-6">
            <div className="grid grid-cols-1 gap-5 md:grid-cols-2 lg:grid-cols-3">
              <div className="relative overflow-hidden rounded-xl border border-gray-200 bg-white p-5 shadow-inner dark:border-gray-700 dark:bg-gray-800">
                <h4 className="relative z-10 mb-4 flex items-center gap-2 text-base font-semibold text-gray-900 dark:text-white">
                  <span className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 text-gray-500 dark:bg-gray-700 dark:text-gray-300">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </span>
                  Redis 状态
                </h4>
                <dl className="relative z-10 space-y-3">
                  <div className="flex items-center justify-between rounded-lg border border-gray-200 bg-white/80 px-3 py-2 dark:border-gray-700 dark:bg-gray-800/50">
                    <dt className="text-sm text-gray-500 dark:text-gray-400">连接模式</dt>
                    <dd className="font-medium">
                      <span className={`flex items-center gap-2 rounded-full px-3 py-1 text-xs font-medium ${
                        snapshot.metrics?.redis_status === 'connected'
                          ? 'bg-green-500/15 text-green-500'
                          : 'bg-yellow-500/15 text-yellow-500'
                      }`}>
                        <span className="h-2.5 w-2.5 rounded-full bg-current" />
                        {snapshot.metrics?.redis_status === 'connected' ? 'Redis 已连接' : '本地回退'}
                      </span>
                    </dd>
                  </div>
                  <div className="flex items-center justify-between rounded-lg border border-gray-200 bg-white/80 px-3 py-2 dark:border-gray-700 dark:bg-gray-800/50">
                    <dt className="text-sm text-gray-500 dark:text-gray-400">配置全局上限</dt>
                    <dd className="font-medium">{summary.configuredGlobalLimit}</dd>
                  </div>
                  <div className="flex items-center justify-between rounded-lg border border-gray-200 bg-white/80 px-3 py-2 dark:border-gray-700 dark:bg-gray-800/50">
                    <dt className="text-sm text-gray-500 dark:text-gray-400">生效全局上限</dt>
                    <dd className="font-medium">{summary.effectiveGlobalLimit}</dd>
                  </div>
                  <div className="flex items-center justify-between rounded-lg border border-gray-200 bg-white/80 px-3 py-2 dark:border-gray-700 dark:bg-gray-800/50">
                    <dt className="text-sm text-gray-500 dark:text-gray-400">实例上限</dt>
                    <dd className="font-medium">{summary.instanceLimit}</dd>
                  </div>
                  <div className="flex items-center justify-between rounded-lg border border-gray-200 bg-white/80 px-3 py-2 dark:border-gray-700 dark:bg-gray-800/50">
                    <dt className="text-sm text-gray-500 dark:text-gray-400">线程池并发</dt>
                    <dd className="font-medium">{summary.threadLimit || summary.instanceLimit}</dd>
                  </div>
                  <div className="flex items-center justify-between rounded-lg border border-gray-200 bg-white/80 px-3 py-2 dark:border-gray-700 dark:bg-gray-800/50">
                    <dt className="text-sm text-gray-500 dark:text-gray-400">槽位 TTL</dt>
                    <dd className="font-medium">{snapshot.metrics?.slot_ttl_seconds ?? '-'} 秒</dd>
                  </div>
                  <div className="flex items-center justify-between rounded-lg border border-gray-200 bg-white/80 px-3 py-2 dark:border-gray-700 dark:bg-gray-800/50">
                    <dt className="text-sm text-gray-500 dark:text-gray-400">作业 TTL</dt>
                    <dd className="font-medium">{snapshot.metrics?.job_ttl_seconds ?? '-'} 秒</dd>
                  </div>
                </dl>
              </div>

              <div className="relative overflow-hidden rounded-xl border border-gray-200 bg-white p-5 shadow-inner dark:border-gray-700 dark:bg-gray-800 md:col-span-2">
                <h4 className="relative z-10 mb-4 flex items-center gap-2 text-base font-semibold text-gray-900 dark:text-white">
                  <span className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-500 dark:bg-blue-900/50 dark:text-blue-400">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                  </span>
                  Redis 键结构
                </h4>
                <div className="relative z-10 grid grid-cols-1 gap-3 sm:grid-cols-2">
                  {REDIS_KEY_LABELS.map(({ key, label }) => (
                    <div key={key} className="rounded-lg border border-gray-200 bg-white/80 px-3 py-3 shadow-sm hover:border-blue-300 dark:border-gray-700 dark:bg-gray-800/50 hover:dark:border-blue-700">
                      <p className="mb-1 text-xs uppercase tracking-wide text-gray-500 dark:text-gray-400">{label}</p>
                      <p className="break-all font-mono text-sm text-gray-900 dark:text-gray-200">{snapshot.redis_keys?.[key] || '-'}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  )
}
