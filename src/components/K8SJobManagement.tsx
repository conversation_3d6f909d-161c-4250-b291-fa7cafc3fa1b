'use client'

import React, { useState, useEffect } from 'react'
import Toast from './k8s/Toast'
import Too<PERSON><PERSON> from './k8s/Toolbar'
import JobsTable from './k8s/JobsTable'
import CreateSandboxModal from './k8s/CreateSandboxModal'
import JobDetailsModal from './k8s/JobDetailsModal'
import SandboxUserManagementTable from './k8s/SandboxUserManagementTable'
import WikiInfoManagementTable from './k8s/WikiInfoManagementTable'
import { SyncQueueDashboard } from './SyncQueueDashboard'
import CountUp from 'react-countup'
import { authFetch } from '@/utils/authFetch'
import { useSettings } from '@/contexts/SettingsContext'
import ProblemWikisModal from './k8s/ProblemWikisModal'
import QuickWikiModal from './k8s/QuickWikiModal'
import { useLanguage } from '@/contexts/LanguageContext'

// 定义接口类型
// TODO: 后续完全删除本地接口定义，改为从 ./k8s/types 引入
interface PodInfo {
  name: string
  namespace: string
  status: string
  pod_ip: string
  host_ip: string
  node_name: string
  creation_time: string
  labels: Record<string, string>
  annotations: Record<string, string>
  // 容器列表信息，支持跳板机跳转
  containers?: Array<{
    name: string
    image?: string
  }>
  container_statuses?: Array<{
    name: string
    ready: boolean
    restart_count: number
    state: string
    container_id?: string
  }>
  resources?: {
    containers: Array<{
      name: string
      requests: {
        cpu: string
        memory: string
      }
      limits: {
        cpu: string
        memory: string
      }
    }>
    total_requests: {
      cpu: string
      memory: string
    }
    total_limits: {
      cpu: string
      memory: string
    }
  }
  metrics?: {
    pod_name: string
    timestamp: string
    window: string
    containers: Array<{
      name: string
      cpu_usage: number  // 毫核 (millicores)
      cpu_usage_str: string
      memory_usage: number  // KiB
      memory_usage_str: string
    }>
    total_cpu_usage: number  // 毫核 (millicores)
    total_memory_usage: number  // KiB
    total_cpu_usage_m: number  // 毫核 (millicores)
    total_memory_usage_kib: number  // KiB
  }
}

interface JobInfo {
  name: string
  namespace: string
  user_code: string
  git_url: string
  branch: string
  creation_time: string
  last_access_time?: string
  status: string
  labels: Record<string, string>
  annotations: Record<string, string>
  mounts?: Array<{
    volume_name: string
    container_path: string
    host_path?: string
    read_only?: boolean
  }>
  pods?: PodInfo[]
  metrics?: {
    job_name: string
    pods: Array<{
      pod_name: string
      timestamp: string
      window: string
      containers: Array<{
        name: string
        cpu_usage: number
        cpu_usage_str: string
        memory_usage: number
        memory_usage_str: string
      }>
      total_cpu_usage: number
      total_memory_usage: number
    }>
    total_cpu_usage: number
    total_memory_usage: number
    pod_count: number
  }
  user_name?: string
}

// 新增沙盒状态接口
interface SandboxStatus {
  status: string
  status_description?: string
  job_name: string
  message: string
  pod_ip?: string
  api_response?: string
  timestamp: string
  pod_status?: string[]
  container_statuses?: Array<{
    name: string
    ready: boolean
    restart_count: number
    state: string
  }>
}

// 用户配额管理
interface UserQuotaUser {
  id: number
  user_code: string
  user_name: string
  sandbox_quota?: number | null
  effective_quota?: number | null
}

// 用户沙盒列表项
interface SandboxListItem {
  name?: string
  git_url?: string
  branch?: string
  status?: string
  detailed_status?: string
  pod_ip?: string
  annotations?: Record<string, string>
}

interface ToastState {
  show: boolean
  message: string
  type: 'success' | 'error' | 'info'
}

export function JobManagementComponent() {
  const { messages: t } = useLanguage();
  const { settings } = useSettings()
  const [jobs, setJobs] = useState<JobInfo[]>([])
  const [loading, setLoading] = useState(false)
  const [filterUserCode, setFilterUserCode] = useState('')
  const [selectedJob, setSelectedJob] = useState<JobInfo | null>(null)
  const [showCreateSandboxModal, setShowCreateSandboxModal] = useState(false)
  const [newSandboxUserCode, setNewSandboxUserCode] = useState('')
  const [newSandboxGitUrl, setNewSandboxGitUrl] = useState('')
  const [newSandboxBranch, setNewSandboxBranch] = useState('main')
  const [toast, setToast] = useState<ToastState>({ show: false, message: '', type: 'info' })
  const [metricsRefreshInterval, setMetricsRefreshInterval] = useState<NodeJS.Timeout | null>(null)
  const [isMonitoring, setIsMonitoring] = useState(false)
  const [lastMetricsUpdate, setLastMetricsUpdate] = useState<string>('')
  const [k8sNamespace, setK8sNamespace] = useState('')
  const [k8sEnvironment, setK8sEnvironment] = useState('')
  // 记录K8s主控地址与门户地址供跳转使用
  const [k8sMasterUrl, setK8sMasterUrl] = useState('')
  const [portalBaseUrl, setPortalBaseUrl] = useState('')
  const [enableContainerPortalButton, setEnableContainerPortalButton] = useState(false)
  const [enableJumpServerButton, setEnableJumpServerButton] = useState(false)
  const [jumpServerUrl, setJumpServerUrl] = useState('')
  const [metricsError, setMetricsError] = useState<string>('')
  // 新增沙盒状态相关状态
  const [sandboxStatuses, setSandboxStatuses] = useState<Record<string, SandboxStatus>>({})
  const [statusLoading, setStatusLoading] = useState<Record<string, boolean>>({})
  // 用户配额管理状态
  const [quotaUsers, setQuotaUsers] = useState<UserQuotaUser[]>([])
  const [quotaLoading, setQuotaLoading] = useState(false)
  const [quotaKeyword, setQuotaKeyword] = useState('')
  const [quotaPage, setQuotaPage] = useState(1)
  const [quotaPageSize, setQuotaPageSize] = useState(20)
  const [quotaTotal, setQuotaTotal] = useState(0)
  const [quotaEdits, setQuotaEdits] = useState<Record<string, string>>({})
  const [wikiSubTab, setWikiSubTab] = useState<'info' | 'sync'>('info')
  // 用户管理改为页内表格，不再使用模态框
  const [expandedUsers, setExpandedUsers] = useState<Record<string, boolean>>({})
  const [userSandboxMap, setUserSandboxMap] = useState<Record<string, { loading: boolean; items: SandboxListItem[] }>>({})
  // Job分页状态
  const [jobsPageSize, setJobsPageSize] = useState<number>(20)
  const [jobsContinueToken, setJobsContinueToken] = useState<string | null>(null)
  const [jobsNextContinueToken, setJobsNextContinueToken] = useState<string | null>(null)
  const [jobsPrevTokens, setJobsPrevTokens] = useState<string[]>([])
  const [jobsRemaining, setJobsRemaining] = useState<number | null>(null)
  const [jobsPageInput, setJobsPageInput] = useState<string>('1')
  const [showProblemWikis, setShowProblemWikis] = useState(false)
  const [problemWikis, setProblemWikis] = useState<Array<{ wiki_id: string; repo_url: string; branch: string; status?: string; user_code?: string; user_name?: string }>>([])
  const [generatingWikis, setGeneratingWikis] = useState<Set<string>>(new Set())
  // 新增Tab状态
  const [activeTab, setActiveTab] = useState('jobs')
  const [jobsAccentIndex, setJobsAccentIndex] = useState(0)
  const jobsAccentColors = ['text-cyan-600','text-indigo-600','text-rose-600','text-emerald-600','text-amber-600']
  // 快速创建Wiki状态
  const [showQuickWikiModal, setShowQuickWikiModal] = useState(false)
  // 右侧操作面板状态
  const [showActionsPanel, setShowActionsPanel] = useState(false)
  // 批量创建Jobs状态
  const [showBatchCreateJobsModal, setShowBatchCreateJobsModal] = useState(false)
  const [batchJobNumber, setBatchJobNumber] = useState('')
  // 新增自定义Job相关状态
  const [showCreateCustomJobModal, setShowCreateCustomJobModal] = useState(false)
  const [customJobForm, setCustomJobForm] = useState({
    job_name: '',
    image: '',
    volumes: [{ name: '', host_path: '', container_path: '', read_only: false }],
    env_vars: [{ name: '', value: '' }],
    cpu_request: '10m',
    memory_request: '256Mi',
    cpu_limit: '100m',
    memory_limit: '256Mi',
    user_code: '',
    git_url: '',
    branch: 'main',
    user_name: '',
    namespace: ''
  })

  const showToast = (message: string, type: 'success' | 'error' | 'info') => {
    setToast({ show: true, message, type })
    setTimeout(() => setToast({ show: false, message: '', type: 'info' }), 5000)
  }

  // 快速创建Wiki成功回调
  const handleQuickWikiSuccess = (result: { wiki_id: string; job_id: string; message: string }) => {
    showToast(`${result.message} - Job ID: ${result.job_id}`, 'success')
    // 刷新Jobs列表
    loadJobs(true)
  }

  // 批量创建Jobs
  const handleBatchCreateJobs = async () => {
    if (!batchJobNumber) {
      showToast(t.components.K8SJobManagement.batchCreateJobsModal.fillAllFields, 'error')
      return
    }

    const jobNumber = parseInt(batchJobNumber)
    if (isNaN(jobNumber) || jobNumber <= 0) {
      showToast(t.components.K8SJobManagement.batchCreateJobsModal.invalidJobNumber, 'error')
      return
    }

    setLoading(true)
    try {
      const response = await authFetch('/api/k8s/job/pool/batch/jobs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          job_number: jobNumber,
          wct_api_key: "ailab_A9c0KTF9PiKzw4ZWIgsFH5mYOtIa3KonbLVGN2Y9pRHJdOu6772rDWa+MA8GLc6L0siuiJ+qxQEm+kOxQ3DbJHeiGn/w7N5ROFItZm65t1D9Gu2LZGj2O8E="
        }),
      })

      if (!response) {
        showToast(t.components.K8SJobManagement.batchCreateJobsModal.createFailed, 'error')
        return
      }

      const data = await response.json()
      if (data.success || response.ok) {
        showToast(t.components.K8SJobManagement.batchCreateJobsModal.createSuccess, 'success')
        setShowBatchCreateJobsModal(false)
        setBatchJobNumber('')
        // 刷新Jobs列表
        loadJobs(true)
      } else {
        showToast(`${t.components.K8SJobManagement.batchCreateJobsModal.createFailed}: ${data.message || data.error || t.common.error}`, 'error')
      }
    } catch (error) {
      showToast(`${t.components.K8SJobManagement.batchCreateJobsModal.createFailed}: ${error instanceof Error ? error.message : t.common.error}`, 'error')
    } finally {
      setLoading(false)
    }
  }

  // 创建自定义Job
  const createCustomJob = async () => {
    if (!customJobForm.job_name || !customJobForm.image) {
      showToast(t.components.K8SJobManagement.createCustomJob.fillJobNameAndImage, 'error')
      return
    }
    
    // 验证卷配置
    const validVolumes = customJobForm.volumes.filter(v => v.host_path && v.container_path)
    if (validVolumes.length === 0) {
      showToast(t.components.K8SJobManagement.createCustomJob.fillAtLeastOneValidVolume, 'error')
      return
    }
    
    // 验证环境变量
    const validEnvVars = customJobForm.env_vars.filter(env => env.name && env.value !== '')
    
    setLoading(true)
    try {
      const response = await fetch('/api/k8s/jobs/create/custom', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          job_name: customJobForm.job_name,
          image: customJobForm.image,
          volumes: validVolumes,
          env_vars: validEnvVars,
          cpu_request: customJobForm.cpu_request,
          memory_request: customJobForm.memory_request,
          cpu_limit: customJobForm.cpu_limit,
          memory_limit: customJobForm.memory_limit,
          user_code: customJobForm.user_code || undefined,
          git_url: customJobForm.git_url || undefined,
          branch: customJobForm.branch || undefined,
          user_name: customJobForm.user_name || undefined,
          namespace: customJobForm.namespace || undefined
        }),
      })
      
      const data = await response.json()
      if (data.success) {
        showToast(t.components.K8SJobManagement.createCustomJob.createCustomJobSuccess, 'success')
        setShowCreateCustomJobModal(false)
        // 重置表单
        setCustomJobForm({
          job_name: '',
          image: '',
          volumes: [{ name: '', host_path: '', container_path: '', read_only: false }],
          env_vars: [{ name: '', value: '' }],
          cpu_request: '10m',
          memory_request: '256Mi',
          cpu_limit: '100m',
          memory_limit: '256Mi',
          user_code: '',
          git_url: '',
          branch: 'main',
          user_name: '',
          namespace: ''
        })
        // 刷新Jobs列表
        loadJobs(true)
      } else {
        showToast(`${t.components.K8SJobManagement.createCustomJob.createCustomJobFailed}: ${data.error}`, 'error')
      }
    } catch (error) {
      showToast(`${t.components.K8SJobManagement.createCustomJob.networkError}: ${error instanceof Error ? error.message : t.common.error}`, 'error')
    } finally {
      setLoading(false)
    }
  }

  // 添加卷挂载
  const addVolume = () => {
    setCustomJobForm(prev => ({
      ...prev,
      volumes: [...prev.volumes, { name: '', host_path: '', container_path: '', read_only: false }]
    }))
  }

  // 删除卷挂载
  const removeVolume = (index: number) => {
    setCustomJobForm(prev => ({
      ...prev,
      volumes: prev.volumes.filter((_, i) => i !== index)
    }))
  }

  // 更新卷挂载
  const updateVolume = (index: number, field: string, value: string | boolean) => {
    setCustomJobForm(prev => ({
      ...prev,
      volumes: prev.volumes.map((vol, i) => 
        i === index ? { ...vol, [field]: value } : vol
      )
    }))
  }

  // 添加环境变量
  const addEnvVar = () => {
    setCustomJobForm(prev => ({
      ...prev,
      env_vars: [...prev.env_vars, { name: '', value: '' }]
    }))
  }

  // 删除环境变量
  const removeEnvVar = (index: number) => {
    setCustomJobForm(prev => ({
      ...prev,
      env_vars: prev.env_vars.filter((_, i) => i !== index)
    }))
  }

  // 更新环境变量
  const updateEnvVar = (index: number, field: string, value: string) => {
    setCustomJobForm(prev => ({
      ...prev,
      env_vars: prev.env_vars.map((env, i) => 
        i === index ? { ...env, [field]: value } : env
      )
    }))
  }

  // 查询沙盒状态
  const querySandboxStatus = async (userCode: string, gitUrl: string, branch: string, jobName?: string) => {
    const statusKey = jobName || `${userCode}-${gitUrl}-${branch}`
    setStatusLoading(prev => ({ ...prev, [statusKey]: true }))
    
    try {
      const params = new URLSearchParams({
        user_code: userCode,
        git_url: gitUrl,
        branch: branch,
        job_name: jobName || ''
      })
      
      const response = await fetch(`/api/k8s/sandbox/status?${params.toString()}`)
      const data = await response.json()
      
      if (data.success) {
        setSandboxStatuses(prev => ({
          ...prev,
          [statusKey]: data.data
        }))
        showToast(`${t.components.K8SJobManagement.createCustomJob.statusQuerySuccess}: ${data.data.status}`, 'success')
      } else {
        showToast(`${t.components.K8SJobManagement.createCustomJob.statusQueryFailed}: ${data.error}`, 'error')
      }
    } catch (error) {
      showToast(`${t.components.K8SJobManagement.createCustomJob.networkError}: ${error instanceof Error ? error.message : t.common.error}`, 'error')
    } finally {
      setStatusLoading(prev => ({ ...prev, [statusKey]: false }))
    }
  }

  // 获取沙盒状态
  const getSandboxStatus = (userCode: string, gitUrl: string, branch: string, jobName?: string): SandboxStatus | null => {
    const statusKey = jobName || `${userCode}-${gitUrl}-${branch}`
    return sandboxStatuses[statusKey] || null
  }

  const loadJobs = async (reset: boolean = false, tokenOverride?: string | null) => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      if (filterUserCode) params.append('user_code', filterUserCode)
      params.append('limit', String(jobsPageSize))
      const tokenToUse = reset ? null : (tokenOverride !== undefined ? tokenOverride : jobsContinueToken)
      if (tokenToUse) params.append('continue_token', tokenToUse)
      const url = `/api/k8s/jobs/list/paginated?${params.toString()}`
      const response = await fetch(url)
      const data = await response.json()
      if (data.success) {
        const result = (data.data || {}) as { jobs?: JobInfo[]; continue_token?: string | null; remaining_item_count?: number | null }
        setJobs(result.jobs || [])
        setJobsNextContinueToken(result.continue_token || null)
        setJobsRemaining(typeof result.remaining_item_count === 'number' ? result.remaining_item_count : null)
        if (reset) {
          setJobsPrevTokens([])
          setJobsContinueToken(null)
          setJobsPageInput('1')
        } else {
          setJobsContinueToken(tokenToUse || null)
        }
      } else {
        showToast(`${t.components.K8SJobManagement.createCustomJob.loadJobListFailed}: ${data.error}`, 'error')
      }
    } catch (error) {
      showToast(`${t.components.K8SJobManagement.createCustomJob.networkError}: ${error instanceof Error ? error.message : t.common.error}`, 'error')
    } finally {
      setLoading(false)
    }
  }

  const goFirstJobsPage = async () => {
    setJobsPrevTokens([])
    setJobsContinueToken(null)
    setJobsNextContinueToken(null)
    await loadJobs(true)
  }

  const goNextJobsPage = async () => {
    if (!jobsNextContinueToken) return
    const currentToken = jobsContinueToken || ''
    setJobsPrevTokens(prev => [...prev, currentToken])
    await loadJobs(false, jobsNextContinueToken)
    setJobsPageInput(String(jobsPrevTokens.length + 2))
  }

  const goPrevJobsPage = async () => {
    if (jobsPrevTokens.length === 0) return
    const prevToken = jobsPrevTokens[jobsPrevTokens.length - 1]
    setJobsPrevTokens(tokens => tokens.slice(0, -1))
    await loadJobs(false, prevToken || null)
    setJobsPageInput(String(Math.max(1, jobsPrevTokens.length)))
  }

  const gotoPage = async (targetPage: number) => {
    const currentPage = jobsPrevTokens.length + 1
    if (Number.isNaN(targetPage) || targetPage < 1) {
      await goFirstJobsPage()
      return
    }
    if (targetPage === currentPage) return
    if (targetPage < currentPage) {
      const tokenToUse = targetPage === 1 ? null : jobsPrevTokens[targetPage - 2] || null
      setJobsPrevTokens(prev => prev.slice(0, targetPage - 1))
      await loadJobs(false, tokenToUse)
      setJobsPageInput(String(targetPage))
      return
    }
    // targetPage > currentPage: 循环前进
    const steps = targetPage - currentPage
    for (let i = 0; i < steps; i++) {
      if (!jobsNextContinueToken) break
      await goNextJobsPage()
    }
    setJobsPageInput(String(jobsPrevTokens.length + 1))
  }


  const createSandbox = async () => {
    if (!newSandboxUserCode || !newSandboxGitUrl) {
      showToast(t.components.K8SJobManagement.fillUserCodeAndGitUrl, 'error')
      return
    }
    setLoading(true)
    try {
      const response = await fetch('/api/k8s/sandbox/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          user_code: newSandboxUserCode,
          git_url: newSandboxGitUrl,
          branch: newSandboxBranch || 'main',
          wct_api_key: settings.apiKey,
        }),
      })
      const data = await response.json()
      if (data.success) {
        showToast(t.components.K8SJobManagement.createSandboxSuccess, 'success')
        setShowCreateSandboxModal(false)
        setNewSandboxUserCode('')
        setNewSandboxGitUrl('')
        setNewSandboxBranch('main')
        loadJobs(true)
      } else {
        showToast(`${t.components.K8SJobManagement.createSandboxFailed}: ${data.error}`, 'error')
      }
    } catch (error) {
      showToast(`${t.components.K8SJobManagement.networkError}: ${error instanceof Error ? error.message : t.common.error}`, 'error')
    } finally {
      setLoading(false)
    }
  }

  const deleteJob = async (jobName: string) => {
    // 优先从本地 jobs 列表解析 user_code/git_url/branch
    const job = jobs.find(j => j.name === jobName)
    const isApp = (() => {
      try {
        const name = (job?.name || '').toLowerCase()
        const st = job?.labels?.['sandbox-type']
        return (typeof name === 'string' && name.startsWith('app-')) || st === 'app'
      } catch {
        return false
      }
    })()

    if (isApp) {
      if (!confirm('确认删除该应用任务？')) return
      setLoading(true)
      try {
        const res = await fetch(`/api/k8s/jobs/${jobName}`, { method: 'DELETE' })
        const data = await res.json()
        if (data.success) {
          showToast(t.components.K8SJobManagement.deleteSandboxSuccess, 'success')
          loadJobs()
        } else {
          showToast(`${t.components.K8SJobManagement.deleteSandboxFailed}: ${data.error}`, 'error')
        }
      } catch (error) {
        showToast(`${t.components.K8SJobManagement.networkError}: ${error instanceof Error ? error.message : t.common.error}`, 'error')
      } finally {
        setLoading(false)
      }
      return
    }
    // let userCode = job?.user_code || ''
    // let gitUrl = job?.git_url || ''
    // let branch = job?.branch || ''

    // 如果本地数据缺失，则拉取详情补全
    // if (!userCode || !gitUrl) {
    //   try {
    //     const resp = await fetch(`/api/k8s/jobs/${jobName}`)
    //     const json = await resp.json()
    //     if (json?.success && json?.data) {
    //       userCode = json.data.user_code || json.data.annotations?.['user.code'] || userCode
    //       gitUrl = json.data.git_url || json.data.annotations?.['git.url'] || gitUrl
    //       branch = json.data.branch || json.data.annotations?.['git.branch'] || branch || 'main'
    //     }
    //   } catch {
    //     // ignore, 下面会校验
    //   }
    // }

    // if (!userCode || !gitUrl) {
    //   showToast(t.components.K8SJobManagement.cannotIdentifySandboxParams, 'error')
    //   return
    // }

    // const confirmMsg = t.components.K8SJobManagement.confirmDeleteSandbox.replace('${userCode}', userCode).replace('${branch}', branch).replace('${gitUrl}', gitUrl)
    // if (!confirm(confirmMsg)) return

    setLoading(true)
    try {
      const params = new URLSearchParams({ job_name: jobName })
      const response = await fetch(`/api/k8s/sandbox/delete?${params.toString()}`, { method: 'DELETE' })
      const data = await response.json()
      if (data.success) {
        showToast(t.components.K8SJobManagement.deleteSandboxSuccess, 'success')
        loadJobs()
      } else {
        showToast(`${t.components.K8SJobManagement.deleteSandboxFailed}: ${data.error}`, 'error')
      }
    } catch (error) {
      showToast(`${t.components.K8SJobManagement.networkError}: ${error instanceof Error ? error.message : t.common.error}`, 'error')
    } finally {
      setLoading(false)
    }
  }

  const viewJobDetails = async (jobName: string) => {
    setLoading(true)
    try {
      const response = await fetch(`/api/k8s/jobs/${jobName}`)
      const data = await response.json()
      if (data.success) {
        setSelectedJob(data.data)
        // 获取Job的实时metrics
        await loadJobMetrics(jobName)
      } else {
        showToast(`${t.components.K8SJobManagement.getJobDetailsFailed}: ${data.error}`, 'error')
      }
    } catch (error) {
      showToast(`${t.components.K8SJobManagement.networkError}: ${error instanceof Error ? error.message : t.common.error}`, 'error')
    } finally {
      setLoading(false)
    }
  }

  // 更新Job元数据后同步列表与详情
  const handleJobMetadataUpdated = (updatedJob: JobInfo) => {
    setSelectedJob(prev => {
      if (prev && prev.name === updatedJob.name) {
        return {
          ...prev,
          ...updatedJob,
          metrics: prev.metrics
        }
      }
      return updatedJob
    })

    setJobs(prev => prev.map(job => job.name === updatedJob.name ? { ...job, ...updatedJob } : job))
  }

  const loadJobMetrics = async (jobName: string) => {
    try {
      console.log(`正在获取Job ${jobName} 的metrics...`)
      const response = await fetch(`/api/k8s/jobs/${jobName}/metrics`)
      const data = await response.json()
      if (data.success) {
        console.log(`成功获取Job ${jobName} 的metrics:`, data.data)
        // 更新Job数据，包含metrics信息
        setSelectedJob(prevJob => {
          if (prevJob && prevJob.name === jobName) {
            return {
              ...prevJob,
              metrics: data.data
            }
          }
          return prevJob
        })
      } else {
        console.warn(`获取Job metrics失败: ${data.error}`)
      }
    } catch (error) {
      console.warn(`获取Job metrics网络错误: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  const startMetricsRefresh = (jobName: string) => {
    // 清除之前的定时器
    if (metricsRefreshInterval) {
      clearInterval(metricsRefreshInterval)
    }
    
    console.log(`开始监控Job ${jobName} 的metrics，每10秒刷新一次`)
    setIsMonitoring(true)
    setMetricsError('')
    
    // 立即获取一次metrics
    const fetchMetrics = async () => {
      try {
        console.log(`[${new Date().toLocaleTimeString()}] 正在获取Job ${jobName} 的metrics...`)
        const response = await fetch(`/api/k8s/jobs/${jobName}/metrics`)
        const data = await response.json()
        
        if (data.success) {
          console.log(`[${new Date().toLocaleTimeString()}] 成功获取Job ${jobName} 的metrics:`, data.data)
          setSelectedJob(prevJob => {
            if (prevJob && prevJob.name === jobName) {
              return {
                ...prevJob,
                metrics: data.data
              }
            }
            return prevJob
          })
          setLastMetricsUpdate(new Date().toLocaleTimeString())
          setMetricsError('')
        } else {
          console.warn(`[${new Date().toLocaleTimeString()}] 获取Job metrics失败: ${data.error}`)
          setMetricsError(`${t.components.K8SJobManagement.getJobMetricsFailed}: ${data.error}`)
        }
      } catch (error) {
        console.warn(`[${new Date().toLocaleTimeString()}] 获取Job metrics网络错误: ${error}`)
        setMetricsError(`${t.components.K8SJobManagement.networkError}: ${error instanceof Error ? error.message : t.common.error}`)
      }
    }
    
    // 立即执行一次
    fetchMetrics()
    
    // 设置新的定时器，每10秒刷新一次metrics
    const interval = setInterval(fetchMetrics, 10000)
    setMetricsRefreshInterval(interval)
  }

  const stopMetricsRefresh = () => {
    if (metricsRefreshInterval) {
      console.log('停止metrics监控')
      clearInterval(metricsRefreshInterval)
      setMetricsRefreshInterval(null)
      setIsMonitoring(false)
      setMetricsError('')
    }
  }

  const cleanupIdleJobs = async () => {
    if (!confirm(t.components.K8SJobManagement.confirmCleanupIdleJobs)) return
    setLoading(true)
    try {
      const response = await fetch('/api/k8s/jobs/cleanup', { method: 'POST' })
      const data = await response.json()
      if (data.success) {
        showToast(`${t.components.K8SJobManagement.cleanupIdleJobsSuccess}: ${data.cleaned_count}${t.components.K8SJobManagement.createCustomJobModal.idleJobsCount}`, 'success')
        await goFirstJobsPage()
      } else {
        showToast(`${t.components.K8SJobManagement.cleanupIdleJobsFailed}: ${data.error}`, 'error')
      }
    } catch (error) {
      showToast(`${t.components.K8SJobManagement.networkError}: ${error instanceof Error ? error.message : t.common.error}`, 'error')
    } finally {
      setLoading(false)
    }
  }

  // 快速生成Wiki
  const handleFastGenerate = async (wikiId: string, modelSettings?: {
    provider: string
    model: string
    comprehensive: boolean
    api_key?: string
    model_kwargs: Record<string, unknown>
  }) => {
    if (!confirm(t.components.K8SJobManagement.confirmFastGenerate)) return
    
    // 添加到生成中状态
    setGeneratingWikis(prev => new Set(prev).add(wikiId))
    
    try {
      // 使用用户设置
      const userSettings = settings || {
        apiKey: '',
        defaultWikiModel: 'gemini-2.5-flash',
        whaleDevCloudToken: ''
      }
      
      // 如果传递了模型设置，使用传递的设置；否则使用用户默认设置
      const finalModelSettings = modelSettings || {
        provider: 'whalecloud',
        model: userSettings.defaultWikiModel,
        comprehensive: true,
        model_kwargs: {}
      }
      
      // 不自动填充API key，只使用用户在弹窗中输入的值
      // 如果用户没有输入，则传递undefined（不传递该字段）
      
      const response = await authFetch('/api/k8s/wiki/generate/fast', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          wiki_id: wikiId,
          comprehensive: finalModelSettings.comprehensive !== undefined ? finalModelSettings.comprehensive : true,
          model_settings: finalModelSettings,
          token: userSettings.whaleDevCloudToken
        }),
      })
      
      if (!response) {
        throw new Error(t.components.K8SJobManagement.createCustomJobModal.requestFailedNoResponse)
      }
      
      const data = await response.json()
      if (data.success) {
        showToast(`${t.components.K8SJobManagement.wikiFastGenerateSuccess}: ${data.job_id}`, 'success')
        // 可选：延迟一段时间后刷新异常wiki列表
        setTimeout(async () => {
          try {
            const res = await fetch('/api/k8s/wiki/info/pending-failed')
            if (res.ok) {
              const json = await res.json()
              const rows = (json?.data || json) as Array<{ wiki_id: string; repo_url: string; branch: string; status?: string }>
              setProblemWikis(Array.isArray(rows) ? rows : [])
            }
          } catch (error) {
            console.error('刷新异常wiki列表失败:', error)
          }
        }, 2000)
      } else {
        showToast(`${t.components.K8SJobManagement.wikiFastGenerateFailed}: ${data.error}`, 'error')
      }
    } catch (error) {
      showToast(`${t.components.K8SJobManagement.networkError}: ${error instanceof Error ? error.message : t.common.error}`, 'error')
    } finally {
      // 从生成中状态移除
      setGeneratingWikis(prev => {
        const newSet = new Set(prev)
        newSet.delete(wikiId)
        return newSet
      })
    }
  }

  // 加载用户配额
  const loadUserQuotas = async () => {
    setQuotaLoading(true)
    try {
      const params = new URLSearchParams()
      if (quotaKeyword) params.append('keyword', quotaKeyword)
      params.append('page', String(quotaPage))
      params.append('page_size', String(quotaPageSize))
      const res = await fetch(`/api/k8s/sandbox/quotas?${params.toString()}`)
      const data = await res.json()
      if (data.success) {
        const result = data.data || { total: 0, users: [] }
        setQuotaUsers(result.users || [])
        setQuotaTotal(result.total || 0)
        // 初始化编辑缓存
        const edits: Record<string, string> = {}
        for (const u of result.users || []) {
          edits[u.user_code] = u.sandbox_quota === null || u.sandbox_quota === undefined ? '' : String(u.sandbox_quota)
        }
        setQuotaEdits(edits)
      } else {
        showToast(`${t.components.K8SJobManagement.loadUserQuotasFailed}: ${data.error}`, 'error')
      }
    } catch (e) {
      showToast(`${t.components.K8SJobManagement.networkError}: ${e instanceof Error ? e.message : t.common.error}`, 'error')
    } finally {
      setQuotaLoading(false)
    }
  }

  // 保存或清除用户配额
  const saveUserQuota = async (user_code: string) => {
    const val = quotaEdits[user_code]
    let quotaVal: number | null = null
    if (!(val === '' || val === undefined || val === null)) {
      const parsed = parseInt(val, 10)
      if (isNaN(parsed) || parsed < 0) {
        showToast(t.components.K8SJobManagement.invalidQuota, 'error')
        return
      }
      quotaVal = parsed
    }
    const body: { user_code: string; quota: number | null } = { user_code, quota: quotaVal }
    try {
      const res = await fetch('/api/k8s/sandbox/quotas/set', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      })
      const data = await res.json()
      if (data.success) {
        showToast(t.components.K8SJobManagement.saveUserQuotasSuccess, 'success')
        await loadUserQuotas()
      } else {
        showToast(`${t.components.K8SJobManagement.saveUserQuotasFailed}: ${data.error || ''}`, 'error')
      }
    } catch (e) {
      showToast(`${t.components.K8SJobManagement.networkError}: ${e instanceof Error ? e.message : t.common.error}`, 'error')
    }
  }

  // 加载指定用户的沙盒（行展开加载）
  const loadUserSandboxes = async (userCode: string) => {
    setUserSandboxMap(prev => ({ ...prev, [userCode]: { loading: true, items: prev[userCode]?.items || [] } }))
    try {
      const params = new URLSearchParams({ user_code: userCode })
      const res = await fetch(`/api/k8s/sandbox/list?${params.toString()}`)
      const data = await res.json()
      if (data.success) {
        setUserSandboxMap(prev => ({ ...prev, [userCode]: { loading: false, items: (data.data || []) as SandboxListItem[] } }))
      } else {
        setUserSandboxMap(prev => ({ ...prev, [userCode]: { loading: false, items: [] } }))
        showToast(`${t.components.K8SJobManagement.getUserSandboxesFailed}: ${data.error}`, 'error')
      }
    } catch (e) {
      setUserSandboxMap(prev => ({ ...prev, [userCode]: { loading: false, items: [] } }))
      showToast(`${t.components.K8SJobManagement.networkError}: ${e instanceof Error ? e.message : t.common.error}`, 'error')
    }
  }

  // 切换展开/收起并按需加载
  const toggleUserSandboxes = (userCode: string) => {
    setExpandedUsers(prev => ({ ...prev, [userCode]: !prev[userCode] }))
    if (!userSandboxMap[userCode]) {
      loadUserSandboxes(userCode)
    }
  }

  // 删除用户沙盒
  const deleteUserSandbox = async (userCode: string, sb: SandboxListItem) => {
    // if (!confirm(t.components.K8SJobManagement.confirmDeleteSandbox.replace('${userCode}', userCode).replace('${branch}', sb.branch).replace('${gitUrl}', sb.git_url))) return
    try {
      const params = new URLSearchParams({ job_name: sb.name || '' })
      const res = await fetch(`/api/k8s/sandbox/delete?${params.toString()}`, { method: 'DELETE' })
      const data = await res.json()
      if (data.success) {
        showToast(t.components.K8SJobManagement.deleteSandboxSuccess, 'success')
        await loadUserSandboxes(userCode)
      } else {
      }
    } catch (e) {
      showToast(`${t.components.K8SJobManagement.networkError}: ${e instanceof Error ? e.message : t.common.error}`, 'error')
    }
  }

  useEffect(() => {
    loadJobs()
    // eslint-disable-next-line
  }, [])

  useEffect(() => {
    (async () => {
      try {
        const resp = await authFetch('/api/k8s/config')
        if (resp?.ok) {
          const data = await resp.json()
          const cfg = data?.data || data
          setK8sNamespace(cfg?.namespace || '')
          setK8sEnvironment(cfg?.environment || '')
          setK8sMasterUrl(cfg?.api_server || '')
          setPortalBaseUrl(cfg?.portal_base_url || '')
          setEnableContainerPortalButton(Boolean(cfg?.enable_container_portal_button))
          setEnableJumpServerButton(Boolean(cfg?.enable_jump_server_button))
          setJumpServerUrl(cfg?.jump_server_url || '')
        }
      } catch {
        // ignore
      }
    })()
  }, [])

  useEffect(() => {
    const timer = setInterval(() => {
      setJobsAccentIndex((idx) => (idx + 1) % jobsAccentColors.length)
    }, 2000)
    return () => clearInterval(timer)
  }, [jobsAccentColors.length])

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (metricsRefreshInterval) {
        clearInterval(metricsRefreshInterval)
      }
    }
  }, [metricsRefreshInterval])

  // 分类：应用任务与非应用任务
  const isAppJob = (j: JobInfo) => {
    try {
      const name = (j?.name || '').toLowerCase()
      const st = j?.labels?.['sandbox-type']
      return (typeof name === 'string' && name.startsWith('app-')) || st === 'app'
    } catch {
      return false
    }
  }
  const appJobs = jobs.filter(isAppJob)
  const nonAppJobs = jobs.filter(j => !isAppJob(j))

  return (
    <div className="relative min-h-screen bg-gradient-to-b from-white via-slate-50 to-slate-100 p-4 sm:p-6 lg:p-8">
      {/* 背景纹理与光影层 */}
      <div className="pointer-events-none absolute inset-0 [background:repeating-linear-gradient(0deg,rgba(0,0,0,0.015)_0,rgba(0,0,0,0.015)_1px,transparent_1px,transparent_2px),repeating-linear-gradient(90deg,rgba(0,0,0,0.015)_0,rgba(0,0,0,0.015)_1px,transparent_1px,transparent_2px)] [mask-image:radial-gradient(ellipse_at_center,black_60%,transparent_100%)]"></div>
      <div className="pointer-events-none absolute -top-1/3 -left-1/3 w-[130%] h-[130%] bg-[radial-gradient(1000px_600px_at_0%_0%,rgba(255,255,255,0.6),transparent_60%),radial-gradient(800px_500px_at_100%_0%,rgba(173,216,230,0.25),transparent_60%)]"></div>
      {/* 右侧悬浮操作面板 */}
      <div className="fixed top-1/3 -translate-y-1/2 right-6 md:right-10 lg:right-14 flex flex-col items-end gap-5 z-50">
        <button
          onClick={() => setShowActionsPanel(prev => !prev)}
          className="w-12 h-12 rounded-full bg-indigo-600 text-white shadow-lg flex items-center justify-center hover:bg-indigo-700 transition"
          aria-label={t.components.K8SJobManagement.actions.expandActions}
        >
          <svg className={`w-6 h-6 transform transition-transform duration-700 ${showActionsPanel ? 'rotate-45' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" /></svg>
        </button>
        {showActionsPanel && (
          <div className="mt-3 flex flex-col items-end gap-5 transition-all duration-700">
            <button onClick={() => setShowCreateSandboxModal(true)} className="w-44 md:w-48 lg:w-52 text-left px-4 py-2 bg-white text-slate-900 rounded-full hover:bg-slate-50 border border-white/60 shadow">{t.components.K8SJobManagement.actions.createSandbox}</button>
            <button onClick={() => setShowCreateCustomJobModal(true)} className="w-44 md:w-48 lg:w-52 text-left px-4 py-2 bg-gradient-to-r from-emerald-500 to-teal-600 text-white rounded-full hover:from-emerald-600 hover:to-teal-700 shadow">{t.components.K8SJobManagement.actions.createCustomJob}</button>
            <button onClick={() => setShowQuickWikiModal(true)} className="w-44 md:w-48 lg:w-52 text-left px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-full hover:from-blue-600 hover:to-indigo-700 shadow">{t.components.K8SJobManagement.actions.quickCreateWiki}</button>
            <button onClick={async () => { await cleanupIdleJobs(); await goFirstJobsPage() }} disabled={loading} className="w-44 md:w-48 lg:w-52 text-left px-4 py-2 bg-slate-900 text-white rounded-full hover:bg-black disabled:opacity-50">{t.components.K8SJobManagement.actions.cleanupIdle}</button>
            <button
              onClick={async () => {
                setLoading(true)
                try {
                  const res = await fetch('/api/k8s/wiki/info/pending-failed')
                  if (!res.ok) throw new Error(t.components.K8SJobManagement.requestFailed)
                  const json = await res.json()
                  const rows = (json?.data || json) as Array<{ wiki_id: string; repo_url: string; branch: string; status?: string }>
                  setProblemWikis(Array.isArray(rows) ? rows : [])
                  setShowProblemWikis(true)
                }
                catch (err) {
                  showToast(`${t.components.K8SJobManagement.loadProblemWikisFailed}: ${err instanceof Error ? err.message : t.common.error}`, 'error')
                }
                finally {
                  setLoading(false)
                }
              }}
              disabled={loading}
              className="w-44 md:w-48 lg:w-52 text-left px-4 py-2 rounded-full text-white bg-gradient-to-r from-amber-500 to-orange-600 hover:from-amber-600 hover:to-orange-700 shadow disabled:opacity-50"
            >{t.components.K8SJobManagement.actions.problemWikis}</button>
            <button onClick={() => setShowBatchCreateJobsModal(true)} className="w-44 md:w-48 lg:w-52 text-left px-4 py-2 bg-purple-500 text-white rounded-full hover:bg-purple-600 shadow">{t.components.K8SJobManagement.actions.batchCreateJobs}</button>
          </div>
        )}
      </div>
      <div className="max-w-screen-2xl mx-auto space-y-6 relative">
        <div className="py-4">
          <div className="flex items-center justify-between flex-wrap gap-4">
            <div>
              <h1 className="text-4xl font-extrabold text-slate-900 tracking-tight">{t.components.K8SJobManagement.sandboxCenter}</h1>
              <p className="text-sm text-slate-500 mt-1">{t.components.K8SJobManagement.sandboxCenterDescription}</p>
            </div>
            <div className="p-px rounded-2xl bg-gradient-to-br from-white/60 via-white/30 to-white/10">
              <div className="bg-white/10 backdrop-blur-xl px-6 py-4 rounded-2xl border border-white/20 shadow ring-1 ring-white/30 shadow-[inset_0_1px_0_rgba(255,255,255,0.6)]">
                <div className="mb-2 text-sm text-slate-600">{t.components.K8SJobManagement.currentEnvironment}{k8sNamespace && k8sEnvironment ? `${k8sNamespace}/${k8sEnvironment}` : t.components.K8SJobManagement.createCustomJobModal.currentNamespace}</div>
                <div className="text-sm text-slate-600">{t.components.K8SJobManagement.currentJobs}</div>
                <div className={`text-5xl font-extrabold tracking-tight text-right transition-colors duration-700 ${jobsAccentColors[jobsAccentIndex]}`}>
                  <CountUp key={jobs.length} end={jobs.length} duration={1.2} separator="," />
                </div>
              </div>
            </div>
          </div>
        </div>

        {toast.show && (
          <Toast toast={toast} onClose={() => setToast({ show: false, message: '', type: 'info' })} />
        )}

        {/* 内容 Tabs */}
        <div className="p-px rounded-3xl bg-gradient-to-br from-white/60 via-white/30 to-white/10">
          <div className="bg-white/30 backdrop-blur-xl rounded-3xl shadow border border-white/40 overflow-hidden ring-1 ring-white/30">
            <div className="flex items-center justify-center gap-3 p-3 bg-white/40 border-b border-white/30">
              <button onClick={() => setActiveTab('jobs')} className={`px-6 py-3 rounded-2xl text-base font-semibold transition-all ring-1 ${activeTab === 'jobs' ? 'bg-white text-slate-900 shadow ring-white/50' : 'text-slate-600 hover:bg-white/60 ring-transparent'}`}>{t.components.K8SJobManagement.tabs.jobs}</button>
              <button onClick={() => setActiveTab('apps')} className={`px-6 py-3 rounded-2xl text-base font-semibold transition-all ring-1 ${activeTab === 'apps' ? 'bg-white text-slate-900 shadow ring-white/50' : 'text-slate-600 hover:bg-white/60 ring-transparent'}`}>应用列表</button>
              <button onClick={() => { setActiveTab('users'); setQuotaPage(1); loadUserQuotas(); setExpandedUsers({}); setUserSandboxMap({}) }} className={`px-6 py-3 rounded-2xl text-base font-semibold transition-all ring-1 ${activeTab === 'users' ? 'bg-white text-slate-900 shadow ring-white/50' : 'text-slate-600 hover:bg-white/60 ring-transparent'}`}>{t.components.K8SJobManagement.tabs.sandboxUsers}</button>
              <button onClick={() => setActiveTab('wikis')} className={`px-6 py-3 rounded-2xl text-base font-semibold transition-all ring-1 ${activeTab === 'wikis' ? 'bg-white text-slate-900 shadow ring-white/50' : 'text-slate-600 hover:bg-white/60 ring-transparent'}`}>{t.components.K8SJobManagement.tabs.wikiInfo}</button>
            </div>
            <div className="p-4 md:p-6 lg:p-8">
              {activeTab === 'jobs' && (
                <>
                  <div className="mb-4">
                    <Toolbar
                      loading={loading}
                      filterUserCode={filterUserCode}
                      setFilterUserCode={setFilterUserCode}
                      onRefreshJobs={() => loadJobs(true)}
                      onBatchQueryStatus={async (jobList) => {
                        setLoading(true)
                        try {
                          for (const job of jobList) {
                            await querySandboxStatus(job.user_code, job.git_url, job.branch, job.name)
                            await new Promise(resolve => setTimeout(resolve, 100))
                          }
                          showToast(t.components.K8SJobManagement.createCustomJob.batchQueryStatusSuccess, 'success')
                        } catch (error) {
                          showToast(`${t.components.K8SJobManagement.createCustomJob.batchQueryStatusFailed}: ${error instanceof Error ? error.message : t.common.error}`, 'error')
                        } finally {
                          setLoading(false)
                        }
                      }}
                      jobs={nonAppJobs}
                    />
                  </div>
                  <JobsTable
                    jobs={nonAppJobs}
                    loading={loading}
                    jobsRemaining={jobsRemaining}
                    jobsPageSize={jobsPageSize}
                    jobsPrevTokens={jobsPrevTokens}
                    jobsNextContinueToken={jobsNextContinueToken}
                    jobsPageInput={jobsPageInput}
                    setJobsPageSize={(n) => { setJobsPageSize(n); setJobsPrevTokens([]); setJobsContinueToken(null); setJobsNextContinueToken(null); setJobsPageInput('1'); loadJobs(true) }}
                    goPrevJobsPage={goPrevJobsPage}
                    goNextJobsPage={goNextJobsPage}
                    gotoPage={gotoPage}
                    setJobsPageInput={setJobsPageInput}
                    onViewDetails={viewJobDetails}
                    onDelete={deleteJob}
                    getSandboxStatus={getSandboxStatus}
                    querySandboxStatus={querySandboxStatus}
                    statusLoading={statusLoading}
                    appsView={false}
                    k8sNamespace={k8sNamespace}
                    k8sMasterUrl={k8sMasterUrl}
                    portalBaseUrl={portalBaseUrl}
                    enableContainerPortalButton={enableContainerPortalButton}
                    enableJumpServerButton={enableJumpServerButton}
                    jumpServerUrl={jumpServerUrl}
                  />
                </>
              )}
              {activeTab === 'apps' && (
                <>
                  <div className="mb-4">
                    <Toolbar
                      loading={loading}
                      filterUserCode={filterUserCode}
                      setFilterUserCode={setFilterUserCode}
                      onRefreshJobs={() => loadJobs(true)}
                      onBatchQueryStatus={async (jobList) => {
                        setLoading(true)
                        try {
                          for (const job of jobList) {
                            await querySandboxStatus(job.user_code, job.git_url, job.branch, job.name)
                            await new Promise(resolve => setTimeout(resolve, 100))
                          }
                          showToast(t.components.K8SJobManagement.createCustomJob.batchQueryStatusSuccess, 'success')
                        } catch (error) {
                          showToast(`${t.components.K8SJobManagement.createCustomJob.batchQueryStatusFailed}: ${error instanceof Error ? error.message : t.common.error}`, 'error')
                        } finally {
                          setLoading(false)
                        }
                      }}
                      jobs={appJobs}
                    />
                  </div>
                  <JobsTable
                    jobs={appJobs}
                    loading={loading}
                    jobsRemaining={jobsRemaining}
                    jobsPageSize={jobsPageSize}
                    jobsPrevTokens={jobsPrevTokens}
                    jobsNextContinueToken={jobsNextContinueToken}
                    jobsPageInput={jobsPageInput}
                    setJobsPageSize={(n) => { setJobsPageSize(n); setJobsPrevTokens([]); setJobsContinueToken(null); setJobsNextContinueToken(null); setJobsPageInput('1'); loadJobs(true) }}
                    goPrevJobsPage={goPrevJobsPage}
                    goNextJobsPage={goNextJobsPage}
                    gotoPage={gotoPage}
                    setJobsPageInput={setJobsPageInput}
                    onViewDetails={viewJobDetails}
                    onDelete={deleteJob}
                    getSandboxStatus={getSandboxStatus}
                    querySandboxStatus={querySandboxStatus}
                    statusLoading={statusLoading}
                    appsView={true}
                    k8sNamespace={k8sNamespace}
                    k8sMasterUrl={k8sMasterUrl}
                    portalBaseUrl={portalBaseUrl}
                    enableContainerPortalButton={enableContainerPortalButton}
                    enableJumpServerButton={enableJumpServerButton}
                    jumpServerUrl={jumpServerUrl}
                  />
                </>
              )}
              {activeTab === 'users' && (
                <>
                  <div className="mb-4">
                    <div className="bg-white/10 backdrop-blur-xl rounded-2xl shadow-lg p-5 border border-white/20">
                      <div className="flex items-end justify-between gap-4 flex-wrap">
                        <div className="flex items-center gap-3">
                          <input
                            type="text"
                            value={quotaKeyword}
                            onChange={(e) => setQuotaKeyword(e.target.value)}
                            placeholder={t.components.K8SJobManagement.searchUserPlaceholder}
                            className="w-48 md:w-56 lg:w-64 px-3 py-2 bg-white/5 border border-white/20 rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-400/50 focus:border-transparent transition-all shadow-sm"
                          />
                          <button onClick={() => { setQuotaPage(1); loadUserQuotas() }} disabled={quotaLoading} className="px-4 py-2.5 bg-gradient-to-r from-indigo-500/80 to-purple-600/80 text-white rounded-xl hover:shadow-lg hover:from-indigo-500 hover:to-purple-600 disabled:opacity-50 flex items-center gap-2 shadow-md transition-all">
                            {quotaLoading ? <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" /> : (
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 6h13M8 12h13M8 18h13M3 6h.01M3 12h.01M3 18h.01" /></svg>
                            )}
                            {t.user.search}
                          </button>
                          <button onClick={loadUserQuotas} disabled={quotaLoading} className="px-4 py-2.5 bg-white text-slate-700 rounded-xl hover:bg-slate-50 border border-white/60 disabled:opacity-50">{t.settings.refresh}</button>
                        </div>
                      </div>
                    </div>
                  </div>
                  <SandboxUserManagementTable
                    quotaUsers={quotaUsers}
                    quotaTotal={quotaTotal}
                    quotaLoading={quotaLoading}
                    quotaPage={quotaPage}
                    setQuotaPage={setQuotaPage}
                    quotaPageSize={quotaPageSize}
                    setQuotaPageSize={setQuotaPageSize}
                    loadUserQuotas={loadUserQuotas}
                    quotaEdits={quotaEdits}
                    onChangeQuotaEdit={(user_code, value) => setQuotaEdits(prev => ({ ...prev, [user_code]: value }))}
                    saveUserQuota={saveUserQuota}
                    expandedUsers={expandedUsers}
                    toggleUserSandboxes={toggleUserSandboxes}
                    userSandboxMap={userSandboxMap}
                    loadUserSandboxes={loadUserSandboxes}
                    deleteUserSandbox={deleteUserSandbox}
                  />
                </>
              )}
              {activeTab === 'wikis' && (
                <>
                  <div className="mb-4">
                    <div className="bg-white/10 backdrop-blur-xl rounded-2xl shadow-lg p-5 border border-white/20">
                      <div className="flex flex-wrap items-end justify-between gap-4">
                        <div className="flex items-center gap-3">
                          <input
                            type="text"
                            value={quotaKeyword}
                            onChange={(e) => setQuotaKeyword(e.target.value)}
                            placeholder={t.components.K8SJobManagement.searchWikiPlaceholder}
                            className="w-48 md:w-56 lg:w-64 px-3 py-2 bg-white/5 border border-white/20 rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-400/50 focus:border-transparent transition-all shadow-sm"
                          />
                          <button
                            onClick={() => {
                              setProblemWikis([])
                              setShowProblemWikis(false)
                              setGeneratingWikis(new Set())
                              setLastMetricsUpdate('')
                              setMetricsError('')
                              setIsMonitoring(false)
                              stopMetricsRefresh()
                              setMetricsRefreshInterval(null)
                            }}
                            disabled={quotaLoading}
                            className="px-4 py-2.5 bg-white text-slate-700 rounded-xl hover:bg-slate-50 border border-white/60 disabled:opacity-50"
                          >
                            {t.settings.refresh}
                          </button>
                        </div>
                        <div className="flex items-center rounded-xl bg-white/5 p-1 text-sm shadow-inner">
                          <button
                            type="button"
                            onClick={() => setWikiSubTab('info')}
                            className={`rounded-lg px-4 py-2 transition-colors ${wikiSubTab === 'info' ? 'bg-white text-slate-900 shadow-sm' : 'text-slate-200 hover:bg-white/10'}`}
                          >
                            {t.components.K8SJobManagement.tabs?.wikiInfo || 'Wiki 信息'}
                          </button>
                          <button
                            type="button"
                            onClick={() => setWikiSubTab('sync')}
                            className={`rounded-lg px-4 py-2 transition-colors ${wikiSubTab === 'sync' ? 'bg-white text-slate-900 shadow-sm' : 'text-slate-200 hover:bg-white/10'}`}
                          >
                            {t.components.K8SJobManagement.tabs?.syncIndex || '索引同步'}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                  {wikiSubTab === 'info' ? (
                    <WikiInfoManagementTable />
                  ) : (
                    <SyncQueueDashboard />
                  )}
                </>
              )}
            </div>
          </div>
        </div>

        {/* 模态框基类 */}
        {(showCreateSandboxModal || selectedJob || showCreateCustomJobModal) && (
          <div className="fixed inset-0 bg-black/40 backdrop-blur-md flex items-center justify-center z-50 p-4 transition-opacity duration-300">
            {/* 创建沙盒模态框 */}
            {showCreateSandboxModal && (
              <CreateSandboxModal
                show={showCreateSandboxModal}
                loading={loading}
                userCode={newSandboxUserCode}
                setUserCode={setNewSandboxUserCode}
                gitUrl={newSandboxGitUrl}
                setGitUrl={setNewSandboxGitUrl}
                branch={newSandboxBranch}
                setBranch={setNewSandboxBranch}
                onCreate={createSandbox}
                onClose={() => setShowCreateSandboxModal(false)}
              />
            )}

            {/* Job详情模态框 */}
            {selectedJob && (
              <JobDetailsModal
                job={selectedJob}
                metricsRefreshInterval={metricsRefreshInterval}
                isMonitoring={isMonitoring}
                lastMetricsUpdate={lastMetricsUpdate}
                metricsError={metricsError}
                startMetricsRefresh={startMetricsRefresh}
                stopMetricsRefresh={stopMetricsRefresh}
                onClose={() => setSelectedJob(null)}
                getSandboxStatus={getSandboxStatus}
                statusLoading={statusLoading}
                querySandboxStatus={querySandboxStatus}
                onJobUpdated={handleJobMetadataUpdated}
                k8sNamespace={k8sNamespace}
                k8sMasterUrl={k8sMasterUrl}
                portalBaseUrl={portalBaseUrl}
              />
            )}

            {/* 创建自定义Job模态框 */}
            {showCreateCustomJobModal && (
              <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div className="p-6 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <h2 className="text-2xl font-bold text-gray-900">{t.components.K8SJobManagement.createCustomJobModal.title}</h2>
                    <button
                      onClick={() => setShowCreateCustomJobModal(false)}
                      className="text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                </div>
                
                <div className="p-6 space-y-6">
                  {/* 基本信息 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">{t.components.K8SJobManagement.createCustomJobModal.jobName} *</label>
                      <input
                        type="text"
                        value={customJobForm.job_name}
                        onChange={(e) => setCustomJobForm(prev => ({ ...prev, job_name: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="my-custom-job"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">{t.components.K8SJobManagement.createCustomJobModal.containerImage} *</label>
                      <input
                        type="text"
                        value={customJobForm.image}
                        onChange={(e) => setCustomJobForm(prev => ({ ...prev, image: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="nginx:latest"
                      />
                    </div>
                  </div>

                  {/* 资源配置 */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">{t.components.K8SJobManagement.createCustomJobModal.cpuRequest}</label>
                      <input
                        type="text"
                        value={customJobForm.cpu_request}
                        onChange={(e) => setCustomJobForm(prev => ({ ...prev, cpu_request: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="10m"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">{t.components.K8SJobManagement.createCustomJobModal.memoryRequest}</label>
                      <input
                        type="text"
                        value={customJobForm.memory_request}
                        onChange={(e) => setCustomJobForm(prev => ({ ...prev, memory_request: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="256Mi"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">{t.components.K8SJobManagement.createCustomJobModal.cpuLimit}</label>
                      <input
                        type="text"
                        value={customJobForm.cpu_limit}
                        onChange={(e) => setCustomJobForm(prev => ({ ...prev, cpu_limit: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="100m"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">{t.components.K8SJobManagement.createCustomJobModal.memoryLimit}</label>
                      <input
                        type="text"
                        value={customJobForm.memory_limit}
                        onChange={(e) => setCustomJobForm(prev => ({ ...prev, memory_limit: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="256Mi"
                      />
                    </div>
                  </div>

                  {/* 卷挂载配置 */}
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <label className="block text-sm font-medium text-gray-700">{t.components.K8SJobManagement.createCustomJobModal.volumeMountConfig} *</label>
                      <button
                        type="button"
                        onClick={addVolume}
                        className="px-3 py-1 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm"
                      >
                        {t.components.K8SJobManagement.createCustomJobModal.addVolume}
                      </button>
                    </div>
                    <div className="space-y-3">
                      {customJobForm.volumes.map((volume, index) => (
                        <div key={index} className="grid grid-cols-1 md:grid-cols-4 gap-3 items-end">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">{t.components.K8SJobManagement.createCustomJobModal.volumeName}</label>
                            <input
                              type="text"
                              value={volume.name}
                              onChange={(e) => updateVolume(index, 'name', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="volume-name"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">{t.components.K8SJobManagement.createCustomJobModal.hostPath}</label>
                            <input
                              type="text"
                              value={volume.host_path}
                              onChange={(e) => updateVolume(index, 'host_path', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="/host/path"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">{t.components.K8SJobManagement.createCustomJobModal.containerPath}</label>
                            <input
                              type="text"
                              value={volume.container_path}
                              onChange={(e) => updateVolume(index, 'container_path', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="/container/path"
                            />
                          </div>
                          <div className="flex items-center gap-2">
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                checked={volume.read_only}
                                onChange={(e) => updateVolume(index, 'read_only', e.target.checked)}
                                className="mr-2"
                              />
                              <span className="text-sm text-gray-700">{t.components.K8SJobManagement.createCustomJobModal.readOnly}</span>
                            </label>
                            {customJobForm.volumes.length > 1 && (
                              <button
                                type="button"
                                onClick={() => removeVolume(index)}
                                className="px-2 py-1 bg-red-500 text-white rounded hover:bg-red-600 transition-colors text-sm"
                              >
                                {t.components.K8SJobManagement.createCustomJobModal.delete}
                              </button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 环境变量配置 */}
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <label className="block text-sm font-medium text-gray-700">{t.components.K8SJobManagement.createCustomJobModal.envVarConfig}</label>
                      <button
                        type="button"
                        onClick={addEnvVar}
                        className="px-3 py-1 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm"
                      >
                        {t.components.K8SJobManagement.createCustomJobModal.addEnvVar}
                      </button>
                    </div>
                    <div className="space-y-3">
                      {customJobForm.env_vars.map((env, index) => (
                        <div key={index} className="grid grid-cols-1 md:grid-cols-3 gap-3 items-end">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">{t.components.K8SJobManagement.createCustomJobModal.varName}</label>
                            <input
                              type="text"
                              value={env.name}
                              onChange={(e) => updateEnvVar(index, 'name', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder={t.components.K8SJobManagement.createCustomJobModal.varNamePlaceholder}
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">{t.components.K8SJobManagement.createCustomJobModal.varValue}</label>
                            <input
                              type="text"
                              value={env.value}
                              onChange={(e) => updateEnvVar(index, 'value', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder={t.components.K8SJobManagement.createCustomJobModal.varValuePlaceholder}
                            />
                          </div>
                          <div>
                            {customJobForm.env_vars.length > 1 && (
                              <button
                                type="button"
                                onClick={() => removeEnvVar(index)}
                                className="px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors text-sm"
                              >
                                {t.components.K8SJobManagement.createCustomJobModal.delete}
                              </button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 可选信息 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">{t.components.K8SJobManagement.createCustomJobModal.userCode}</label>
                      <input
                        type="text"
                        value={customJobForm.user_code}
                        onChange={(e) => setCustomJobForm(prev => ({ ...prev, user_code: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder={t.components.K8SJobManagement.createCustomJobModal.userCodePlaceholder}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">{t.components.K8SJobManagement.createCustomJobModal.userName}</label>
                      <input
                        type="text"
                        value={customJobForm.user_name}
                        onChange={(e) => setCustomJobForm(prev => ({ ...prev, user_name: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder={t.components.K8SJobManagement.createCustomJobModal.userNamePlaceholder}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">{t.components.K8SJobManagement.createCustomJobModal.gitRepoUrl}</label>
                      <input
                        type="text"
                        value={customJobForm.git_url}
                        onChange={(e) => setCustomJobForm(prev => ({ ...prev, git_url: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder={t.components.K8SJobManagement.createCustomJobModal.gitRepoUrlPlaceholder}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">{t.components.K8SJobManagement.createCustomJobModal.branchName}</label>
                      <input
                        type="text"
                        value={customJobForm.branch}
                        onChange={(e) => setCustomJobForm(prev => ({ ...prev, branch: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder={t.components.K8SJobManagement.createCustomJobModal.branchNamePlaceholder}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">{t.components.K8SJobManagement.createCustomJobModal.namespace}</label>
                      <input
                        type="text"
                        value={customJobForm.namespace}
                        onChange={(e) => setCustomJobForm(prev => ({ ...prev, namespace: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder={t.components.K8SJobManagement.createCustomJobModal.namespacePlaceholder}
                      />
                    </div>
                  </div>
                </div>

                <div className="p-6 border-t border-gray-200 flex justify-end gap-3">
                  <button
                    onClick={() => setShowCreateCustomJobModal(false)}
                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    {t.components.K8SJobManagement.createCustomJobModal.cancel}
                  </button>
                  <button
                    onClick={createCustomJob}
                    disabled={loading}
                    className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 transition-colors"
                  >
                    {loading ? t.components.K8SJobManagement.createCustomJobModal.creating : t.components.K8SJobManagement.createCustomJobModal.createJob}
                  </button>
                </div>
              </div>
            )}
          </div>
        )}

        {/* 快速创建Wiki模态框 */}
        <QuickWikiModal
          show={showQuickWikiModal}
          onClose={() => setShowQuickWikiModal(false)}
          onSuccess={handleQuickWikiSuccess}
        />

        <ProblemWikisModal
          show={showProblemWikis}
          onClose={() => setShowProblemWikis(false)}
          problemWikis={problemWikis}
          onFastGenerate={handleFastGenerate}
          generatingWikis={generatingWikis}
        />

        {/* 批量创建Jobs模态框 */}
        {showBatchCreateJobsModal && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-xl font-bold text-gray-900">{t.components.K8SJobManagement.batchCreateJobsModal.title}</h2>
              </div>
              <div className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t.components.K8SJobManagement.batchCreateJobsModal.jobNumber}
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={batchJobNumber}
                    onChange={(e) => setBatchJobNumber(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    placeholder={t.components.K8SJobManagement.batchCreateJobsModal.jobNumberPlaceholder}
                  />
                </div>
              </div>
              <div className="px-6 py-4 border-t border-gray-200 flex justify-end gap-3">
                <button
                  onClick={() => {
                    setShowBatchCreateJobsModal(false)
                    setBatchJobNumber('')
                  }}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  {t.components.K8SJobManagement.batchCreateJobsModal.cancel}
                </button>
                <button
                  onClick={handleBatchCreateJobs}
                  disabled={loading}
                  className="px-6 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50 transition-colors"
                >
                  {loading ? t.components.K8SJobManagement.batchCreateJobsModal.creating : t.components.K8SJobManagement.batchCreateJobsModal.confirm}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
