'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import { fetchModelConfig } from '../utils/modelConfigCache';
import { ModelConfig, ModelSettings } from '@/types/modelConfig'; 

interface RefreshSectionOption {
  id: string;
  title: string;
  pages: string[];
}

interface RefreshOptions {
  forceRefresh: boolean;
  refreshPages?: string[];
  rebuildStructure: boolean;
  customInstructions?: string;
}

interface RefreshModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (modelSettings: ModelSettings, options: RefreshOptions) => void;
  currentModel?: string;
  apiKey?: string;
  sections?: RefreshSectionOption[];
}

export default function RefreshModal({
  isOpen,
  onClose,
  onConfirm,
  currentModel = 'gemini-2.5-flash',
  apiKey = '',
  sections = []
}: RefreshModalProps) {
  
  const [modelConfig, setModelConfig] = useState<ModelConfig | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // 固定使用whalecloud作为provider
  const [selectedProvider] = useState('whalecloud');
  const [selectedModel, setSelectedModel] = useState(currentModel);
  const [forceRefresh, setForceRefresh] = useState(false);
  const [rebuildStructure, setRebuildStructure] = useState(false);
  const [isCustomModel] = useState(false);
  const [customModel] = useState('');
  const [selectedSectionIds, setSelectedSectionIds] = useState<Set<string>>(new Set());
  const [customInstructions, setCustomInstructions] = useState('');
  const { messages } = useLanguage();

  const hasSections = sections.length > 0;
  const sectionLabel = messages.components?.refreshModal?.selectSections ?? '选择需要刷新的章节';
  const sectionHelper = messages.components?.refreshModal?.selectSectionsHelper ?? '未选择章节时将刷新全部页面。';

  const selectedPageIds = useMemo(() => {
    if (!hasSections || selectedSectionIds.size === 0) {
      return [];
    }
    const pageSet = new Set<string>();
    sections.forEach((section) => {
      if (selectedSectionIds.has(section.id)) {
        section.pages.forEach((pageId) => pageSet.add(pageId));
      }
    });
    return Array.from(pageSet);
  }, [hasSections, sections, selectedSectionIds]);

  const selectedPageCount = selectedPageIds.length;

  const selectionSummary = selectedPageCount > 0
    ? (messages.components?.refreshModal?.selectedPagesSummary
        ? messages.components.refreshModal.selectedPagesSummary.replace('{count}', selectedPageCount.toString())
        : `已选择 ${selectedPageCount} 个页面`)
    : (messages.components?.refreshModal?.noSectionSelected ?? '未选择章节，将刷新整个 Wiki。');
  const clearSelectionLabel = messages.components?.refreshModal?.clearSelection ?? '清除选择';

  // 加载模型配置
  useEffect(() => {
    const loadModelConfig = async () => {
      if (!isOpen) return;
      
      try {
        setIsLoading(true);
        setError(null);
        
        const config = await fetchModelConfig();
        setModelConfig(config);
        
        // 设置默认模型
        const provider = config.providers.find(p => p.id === 'whalecloud');
        if (provider && provider.models.length > 0) {
          const modelExists = provider.models.find(m => m.id === selectedModel);
          if (!modelExists) {
            setSelectedModel(provider.models[0].id);
          }
        }
        
      } catch (err) {
        console.error('Failed to fetch model configurations:', err);
        setError('Failed to load model configurations. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    loadModelConfig();
  }, [isOpen, selectedModel]);

  useEffect(() => {
    if (!isOpen) {
      setSelectedSectionIds(new Set());
      setForceRefresh(false);
      setRebuildStructure(false);
      setCustomInstructions('');
    }
  }, [isOpen]);

  const handleConfirm = () => {
    const modelToUse = isCustomModel ? customModel : selectedModel;
    
    const modelSettings: ModelSettings = {
      provider: selectedProvider,
      model: modelToUse,
      api_key: apiKey,
      model_kwargs: {}
    };

    const pagesToRefresh = selectedPageCount > 0 ? selectedPageIds : undefined;

    onConfirm(modelSettings, {
      forceRefresh,
      refreshPages: pagesToRefresh,
      rebuildStructure,
      customInstructions: customInstructions.trim() || undefined,
    });
    setSelectedSectionIds(new Set());
    setCustomInstructions('');
    onClose();
  };

  // 获取whalecloud的可用模型
  const availableModels = modelConfig?.providers.find(p => p.id === 'whalecloud')?.models || [];
  // const currentProviderConfig = modelConfig?.providers.find(p => p.id === 'whalecloud');

  const toggleSection = (sectionId: string) => {
    setSelectedSectionIds((prev) => {
      const next = new Set(prev);
      if (next.has(sectionId)) {
        next.delete(sectionId);
      } else {
        next.add(sectionId);
      }
      return next;
    });
  };

  const clearSectionSelection = () => {
    setSelectedSectionIds(new Set());
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-[var(--background)] border border-[var(--border-color)] rounded-lg shadow-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* 标题 */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-[var(--foreground)]">
              {messages.components.refreshModal.title}
            </h2>
            <button
              onClick={onClose}
              className="text-[var(--muted)] hover:text-[var(--foreground)] text-2xl leading-none"
            >
              ×
            </button>
          </div>

          {/* 错误信息 */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md text-red-700 text-sm">
              {error}
            </div>
          )}

          {/* 加载状态 */}
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-sm text-[var(--muted)]">
                {messages.components.refreshModal.loadingModelConfigurations}
              </div>
            </div>
          ) : modelConfig ? (
            <div className="space-y-4">
              {/* AI提供商信息（只显示whalecloud） */}
              <div>
                <label className="block text-sm font-medium text-[var(--foreground)] mb-2">
                  {messages.components.refreshModal.aiProvider}
                </label>
                <div className="px-3 py-2 border border-[var(--border-color)] rounded-md bg-[var(--background)] text-[var(--foreground)]">
                  {messages.components.refreshModal.whaleCloud}
                </div>
              </div>

              {/* 模型选择 */}
              <div>
                <label className="block text-sm font-medium text-[var(--foreground)] mb-2">
                  {messages.components.refreshModal.selectModel}
                </label>
                <div className="space-y-2">
                  <select
                      value={selectedModel}
                      onChange={(e) => setSelectedModel(e.target.value)}
                      className="w-full px-3 py-2 border border-[var(--border-color)] rounded-md bg-[var(--background)] text-[var(--foreground)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] focus:border-transparent"
                      disabled={availableModels.length === 0}
                    >
                      {availableModels.map((model) => (
                        <option key={model.id} value={model.id}>
                          {model.name}
                        </option>
                      ))}
                    </select>
              </div>
            </div>

            {/* 暂时注释掉重新生成结构选项 */}
            {/* <div className="flex items-start gap-3 py-2">
              <input
                id="rebuild-structure"
                type="checkbox"
                checked={rebuildStructure}
                onChange={(event) => setRebuildStructure(event.target.checked)}
                className="mt-1 h-4 w-4 text-[var(--accent-primary)] focus:ring-[var(--accent-primary)] border-[var(--border-color)] rounded"
              />
              <div>
                <label
                  htmlFor="rebuild-structure"
                  className="text-sm font-medium text-[var(--foreground)]"
                >
                  {messages.components?.refreshModal?.rebuildStructureLabel ?? '重建章节结构'}
                </label>
                <p className="text-xs text-[var(--muted)] mt-1">
                  {messages.components?.refreshModal?.rebuildStructureHelper ?? '即使没有检测到代码变更，也重新创建章节划分。'}
                </p>
              </div>
            </div> */}

            {hasSections && (
              <div className="pt-4">
                <label className="block text-sm font-medium text-[var(--foreground)] mb-2">
                  {sectionLabel}
                </label>
                  <p className="text-xs text-[var(--muted)] mb-2">
                    {sectionHelper}
                  </p>
                  <div className="border border-[var(--border-color)] rounded-md divide-y divide-[var(--border-color)] max-h-48 overflow-y-auto">
                    {sections.map((section) => {
                      const isSelected = selectedSectionIds.has(section.id);
                      return (
                        <label
                          key={section.id}
                          className="flex items-start gap-3 px-3 py-2 cursor-pointer hover:bg-[var(--muted)]/10"
                        >
                          <input
                            type="checkbox"
                            checked={isSelected}
                            onChange={() => toggleSection(section.id)}
                            className="mt-1 h-4 w-4 text-[var(--accent-primary)] focus:ring-[var(--accent-primary)] border-[var(--border-color)] rounded"
                          />
                          <div className="flex-1">
                            <p className="text-sm font-medium text-[var(--foreground)] line-clamp-1">{section.title}</p>
                            <p className="text-xs text-[var(--muted)] mt-0.5">
                              {messages.components?.refreshModal?.sectionPageCount
                                ? messages.components.refreshModal.sectionPageCount.replace('{count}', section.pages.length.toString())
                                : `${section.pages.length} 个页面`}
                            </p>
                          </div>
                        </label>
                      );
                    })}
                  </div>
                  <div className="flex items-center justify-between mt-2 text-xs text-[var(--muted)]">
                    <span>{selectionSummary}</span>
                    {selectedSectionIds.size > 0 && (
                      <button
                        type="button"
                        onClick={clearSectionSelection}
                        className="text-[var(--accent-primary)] hover:text-[var(--accent-primary)]/80"
                      >
                        {clearSelectionLabel}
                      </button>
                    )}
                </div>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-[var(--foreground)] mb-2">
                {messages.components?.refreshModal?.customInstructionLabel ?? '额外指令（可选）'}
              </label>
              <textarea
                value={customInstructions}
                onChange={(event) => setCustomInstructions(event.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-[var(--border-color)] rounded-md bg-[var(--background)] text-[var(--foreground)] text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)]"
                placeholder={messages.components?.refreshModal?.customInstructionPlaceholder ?? '补充刷新目的、生成风格或其他要求。'}
              />
            </div>

            {/* 强制刷新选项 */}
            <div className="pt-4">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="force-refresh"
                    checked={forceRefresh}
                    onChange={(e) => setForceRefresh(e.target.checked)}
                    className="h-4 w-4 text-[var(--accent-primary)] focus:ring-[var(--accent-primary)] border-[var(--border-color)] rounded"
                  />
                  <label htmlFor="force-refresh" className="text-sm text-[var(--foreground)]">
                    {messages.components.refreshModal.forceRefresh}
                  </label>
                </div>
                <p className="text-xs text-[var(--muted)] mt-1 ml-7">
                  {messages.components.refreshModal.forceRefreshDescription}
                </p>
              </div>
            </div>
          ) : null}

          {/* 按钮 */}
          <div className="flex gap-3 mt-6">
            <button
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-[var(--border-color)] text-[var(--foreground)] rounded-md hover:bg-[var(--muted)]/10 transition-colors"
            >
              {messages.components.refreshModal.cancel}
            </button>
            <button
              onClick={handleConfirm}
              disabled={isLoading || (isCustomModel && !customModel.trim()) || !modelConfig}
              className="flex-1 px-4 py-2 bg-[var(--accent-primary)] text-white rounded-md hover:bg-[var(--accent-primary)]/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {messages.components.refreshModal.confirmRefresh}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
} 
