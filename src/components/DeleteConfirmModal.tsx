'use client';

import React from 'react';
import { FaTimes, FaExclamationTriangle } from 'react-icons/fa';
import { useLanguage } from '@/contexts/LanguageContext';

interface DeleteConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  itemName?: string;
  isDeleting?: boolean;
}

export default function DeleteConfirmModal({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  itemName,
  isDeleting = false
}: DeleteConfirmModalProps) {
  const { messages: t } = useLanguage();

  if (!isOpen) return null;

  const formattedMessage = itemName ? message.replace('{name}', itemName) : message;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm">
      <div className="relative w-full max-w-md bg-[var(--card-bg)] rounded-lg shadow-lg border border-[var(--border-color)]">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-3 right-3 p-1.5 rounded-full text-[var(--muted)] hover:bg-[var(--accent-secondary)] hover:text-[var(--foreground)] transition-colors"
          disabled={isDeleting}
        >
          <FaTimes className="w-4 h-4" />
        </button>

        {/* Content */}
        <div className="p-6">
          {/* Header with icon */}
          <div className="flex items-start gap-3 mb-4">
            <FaExclamationTriangle className="flex-shrink-0 w-5 h-5 text-yellow-500 mt-0.5" />
            <div className="flex-1">
              <h4 className="text-lg font-medium text-[var(--foreground)] mb-2">
                {title}
              </h4>
              <p className="text-sm text-[var(--muted)] leading-relaxed">
                {formattedMessage}
              </p>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2 mt-6">
            <button
              onClick={onClose}
              className="px-3 py-1.5 text-sm font-medium text-[var(--muted)] bg-[var(--background)] border border-[var(--border-color)] hover:bg-[var(--accent-secondary)] hover:border-[var(--border-color)] rounded-md transition-all duration-200"
              disabled={isDeleting}
            >
              {t.common.cancel}
            </button>
            <button
              onClick={onConfirm}
              className="px-3 py-1.5 text-sm font-medium text-white bg-[var(--accent-primary)] hover:bg-[var(--accent-primary)]/80 rounded-md transition-colors duration-200 flex items-center gap-1 disabled:opacity-50"
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>{t.common.processing}</span>
                </>
              ) : (
                <span>{t.common.confirm}</span>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
