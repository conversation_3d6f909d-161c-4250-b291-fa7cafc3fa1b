'use client';

import React, { useEffect, useRef } from 'react';
import { FileSearchSuggestion } from '@/hooks/useFileSearch';
import { useLanguage } from '@/contexts/LanguageContext';
import FileIcon from './FileIcon';

interface FileSearchSuggestionsProps {
  suggestions: FileSearchSuggestion[];
  activeIndex: number;
  isLoading: boolean;
  onSelect: (suggestion: FileSearchSuggestion) => void;
  onHover: (index: number) => void;
  maxHeight?: number;
  showSearchBox?: boolean;
  searchQuery?: string;
  onSearchQueryChange?: (query: string) => void;
  onRequestClose?: () => void;
  onSelectDirectory?: (suggestion: FileSearchSuggestion) => void;
  keepOpenWhenEmpty?: boolean;
}

const FileSearchSuggestions: React.FC<FileSearchSuggestionsProps> = ({
  suggestions,
  activeIndex,
  isLoading,
  onSelect,
  onHover,
  maxHeight = 300,
  showSearchBox = false,
  searchQuery = '',
  onSearchQueryChange,
  onRequestClose,
  onSelectDirectory,
  keepOpenWhenEmpty = false,
}) => {
  const { messages } = useLanguage();
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (showSearchBox && inputRef.current) {
      inputRef.current.focus();
    }
  }, [showSearchBox]);

  // 点击组件外部时关闭建议框
  useEffect(() => {
    if (!showSearchBox) return;

    const handleClickOutside = (event: MouseEvent) => {
      const container = containerRef.current;
      if (!container) return;
      if (event.target instanceof Node && !container.contains(event.target)) {
        onRequestClose?.();
      }
    };

    document.addEventListener('mousedown', handleClickOutside, true);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside, true);
    };
  }, [showSearchBox, onRequestClose]);

  // 当 activeIndex 或建议列表变化时，将当前高亮项滚动到视图内
  useEffect(() => {
    const listEl = listRef.current;
    if (!listEl) return;
    const items = listEl.querySelectorAll('[data-suggestion-item="true"]');
    const el = items[activeIndex] as HTMLElement | undefined;
    if (el && typeof el.scrollIntoView === 'function') {
      el.scrollIntoView({ block: 'nearest' });
    }
  }, [activeIndex, suggestions]);

  // 路径截断显示函数 - 按目录级别省略
  const truncatePath = (path: string, maxLength: number = 50): string => {
    if (path.length <= maxLength) {
        return path;
    }

    const isDirectory = path.endsWith('/');
    const cleanPath = isDirectory ? path.slice(0, -1) : path;
    const parts = cleanPath.split('/');
    
    // If path has no directory structure, just truncate from the start.
    if (parts.length <= 1) {
        return `...${path.slice(path.length - maxLength + 3)}`;
    }

    let truncated = '';
    for (let i = parts.length - 1; i >= 0; i--) {
        const part = parts[i];
        const next = truncated === '' ? part : `${part}/${truncated}`;
        
        if (`.../${next}`.length > maxLength) {
            break;
        }
        truncated = next;
    }

    // If even the last part is too long, truncate it.
    if (truncated === '') {
        const lastPart = parts[parts.length - 1];
        if (lastPart.length > maxLength) {
            return `...${lastPart.slice(lastPart.length - maxLength + 3)}`;
        }
        truncated = lastPart;
    }

    const finalPath = parts.slice(parts.length - truncated.split('/').length).join('/');
    
    if (cleanPath !== finalPath) {
        truncated = `.../${truncated}`;
    }

    if (isDirectory && !truncated.endsWith('/')) {
        truncated += '/';
    }

    return truncated;
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    const navigateUp = () => {
      if (!onSearchQueryChange) return;
      const q = (searchQuery || '').trim();
      if (!q) {
        onSearchQueryChange('/');
        return;
      }
      const noTrailing = q.endsWith('/') ? q.slice(0, -1) : q;
      const lastSlash = noTrailing.lastIndexOf('/');
      const parent = lastSlash > -1 ? noTrailing.slice(0, lastSlash + 1) : '/';
      onSearchQueryChange(parent);
      // 重新聚焦输入框
      if (inputRef.current) {
        // 下一帧再聚焦，避免与按键事件冲突
        requestAnimationFrame(() => inputRef.current?.focus());
      }
    };

    if (e.key === 'Escape') {
      e.preventDefault();
      onRequestClose?.();
      return;
    }
    if (e.altKey && e.key === 'ArrowLeft') {
      e.preventDefault();
      navigateUp();
      return;
    }
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (suggestions && suggestions.length > 0) {
        const next = Math.min(activeIndex + 1, suggestions.length - 1);
        onHover(next);
      }
      return;
    }
    if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (suggestions && suggestions.length > 0) {
        const prev = Math.max(activeIndex - 1, 0);
        onHover(prev);
      }
      return;
    }
    if (e.key === 'Enter') {
      if (suggestions && suggestions.length > 0 && suggestions[activeIndex]) {
        e.preventDefault();
        const currentSuggestion = suggestions[activeIndex];
        
        // Ctrl+Enter: 选择目录（如果是目录的话）
        if (e.ctrlKey && currentSuggestion.isDirectory && onSelectDirectory) {
          onSelectDirectory(currentSuggestion);
        } else {
          // Enter: 正常选择（进入目录或选择文件）
          onSelect(currentSuggestion);
        }
      }
      return;
    }
  };
  
  if (!showSearchBox && !isLoading && !suggestions.length && !keepOpenWhenEmpty) {
    return null;
  }

  return (
    <div 
      ref={containerRef}
      className="absolute left-0 bottom-full mb-12 w-full bg-white border border-gray-200 rounded-lg shadow-lg z-50 overflow-hidden flex flex-col"
      style={{ 
        backgroundColor: 'var(--card-bg)', 
        borderColor: 'var(--border-color)',
        maxHeight: `${maxHeight}px`
      }}
      onKeyDown={handleKeyDown}
      tabIndex={-1}
    >
      {showSearchBox && (
        <div className="p-2 border-b border-gray-200" style={{ borderColor: 'var(--border-color)' }}>
          <input
            ref={inputRef}
            type="text"
            placeholder={messages.components?.fileSearchSuggestions?.searchPlaceholder || 'Search files or directories...'}
            value={searchQuery}
            onChange={(e) => onSearchQueryChange?.(e.target.value)}
            onKeyDown={handleKeyDown}
            className="w-full px-2 py-1.5 text-sm border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 bg-transparent"
            style={{
              borderColor: 'var(--border-color)',
              color: 'var(--foreground)'
            }}
          />
          {/* 返回上级按钮 */}
          <div className="mt-2 flex justify-between items-center">
            <button
              type="button"
              onClick={() => {
                const q = (searchQuery || '').trim();
                const noTrailing = q.endsWith('/') ? q.slice(0, -1) : q;
                const lastSlash = noTrailing.lastIndexOf('/');
                const parent = lastSlash > -1 ? noTrailing.slice(0, lastSlash + 1) : '/';
                onSearchQueryChange?.(parent);
                if (inputRef.current) {
                  requestAnimationFrame(() => inputRef.current?.focus());
                }
              }}
              className="text-xs text-blue-600 hover:text-blue-800"
              title={messages.components?.fileSearchSuggestions?.goUp || 'Go Up (Alt+←)'}
              disabled={!searchQuery || searchQuery === ''}
            >
              {messages.components?.fileSearchSuggestions?.goUp || 'Go Up (Alt+←)'}
            </button>
            {/* 预留右侧空间，可扩展快捷提示 */}
            <span className="text-[10px] text-gray-400">{messages.components?.fileSearchSuggestions?.close || 'Esc Close'}</span>
          </div>
        </div>
      )}
      
      {suggestions.length === 0 ? (
        <div className="p-3 text-sm text-center text-gray-500" style={{ color: 'var(--muted)' }}>
          {isLoading ? (messages.components?.fileSearchSuggestions?.searching || 'Searching...') : (messages.components?.fileSearchSuggestions?.noResults || 'No matches found')}
        </div>
      ) : (
        <div ref={listRef} className="overflow-y-auto" style={{ maxHeight: `${maxHeight}px` }}>
          {suggestions.map((suggestion, index) => (
            <div
              key={`${suggestion.value}-${index}`}
              data-suggestion-item="true"
              className={`px-3 py-2 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors duration-150 ${
                index === activeIndex 
                  ? 'bg-blue-50 text-blue-700' 
                  : 'hover:bg-gray-50 text-gray-700'
              }`}
              style={index === activeIndex ? {
                backgroundColor: 'var(--accent-secondary)',
                color: 'var(--accent-primary)'
              } : {
                color: 'var(--foreground)'
              }}
              onClick={() => onSelect(suggestion)}
              onMouseEnter={() => onHover(index)}
              onKeyDown={handleKeyDown}
            >
              <div className="flex items-center space-x-2">
                {/* 文件/目录图标 */}
                <div className="flex-shrink-0">
                  <FileIcon 
                    fileName={suggestion.label} 
                    isDirectory={suggestion.isDirectory} 
                    className="w-4 h-4" 
                  />
                </div>
                
                {/* 文件路径 */}
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium truncate" title={suggestion.label}>
                    {suggestion.isDirectory ? (
                      <span className="flex items-center">
                        {truncatePath(suggestion.label)}
                        {suggestion.label.endsWith('/') ? '' : '/'}
                      </span>
                    ) : (
                      truncatePath(suggestion.label)
                    )}
                  </div>
                  {suggestion.description && (
                    <div className="text-xs text-gray-500 truncate" style={{ color: 'var(--muted)' }}>
                      {suggestion.description}
                    </div>
                  )}
                </div>
                
                {/* 目录选择按钮和类型标识 */}
                <div className="flex items-center space-x-2 flex-shrink-0">
                  {/* 目录选择按钮 */}
                  {suggestion.isDirectory && onSelectDirectory && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onSelectDirectory(suggestion);
                      }}
                      className="px-2 py-1 text-xs bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors duration-150"
                      title={messages.components?.fileSearchSuggestions?.selectDirectory || 'Select Directory (Ctrl+Enter)'}
                    >
                      {messages.components?.fileSearchSuggestions?.select || 'Select'}
                    </button>
                  )}
                  
                  {/* 类型标识 */}
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    suggestion.isDirectory 
                      ? 'bg-blue-100 text-blue-600' 
                      : 'bg-gray-100 text-gray-600'
                  }`}>
                    {suggestion.isDirectory ? (messages.components?.fileSearchSuggestions?.directory || 'Directory') : (messages.components?.fileSearchSuggestions?.file || 'File')}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      
      {/* 提示信息 */}
      <div className="px-3 py-2 bg-gray-50 border-t border-gray-200 text-xs text-gray-500" style={{ backgroundColor: 'var(--accent-secondary)', borderColor: 'var(--border-color)', color: 'var(--muted)' }}>
        <div className="flex justify-between items-center">
          <span>{(messages.components?.fileSearchSuggestions?.totalResults || '{count} results').replace('{count}', suggestions.length.toString())}</span>
          {isLoading && <span className="text-xs text-gray-500">{messages.components?.fileSearchSuggestions?.searching || 'Searching...'}</span>}
          <span>{messages.components?.fileSearchSuggestions?.keyboardShortcuts || '↑↓ Select • Enter Enter/Select • Ctrl+Enter Select Directory • Esc Cancel'}</span>
        </div>
      </div>
    </div>
  );
};

export default FileSearchSuggestions; 