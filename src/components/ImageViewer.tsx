import React from "react";
import { createPortal } from "react-dom";
import { GrClose } from "react-icons/gr";

interface ImageViewerProps {
  data: string;
  onClose: () => void;
}

const ImageViewer = (props: ImageViewerProps) => {
  const { data, onClose } = props;
  return createPortal(
    <div className="flex justify-center items-center absolute top-0 bottom-0 left-0 right-0 z-50 bg-black/85">
      <button
        className="absolute top-8 right-8 text-white p-2 rounded-full bg-black/30 cursor-pointer text-xl"
        onClick={onClose}
      >
        <GrClose />
      </button>
      <img height='100%' width='auto' className="object-contain" src={data} />
    </div>,
    document.body
  );
};

export default ImageViewer;
