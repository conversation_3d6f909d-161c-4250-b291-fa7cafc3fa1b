'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { FaRedo, FaCheck, FaPlay, FaStop, FaEye } from 'react-icons/fa';
import { authFetch } from '@/utils/authFetch';
import { useAuth } from '@/contexts/AuthContext';
import { SubRepoInfo } from '@/types/repoinfo';
import { useToast } from '@/contexts/ToastContext';
import DeleteConfirmModal from './DeleteConfirmModal';
import { useLanguage, Messages } from '@/contexts/LanguageContext';

// --- Inline SVG Icons ---
const RefreshIcon = ({ className = '' }: { className?: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 ${className}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
  </svg>
);

const CloseIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
    </svg>
);

const StatusIcon = ({ status }: { status: string }) => {
    switch (status) {
        case 'completed':
            return <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-600 dark:text-green-400" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" /></svg>;
        case 'processing':
        case 'resuming':
        case 'syncing':
            return <svg className="h-5 w-5 text-blue-600 dark:text-blue-400 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>;
        case 'failed':
        case 'timeout':
            return <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-600 dark:text-red-400" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" /></svg>;
        case 'cancelled':
            return <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-orange-600 dark:text-orange-400" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9.5 8.293 11.793a1 1 0 101.414 1.414L12 10.914l2.293 2.293a1 1 0 001.414-1.414L13.414 9.5l2.293-2.293a1 1 0 00-1.414-1.414L12 8.086 9.707 5.793a1 1 0 00-1.414 1.414L10.586 9.5z" clipRule="evenodd" /></svg>;
        case 'paused':
            return <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-600 dark:text-yellow-400" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 012 0v6a1 1 0 11-2 0V7zM12 7a1 1 0 012 0v6a1 1 0 11-2 0V7z" clipRule="evenodd" /></svg>;
        case 'pending':
        case 'pending_resume':
        default:
            return <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 dark:text-gray-400" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.414-1.415L11 9.586V6z" clipRule="evenodd" /></svg>;
    }
};


interface WikiJob {
  id: string;
  repo_url: string;
  repo_owner: string;
  repo_name: string;
  branch: string;
  job_type?: number | string | null;
  status: 'pending' | 'pending_resume' | 'processing' | 'resuming' | 'completed' | 'failed' | 'cancelled' | 'timeout' | 'paused' | 'syncing';
  progress: number;
  message?: string;
  total_files?: number | string | null;
  processed_files?: number | string | null;
  error_message?: string; 
  stage?: 'init' | 'pending' | 'download' | 'upload' | 'structure' | 'pages' | 'generate' | 'completed' | 'resuming' | 'paused' | 'sync_index' | 'sync_preparing' | 'sync_uploading' | 'sync_indexing';
  stage_progress?: number;
  stage_message?: string;
  created_time: string;
  updated_time: string;
  created_by?: number;
  creator_info?: {
    id: number;
    user_name: string;
    user_code: string;
  };
  wiki_info_id?: string;
  // JobManager相关字段
  in_manager?: boolean;
  manager_status?: {
    start_time?: string;
    last_heartbeat?: string;
    retry_count?: number;
    thread_alive?: boolean;
  };
  // 子仓库地址信息
  main_repo?: SubRepoInfo | null;
  sub_repos?: SubRepoInfo[];
}

// 阶段名称映射 - 从国际化配置获取
const getStageNames = (t: Messages): Record<string, string> => ({
  'init': t.components.JobsProgressModal.stages.init,
  'download': t.components.JobsProgressModal.stages.download,
  'upload': t.components.JobsProgressModal.stages.upload,
  'structure': t.components.JobsProgressModal.stages.structure,
  'pages': t.components.JobsProgressModal.stages.pages,
  'generate': t.components.JobsProgressModal.stages.generate, // 兼容旧版
  'completed': t.components.JobsProgressModal.stages.completed,
  'resuming': t.components.JobsProgressModal.stages.resuming,
  'paused': t.components.JobsProgressModal.stages.paused,
  'sync_index': '同步索引',
  'sync_preparing': '准备同步',
  'sync_uploading': '上传文件',
  'sync_indexing': '建立索引'
});

// 阶段图标组件
const StageIcon = ({ stage }: { stage?: string }) => {
  switch (stage) {
    case 'download':
      return <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" /></svg>;
    case 'upload':
      return <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" /></svg>;
    case 'generate':
      return <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" /></svg>;
    case 'completed':
      return <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>;
    case 'resuming':
      return <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" /></svg>;
    case 'paused':
      return <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" /></svg>;
    case 'sync_index':
    case 'sync_preparing':
    case 'sync_uploading':
    case 'sync_indexing':
      return <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" /></svg>;
    default:
      return <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>;
  }
};

const getJobTypeMeta = (jobType: number | string | null | undefined, t: Messages) => {
  const normalizedType = typeof jobType === 'string'
    ? Number.parseInt(jobType, 10)
    : jobType ?? 0;
  const safeType = Number.isNaN(normalizedType) ? 0 : normalizedType;
  const labels = {
    generation: t.components.JobsProgressModal.jobTypes?.generation ?? 'Generation Job',
    refresh: t.components.JobsProgressModal.jobTypes?.refresh ?? 'Refresh Job',
    sync: t.components.JobsProgressModal.jobTypes?.sync ?? 'Sync Job',
    unknown: t.components.JobsProgressModal.jobTypes?.unknown ?? 'Unknown Job Type',
  };

  switch (safeType) {
    case 0:
      return {
        label: labels.generation,
        className: 'bg-purple-100 text-purple-700 dark:bg-purple-500/20 dark:text-purple-300',
      };
    case 1:
      return {
        label: labels.refresh,
        className: 'bg-orange-100 text-orange-700 dark:bg-orange-500/20 dark:text-orange-300',
      };
    case 2:
      return {
        label: labels.sync,
        className: 'bg-teal-100 text-teal-700 dark:bg-teal-500/20 dark:text-teal-300',
      };
    default:
      return {
        label: labels.unknown,
        className: 'bg-gray-100 text-gray-700 dark:bg-gray-500/20 dark:text-gray-300',
      };
  }
};

const normalizeNumericValue = (value?: number | string | null) => {
  if (typeof value === 'number') {
    return Number.isFinite(value) ? value : 0;
  }
  if (typeof value === 'string') {
    const parsed = Number(value);
    return Number.isFinite(parsed) ? parsed : 0;
  }
  return 0;
};

interface JobRepoInfo {
  url?: string;
  branch?: string;
  owner?: string;
  name?: string;
  type?: string;
  is_main?: boolean;
}

const parseRepoIdentifiers = (url?: string): { owner?: string; name?: string } => {
  if (!url) {
    return {};
  }
  try {
    const parsedUrl = new URL(url);
    const segments = parsedUrl.pathname.split('/').filter(Boolean);
    if (segments.length >= 2) {
      const owner = segments[segments.length - 2];
      const name = segments[segments.length - 1].replace(/\.git$/, '');
      return { owner, name };
    }
    if (segments.length === 1) {
      return { name: segments[0].replace(/\.git$/, '') };
    }
  } catch (err) {
    console.warn('Failed to parse repo url:', err);
  }
  return {};
};

const normalizeRepoInfo = (
  repo: Partial<JobRepoInfo> | null | undefined,
  fallback: JobRepoInfo = {}
): JobRepoInfo => {
  const targetUrl = repo?.url ?? fallback.url;
  const parsed = parseRepoIdentifiers(targetUrl);
  return {
    url: targetUrl,
    branch: repo?.branch ?? fallback.branch,
    owner: repo?.owner ?? fallback.owner ?? parsed.owner,
    name: repo?.name ?? fallback.name ?? parsed.name,
    type: repo?.type ?? fallback.type,
    is_main: repo?.is_main ?? fallback.is_main ?? false,
  };
};

const buildMainRepoInfo = (job: WikiJob): JobRepoInfo => {
  const fallback: JobRepoInfo = {
    url: job.repo_url,
    branch: job.branch,
    owner: job.repo_owner,
    name: job.repo_name,
    is_main: true,
  };
  return normalizeRepoInfo(job.main_repo, fallback);
};

const buildSubRepoInfos = (job: WikiJob, mainRepo: JobRepoInfo): JobRepoInfo[] => {
  const rawSubs = Array.isArray(job.sub_repos) ? job.sub_repos : [];
  const normalized = rawSubs.map((sub) => normalizeRepoInfo(sub));
  const filtered = normalized.filter((sub) => {
    if (!sub.url) {
      return false;
    }
    if (
      sub.url === mainRepo.url &&
      (sub.branch ?? '') === (mainRepo.branch ?? '')
    ) {
      return false;
    }
    return true;
  });

  const deduped: JobRepoInfo[] = [];
  const seen = new Set<string>();
  filtered.forEach((sub) => {
    const key = `${sub.url ?? ''}|${sub.branch ?? ''}`;
    if (!seen.has(key)) {
      seen.add(key);
      deduped.push({ ...sub, is_main: false });
    }
  });
  return deduped;
};

const formatRepoLabel = (repo: JobRepoInfo): string => {
  if (repo.owner && repo.name) {
    return `${repo.owner}/${repo.name}`;
  }
  if (repo.name) {
    return repo.name;
  }
  if (repo.url) {
    const parsed = parseRepoIdentifiers(repo.url);
    if (parsed.owner && parsed.name) {
      return `${parsed.owner}/${parsed.name}`;
    }
    return repo.url;
  }
  return '-';
};

const formatRepoWithBranch = (repo: JobRepoInfo, branchLabel: string): string => {
  let base = formatRepoLabel(repo);
  if (base === '-' && repo.url) {
    base = repo.url;
  }
  if (!repo.branch) {
    return base;
  }
  return `${base} [${branchLabel}: ${repo.branch}]`;
};

// 任务详情组件
interface JobDetailsProps {
  job?: WikiJob;
  onRetry: (e: React.MouseEvent) => void;
  onCancel?: (e: React.MouseEvent) => void;
  onResume?: (e: React.MouseEvent) => void;
  onRestart?: (e: React.MouseEvent) => void;
  onDelete?: (e: React.MouseEvent) => void;
  retryingJob: string | null;
  cancellingJob?: string | null;
  resumingJob?: string | null;
  restartingJob?: string | null;
  deletingJob?: string | null;

}

const JobDetails: React.FC<JobDetailsProps> = ({ 
  job, 
  onRetry, 
  onCancel, 
  onResume, 
  onRestart,
  onDelete,
  retryingJob, 
  cancellingJob, 
  resumingJob,
  restartingJob,
  deletingJob
}) => {
  const { userInfo, userRoles } = useAuth();
  const { messages: t } = useLanguage();
  const stageNames = getStageNames(t);
  
  if (!job) return null;
  
  // 阶段顺序 - 用于任务详情的展示
  const stageOrder = ['init', 'download', 'upload', 'structure', 'pages', 'completed'];
  
  // 控制是否显示文件处理进度：到“AI生成结构（structure）”及之后不展示
  const stageIndexMap: Record<string, number> = {
    init: 0,
    download: 1,
    upload: 2,
    structure: 3,
    pages: 4,
    generate: 4,
    completed: 5,
    resuming: 2,
    paused: 2
  };
  const currentStageIndex = stageIndexMap[job.stage || 'init'] ?? 0;
  const totalFiles = normalizeNumericValue(job.total_files);
  const processedFiles = normalizeNumericValue(job.processed_files);
  const shouldShowFileProgress = totalFiles > 0 && currentStageIndex < stageIndexMap['structure'];
  const fileProgressPercent = totalFiles > 0 ? Math.floor((processedFiles / totalFiles) * 100) : null;
  const jobTypeMeta = getJobTypeMeta(job.job_type, t);
  
  const mainRepo = buildMainRepoInfo(job);
  const subRepos = buildSubRepoInfos(job, mainRepo);

  let displayOwner = mainRepo.owner || '';
  let displayRepo = mainRepo.name || '';
  if (!displayOwner || !displayRepo) {
    const parsed = parseRepoIdentifiers(mainRepo.url);
    displayOwner = displayOwner || parsed.owner || '';
    displayRepo = displayRepo || parsed.name || '';
  }
  if ((!displayOwner || !displayRepo) && job.repo_url) {
    const parsedFallback = parseRepoIdentifiers(job.repo_url);
    displayOwner = displayOwner || parsedFallback.owner || '';
    displayRepo = displayRepo || parsedFallback.name || '';
  }
  const displayBranch = mainRepo.branch || job.branch;
  let headerRepoLabel = formatRepoLabel({ ...mainRepo, owner: displayOwner, name: displayRepo });
  if (headerRepoLabel === '-' && job.repo_url) {
    headerRepoLabel = job.repo_url;
  }
  const headerTitle = displayBranch ? `${headerRepoLabel}/${displayBranch}` : headerRepoLabel;
  
  // 检查是否是任务创建者
  const isTaskCreator = userInfo && job.created_by && userInfo.id === job.created_by;
  
  // 检查用户角色权限
  const isSuperAdmin = userRoles?.some(role => role.role_code === 'super_admin') || false;
  const isRepoOwner = userRoles?.some(role => role.role_code === 'repo_owner') || false;
  
  // 权限检查：超级管理员或者（wiki管理员且是任务创建者）
  const hasPermission = isSuperAdmin || (isRepoOwner && isTaskCreator);
  
  // 失败时的通用提示：隐藏具体错误，仅提示阶段异常
  // 是否为失败态（仅失败/超时才展示错误信息）
  const isSensitiveFailure = (job.status === 'failed' || job.status === 'timeout') && !!job.error_message;
  // 运行中或排队中的任务（processing/resuming/syncing/pending/pending_resume/paused）不展示错误信息
  const isRunningOrQueued = ['processing', 'resuming', 'syncing', 'pending', 'pending_resume', 'paused'].includes(job.status);
  const failureStageLabel = stageNames[job.stage || ''] || t.components.JobsProgressModal.errors.currentStage;
  const failureNoticeText = `${failureStageLabel}${t.components.JobsProgressModal.errors.stageError}`;
  
  // 根据任务状态显示不同的操作按钮
  const renderActionButtons = () => {
    const buttons = [];
    
    // 重试按钮 - 对失败的任务显示
    if (job.status === 'failed' || job.status === 'timeout') {
      buttons.push(
        <button 
          key="retry"
          onClick={onRetry}
          disabled={retryingJob === job.id}
          title={retryingJob === job.id ? t.components.JobsProgressModal.actions.retrying : t.components.JobsProgressModal.actions.retry}
          className="w-8 h-8 flex items-center justify-center rounded-full bg-blue-500 text-white hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <FaRedo className={`${retryingJob === job.id ? 'animate-spin' : ''} w-4 h-4`} />
        </button>
      );
    }
    
    // 删除按钮 - 对失败的任务显示（仅有权限的用户可操作）
    if ((job.status === 'failed' || job.status === 'timeout') && onDelete && hasPermission) {
      buttons.push(
        <button 
          key="delete"
          onClick={onDelete}
          disabled={deletingJob === job.id}
          title={deletingJob === job.id ? t.components.JobsProgressModal.actions.deleting : t.components.JobsProgressModal.actions.deleteTask}
          className="w-8 h-8 flex items-center justify-center rounded-none bg-red-600 text-white hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className={`w-4 h-4 ${deletingJob === job.id ? 'animate-spin' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>
      );
    }
    
    // 继续按钮 - 对暂停的任务显示（仅有权限的用户可操作）
    if (job.status === 'paused' && onResume && hasPermission) {
      buttons.push(
        <button 
          key="resume"
          onClick={onResume}
          disabled={resumingJob === job.id}
          title={resumingJob === job.id ? t.components.JobsProgressModal.actions.resuming : t.components.JobsProgressModal.actions.resumeTask}
          className="w-8 h-8 flex items-center justify-center bg-green-500 text-white hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          style={{ clipPath: 'polygon(50% 0, 0 100%, 100% 100%)' }}
        >
          <FaPlay className={`${resumingJob === job.id ? 'animate-spin' : ''} w-4 h-4`} />
        </button>
      );
    }
    
    // 重新开始按钮 - 对取消的任务显示（仅有权限的用户可操作）
    if (job.status === 'cancelled' && onRestart && hasPermission) {
      buttons.push(
        <button 
          key="restart"
          onClick={onRestart}
          disabled={restartingJob === job.id}
          title={restartingJob === job.id ? t.components.JobsProgressModal.actions.restarting : t.components.JobsProgressModal.actions.restart}
          className="w-8 h-8 flex items-center justify-center rounded-full bg-purple-500 text-white hover:bg-purple-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <FaRedo className={`${restartingJob === job.id ? 'animate-spin' : ''} w-4 h-4`} />
        </button>
      );
    }
    
    // 删除按钮 - 对取消的任务显示（仅有权限的用户可操作）
    if (job.status === 'cancelled' && onDelete && hasPermission) {
      buttons.push(
        <button 
          key="delete"
          onClick={onDelete}
          disabled={deletingJob === job.id}
          title={deletingJob === job.id ? t.components.JobsProgressModal.actions.deleting : t.components.JobsProgressModal.actions.deleteTask}
          className="w-8 h-8 flex items-center justify-center rounded-none bg-red-600 text-white hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className={`w-4 h-4 ${deletingJob === job.id ? 'animate-spin' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>
      );
    }
    
    // 查看wiki按钮 - 对进行中的任务显示
    if (['pending', 'pending_resume', 'processing', 'resuming', 'syncing'].includes(job.status)) {
      buttons.push(
        <button 
          key="view"
          onClick={() => {
            // 从 repo_url 解析 owner 和 repo
            let owner = job.repo_owner || '';
            let repo = job.repo_name || '';
            
            if ((!owner || !repo) && job.repo_url) {
              try {
                const url = new URL(job.repo_url);
                const pathParts = url.pathname.split('/').filter(Boolean);
                
                if (pathParts.length >= 2) {
                  if (!owner) {
                    owner = pathParts[pathParts.length - 2];
                  }
                  if (!repo) {
                    repo = pathParts[pathParts.length - 1].replace(/\.git$/, '');
                  }
                }
              } catch (error) {
                console.error('Failed to parse repo URL:', error);
              }
            }
            
            if (owner && repo) {
              // 获取当前页面的查询参数
              const urlParams = new URLSearchParams(window.location.search);
              const type = urlParams.get('type') || 'whaleDevCloud';
              const language = urlParams.get('language') || 'zh';
              
              // 构造带参数的URL
              const url = `/${owner}/${repo}?type=${type}&language=${language}&branch=${encodeURIComponent(job.branch || 'main')}&wiki_id=${encodeURIComponent(job.id || '')}`;
              window.open(url, '_blank');
            }
          }}
          title={t.components.JobsProgressModal.actions.viewWiki}
          className="w-8 h-8 flex items-center justify-center rounded-full bg-blue-500 text-white hover:bg-blue-600 transition-colors"
        >
          <FaEye className="w-4 h-4" />
        </button>
      );
    }
    
    // 取消按钮 - 进行中/失败/等待恢复等状态显示（仅有权限的用户可操作）
    if (['pending', 'pending_resume', 'processing', 'resuming', 'syncing', 'failed', 'timeout'].includes(job.status) && onCancel && hasPermission) {
      buttons.push(
        <button 
          key="cancel"
          onClick={onCancel}
          disabled={cancellingJob === job.id}
          title={cancellingJob === job.id ? t.components.JobsProgressModal.actions.cancelling : t.components.JobsProgressModal.actions.cancelTask}
          className="w-8 h-8 flex items-center justify-center rounded-full bg-orange-500 text-white hover:bg-orange-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <FaStop className={`${cancellingJob === job.id ? 'animate-spin' : ''} w-4 h-4`} />
        </button>
      );
    }
    
    return buttons;
  };
  
  return (
    <div className="flex flex-col h-full overflow-y-auto p-6">
      <div className="flex justify-between items-start mb-6">
        <div>
          <h3 className="text-xl font-bold text-gray-800 dark:text-gray-100 flex items-center gap-3">
            <StatusIcon status={job.status} />
            <span>{headerTitle}</span>
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {t.components.JobsProgressModal.details.branch}: {displayBranch || job.branch} | 
            {t.components.JobsProgressModal.details.status}: {job.status === 'resuming' ? t.components.JobsProgressModal.status.resuming : 
                  job.status === 'pending_resume' ? t.components.JobsProgressModal.status.pending_resume :
                  job.status === 'processing' ? t.components.JobsProgressModal.status.processing :
                  job.status === 'paused' ? t.components.JobsProgressModal.status.paused :
                  job.status === 'completed' ? t.components.JobsProgressModal.status.completed :
                  job.status === 'cancelled' ? t.components.JobsProgressModal.status.cancelled :
                  job.status === 'failed' ? t.components.JobsProgressModal.status.failed : t.components.JobsProgressModal.status.pending}
            {job.creator_info && (
              <> | {t.components.JobsProgressModal.details.createdBy}: {job.creator_info.user_name}({job.creator_info.user_code})</>
            )}
          </p>
        </div>
        
        <div className="flex gap-2 flex-wrap items-center justify-end">
          {renderActionButtons()}
        </div>
      </div>
      
      {/* 总体进度 */}
      <div className="mb-6">
        <div className="flex justify-between text-sm text-gray-600 dark:text-gray-300 mb-2">
          <span className="font-medium">{t.components.JobsProgressModal.details.overallProgress}</span>
          <span className="font-medium">{job.progress}%</span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
          <div className={`h-2.5 rounded-full transition-all duration-500 ${
            job.status === 'failed' ? 'bg-red-500' : 
            job.status === 'cancelled' ? 'bg-orange-500' :
            job.status === 'paused' ? 'bg-yellow-500' :
            'bg-blue-500'
          }`} style={{ width: `${job.progress || 0}%` }}></div>
        </div>
      </div>
      
      {/* 阶段进度指示器 */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-3">{t.components.JobsProgressModal.details.taskStages}</h4>
        <StageProgressIndicator 
          stages={stageOrder} 
          currentStage={job.stage || 'init'}
          stageNames={stageNames}
        />
      </div>
      
      {/* 当前阶段详情 */}
      {job.stage && job.stage_progress !== undefined && job.stage !== 'completed' && (
        <div className="mb-6">
          <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-3">{t.components.JobsProgressModal.details.currentStage}: {stageNames[job.stage] || job.stage}</h4>
          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-300 mb-2">
            <span className="flex items-center gap-2">
              <StageIcon stage={job.stage} />
              <span>{isSensitiveFailure ? failureNoticeText : (job.stage_message || t.components.JobsProgressModal.details.processing)}</span>
            </span>
            <span>{job.stage_progress}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-500 ${
                job.stage === 'download' ? 'bg-blue-500' : 
                job.stage === 'upload' ? 'bg-purple-500' : 
                job.stage === 'structure' ? 'bg-amber-500' :
                job.stage === 'pages' ? 'bg-green-500' :
                job.stage === 'generate' ? 'bg-green-500' : 
                job.stage === 'resuming' ? 'bg-yellow-500' :
                job.stage === 'paused' ? 'bg-yellow-500' :
                'bg-gray-500'
              }`} 
              style={{ width: `${job.stage_progress}%` }}
            ></div>
          </div>
        </div>
      )}
      
      {/* 文件处理进度 */}
      {shouldShowFileProgress && (
        <div className="mb-6">
          <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">{t.components.JobsProgressModal.details.fileProgress}</h4>
          <p className="text-sm text-gray-600 dark:text-gray-300">
            {processedFiles} / {totalFiles} {t.components.JobsProgressModal.details.files}
            {fileProgressPercent !== null && ` (${fileProgressPercent}%)`}
          </p>
        </div>
      )}
      
      {/* 详细信息 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="bg-gray-50/80 dark:bg-gray-800/50 p-4 rounded-lg">
          <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-3">{t.components.JobsProgressModal.details.taskInfo}</h4>
          <div className="space-y-2 text-sm">
            <p><span className="font-medium">{t.components.JobsProgressModal.details.taskId}:</span> <span className="font-mono text-xs">{job.id}</span></p>
            <p><span className="font-medium">{t.components.JobsProgressModal.details.createdTime}:</span> {new Date(job.created_time).toLocaleString()}</p>
            <p><span className="font-medium">{t.components.JobsProgressModal.details.mainRepo}:</span> <span className="font-mono text-xs">{formatRepoWithBranch(mainRepo, t.components.JobsProgressModal.details.branch)}</span></p>
            {mainRepo.url && (
              <p><span className="font-medium">{t.components.JobsProgressModal.details.repoUrl}:</span> {mainRepo.url}</p>
            )}
            <p className="flex items-center gap-2"><span className="font-medium">{t.components.JobsProgressModal.details.jobType}:</span>
              <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${jobTypeMeta.className}`}>
                {jobTypeMeta.label}
              </span>
            </p>
            {/* 子仓库信息 */}
            {subRepos.length > 0 && (
              <div>
                <span className="font-medium">{t.components.JobsProgressModal.details.subRepos}:</span>
                <ul className="ml-4 list-disc">
                  {subRepos.map((sub, idx) => (
                    <li key={idx}>
                      <span className="font-mono text-xs">{formatRepoWithBranch(sub, t.components.JobsProgressModal.details.branch)}</span>
                      {sub.url && (
                        <span className="ml-2 text-gray-500 text-xs">{sub.url}</span>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            )}
            {job.manager_status && (
              <>
                {job.manager_status.start_time && (
                  <p><span className="font-medium">{t.components.JobsProgressModal.details.startTime}:</span> {new Date(job.manager_status.start_time).toLocaleString()}</p>
                )}
                {job.manager_status.retry_count !== undefined && (
                  <p><span className="font-medium">{t.components.JobsProgressModal.details.retryCount}:</span> {job.manager_status.retry_count}</p>
                )}
              </>
            )}
          </div>
        </div>
        
        <div className="bg-gray-50/80 dark:bg-gray-800/50 p-4 rounded-lg">
          <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-3">{t.components.JobsProgressModal.details.statusInfo}</h4>
          <div className="space-y-2 text-sm">
            {isSensitiveFailure ? (
              <p className="text-red-600 dark:text-red-400">
                <span className="font-medium">{t.components.JobsProgressModal.details.errorInfo}:</span> {failureNoticeText}
              </p>
            ) : (
              <>
                {job.message && <p><span className="font-medium">{t.components.JobsProgressModal.details.statusMessage}:</span> {job.message}</p>}
                {job.stage_message && <p><span className="font-medium">{t.components.JobsProgressModal.details.stageMessage}:</span> {job.stage_message}</p>}
                {!isRunningOrQueued && job.error_message && (
                  <p className="text-red-600 dark:text-red-400">
                    <span className="font-medium">{t.components.JobsProgressModal.details.errorInfo}:</span> {job.error_message}
                  </p>
                )}
              </>
            )}
            {job.manager_status && job.manager_status.thread_alive !== undefined && (
              <p><span className="font-medium">{t.components.JobsProgressModal.details.executionStatus}:</span> 
                <span className={`ml-1 ${job.manager_status.thread_alive ? 'text-green-600' : 'text-red-600'}`}>
                  {job.manager_status.thread_alive ? t.components.JobsProgressModal.details.running : t.components.JobsProgressModal.details.stopped}
                </span>
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// 添加标签组件
const TabButton = ({ active, onClick, children, count }: { active: boolean, onClick: () => void, children: React.ReactNode, count?: number }) => (
  <button
    onClick={onClick}
    className={`px-4 py-2 text-sm font-medium rounded-t-lg transition-colors relative
      ${active 
        ? 'bg-white dark:bg-gray-800 border-b-2 border-blue-500 text-blue-600 dark:text-blue-400' 
        : 'bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700'}`}
  >
    {children}
    {count !== undefined && count > 0 && (
      <span className={`absolute -top-1 -right-1 text-xs px-1.5 py-0.5 rounded-full ${active ? 'bg-blue-500 text-white' : 'bg-gray-500 text-white'}`}>
        {count}
      </span>
    )}
  </button>
);

// 阶段进度指示器组件
const StageProgressIndicator = ({ stages, currentStage, stageNames }: { 
  stages: string[], 
  currentStage: string,
  stageNames: Record<string, string>
}) => {
  // 确定每个阶段的状态：已完成、进行中、未开始
  const getStageStatus = (stage: string) => {
    const stageOrder = {
      'init': 0,
      'download': 1,
      'upload': 2,
      'structure': 3,
      'pages': 4,
      'completed': 5
    };
    
    const currentIdx = stageOrder[currentStage as keyof typeof stageOrder] || 0;
    const stageIdx = stageOrder[stage as keyof typeof stageOrder] || 0;
    
    if (stageIdx < currentIdx) return 'completed';
    if (stageIdx === currentIdx) return 'current';
    return 'pending';
  };

  return (
    <div className="flex items-center w-full justify-between mt-3 mb-2">
      {stages.map((stage, index) => (
        <React.Fragment key={stage}>
          {/* 连接线 */}
          {index > 0 && (
            <div 
              className={`flex-grow h-0.5 mx-1 ${
                getStageStatus(stage) === 'completed' 
                  ? 'bg-green-500' 
                  : getStageStatus(stages[index-1]) === 'completed'
                    ? 'bg-blue-500'
                    : 'bg-gray-300 dark:bg-gray-700'
              }`}
            ></div>
          )}
          
          {/* 阶段点 */}
          <div className="flex flex-col items-center relative">
            <div className={`
              w-4 h-4 rounded-full flex items-center justify-center border-2
              ${getStageStatus(stage) === 'completed' 
                ? 'bg-green-500 border-green-500 text-white' 
                : getStageStatus(stage) === 'current'
                  ? 'bg-blue-500 border-blue-500 text-white'
                  : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-700'
              }
            `}>
              {getStageStatus(stage) === 'completed' && (
                <FaCheck className="w-2 h-2" />
              )}
            </div>
            <span className={`text-xs mt-1 whitespace-nowrap max-w-15 overflow-hidden text-ellipsis ${
              getStageStatus(stage) === 'completed' 
                ? 'text-green-600 dark:text-green-400' 
                : getStageStatus(stage) === 'current'
                  ? 'text-blue-600 dark:text-blue-400 font-medium'
                  : 'text-gray-500 dark:text-gray-400'
            }`}
            title={stageNames[stage] || stage}
            >
              {stageNames[stage] || stage}
            </span>
          </div>
        </React.Fragment>
      ))}
    </div>
  );
};

interface JobsProgressModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const JobsProgressModal: React.FC<JobsProgressModalProps> = ({ isOpen, onClose }) => {
  const { messages:t } = useLanguage();
  const stageNames = getStageNames(t);
  const [jobs, setJobs] = useState<WikiJob[]>([]);
  const [loading, setLoading] = useState(false);
  const [expandedJobId, setExpandedJobId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'processing' | 'queued' | 'failed' | 'cancelled'>('processing'); // 将active拆分为processing与queued
  const [retryingJob, setRetryingJob] = useState<string | null>(null);
  const [cancellingJob, setCancellingJob] = useState<string | null>(null);
  const [resumingJob, setResumingJob] = useState<string | null>(null);
  const [restartingJob, setRestartingJob] = useState<string | null>(null);
  const [deletingJob, setDeletingJob] = useState<string | null>(null);
  
  // 删除确认框状态管理
  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);
  const [jobToDelete, setJobToDelete] = useState<string | null>(null);
  const { addToast } = useToast();

  // 获取所有类型的任务
  const fetchJobs = useCallback(async () => {
    setLoading(true);
    try {
      // 获取进行中任务
      const activeResponse = await authFetch('/api/wiki/jobs/active');
      const activeJobs = activeResponse?.ok ? await activeResponse.json() : [];
      
      // 获取排队中任务
      const pendingResponse = await authFetch('/api/wiki/jobs/pending');
      const pendingJobs = pendingResponse?.ok ? await pendingResponse.json() : [];
      
      // 获取失败任务
      const failedResponse = await authFetch('/api/wiki/jobs/failed');
      const failedJobs = failedResponse?.ok ? await failedResponse.json() : [];
      
      // 获取取消的任务
      const cancelledResponse = await authFetch('/api/wiki/jobs/cancelled');
      const cancelledJobs = cancelledResponse?.ok ? await cancelledResponse.json() : [];
      
      // 合并所有任务
      setJobs([...activeJobs, ...pendingJobs, ...failedJobs, ...cancelledJobs]);
    } catch (error) {
      console.error('Failed to fetch jobs:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // 辅助函数：智能选择job
  const selectFirstJobInTab = useCallback((tab: 'processing' | 'queued' | 'failed' | 'cancelled') => {
    const processingJobs = jobs.filter(job => ['processing', 'resuming', 'syncing'].includes(job.status));
    const queuedJobs = jobs.filter(job => ['pending', 'pending_resume', 'paused'].includes(job.status));
    const failedJobs = jobs.filter(job => ['failed', 'timeout'].includes(job.status));
    const cancelledJobs = jobs.filter(job => job.status === 'cancelled');
    
    let targetJobs: WikiJob[] = [];
    switch (tab) {
      case 'processing':
        targetJobs = processingJobs;
        break;
      case 'queued':
        targetJobs = queuedJobs;
        break;
      case 'failed':
        targetJobs = failedJobs;
        break;
      case 'cancelled':
        targetJobs = cancelledJobs;
        break;
    }
    
    if (targetJobs.length > 0) {
      // 确保选择的是有效的job ID
      const validJobId = targetJobs[0].id;
      if (validJobId !== expandedJobId) {
        setExpandedJobId(validJobId);
      } else {
        // 如果ID相同，强制重新渲染右侧内容
        setExpandedJobId(null);
        setTimeout(() => setExpandedJobId(validJobId), 10);
      }
    } else {
      setExpandedJobId(null);
    }
  }, [jobs, expandedJobId]);

  // 辅助函数：智能切换tab并选择job
  const switchTabAndSelectJob = useCallback((targetTab: 'processing' | 'queued' | 'failed' | 'cancelled') => {
    setActiveTab(targetTab);
    // 延迟选择job，确保tab切换完成
    setTimeout(() => {
      selectFirstJobInTab(targetTab);
    }, 50);
  }, [selectFirstJobInTab]);

  // 重试任务函数
  const handleRetryJob = async (e: React.MouseEvent, jobId: string) => {
    e.stopPropagation();
    
    setRetryingJob(jobId);
    try {
      const response = await authFetch(`/api/wiki/jobs/${jobId}/retry`, {
        method: 'POST'
      });
      
      if (response?.ok) {
        const result = await response.json();
        console.log('Job retry result:', result);
        
        // 先更新jobs数组
        await fetchJobs();
        
        // 等待jobs更新完成后再切换tab和选择job
        setTimeout(() => {
          // 重试后，任务状态会变为processing，应该切换到processing tab
          switchTabAndSelectJob('processing');
        }, 200);
      } else {
        console.error('Failed to retry job:', await response?.text());
      }
    } catch (error) {
      console.error('Error retrying job:', error);
    } finally {
      setRetryingJob(null);
    }
  };

  // 取消任务函数
  const handleCancelJob = async (e: React.MouseEvent, jobId: string) => {
    e.stopPropagation();
    
    setCancellingJob(jobId);
    try {
      const response = await authFetch(`/api/wiki/jobs/${jobId}/cancel`, {
        method: 'POST'
      });
      
      if (response?.ok) {
        const result = await response.json();
        console.log('Job cancel result:', result);
        
        // 先更新jobs数组
        await fetchJobs();
        // 取消后直接切换到“已取消”标签，避免使用可能过期的闭包状态
        switchTabAndSelectJob('cancelled');
      } else {
        console.error('Failed to cancel job:', await response?.text());
      }
    } catch (error) {
      console.error('Error cancelling job:', error);
    } finally {
      setCancellingJob(null);
    }
  };

  // 继续任务函数
  const handleResumeJob = async (e: React.MouseEvent, jobId: string) => {
    e.stopPropagation();
    
    setResumingJob(jobId);
    try {
      const response = await authFetch(`/api/wiki/jobs/${jobId}/resume`, {
        method: 'POST'
      });
      
      if (response?.ok) {
        const result = await response.json();
        console.log('Job resume result:', result);
        
        // 先更新jobs数组
        await fetchJobs();
        
        // 等待jobs更新完成后再切换tab和选择job
        setTimeout(() => {
          // 继续后，任务状态会变为processing，应该切换到processing tab
          switchTabAndSelectJob('processing');
        }, 200);
      } else {
        console.error('Failed to resume job:', await response?.text());
      }
    } catch (error) {
      console.error('Error resuming job:', error);
    } finally {
      setResumingJob(null);
    }
  };

  // 重新开始任务函数
  const handleRestartJob = async (e: React.MouseEvent, jobId: string) => {
    e.stopPropagation();
    
    setRestartingJob(jobId);
    try {
      const response = await authFetch(`/api/wiki/jobs/${jobId}/restart`, {
        method: 'POST'
      });
      
      if (response?.ok) {
        const result = await response.json();
        console.log('Job restart result:', result);
        
        // 先更新jobs数组
        await fetchJobs();
        
        // 等待jobs更新完成后再切换tab和选择job
        setTimeout(() => {
          // 重新开始后，任务状态会变为processing，应该切换到processing tab
          switchTabAndSelectJob('processing');
        }, 200);
      } else {
        console.error('Failed to restart job:', await response?.text());
      }
    } catch (error) {
      console.error('Error restarting job:', error);
    } finally {
      setRestartingJob(null);
    }
  };

  // 删除任务函数
  const handleDeleteJob = async (e: React.MouseEvent, jobId: string) => {
    e.stopPropagation();
    
    // 设置要删除的任务ID并显示确认框
    setJobToDelete(jobId);
    setShowDeleteConfirmModal(true);
  };

  // 确认删除任务
  const confirmDeleteJob = async () => {
    if (!jobToDelete) return;
    
    setDeletingJob(jobToDelete);
    try {
      const response = await authFetch(`/api/wiki/jobs/${jobToDelete}`, {
        method: 'DELETE'
      });
      
      if (response?.ok) {
        const result = await response.json();
        console.log('Job delete result:', result);
        
        // 先更新jobs数组
        await fetchJobs();
        
        // 等待jobs更新完成后再处理tab和job选择
        setTimeout(() => {
          const processingJobs = jobs.filter(job => ['processing', 'resuming', 'syncing'].includes(job.status));
          const queuedJobs = jobs.filter(job => ['pending', 'pending_resume', 'paused'].includes(job.status));
          const failedJobs = jobs.filter(job => ['failed', 'timeout'].includes(job.status));
          const cancelledJobs = jobs.filter(job => job.status === 'cancelled');
          
          // 如果当前tab没有任务了，切换到有任务的tab
          if (activeTab === 'cancelled' && cancelledJobs.length === 0) {
            if (processingJobs.length > 0) {
              switchTabAndSelectJob('processing');
            } else if (queuedJobs.length > 0) {
              switchTabAndSelectJob('queued');
            } else if (failedJobs.length > 0) {
              switchTabAndSelectJob('failed');
            } else {
              setExpandedJobId(null);
            }
          } else if (activeTab === 'processing' && processingJobs.length === 0) {
            if (queuedJobs.length > 0) {
              switchTabAndSelectJob('queued');
            } else if (failedJobs.length > 0) {
              switchTabAndSelectJob('failed');
            } else if (cancelledJobs.length > 0) {
              switchTabAndSelectJob('cancelled');
            } else {
              setExpandedJobId(null);
            }
          } else if (activeTab === 'queued' && queuedJobs.length === 0) {
            if (processingJobs.length > 0) {
              switchTabAndSelectJob('processing');
            } else if (failedJobs.length > 0) {
              switchTabAndSelectJob('failed');
            } else if (cancelledJobs.length > 0) {
              switchTabAndSelectJob('cancelled');
            } else {
              setExpandedJobId(null);
            }
          } else if (activeTab === 'failed' && failedJobs.length === 0) {
            if (processingJobs.length > 0) {
              switchTabAndSelectJob('processing');
            } else if (queuedJobs.length > 0) {
              switchTabAndSelectJob('queued');
            } else if (cancelledJobs.length > 0) {
              switchTabAndSelectJob('cancelled');
            } else {
              setExpandedJobId(null);
            }
          } else {
            // 如果当前tab还有任务，选择第一个
            selectFirstJobInTab(activeTab);
          }
        }, 200); // 增加延迟确保jobs数组完全更新
      } else {
        const errorData = await response?.text();
        console.error('Failed to delete job:', errorData);
        addToast({
          type: 'error',
          title: `Failed to delete job: ${errorData}`,
          message: ''
        });
      }
    } catch (error) {
      console.error('Error deleting job:', error);
      addToast({
        type: 'error',
        title: `Failed to delete job: ${error}`,
        message: ''
      });
    } finally {
      setDeletingJob(null);
      // 无论成功还是失败，都要关闭确认框
      setShowDeleteConfirmModal(false);
      setJobToDelete(null);
    }
  };

  useEffect(() => {
    if (!isOpen) return;

    fetchJobs();
    const interval = setInterval(fetchJobs, 5000); // 每5秒轮询一次

    return () => clearInterval(interval);
  }, [isOpen, fetchJobs]);

  // 监听jobs变化，确保在操作完成后能够正确选择job
  useEffect(() => {
    if (jobs.length > 0 && !expandedJobId) {
      // 如果没有选中的job，根据当前tab选择第一个
      selectFirstJobInTab(activeTab);
    }
  }, [jobs, expandedJobId, activeTab, selectFirstJobInTab]);

  // 点击任务卡片切换详情展示
  // const handleJobClick = (job: WikiJob) => {
  //   // 从 repo_url 解析 owner 和 repo
  //   let owner = job.repo_owner || '';
  //   let repo = job.repo_name || '';
    
  //   if ((!owner || !repo) && job.repo_url) {
  //     try {
  //       const url = new URL(job.repo_url);
  //       const pathParts = url.pathname.split('/').filter(Boolean);
        
  //       if (pathParts.length >= 2) {
  //         if (!owner) {
  //           owner = pathParts[pathParts.length - 2];
  //         }
  //         if (!repo) {
  //           repo = pathParts[pathParts.length - 1].replace(/\.git$/, '');
  //         }
  //       }
  //     } catch (error) {
  //       console.error('Failed to parse repo URL:', error);
  //       return;
  //     }
  //   }
    
  //   if (owner && repo) {
  //     onClose();
  //     router.push(`/${owner}/${repo}`);
  //   }
  // };

  // 过滤任务列表
  const processingJobs = jobs.filter(job => ['processing', 'resuming', 'syncing'].includes(job.status));
  const queuedJobs = jobs.filter(job => ['pending', 'pending_resume', 'paused'].includes(job.status));
  const failedJobs = jobs.filter(job => ['failed', 'timeout'].includes(job.status));
  const cancelledJobs = jobs.filter(job => job.status === 'cancelled');
  
  const displayedJobs = activeTab === 'processing' ? processingJobs :
                       activeTab === 'queued' ? queuedJobs :
                       activeTab === 'failed' ? failedJobs : cancelledJobs;

  if (!isOpen) return null;

  return (
    <>
      <div 
        className="fixed inset-0 bg-black/30 dark:bg-black/60 z-40 transition-opacity duration-300 ease-in-out backdrop-blur-sm" 
        onClick={onClose}
        style={{ opacity: isOpen ? 1 : 0 }}
      ></div>
      <div 
        className={`fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[80%] max-w-5xl h-[70%] bg-white/90 dark:bg-gray-900/90 text-gray-800 dark:text-white shadow-2xl z-50 flex flex-col backdrop-blur-md border border-gray-200/50 dark:border-gray-700/50 rounded-2xl transition-all duration-300 ease-in-out ${
          isOpen ? 'opacity-100' : 'opacity-0 translate-y-4'
        }`}
      >
        {/* Header */}
        <div className="flex justify-between items-center p-5 border-b border-gray-200/50 dark:border-gray-700/50 flex-shrink-0">
          <h2 className="text-xl font-bold text-gray-800 dark:text-gray-100">{t.components.JobsProgressModal.title}</h2>
          <div className="flex items-center gap-4">
            <button 
              onClick={async () => {
                // 先更新jobs数组
                await fetchJobs();
                // 等待jobs更新完成后再选择job
                setTimeout(() => {
                  // 刷新后，确保当前tab有选中的job
                  selectFirstJobInTab(activeTab);
                }, 200);
              }} 
              disabled={loading}
              className="text-gray-500 dark:text-gray-400 hover:text-gray-800 dark:hover:text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <RefreshIcon className={loading ? 'animate-spin' : ''} />
            </button>
            <button onClick={onClose} className="text-gray-500 dark:text-gray-400 hover:text-gray-800 dark:hover:text-white transition-colors">
              <CloseIcon />
            </button>
          </div>
        </div>

        {/* Main Content - Split into two columns */}
        <div className="flex flex-1 overflow-hidden">
          {/* Left Column - Tabs and Job List */}
          <div className="max-w-sm flex flex-col border-r border-gray-200/50 dark:border-gray-700/50">
            {/* Tabs */}
            <div className="flex border-b border-gray-200 dark:border-gray-700 px-5">
              <TabButton 
                active={activeTab === 'processing'} 
                onClick={() => {
                  if (activeTab !== 'processing') {
                    switchTabAndSelectJob('processing');
                  }
                }}
                count={processingJobs.length}
              >
                {t.components.JobsProgressModal.tabs.processing}
              </TabButton>
              <TabButton 
                active={activeTab === 'queued'} 
                onClick={() => {
                  if (activeTab !== 'queued') {
                    switchTabAndSelectJob('queued');
                  }
                }}
                count={queuedJobs.length}
              >
                {t.components.JobsProgressModal.tabs.queued}
              </TabButton>
              <TabButton 
                active={activeTab === 'failed'} 
                onClick={() => {
                  if (activeTab !== 'failed') {
                    switchTabAndSelectJob('failed');
                  }
                }}
                count={failedJobs.length}
              >
                {t.components.JobsProgressModal.tabs.failed}
              </TabButton>
              <TabButton 
                active={activeTab === 'cancelled'} 
                onClick={() => {
                  if (activeTab !== 'cancelled') {
                    switchTabAndSelectJob('cancelled');
                  }
                }}
                count={cancelledJobs.length}
              >
                {t.components.JobsProgressModal.tabs.cancelled}
              </TabButton>
            </div>

            {/* Jobs List */}
            <div className="flex-grow overflow-y-auto p-3 space-y-3">
              {/* 加载状态占位符 */}
              {loading && jobs.length === 0 && (
                <div className="animate-pulse space-y-3">
                  {[1, 2, 3].map(i => (
                    <div key={i} className="bg-gray-100 dark:bg-gray-800/50 p-3 rounded-lg">
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 bg-gray-300 dark:bg-gray-700 rounded-full"></div>
                        <div className="flex-1">
                          <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-3/4 mb-1"></div>
                          <div className="h-2 bg-gray-300 dark:bg-gray-700 rounded w-1/2"></div>
                        </div>
                      </div>
                      <div className="w-full h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full mt-2"></div>
                    </div>
                  ))}
                </div>
              )}

              {/* 空状态提示 */}
              {!loading && displayedJobs.length === 0 && (
                <div className="flex flex-col items-center justify-center h-full text-center text-gray-500 py-8">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" /></svg>
                  {activeTab === 'processing' ? (
                    <>
                      <p className="text-base">{t.components.JobsProgressModal.emptyStates.noProcessingTasks}</p>
                      <p className="text-xs">{t.components.JobsProgressModal.emptyStates.noProcessingTasksDesc}</p>
                    </>
                  ) : activeTab === 'queued' ? (
                    <>
                      <p className="text-base">{t.components.JobsProgressModal.emptyStates.noQueuedTasks}</p>
                      <p className="text-xs">{t.components.JobsProgressModal.emptyStates.noQueuedTasksDesc}</p>
                    </>
                  ) : activeTab === 'failed' ? (
                    <>
                      <p className="text-base">{t.components.JobsProgressModal.emptyStates.noFailedTasks}</p>
                      <p className="text-xs">{t.components.JobsProgressModal.emptyStates.noFailedTasksDesc}</p>
                    </>
                  ) : (
                    <>
                      <p className="text-base">{t.components.JobsProgressModal.emptyStates.noCancelledTasks}</p>
                      <p className="text-xs">{t.components.JobsProgressModal.emptyStates.noCancelledTasksDesc}</p>
                    </>
                  )}
                </div>
              )}

              {/* 简化的任务列表 - 只显示基本信息 */}
              {displayedJobs.map(job => {
                const listMainRepo = buildMainRepoInfo(job);
                let listRepoLabel = formatRepoLabel(listMainRepo);
                if (listRepoLabel === '-' && job.repo_url) {
                  listRepoLabel = job.repo_url;
                }
                const listBranch = listMainRepo.branch || job.branch;
                const listTitle = listBranch ? `${listRepoLabel}/${listBranch}` : listRepoLabel;
                
                const listSensitiveFailure = (job.status === 'failed' || job.status === 'timeout') && !!job.error_message;
                const listFailureStageLabel = stageNames[job.stage || ''] || t.components.JobsProgressModal.errors.currentStage;
                const listFailureNoticeText = `${listFailureStageLabel}${t.components.JobsProgressModal.errors.stageError}`;
                const jobTypeMeta = getJobTypeMeta(job.job_type, t);

                return (
                  <div 
                    key={job.id} 
                    onClick={() => setExpandedJobId(job.id)}
                    className={`bg-gray-50/80 dark:bg-gray-800/50 p-3 rounded-lg cursor-pointer transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800 border border-gray-200/50 dark:border-gray-700/50 ${
                      job.status === 'failed' ? 'border-l-4 border-l-red-500' :
                      job.status === 'cancelled' ? 'border-l-4 border-l-orange-500' :
                      job.status === 'paused' ? 'border-l-4 border-l-yellow-500' : ''
                    } ${expandedJobId === job.id ? 'bg-blue-50/80 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800' : ''}`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3 min-w-0">
                        <StatusIcon status={job.status} />
                        <div className="flex-1 min-w-0">
                          <p className="font-semibold text-gray-800 dark:text-gray-200 truncate" title={listTitle}>
                            {listTitle}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {listSensitiveFailure ? listFailureNoticeText : (job.stage_message || job.message || stageNames[job.stage || ''] || t.components.JobsProgressModal.details.processing)}
                          </p>
                          <div className="mt-1 flex items-center gap-2">
                            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium ${jobTypeMeta.className}`}>
                              {jobTypeMeta.label}
                            </span>
                          </div>
                        </div>
                      </div>
                      <span className={`text-xs font-medium px-2 py-0.5 rounded-full whitespace-nowrap ml-2
                        ${job.status === 'completed' ? 'bg-green-100 text-green-700 dark:bg-green-500/20 dark:text-green-400' : ''}
                        ${job.status === 'processing' || job.status === 'resuming' ? 'bg-blue-100 text-blue-700 dark:bg-blue-500/20 dark:text-blue-400' : ''}
                        ${job.status === 'failed' ? 'bg-red-100 text-red-700 dark:bg-red-500/20 dark:text-red-400' : ''}
                        ${job.status === 'cancelled' ? 'bg-orange-100 text-orange-700 dark:bg-orange-500/20 dark:text-orange-400' : ''}
                        ${job.status === 'paused' ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-500/20 dark:text-yellow-400' : ''}
                        ${job.status === 'pending' || job.status === 'pending_resume' ? 'bg-gray-100 text-gray-700 dark:bg-gray-500/20 dark:text-gray-400' : ''}`}
                      >
                        {job.progress}%
                      </span>
                    </div>
                    
                    {/* 简化的进度条 */}
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1 mt-2">
                      <div className={`h-1 rounded-full transition-all duration-500 ${
                        job.status === 'failed' ? 'bg-red-500' : 
                        job.status === 'cancelled' ? 'bg-orange-500' :
                        job.status === 'paused' ? 'bg-yellow-500' :
                        'bg-blue-500'
                      }`} style={{ width: `${job.progress || 0}%` }}></div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
          
          {/* Right Column - Selected Job Details */}
          <div className="w-2/3 flex flex-col overflow-hidden">
            {expandedJobId ? (
              <JobDetails 
                job={jobs.find(j => j.id === expandedJobId)} 
                onRetry={(e) => handleRetryJob(e, expandedJobId)}
                onCancel={(e) => handleCancelJob(e, expandedJobId)}
                onResume={(e) => handleResumeJob(e, expandedJobId)}
                onRestart={(e) => handleRestartJob(e, expandedJobId)}
                onDelete={(e) => handleDeleteJob(e, expandedJobId)}
                retryingJob={retryingJob}
                cancellingJob={cancellingJob}
                resumingJob={resumingJob}
                restartingJob={restartingJob}
                deletingJob={deletingJob}
              />
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-center text-gray-500">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mb-4 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                <p className="text-lg">{t.components.JobsProgressModal.emptyStates.selectTask}</p>
                <p className="text-sm max-w-md mt-2">{t.components.JobsProgressModal.emptyStates.selectTaskDesc}</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 删除任务确认框 */}
      <DeleteConfirmModal
        isOpen={showDeleteConfirmModal}
        onClose={() => {
          setShowDeleteConfirmModal(false);
          setJobToDelete(null);
        }}
        onConfirm={confirmDeleteJob}
        title={t.components.JobsProgressModal.deleteConfirm.title}
        message={t.components.JobsProgressModal.deleteConfirm.message}
      />
    </>
  );
};
