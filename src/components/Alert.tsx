import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';

interface AlertProps {
  isOpen: boolean;
  title?: string;
  message?: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void;
}

const Alert: React.FC<AlertProps> = (props: AlertProps) => {
  const { messages: t } = useLanguage()
  const {
    isOpen,
    title = t.components.alert.title,
    message = '',
    confirmText = t.components.alert.confirm,
    cancelText = t.components.alert.cancel,
    onConfirm,
    onCancel,
  } = props

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[60] flex items-center justify-center bg-black/60 backdrop-blur-sm">
      <div className="relative w-full max-w-md bg-[var(--card-bg)] rounded-xl shadow-2xl border border-[var(--border-color)]">
        <div className="p-5 border-b border-[var(--border-color)]">
          <h3 className="text-lg font-semibold text-[var(--foreground)]">{title}</h3>
        </div>
        <div className="p-5 text-[var(--muted)]">
          {message && <p className="leading-relaxed text-[var(--foreground)]/80">{message}</p>}
        </div>
        <div className="flex items-center justify-end gap-3 p-4 border-t border-[var(--border-color)]">
          <button
            onClick={onCancel}
            className="btn-secondary"
          >
            {cancelText}
          </button>
          <button
            onClick={onConfirm}
            className="btn-primary"
          >
            {confirmText}
          </button>
        </div>
      </div>
    </div>
  );
};

export default Alert; 