import { useLanguage } from "@/contexts/LanguageContext";
import React from "react";
import CustomRadio from "../CustomRadio";
import Select from "react-select";
import { useTheme } from "next-themes";

interface WikiConfigStepProps {
  selectedLanguage: string;
  setSelectedLanguage: (value: React.SetStateAction<string>) => void;
  comprehensive: boolean;
  setComprehensive: (value: React.SetStateAction<boolean>) => void;
}

const WikiConfigStep = (props: WikiConfigStepProps) => {
  const {
    selectedLanguage,
    setSelectedLanguage,
    comprehensive,
    setComprehensive,
  } = props;

  const { theme } = useTheme();

  const { supportedLanguages, messages } = useLanguage();

  return (
    <div className="w-full flex flex-col gap-y-4">
      {/* Language selection */}
      <div>
        <label
          htmlFor="language-select"
          className="block text-sm font-medium text-black mb-2 dark:text-white"
        >
          {messages.form.wikiLanguage}
        </label>
        <Select
          classNamePrefix={`${theme === 'dark' ? 'dark-react-select' : 'react-select'}`}
          value={{ value: selectedLanguage, label: supportedLanguages[selectedLanguage] }}
          options={Object.entries(supportedLanguages).map(([key, value]) => {
            return { value: key, label: value };
          })}
          onChange={(newValue) => setSelectedLanguage(newValue?.value ?? "")}
        />
      </div>

      {/* Wiki Type Selector */}
      <div>
        <label className="block text-sm font-medium text-black mb-2 dark:text-white">
          {messages.form.wikiType}
        </label>
        <div className="flex gap-3">
          <CustomRadio
            selected={comprehensive}
            onClick={() => setComprehensive(true)}
          >
            <div className="flex items-center">
              <div className="text-left">
                <div className="font-medium text-sm">{messages.form.comprehensive}</div>
                <div className="text-xs opacity-80">
                  {messages.form.comprehensiveDescription}
                </div>
              </div>
            </div>
          </CustomRadio>

          <CustomRadio
            selected={!comprehensive}
            onClick={() => setComprehensive(false)}
          >
            <div className="flex items-center">
              <div className="text-left">
                <div className="font-medium text-sm">{messages.form.concise}</div>
                <div className="text-xs opacity-80">
                  {messages.form.conciseDescription}
                </div>
              </div>
            </div>
          </CustomRadio>
        </div>
      </div>
    </div>
  );
};

export default WikiConfigStep;
