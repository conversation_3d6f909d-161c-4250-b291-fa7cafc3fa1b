import { useLanguage } from "@/contexts/LanguageContext";
import { ModelConfig, Provider } from "@/types/modelConfig";
import { authFetch } from "@/utils/authFetch";
import { fetchModelConfig } from "@/utils/modelConfigCache";
import { useTheme } from "next-themes";
import React, { useEffect, useMemo, useState } from "react";
import Select, { SingleValue } from "react-select";

interface SelectOption {
  label: string | null;
  value: string | null;
}

interface ModelConfigStepProps {
  setModel: (value: React.SetStateAction<string>) => void;
  provider: string;
  setProvider: (value: React.SetStateAction<string>) => void;
  selectedTopicId: string;
  setSelectedTopicId: (value: React.SetStateAction<string>) => void;
  selectedTopicIdCode: string;
  setSelectedTopicIdCode: (value: React.SetStateAction<string>) => void;
  mainRepo: string;
  mainRepoBranch: string;
}

const ModelConfigStep = (props: ModelConfigStepProps) => {
  const {
    setModel,
    provider,
    setProvider,
    selectedTopicId,
    setSelectedTopicId,
    selectedTopicIdCode,
    setSelectedTopicIdCode,
    mainRepo,
    mainRepoBranch,
  } = props;

  const { theme } = useTheme();
  const { messages: t } = useLanguage();
  const [modelConfig, setModelConfig] = useState<ModelConfig | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [showDropdown, setShowDropdown] = useState(false);
  const [isLoadingTopics, setIsLoadingTopics] = useState(false);
  const [availableDocTopics, setAvailableDocTopics] = useState<
    Array<{ id: number; name: string; state: string }>
  >([]);
  const [availableTopics, setAvailableTopics] = useState<
    Array<{ id: number; name: string; state: string }>
  >([]);
  const [searchDocchainCode, setSearchDocchainCode] = useState("");
  const [showDropdownCode, setShowDropdownCode] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<SelectOption | null>(
    null
  );
  const [selectedModel, setSelectedModel] = useState<SelectOption | null>(null);
  const { messages } = useLanguage();

  useEffect(() => {
    const loadModelConfig = async () => {
      try {
        const data: ModelConfig = await fetchModelConfig();
        setModelConfig(data);

        // Initialize provider and model with defaults from API if not already set
        if (!provider && data.defaultProvider) {
          setProvider(data.defaultProvider);

          // Find the default provider and set its default model
          const selectedProvider = data.providers.find(
            (p: Provider) => p.id === data.defaultProvider
          );

          setSelectedProvider({
            value: selectedProvider?.id ?? null,
            label: selectedProvider?.name ?? null,
          });

          if (selectedProvider && selectedProvider.models.length > 0) {
            setModel(selectedProvider.models[0].id);
            setSelectedModel({
              value: selectedProvider.models[0].id,
              label: selectedProvider.models[0].name,
            });
          }
        }
      } catch (err) {
        console.error("Failed to fetch model configurations:", err);
      }
    };

    loadModelConfig();
  }, [provider, setModel, setProvider]);

  useEffect(() => {
    const loadTopics = async () => {
      setIsLoadingTopics(true);
      try {
        const response = await authFetch("/api/docchain/topics?type=lab");
        if (response) {
          if (response.ok) {
            const data = await response.json();
            if (data.success) {
              setAvailableDocTopics(data.data || []);
            }
          } else {
            const data = await response.json();
            console.error(
              `Failed to load DocChain topics: ${data.error}, status: ${response.status}`
            );
            // throw new Error(`Failed to load DocChain topics: ${data.error}, status: ${response.status}`)
          }
        }
      } catch (error) {
        console.error("Failed to load DocChain topics:", error);
      }
      setIsLoadingTopics(false);
    };

    loadTopics();
  }, []);

  // 加载 本地docchain的Topics
  useEffect(() => {
    const loadTopics = async () => {
      setIsLoadingTopics(true);
      try {
        const response = await authFetch("/api/docchain/topics");
        if (response && response.ok) {
          const data = await response?.json();
          if (data.success) {
            setAvailableTopics(data.data || []);
          }
        } else {
          const data = await response?.json();
          console.error(
            `Failed to load DocChain topics: ${data.error}, status: ${response?.status}`
          );
        }
      } catch (error) {
        console.error("Failed to load DocChain topics:", error);
      }
      setIsLoadingTopics(false);
    };

    loadTopics();
  }, []);

  useEffect(() => {
    if (mainRepo) {
      const tempName = getRepoName(mainRepo, mainRepoBranch);
      if (tempName && tempName !== "") {
        const codeTopicName = tempName.toLowerCase();
        const codeTopic = availableTopics.find(
          (topic) => topic.name.toLowerCase() === codeTopicName
        );
        if (codeTopic) {
          setSearchDocchainCode(codeTopic.name);
          setSelectedTopicIdCode(codeTopic.id.toString());
        } else {
          setSearchDocchainCode("");
          setSelectedTopicIdCode("");
        }
      }
    }
  }, [mainRepo, mainRepoBranch, availableTopics]);

  const handleProviderChange = (newProvider: SingleValue<SelectOption>) => {
    setProvider(newProvider?.value ?? "");
    setSelectedProvider(newProvider);
    setTimeout(() => {
      // Set default model for the selected provider
      if (modelConfig) {
        const selectedProvider = modelConfig.providers.find(
          (p: Provider) => p.id === newProvider?.value
        );
        if (selectedProvider && selectedProvider.models.length > 0) {
          setModel(selectedProvider.models[0].id);
        }
      }
    }, 10);
  };

  const filteredTopics = useMemo(() => {
    if (!searchQuery.trim()) return availableDocTopics;
    const query = searchQuery.toLowerCase();
    return availableDocTopics.filter(
      (topic) =>
        topic.name.toLowerCase().includes(query) ||
        topic.id.toString().includes(query) ||
        topic.state.toLowerCase().includes(query)
    );
  }, [searchQuery, availableDocTopics]);

  const filteredTopicsCode = useMemo(() => {
    if (!searchDocchainCode.trim()) return availableTopics;
    const query = searchDocchainCode.toLowerCase();
    return availableTopics.filter(
      (topic) =>
        topic.name.toLowerCase().includes(query) ||
        topic.id.toString().includes(query) ||
        topic.state.toLowerCase().includes(query)
    );
  }, [searchDocchainCode, availableTopics]);

  const getRepoName = (repoUrlOrPath: string, branch: string): string => {
    if (!repoUrlOrPath || typeof repoUrlOrPath !== "string") {
      return "";
    }
    repoUrlOrPath = repoUrlOrPath.trim();

    // Handle remote repository URL (http or https)
    if (
      repoUrlOrPath.startsWith("http://") ||
      repoUrlOrPath.startsWith("https://")
    ) {
      // 去掉最后的.git
      let url = repoUrlOrPath;
      if (url.endsWith(".git")) url = url.slice(0, -4);
      // 拆分路径
      const parts = url.split("/");
      if (parts.length < 2) return "";
      const orgName = parts[parts.length - 2]; // 组织名
      const repoName = parts[parts.length - 1]; // 仓库名
      // 拼接
      return `${orgName}_${branch}_${repoName}_code`;
    }

    // Handle local path, simulating os.path.basename(os.path.normpath(path))
    const normalizedPath = repoUrlOrPath.replace(/\\/g, "/"); // Normalize separators to forward slashes
    // Split by slash and get the last non-empty part to handle trailing slashes
    const parts = normalizedPath.split("/");
    return parts.filter((p) => p).pop() || "";
  };

  return (
    <div className="w-full flex flex-col gap-y-4">
      {/* Provider Selection */}
      <div>
        <label
          htmlFor="provider-dropdown"
          className="block text-sm font-medium text-black mb-2 dark:text-white"
        >
          {messages.newWiki.aiProvider}
        </label>
        <Select
          classNamePrefix={`${theme === 'dark' ? 'dark-react-select' : 'react-select'}`}
          value={selectedProvider}
          options={modelConfig?.providers
            .filter((item) => item.id.toLowerCase() === "whalecloud")
            .map((item) => {
              return { value: item.id, label: item.name };
            })}
          onChange={(newValue) => handleProviderChange(newValue)}
        />
      </div>

      {/* Model Selection - consistent height regardless of type */}
      <div>
        <label
          htmlFor="model-dropdown"
          className="block text-sm font-medium text-black mb-2 dark:text-white"
        >
          {messages.newWiki.aiModel}
        </label>
        <div className="space-y-2">
          {/* Predefined Model Option */}
          <Select
            classNamePrefix={`${theme === 'dark' ? 'dark-react-select' : 'react-select'}`}
            value={selectedModel}
            options={modelConfig?.providers
              .find((p) => p.id === provider)
              ?.models.map((item) => {
                return { value: item.id, label: item.name };
              })}
            onChange={(newValue) => {
              setSelectedModel(newValue);
              setModel(newValue?.value ?? "");
            }}
          />
        </div>
      </div>

      {/* DocChain Topic 选择器（第一个） */}
      <div>
        <label className="text-sm font-medium text-black mb-2 flex items-center gap-1 dark:text-white">
          {t.form?.docchainTopic}
          {/* docchaintopic的地址来源说明，使用了任意SVG图标，info 圆圈 */}
          <span
            className="inline-flex items-center cursor-pointer"
            title={t.form?.docchainInfo}
          >
            <svg
              className="w-4 h-4 text-[var(--muted)] hover:text-[var(--accent-primary)] transition-colors"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M12 2a10 10 0 100 20 10 10 0 000-20z"
              />
            </svg>
          </span>
        </label>

        <div className="bg-[var(--background)]/50 border border-[var(--border-color)] rounded-md p-3">
          <p className="text-xs text-[var(--muted)] mb-3">
            {t.form?.topicSelectorDescription ||
              "Select the topic of the docchain corresponding to an existing product."}
          </p>

          {isLoadingTopics ? (
            <div className="text-sm text-[var(--muted)]">
              {t.common?.loading || "Loading topics..."}
            </div>
          ) : availableDocTopics.length > 0 ? (
            <div className="relative">
              <div className="relative">
                <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                  <svg
                    className="w-4 h-4 text-[var(--muted)]"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </span>
                <input
                  type="text"
                  placeholder={t.form?.searchTopics || "默认空值..."}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onFocus={() => setShowDropdown(true)}
                  onBlur={() => setTimeout(() => setShowDropdown(false), 150)}
                  className="input-japanese block w-full pl-10 pr-3 py-2 text-sm rounded-md bg-transparent text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)] mb-2"
                />
              </div>

              {showDropdown && (
                <div className="absolute z-10 w-full max-h-60 overflow-y-auto bg-[var(--card-bg)] border border-[var(--border-color)] rounded-md mt-1 shadow-lg">
                  <div
                    key="empty"
                    onMouseDown={() => {
                      setSelectedTopicId("");
                      setSearchQuery("");
                      setShowDropdown(false);
                    }}
                    className={`flex items-center justify-between p-3 text-sm hover:bg-[var(--accent-primary)]/10 cursor-pointer transition-colors ${
                      !selectedTopicId
                        ? "bg-[var(--accent-primary)]/5 text-[var(--accent-primary)] font-medium"
                        : "text-[var(--foreground)]"
                    }`}
                  >
                    <span>{t.form?.selectTopic || "默认空值"}</span>
                    {!selectedTopicId && (
                      <svg
                        className="w-4 h-4 text-[var(--accent-primary)]"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    )}
                  </div>

                  <div className="border-t border-[var(--border-color)]/30 my-1" />

                  {filteredTopics.length > 0 ? (
                    filteredTopics.map((topic) => (
                      <div
                        key={topic.id}
                        onMouseDown={() => {
                          setSelectedTopicId(topic.id.toString());
                          setSearchQuery(topic.name);
                          setShowDropdown(false);
                        }}
                        className={`flex items-center justify-between p-3 text-sm hover:bg-[var(--accent-primary)]/10 cursor-pointer transition-colors ${
                          selectedTopicId === topic.id.toString()
                            ? "bg-[var(--accent-primary)]/5 text-[var(--accent-primary)] font-medium"
                            : "text-[var(--foreground)]"
                        }`}
                      >
                        <div>
                          <p className="font-medium">{topic.name}</p>
                          <p className="text-xs text-[var(--muted)]">
                            ID: {topic.id} - {topic.state}
                          </p>
                        </div>
                        {selectedTopicId === topic.id.toString() && (
                          <svg
                            className="w-4 h-4 text-[var(--accent-primary)]"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="text-sm text-center text-[var(--muted)] p-4">
                      {t.form?.noMatchingTopics || "No matching topics found"}
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            <div className="text-sm text-[var(--muted)]">
              {t.form?.noTopicsAvailable ||
                "No topics available. A new topic will be created."}
            </div>
          )}
        </div>
      </div>

      {/* DocChain Topic 选择器（第二个，Code） */}
      <div>
        <label className="block text-sm font-medium text-black mb-2 dark:text-white">
          {t.form?.docchainTopicCode || "Docchain Topic (Code)"}
        </label>

        <div className="bg-[var(--background)]/50 border border-[var(--border-color)] rounded-md p-3">
          <p className="text-xs text-[var(--muted)] mb-3">
            {t.form?.topicSelectorCodeDescription ||
              "Select the topic of the docchain corresponding to an existing product."}
          </p>

          {isLoadingTopics ? (
            <div className="text-sm text-[var(--muted)]">
              {t.common?.loading || "Loading topics..."}
            </div>
          ) : availableTopics.length > 0 ? (
            <div className="relative">
              <div className="relative">
                <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                  <svg
                    className="w-4 h-4 text-[var(--muted)]"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </span>
                <input
                  type="text"
                  placeholder={t.form?.selectTopic || "默认空值..."}
                  value={searchDocchainCode}
                  onChange={(e) => setSearchDocchainCode(e.target.value)}
                  onFocus={() => setShowDropdownCode(true)}
                  onBlur={() =>
                    setTimeout(() => setShowDropdownCode(false), 150)
                  }
                  className="input-japanese block w-full pl-10 pr-3 py-2 text-sm rounded-md bg-transparent text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)] mb-2"
                />
              </div>

              {showDropdownCode && (
                <div className="absolute z-10 w-full max-h-60 overflow-y-auto bg-[var(--card-bg)] border border-[var(--border-color)] rounded-md mt-1 shadow-lg">
                  <div
                    key="empty"
                    onMouseDown={() => {
                      setSelectedTopicIdCode("");
                      setSearchDocchainCode("");
                      setShowDropdownCode(false);
                    }}
                    className={`flex items-center justify-between p-3 text-sm hover:bg-[var(--accent-primary)]/10 cursor-pointer transition-colors ${
                      !selectedTopicIdCode
                        ? "bg-[var(--accent-primary)]/5 text-[var(--accent-primary)] font-medium"
                        : "text-[var(--foreground)]"
                    }`}
                  >
                    <span>{t.form?.selectTopic || "默认空值"}</span>
                    {!selectedTopicIdCode && (
                      <svg
                        className="w-4 h-4 text-[var(--accent-primary)]"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    )}
                  </div>
                  <div className="border-t border-[var(--border-color)]/30 my-1" />
                  {filteredTopicsCode.length > 0 ? (
                    filteredTopicsCode.map((topic) => (
                      <div
                        key={topic.id}
                        onMouseDown={() => {
                          setSelectedTopicIdCode(topic.id.toString());
                          setSearchDocchainCode(topic.name);
                          setShowDropdownCode(false);
                        }}
                        className={`flex items-center justify-between p-3 text-sm hover:bg-[var(--accent-primary)]/10 cursor-pointer transition-colors ${
                          selectedTopicIdCode === topic.id.toString()
                            ? "bg-[var(--accent-primary)]/5 text-[var(--accent-primary)] font-medium"
                            : "text-[var(--foreground)]"
                        }`}
                      >
                        <div>
                          <p className="font-medium">{topic.name}</p>
                          <p className="text-xs text-[var(--muted)]">
                            ID: {topic.id} - {topic.state}
                          </p>
                        </div>
                        {selectedTopicIdCode === topic.id.toString() && (
                          <svg
                            className="w-4 h-4 text-[var(--accent-primary)]"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="text-sm text-center text-[var(--muted)] p-4">
                      {t.form?.noMatchingTopics || "No matching topics found"}
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            <div className="text-sm text-[var(--muted)]">
              {t.form?.noTopicsAvailable ||
                "No topics available. A new topic will be created."}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ModelConfigStep;
