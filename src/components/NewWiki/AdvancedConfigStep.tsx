import { useLanguage } from "@/contexts/LanguageContext";
import React, { useState } from "react";
import CustomRadio from "../CustomRadio";

interface AdvancedConfigStepProps {
  excludedDirs: string;
  setExcludedDirs: (value: React.SetStateAction<string>) => void;
  excludedFiles: string;
  setExcludedFiles: (value: React.SetStateAction<string>) => void;
  includedDirs: string;
  setIncludedDirs: (value: React.SetStateAction<string>) => void;
  includedFiles: string;
  setIncludedFiles: (value: React.SetStateAction<string>) => void;
}

const AdvancedConfigStep = (props: AdvancedConfigStepProps) => {
  const { messages: t } = useLanguage();

  const {
    excludedDirs,
    setExcludedDirs,
    excludedFiles,
    setExcludedFiles,
    includedDirs,
    setIncludedDirs,
    includedFiles,
    setIncludedFiles,
  } = props;

  const [filterMode, setFilterMode] = useState<"exclude" | "include">(
    "exclude"
  );
  const [showDefaultDirs, setShowDefaultDirs] = useState(false);
  const [showDefaultFiles, setShowDefaultFiles] = useState(false);

  // Default excluded directories from config.py
  const defaultExcludedDirs = `./.venv/
./venv/
./env/
./virtualenv/
./node_modules/
./bower_components/
./jspm_packages/
./.git/
./.svn/
./.hg/
./.bzr/
./__pycache__/
./.pytest_cache/
./.mypy_cache/
./.ruff_cache/
./.coverage/
./dist/
./build/
./out/
./target/
./bin/
./obj/
./docs/
./_docs/
./site-docs/
./_site/
./.idea/
./.vscode/
./.vs/
./.eclipse/
./.settings/
./logs/
./log/
./tmp/
./temp/
./.eng`;

  // Default excluded files from config.py
  const defaultExcludedFiles = `yarn.lock
pnpm-lock.yaml
npm-shrinkwrap.json
poetry.lock
Pipfile.lock
requirements.txt.lock
Cargo.lock
composer.lock
.lock
.DS_Store
Thumbs.db
desktop.ini
*.lnk
.env
.env.*
*.env
*.cfg
*.ini
.flaskenv
.gitignore
.gitattributes
.gitmodules
.github
.gitlab-ci.yml
.prettierrc
.eslintrc
.eslintignore
.stylelintrc
.editorconfig
.jshintrc
.pylintrc
.flake8
mypy.ini
pyproject.toml
tsconfig.json
webpack.config.js
babel.config.js
rollup.config.js
jest.config.js
karma.conf.js
vite.config.js
next.config.js
*.min.js
*.min.css
*.bundle.js
*.bundle.css
*.map
*.gz
*.zip
*.tar
*.tgz
*.rar
*.pyc
*.pyo
*.pyd
*.so
*.dll
*.class
*.exe
*.o
*.a
*.jpg
*.jpeg
*.png
*.gif
*.ico
*.svg
*.webp
*.mp3
*.mp4
*.wav
*.avi
*.mov
*.webm
*.csv
*.tsv
*.xls
*.xlsx
*.db
*.sqlite
*.sqlite3
*.pdf
*.docx
*.pptx`;

  return (
    <div className="w-full">
      <label className="block text-sm font-medium text-black mb-2 dark:text-white">
        {t.newWiki?.fileFilters || "文件过滤"}
      </label>
      <div className="w-full flex flex-col gap-y-2">
        {/* Filter Mode Toggle */}
        <div className="flex items-center space-x-4">
          <CustomRadio
            selected={filterMode === "exclude"}
            onClick={() => setFilterMode("exclude")}
          >
            <span className="text-sm text-[var(--accent-primary)]">
              {t.newWiki?.excludedFiles || "排除文件"}
            </span>
          </CustomRadio>
        </div>

        {filterMode === "exclude" && (
          <div className="w-full flex flex-col gap-y-2">
            {/* Directory Filters */}
            <div className="border border-[var(--border-color)] rounded-md p-2">
              <label className="block text-xs font-medium text-[var(--foreground)] mb-1.5">
                {t.newWiki?.excludedDirs || "排除文件夹"}
              </label>
              <div className="space-y-2">
                <textarea
                  value={excludedDirs}
                  onChange={(e) => setExcludedDirs?.(e.target.value)}
                  placeholder={t.newWiki?.excludedDirsHelp || "输入想要排除的文件夹列表 (每行一个)"}
                  className="input-japanese block w-full text-sm bg-transparent text-[var(--foreground)] resize-none focus:outline-none"
                  rows={4}
                />
                <button
                  type="button"
                  onClick={() => setShowDefaultDirs(!showDefaultDirs)}
                  className="text-xs text-[var(--accent-primary)] hover:text-[var(--accent-primary)]/80 transition-colors"
                >
                  {showDefaultDirs
                    ? t.common?.hide || "隐藏"
                    : t.common?.show || "展示"}{" "}
                  {t.form?.defaultExcludedDirs ||
                    "默认排除的文件夹"}
                </button>
                {showDefaultDirs && filterMode === "exclude" && (
                  <div className="p-2 bg-[var(--background)]/30 rounded text-xs text-[var(--muted)] font-mono whitespace-pre-wrap max-h-32 overflow-y-auto">
                    {defaultExcludedDirs}
                  </div>
                )}
              </div>
            </div>

            {/* File Filters */}
            <div className="border border-[var(--border-color)] rounded-md p-2">
              <label className="block text-xs font-medium text-[var(--foreground)] mb-1.5">
                {t.newWiki?.excludedFiles || "排除文件"}
              </label>
              <div className="space-y-2">
                <textarea
                  value={excludedFiles}
                  onChange={(e) => setExcludedFiles?.(e.target.value)}
                  placeholder={t.newWiki?.excludedFilesHelp || "输入想要排除的文件列表 (每行一个)"}
                  className="input-japanese block w-full text-sm bg-transparent text-[var(--foreground)] resize-none focus:outline-none"
                  rows={4}
                />
                <button
                  type="button"
                  onClick={() => setShowDefaultFiles(!showDefaultFiles)}
                  className="text-xs text-[var(--accent-primary)] hover:text-[var(--accent-primary)]/80 transition-colors"
                >
                  {showDefaultFiles
                    ? t.common?.hide || "隐藏"
                    : t.common?.show || "展示"}{" "}
                  {t.form?.defaultExcludedFiles || "默认排除的文件"}
                </button>
                {showDefaultFiles && filterMode === "exclude" && (
                  <div className="p-2 bg-[var(--background)]/30 rounded text-xs text-[var(--muted)] font-mono whitespace-pre-wrap max-h-32 overflow-y-auto">
                    {defaultExcludedFiles}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        <div className="flex items-center space-x-4 mt-2">
          <CustomRadio
            selected={filterMode === "include"}
            onClick={() => setFilterMode("include")}
          >
            <span className="text-sm text-[var(--accent-primary)]">
              {t.newWiki?.includedFiles || "包含文件"}
            </span>
          </CustomRadio>
        </div>

        {filterMode === "include" && (
          <div className="w-full flex flex-col gap-y-2">
            {/* Directory Filters */}
            <div className="border border-[var(--border-color)] rounded-md p-2">
              <label className="block text-xs font-medium text-[var(--foreground)] mb-1.5">
                {t.newWiki?.includedDirs || "包含文件夹"}
              </label>
              <div className="space-y-2">
                <textarea
                  value={includedDirs}
                  onChange={(e) => setIncludedDirs?.(e.target.value)}
                  placeholder={t.newWiki?.includedDirsHelp || "输入想要包含的文件夹列表 (每行一个)"}
                  className="input-japanese block w-full text-sm bg-transparent text-[var(--foreground)] resize-none focus:outline-none"
                  rows={4}
                />
              </div>
            </div>

            {/* File Filters */}
            <div className="border border-[var(--border-color)] rounded-md p-2">
              <label className="block text-xs font-medium text-[var(--foreground)] mb-1.5">
                {t.newWiki?.includedFiles || "包含文件"}
              </label>
              <div className="space-y-2">
                <textarea
                  value={includedFiles}
                  onChange={(e) => setIncludedFiles?.(e.target.value)}
                  placeholder={t.newWiki?.includedFilesHelp || "输入想要包含的文件列表 (每行一个)"}
                  className="input-japanese block w-full text-sm bg-transparent text-[var(--foreground)] resize-none focus:outline-none"
                  rows={4}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdvancedConfigStep;
