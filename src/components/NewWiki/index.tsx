import { useSettings } from "@/contexts/SettingsContext";
import { useToast } from "@/contexts/ToastContext";
import { extractUrlPath } from "@/utils/urlDecoder";
import React, { useState } from "react";
import RepoStep from "./RepoStep";
import WikiConfigStep from "./WikiConfigStep";
import ModelConfigStep from "./ModelConfigStep";
import AdvancedConfigStep from "./AdvancedConfigStep";
import Stepper from "../Stepper";
import { useLanguage } from "@/contexts/LanguageContext";
import { authFetch } from "@/utils/authFetch";
import { Tag } from "@/types/tag";
import { RepoMetadata } from "@/types/RepoMetadata";

interface NewWikiProps {
  onClose: () => void;
  onSuccess: () => void;
}

interface SubRepoInfo {
  url: string;
  branch: string;
}

const NewWiki = (props: NewWikiProps) => {
  const { onClose, onSuccess } = props;

  const { addToast } = useToast();
  const { settings } = useSettings();
  const { language, messages } = useLanguage();

  const [selectedPlatform] = useState<
    "whaleDevCloud" | "gitlab" | "bitbucket" | "github"
  >("whaleDevCloud");
  const [mainRepo, setMainRepo] = useState(
    "https://git-nj.iwhalecloud.com/ptdev01/whale-deepwiki.git"
  );
  const [mainRepoBranch, setMainRepoBranch] = useState<string>("");
  const [mainRepoBranches, setMainRepoBranches] = useState<
    Record<string, string[]>
  >({});
  const [subRepos, setSubRepos] = useState<SubRepoInfo[]>([]);
  const [subRepoBranches, setSubRepoBranches] = useState<
    Record<string, string[]>
  >({});
  const [selectedLanguage, setSelectedLanguage] = useState<string>(language);
  const [comprehensive, setComprehensive] = useState<boolean>(true);
  const [provider, setProvider] = useState<string>("");
  const [isCustomModel] = useState<boolean>(false);
  const [model, setModel] = useState<string>("");
  const [excludedDirs, setExcludedDirs] = useState("");
  const [excludedFiles, setExcludedFiles] = useState("");
  const [includedDirs, setIncludedDirs] = useState("");
  const [includedFiles, setIncludedFiles] = useState("");
  const [selectedTopicId, setSelectedTopicId] = useState<string>("");
  const [selectedTopicIdCode, setSelectedTopicIdCode] = useState<string>("");
  const [selectedTags, setSelectedTags] = useState<Tag[]>([]);
  const [repoMetadata, setRepoMetadata] = useState<RepoMetadata | null>(null);
  const [comments, setComments] = useState<string>("");

  const handleGenerateWiki = async () => {
    // Parse the repository input
    const parsedRepo = parseRepositoryInput(mainRepo);

    // Validate that we have a parsed repo
    if (!parsedRepo) {
      addToast({
        type: "warning",
        title: messages.newWiki.parameterVerificationFailed,
        message: messages.newWiki.pleaseEnterValidRepositoryUrl,
      });
      return;
    }

    if (!mainRepoBranch) {
      addToast({
        type: "warning",
        title: messages.newWiki.parameterVerificationFailed,
        message: messages.newWiki.pleaseSelectBranch,
      });
      return;
    }

    try {
      // 准备API请求参数
      const cleanedApiKey = settings.apiKey.replace(/●/g, "");
      const cleanedWhaleDevCloudToken = settings.whaleDevCloudToken.replace(
        /●/g,
        ""
      );
      const newWikiData = {
        repo_url: mainRepo,
        branch: mainRepoBranch || "master",
        repo_type: parsedRepo.type === "local" ? "local" : selectedPlatform,
        sub_repos: subRepos || undefined,
        token: cleanedWhaleDevCloudToken,
        language: selectedLanguage,
        model_settings: {
          provider: provider,
          model: model,
          is_custom_model: isCustomModel,
          api_key: cleanedApiKey,
        },
        comprehensive: comprehensive,
        existing_topic_id: selectedTopicId || undefined,
        existing_topic_id_code: selectedTopicIdCode || undefined,
        excluded_dirs: excludedDirs || undefined,
        excluded_files: excludedFiles || undefined,
        included_dirs: includedDirs || undefined,
        included_files: includedFiles || undefined,
        tag_ids:
          selectedTags.length > 0
            ? selectedTags.map((tag) => tag.id)
            : undefined,
        repo_metadata: repoMetadata || undefined,
        comments: comments,
      };

      // 发送API请求创建wiki生成任务
      const response = await authFetch("/api/wiki/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newWikiData),
      });

      if (!response || !response.ok) {
        throw new Error(`API request failed with status: ${response?.status}`);
      }

      const result = await response.json();

      // 显示进度窗口
      if (onSuccess) {
        onSuccess();
      }

      // 检查是否已存在相同仓库的任务
      if (result.exists) {
        addToast({
          type: "warning",
          title: messages.newWiki.taskExists,
          message: messages.newWiki.taskExistsDescription,
        });
      } else {
        addToast({
          type: "success",
          title: messages.newWiki.createSuccess,
          message: messages.newWiki.createSuccessDescription,
        });
      }
    } catch (err) {
      console.error(err);
      addToast({
        type: "error",
        title: messages.newWiki.createFailed,
        message: messages.newWiki.createFailedDescription,
      });
    }
  };

  const parseRepositoryInput = (
    input: string
  ): {
    owner: string;
    repo: string;
    type: string;
    fullPath?: string;
    localPath?: string;
  } | null => {
    input = input.trim();

    let owner = "",
      repo = "",
      type = "whaleDevCloud",
      fullPath;
    let localPath: string | undefined;

    // Handle Windows absolute paths (e.g., C:\path\to\folder)
    const windowsPathRegex =
      /^[a-zA-Z]:\\(?:[^\\/:*?"<>|\r\n]+\\)*[^\\/:*?"<>|\r\n]*$/;
    const customGitRegex =
      /^(?:https?:\/\/)?([^\/]+)\/(.+?)\/([^\/]+)(?:\.git)?\/?$/;

    if (windowsPathRegex.test(input)) {
      type = "local";
      localPath = input;
      repo = input.split("\\").pop() || "local-repo";
      owner = "local";
    }
    // Handle Unix/Linux absolute paths (e.g., /path/to/folder)
    else if (input.startsWith("/")) {
      type = "local";
      localPath = input;
      repo = input.split("/").filter(Boolean).pop() || "local-repo";
      owner = "local";
    } else if (customGitRegex.test(input)) {
      type = "web";
      fullPath = extractUrlPath(input)?.replace(/\.git$/, "");
      const parts = fullPath?.split("/") ?? [];
      if (parts.length >= 2) {
        repo = parts[parts.length - 1] || "";
        owner = parts[parts.length - 2] || "";
      }
    }
    // Unsupported URL formats
    else {
      console.error("Unsupported URL format:", input);
      return null;
    }

    if (!owner || !repo) {
      return null;
    }

    // Clean values
    owner = owner.trim();
    repo = repo.trim();

    // Remove .git suffix if present
    if (repo.endsWith(".git")) {
      repo = repo.slice(0, -4);
    }

    return { owner, repo, type, fullPath, localPath };
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4 text-center bg-black/50">
        <div className="relative transform overflow-hidden rounded-lg bg-[var(--card-bg)] text-left shadow-xl transition-all sm:my-8 sm:max-w-2xl sm:w-full">
          {/* modal header */}
          <div className="flex items-center justify-between px-6 py-4 border-b border-[var(--border-color)]">
            <h3 className="text-lg font-medium text-[var(--accent-primary)]">
              <span className="text-[var(--accent-primary)]">{messages.newWiki.configureWiki}</span>
            </h3>
            <button
              type="button"
              onClick={onClose}
              className="text-[var(--muted)] hover:text-[var(--foreground)] focus:outline-none transition-colors"
            >
              <svg
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
          {/* modal body */}
          <div className="p-6 overflow-y-auto">
            <Stepper
              onFinish={handleGenerateWiki}
              onClose={onClose}
              titles={[messages.newWiki.basicInfo, messages.newWiki.wikiStyleSettings, messages.newWiki.aiSettings, messages.newWiki.advancedSettings]}
            >
              <RepoStep
                mainRepo={mainRepo}
                setMainRepo={setMainRepo}
                mainRepoBranch={mainRepoBranch}
                setMainRepoBranch={setMainRepoBranch}
                mainRepoBranches={mainRepoBranches}
                setMainRepoBranches={setMainRepoBranches}
                subRepos={subRepos}
                setSubRepos={setSubRepos}
                subRepoBranches={subRepoBranches}
                setSubRepoBranches={setSubRepoBranches}
                selectedTags={selectedTags}
                setSelectedTags={setSelectedTags}
                repoMetadata={repoMetadata}
                setRepoMetadata={setRepoMetadata}
                comments={comments}
                setComments={setComments}
              />
              <WikiConfigStep
                selectedLanguage={selectedLanguage}
                setSelectedLanguage={setSelectedLanguage}
                comprehensive={comprehensive}
                setComprehensive={setComprehensive}
              />
              <ModelConfigStep
                mainRepo={mainRepo}
                mainRepoBranch={mainRepoBranch}
                provider={provider}
                setProvider={setProvider}
                setModel={setModel}
                selectedTopicId={selectedTopicId}
                setSelectedTopicId={setSelectedTopicId}
                selectedTopicIdCode={selectedTopicIdCode}
                setSelectedTopicIdCode={setSelectedTopicIdCode}
              />
              <AdvancedConfigStep
                excludedDirs={excludedDirs}
                setExcludedDirs={setExcludedDirs}
                excludedFiles={excludedFiles}
                setExcludedFiles={setExcludedFiles}
                includedDirs={includedDirs}
                setIncludedDirs={setIncludedDirs}
                includedFiles={includedFiles}
                setIncludedFiles={setIncludedFiles}
              />
            </Stepper>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewWiki;
