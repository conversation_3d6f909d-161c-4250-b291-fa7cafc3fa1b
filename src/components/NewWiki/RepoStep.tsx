import { useSettings } from "@/contexts/SettingsContext";
import { authFetch } from "@/utils/authFetch";
import React, { useEffect, useState, useRef, useCallback } from "react";
import { AiOutlineMinus, AiOutlinePlus } from "react-icons/ai";
import { FaTimes, FaSearch, FaChevronDown, FaPlus } from "react-icons/fa";
import { Tag } from "@/types/tag";
import AsyncSelect from "react-select/async";
import Select, { MultiValue, SingleValue } from "react-select";
import {
  defaultRepoMetadata,
  RepoMetadata,
  RepoMetadataResponse,
  Solution,
} from "@/types/RepoMetadata";
import { useTheme } from "next-themes";
import { useLanguage } from "@/contexts/LanguageContext";

interface SubRepoInfo {
  url: string;
  branch: string;
}

interface SelectOption {
  label: string | null;
  value: string | number | null;
}

interface ProductLineOption {
  productLineId: number;
  productLineName: string;
}

interface ProductOption {
  productId: number;
  productName: string;
  classType: string;
}

interface ProductVersionOption {
  productVersionId: number;
  productVersionCode: string;
  classType: string;
  productDto: null;
}

interface SolutionOption {
  productId: number;
  productName: string;
  classType: string;
}

interface RepoStepProps {
  mainRepo: string;
  setMainRepo: (value: React.SetStateAction<string>) => void;
  mainRepoBranch: string;
  setMainRepoBranch: (value: React.SetStateAction<string>) => void;
  mainRepoBranches: Record<string, string[]>;
  setMainRepoBranches: (
    value: React.SetStateAction<Record<string, string[]>>
  ) => void;
  subRepos: SubRepoInfo[];
  setSubRepos: (value: React.SetStateAction<SubRepoInfo[]>) => void;
  subRepoBranches: Record<string, string[]>;
  setSubRepoBranches: (
    value: React.SetStateAction<Record<string, string[]>>
  ) => void;
  selectedTags: Tag[];
  setSelectedTags: (value: React.SetStateAction<Tag[]>) => void;
  repoMetadata: RepoMetadata | null;
  setRepoMetadata: (value: React.SetStateAction<RepoMetadata | null>) => void;
  comments: string;
  setComments: (value: React.SetStateAction<string>) => void;
}

const RepoStep = (props: RepoStepProps) => {
  const { settings } = useSettings();
  const { theme } = useTheme();
  const [allTags, setAllTags] = useState<Tag[]>([]);
  const [showTagDropdown, setShowTagDropdown] = useState(false);
  const [tagSearchTerm, setTagSearchTerm] = useState("");
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState<"top" | "bottom">(
    "bottom"
  );
  const [dropdownAlignment, setDropdownAlignment] = useState<"left" | "right">(
    "left"
  );
  const [newTagForm, setNewTagForm] = useState({
    name: "",
    type: 2, // 用户标签
    color: "#4ECDC4",
    comments: "",
    module_type: 1,
    state: 1,
  });
  const tagDropdownRef = useRef<HTMLDivElement>(null);
  const colorPickerRef = useRef<HTMLDivElement>(null);
  const colorButtonRef = useRef<HTMLButtonElement>(null);
  const addTagButtonRef = useRef<HTMLButtonElement>(null);

  const debounceTimeout = useRef<ReturnType<typeof setTimeout> | null>(null);
  const latestInputValue = useRef<string>("");

  // 预设颜色
  const PRESET_COLORS = [
    "#FF6B6B",
    "#4ECDC4",
    "#45B7D1",
    "#96CEB4",
    "#DDA0DD",
    "#98D8C8",
    "#F7DC6F",
    "#BB8FCE",
    "#85C1E9",
    "#F8C471",
    "#82E0AA",
    "#F1948A",
    "#E74C3C",
    "#D7BDE2",
    "#F9E79F",
    "#D5A6BD",
    "#A9CCE3",
    "#FAD7A0",
    "#D2B4DE",
    "#2C3E50",
    "#34495E",
    "#8B4513",
    "#4A235A",
    "#1B4F72",
  ];

  const {
    mainRepo,
    setMainRepo,
    mainRepoBranch,
    setMainRepoBranch,
    mainRepoBranches,
    setMainRepoBranches,
    subRepos,
    setSubRepos,
    subRepoBranches,
    setSubRepoBranches,
    selectedTags,
    setSelectedTags,
    repoMetadata,
    setRepoMetadata,
    comments,
    setComments,
  } = props;

  const [selectedProductLine, setSelectedProductLine] =
    useState<SelectOption | null>();
  const [selectedProduct, setSelectedProduct] = useState<SelectOption | null>();
  const [selectedProductVersion, setSelectedProductVersion] =
    useState<SelectOption | null>();
  const [selectedSolutions, setSelectedSolutions] = useState<SelectOption[]>(
    []
  );
  const { messages } = useLanguage();

  // 加载所有标签
  const loadTags = async () => {
    try {
      const response = await authFetch("/api/tags/tag");
      if (response?.ok) {
        const data = await response.json();
        setAllTags(data.data.filter((tag: Tag) => tag.state === 1) || []);
      }
    } catch (error) {
      console.error("加载标签失败:", error);
    }
  };

  // 添加标签
  const addTag = (tag: Tag) => {
    if (!selectedTags.some((t) => t.id === tag.id)) {
      setSelectedTags([...selectedTags, tag]);
    }
    setShowTagDropdown(false);
    setTagSearchTerm("");
  };

  // 删除标签
  const removeTag = (tagId: number) => {
    setSelectedTags(selectedTags.filter((tag) => tag.id !== tagId));
  };

  // 创建新标签
  const createNewTag = async () => {
    if (!newTagForm.name.trim()) {
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await authFetch("/api/tags/tag", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(newTagForm),
      });

      if (response?.ok) {
        const newTag = await response.json();
        // 创建成功后自动添加到已选标签
        if (newTag.data) {
          setSelectedTags([...selectedTags, newTag.data]);
        }
        setShowCreateForm(false);
        setNewTagForm({
          name: "",
          type: 2,
          color: "#4ECDC4",
          comments: "",
          module_type: 1,
          state: 1,
        });
        loadTags(); // 重新加载标签列表
      }
    } catch (error) {
      console.error("创建标签失败:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 过滤可添加的标签（排除已选择的）
  const availableTags = allTags.filter(
    (tag) =>
      !selectedTags.some((selectedTag) => selectedTag.id === tag.id) &&
      tag.name.toLowerCase().includes(tagSearchTerm.toLowerCase())
  );

  // 计算下拉框位置
  const calculateDropdownPosition = () => {
    if (!addTagButtonRef.current) return;

    const buttonRect = addTagButtonRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;
    const dropdownHeight = 300; // 预估下拉框高度
    const dropdownWidth = 288; // 72 * 4 = 288px (w-72)
    const minSpacing = 20; // 最小间距

    // 检查按钮下方空间
    const spaceBelow = viewportHeight - buttonRect.bottom;
    // 检查按钮上方空间
    const spaceAbove = buttonRect.top;

    // 检查水平空间
    const spaceRight = viewportWidth - buttonRect.left;
    const spaceLeft = buttonRect.left;

    // 确定垂直位置
    if (spaceBelow >= dropdownHeight + minSpacing || spaceBelow > spaceAbove) {
      setDropdownPosition("bottom");
    } else if (spaceAbove >= dropdownHeight + minSpacing) {
      setDropdownPosition("top");
    } else {
      // 如果上下空间都不够，选择空间较大的方向
      setDropdownPosition(spaceBelow > spaceAbove ? "bottom" : "top");
    }

    // 确定水平对齐方式
    if (spaceRight >= dropdownWidth + minSpacing) {
      setDropdownAlignment("left");
    } else if (spaceLeft >= dropdownWidth + minSpacing) {
      setDropdownAlignment("right");
    } else {
      // 如果都不够，选择空间较大的方向
      setDropdownAlignment(spaceRight > spaceLeft ? "left" : "right");
    }
  };

  // 切换标签下拉框
  const toggleTagDropdown = () => {
    if (!showTagDropdown) {
      calculateDropdownPosition();
    }
    setShowTagDropdown(!showTagDropdown);
    setTagSearchTerm("");
  };

  // 点击外部关闭标签下拉框和颜色选择器
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        tagDropdownRef.current &&
        !tagDropdownRef.current.contains(event.target as Node)
      ) {
        setShowTagDropdown(false);
        setShowCreateForm(false);
      }
      if (
        colorPickerRef.current &&
        !colorPickerRef.current.contains(event.target as Node) &&
        colorButtonRef.current &&
        !colorButtonRef.current.contains(event.target as Node)
      ) {
        setShowColorPicker(false);
      }
    };

    // 监听窗口大小改变和滚动事件
    const handleResize = () => {
      if (showTagDropdown) {
        calculateDropdownPosition();
      }
    };

    const handleScroll = () => {
      if (showTagDropdown) {
        calculateDropdownPosition();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    window.addEventListener("resize", handleResize);
    window.addEventListener("scroll", handleScroll, true);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      window.removeEventListener("resize", handleResize);
      window.removeEventListener("scroll", handleScroll, true);
    };
  }, [showTagDropdown]);

  // 打开时自动拉主仓库分支和标签
  useEffect(() => {
    fetchRepoBranches();
    loadTags();
  }, []);

  useEffect(() => {
    if (repoMetadata) {
      if (repoMetadata.product_line_id) {
        setSelectedProductLine({
          value: repoMetadata.product_line_id,
          label: repoMetadata.product_line_name,
        });
      } else {
        setSelectedProductLine(null);
      }
      if (repoMetadata.product_id) {
        setSelectedProduct({
          value: repoMetadata.product_id,
          label: repoMetadata.product_name,
        });
      } else {
        setSelectedProduct(null);
      }
      if (repoMetadata.product_version_id) {
        setSelectedProductVersion({
          value: repoMetadata.product_version_id,
          label: repoMetadata.product_version_code,
        });
      } else {
        setSelectedProductVersion(null);
      }
      if (repoMetadata.solutions) {
        setSelectedSolutions(
          repoMetadata.solutions.map((solution) => {
            return {
              value: solution.solution_id,
              label: solution.solution_name,
            };
          })
        );
      } else {
        setSelectedSolutions([]);
      }
    }
  }, [repoMetadata]);

  // 主仓库输入失焦/回车
  const onRepoInputBlur = () => {
    fetchRepoBranches();
    fetchRepoMetadata();
  };
  const onRepoInputEnter = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      fetchRepoBranches();
      fetchRepoMetadata();
    }
  };

  // 子仓库输入失焦/回车
  const onSubRepoInputBlur = (url: string, idx: number) => {
    fetchSubRepoBranches(url, idx);
  };
  const onSubRepoInputEnter = (
    e: React.KeyboardEvent<HTMLInputElement>,
    url: string,
    idx: number
  ) => {
    if (e.key === "Enter") {
      fetchSubRepoBranches(url, idx);
    }
  };

  const fetchRepoBranches = () => {
    if (!mainRepo) return;
    const body: Record<string, string> = {
      repo_type: "whaleDevCloud",
      repo_url: mainRepo,
    };
    if (settings.whaleDevCloudToken) {
      body.token = settings.whaleDevCloudToken;
    }
    authFetch(`/api/repo/branches`, {
      headers: { "Content-Type": "application/json" },
      method: "POST",
      body: JSON.stringify(body),
    })
      .then((response) => {
        if (response && response.ok) {
          response.json().then((data) => {
            setMainRepoBranches({ [mainRepo]: data });
            if (!mainRepoBranch) {
              if (data.includes("master")) setMainRepoBranch("master");
              else if (data.includes("release")) setMainRepoBranch("release");
              else if (data.includes("develop")) setMainRepoBranch("develop");
              else setMainRepoBranch(data[0]);
            }
          });
        }
      })
      .catch((err) => {
        console.error("Failed to get branches: ", err);
      });
  };

  // 拉取子仓库分支
  const fetchSubRepoBranches = (url: string, idx: number) => {
    if (!url) return;
    const body: Record<string, string> = {
      repo_type: "whaleDevCloud",
      repo_url: url,
    };
    if (settings.whaleDevCloudToken) {
      body.token = settings.whaleDevCloudToken;
    }
    authFetch(`/api/repo/branches`, {
      headers: { "Content-Type": "application/json" },
      method: "POST",
      body: JSON.stringify(body),
    })
      .then((response) => {
        if (response && response.ok) {
          response.json().then((data) => {
            setSubRepoBranches((prev: Record<string, string[]>) => ({
              ...prev,
              [url]: data,
            }));
            setSubRepos((subs: SubRepoInfo[]) =>
              subs.map((item, i) => {
                if (i !== idx) return item;
                // 只有分支为空时才自动赋值
                if (!item.branch) {
                  const defaultBranch = data.includes("master")
                    ? "master"
                    : data.includes("release")
                    ? "release"
                    : data.includes("develop")
                    ? "develop"
                    : data[0] || "";
                  return { ...item, branch: defaultBranch };
                }
                // 如果当前选中的分支不存在于最新分支列表，则自动切换到第一个可用分支
                if (!data.includes(item.branch)) {
                  const defaultBranch = data.includes("master")
                    ? "master"
                    : data.includes("release")
                    ? "release"
                    : data.includes("develop")
                    ? "develop"
                    : data[0] || "";
                  return { ...item, branch: defaultBranch };
                }
                // 否则保留当前分支
                return item;
              })
            );
          });
        }
      })
      .catch((err) => {
        console.error("Failed to get branches: ", err);
      });
  };

  const addSubRepo = () =>
    setSubRepos((subs: SubRepoInfo[]) => [...subs, { url: "", branch: "" }]);
  const removeSubRepo = (idx: number) =>
    setSubRepos((subs: SubRepoInfo[]) => subs.filter((_, i) => i !== idx));

  const loadProductLines = useCallback(
    (inputValue: string, callback: (options: SelectOption[]) => void) => {
      latestInputValue.current = inputValue;
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }

      debounceTimeout.current = setTimeout(() => {
        fetch(`/api/zcm/product-lines?search=${inputValue}`).then(
          (response) => {
            if (response && response.ok) {
              response.json().then((data: ProductLineOption[]) => {
                callback(
                  data.map((item: ProductLineOption) => {
                    return {
                      label: item.productLineName,
                      value: item.productLineId,
                    };
                  })
                );
              });
            }
          }
        );
      }, 1000);
    },
    []
  );

  const onProductLineChange = (option: SingleValue<SelectOption>) => {
    setRepoMetadata((pre: RepoMetadata | null) => {
      const newData = pre ? { ...pre } : defaultRepoMetadata();
      newData.product_line_id = option?.value ?? null;
      newData.product_line_name = option?.label ?? null;
      return newData;
    });
  };

  const loadProducts = useCallback(
    (inputValue: string, callback: (options: SelectOption[]) => void) => {
      latestInputValue.current = inputValue;
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }

      debounceTimeout.current = setTimeout(() => {
        fetch(`/api/zcm/products?search=${inputValue}`).then((response) => {
          if (response && response.ok) {
            response.json().then((data: ProductOption[]) => {
              callback(
                data.map((item: ProductOption) => {
                  return { label: item.productName, value: item.productId };
                })
              );
            });
          }
        });
      }, 1000);
    },
    []
  );

  const onProductChange = (option: SingleValue<SelectOption>) => {
    setRepoMetadata((pre: RepoMetadata | null) => {
      const newData = pre ? { ...pre } : defaultRepoMetadata();
      newData.product_id = option?.value ?? null;
      newData.product_name = option?.label ?? null;
      return newData;
    });
  };

  const loadProductVersions = useCallback(
    (inputValue: string, callback: (options: SelectOption[]) => void) => {
      latestInputValue.current = inputValue;
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }

      debounceTimeout.current = setTimeout(() => {
        fetch(`/api/zcm/product-versions?search=${inputValue}`).then(
          (response) => {
            if (response && response.ok) {
              response.json().then((data: ProductVersionOption[]) => {
                callback(
                  data.map((item: ProductVersionOption) => {
                    return {
                      label: item.productVersionCode,
                      value: item.productVersionId,
                    };
                  })
                );
              });
            }
          }
        );
      }, 1000);
    },
    []
  );

  const onProductVersionChange = (option: SingleValue<SelectOption>) => {
    setRepoMetadata((pre: RepoMetadata | null) => {
      const newData = pre ? { ...pre } : defaultRepoMetadata();
      newData.product_version_id = option?.value ?? null;
      newData.product_version_code = option?.label ?? null;
      return newData;
    });
  };

  const loadSolutions = useCallback(
    (inputValue: string, callback: (options: SelectOption[]) => void) => {
      latestInputValue.current = inputValue;
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }

      debounceTimeout.current = setTimeout(() => {
        fetch(`/api/zcm/solutions?search=${inputValue}`).then((response) => {
          if (response && response.ok) {
            response.json().then((data) => {
              callback(
                data.map((item: SolutionOption) => {
                  return { value: item.productId, label: item.productName };
                })
              );
            });
          }
        });
      }, 1000);
    },
    []
  );

  const onSolutionsChange = (options: MultiValue<SelectOption>) => {
    setRepoMetadata((pre: RepoMetadata | null) => {
      const newData = pre ? { ...pre } : defaultRepoMetadata();
      const solutions = newData.solutions;
      const newSolutions: Solution[] = options.map((option: SelectOption) => {
        const oldSolution = solutions.find(
          (solution) => solution.solution_id === option.value
        );
        if (oldSolution) {
          return { ...oldSolution };
        } else {
          return {
            release_pkg_id: null,
            release_pkg_code: null,
            solution_id: option.value,
            solution_name: option.label,
          };
        }
      });
      newData.solutions = newSolutions;
      return newData;
    });
  };

  const fetchRepoMetadata = () => {
    if (!mainRepo) return;
    const params = new URLSearchParams({ repo_url: mainRepo });
    authFetch(`/api/zcm/repo/metadata?${params.toString()}`)
      .then((response) => response?.json())
      .then((data: RepoMetadataResponse) => {
        if (data) {
          const repo_metadata: RepoMetadata = defaultRepoMetadata();
          repo_metadata.dc_repo_id = data.repositoryId;
          repo_metadata.dc_project_id = data.projectId;
          if (data.apiBranchVersionDto) {
            repo_metadata.branch_version_id =
              data.apiBranchVersionDto?.branchVersionId ?? null;
            repo_metadata.branch_version_name =
              data.apiBranchVersionDto?.branchName ?? null;
          }
          if (data.productLineDto) {
            repo_metadata.product_line_id =
              data.productLineDto?.productLineId ?? null;
            repo_metadata.product_line_name =
              data.productLineDto?.productLineName ?? null;
          }
          if (
            data.apiProductVersionDto &&
            data.apiProductVersionDto.classType === "V"
          ) {
            repo_metadata.product_version_id =
              data.apiProductVersionDto.productVersionId;
            repo_metadata.product_version_code =
              data.apiProductVersionDto.productVersionCode;
            if (
              data.apiProductVersionDto.productDto &&
              data.apiProductVersionDto.productDto.classType === "P"
            ) {
              repo_metadata.product_id =
                data.apiProductVersionDto.productDto.productId;
              repo_metadata.product_name =
                data.apiProductVersionDto.productDto.productName;
            }
          }
          if (
            data.apiProductVersionDto &&
            data.apiProductVersionDto.classType === "D"
          ) {
            const solution: Solution = {
              release_pkg_id: data.apiProductVersionDto.productVersionId,
              release_pkg_code: data.apiProductVersionDto.productVersionCode,
              solution_name: null,
              solution_id: null,
            };
            if (
              data.apiProductVersionDto.productDto &&
              data.apiProductVersionDto.productDto.classType === "D"
            ) {
              solution.solution_name =
                data.apiProductVersionDto.productDto.productName;
              solution.solution_id =
                data.apiProductVersionDto.productDto.productId;
            }
            repo_metadata.solutions.push(solution);
          }
          if (data.apiDistributionDtoList) {
            for (const apiDistributionDto of data.apiDistributionDtoList) {
              if (apiDistributionDto.classType === "D") {
                const solution_id =
                  apiDistributionDto.productDto?.productId ?? null;
                if (
                  solution_id &&
                  !repo_metadata.solutions.some((item) => item.solution_id === solution_id)
                ) {
                  const solution: Solution = {
                    release_pkg_id: apiDistributionDto.productVersionId,
                    release_pkg_code: apiDistributionDto.productVersionCode,
                    solution_name:
                      apiDistributionDto.productDto?.productName ?? null,
                    solution_id: solution_id,
                  };
                  repo_metadata.solutions.push(solution);
                }
              }
            }
          }
          setRepoMetadata(repo_metadata);
        } else {
          setRepoMetadata((pre: RepoMetadata | null) => {
            if (pre) {
              return {
                ...pre,
                dc_repo_id: null,
                dc_project_id: null,
                branch_version_id: null,
                branch_version_name: null,
              };
            }
            return pre;
          });
        }
      })
      .catch((error) => {
        console.log(error);
      });
  };

  return (
    <div className="w-full flex flex-col gap-y-4">
      <div>
        <label className="block text-sm font-medium text-black mb-2 dark:text-white">
          {messages.form.repository}
        </label>
        <input
          type="text"
          value={mainRepo}
          onChange={(e) => {
            setMainRepo(e.target.value);
            setMainRepoBranch("");
          }}
          className="input-japanese block w-full px-3 py-2 text-sm rounded-md bg-transparent text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)] border border-[var(--border-color)]"
          placeholder="https://github.com/owner/repo"
          onBlur={onRepoInputBlur}
          onKeyDown={onRepoInputEnter}
        />
      </div>

      {/* Branch selector */}
      <div>
        <label
          htmlFor="branch-select"
          className="block text-sm font-medium text-black mb-2 border-red-50 dark:text-white"
        >
          {messages.form.branch}
        </label>
        <Select
          classNamePrefix={`${theme === 'dark' ? 'dark-react-select' : 'react-select'}`}
          value={{ value: mainRepoBranch, label: mainRepoBranch }}
          options={mainRepoBranches[mainRepo]?.map((branch) => {
            return { value: branch, label: branch };
          })}
          onChange={(newValue) => setMainRepoBranch(newValue?.value ?? "")}
          placeholder={messages.form.selectBranch}
        />
      </div>

      {/* Sub Repository info */}
      <div>
        <label className="block text-sm font-medium text-black mb-2 dark:text-white">
          {messages.form.subRepositories}
        </label>
        <div className="flex flex-col gap-2">
          {subRepos.map((subRepo, idx) => (
            <div key={idx} className="flex gap-2 items-center">
              {/* 子仓库地址输入 */}
              <input
                type="text"
                value={subRepo.url}
                onChange={(e) => {
                  const url = e.target.value;
                  setSubRepos((subs: SubRepoInfo[]) =>
                    subs.map((item, i) =>
                      i === idx ? { ...item, url, branch: "" } : item
                    )
                  );
                }}
                onBlur={() => onSubRepoInputBlur(subRepo.url, idx)}
                onKeyDown={(e) => onSubRepoInputEnter(e, subRepo.url, idx)}
                placeholder="https://github.com/owner/repo"
                className="input-japanese block w-full px-3 py-2 text-sm rounded-md bg-transparent text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)] border border-[var(--border-color)] flex-[3_3_0%] min-w-0"
              />

              {/* 子仓库分支选择 */}
              <Select
                className="flex-[1_1_0%]"
                classNamePrefix={`${theme === 'dark' ? 'dark-react-select' : 'react-select'}`}
                placeholder={messages.form.subRepoBranch}
                value={{ value: subRepo.branch, label: subRepo.branch }}
                options={subRepoBranches[subRepo.url]?.map((branch) => {
                  return { value: branch, label: branch };
                })}
                onChange={(newValue) => {
                  const branch = newValue?.value ?? "";
                  setSubRepos((subs: SubRepoInfo[]) =>
                    subs.map((item, i) =>
                      i === idx ? { ...item, branch } : item
                    )
                  );
                }}
              />

              {/* 删除子仓库按钮 */}
              <button
                type="button"
                onClick={() => removeSubRepo(idx)}
                className="px-2 py-2 text-lg font-medium rounded-md border border-[var(--border-color)] hover:bg-[var(--accent-primary)]/10 transition-colors cursor-pointer"
                aria-label="Remove"
                title={messages.common.remove}
              >
                <AiOutlineMinus />
              </button>
            </div>
          ))}

          {/* 添加子仓库按钮 */}
          <button
            type="button"
            onClick={addSubRepo}
            className="px-3 py-1 text-sm rounded-md border border-[var(--border-color)] text-[var(--accent-primary)] hover:bg-[var(--accent-primary)]/10 transition-colors self-start"
          >
            <div className="flex items-center gap-x-1 cursor-pointer">
              <AiOutlinePlus /> {messages.form.addSubRepository}
            </div>
          </button>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-black mb-2 dark:text-white">
          {messages.components.wikiInfoEditor.tags}
        </label>
        <div className="flex flex-wrap items-center gap-2 p-2 border border-[var(--border-color)] rounded-md bg-transparent min-h-[42px]">
          {selectedTags.map((tag) => (
            <span
              key={tag.id}
              className="flex items-center gap-1 bg-[var(--accent-primary)]/10 text-[var(--accent-primary)] text-sm font-medium px-2.5 py-1 rounded-full border border-[var(--accent-primary)]/20 hover:bg-[var(--accent-primary)]/20 transition-colors"
            >
              <div
                className="w-3 h-3 rounded-full flex-shrink-0"
                style={{ backgroundColor: tag.color }}
              />
              <span className="truncate max-w-[120px]">{tag.name}</span>
              <button
                type="button"
                onClick={() => removeTag(tag.id)}
                className="ml-1 text-[var(--accent-primary)] hover:text-[var(--accent-primary)]/70 transition-colors flex-shrink-0"
                aria-label="Remove tag"
              >
                <FaTimes size={12} />
              </button>
            </span>
          ))}
          <div className="relative" ref={tagDropdownRef}>
            <button
              type="button"
              onClick={toggleTagDropdown}
              ref={addTagButtonRef}
              className="flex items-center gap-2 px-3 py-1.5 text-sm rounded-md border border-[var(--border-color)] text-[var(--foreground)] hover:bg-[var(--accent-primary)]/10 hover:border-[var(--accent-primary)] transition-colors"
              aria-label="Add tag"
            >
              <FaPlus size={12} />
              {messages.tagManagement.addTag}
              <FaChevronDown
                size={12}
                className={`transition-transform ${
                  showTagDropdown ? "rotate-180" : ""
                }`}
              />
            </button>
            {showTagDropdown && (
              <div
                className={`absolute z-50 w-72 bg-[var(--card-bg)] border border-[var(--border-color)] rounded-md shadow-lg overflow-hidden ${
                  dropdownPosition === "top"
                    ? "bottom-full mb-1"
                    : "top-full mt-1"
                } ${dropdownAlignment === "left" ? "left-0" : "right-0"}`}
                style={{
                  maxHeight: showCreateForm ? "400px" : "300px",
                }}
              >
                {!showCreateForm ? (
                  <>
                    <div className="p-3 border-b border-[var(--border-color)]">
                      <div className="relative">
                        <FaSearch
                          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[var(--muted)]"
                          size={14}
                        />
                        <input
                          type="text"
                          value={tagSearchTerm}
                          onChange={(e) => setTagSearchTerm(e.target.value)}
                          className="w-full pl-10 pr-3 py-2 text-sm rounded-md border border-[var(--border-color)] bg-[var(--input-bg)] text-[var(--foreground)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] focus:border-transparent"
                          placeholder="搜索标签..."
                          autoFocus
                        />
                      </div>
                    </div>
                    <div className="max-h-48 overflow-y-auto">
                      {availableTags.length === 0 ? (
                        <div className="px-3 py-4 text-center text-sm text-[var(--muted)]">
                          {tagSearchTerm ? messages.tagManagement.noMatchingTags : messages.tagManagement.noAvailableTags}
                        </div>
                      ) : (
                        availableTags.map((tag) => (
                          <div
                            key={tag.id}
                            className="flex items-center gap-3 px-3 py-2 text-sm cursor-pointer hover:bg-[var(--hover-bg)] transition-colors"
                            onClick={() => addTag(tag)}
                          >
                            <div
                              className="w-4 h-4 rounded-full flex-shrink-0"
                              style={{ backgroundColor: tag.color }}
                            />
                            <span className="text-[var(--foreground)] flex-1">
                              {tag.name}
                            </span>
                            {tag.comments && (
                              <span
                                className="text-xs text-[var(--muted)] truncate max-w-[120px]"
                                title={tag.comments}
                              >
                                {tag.comments}
                              </span>
                            )}
                          </div>
                        ))
                      )}

                      {/* 创建新标签选项 */}
                      <div className="border-t border-[var(--border-color)]">
                        <div
                          className="flex items-center justify-between px-3 py-2 hover:bg-[var(--accent-primary)]/10 transition-colors cursor-pointer"
                          onClick={() => setShowCreateForm(true)}
                        >
                          <div className="flex items-center gap-3">
                            <FaPlus
                              size={12}
                              className="text-[var(--accent-primary)]"
                            />
                            <span className="text-[var(--accent-primary)] font-medium">
                              {messages.tagManagement.createNewTag}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                ) : (
                  /* 创建标签表单 */
                  <div className="h-full overflow-y-auto">
                    <div className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="text-sm font-medium text-[var(--foreground)]">
                          {messages.tagManagement.createNewTag}
                        </h4>
                        <button
                          onClick={() => setShowCreateForm(false)}
                          className="text-[var(--muted)] hover:text-[var(--foreground)]"
                        >
                          <FaTimes size={14} />
                        </button>
                      </div>
                      <div className="space-y-3">
                        {/* 标签名称 */}
                        <div>
                          <label className="block text-xs font-medium text-[var(--muted)] mb-1">
                            {messages.tagManagement.tagName} *
                          </label>
                          <input
                            type="text"
                            value={newTagForm.name}
                            onChange={(e) =>
                              setNewTagForm((prev) => ({
                                ...prev,
                                name: e.target.value,
                              }))
                            }
                            className="w-full px-3 py-2 text-sm bg-[var(--input-bg)] border border-[var(--border-color)] rounded-md text-[var(--foreground)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] focus:border-transparent"
                            placeholder={messages.tagManagement.tagNamePlaceholder}
                            maxLength={60}
                            required
                          />
                        </div>

                        {/* 标签颜色 */}
                        <div className="relative">
                          <label className="block text-xs font-medium text-[var(--muted)] mb-1">
                            {messages.tagManagement.tagColor}
                          </label>
                          <div className="flex items-center gap-3">
                            <button
                              ref={colorButtonRef}
                              onClick={() =>
                                setShowColorPicker(!showColorPicker)
                              }
                              className="w-8 h-8 rounded border border-[var(--border-color)]"
                              style={{ backgroundColor: newTagForm.color }}
                            />
                            {/* <input
                              type="text"
                              disabled={true}
                              value={newTagForm.color}
                              onChange={(e) =>
                                setNewTagForm((prev) => ({
                                  ...prev,
                                  color: e.target.value,
                                }))
                              }
                              className="flex-1 px-3 py-2 text-sm bg-[var(--input-bg)] border border-[var(--border-color)] rounded-md text-[var(--foreground)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] focus:border-transparent"
                              placeholder="#FF0000"
                            /> */}
                          </div>

                          {/* 颜色选择器 */}
                          {showColorPicker && (
                            <div
                              ref={colorPickerRef}
                              className="absolute z-10 mt-2 p-3 bg-[var(--card-bg)] border border-[var(--border-color)] rounded-lg shadow-lg grid grid-cols-6 gap-2"
                            >
                              {PRESET_COLORS.map((color) => (
                                <button
                                  key={color}
                                  onClick={() => {
                                    setNewTagForm((prev) => ({
                                      ...prev,
                                      color,
                                    }));
                                    setShowColorPicker(false);
                                  }}
                                  className="w-6 h-6 rounded border border-[var(--border-color)] hover:scale-110 transition-transform"
                                  style={{ backgroundColor: color }}
                                />
                              ))}
                            </div>
                          )}
                        </div>

                        {/* 标签说明 */}
                        <div>
                          <label className="block text-xs font-medium text-[var(--muted)] mb-1">
                            {messages.tagManagement.tagDescription}
                          </label>
                          <input
                            type="text"
                            value={newTagForm.comments}
                            onChange={(e) =>
                              setNewTagForm((prev) => ({
                                ...prev,
                                comments: e.target.value,
                              }))
                            }
                            className="w-full px-3 py-2 text-sm bg-[var(--input-bg)] border border-[var(--border-color)] rounded-md text-[var(--foreground)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] focus:border-transparent"
                            placeholder={messages.tagManagement.tagDescriptionPlaceholder}
                            maxLength={200}
                          />
                        </div>

                        {/* 操作按钮 */}
                        <div className="flex gap-2 pt-2">
                          <button
                            onClick={() => setShowCreateForm(false)}
                            className="flex-1 px-3 py-2 text-sm border border-[var(--border-color)] text-[var(--foreground)] rounded-md hover:bg-[var(--hover-bg)] transition-colors"
                          >
                            {messages.common.cancel}
                          </button>
                          <button
                            onClick={createNewTag}
                            disabled={isSubmitting || !newTagForm.name.trim()}
                            className="flex-1 px-3 py-2 text-sm bg-[var(--accent-primary)] text-white rounded-md hover:bg-[var(--accent-primary)]/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {isSubmitting ? messages.tagManagement.creating : messages.tagManagement.createTag}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-black mb-2 dark:text-white">
          {messages.components.wikiInfoEditor.productLine}
        </label>
        <AsyncSelect
          classNamePrefix={`${theme === 'dark' ? 'dark-react-select' : 'react-select'}`}
          isClearable
          value={selectedProductLine}
          loadOptions={loadProductLines}
          onChange={onProductLineChange}
          placeholder={messages.components.wikiInfoEditor.productLinePlaceholder}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-black mb-2 dark:text-white">
          {messages.components.wikiInfoEditor.product}
        </label>
        <AsyncSelect
          classNamePrefix={`${theme === 'dark' ? 'dark-react-select' : 'react-select'}`}
          isClearable
          value={selectedProduct}
          loadOptions={loadProducts}
          onChange={onProductChange}
          placeholder={messages.components.wikiInfoEditor.productPlaceholder}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-black mb-2 dark:text-white">
          {messages.components.wikiInfoEditor.productVersion}
        </label>
        <AsyncSelect
          classNamePrefix={`${theme === 'dark' ? 'dark-react-select' : 'react-select'}`}
          isClearable
          value={selectedProductVersion}
          loadOptions={loadProductVersions}
          onChange={onProductVersionChange}
          placeholder={messages.components.wikiInfoEditor.productVersionPlaceholder}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-black mb-2 dark:text-white">
          {messages.components.wikiInfoEditor.solution}
        </label>
        <AsyncSelect
          classNamePrefix={`${theme === 'dark' ? 'dark-react-select' : 'react-select'}`}
          isClearable
          value={selectedSolutions}
          loadOptions={loadSolutions}
          isMulti
          onChange={onSolutionsChange}
          placeholder={messages.components.wikiInfoEditor.solutionPlaceholder}
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-2 text-black dark:text-white">
          {messages.components.wikiInfoEditor.comments}
        </label>
        <textarea
          rows={3}
          maxLength={255}
          className="input-japanese block w-full px-3 py-2 text-sm rounded-md bg-transparent text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)] border border-[var(--border-color)] resize-none"
          placeholder={messages.components.wikiInfoEditor.commentsPlaceholder}
          value={comments}
          onChange={(e) => setComments(e.target.value)}
        />
      </div>
    </div>
  );
};

export default RepoStep;
