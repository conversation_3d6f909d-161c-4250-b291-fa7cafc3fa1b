import React, { useEffect, useState } from 'react'
import { createPortal } from 'react-dom'
import { WikiInfo, WikiJob } from './types'
import { useToast } from '@/contexts/ToastContext'
import { useLanguage } from '@/contexts/LanguageContext'

// 模型选择相关的接口
interface Model {
  id: string
  name: string
  temperature: number
  top_p: number
}

interface ModelConfig {
  provider: string
  default_model: string
  models: Model[]
}

interface ModelSettings {
  provider: string
  model: string
  comprehensive: boolean
  api_key?: string
  model_kwargs: Record<string, unknown>
}

// 模型选择弹窗组件
function ModelSelectionModal({ 
  show, 
  onClose, 
  onConfirm, 
  wikiId, 
  modelConfig,
  loadingModels
}: { 
  show: boolean
  onClose: () => void
  onConfirm: (modelSettings: ModelSettings) => void
  wikiId: string
  modelConfig: ModelConfig | null
  loadingModels: boolean
}) {
  const [provider, setProvider] = useState('whalecloud')
  const [model, setModel] = useState('')
  const [isCustomModel, setIsCustomModel] = useState(false)
  const [customModel, setCustomModel] = useState('')
  const [comprehensive, setComprehensive] = useState(true)
  const [apiKey, setApiKey] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { addToast } = useToast()
  const { messages } = useLanguage()

  // 当弹窗打开时重置状态
  useEffect(() => {
    if (show && modelConfig) {
      setProvider(modelConfig.provider)
      setModel(modelConfig.default_model)
      setIsCustomModel(false)
      setCustomModel('')
      setApiKey('')
      setIsSubmitting(false)
    }
  }, [show, modelConfig])

  useEffect(() => {
    if (modelConfig) {
      setProvider(modelConfig.provider)
      // 当模型配置加载完成时，总是设置默认模型
      setModel(modelConfig.default_model)
    } else {
      // 如果没有模型配置，设置默认值
      setProvider('whalecloud')
      setModel('gemini-2.5-flash')
    }
  }, [modelConfig])

  const handleConfirm = async () => {
    // 验证模型选择
    const selectedModel = isCustomModel ? customModel.trim() : model
    
    if (!selectedModel) {
      addToast({
        type: 'error',
        title: messages.k8s.problemWikisModal.modelRequired,
        message: ''
      });
      return
    }

    // 防止重复提交
    if (isSubmitting) return
    setIsSubmitting(true)

    try {
      // 确保使用正确的模型ID
      const finalModel = isCustomModel ? selectedModel : model

      const modelSettings: ModelSettings = {
        provider,
        model: finalModel,
        comprehensive,
        api_key: apiKey.trim() || undefined,
        model_kwargs: {}
      }
      
      await onConfirm(modelSettings)
    } catch (error) {
      console.error('确认模型设置时出错:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!show) {
    console.log('弹窗组件：show为false，返回null')
    return null
  }

  console.log('弹窗组件：show为true，渲染弹窗')
  return (
    <div 
      className="fixed inset-0 bg-gray-900/70 backdrop-blur-md flex items-center justify-center p-4" 
      style={{ 
        position: 'fixed', 
        top: 0, 
        left: 0, 
        right: 0, 
        bottom: 0, 
        width: '100vw', 
        height: '100vh',
        zIndex: 99999
      }}
    >
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md overflow-hidden border border-cyan-100/30">
        {/* Header */}
        <div className="px-6 py-4 border-b bg-gradient-to-r from-cyan-50 to-gray-50">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-bold text-gray-800 flex items-center gap-2">
              <svg className="w-5 h-5 text-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              {messages.k8s.problemWikisModal.selectGenerationModel}
            </h3>
            <button
              onClick={onClose}
              disabled={isSubmitting}
              className="w-8 h-8 bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 rounded-full flex items-center justify-center transition disabled:opacity-50"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <p className="text-sm text-gray-600 mt-1">Wiki ID: {wikiId}</p>
        </div>

        {/* Body */}
        <div className="p-6 space-y-4">
          {/* Provider Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {messages.k8s.problemWikisModal.modelProvider}
            </label>
            <select
              value={provider}
              onChange={(e) => setProvider(e.target.value)}
              disabled={isSubmitting}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all disabled:opacity-50"
            >
              <option value="whalecloud">WhaleCloud</option>
              <option value="openai">OpenAI</option>
              <option value="anthropic">Anthropic</option>
            </select>
          </div>

          {/* Model Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {messages.k8s.problemWikisModal.modelSelection}
            </label>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <input
                  type="radio"
                  id="preset-model"
                  checked={!isCustomModel}
                  onChange={() => setIsCustomModel(false)}
                  disabled={isSubmitting}
                  className="text-cyan-600 focus:ring-cyan-500 disabled:opacity-50"
                />
                <label htmlFor="preset-model" className="text-sm text-gray-700">{messages.k8s.problemWikisModal.presetModel}</label>
              </div>
              
              {!isCustomModel && (
                <div className="ml-6">
                  {modelConfig?.models && modelConfig.models.length > 0 ? (
                    <select
                      value={model}
                      onChange={(e) => {
                        const selectedValue = e.target.value
                        setModel(selectedValue)
                      }}
                      disabled={isSubmitting}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all disabled:opacity-50"
                    >
                      {modelConfig.models.map((m) => (
                        <option key={m.id} value={m.id}>
                          {m.name}
                        </option>
                      ))}
                    </select>
                  ) : (
                    <div className="text-sm text-gray-500 py-2 px-3 bg-gray-50 rounded-lg border border-gray-200">
                      {loadingModels ? messages.k8s.problemWikisModal.loadingModels : messages.k8s.problemWikisModal.noAvailableModels}
                    </div>
                  )}
                </div>
              )}

              <div className="flex items-center gap-2">
                <input
                  type="radio"
                  id="custom-model"
                  checked={isCustomModel}
                  onChange={() => setIsCustomModel(true)}
                  disabled={isSubmitting}
                  className="text-cyan-600 focus:ring-cyan-500 disabled:opacity-50"
                />
                <label htmlFor="custom-model" className="text-sm text-gray-700">{messages.k8s.problemWikisModal.customModel}</label>
              </div>
              
              {isCustomModel && (
                <input
                  type="text"
                  value={customModel}
                  onChange={(e) => setCustomModel(e.target.value)}
                  placeholder={messages.k8s.problemWikisModal.enterModelName}
                  disabled={isSubmitting}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all ml-6 disabled:opacity-50"
                />
              )}
            </div>
          </div>

          {/* Wiki Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {messages.k8s.problemWikisModal.wikiType}
            </label>
            <div className="grid grid-cols-2 gap-3">
              <button
                type="button"
                onClick={() => setComprehensive(true)}
                disabled={isSubmitting}
                className={`p-3 rounded-lg border text-left transition-all disabled:opacity-50 ${
                  comprehensive
                    ? 'bg-cyan-50 border-cyan-200 text-cyan-700'
                    : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
                }`}
              >
                <div className="font-medium text-sm">{messages.k8s.problemWikisModal.detailedMode}</div>
                <div className="text-xs opacity-70">{messages.k8s.problemWikisModal.detailedModeDescription}</div>
              </button>
              <button
                type="button"
                onClick={() => setComprehensive(false)}
                disabled={isSubmitting}
                className={`p-3 rounded-lg border text-left transition-all disabled:opacity-50 ${
                  !comprehensive
                    ? 'bg-cyan-50 border-cyan-200 text-cyan-700'
                    : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
                }`}
              >
                <div className="font-medium text-sm">{messages.k8s.problemWikisModal.conciseMode}</div>
                <div className="text-xs opacity-70">{messages.k8s.problemWikisModal.conciseModeDescription}</div>
              </button>
            </div>
          </div>

          {/* API Key Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {messages.k8s.problemWikisModal.apiKeyOptional}
            </label>
            <input
              type="password"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder={messages.k8s.problemWikisModal.enterApiKeyNote}
              disabled={isSubmitting}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all disabled:opacity-50"
            />
            <p className="text-xs text-gray-500 mt-1">
              {messages.k8s.problemWikisModal.enterApiKey}
            </p>
          </div>

          {/* 调试信息 */}
          <div className="p-3 bg-gray-50 rounded-lg border border-gray-200">
            <div className="text-xs text-gray-600">
              <div>{messages.k8s.problemWikisModal.currentStatus}:</div>
              <div>{messages.k8s.problemWikisModal.provider}: {provider}</div>
              <div>{messages.k8s.problemWikisModal.model}: {model}</div>
              <div>{messages.k8s.problemWikisModal.customModelValue}: {customModel}</div>
              <div>{messages.k8s.problemWikisModal.isCustom}: {isCustomModel ? messages.common.yes : messages.common.no}</div>
              <div>{messages.k8s.problemWikisModal.comprehensive}: {comprehensive ? messages.k8s.problemWikisModal.detailedMode : messages.k8s.problemWikisModal.conciseMode}</div>
              <div>{messages.k8s.problemWikisModal.apiKeyStatus}: {apiKey ? messages.common.entered : messages.common.notEntered}</div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t bg-gray-50 flex justify-end gap-3">
          <button
            onClick={onClose}
            disabled={isSubmitting}
            className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
          >
            {messages.common.cancel}
          </button>
          <button
            onClick={handleConfirm}
            disabled={!model || (isCustomModel && !customModel.trim()) || isSubmitting}
            className="px-6 py-2 bg-cyan-600 text-white rounded-lg hover:bg-cyan-700 transition-colors disabled:opacity-50 flex items-center gap-2"
          >
            {isSubmitting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                {messages.common.processing}
              </>
            ) : (
              <>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                {messages.k8s.problemWikisModal.confirmGeneration}
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  )
}

export default function WikiInfoManagementTable() {
  const { addToast } = useToast()
  const { messages, language } = useLanguage()
  
  // 状态管理
  const [wikis, setWikis] = useState<WikiInfo[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  
  // 筛选条件
  const [keyword, setKeyword] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [jobStatusFilter, setJobStatusFilter] = useState('')
  const [hasWikiDataFilter, setHasWikiDataFilter] = useState<string>('')
  
  // 展开状态管理
  const [expandedWikis, setExpandedWikis] = useState<Record<string, boolean>>({})
  const [wikiJobMap, setWikiJobMap] = useState<Record<string, { loading: boolean; items: WikiJob[] }>>({})
  
  // 快速刷新状态管理
  const [refreshingWikis, setRefreshingWikis] = useState<Set<string>>(new Set())
  
  // 状态设置相关状态
  const [updatingStatus, setUpdatingStatus] = useState<Set<string>>(new Set())
  
  // 模型选择相关状态
  const [showModelModal, setShowModelModal] = useState(false)
  const [selectedWikiId, setSelectedWikiId] = useState('')
  const [modelConfig, setModelConfig] = useState<ModelConfig | null>(null)
  const [loadingModels, setLoadingModels] = useState(false)
  
  // 分页输入
  const [pageInput, setPageInput] = useState(String(page))
  useEffect(() => { setPageInput(String(page)) }, [page])

  // 计算最大页数
  const maxPage = Math.max(1, Math.ceil((total || 0) / (pageSize || 1)))
  
  // 跳转页面
  const gotoPage = (p: number) => {
    const pageNum = Math.min(Math.max(1, p || 1), maxPage)
    if (pageNum !== page) {
      setPage(pageNum)
      setTimeout(() => loadWikis(), 0)
    }
  }

  // 显示Toast通知
  const showToast = (message: string, type: 'success' | 'error' | 'info') => {
    const toastType = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info'
    const title = type === 'error' ? messages.k8s.toast.error : 
                  type === 'success' ? messages.k8s.toast.success : 
                  messages.k8s.toast.info
    
    addToast({
      type: toastType,
      title: title,
      message: message,
      duration: type === 'error' ? 6000 : 4000
    })
  }

  // 加载Wiki信息
  const loadWikis = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      if (keyword) params.append('keyword', keyword)
      if (statusFilter) params.append('status', statusFilter)
      if (jobStatusFilter) params.append('job_status', jobStatusFilter)
      if (hasWikiDataFilter !== '') {
        params.append('has_wiki_data', hasWikiDataFilter)
      }
      params.append('page', String(page))
      params.append('page_size', String(pageSize))
      
      const response = await fetch(`/api/k8s/wiki/info/list?${params.toString()}`)
      const data = await response.json()
      
      if (data.success) {
        setWikis(data.data.wikis || [])
        setTotal(data.data.total || 0)
      } else {
        showToast(`${messages.k8s.wikiInfoManagementTable.errorLoadingWikis}: ${data.error}`, 'error')
      }
    } catch (error) {
      showToast(`${messages.common.networkError}: ${error instanceof Error ? error.message : messages.common.unknownError}`, 'error')
    } finally {
      setLoading(false)
    }
  }

  // 加载Wiki Jobs
  const loadWikiJobs = async (wikiId: string) => {
    setWikiJobMap(prev => ({ ...prev, [wikiId]: { loading: true, items: prev[wikiId]?.items || [] } }))
    try {
      const response = await fetch(`/api/k8s/wiki/jobs/${wikiId}`)
      const data = await response.json()
      
      if (data.success) {
        const jobs = Array.isArray(data.data)
          ? data.data
          : Array.isArray(data.data?.jobs)
            ? data.data.jobs
            : []
        setWikiJobMap(prev => ({ ...prev, [wikiId]: { loading: false, items: jobs } }))
      } else {
        setWikiJobMap(prev => ({ ...prev, [wikiId]: { loading: false, items: [] } }))
        showToast(`${messages.k8s.wikiInfoManagementTable.errorLoadingWikis}: ${data.error}`, 'error')
      }
    } catch (error) {
      setWikiJobMap(prev => ({ ...prev, [wikiId]: { loading: false, items: [] } }))
      showToast(`${messages.common.networkError}: ${error instanceof Error ? error.message : messages.common.unknownError}`, 'error')
    }
  }

  // 切换展开/收起
  const toggleWikiJobs = (wikiId: string) => {
    setExpandedWikis(prev => ({ ...prev, [wikiId]: !prev[wikiId] }))
    if (!wikiJobMap[wikiId]) {
      loadWikiJobs(wikiId)
    }
  }

  // 删除Wiki Job
  const deleteWikiJob = async (jobId: string, wikiId: string) => {
    if (!confirm(messages.k8s.wikiInfoManagementTable.confirmDelete)) return
    
    try {
      const response = await fetch(`/api/k8s/wiki/jobs/${jobId}`, { method: 'DELETE' })
      const data = await response.json()
      
      if (data.success) {
        showToast(messages.k8s.wikiInfoManagementTable.wikiDeleted, 'success')
        // 重新加载jobs
        await loadWikiJobs(wikiId)
      } else {
        showToast(`${messages.common.deleteFailed}: ${data.error}`, 'error')
      }
    } catch (error) {
      showToast(`${messages.common.networkError}: ${error instanceof Error ? error.message : messages.common.unknownError}`, 'error')
    }
  }

  // 删除Wiki信息
  const deleteWikiInfo = async (wikiId: string) => {
    if (!confirm(messages.k8s.wikiInfoManagementTable.deleteConfirmMessage.replace('{name}', 'Wiki'))) return
    
    try {
      const response = await fetch(`/api/k8s/wiki/info/${wikiId}`, { method: 'DELETE' })
      const data = await response.json()
      
      if (data.success) {
        showToast(messages.k8s.wikiInfoManagementTable.wikiDeleted, 'success')
        // 重新加载wikis
        await loadWikis()
        // 清理展开状态
        setExpandedWikis(prev => {
          const newState = { ...prev }
          delete newState[wikiId]
          return newState
        })
        // 清理jobs缓存
        setWikiJobMap(prev => {
          const newState = { ...prev }
          delete newState[wikiId]
          return newState
        })
      } else {
        showToast(`${messages.common.deleteFailed}: ${data.error}`, 'error')
      }
    } catch (error) {
      showToast(`${messages.common.networkError}: ${error instanceof Error ? error.message : messages.common.unknownError}`, 'error')
    }
  }

  const prepareWikiJob = async (wikiId: string) => {
    try {
      showToast(messages.k8s.wikiInfoManagementTable.preparing || '正在准备...', 'info')
      const response = await fetch(`/api/k8s/wiki/jobs/${wikiId}/prepare`, { method: 'POST' })
      const data = await response.json()
      
      if (data.success) {
        showToast(messages.k8s.wikiInfoManagementTable.prepareStarted || '准备已启动', 'success')
        // 刷新jobs列表
        await loadWikiJobs(wikiId)
      } else {
        showToast(`${messages.common.operationFailed || '操作失败'}: ${data.error}`, 'error')
      }
    } catch (error) {
      showToast(`${messages.common.networkError}: ${error instanceof Error ? error.message : messages.common.unknownError}`, 'error')
    }
  }

  // 获取Job状态颜色
  const getJobStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'processing':
        return 'bg-blue-100 text-blue-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'resume_pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'cancelled':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const normalizeJobType = (jobType?: number | string | null) => {
    if (typeof jobType === 'string') {
      const parsed = Number.parseInt(jobType, 10)
      return Number.isNaN(parsed) ? null : parsed
    }
    return jobType ?? null
  }

  const getJobTypeLabel = (jobType?: number | string | null) => {
    const type = normalizeJobType(jobType)
    switch (type) {
      case 0:
        return messages.k8s.wikiInfoManagementTable.jobTypeGeneration
      case 1:
        return messages.k8s.wikiInfoManagementTable.jobTypeRefresh
      case 2:
        return messages.k8s.wikiInfoManagementTable.jobTypeSync
      default:
        return messages.k8s.wikiInfoManagementTable.jobTypeUnknown
    }
  }

  const getJobTypeStyle = (jobType?: number | string | null) => {
    const type = normalizeJobType(jobType)
    switch (type) {
      case 0:
        return 'bg-purple-100 text-purple-700'
      case 1:
        return 'bg-orange-100 text-orange-700'
      case 2:
        return 'bg-teal-100 text-teal-700'
      default:
        return 'bg-gray-100 text-gray-700'
    }
  }

  // —— 状态样式与图标（苹果风格） ——

  const getStatusStyles = (status: string) => {
    switch (status) {
      case 'completed':
        return {
          pillBg: 'bg-emerald-50',
          pillText: 'text-emerald-700',
          ring: 'ring-emerald-200',
          leftBorder: 'border-emerald-300'
        }
      case 'processing':
        return {
          pillBg: 'bg-cyan-50',
          pillText: 'text-cyan-700',
          ring: 'ring-cyan-200',
          leftBorder: 'border-cyan-300'
        }
      case 'failed':
        return {
          pillBg: 'bg-rose-50',
          pillText: 'text-rose-700',
          ring: 'ring-rose-200',
          leftBorder: 'border-rose-300'
        }
      case 'pending':
        return {
          pillBg: 'bg-amber-50',
          pillText: 'text-amber-700',
          ring: 'ring-amber-200',
          leftBorder: 'border-amber-300'
        }
      case 'cancelled':
        return {
          pillBg: 'bg-slate-50',
          pillText: 'text-slate-700',
          ring: 'ring-slate-200',
          leftBorder: 'border-slate-300'
        }
      default:
        return {
          pillBg: 'bg-gray-50',
          pillText: 'text-gray-700',
          ring: 'ring-gray-200',
          leftBorder: 'border-gray-300'
        }
    }
  }

  // 格式化时间
  const formatTime = (timeStr: string) => {
    if (!timeStr) return '-'
    try {
      return new Date(timeStr).toLocaleString('zh')
    } catch {
      return timeStr
    }
  }

  // 初始加载
  useEffect(() => {
    loadWikis()
  }, [page, pageSize, keyword, statusFilter, jobStatusFilter, hasWikiDataFilter])

  // 加载模型配置
  useEffect(() => {
    if (showModelModal) {
      loadModels()
    }
  }, [showModelModal])

  // 监听弹窗状态变化，确保弹窗能够正确关闭
  useEffect(() => {
    if (!showModelModal) {
      // 弹窗关闭时，确保清理状态
      setSelectedWikiId('')
      setModelConfig(null)
      console.log('弹窗状态变化监听器：弹窗已关闭，清理状态')
    }
  }, [showModelModal])

  // 添加调试信息，监听弹窗状态变化
  useEffect(() => {
    console.log('弹窗状态变化:', { showModelModal, selectedWikiId })
  }, [showModelModal, selectedWikiId])

  const loadModels = async () => {
    setLoadingModels(true)
    try {
      const response = await fetch('/api/k8s/models/config')
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setModelConfig(data.data)
        }
      }
    } catch (error) {
      console.error('Error loading models:', error)
    } finally {
      setLoadingModels(false)
    }
  }

  // 快速刷新功能
  const handleFastRefresh = async (wikiId: string) => {
    // 找到对应的wiki信息
    const wiki = wikis.find(w => w.wiki_id === wikiId)
    if (!wiki) return
    
    // 显示提示信息
    showToast(`${messages.k8s.quickWikiModal.generationStarted} ${wiki.repo_owner}/${wiki.repo_name}...`, 'info')
    
    // 设置选中的wiki ID并显示模型选择弹窗
    setSelectedWikiId(wikiId)
    setShowModelModal(true)
  }

  // 处理模型确认
  const handleModelConfirm = async (modelSettings: ModelSettings) => {
    const wikiId = selectedWikiId
    if (!wikiId) return
    
    // 找到对应的wiki信息
    const wiki = wikis.find(w => w.wiki_id === wikiId)
    if (!wiki) return
    
    console.log('开始调用快速生成接口:', { wikiId, modelSettings })
    console.log('弹窗状态 - 调用前:', { showModelModal, selectedWikiId })
    
    // 添加到生成中状态
    setRefreshingWikis(prev => new Set(prev).add(wikiId))
    
    try {
      const response = await fetch(`/api/k8s/wiki/generate/fast`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          wiki_id: wikiId,
          comprehensive: modelSettings.comprehensive,
          model_settings: modelSettings,
        }),
      })
      
      console.log('接口响应状态:', response.status, response.statusText)
      
      const data = await response.json()
      console.log('接口返回数据:', data)

      if (data.success) {
        console.log('接口调用成功，显示成功消息')
        showToast(messages.k8s.wikiInfoManagementTable.fastGenerationStarted.replace('{jobId}', data.job_id), 'success')
        
        console.log('准备关闭弹窗...')
        // 立即关闭弹窗
        setShowModelModal(false)
        setSelectedWikiId('')
        console.log('弹窗状态已设置为关闭')
        
        // 重新加载wikis
        await loadWikis()
        // 如果当前wiki是展开状态，重新加载jobs
        if (expandedWikis[wikiId]) {
          await loadWikiJobs(wikiId)
        }
        // 延迟一段时间后再次刷新，确保状态更新
        setTimeout(async () => {
          try {
            await loadWikis()
            if (expandedWikis[wikiId]) {
              await loadWikiJobs(wikiId)
            }
          } catch (error) {
            console.warn('自动刷新失败:', error)
          }
        }, 3000)
      } else {
        console.log('接口调用失败，显示错误消息:', data.error)
        showToast(messages.k8s.wikiInfoManagementTable.fastGenerationFailed.replace('{error}', data.error), 'error')
      }
    } catch (error) {
      console.error('网络请求错误:', error)
      showToast(`${messages.common.networkError}: ${error instanceof Error ? error.message : messages.common.unknownError}`, 'error')
    } finally {
      console.log('清理生成中状态')
      // 从生成中状态移除
      setRefreshingWikis(prev => {
        const newSet = new Set(prev)
        newSet.delete(wikiId)
        return newSet
      })
      
      console.log('确保弹窗关闭...')
      // 确保弹窗关闭（即使出错也要关闭）
      setShowModelModal(false)
      setSelectedWikiId('')
      console.log('弹窗状态已设置为关闭（finally块）')
    }
  }

  // 设置Wiki状态
  const setWikiStatus = async (wikiId: string, newStatus: string) => {
    if (!confirm(`${messages.k8s.wikiInfoManagementTable.confirmDelete} ${newStatus}?`)) return
    
    setUpdatingStatus(prev => new Set(prev).add(wikiId))
    
    try {
      const response = await fetch(`/api/k8s/wiki/info/${wikiId}/status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus,
          error_message: newStatus === 'failed' ? messages.k8s.wikiInfoManagementTable.manuallySetToFailed : null
        }),
      })
      
      const data = await response.json()
      
      if (data.success) {
        showToast(`${messages.k8s.wikiInfoManagementTable.wikiUpdated} ${newStatus}`, 'success')
        // 重新加载wikis
        await loadWikis()
      } else {
        showToast(`${messages.k8s.wikiInfoManagementTable.errorUpdatingWiki}: ${data.error}`, 'error')
      }
    } catch (error) {
      showToast(`${messages.common.networkError}: ${error instanceof Error ? error.message : messages.common.unknownError}`, 'error')
    } finally {
      setUpdatingStatus(prev => {
        const newSet = new Set(prev)
        newSet.delete(wikiId)
        return newSet
      })
    }
  }

  return (
    <>
    <div className="bg-white/50 backdrop-blur-2xl rounded-3xl shadow-lg border border-white/40 overflow-hidden">
      {/* 头部搜索和筛选 */}
      <div className="px-6 py-4 border-b border-white/40 flex items-center justify-between flex-wrap gap-3">
        <h2 className="text-base md:text-lg font-semibold text-slate-900 tracking-tight flex items-center gap-3">
          <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14-4H3m16 8H5m14-4H3" />
          </svg>
          {messages.k8s.wikiInfoManagementTable.title}
          <span className="text-xs md:text-sm text-slate-500 ml-2">{messages.k8s.sandboxUserManagementTable.totalRecords.replace('{total}', total.toString())}</span>
        </h2>
        
        {/* 分页控制 */}
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2 bg-white/60 border border-white/40 px-3 py-2 rounded-xl">
            <span className="text-xs text-slate-600">{messages.k8s.sandboxUserManagementTable.perPage}</span>
            <select
              value={pageSize}
              onChange={(e) => { setPageSize(parseInt(e.target.value)); setPage(1); setTimeout(() => loadWikis(), 0) }}
              className="px-2 py-1 bg-transparent text-slate-800 rounded-md focus:outline-none"
            >
              {[10, 20, 50, 100].map(n => <option key={n} value={n}>{n}</option>)}
            </select>
          </div>
          <div className="flex items-center gap-2 bg-white/60 border border-white/40 px-3 py-2 rounded-xl">
            <button
              onClick={() => { if (page > 1) { setPage(page - 1); setTimeout(() => loadWikis(), 0) } }}
              disabled={loading || page <= 1}
              className="px-3 py-1.5 bg-white text-slate-700 rounded-lg hover:bg-slate-50 disabled:opacity-50"
            >{messages.k8s.sandboxUserManagementTable.previousPage}</button>
            <div className="flex items-center gap-2">
              <span className="text-xs text-slate-600">{messages.k8s.sandboxUserManagementTable.pageNumber}</span>
              <input
                value={pageInput}
                onChange={(e) => setPageInput(e.target.value.replace(/[^0-9]/g, ''))}
                onBlur={() => { const p = parseInt(pageInput || '1', 10); gotoPage(isNaN(p) ? 1 : p) }}
                onKeyDown={(e) => { if (e.key === 'Enter') { const p = parseInt(pageInput || '1', 10); gotoPage(isNaN(p) ? 1 : p) } }}
                className="w-14 px-2 py-1.5 bg-white text-center border border-white/50 rounded-lg focus:outline-none"
                inputMode="numeric"
              />
              {language === 'zh' && <span className="text-xs text-slate-600">{messages.k8s.sandboxUserManagementTable.page}</span>}
            </div>
            <button
              onClick={() => { if (page < maxPage) { setPage(page + 1); setTimeout(() => loadWikis(), 0) } }}
              disabled={loading || page >= maxPage}
              className="px-3 py-1.5 bg-white text-slate-700 rounded-lg hover:bg-slate-50 disabled:opacity-50"
            >{messages.k8s.sandboxUserManagementTable.nextPage}</button>
          </div>
        </div>
      </div>

      {/* 搜索和筛选区域 */}
      <div className="px-6 py-4 border-b border-white/40">
        <div className="flex items-end justify-between gap-4 flex-wrap">
          <div className="flex items-center gap-3 flex-wrap">
            <input
              type="text"
              value={keyword}
              onChange={(e) => setKeyword(e.target.value)}
              placeholder={messages.k8s.wikiInfoManagementTable.searchPlaceholder}
              className="w-48 md:w-56 lg:w-64 px-3 py-2 bg-white/5 border border-white/20 rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-400/50 focus:border-transparent transition-all shadow-sm"
            />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 bg-white/5 border border-white/20 rounded-xl text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-400/50 focus:border-transparent transition-all shadow-sm"
            >
              <option value="">{messages.common.allStatus}</option>
              <option value="pending">{messages.common.pending || 'Pending'}</option>
              <option value="processing">{messages.common.processing || 'Processing'}</option>
              <option value="completed">{messages.common.completed || 'Completed'}</option>
              <option value="failed">{messages.common.failed || 'Failed'}</option>
            </select>


            <select
              value={jobStatusFilter}
              onChange={(e) => setJobStatusFilter(e.target.value)}
              className="px-3 py-2 bg-white/5 border border-white/20 rounded-xl text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-400/50 focus:border-transparent transition-all shadow-sm"
              title={messages.k8s.wikiInfoManagementTable.filterByJobStatus}
            >
              <option value="">{messages.common.allJobStatus}</option>
              <option value="running">{messages.common.runningJobs}</option>
              <option value="failed">{messages.common.failedJobs}</option>
              <option value="cancelled">{messages.common.cancelledJobs}</option>
              <option value="pending">{messages.common.pendingJobs}</option>
            </select>
            <select
              value={hasWikiDataFilter}
              onChange={(e) => setHasWikiDataFilter(e.target.value)}
              className="px-3 py-2 bg-white/5 border border-white/20 rounded-xl text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-400/50 focus:border-transparent transition-all shadow-sm"
              title={messages.k8s.wikiInfoManagementTable.filterByWikiData}
            >
              <option value="">{messages.common.allWikiData}</option>
              <option value="true">{messages.common.hasData}</option>
              <option value="false">{messages.common.noData}</option>
            </select>
            <button 
              onClick={() => { setPage(1); loadWikis() }} 
              disabled={loading} 
              className="px-4 py-2.5 bg-gradient-to-r from-indigo-500/80 to-purple-600/80 text-white rounded-xl hover:shadow-lg hover:from-indigo-500 hover:to-purple-600 disabled:opacity-50 flex items-center gap-2 shadow-md transition-all"
            >
              {loading ? <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" /> : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 6h13M8 12h13M8 18h13M3 6h.01M3 12h.01M3 18h.01" /></svg>
              )}
              {messages.common.search}
            </button>
            <button onClick={loadWikis} disabled={loading} className="px-4 py-2.5 bg-white text-slate-700 rounded-xl hover:bg-slate-50 border border-white/60 disabled:opacity-50">{messages.common.refresh}</button>
          </div>
        </div>
      </div>

      {/* 表格内容 */}
      <div className="relative overflow-x-auto">
        <table className="w-full">
          <thead className="bg-white/60">
            <tr>
              <th className="px-5 py-3 text-left text-xs font-semibold text-slate-600 uppercase tracking-wide w-1/4">{messages.k8s.sandboxUserManagementTable.repository}</th>
              <th className="px-5 py-3 text-left text-xs font-semibold text-slate-600 uppercase tracking-wide w-20">{messages.k8s.sandboxUserManagementTable.status}</th>
              <th className="px-5 py-3 text-left text-xs font-semibold text-slate-600 uppercase tracking-wide w-32">{messages.k8s.wikiInfoManagementTable.createdBy}</th>
              <th className="px-5 py-3 text-left text-xs font-semibold text-slate-600 uppercase tracking-wide w-40">{messages.k8s.wikiInfoManagementTable.createdDate}</th>
              <th className="px-5 py-3 text-left text-xs font-semibold text-slate-600 uppercase tracking-wide">{messages.k8s.sandboxUserManagementTable.actions}</th>
            </tr>
          </thead>
          <tbody className="bg-white/70 divide-y divide-slate-100">
            {wikis.map(wiki => {
              const isOpen = !!expandedWikis[wiki.wiki_id]
              const jobState = wikiJobMap[wiki.wiki_id]
              const statusStyles = getStatusStyles(wiki.status)
              return (
                <React.Fragment key={wiki.id}>
                  <tr className={`transition-colors group odd:bg-white/60 even:bg-white/80 hover:bg-white/90`}>
                    <td className={`px-5 py-3 align-middle border-l-4 ${statusStyles.leftBorder}`}>
                      <div className="space-y-1">
                        <div className="text-sm font-medium text-slate-900">
                          {wiki.repo_owner}/{wiki.repo_name}
                        </div>
                        <div className="text-xs text-slate-600">
                          <span className="font-medium">{messages.form.branch}:</span> {wiki.branch}
                        </div>
                        <div className="text-xs text-slate-600">
                          <span className="font-medium">{messages.common.type}:</span> {wiki.repo_type}
                        </div>
                        <div className="text-xs text-slate-600">
                          <span className="font-medium">{messages.form.model}:</span> {wiki.provider}/{wiki.model}
                        </div>
                        <div className="text-xs text-slate-600">
                          <span className="font-medium">{messages.form.wikiLanguage}:</span> {wiki.language}
                        </div>
                        <div className="text-xs text-slate-600">
                          <span className="font-medium">{messages.common.wikiData}:</span> 
                          <span className={`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-semibold ${
                            wiki.has_wiki_data 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {wiki.has_wiki_data ? messages.common.hasData : messages.common.noData}
                          </span>
                        </div>
                      </div>
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap w-20 align-middle">
                      <div className="flex flex-col items-start gap-2">
                        {/* 状态圆点切换（水平一排） */}
                                                 <div className="flex items-center gap-2">
                           {/* 已完成 */}
                           <button
                             onClick={() => setWikiStatus(wiki.wiki_id, 'completed')}
                             disabled={updatingStatus.has(wiki.wiki_id)}
                             aria-pressed={wiki.status === 'completed'}
                             title={messages.common.setToCompleted}
                             className={`w-5 h-5 rounded-full border transition-all ${
                               updatingStatus.has(wiki.wiki_id) ? 'opacity-50 cursor-not-allowed' : ''
                             } ${
                               wiki.status === 'completed'
                                 ? 'bg-gradient-to-r from-emerald-500 to-emerald-600 border-emerald-600 ring-2 ring-emerald-200'
                                 : 'bg-white border-emerald-300 hover:bg-emerald-50'
                             }`}
                           />
                           {/* 处理中 */}
                           <button
                             onClick={() => setWikiStatus(wiki.wiki_id, 'processing')}
                             disabled={updatingStatus.has(wiki.wiki_id)}
                             aria-pressed={wiki.status === 'processing'}
                             title={messages.common.setToProcessing}
                             className={`w-5 h-5 rounded-full border transition-all ${
                               updatingStatus.has(wiki.wiki_id) ? 'opacity-50 cursor-not-allowed' : ''
                             } ${
                               wiki.status === 'processing'
                                 ? 'bg-gradient-to-r from-cyan-500 to-sky-600 border-cyan-600 ring-2 ring-cyan-200'
                                 : 'bg-white border-cyan-300 hover:bg-cyan-50'
                             }`}
                           />
                           {/* 待处理 */}
                           <button
                             onClick={() => setWikiStatus(wiki.wiki_id, 'pending')}
                             disabled={updatingStatus.has(wiki.wiki_id)}
                             aria-pressed={wiki.status === 'pending'}
                             title={messages.common.setToPending}
                             className={`w-5 h-5 rounded-full border transition-all ${
                               updatingStatus.has(wiki.wiki_id) ? 'opacity-50 cursor-not-allowed' : ''
                             } ${
                               wiki.status === 'pending'
                                 ? 'bg-gradient-to-r from-amber-500 to-orange-600 border-amber-600 ring-2 ring-amber-200'
                                 : 'bg-white border-amber-300 hover:bg-amber-50'
                             }`}
                           />
                           {/* 失败 */}
                           <button
                             onClick={() => setWikiStatus(wiki.wiki_id, 'failed')}
                             disabled={updatingStatus.has(wiki.wiki_id)}
                             aria-pressed={wiki.status === 'failed'}
                             title={messages.common.setToFailed}
                             className={`w-5 h-5 rounded-full border transition-all ${
                               updatingStatus.has(wiki.wiki_id) ? 'opacity-50 cursor-not-allowed' : ''
                             } ${
                               wiki.status === 'failed'
                                 ? 'bg-gradient-to-r from-rose-500 to-pink-600 border-rose-600 ring-2 ring-rose-200'
                                 : 'bg-white border-rose-300 hover:bg-rose-50'
                             }`}
                           />
                         </div>
                        {wiki.error_message && (
                          <div className="flex items-center gap-1 text-xs text-rose-600">
                            <svg className="w-3.5 h-3.5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.721-1.36 3.486 0l6.518 11.591c.75 1.335-.213 3.01-1.743 3.01H3.482c-1.53 0-2.493-1.675-1.743-3.01L8.257 3.1zM11 14a1 1 0 10-2 0 1 1 0 002 0zm-1-2a1 1 0 01-1-1V8a1 1 0 112 0v3a1 1 0 01-1 1z" clipRule="evenodd" />
                            </svg>
                            <span className="text-slate-500" title={wiki.error_message}>{messages.common.errorMessage}</span>
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-5 py-3 whitespace-nowrap text-sm text-slate-800 align-middle">
                      {wiki.user_name || wiki.user_code || wiki.created_by || '-'}
                    </td>
                    <td className="px-5 py-3 whitespace-nowrap text-sm text-slate-800 align-middle">
                      {formatTime(wiki.created_time)}
                    </td>
                    <td className="px-5 py-3 whitespace-nowrap text-sm align-middle">
                      <div className="flex flex-wrap gap-2">
                                                  <button 
                            onClick={() => toggleWikiJobs(wiki.wiki_id)} 
                            className={`px-3 py-1.5 rounded-md border ${isOpen ? 'bg-indigo-600 text-white border-indigo-600' : 'bg-white text-slate-700 border-slate-200 hover:bg-slate-50'} shadow-sm`}
                          >
                          {isOpen ? messages.k8s.sandboxUserManagementTable.collapseSandbox : messages.k8s.sandboxUserManagementTable.viewSandbox}
                        </button>
                        <div className="flex items-center gap-2">
                          <button 
                            onClick={() => handleFastRefresh(wiki.wiki_id)} 
                            disabled={refreshingWikis.has(wiki.wiki_id)}
                            title={messages.k8s.wikiInfoManagementTable.fastRefreshTooltip.replace('{repoOwner}', wiki.repo_owner).replace('{repoName}', wiki.repo_name)}
                            className={`px-3 py-1.5 rounded-lg border shadow-md transition-all ${
                              refreshingWikis.has(wiki.wiki_id)
                                ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                                : wiki.status === 'processing'
                                ? 'bg-gradient-to-r from-indigo-500/80 to-purple-600/80 text-white hover:from-indigo-500 hover:to-purple-600 border-transparent'
                                : wiki.status === 'completed'
                                ? 'bg-gradient-to-r from-indigo-500/80 to-purple-600/80 text-white hover:from-indigo-500 hover:to-purple-600 border-transparent'
                                : wiki.status === 'failed'
                                ? 'bg-gradient-to-r from-indigo-500/80 to-purple-600/80 text-white hover:from-indigo-500 hover:to-purple-600 border-transparent'
                                : 'bg-gradient-to-r from-indigo-500/80 to-purple-600/80 text-white hover:from-indigo-500 hover:to-purple-600 border-transparent'
                            }`}
                          >
                            {refreshingWikis.has(wiki.wiki_id) ? (
                              <div className="flex items-center gap-2">
                                <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
                                {messages.k8s.problemWikisModal.refreshing}
                              </div>
                            ) : (
                              <div className="flex items-center gap-2">
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                                {wiki.status === 'processing' ? messages.k8s.problemWikisModal.refresh : 
                                 wiki.status === 'completed' ? messages.k8s.problemWikisModal.refresh : 
                                 wiki.status === 'failed' ? messages.k8s.problemWikisModal.refresh : messages.k8s.problemWikisModal.fastGenerate}
                              </div>
                            )}
                          </button>
                          {/* 生成进度指示器 */}
                          {refreshingWikis.has(wiki.wiki_id) && (
                            <div className="flex items-center gap-1 text-xs">
                              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                              <span className="text-blue-600">{messages.k8s.quickWikiModal.creating}</span>
                            </div>
                          )}
                        </div>
                                                  
                        <button 
                          onClick={() => deleteWikiInfo(wiki.wiki_id)} 
                                                      className="px-3 py-1.5 rounded-md bg-white text-red-700 hover:bg-red-50 border border-red-200 shadow-sm"
                        >
                          {messages.k8s.wikiInfoManagementTable.deleteWiki}
                        </button>
                        <button 
                          onClick={() => prepareWikiJob(wiki.wiki_id)} 
                          className="px-3 py-1.5 rounded-md bg-white text-blue-700 hover:bg-blue-50 border border-blue-200 shadow-sm"
                        >
                          {messages.k8s.wikiInfoManagementTable.prepareWiki}
                        </button>
                      </div>
                    </td>
                  </tr>
                  
                  {/* 展开的Jobs行 */}
                  {isOpen && (
                    <tr>
                      <td colSpan={5} className="px-6 pb-6">
                        <div className="mt-2 rounded-2xl bg-white/50 backdrop-blur-xl border border-white/40 p-4 shadow ring-1 ring-white/30">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 rounded-xl bg-indigo-100 text-indigo-700 flex items-center justify-center shadow">
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14-4H3m16 8H5m14-4H3" /></svg>
                              </div>
                              <h4 className="text-sm font-bold text-indigo-900">{wiki.repo_owner}/{wiki.repo_name} {messages.k8s.sandboxUserManagementTable.sandboxList}</h4>
                            </div>
                            <div className="flex items-center gap-2">
                              <button onClick={() => loadWikiJobs(wiki.wiki_id)} className="text-xs px-3 py-1 rounded-lg bg-indigo-600 text-white hover:bg-indigo-700">{messages.common.refresh}</button>
                            </div>
                          </div>
                          
                          <div className="bg-white rounded-xl shadow border border-slate-200 overflow-hidden">
                            {jobState?.loading ? (
                              <div className="py-10 text-center text-gray-500">{messages.common.loading}</div>
                            ) : (jobState?.items?.length || 0) === 0 ? (
                              <div className="py-10 text-center text-gray-500">{messages.k8s.sandboxUserManagementTable.noSandbox}</div>
                            ) : (
                              <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-slate-50">
                                      <tr>
                                        <th className="px-4 py-2 text-left text-xs font-semibold text-slate-600 uppercase">Job ID</th>
                                        <th className="px-4 py-2 text-left text-xs font-semibold text-slate-600 uppercase">{messages.common.type}</th>
                                        <th className="px-4 py-2 text-left text-xs font-semibold text-slate-600 uppercase">{messages.k8s.sandboxUserManagementTable.status}</th>
                                        <th className="px-4 py-2 text-left text-xs font-semibold text-slate-600 uppercase">{messages.common.stage}</th>
                                        <th className="px-4 py-2 text-left text-xs font-semibold text-slate-600 uppercase">{messages.common.progress}</th>
                                        <th className="px-4 py-2 text-left text-xs font-semibold text-slate-600 uppercase">{messages.k8s.wikiInfoManagementTable.createdDate}</th>
                                        <th className="px-4 py-2 text-left text-xs font-semibold text-slate-600 uppercase">{messages.k8s.sandboxUserManagementTable.actions}</th>
                                      </tr>
                                    </thead>
                                  <tbody className="bg-white/70 divide-y divide-slate-100">
                                    {jobState?.items?.map((job) => (
                                      <tr key={job.id} className="hover:bg-white/90 transition-colors">
                                        <td className="px-4 py-2 text-xs text-slate-800 font-mono">
                                          {job.id.substring(0, 8)}...
                                        </td>
                                        <td className="px-4 py-2 text-xs">
                                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium ${getJobTypeStyle(job.job_type)}`}>
                                            {getJobTypeLabel(job.job_type)}
                                          </span>
                                        </td>
                                        <td className="px-4 py-2 text-xs">
                                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium ${getJobStatusColor(job.status)}`}>
                                            {job.status}
                                          </span>
                                        </td>
                                        <td className="px-4 py-2 text-xs text-slate-800">
                                          <div className="space-y-1">
                                            <div className="text-xs">{job.stage || '-'}</div>
                                            {job.stage_message && (
                                              <div className="text-[11px] text-slate-600">{job.stage_message}</div>
                                            )}
                                          </div>
                                        </td>
                                        <td className="px-4 py-2 text-xs text-slate-800">
                                          <div className="space-y-1">
                                            <div className="flex items-center gap-2">
                                              <span>{messages.common.overall}: {job.progress}%</span>
                                              {job.stage_progress > 0 && (
                                                <span>{messages.common.stage}: {job.stage_progress}%</span>
                                              )}
                                            </div>
                                            {job.total_files !== undefined && job.total_files !== null && job.processed_files !== undefined && job.processed_files !== null && (
                                              <div className="text-[11px] text-slate-600">
                                                {messages.common.files}: {job.processed_files}/{job.total_files}
                                              </div>
                                            )}
                                          </div>
                                        </td>
                                        <td className="px-4 py-2 text-xs text-slate-800">
                                          {formatTime(job.created_time)}
                                        </td>
                                        <td className="px-4 py-2 text-xs">
                                          <button 
                                            onClick={() => deleteWikiJob(job.id, wiki.wiki_id)} 
                                            className="px-2.5 py-1 rounded-md bg-white text-red-700 hover:bg-red-50 border border-red-200 shadow-sm text-xs"
                                          >
                                            {messages.common.delete}
                                          </button>
                                        </td>
                                      </tr>
                                    ))}
                                  </tbody>
                                </table>
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              )
            })}
            
            {wikis.length === 0 && (
              <tr><td colSpan={5} className="px-6 py-10 text-center text-gray-500">{messages.k8s.sandboxUserManagementTable.noData}</td></tr>
            )}
          </tbody>
        </table>
      </div>
    </div>

    {/* 模型选择弹窗 */}
    {createPortal(
      <ModelSelectionModal
        key={`modal-${showModelModal}-${selectedWikiId}`}
        show={showModelModal}
        onClose={() => {
          console.log('弹窗onClose回调被调用')
          setShowModelModal(false)
          setSelectedWikiId('')
          console.log('弹窗状态已设置为关闭（onClose回调）')
        }}
        onConfirm={handleModelConfirm}
        wikiId={selectedWikiId}
        modelConfig={modelConfig}
        loadingModels={loadingModels}
      />,
      document.body
    )}
  </>
  )
} 
