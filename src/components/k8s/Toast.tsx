import React from 'react'
import { ToastState } from './types'

interface ToastProps {
  toast: ToastState
  onClose: () => void
}

export function Toast({ toast, onClose }: ToastProps) {
  if (!toast.show) return null
  const accent = toast.type === 'success' ? 'ring-emerald-300/40 text-emerald-900' : toast.type === 'error' ? 'ring-rose-300/40 text-rose-900' : 'ring-cyan-300/40 text-cyan-900'
  const icon = toast.type === 'success' ? (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" /></svg>
  ) : toast.type === 'error' ? (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" /></svg>
  ) : (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
  )
  return (
    <div className={`fixed top-6 right-6 z-50 p-4 rounded-2xl shadow-xl max-w-sm border border-white/30 ring-1 ${accent} bg-white/60 backdrop-blur-xl`}> 
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="w-6 h-6 rounded-full bg-white/70 flex items-center justify-center ring-1 ring-white/60">
            {icon}
          </div>
          <span className="text-sm font-medium text-slate-900">{toast.message}</span>
        </div>
        <button onClick={onClose} className="ml-3 text-lg font-bold text-slate-500 hover:text-slate-700 transition-colors">
          ×
        </button>
      </div>
    </div>
  )
}

export default Toast 