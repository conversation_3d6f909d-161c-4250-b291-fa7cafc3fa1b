export interface PodInfo {
  name: string
  namespace: string
  status: string
  pod_ip: string
  host_ip: string
  node_name: string
  creation_time: string
  labels: Record<string, string>
  annotations: Record<string, string>
  // 容器列表，提供容器名称与镜像，便于跳转控制台
  containers?: Array<{
    name: string
    image?: string
  }>
  container_statuses?: Array<{
    name: string
    ready: boolean
    restart_count: number
    state: string
    container_id?: string
  }>
  resources?: {
    containers: Array<{
      name: string
      requests: {
        cpu: string
        memory: string
      }
      limits: {
        cpu: string
        memory: string
      }
    }>
    total_requests: {
      cpu: string
      memory: string
    }
    total_limits: {
      cpu: string
      memory: string
    }
  }
  metrics?: {
    pod_name: string
    timestamp: string
    window: string
    containers: Array<{
      name: string
      cpu_usage: number
      cpu_usage_str: string
      memory_usage: number
      memory_usage_str: string
    }>
    total_cpu_usage: number
    total_memory_usage: number
    total_cpu_usage_m: number
    total_memory_usage_kib: number
  }
}

export interface JobInfo {
  name: string
  namespace: string
  user_code: string
  git_url: string
  branch: string
  creation_time: string
  last_access_time?: string
  status: string
  labels: Record<string, string>
  annotations: Record<string, string>
  mounts?: Array<{
    volume_name: string
    container_path: string
    host_path?: string
    read_only?: boolean
  }>
  pods?: PodInfo[]
  metrics?: {
    job_name: string
    pods: Array<{
      pod_name: string
      timestamp: string
      window: string
      containers: Array<{
        name: string
        cpu_usage: number
        cpu_usage_str: string
        memory_usage: number
        memory_usage_str: string
      }>
      total_cpu_usage: number
      total_memory_usage: number
    }>
    total_cpu_usage: number
    total_memory_usage: number
    pod_count: number
  }
  user_name?: string
}

export interface SandboxStatus {
  status: string
  status_description?: string
  job_name: string
  message: string
  pod_ip?: string
  api_response?: string
  timestamp: string
  pod_status?: string[]
  container_statuses?: Array<{
    name: string
    ready: boolean
    restart_count: number
    state: string
  }>
}

// Wiki信息接口
export interface WikiInfo {
  id: number
  wiki_id: string
  repo_url: string
  branch: string
  repo_owner: string
  repo_name: string
  repo_type: string
  provider: string
  model: string
  language: string
  comprehensive: boolean
  status: string
  error_message?: string
  created_time: string
  updated_time: string
  created_by: number
  updated_by: number
  user_code?: string
  user_name?: string
  has_wiki_data?: boolean
}

// Wiki Job接口
export interface WikiJob {
  id: string
  status: string
  job_type?: number | string | null
  stage?: string
  stage_progress: number
  stage_message?: string
  progress: number
  total_files?: number
  processed_files?: number
  error_message?: string
  repo_url: string
  branch: string
  language: string
  comprehensive: boolean
  created_time: string
  updated_time: string
  created_by?: number
  updated_by?: number
}

// 沙盒列表项
export interface SandboxListItem {
  name?: string
  git_url?: string
  branch?: string
  status?: string
  detailed_status?: string
  pod_ip?: string
  annotations?: Record<string, string>
}

// 用户配额管理
export interface UserQuotaUser {
  id: number
  user_code: string
  user_name: string
  sandbox_quota?: number | null
  effective_quota?: number | null
}

export type ToastType = 'success' | 'error' | 'info'

export interface ToastState {
  show: boolean
  message: string
  type: ToastType
} 
