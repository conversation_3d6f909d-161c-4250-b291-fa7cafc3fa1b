import React, { useState } from 'react'
import { JobInfo, SandboxStatus } from './types'
import { extractRepoInfo, formatTime, getStatusStyle } from './utils'
import { getSandboxStatusDescription, getSandboxStatusStyle } from './status'
import { useLanguage } from '@/contexts/LanguageContext'
import { useToast } from '@/contexts/ToastContext'
import { copyToClipboard } from '@/utils/clipboard'

interface JobsTableProps {
  jobs: JobInfo[]
  loading: boolean
  jobsRemaining: number | null
  jobsPageSize: number
  jobsPrevTokens: string[]
  jobsNextContinueToken: string | null
  jobsPageInput: string
  setJobsPageSize: (n: number) => void
  goPrevJobsPage: () => void
  goNextJobsPage: () => void
  gotoPage: (page: number) => void
  setJobsPageInput: (v: string) => void
  onViewDetails: (jobName: string) => void
  onDelete: (jobName: string) => void
  getSandboxStatus: (userCode: string, gitUrl: string, branch: string, jobName?: string) => SandboxStatus | null
  querySandboxStatus: (userCode: string, gitUrl: string, branch: string, job_name: string) => void
  statusLoading: Record<string, boolean>
  appsView?: boolean
  // Kubernetes命名空间
  k8sNamespace: string
  // Kubernetes主控地址
  k8sMasterUrl: string
  // 研发云门户基础地址
  portalBaseUrl: string
  // 是否展示容器登录按钮
  enableContainerPortalButton?: boolean
  // 是否展示跳板机按钮
  enableJumpServerButton?: boolean
  // 跳板机地址
  jumpServerUrl?: string
}

export function JobsTable(props: JobsTableProps) {
  const { messages: t, language } = useLanguage();
  const {addToast} = useToast();
  const [copiedJobName, setCopiedJobName] = useState<string | null>(null);
  const {
    jobs,
    loading,
    jobsRemaining,
    jobsPageSize,
    jobsPrevTokens,
    jobsNextContinueToken,
    jobsPageInput,
    setJobsPageSize,
    goPrevJobsPage,
    goNextJobsPage,
    gotoPage,
    setJobsPageInput,
    onViewDetails,
    onDelete,
    getSandboxStatus,
    querySandboxStatus,
    statusLoading,
    appsView,
    k8sNamespace,
    k8sMasterUrl,
    portalBaseUrl,
    enableContainerPortalButton = false,
    enableJumpServerButton = false,
    jumpServerUrl
  } = props

  // 生成容器跳转链接并打开新窗口
  const handleJumpToContainer = (job: JobInfo) => {
    const jumpLabel = t.k8s.jobsTable.loginContainer || t.k8s.jobsTable.jumpServer
    if (!enableContainerPortalButton) {
      addToast({
        title: jumpLabel,
        message: t.k8s.jobsTable.portalConfigMissing || '未配置研发云门户或K8s参数',
        type: 'error'
      })
      return
    }
    // 校验门户与K8s配置是否齐全
    if (!portalBaseUrl || !k8sNamespace || !k8sMasterUrl) {
      addToast({
        title: jumpLabel,
        message: t.k8s.jobsTable.portalConfigMissing || '未配置研发云门户或K8s参数',
        type: 'error'
      })
      return
    }

    const targetPod = job.pods?.find((pod) => pod.pod_ip) || job.pods?.[0]
    const containerStatuses = targetPod?.container_statuses || []
    const targetContainerStatus = containerStatuses.find((status) => status.container_id) || containerStatuses[0]
    const targetContainer = targetPod?.containers?.find((container) => container.name === targetContainerStatus?.name) || targetPod?.containers?.[0]
    const containerName = targetContainerStatus?.name || targetContainer?.name
    const containerId = targetContainerStatus?.container_id
    const podName = targetPod?.name
    const ip = targetPod?.pod_ip || targetPod?.host_ip

    if (!targetPod || !containerName || !containerId || !podName || !ip) {
      addToast({
        title: jumpLabel,
        message: t.k8s.jobsTable.portalDataMissing || '缺少容器或Pod信息，无法跳转',
        type: 'error'
      })
      return
    }

    const sanitizedBase = portalBaseUrl.endsWith('/') ? portalBaseUrl : `${portalBaseUrl}/`
    const queryPairs: Array<[string, string]> = [
      ['applicationName', job.name || 'sandbox'],
      ['ip', ip],
      ['containerName', containerName],
      ['containerId', containerId],
      ['podName', podName],
      ['jobName', job.name],
      ['privileger', 'DEEP_WIKI'],
      ['k8sMasterUrl', k8sMasterUrl],
      ['k8sNamespace', k8sNamespace],
      ['applicationType', 'STATELESS'],
      ['concise', 'true'],
      ['url', 'zcm-tool/modules/newconsole/views/newConsole'],
      ['channel', 'k8s'],
      ['cmd', 'sudo -E -u wct env HOME=/home/<USER>"cd /data/workspace && wct-cli"']
    ]

    const buildParam = (key: string, value: string) => {
      const normalized = value ?? ''
      if (key === 'k8sMasterUrl' || key === 'url') {
        return `${key}=${normalized}`
      }
      return `${key}=${encodeURIComponent(normalized)}`
    }

    const queryString = queryPairs
      .filter(([, value]) => value !== undefined && value !== null)
      .map(([key, value]) => buildParam(key, value))
      .join('&')

    window.open(`${sanitizedBase}?${queryString}`, '_blank')
  }

  const handleJumpServer = () => {
    if (!enableJumpServerButton || !jumpServerUrl) {
      addToast({
        title: t.k8s.jobsTable.jumpServer,
        message: t.k8s.jobsTable.portalConfigMissing || '未配置跳板机地址',
        type: 'error'
      })
      return
    }
    window.open(jumpServerUrl, '_blank')
  }

  return (
    <div className="bg-white/50 backdrop-blur-2xl rounded-3xl shadow-lg border border-white/40 overflow-hidden">
      <div className="px-6 py-4 border-b border-white/40 flex items-center justify-between flex-wrap gap-3">
        <h2 className="text-base md:text-lg font-semibold text-slate-900 tracking-tight flex items-center gap-3">
          <svg className="w-6 h-6 text-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14-4H3m16 8H5m14-4H3" />
          </svg>
          {t.k8s.jobsTable.jobList}
          <span className="text-xs md:text-sm text-slate-500 ml-2">{t.k8s.jobsTable.currentPage} {jobs.length}{jobsRemaining !== null && `｜${t.k8s.jobsTable.remainingJobs} ${jobsRemaining}`}</span>
        </h2>
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2 bg-white/60 border border-white/40 px-3 py-2 rounded-xl">
            <span className="text-xs text-slate-600">{t.k8s.jobsTable.perPage}</span>
            <select
              value={jobsPageSize}
              onChange={(e) => { const size = parseInt(e.target.value); setJobsPageSize(size) }}
              className="px-2 py-1 bg-transparent text-slate-800 rounded-md focus:outline-none"
            >
              {[10,20,50,100].map(n => <option key={n} value={n}>{n}</option>)}
            </select>
          </div>
          <div className="flex items-center gap-2 bg-white/60 border border-white/40 px-3 py-2 rounded-xl">
            <button onClick={goPrevJobsPage} disabled={loading || jobsPrevTokens.length === 0} className="px-3 py-1.5 bg-white text-slate-700 rounded-lg hover:bg-slate-50 disabled:opacity-50">{t.k8s.jobsTable.previousPage}</button>
            <div className="flex items-center gap-2">
                              <span className="text-xs text-slate-600">{t.k8s.jobsTable.page}</span>
              <input
                value={jobsPageInput}
                onChange={(e) => setJobsPageInput(e.target.value.replace(/[^0-9]/g, ''))}
                onBlur={() => { const p = parseInt(jobsPageInput || '1', 10); gotoPage(isNaN(p) ? 1 : p) }}
                onKeyDown={(e) => { if (e.key === 'Enter') { const p = parseInt(jobsPageInput || '1', 10); gotoPage(isNaN(p) ? 1 : p) } }}
                className="w-14 px-2 py-1.5 bg-white text-center border border-white/50 rounded-lg focus:outline-none"
                inputMode="numeric"
              />
                              {language === 'zh' && <span className="text-xs text-slate-600">{t.k8s.jobsTable.page}</span>}
            </div>
            <button onClick={goNextJobsPage} disabled={loading || !jobsNextContinueToken} className="px-3 py-1.5 bg-white text-slate-700 rounded-lg hover:bg-slate-50 disabled:opacity-50">{t.k8s.jobsTable.nextPage}</button>
          </div>
        </div>
      </div>
      {loading && jobs.length === 0 ? (
        <div className="p-16 text-center">
          <div className="w-12 h-12 border-4 border-cyan-600 border-t-transparent rounded-full animate-spin mx-auto mb-6" />
          <p className="text-slate-500 text-base">{t.k8s.jobsTable.loading}</p>
        </div>
      ) : jobs.length === 0 ? (
        <div className="p-16 text-center">
          <svg className="w-16 h-16 text-slate-300 mx-auto mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
          </svg>
          <p className="text-slate-600 text-base font-medium">{t.k8s.jobsTable.noJobsFound}</p>
          <p className="text-slate-400 text-sm mt-1">{t.k8s.jobsTable.tryCreateSandbox}</p>
        </div>
      ) : (
        <div className="relative overflow-x-auto">
          <table className="w-full">
            <thead className="bg-white/60">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">{t.k8s.jobsTable.nameAndNamespace}</th>
                {!appsView && (
                  <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">{t.k8s.jobsTable.userCode}</th>
                )}
                {!appsView && (
                  <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">{t.k8s.jobsTable.repositoryInfo}</th>
                )}
                {!appsView && (
                  <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">{t.k8s.jobsTable.branch}</th>
                )}
                <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">{t.k8s.jobsTable.jobStatus}</th>
                <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">{t.k8s.jobsTable.sandboxStatus}</th>
                <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">{t.k8s.jobsTable.podIp}</th>
                <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">{t.k8s.jobsTable.creationTime}</th>
                <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider sticky right-0 z-10 bg-white/60 min-w-[200px]">{t.k8s.jobsTable.actions}</th>
              </tr>
            </thead>
            <tbody className="bg-white/70 divide-y divide-slate-100">
              {jobs.map((job) => {
                const repoInfo = extractRepoInfo(job.git_url)
                return (
                  <tr key={job.name} className="hover:bg-white/90 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        <div>
                          <div className="text-sm font-semibold text-slate-900">{job.name}</div>
                          <div className="text-xs text-slate-500">{job.namespace}</div>
                        </div>
                        <button
                          onClick={async () => {
                            try {
                              const success = await copyToClipboard(job.name);
                              if (success) {
                                setCopiedJobName(job.name);
                                addToast({
                                  title: t.k8s.jobsTable.copied,
                                  message: t.k8s.jobsTable.copiedDescription,
                                  type: 'success'
                                });
                                setTimeout(() => setCopiedJobName(null), 2000);
                              } else {
                                addToast({
                                  title: t.k8s.jobsTable.copyFailed || 'Copy Failed',
                                  message: t.k8s.jobsTable.copyFailedDescription || 'Failed to copy to clipboard',
                                  type: 'error'
                                });
                              }
                            } catch (err) {
                              console.error('Failed to copy job name:', err);
                              addToast({
                                title: t.k8s.jobsTable.copyFailed || 'Copy Failed',
                                message: t.k8s.jobsTable.copyFailedDescription || 'An error occurred while copying',
                                type: 'error'
                              });
                            }
                          }}
                          className={`mb-4 rounded transition-colors relative ${
                            copiedJobName === job.name 
                              ? 'text-green-600 hover:text-green-700 hover:bg-green-50' 
                              : 'text-slate-400 hover:text-slate-600 hover:bg-slate-100'
                          }`}
                          title={copiedJobName === job.name ? t.k8s.jobsTable.copied : t.k8s.jobsTable.copyJobName}
                        >
                          {copiedJobName === job.name ? (
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          ) : (
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                          )}
                        </button>
                      </div>
                    </td>
                    {!appsView && (
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm font-semibold text-slate-900">
                          {(() => {
                            const name = job.user_name || job.annotations?.['user.name'] || ''
                            const code = job.user_code || '-'
                            return name ? `${name}(${code})` : code
                          })()}
                        </span>
                      </td>
                    )}
                    {!appsView && (
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm">
                          <div className="font-semibold text-slate-900">{repoInfo.owner}/{repoInfo.repo}</div>
                          <div className="text-xs text-slate-500 truncate max-w-xs" title={job.git_url}>{job.git_url}</div>
                        </div>
                      </td>
                    )}
                    {!appsView && (
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm font-medium text-slate-900">{job.branch}</span>
                      </td>
                    )}
                    <td className="px-6 py-4 whitespace-nowrap"><span className={getStatusStyle(job.status)}>{job.status}</span></td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {(() => {
                        const sandboxStatus = getSandboxStatus(job.user_code, job.git_url, job.branch, job.name || '')
                        const statusKey = job.name || `${job.user_code}-${job.git_url}-${job.branch}`
                        const isLoading = statusLoading[statusKey]
                        return (
                          <div className="space-y-2">
                            {sandboxStatus ? (
                              <div className="flex items-center gap-2">
                                <span className={getSandboxStatusStyle(sandboxStatus.status)}>
                                  {getSandboxStatusDescription(sandboxStatus.status, language)}
                                </span>
                                <button
                                  onClick={() => querySandboxStatus(job.user_code, job.git_url, job.branch, job.name)}
                                  disabled={isLoading}
                                  className="text-xs text-cyan-700 hover:text-cyan-900 hover:underline transition-colors disabled:opacity-50"
                                >
                                  {isLoading ? t.k8s.jobsTable.querying : t.k8s.jobsTable.refresh}
                                </button>
                              </div>
                            ) : (
                              <button
                                onClick={() => querySandboxStatus(job.user_code, job.git_url, job.branch, job.name)}
                                disabled={isLoading}
                                className="text-xs text-cyan-700 hover:text-cyan-900 hover:underline transition-colors disabled:opacity-50"
                              >
                                {isLoading ? t.k8s.jobsTable.querying : t.k8s.jobsTable.queryStatus}
              </button>
                            )}
                            {sandboxStatus && (
                              <div className="text-xs text-slate-500 max-w-xs truncate" title={sandboxStatus.message}>
                                {sandboxStatus.message}
                              </div>
                            )}
                          </div>
                        )
                      })()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {job.pods && job.pods.length > 0 ? (
                        <div className="space-y-1">
                          {job.pods.map((pod, index) => (
                            <div key={index} className="text-xs font-mono bg-slate-50 text-slate-700 px-2 py-1 rounded-md">
                              {pod.pod_ip || '-'}
                            </div>
                          ))}
                        </div>
                      ) : <span className="text-sm text-slate-500">-</span>}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">{formatTime(job.creation_time)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium sticky right-0 bg-white/70 backdrop-blur-sm z-10 min-w-[220px] border-l border-white/50">
                      <div className="flex items-center gap-4">
                        <button onClick={() => onViewDetails(job.name)} className="text-cyan-700 hover:text-cyan-900 hover:underline transition-colors">{t.k8s.jobsTable.details}</button>
                        <button onClick={() => onDelete(job.name)} className="text-rose-700 hover:text-rose-900 hover:underline transition-colors" disabled={loading}>{t.k8s.jobsTable.delete}</button>
                        {enableContainerPortalButton && (
                          <button
                            onClick={() => handleJumpToContainer(job)}
                            className="text-cyan-700 hover:text-cyan-900 hover:underline transition-colors"
                          >
                            {t.k8s.jobsTable.loginContainer || t.k8s.jobsTable.jumpServer}
                          </button>
                        )}
                        {enableJumpServerButton && (
                          <button
                            onClick={handleJumpServer}
                            className="text-indigo-700 hover:text-indigo-900 hover:underline transition-colors"
                          >
                            {t.k8s.jobsTable.jumpServer}
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      )}
    </div>
  )
}

export default JobsTable
