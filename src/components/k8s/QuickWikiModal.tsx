'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { useSettings } from '@/contexts/SettingsContext'
import { useAuth } from '@/contexts/AuthContext'
import { authFetch } from '@/utils/authFetch'
import UserSelector from '@/components/UserSelector'
import { SubRepoInfo } from '@/types/repoinfo'
import { useLanguage } from '@/contexts/LanguageContext'

interface QuickWikiModalProps {
  show: boolean
  onClose: () => void
  onSuccess: (result: { wiki_id: string; job_id: string; message: string }) => void
}

// We'll use t.k8s.chinese and 'English' directly in the component

interface User {
  id: number
  user_code: string
  user_name: string
  email?: string
  dept?: string
  org?: string
}

interface UserSearchResult {
  users: User[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

interface Model {
  id: string
  name: string
  temperature: number
  top_p: number
}

interface ModelConfig {
  provider: string
  default_model: string
  models: Model[]
}

export default function QuickWikiModal({ show, onClose, onSuccess }: QuickWikiModalProps) {
  const { messages: t } = useLanguage();
  const { settings } = useSettings()
  const { userInfo } = useAuth()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Form state
  const [repoUrl, setRepoUrl] = useState('')
  const [branch, setBranch] = useState('control')
  const [language, setLanguage] = useState('zh')
  const [comprehensive, setComprehensive] = useState(true)
  const [selectedUserId, setSelectedUserId] = useState<string>('')
  const [, setSelectedModel] = useState<string>('')
  
  // 子仓库状态
  const [subRepositories, setSubRepositories] = useState<SubRepoInfo[]>([])
  
  // 模型选择状态
  const [provider, setProvider] = useState('whalecloud')
  const [model, setModel] = useState('gemini-2.5-flash')
  const [isCustomModel, setIsCustomModel] = useState(false)
  const [customModel, setCustomModel] = useState('')
  
  // 文件过滤状态
  const [excludedDirs, setExcludedDirs] = useState('')
  const [excludedFiles, setExcludedFiles] = useState('')
  const [includedDirs, setIncludedDirs] = useState('')
  const [includedFiles, setIncludedFiles] = useState('')
  
  // User search state
  const [userSearchTerm, setUserSearchTerm] = useState('')
  const [userSearchResults, setUserSearchResults] = useState<UserSearchResult | null>(null)
  const [userSearchLoading, setUserSearchLoading] = useState(false)
  const [showUserDropdown, setShowUserDropdown] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  
  // Data state
  const [, setModelConfig] = useState<ModelConfig | null>(null)
  const [, setLoadingModels] = useState(false)
  
  // Exception wiki info
  const [matchingExceptionWiki, setMatchingExceptionWiki] = useState<{
    wiki_id: string
    user_code?: string
    user_name?: string
    status?: string
  } | null>(null)
  
  // Define interface for exception wiki
  interface ExceptionWiki {
    wiki_id: string
    repo_url: string
    branch: string
    status: string
    user_code?: string
    user_name?: string
  }
  

  
  // Load models on mount
  useEffect(() => {
    if (show) {
      loadModels()
      // 默认选择当前用户
      if (userInfo?.id) {
        setSelectedUserId(userInfo.id.toString())
        setSelectedUser({
          id: userInfo.id,
          user_code: userInfo.user_code || '',
          user_name: userInfo.user_name || '',
          email: userInfo.email,
          dept: userInfo.dept,
          org: userInfo.org
        })
      }
    }
  }, [show, userInfo])
  
  // Search users with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (userSearchTerm.trim() && userSearchTerm.length >= 1) {
        searchUsers(userSearchTerm.trim(), 1)
      } else {
        setUserSearchResults(null)
        setShowUserDropdown(false)
      }
    }, 300) // 减少延迟时间到300ms，提升响应速度
    
    return () => clearTimeout(timeoutId)
  }, [userSearchTerm])
  
  // Search users
  const searchUsers = async (search: string, page: number = 1) => {
    setUserSearchLoading(true)
    try {
      const response = await authFetch(`/api/k8s/users/search?search=${encodeURIComponent(search)}&page=${page}&page_size=10`)
      if (response?.ok) {
        const data = await response.json()
        if (data.success) {
          setUserSearchResults(data.data)
          setShowUserDropdown(true)
        } else {
          // 处理搜索失败的情况
          setUserSearchResults(null)
          setShowUserDropdown(false)
        }
      } else {
        // 处理网络错误
        setUserSearchResults(null)
        setShowUserDropdown(false)
      }
    } catch (error) {
      console.error('Error searching users:', error)
      setUserSearchResults(null)
      setShowUserDropdown(false)
    } finally {
      setUserSearchLoading(false)
    }
  }
  
  // Load models
  const loadModels = async () => {
    setLoadingModels(true)
    try {
      const response = await authFetch('/api/k8s/models/config')
      if (response?.ok) {
        const data = await response.json()
        if (data.success) {
          setModelConfig(data.data)
          setSelectedModel(data.data.default_model)
        }
      }
    } catch (error) {
      console.error('Error loading models:', error)
    } finally {
      setLoadingModels(false)
    }
  }
  








  // 添加/删除子仓库
  const addSubRepo = () => setSubRepositories((subs) => [...subs, { url: '', branch: '' }]);
  const removeSubRepo = (idx: number) => setSubRepositories((subs) => subs.filter((_, i) => i !== idx));
  
  // Reset form when modal opens
  useEffect(() => {
    if (show) {
      setRepoUrl('')
      setBranch('control')
      setLanguage('zh')
      setComprehensive(true)
      setSubRepositories([])
      setMatchingExceptionWiki(null)
      setError(null)
      setUserSearchTerm('')
      setUserSearchResults(null)
      setShowUserDropdown(false)
      setSelectedUser(null)
      
      // 默认选择当前用户
      if (userInfo?.id) {
        setSelectedUserId(userInfo.id.toString())
        setSelectedUser({
          id: userInfo.id,
          user_code: userInfo.user_code || '',
          user_name: userInfo.user_name || '',
          email: userInfo.email,
          dept: userInfo.dept,
          org: userInfo.org
        })
      }
    }
  }, [show, userInfo])
  
  // Handle user selection
  const handleUserSelect = (user: User) => {
    setSelectedUser(user)
    setSelectedUserId(user.id.toString())
    setUserSearchTerm(`${user.user_name} (${user.user_code})`)
    setShowUserDropdown(false)
    setUserSearchResults(null)
  }
  
  // Handle user search input focus
  const handleUserSearchFocus = () => {
    if (userSearchTerm.trim()) {
      setShowUserDropdown(true)
    }
  }
  
  // Handle user search input blur
  const handleUserSearchBlur = () => {
    // 延迟关闭，让点击事件能够触发
    setTimeout(() => {
      setShowUserDropdown(false)
    }, 200)
  }
  
  // Check for matching exception wiki
  const checkMatchingExceptionWiki = useCallback(async (url: string, branchName: string) => {
    if (!url.trim() || !branchName.trim()) {
      setMatchingExceptionWiki(null)
      return
    }
    
    try {
      const response = await authFetch('/api/k8s/wiki/info/pending-failed')
      if (response?.ok) {
        const data = await response.json()
        const exceptionWikis = data?.data || []
        
        // Find matching wiki by repo_url and branch
        const matching = exceptionWikis.find((wiki: ExceptionWiki) => 
          wiki.repo_url === url.trim() && wiki.branch === branchName
        )
        
        setMatchingExceptionWiki(matching || null)
      }
    } catch (error) {
      console.error('Error checking exception wikis:', error)
      setMatchingExceptionWiki(null)
    }
  }, [])
  
  // Check for matching exception wiki when branch changes
  useEffect(() => {
    if (repoUrl.trim() && branch) {
      checkMatchingExceptionWiki(repoUrl.trim(), branch)
    }
  }, [branch, repoUrl, checkMatchingExceptionWiki])
  
  const handleSubmit = async () => {
    if (!repoUrl.trim()) {
      setError(t.k8s.quickWikiModal.pleaseEnterRepositoryUrl)
      return
    }
    
    if (!selectedUserId) {
      setError(t.k8s.quickWikiModal.pleaseSelectUser)
      return
    }
    
    setLoading(true)
    setError(null)
    
    try {
      // Prepare model settings using UserSelector state
      const modelSettings = {
        provider: provider,
        model: isCustomModel ? customModel : model,
        api_key: settings.apiKey || '',
        model_kwargs: {}
      }
      
      const response = await authFetch('/api/k8s/wiki/create-quick', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          repo_url: repoUrl.trim(),
          branch: branch,
          language: language,
          user_id: selectedUserId,
          comprehensive: comprehensive,
          model_settings: modelSettings,
          token: settings.whaleDevCloudToken || null,
          sub_repos: subRepositories.filter(sub => sub.url.trim() && sub.branch.trim()),
          excluded_dirs: excludedDirs,
          excluded_files: excludedFiles,
          included_dirs: includedDirs,
          included_files: includedFiles
        })
      })
      
      if (!response) {
        throw new Error(t.k8s.quickWikiModal.requestFailed)
      }
      
      const data = await response.json()
      if (data.success) {
        onSuccess({
          wiki_id: data.wiki_id,
          job_id: data.job_id,
          message: data.message
        })
        onClose()
      } else {
        setError(data.error || t.k8s.quickWikiModal.createFailed)
      }
    } catch (error) {
      console.error('Create quick wiki error:', error)
      setError(error instanceof Error ? error.message : t.k8s.quickWikiModal.createFailed)
    } finally {
      setLoading(false)
    }
  }
  
  if (!show) return null
  
  return (
    <div className="fixed inset-0 bg-black/40 backdrop-blur-md flex items-center justify-center z-50 p-4">
      <div className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl w-full max-w-2xl overflow-hidden border border-white/20 ring-1 ring-white/30">
        {/* Header */}
        <div className="px-6 py-4 border-b bg-gradient-to-r from-blue-50 to-indigo-50">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-bold text-gray-900 flex items-center gap-2">
              <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              {t.k8s.quickWikiModal.quickCreateWiki}
            </h3>
            <button
              onClick={onClose}
              className="w-8 h-8 bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 rounded-full flex items-center justify-center transition"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Body */}
        <div className="p-6 space-y-4 max-h-[70vh] overflow-y-auto">
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
              {error}
            </div>
          )}

          {/* User Selection */}
          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t.k8s.quickWikiModal.selectUser} *
            </label>
            <div className="relative">
              <input
                type="text"
                value={userSearchTerm}
                onChange={(e) => setUserSearchTerm(e.target.value)}
                onFocus={handleUserSearchFocus}
                onBlur={handleUserSearchBlur}
                placeholder={t.k8s.quickWikiModal.enterUserSearchPlaceholder}
                className="w-full px-4 py-3 pl-10 pr-10 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all shadow-sm hover:shadow-md"
              />
              {/* 搜索图标 */}
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              {userSearchLoading && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                </div>
              )}
              {!userSearchLoading && userSearchTerm && (
                <button
                  type="button"
                  onClick={() => {
                    setUserSearchTerm('')
                    setSelectedUser(null)
                    setSelectedUserId('')
                    setUserSearchResults(null)
                    setShowUserDropdown(false)
                  }}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-full hover:bg-gray-100"
                  title={t.k8s.quickWikiModal.clearSearch}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              )}
            </div>
            
            {/* 搜索提示 */}
            {userSearchTerm && userSearchTerm.length < 1 && (
              <div className="mt-1 text-xs text-gray-500">
                {t.k8s.quickWikiModal.enterAtLeastOneCharacter}
              </div>
            )}
            

            
            {/* User Dropdown */}
            {showUserDropdown && (
              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-xl max-h-60 overflow-y-auto">
                {userSearchLoading ? (
                  <div className="px-3 py-4 text-center text-gray-500 text-sm">
                    <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                    {t.k8s.quickWikiModal.searching}
                  </div>
                ) : userSearchResults && userSearchResults.users.length > 0 ? (
                  <>
                    {userSearchResults.users.map((user) => (
                      <div
                        key={user.id}
                        onClick={() => handleUserSelect(user)}
                        className="px-3 py-3 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="font-medium text-sm text-gray-900 flex items-center gap-2">
                              <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                              </svg>
                              {user.user_name}
                            </div>
                            <div className="text-xs text-gray-500 mt-1 ml-6">{user.user_code}</div>
                            {user.dept && (
                              <div className="text-xs text-gray-400 mt-1 ml-6 flex items-center gap-1">
                                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                </svg>
                                {user.dept}
                              </div>
                            )}
                          </div>
                          <div className="text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          </div>
                        </div>
                      </div>
                    ))}
                    
                    {/* Pagination */}
                    {userSearchResults.total_pages > 1 && (
                      <div className="px-3 py-2 border-t border-gray-200 bg-gray-50">
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span>
                            {t.k8s.quickWikiModal.pageInfo.replace('{page}', userSearchResults.page.toString()).replace('{total}', userSearchResults.total_pages.toString())}
                          </span>
                          <div className="flex gap-1">
                            {userSearchResults.page > 1 && (
                              <button
                                onClick={() => searchUsers(userSearchTerm, userSearchResults.page - 1)}
                                className="px-2 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors"
                              >
                                {t.k8s.quickWikiModal.previousPage}
                              </button>
                            )}
                            {userSearchResults.page < userSearchResults.total_pages && (
                              <button
                                onClick={() => searchUsers(userSearchTerm, userSearchResults.page + 1)}
                                className="px-2 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors"
                              >
                                {t.k8s.quickWikiModal.nextPage}
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                ) : userSearchTerm.length >= 1 ? (
                  <div className="px-3 py-4 text-center text-gray-500 text-sm">
                    <div className="w-8 h-8 mx-auto mb-2 text-gray-300">
                      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                    <p className="font-medium text-gray-700 mb-1">{t.k8s.quickWikiModal.noUsersFound}</p>
                    <p className="text-xs">{t.k8s.quickWikiModal.checkUserInput}</p>
                  </div>
                ) : null}
              </div>
            )}
            
            {/* Selected User Info */}
            {selectedUser && (
              <div className="mt-2 p-3 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg shadow-sm">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="text-sm font-medium text-green-800 flex items-center gap-2 mb-1">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      {t.k8s.quickWikiModal.selectedUser}
                    </div>
                    <div className="text-sm text-green-700 font-medium">
                      {selectedUser.user_name} ({selectedUser.user_code})
                    </div>
                    {selectedUser.dept && (
                      <div className="text-xs text-green-600 mt-1 flex items-center gap-1">
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                        {selectedUser.dept}
                      </div>
                    )}
                    {selectedUser.email && (
                      <div className="text-xs text-green-600 mt-1 flex items-center gap-1">
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        {selectedUser.email}
                      </div>
                    )}
                  </div>
                  <button
                    type="button"
                    onClick={() => {
                      setSelectedUser(null)
                      setSelectedUserId('')
                      setUserSearchTerm('')
                    }}
                    className="text-green-600 hover:text-green-800 p-2 rounded-full hover:bg-green-100 transition-colors"
                    title={t.k8s.quickWikiModal.reselectUser}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Repository URL */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t.k8s.quickWikiModal.repositoryUrl} *
            </label>
            <input
              type="text"
              value={repoUrl}
              onChange={(e) => setRepoUrl(e.target.value)}
              placeholder="https://github.com/owner/repo"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
            />
          </div>

          {/* Branch */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t.k8s.quickWikiModal.branch}
            </label>
            <input
              type="text"
              value={branch}
              onChange={(e) => setBranch(e.target.value)}
              placeholder="control"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
            />
          </div>

          {/* Sub Repository info */}
          <div className="ml-6 border-l-4 border-gray-200 pl-4 bg-gray-50 rounded-md">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t.k8s.quickWikiModal.subRepositories}
            </label>
            <div className="flex flex-col gap-2">
              {subRepositories.map((subRepo, idx) => (
                <div key={idx} className="mb-4 flex gap-2 items-center">
                  {/* 子仓库地址输入 */}
                  <input
                    type="text"
                    value={subRepo.url}
                    onChange={(e) => {
                      const url = e.target.value;
                      setSubRepositories((subs) =>
                        subs.map((item, i) => (i === idx ? { ...item, url } : item))
                      );
                    }}
                    placeholder="https://github.com/owner/repo"
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all flex-[3_3_0%] min-w-0"
                  />

                  {/* 子仓库分支输入 */}
                  <input
                    type="text"
                    value={subRepo.branch || ''}
                    onChange={(e) => {
                      const branch = e.target.value;
                      setSubRepositories((subs) =>
                        subs.map((item, i) => (i === idx ? { ...item, branch } : item))
                      );
                    }}
                    placeholder="control"
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all flex-[1_1_0%] min-w-0"
                  />
                  
                  {/* 删除子仓库按钮 */}
                  <button
                    type="button"
                    onClick={() => removeSubRepo(idx)}
                    className="text-gray-400 hover:text-red-500 px-2 py-1 rounded transition-colors text-xl leading-none"
                    aria-label={t.k8s.quickWikiModal.delete}
                  >
                    &times;
                  </button>
                </div>
              ))}

              {/* 添加子仓库按钮 */}
              <button
                type="button"
                onClick={addSubRepo}
                className="mt-2 px-3 py-1 text-sm rounded-md border border-gray-300 text-blue-600 hover:bg-blue-50 transition-colors self-start"
              >
                {t.k8s.quickWikiModal.addSubRepository}
              </button>
            </div>
          </div>

          {/* Exception Wiki Info */}
          {matchingExceptionWiki && (
            <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
              <div className="flex items-start gap-2">
                <svg className="w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <div className="flex-1">
                  <div className="text-sm font-medium text-amber-800">
                    {t.k8s.quickWikiModal.foundExceptionWiki}
                  </div>
                  <div className="text-xs text-amber-700 mt-1">
                    <div>Wiki ID: {matchingExceptionWiki.wiki_id}</div>
                    <div>{t.k8s.quickWikiModal.status}: {matchingExceptionWiki.status}</div>
                    {matchingExceptionWiki.user_name && matchingExceptionWiki.user_code && (
                      <div>{t.k8s.quickWikiModal.creator}: {matchingExceptionWiki.user_name}({matchingExceptionWiki.user_code})</div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Language */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t.k8s.quickWikiModal.wikiLanguage}
            </label>
            <select
              value={language}
              onChange={(e) => setLanguage(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
            >
              <option value="zh">{t.k8s.quickWikiModal.chinese}</option>
              <option value="en">English</option>
            </select>
          </div>

          {/* Wiki Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t.k8s.quickWikiModal.wikiType}
            </label>
            <div className="grid grid-cols-2 gap-3">
              <button
                type="button"
                onClick={() => setComprehensive(true)}
                className={`p-3 rounded-lg border text-left transition-all ${
                  comprehensive
                    ? 'bg-blue-50 border-blue-200 text-blue-700'
                    : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
                }`}
              >
                <div className="font-medium text-sm">{t.k8s.quickWikiModal.detailedMode}</div>
                <div className="text-xs opacity-70">{t.k8s.quickWikiModal.detailedModeDescription}</div>
              </button>
              <button
                type="button"
                onClick={() => setComprehensive(false)}
                className={`p-3 rounded-lg border text-left transition-all ${
                  !comprehensive
                    ? 'bg-blue-50 border-blue-200 text-blue-700'
                    : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
                }`}
              >
                <div className="font-medium text-sm">{t.k8s.quickWikiModal.conciseMode}</div>
                <div className="text-xs opacity-70">{t.k8s.quickWikiModal.conciseModeDescription}</div>
              </button>
            </div>
          </div>

          {/* Model Selector */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t.k8s.quickWikiModal.modelSelection}
            </label>
            <UserSelector
              provider={provider}
              setProvider={setProvider}
              model={model}
              setModel={setModel}
              isCustomModel={isCustomModel}
              setIsCustomModel={setIsCustomModel}
              customModel={customModel}
              setCustomModel={setCustomModel}
              showFileFilters={true}
              excludedDirs={excludedDirs}
              setExcludedDirs={setExcludedDirs}
              excludedFiles={excludedFiles}
              setExcludedFiles={setExcludedFiles}
              includedDirs={includedDirs}
              setIncludedDirs={setIncludedDirs}
              includedFiles={includedFiles}
              setIncludedFiles={setIncludedFiles}
            />
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t bg-gray-50 flex justify-end gap-3">
          <button
            onClick={onClose}
            disabled={loading}
            className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
          >
            {t.common.cancel}
          </button>
          <button
            onClick={handleSubmit}
            disabled={loading || !repoUrl.trim() || !selectedUserId}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center gap-2"
          >
            {loading && (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            )}
            {loading ? t.k8s.quickWikiModal.creating : t.k8s.quickWikiModal.createWiki}
          </button>
        </div>
      </div>
    </div>
  )
} 