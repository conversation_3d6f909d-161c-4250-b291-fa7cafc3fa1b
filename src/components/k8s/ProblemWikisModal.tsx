'use client'

import React, { useMemo, useState, useEffect } from 'react'
import { useLanguage } from '@/contexts/LanguageContext'

interface ProblemWiki {
  wiki_id: string
  repo_url: string
  branch: string
  status?: string
  user_code?: string
  user_name?: string
  created_time?: string
}

interface Model {
  id: string
  name: string
  temperature: number
  top_p: number
}

interface ModelConfig {
  provider: string
  default_model: string
  models: Model[]
}

interface ModelSettings {
  provider: string
  model: string
  comprehensive: boolean
  api_key?: string
  model_kwargs: Record<string, unknown>
}

interface ProblemWikisModalProps {
  show: boolean
  onClose: () => void
  problemWikis: ProblemWiki[]
  onFastGenerate: (wikiId: string, modelSettings?: ModelSettings) => void
  generatingWikis: Set<string>
  onRefresh?: () => void
  isRefreshing?: boolean
}

// 模型选择弹窗组件
function ModelSelectionModal({ 
  show, 
  onClose, 
  onConfirm, 
  wikiId, 
  modelConfig,
  loadingModels
}: { 
  show: boolean
  onClose: () => void
  onConfirm: (modelSettings: ModelSettings) => void
  wikiId: string
  modelConfig: ModelConfig | null
  loadingModels: boolean
}) {
  const { messages: t } = useLanguage();
  const [provider, setProvider] = useState('whalecloud')
  const [model, setModel] = useState('')
  const [isCustomModel, setIsCustomModel] = useState(false)
  const [customModel, setCustomModel] = useState('')
  const [comprehensive, setComprehensive] = useState(true)
  const [apiKey, setApiKey] = useState('')

  // 当弹窗打开时重置状态
  useEffect(() => {
    if (show && modelConfig) {
      setProvider(modelConfig.provider)
      setModel(modelConfig.default_model)
      setIsCustomModel(false)
      setCustomModel('')
      setApiKey('')
    }
  }, [show, modelConfig])

  useEffect(() => {
    if (modelConfig) {
      setProvider(modelConfig.provider)
      // 当模型配置加载完成时，总是设置默认模型
      setModel(modelConfig.default_model)
    } else {
      // 如果没有模型配置，设置默认值
      setProvider('whalecloud')
      setModel('gemini-2.5-flash')
    }
  }, [modelConfig])

  // 这个 useEffect 现在不需要了，因为我们在上面的 useEffect 中已经处理了模型设置
  // 保留注释以便将来参考

  const handleConfirm = () => {
    // 验证模型选择
    const selectedModel = isCustomModel ? customModel.trim() : model
    
    if (!selectedModel) {
      alert(t.k8s.problemWikisModal.modelRequired)
      return
    }

    // 确保使用正确的模型ID
    const finalModel = isCustomModel ? selectedModel : model

    const modelSettings: ModelSettings = {
      provider,
      model: finalModel,
      comprehensive,
      api_key: apiKey.trim() || undefined,
      model_kwargs: {}
    }
    
    onConfirm(modelSettings)
  }

  if (!show) return null

  return (
    <div className="fixed inset-0 bg-gray-900/70 backdrop-blur-md flex items-center justify-center z-[60] p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md overflow-hidden border border-cyan-100/30">
        {/* Header */}
        <div className="px-6 py-4 border-b bg-gradient-to-r from-cyan-50 to-gray-50">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-bold text-gray-800 flex items-center gap-2">
              <svg className="w-5 h-5 text-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              {t.k8s.problemWikisModal.selectGenerationModel}
            </h3>
            <button
              onClick={onClose}
              className="w-8 h-8 bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 rounded-full flex items-center justify-center transition"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <p className="text-sm text-gray-600 mt-1">Wiki ID: {wikiId}</p>
        </div>

        {/* Body */}
        <div className="p-6 space-y-4">
          {/* Provider Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t.k8s.problemWikisModal.modelProvider}
            </label>
            <select
              value={provider}
              onChange={(e) => setProvider(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all"
            >
              <option value="whalecloud">WhaleCloud</option>
              <option value="openai">OpenAI</option>
              <option value="anthropic">Anthropic</option>
            </select>
          </div>

          {/* Model Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t.k8s.problemWikisModal.modelSelection}
            </label>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <input
                  type="radio"
                  id="preset-model"
                  checked={!isCustomModel}
                  onChange={() => setIsCustomModel(false)}
                  className="text-cyan-600 focus:ring-cyan-500"
                />
                <label htmlFor="preset-model" className="text-sm text-gray-700">{t.k8s.problemWikisModal.presetModel}</label>
              </div>
              
              {!isCustomModel && (
                <div className="ml-6">
                  {modelConfig?.models && modelConfig.models.length > 0 ? (
                    <select
                      value={model}
                      onChange={(e) => {
                        const selectedValue = e.target.value
                        setModel(selectedValue)
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all"
                    >
                      {modelConfig.models.map((m) => (
                        <option key={m.id} value={m.id}>
                          {m.name}
                        </option>
                      ))}
                    </select>
                  ) : (
                    <div className="text-sm text-gray-500 py-2 px-3 bg-gray-50 rounded-lg border border-gray-200">
                      {loadingModels ? t.k8s.problemWikisModal.loadingModels : t.k8s.problemWikisModal.noAvailableModels}
                    </div>
                  )}
                </div>
              )}

              <div className="flex items-center gap-2">
                <input
                  type="radio"
                  id="custom-model"
                  checked={isCustomModel}
                  onChange={() => setIsCustomModel(true)}
                  className="text-cyan-600 focus:ring-cyan-500"
                />
                <label htmlFor="custom-model" className="text-sm text-gray-700">{t.k8s.problemWikisModal.customModel}</label>
              </div>
              
              {isCustomModel && (
                <input
                  type="text"
                  value={customModel}
                  onChange={(e) => setCustomModel(e.target.value)}
                  placeholder={t.k8s.problemWikisModal.enterModelName}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all ml-6"
                />
              )}
            </div>
          </div>

          {/* Wiki Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t.k8s.problemWikisModal.wikiType}
            </label>
            <div className="grid grid-cols-2 gap-3">
              <button
                type="button"
                onClick={() => setComprehensive(true)}
                className={`p-3 rounded-lg border text-left transition-all ${
                  comprehensive
                    ? 'bg-cyan-50 border-cyan-200 text-cyan-700'
                    : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
                }`}
              >
                <div className="font-medium text-sm">{t.k8s.problemWikisModal.detailedMode}</div>
                <div className="text-xs opacity-70">{t.k8s.problemWikisModal.detailedModeDescription}</div>
              </button>
              <button
                type="button"
                onClick={() => setComprehensive(false)}
                className={`p-3 rounded-lg border text-left transition-all ${
                  !comprehensive
                    ? 'bg-cyan-50 border-cyan-200 text-cyan-700'
                    : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
                }`}
              >
                <div className="font-medium text-sm">{t.k8s.problemWikisModal.conciseMode}</div>
                <div className="text-xs opacity-70">{t.k8s.problemWikisModal.conciseModeDescription}</div>
              </button>
            </div>
          </div>

          {/* API Key Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t.k8s.problemWikisModal.apiKeyOptional}
            </label>
            <input
              type="password"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder={t.k8s.problemWikisModal.enterApiKey}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all"
            />
            <p className="text-xs text-gray-500 mt-1">
              {t.k8s.problemWikisModal.enterApiKeyNote}
            </p>
          </div>

          {/* 调试信息 */}
          <div className="p-3 bg-gray-50 rounded-lg border border-gray-200">
            <div className="text-xs text-gray-600">
              <div>{t.k8s.problemWikisModal.currentStatus}</div>
              <div>{t.k8s.problemWikisModal.provider}: {provider}</div>
              <div>{t.k8s.problemWikisModal.model}: {model}</div>
              <div>{t.k8s.problemWikisModal.customModelValue}: {customModel}</div>
              <div>{t.k8s.problemWikisModal.isCustom}: {isCustomModel ? t.k8s.problemWikisModal.yes : t.k8s.problemWikisModal.no}</div>
              <div>{t.k8s.problemWikisModal.comprehensive}: {comprehensive ? t.k8s.problemWikisModal.detailed : t.k8s.problemWikisModal.concise}</div>
              <div>{t.k8s.problemWikisModal.apiKeyStatus}: {apiKey ? t.k8s.problemWikisModal.entered : t.k8s.problemWikisModal.notEntered}</div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t bg-gray-50 flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            {t.common.cancel}
          </button>
          <button
            onClick={handleConfirm}
            disabled={!model || (isCustomModel && !customModel.trim())}
            className="px-6 py-2 bg-cyan-600 text-white rounded-lg hover:bg-cyan-700 transition-colors disabled:opacity-50 flex items-center gap-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            {t.k8s.problemWikisModal.confirmGeneration}
          </button>
        </div>
      </div>
    </div>
  )
}

export default function ProblemWikisModal({ show, onClose, problemWikis, onFastGenerate, generatingWikis, onRefresh, isRefreshing = false }: ProblemWikisModalProps) {
  const { messages: t } = useLanguage();
  const [query, setQuery] = useState('')
  const [showModelModal, setShowModelModal] = useState(false)
  const [selectedWikiId, setSelectedWikiId] = useState('')
  const [modelConfig, setModelConfig] = useState<ModelConfig | null>(null)
  const [loadingModels, setLoadingModels] = useState(false)

  // 加载模型配置
  useEffect(() => {
    if (show) {
      loadModels()
    }
  }, [show])

  const loadModels = async () => {
    setLoadingModels(true)
    try {
      const response = await fetch('/api/k8s/models/config')
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setModelConfig(data.data)
        }
      }
    } catch (error) {
      console.error('Error loading models:', error)
    } finally {
      setLoadingModels(false)
    }
  }

  const filtered = useMemo(() => {
    const q = query.trim().toLowerCase()
    if (!q) return problemWikis
    return problemWikis.filter(w => {
      return (
        (w.wiki_id && w.wiki_id.toLowerCase().includes(q)) ||
        (w.repo_url && w.repo_url.toLowerCase().includes(q)) ||
        (w.branch && w.branch.toLowerCase().includes(q)) ||
        (w.status && w.status.toLowerCase().includes(q)) ||
        (w.user_code && w.user_code.toLowerCase().includes(q)) ||
        (w.user_name && w.user_name.toLowerCase().includes(q))
      )
    })
  }, [problemWikis, query])

  const copy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
    } catch {}
  }

  const formatTime = (timeStr?: string) => {
    if (!timeStr) return 'N/A'
    try {
      const date = new Date(timeStr)
      return date.toLocaleString('zh', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch {
      return 'N/A'
    }
  }

  const handleFastGenerate = (wikiId: string) => {
    setSelectedWikiId(wikiId)
    setShowModelModal(true)
  }

  const handleModelConfirm = (modelSettings: ModelSettings) => {
    onFastGenerate(selectedWikiId, modelSettings)
    setShowModelModal(false)
    setSelectedWikiId('')
  }

  if (!show) return null

  return (
    <>
      <div className="fixed inset-0 bg-gray-900/60 backdrop-blur-md flex items-center justify-center z-50 p-4">
        <div className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl w-full max-w-[95vw] max-h-[90vh] overflow-hidden border border-cyan-100/30 ring-1 ring-cyan-200/40">
          <div className="px-6 py-5 border-b border-cyan-100/50 bg-gradient-to-r from-cyan-50/80 to-gray-50/80">
            <div className="flex items-center justify-between gap-4 flex-wrap">
              <div className="flex items-center gap-3">
                <h3 className="text-xl font-bold text-gray-800 flex items-center gap-3">
                  <span className="inline-flex w-3 h-3 rounded-full bg-cyan-400 shadow-sm"></span>
                  {t.k8s.problemWikisModal.problemWikis}
                </h3>
                <span className="text-sm px-3 py-1.5 rounded-full bg-cyan-100/70 text-cyan-700 font-medium border border-cyan-200/50">
                  {problemWikis.length}
                </span>
              </div>
              <div className="flex items-center gap-3">
                <div className="relative">
                  <input
                    value={query}
                    onChange={e => setQuery(e.target.value)}
                    placeholder={t.k8s.problemWikisModal.searchPlaceholder}
                    className="w-72 px-4 py-2.5 bg-white/80 border border-cyan-200/60 rounded-xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-cyan-300/60 focus:border-cyan-300/80 transition-all shadow-sm backdrop-blur-sm"
                  />
                  <svg className="w-4 h-4 absolute right-4 top-1/2 -translate-y-1/2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-4.35-4.35M10 18a8 8 0 110-16 8 8 0 010 16z" /></svg>
                </div>
                
                {/* 刷新按钮 */}
                <button 
                  onClick={onRefresh}
                  disabled={isRefreshing}
                  className="inline-flex items-center gap-2 px-4 py-2.5 bg-cyan-500 hover:bg-cyan-600 text-white text-sm font-medium rounded-xl transition-all shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed border border-cyan-400/30"
                  title={t.k8s.problemWikisModal.refreshData}
                >
                  {isRefreshing ? (
                    <>
                      <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {t.k8s.problemWikisModal.refreshing}
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                      {t.k8s.problemWikisModal.refresh}
                    </>
                  )}
                </button>
                
                <button onClick={onClose} className="w-11 h-11 bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 rounded-xl flex items-center justify-center transition-all hover:shadow-md border border-gray-200/60">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
                </button>
              </div>
            </div>
          </div>

          <div className="p-0">
            <div className="scroll-area max-h-[75vh] overflow-auto">
              {filtered.length === 0 ? (
                <div className="p-12 text-center text-gray-500">
                  <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <p className="text-lg font-medium">{t.k8s.problemWikisModal.noData}</p>
                  <p className="text-sm text-gray-400 mt-1">{t.k8s.problemWikisModal.noMatchingProblemWikis}</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full text-sm min-w-[1200px]">
                    <thead className="sticky top-0 z-10 bg-white/90 backdrop-blur border-b border-cyan-100/50">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-bold text-gray-600 uppercase tracking-wider w-32">Wiki ID</th>
                        <th className="px-4 py-3 text-left text-xs font-bold text-gray-600 uppercase tracking-wider w-80">{t.k8s.problemWikisModal.repository}</th>
                        <th className="px-4 py-3 text-left text-xs font-bold text-gray-600 uppercase tracking-wider w-24">{t.k8s.problemWikisModal.branch}</th>
                        <th className="px-4 py-3 text-left text-xs font-bold text-gray-600 uppercase tracking-wider w-32">{t.k8s.problemWikisModal.creator}</th>
                        <th className="px-4 py-3 text-left text-xs font-bold text-gray-600 uppercase tracking-wider w-32">{t.k8s.problemWikisModal.createdTime}</th>
                        <th className="px-4 py-3 text-left text-xs font-bold text-gray-600 uppercase tracking-wider w-24">{t.k8s.problemWikisModal.status}</th>
                        <th className="px-4 py-3 text-left text-xs font-bold text-gray-600 uppercase tracking-wider w-32">{t.k8s.problemWikisModal.actions}</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-cyan-50/60">
                      {filtered.map((w, idx) => (
                        <tr key={`${w.wiki_id}-${idx}`} className="hover:bg-cyan-50/30 transition-colors">
                          <td className="px-4 py-3 text-gray-800 whitespace-nowrap">
                            <div className="flex items-center gap-2">
                              <span className="font-medium text-gray-900">{w.wiki_id}</span>
                              <button
                                onClick={() => copy(w.wiki_id)}
                                className="inline-flex items-center px-2 py-1 text-xs rounded-lg bg-cyan-100/60 text-cyan-700 hover:bg-cyan-200/80 transition-colors border border-cyan-200/40"
                                title={t.k8s.problemWikisModal.copyId}
                              >
                                <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2M8 16h8a2 2 0 002-2v-2M8 16v2a2 2 0 002 2h2M16 8h.01" /></svg>
                              </button>
                            </div>
                          </td>
                          <td className="px-4 py-3 text-gray-800 align-top">
                            <div className="flex items-start gap-2">
                              <div className="flex-1 break-all text-[13px] leading-5 text-gray-700">{w.repo_url}</div>
                              <button
                                onClick={() => copy(w.repo_url)}
                                className="inline-flex items-center px-2 py-1 text-xs rounded-lg bg-cyan-100/60 text-cyan-700 hover:bg-cyan-200/80 transition-colors mt-0.5 border border-cyan-200/40 flex-shrink-0"
                                title={t.k8s.problemWikisModal.copyRepoUrl}
                              >
                                <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2M8 16h8a2 2 0 002-2v-2M8 16v2a2 2 0 002 2h2M16 8h.01" /></svg>
                              </button>
                            </div>
                          </td>
                          <td className="px-4 py-3 text-gray-800 whitespace-nowrap">
                            <span className="px-2.5 py-1 bg-gray-100 text-gray-700 text-xs font-medium rounded-lg border border-gray-200/60">
                              {w.branch}
                            </span>
                          </td>
                          <td className="px-4 py-3 text-gray-800 whitespace-nowrap">
                            <div className="flex items-center gap-1">
                              <span className="font-medium text-gray-900">{w.user_name || 'N/A'}</span>
                              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded border border-gray-200/60">({w.user_code || 'N/A'})</span>
                            </div>
                          </td>
                          <td className="px-4 py-3 text-gray-700 whitespace-nowrap">
                            <span className="text-xs font-medium text-gray-600">
                              {formatTime(w.created_time)}
                            </span>
                          </td>
                          <td className="px-4 py-3">
                            <span className={`inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-xs font-semibold ${
                              w.status === 'failed' 
                                ? 'bg-red-100 text-red-700 border border-red-200/60' 
                                : 'bg-amber-100 text-amber-700 border border-amber-200/60'
                            }`}>
                              {w.status}
                              {w.status === 'failed' ? (
                                <svg className="w-3.5 h-3.5" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm-1-5h2v2H9v-2zm0-8h2v6H9V5z" clipRule="evenodd"/></svg>
                              ) : (
                                <svg className="w-3.5 h-3.5" fill="currentColor" viewBox="0 0 20 20"><path d="M2 10a8 8 0 1116 0A8 8 0 012 10zm9-4H9v5h5V9h-3V6z"/></svg>
                              )}
                            </span>
                          </td>
                          <td className="px-4 py-3">
                            <button
                              onClick={() => handleFastGenerate(w.wiki_id)}
                              disabled={generatingWikis.has(w.wiki_id)}
                              className="inline-flex items-center gap-2 px-4 py-2 bg-cyan-600 hover:bg-cyan-700 text-white text-xs font-medium rounded-lg transition-all shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed border border-cyan-500/30"
                            >
                              {generatingWikis.has(w.wiki_id) ? (
                                <>
                                  <svg className="w-3.5 h-3.5 animate-spin" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                  {t.k8s.problemWikisModal.generating}
                                </>
                              ) : (
                                <>
                                  <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                  </svg>
                                  {t.k8s.problemWikisModal.fastGenerate}
                                </>
                              )}
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>

          <style jsx>{`
            .scroll-area { scrollbar-width: thin; scrollbar-color: rgba(34, 211, 238, 0.4) transparent; }
            .scroll-area::-webkit-scrollbar { width: 10px; height: 10px; }
            .scroll-area::-webkit-scrollbar-thumb { background-color: rgba(34, 211, 238, 0.4); border-radius: 9999px; border: 2px solid rgba(255,255,255,0.7); }
            .scroll-area::-webkit-scrollbar-track { background: transparent; }
          `}</style>
        </div>
      </div>

      {/* 模型选择弹窗 */}
      <ModelSelectionModal
        show={showModelModal}
        onClose={() => {
          setShowModelModal(false)
          setSelectedWikiId('')
        }}
        onConfirm={handleModelConfirm}
        wikiId={selectedWikiId}
        modelConfig={modelConfig}
        loadingModels={loadingModels}
      />
    </>
  )
} 