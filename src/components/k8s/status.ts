export const STATUS_DESCRIPTIONS: Record<string, string> = {
  NOT_CREATED: '未创建',
  CREATING: '已分配创建中',
  INITIALIZING: '初始化智能体',
  READY: '运行中',
  FAILED: '创建失败',
  QUERY_FAILED: '状态查询失败'
}

export const STATUS_DESCRIPTIONS_EN: Record<string, string> = {
  NOT_CREATED: 'Not Created',
  CREATING: 'Creating',
  INITIALIZING: 'Initializing',
  READY: 'Ready',
  FAILED: 'Failed',
  QUERY_FAILED: 'Query Failed'
}

export const STATUS_STYLES: Record<string, string> = {
  NOT_CREATED: 'bg-gray-100 text-gray-800 ring-1 ring-inset ring-gray-600/20',
  CREATING: 'bg-yellow-100 text-yellow-800 ring-1 ring-inset ring-yellow-600/20',
  INITIALIZING: 'bg-blue-100 text-blue-800 ring-1 ring-inset ring-blue-600/20',
  READY: 'bg-green-100 text-green-800 ring-1 ring-inset ring-green-600/20',
  FAILED: 'bg-red-100 text-red-800 ring-1 ring-inset ring-red-600/20',
  QUERY_FAILED: 'bg-orange-100 text-orange-800 ring-1 ring-inset ring-orange-600/20'
}

export function getSandboxStatusStyle(status: string): string {
  const baseClasses = 'inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold'
  const style = STATUS_STYLES[status]
  return style ? `${baseClasses} ${style}` : `${baseClasses} bg-gray-100 text-gray-800 ring-1 ring-inset ring-gray-600/20`
}

export function getSandboxStatusDescription(status: string, lang?:string): string {
  return lang === 'en' ? STATUS_DESCRIPTIONS_EN[status] || status : STATUS_DESCRIPTIONS[status] || status
} 