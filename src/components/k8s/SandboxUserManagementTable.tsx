import React, { useEffect, useState } from 'react'
import { SandboxListItem, UserQuotaUser } from './types'
import { useLanguage } from '@/contexts/LanguageContext'

interface SandboxUserManagementTableProps {
  quotaUsers: UserQuotaUser[]
  quotaTotal: number
  quotaLoading: boolean
  quotaPage: number
  setQuotaPage: (n: number) => void
  quotaPageSize: number
  setQuotaPageSize: (n: number) => void
  loadUserQuotas: () => void | Promise<void>
  quotaEdits: Record<string, string>
  onChangeQuotaEdit: (user_code: string, value: string) => void
  saveUserQuota: (user_code: string) => void | Promise<void>
  expandedUsers: Record<string, boolean>
  toggleUserSandboxes: (user_code: string) => void
  userSandboxMap: Record<string, { loading: boolean; items: SandboxListItem[] }>
  loadUserSandboxes: (user_code: string) => void | Promise<void>
  deleteUserSandbox: (user_code: string, sb: SandboxListItem) => void | Promise<void>
}

export default function SandboxUserManagementTable(props: SandboxUserManagementTableProps) {
  const { messages: t, language } = useLanguage();
  const {
    quotaUsers,
    quotaTotal,
    quotaLoading,
    quotaPage,
    setQuotaPage,
    quotaPageSize,
    setQuotaPageSize,
    loadUserQuotas,
    quotaEdits,
    onChangeQuotaEdit,
    saveUserQuota,
    expandedUsers,
    toggleUserSandboxes,
    userSandboxMap,
    loadUserSandboxes,
    deleteUserSandbox
  } = props

  const [userPageInput, setUserPageInput] = useState(String(quotaPage))
  useEffect(() => { setUserPageInput(String(quotaPage)) }, [quotaPage])

  const maxPage = Math.max(1, Math.ceil((quotaTotal || 0) / (quotaPageSize || 1)))
  const gotoUserPage = (p: number) => {
    const page = Math.min(Math.max(1, p || 1), maxPage)
    if (page !== quotaPage) {
      setQuotaPage(page)
      setTimeout(() => loadUserQuotas(), 0)
    }
  }

  return (
    <div className="bg-white/50 backdrop-blur-2xl rounded-3xl shadow-lg border border-white/40 overflow-hidden">
      <div className="px-6 py-4 border-b border-white/40 flex items-center justify-between flex-wrap gap-3">
        <h2 className="text-base md:text-lg font-semibold text-slate-900 tracking-tight flex items-center gap-3">
          <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M13 20v-2a4 4 0 10-8 0v2m8-10a4 4 0 11-8 0 4 4 0 018 0m6 2a4 4 0 100-8 4 4 0 000 8z" />
          </svg>
          {t.k8s.sandboxUserManagementTable.title}
          <span className="text-xs md:text-sm text-slate-500 ml-2">{t.k8s.sandboxUserManagementTable.totalRecords.replace('{total}', quotaTotal.toString())}</span>
        </h2>
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2 bg-white/60 border border-white/40 px-3 py-2 rounded-xl">
            <span className="text-xs text-slate-600">{t.k8s.sandboxUserManagementTable.perPage}</span>
            <select
              value={quotaPageSize}
              onChange={(e) => { setQuotaPageSize(parseInt(e.target.value)); setQuotaPage(1); setTimeout(() => loadUserQuotas(), 0) }}
              className="px-2 py-1 bg-transparent text-slate-800 rounded-md focus:outline-none"
            >
              {[10,20,50,100].map(n => <option key={n} value={n}>{n}</option>)}
            </select>
          </div>
          <div className="flex items-center gap-2 bg-white/60 border border-white/40 px-3 py-2 rounded-xl">
            <button
              onClick={() => { if (quotaPage > 1) { setQuotaPage(quotaPage - 1); setTimeout(() => loadUserQuotas(), 0) } }}
              disabled={quotaLoading || quotaPage <= 1}
              className="px-3 py-1.5 bg-white text-slate-700 rounded-lg hover:bg-slate-50 disabled:opacity-50"
            >{t.k8s.sandboxUserManagementTable.previousPage}</button>
            <div className="flex items-center gap-2">
              <span className="text-xs text-slate-600">{t.k8s.sandboxUserManagementTable.pageNumber}</span>
              <input
                value={userPageInput}
                onChange={(e) => setUserPageInput(e.target.value.replace(/[^0-9]/g, ''))}
                onBlur={() => { const p = parseInt(userPageInput || '1', 10); gotoUserPage(isNaN(p) ? 1 : p) }}
                onKeyDown={(e) => { if (e.key === 'Enter') { const p = parseInt(userPageInput || '1', 10); gotoUserPage(isNaN(p) ? 1 : p) } }}
                className="w-14 px-2 py-1.5 bg-white text-center border border-white/50 rounded-lg focus:outline-none"
                inputMode="numeric"
              />
              {language === 'zh' && <span className="text-xs text-slate-600">{t.k8s.sandboxUserManagementTable.page}</span>}
            </div>
            <button
              onClick={() => { if (quotaPage < maxPage) { setQuotaPage(quotaPage + 1); setTimeout(() => loadUserQuotas(), 0) } }}
              disabled={quotaLoading || quotaPage >= maxPage}
              className="px-3 py-1.5 bg-white text-slate-700 rounded-lg hover:bg-slate-50 disabled:opacity-50"
            >{t.k8s.sandboxUserManagementTable.nextPage}</button>
          </div>
        </div>
      </div>

      <div className="relative overflow-x-auto">
        <table className="w-full">
          <thead className="bg-white/60">
            <tr>
              <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">{t.k8s.sandboxUserManagementTable.userCode}</th>
                             <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">{t.k8s.sandboxUserManagementTable.userName}</th>
              <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">{t.k8s.sandboxUserManagementTable.personalizedQuota}</th>
              <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">{t.k8s.sandboxUserManagementTable.effectiveQuota}</th>
              <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">{t.k8s.sandboxUserManagementTable.edit}</th>
              <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">{t.k8s.sandboxUserManagementTable.actions}</th>
            </tr>
          </thead>
          <tbody className="bg-white/70 divide-y divide-slate-100">
            {quotaUsers.map(u => {
              const isOpen = !!expandedUsers[u.user_code]
              const sandboxState = userSandboxMap[u.user_code]
              return (
                <React.Fragment key={u.id}>
                  <tr className="hover:bg-white/90 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold text-slate-900">{u.user_code}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-800">{u.user_name}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-800">{u.sandbox_quota ?? '-'}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-800">{u.effective_quota ?? '-'}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input type="number" min={0} value={quotaEdits[u.user_code] ?? ''} onChange={(e) => onChangeQuotaEdit(u.user_code, e.target.value)} placeholder={t.k8s.sandboxUserManagementTable.leaveEmptyToClear} className="w-36 px-3 py-2 bg-white text-slate-800 border border-white/50 rounded-lg focus:outline-none" />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <div className="flex flex-wrap gap-3">
                        <button onClick={() => saveUserQuota(u.user_code)} disabled={quotaLoading} className="px-3 py-1.5 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50">{t.k8s.sandboxUserManagementTable.save}</button>
                        <button onClick={() => { onChangeQuotaEdit(u.user_code, ''); saveUserQuota(u.user_code) }} disabled={quotaLoading} className="px-3 py-1.5 bg-white text-slate-700 rounded-lg hover:bg-slate-50 border border-white/60 disabled:opacity-50">{t.k8s.sandboxUserManagementTable.clear}</button>
                        <button onClick={() => toggleUserSandboxes(u.user_code)} className={`px-3 py-1.5 rounded-lg border ${isOpen ? 'bg-indigo-600 text-white border-indigo-600' : 'bg-indigo-50 text-indigo-700 border-indigo-200'} hover:shadow`}>{isOpen ? t.k8s.sandboxUserManagementTable.collapseSandbox : t.k8s.sandboxUserManagementTable.viewSandbox}</button>
                      </div>
                    </td>
                  </tr>
                  {isOpen && (
                    <tr>
                      <td colSpan={6} className="px-6 pb-6">
                        <div className="mt-2 rounded-2xl bg-white/50 backdrop-blur-xl border border-white/40 p-4 shadow ring-1 ring-white/30">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 rounded-xl bg-indigo-100 text-indigo-700 flex items-center justify-center shadow">
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14-4H3m16 8H5m14-4H3" /></svg>
                              </div>
                              <h4 className="text-sm font-bold text-indigo-900">{u.user_code} {t.k8s.sandboxUserManagementTable.sandboxList}</h4>
                            </div>
                            <div className="flex items-center gap-2">
                              <button onClick={() => loadUserSandboxes(u.user_code)} className="text-xs px-3 py-1 rounded-lg bg-indigo-600 text-white hover:bg-indigo-700">{t.k8s.sandboxUserManagementTable.refresh}</button>
                            </div>
                          </div>
                          <div className="bg-white/30 backdrop-blur-xl rounded-xl shadow border border-white/40 overflow-hidden ring-1 ring-white/30">
                            {sandboxState?.loading ? (
                              <div className="py-10 text-center text-gray-500">{t.k8s.sandboxUserManagementTable.loading}</div>
                            ) : (sandboxState?.items?.length || 0) === 0 ? (
                                                              <div className="py-10 text-center text-gray-500">{t.k8s.sandboxUserManagementTable.noSandbox}</div>
                            ) : (
                              <div className="overflow-x-auto">
                                <table className="w-full">
                                  <thead className="bg-white/60">
                                    <tr>
                                                                             <th className="px-4 py-2 text-left text-xs font-bold text-slate-600 uppercase">{t.k8s.sandboxUserManagementTable.sandboxName}</th>
                                      <th className="px-4 py-2 text-left text-xs font-bold text-slate-600 uppercase">{t.k8s.sandboxUserManagementTable.repository}</th>
                                      <th className="px-4 py-2 text-left text-xs font-bold text-slate-600 uppercase">{t.k8s.sandboxUserManagementTable.branch}</th>
                                      <th className="px-4 py-2 text-left text-xs font-bold text-slate-600 uppercase">{t.k8s.sandboxUserManagementTable.status}</th>
                                      <th className="px-4 py-2 text-left text-xs font-bold text-slate-600 uppercase">{t.k8s.sandboxUserManagementTable.podIp}</th>
                                      <th className="px-4 py-2 text-left text-xs font-bold text-slate-600 uppercase">{t.k8s.sandboxUserManagementTable.actions}</th>
                                    </tr>
                                  </thead>
                                  <tbody className="bg-white/70 divide-y divide-slate-100">
                                    {sandboxState?.items?.map((sb, idx) => {
                                      const gitUrl = sb.git_url || (sb.annotations?.['git.url'] ?? '')
                                      const branch = sb.branch || (sb.annotations?.['git.branch'] ?? '')
                                      const repo = gitUrl ? (() => { try { const u = gitUrl.replace('.git', ''); const parts = u.includes('://') ? u.split('://')[1].split('/') : (u.includes('@') && u.includes(':') ? u.split(':')[1].split('/') : u.split('/')); if (parts.length >= 2) return { owner: parts[parts.length-2], repo: parts[parts.length-1] }; const pathParts = gitUrl.split('/').filter(p => p && p !== 'git'); if (pathParts.length >= 2) return { owner: pathParts[pathParts.length-2], repo: pathParts[pathParts.length-1].replace('.git','') }; return { owner: '-', repo: '-' } } catch { return { owner: '-', repo: '-' } } })() : { owner: '-', repo: '-' }
                                      const status = sb.detailed_status || sb.status || '-'
                                      return (
                                        <tr key={idx} className="hover:bg-white/90 transition-colors">
                                          <td className="px-4 py-2 text-sm text-slate-800">{sb.name || '-'}</td>
                                          <td className="px-4 py-2 text-sm text-slate-800">{repo.owner}/{repo.repo}</td>
                                          <td className="px-4 py-2 text-sm text-slate-800">{branch || '-'}</td>
                                          <td className="px-4 py-2 text-sm">
                                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-semibold ${status === 'READY' ? 'bg-green-100 text-green-800' : status === 'FAILED' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}`}>{status}</span>
                                          </td>
                                          <td className="px-4 py-2 text-sm text-slate-800">{sb.pod_ip || '-'}</td>
                                          <td className="px-4 py-2 text-sm">
                                            <button onClick={() => deleteUserSandbox(u.user_code, sb)} className="px-3 py-1 rounded-lg bg-red-50 text-red-700 hover:bg-red-100 border border-red-200">{t.k8s.sandboxUserManagementTable.delete}</button>
                                          </td>
                                        </tr>
                                      )
                                    })}
                                  </tbody>
                                </table>
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              )
            })}
            {quotaUsers.length === 0 && (
              <tr><td colSpan={6} className="px-6 py-10 text-center text-gray-500">{t.k8s.sandboxUserManagementTable.noData}</td></tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  )
} 