import React from 'react'
import { useLanguage } from '@/contexts/LanguageContext'

interface CreateSandboxModalProps {
  show: boolean
  loading: boolean
  userCode: string
  setUserCode: (v: string) => void
  gitUrl: string
  setGitUrl: (v: string) => void
  branch: string
  setBranch: (v: string) => void
  onCreate: () => void
  onClose: () => void
}

export function CreateSandboxModal(props: CreateSandboxModalProps) {
  const { messages: t } = useLanguage()
  const { show, loading, userCode, setUserCode, gitUrl, setGitUrl, branch, setBranch, onCreate, onClose } = props
  if (!show) return null
  return (
    <div className="animate-[fadeIn_200ms_ease-out]">
      <style jsx>{`
        @keyframes fadeIn { from { opacity: 0; transform: scale(0.98) translateY(4px) } to { opacity: 1; transform: scale(1) translateY(0) } }
      `}</style>
      <div className="w-full max-w-[90vw] md:max-w-5xl lg:max-w-6xl xl:max-w-7xl mx-auto p-px rounded-3xl bg-gradient-to-br from-white/60 via-white/30 to-white/10">
        <div className="bg-white/90 backdrop-blur-xl rounded-3xl shadow-2xl w-full max-w-[90vw] md:max-w-5xl lg:max-w-6xl xl:max-w-7xl md:min-w-[720px] max-h-[90vh] overflow-y-auto border border-white/30 ring-1 ring-white/40 shadow-[inset_0_1px_0_rgba(255,255,255,0.5)]">
          <div className="p-6 md:p-8">
            <h3 className="text-xl font-bold text-slate-900 mb-6 flex items-center gap-3"><svg className="w-6 h-6 text-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" /></svg>{t.k8s.createSandbox.title}</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-semibold text-slate-700 mb-2">{t.k8s.createSandbox.employeeId} *</label>
                <input type="text" value={userCode} onChange={(e) => setUserCode(e.target.value)} placeholder={t.k8s.createSandbox.enterEmplyeeId} className="w-full px-4 py-3 bg-white/70 border border-white/60 rounded-xl focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all shadow-sm" />
              </div>
              <div>
                <label className="block text-sm font-semibold text-slate-700 mb-2">{t.k8s.createSandbox.gitUrl} *</label>
                <input type="text" value={gitUrl} onChange={(e) => setGitUrl(e.target.value)} placeholder={t.k8s.createSandbox.enterGitUrl} className="w-full px-4 py-3 bg-white/70 border border-white/60 rounded-xl focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all shadow-sm" />
              </div>
              <div>
                <label className="block text-sm font-semibold text-slate-700 mb-2">{t.k8s.createSandbox.branch}</label>
                <input type="text" value={branch} onChange={(e) => setBranch(e.target.value)} placeholder={t.k8s.createSandbox.enterBranch} className="w-full px-4 py-3 bg-white/70 border border-white/60 rounded-xl focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all shadow-sm" />
              </div>
            </div>
            <div className="flex gap-4 mt-8">
              <button onClick={onCreate} disabled={loading} className="flex-1 px-6 py-3 bg-gradient-to-r from-cyan-500 to-blue-600 text-white rounded-xl hover:shadow-lg hover:from-cyan-600 hover:to-blue-700 disabled:opacity-50 font-semibold transition-all ring-1 ring-white/40 shadow-[inset_0_1px_0_rgba(255,255,255,0.4)]">
                {loading ? t.k8s.createSandbox.creating : t.k8s.createSandbox.create}
              </button>
              <button onClick={onClose} className="flex-1 px-6 py-3 bg-white/70 text-slate-700 rounded-xl hover:bg-white/80 font-semibold transition-all ring-1 ring-white/40">
                {t.k8s.createSandbox.cancel}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CreateSandboxModal 