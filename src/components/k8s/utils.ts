export function formatTime(timeStr?: string): string {
  if (!timeStr) return '-'
  return new Date(timeStr).toLocaleString('zh')
}

export function formatCpuUsage(cpuUsage: number): string {
  if (cpuUsage >= 1000) {
    return `${(cpuUsage / 1000).toFixed(2)} cores`
  } else {
    return `${cpuUsage.toFixed(0)} m`
  }
}

export function formatMemoryUsage(memoryKiB: number): string {
  if (memoryKiB >= 1024 * 1024) {
    return `${(memoryKiB / (1024 * 1024)).toFixed(2)} Gi`
  } else if (memoryKiB >= 1024) {
    return `${(memoryKiB / 1024).toFixed(2)} Mi`
  } else {
    return `${memoryKiB.toFixed(0)} Ki`
  }
}

export function getStatusStyle(status: string): string {
  const baseClasses = 'inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold'
  switch ((status || '').toLowerCase()) {
    case 'running':
      return `${baseClasses} bg-green-100 text-green-800 ring-1 ring-inset ring-green-600/20`
    case 'succeeded':
      return `${baseClasses} bg-blue-100 text-blue-800 ring-1 ring-inset ring-blue-600/20`
    case 'failed':
      return `${baseClasses} bg-red-100 text-red-800 ring-1 ring-inset ring-red-600/20`
    case 'pending':
      return `${baseClasses} bg-yellow-100 text-yellow-800 ring-1 ring-inset ring-yellow-600/20`
    default:
      return `${baseClasses} bg-gray-100 text-gray-800 ring-1 ring-inset ring-gray-600/20`
  }
}

export function extractRepoInfo(gitUrl: string): { owner: string; repo: string } {
  try {
    const urlClean = gitUrl.replace('.git', '')
    let parts: string[] = []

    if (urlClean.includes('://')) {
      const urlPath = urlClean.split('://')[1]
      parts = urlPath.split('/')
    } else if (urlClean.includes('@') && urlClean.includes(':')) {
      const sshPath = urlClean.split(':')[1]
      parts = sshPath.split('/')
    } else {
      parts = urlClean.split('/')
    }

    if (parts.length >= 2) {
      const owner = parts[parts.length - 2]
      const repo = parts[parts.length - 1]
      return { owner, repo }
    }

    const pathParts = gitUrl.split('/').filter(part => part && part !== 'git')
    if (pathParts.length >= 2) {
      return {
        owner: pathParts[pathParts.length - 2],
        repo: pathParts[pathParts.length - 1].replace('.git', '')
      }
    }

    return { owner: 'Unknown', repo: gitUrl.split('/').pop()?.replace('.git', '') || gitUrl }
  } catch {
    return { owner: 'Unknown', repo: gitUrl }
  }
} 