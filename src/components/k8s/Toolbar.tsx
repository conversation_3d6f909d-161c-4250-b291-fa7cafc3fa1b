import React from 'react'
import { JobInfo } from './types'
import { useLanguage } from '@/contexts/LanguageContext'

interface ToolbarProps {
  loading: boolean
  filterUserCode: string
  setFilterUserCode: (v: string) => void
  onRefreshJobs: () => void
  onBatchQueryStatus: (jobs: JobInfo[]) => Promise<void>
  jobs: JobInfo[]
}

export function Toolbar(props: ToolbarProps) {
  const { messages: t } = useLanguage()
  const {
    loading,
    filterUserCode,
    setFilterUserCode,
    onRefreshJobs,
    onBatchQueryStatus,
    jobs
  } = props

  return (
    <div className="bg-white/10 backdrop-blur-xl rounded-2xl shadow-lg p-5 border border-white/20">
      <div className="flex items-end justify-between gap-4 flex-wrap">
        <div className="flex items-center gap-3">
          <input
            type="text"
            value={filterUserCode}
            onChange={(e) => setFilterUserCode(e.target.value)}
            placeholder={t.k8s.toolbar.filterUserCode}
            className="w-48 md:w-56 lg:w-64 px-3 py-2 bg-white/5 border border-white/20 rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all shadow-sm"
          />
          <button onClick={onRefreshJobs} disabled={loading} className="px-4 py-2.5 bg-gradient-to-r from-cyan-500/80 to-blue-600/80 text-white rounded-xl hover:shadow-lg hover:from-cyan-500 hover:to-blue-600 disabled:opacity-50 flex items-center gap-2 shadow-md transition-all">
            {loading ? <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" /> : <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" /></svg>}
            {t.k8s.toolbar.refresh}
          </button>
        </div>
        <div className="flex items-center gap-3">
          <button 
            onClick={() => onBatchQueryStatus(jobs)}
            disabled={loading || jobs.length === 0}
            className="px-4 py-2.5 bg-gradient-to-r from-blue-600/80 to-indigo-700/80 text-white rounded-xl hover:shadow-lg hover:from-blue-600 hover:to-indigo-700 disabled:opacity-50 flex items-center gap-2 shadow-md transition-all"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
            {t.k8s.toolbar.batchQueryStatus}
          </button>
        </div>
      </div>
    </div>
  )
}

export default Toolbar 