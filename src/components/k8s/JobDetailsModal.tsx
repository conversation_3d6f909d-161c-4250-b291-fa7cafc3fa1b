import React, { useEffect, useMemo, useState } from 'react'
import { JobInfo, SandboxStatus } from './types'
import { formatCpuUsage, formatMemoryUsage, formatTime, getStatusStyle } from './utils'
import { getSandboxStatusDescription, getSandboxStatusStyle } from './status'
import { useLanguage } from '@/contexts/LanguageContext'
import { useToast } from '@/contexts/ToastContext'

interface JobDetailsModalProps {
  job: JobInfo
  metricsRefreshInterval: NodeJS.Timeout | null
  isMonitoring: boolean
  lastMetricsUpdate: string
  metricsError: string
  startMetricsRefresh: (jobName: string) => void
  stopMetricsRefresh: () => void
  onClose: () => void
  getSandboxStatus: (userCode: string, gitUrl: string, branch: string, jobName?: string) => SandboxStatus | null
  statusLoading: Record<string, boolean>
  querySandboxStatus: (userCode: string, gitUrl: string, branch: string) => void
  // 更新父组件中的Job信息
  onJobUpdated: (job: JobInfo) => void
  // Kubernetes命名空间
  k8sNamespace: string
  // Kubernetes主控地址
  k8sMasterUrl: string
  // 研发云门户基础地址
  portalBaseUrl: string
}

export function JobDetailsModal(props: JobDetailsModalProps) {
  const { job: selectedJob, metricsRefreshInterval, isMonitoring, lastMetricsUpdate, metricsError, startMetricsRefresh, stopMetricsRefresh, onClose, getSandboxStatus, statusLoading, querySandboxStatus, onJobUpdated, k8sNamespace, k8sMasterUrl, portalBaseUrl } = props
  const { messages: t, language } = useLanguage();
  const { addToast } = useToast();

  // 元数据编辑的单行结构
  type MetadataRow = { id: string; key: string; value: string }

  // 元数据编辑状态
  const [isEditingMetadata, setIsEditingMetadata] = useState(false)
  const [labelRows, setLabelRows] = useState<MetadataRow[]>([])
  const [annotationRows, setAnnotationRows] = useState<MetadataRow[]>([])
  const [savingMetadata, setSavingMetadata] = useState(false)

  // Pod 与容器选择状态
  const [selectedPodName, setSelectedPodName] = useState<string>('')
  const [selectedContainerName, setSelectedContainerName] = useState<string>('')

  // 将字典数据转换为可编辑的行
  const convertRecordToRows = (record?: Record<string, string>): MetadataRow[] => {
    return Object.entries(record || {}).map(([key, value], index) => ({
      id: `${key}-${index}`,
      key,
      value
    }))
  }

  // 初始化或切换Job时同步编辑表单
  useEffect(() => {
    setLabelRows(convertRecordToRows(selectedJob.labels))
    setAnnotationRows(convertRecordToRows(selectedJob.annotations))
    setIsEditingMetadata(false)
    setSavingMetadata(false)

    const firstPod = selectedJob.pods?.[0]
    setSelectedPodName(firstPod?.name || '')
    const defaultContainerName = firstPod?.container_statuses?.[0]?.name || firstPod?.containers?.[0]?.name || ''
    setSelectedContainerName(defaultContainerName)
  }, [selectedJob])

  // 标签行修改
  const handleLabelRowChange = (index: number, field: 'key' | 'value', value: string) => {
    setLabelRows(prev => {
      const next = [...prev]
      next[index] = { ...next[index], [field]: value }
      return next
    })
  }

  // 注解行修改
  const handleAnnotationRowChange = (index: number, field: 'key' | 'value', value: string) => {
    setAnnotationRows(prev => {
      const next = [...prev]
      next[index] = { ...next[index], [field]: value }
      return next
    })
  }

  // 新增标签行
  const handleAddLabelRow = () => {
    setLabelRows(prev => [...prev, { id: `label-${Date.now()}-${prev.length}`, key: '', value: '' }])
  }

  // 新增注解行
  const handleAddAnnotationRow = () => {
    setAnnotationRows(prev => [...prev, { id: `annotation-${Date.now()}-${prev.length}`, key: '', value: '' }])
  }

  // 删除标签行
  const handleRemoveLabelRow = (index: number) => {
    setLabelRows(prev => prev.filter((_, idx) => idx !== index))
  }

  // 删除注解行
  const handleRemoveAnnotationRow = (index: number) => {
    setAnnotationRows(prev => prev.filter((_, idx) => idx !== index))
  }

  // 取消编辑还原数据
  const handleCancelMetadataEdit = () => {
    setLabelRows(convertRecordToRows(selectedJob.labels))
    setAnnotationRows(convertRecordToRows(selectedJob.annotations))
    setIsEditingMetadata(false)
  }

  // 启动编辑模式
  const handleStartMetadataEdit = () => {
    setIsEditingMetadata(true)
    if (labelRows.length === 0) {
      setLabelRows([{ id: `label-${Date.now()}`, key: '', value: '' }])
    }
    if (annotationRows.length === 0) {
      setAnnotationRows([{ id: `annotation-${Date.now()}`, key: '', value: '' }])
    }
  }

  // 计算容器下拉选项
  const containerOptions = useMemo(() => {
    const targetPod = selectedJob.pods?.find(pod => pod.name === selectedPodName) || selectedJob.pods?.[0]
    if (!targetPod) {
      return []
    }
    const fromStatuses = targetPod.container_statuses?.map(item => item.name) || []
    const fromSpecs = targetPod.containers?.map(item => item.name) || []
    return Array.from(new Set([...fromStatuses, ...fromSpecs]))
  }, [selectedJob.pods, selectedPodName])

  // 切换Pod后更新默认容器
  const handleSelectPod = (podName: string) => {
    setSelectedPodName(podName)
    const targetPod = selectedJob.pods?.find(pod => pod.name === podName)
    const defaultContainerName = targetPod?.container_statuses?.[0]?.name || targetPod?.containers?.[0]?.name || ''
    setSelectedContainerName(defaultContainerName)
  }

  // 构造跳板机跳转链接
  const openPortalForContainer = () => {
    const jumpLabel = t.k8s.jobsTable.loginContainer || t.k8s.jobsTable.jumpServer
    if (!portalBaseUrl || !k8sNamespace || !k8sMasterUrl) {
      addToast({
        title: jumpLabel,
        message: t.k8s.jobsTable.portalConfigMissing || '未配置门户参数',
        type: 'error'
      })
      return
    }

    const targetPod = selectedJob.pods?.find(pod => pod.name === selectedPodName) || selectedJob.pods?.find(pod => pod.pod_ip) || selectedJob.pods?.[0]
    const containerStatuses = targetPod?.container_statuses || []
    const matchedStatus = containerStatuses.find(item => item.name === selectedContainerName && item.container_id) || containerStatuses.find(item => item.container_id)
    const matchedSpec = targetPod?.containers?.find(item => item.name === (matchedStatus?.name || selectedContainerName)) || targetPod?.containers?.[0]

    const containerName = matchedStatus?.name || matchedSpec?.name
    const containerId = matchedStatus?.container_id
    const podName = targetPod?.name
    const ip = targetPod?.pod_ip || targetPod?.host_ip

    if (!targetPod || !containerName || !containerId || !podName || !ip) {
      addToast({
        title: jumpLabel,
        message: t.k8s.jobsTable.portalDataMissing || '缺少容器信息，无法跳转',
        type: 'error'
      })
      return
    }

    const base = portalBaseUrl.endsWith('/') ? portalBaseUrl : `${portalBaseUrl}/`
    const params: Array<[string, string]> = [
      ['applicationName', selectedJob.name || 'sandbox'],
      ['ip', ip],
      ['containerName', containerName],
      ['containerId', containerId],
      ['podName', podName],
      ['jobName', selectedJob.name],
      ['privileger', 'DEEP_WIKI'],
      ['k8sMasterUrl', k8sMasterUrl],
      ['k8sNamespace', k8sNamespace],
      ['applicationType', 'STATELESS'],
      ['concise', 'true'],
      ['url', 'zcm-tool/modules/newconsole/views/newConsole'],
      ['channel', 'k8s'],
      ['cmd', 'sudo+-E+-u+wct+env+HOME%3D%2Fhome%2Fwct+bash+-c+%27cd+%2Fdata%2Fworkspace+%26%26+wct-cli%27']
    ]

    const query = params
      .filter(([, value]) => value !== undefined && value !== null)
      .map(([key, value]) => {
        if (key === 'k8sMasterUrl' || key === 'url') {
          return `${key}=${value}`
        }
        return `${key}=${encodeURIComponent(value ?? '')}`
      })
      .join('&')

    window.open(`${base}?${query}`, '_blank')
  }

  // 保存元数据改动
  const handleSaveMetadata = async () => {
    const buildMap = (rows: MetadataRow[]) => rows.reduce<Record<string, string>>((acc, row) => {
      const key = row.key.trim()
      if (key) {
        acc[key] = row.value
      }
      return acc
    }, {})

    const nextLabels = buildMap(labelRows)
    const nextAnnotations = buildMap(annotationRows)

    const currentLabelKeys = new Set(Object.keys(selectedJob.labels || {}))
    const currentAnnotationKeys = new Set(Object.keys(selectedJob.annotations || {}))
    const newLabelKeys = new Set(Object.keys(nextLabels))
    const newAnnotationKeys = new Set(Object.keys(nextAnnotations))

    const removeLabels = Array.from(currentLabelKeys).filter(key => !newLabelKeys.has(key))
    const removeAnnotations = Array.from(currentAnnotationKeys).filter(key => !newAnnotationKeys.has(key))

    if (!Object.keys(nextLabels).length && !Object.keys(nextAnnotations).length && removeLabels.length === 0 && removeAnnotations.length === 0) {
      addToast({
        title: t.k8s.JobDetail.metadataManagement,
        message: t.k8s.JobDetail.metadataNoChanges || '暂无变更',
        type: 'info'
      })
      setIsEditingMetadata(false)
      return
    }

    const payload: Record<string, unknown> = {}
    if (Object.keys(nextLabels).length) {
      payload.labels = nextLabels
    }
    if (Object.keys(nextAnnotations).length) {
      payload.annotations = nextAnnotations
    }
    if (removeLabels.length) {
      payload.remove_labels = removeLabels
    }
    if (removeAnnotations.length) {
      payload.remove_annotations = removeAnnotations
    }

    setSavingMetadata(true)
    try {
      const response = await fetch(`/api/k8s/jobs/${selectedJob.name}/metadata`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })
      const result = await response.json()
      if (result.success && result.data) {
        addToast({
          title: t.k8s.JobDetail.metadataManagement,
          message: t.k8s.JobDetail.metadataSaveSuccess || '元数据已更新',
          type: 'success'
        })
        onJobUpdated(result.data)
        setLabelRows(convertRecordToRows(result.data.labels))
        setAnnotationRows(convertRecordToRows(result.data.annotations))
        setIsEditingMetadata(false)
      } else {
        addToast({
          title: t.k8s.JobDetail.metadataManagement,
          message: result.error || t.k8s.JobDetail.metadataSaveFailed || '元数据更新失败',
          type: 'error'
        })
      }
    } catch (error) {
      addToast({
        title: t.k8s.JobDetail.metadataManagement,
        message: error instanceof Error ? error.message : (t.k8s.JobDetail.metadataSaveFailed || '元数据更新失败'),
        type: 'error'
      })
    } finally {
      setSavingMetadata(false)
    }
  }

  return (
    <div className="animate-[fadeIn_200ms_ease-out]">
      <style jsx>{`
        @keyframes fadeIn { from { opacity: 0; transform: scale(0.98) translateY(6px) } to { opacity: 1; transform: scale(1) translateY(0) } }
      `}</style>
      <div className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl w-full max-w-7xl max-h-[90vh] border border-white/20 flex flex-col">
        <div className="flex-shrink-0 p-6 border-b border-gray-200 bg-white/95 backdrop-blur-sm">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <div className="flex items-center gap-4 mb-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center shadow-lg">
                  <svg className="w-7 h-7 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-3xl font-bold text-gray-900">{t.k8s.JobDetail.title}</h3>
                  <p className="text-gray-500 mt-1 break-all font-mono text-sm bg-gray-50 px-3 py-1 rounded-lg">{selectedJob.name}</p>
                </div>
              </div>
            </div>
            <button 
              onClick={() => { stopMetricsRefresh(); onClose() }}
              className="w-10 h-10 bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto p-6">
          <div className="space-y-8">
            <div className="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-3xl border border-blue-200/60 p-6 shadow-lg">
              <div className="flex justify-between items-center mb-6">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-xl">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-slate-900">{t.k8s.JobDetail.realTimeResourceUsage}</h3>
                    <p className="text-slate-600 mt-1">{t.k8s.JobDetail.jobCpuAndMemoryRealTimeMonitoring}</p>
                    {isMonitoring && lastMetricsUpdate && (
                      <p className="text-xs text-slate-500 mt-2">{t.k8s.JobDetail.lastUpdated}: {lastMetricsUpdate}</p>
                    )}
                    {metricsError && (
                      <p className="text-xs text-red-500 mt-2">{metricsError}</p>
                    )}
                  </div>
                </div>
                <button 
                  onClick={() => {
                    if (metricsRefreshInterval) {
                      stopMetricsRefresh()
                    }
                    else {
                      startMetricsRefresh(selectedJob.name)
                    }
                  }}
                  className={`px-6 py-3 rounded-2xl text-sm font-bold transition-all duration-200 flex items-center gap-3 ${
                    metricsRefreshInterval 
                      ? 'bg-red-100 text-red-700 hover:bg-red-200 border-2 border-red-200 shadow-lg' 
                      : 'bg-blue-100 text-blue-700 hover:bg-blue-200 border-2 border-blue-200 shadow-lg'
                  }`}
                >
                  {metricsRefreshInterval ? (
                    <>
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-6.219-8.56" />
                      </svg>
                      {t.k8s.JobDetail.stopMonitoring}
                    </>
                  ) : (
                    <>
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                      {t.k8s.JobDetail.startMonitoring}
                    </>
                  )}
                </button>
              </div>

              {selectedJob.metrics ? (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="bg-gradient-to-br from-blue-500 to-blue-600 p-6 rounded-2xl text-white shadow-xl">
                      <div className="flex items-center justify-between mb-3">
                        <h6 className="text-lg font-bold opacity-90">{t.k8s.JobDetail.totalCpuUsage}</h6>
                        <svg className="w-6 h-6 opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                      </div>
                      <p className="text-4xl font-bold mb-2">{formatCpuUsage(selectedJob.metrics.total_cpu_usage)}</p>
                      {selectedJob.pods && selectedJob.pods[0]?.resources?.total_limits?.cpu && (
                        <p className="text-sm opacity-80">{t.k8s.JobDetail.limit}: {selectedJob.pods[0].resources.total_limits.cpu}</p>
                      )}
                    </div>
                    <div className="bg-gradient-to-br from-green-500 to-green-600 p-6 rounded-2xl text-white shadow-xl">
                      <div className="flex items-center justify-between mb-3">
                        <h6 className="text-lg font-bold opacity-90">{t.k8s.JobDetail.totalMemoryUsage}</h6>
                        <svg className="w-6 h-6 opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                        </svg>
                      </div>
                      <p className="text-4xl font-bold mb-2">{formatMemoryUsage(selectedJob.metrics.total_memory_usage)}</p>
                      {selectedJob.pods && selectedJob.pods[0]?.resources?.total_limits?.memory && (
                        <p className="text-sm opacity-80">{t.k8s.JobDetail.limit}: {selectedJob.pods[0].resources.total_limits.memory}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <h6 className="text-xl font-bold text-slate-800 mb-4 flex items-center gap-3">
                      <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14-4H3m16 8H5m14-4H3" />
                      </svg>
                      {t.k8s.JobDetail.podDetailedUsage}
                    </h6>
                    <div className="space-y-4">
                      {selectedJob.metrics.pods.map((podMetrics, index) => (
                        <div key={index} className="bg-white/90 backdrop-blur-sm rounded-2xl border border-blue-200/60 p-6 shadow-lg">
                          <div className="flex justify-between items-center mb-4">
                            <span className="text-lg font-bold text-slate-800">{podMetrics.pod_name}</span>
                            <span className="text-sm text-slate-500 bg-blue-50 px-3 py-1 rounded-lg font-bold">
                              {formatTime(podMetrics.timestamp)}
                            </span>
                          </div>
                          {podMetrics.containers.length > 0 && (
                            <div className="space-y-3">
                              {podMetrics.containers.map((container, cIndex) => (
                                <div key={cIndex} className="bg-slate-50/80 rounded-xl p-4 border border-slate-200/60">
                                  <div className="flex items-center justify-between">
                                    <span className="font-semibold text-slate-700">{container.name}</span>
                                    <div className="flex gap-3 text-sm">
                                      <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-lg font-bold">
                                        {t.k8s.JobDetail.cpu}: {formatCpuUsage(container.cpu_usage)}
                                      </span>
                                      <span className="bg-green-100 text-green-800 px-3 py-1 rounded-lg font-bold">
                                        {t.k8s.JobDetail.memory}: {formatMemoryUsage(container.memory_usage)}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-slate-700 mb-3">{t.k8s.JobDetail.noMonitoringData}</h3>
                  <p className="text-slate-500">{t.k8s.JobDetail.clickStartMonitoring}</p>
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
              <div className="space-y-6">
                <div className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl border border-indigo-200/60 p-6 shadow-sm">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7h18M3 12h18M3 17h18" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-2xl font-bold text-slate-900">{t.k8s.JobDetail.volumeMounts}</h4>
                      <p className="text-sm text-slate-600 mt-1">{t.k8s.JobDetail.volumeMountsDescription}</p>
                    </div>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-white/60">
                        <tr>
                          <th className="px-4 py-2 text-left text-xs font-bold text-gray-600 uppercase">{t.k8s.JobDetail.volumeName}</th>
                          <th className="px-4 py-2 text-left text-xs font-bold text-gray-600 uppercase">{t.k8s.JobDetail.containerPath}</th>
                          <th className="px-4 py-2 text-left text-xs font-bold text-gray-600 uppercase">{t.k8s.JobDetail.hostPath}</th>
                          <th className="px-4 py-2 text-left text-xs font-bold text-gray-600 uppercase">{t.k8s.JobDetail.permissions}</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-indigo-100">
                        {(selectedJob.mounts || []).length > 0 ? (
                          selectedJob.mounts!.map((m, idx) => (
                            <tr key={idx} className="hover:bg-indigo-50/40">
                              <td className="px-4 py-2 text-sm font-semibold text-slate-800">{m.volume_name}</td>
                              <td className="px-4 py-2 text-sm font-mono text-slate-800">{m.container_path}</td>
                              <td className="px-4 py-2 text-sm font-mono text-slate-800">{m.host_path || '-'}</td>
                              <td className="px-4 py-2 text-sm">
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-semibold ${m.read_only ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`}>
                                  {m.read_only ? t.k8s.JobDetail.readOnly : t.k8s.JobDetail.readWrite}
                                </span>
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td className="px-4 py-6 text-center text-sm text-gray-500" colSpan={4}>{t.k8s.JobDetail.noMountInfo}</td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-slate-50 to-gray-50 rounded-2xl border border-slate-200/60 p-6 shadow-sm">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-2xl font-bold text-slate-900">{t.k8s.JobDetail.basicInfo}</h4>
                      <p className="text-sm text-slate-500 mt-1">{t.k8s.JobDetail.basicInfoDescription}</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="bg-white/80 backdrop-blur-sm rounded-xl border border-slate-200/60 p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide">{t.k8s.JobDetail.userCode}</span>
                        </div>
                        <p className="font-bold text-slate-900 text-lg">{(() => { const name = selectedJob.user_name || selectedJob.annotations?.['user.name'] || ''; const code = selectedJob.user_code || '-'; return name ? `${name}(${code})` : code })()}</p>
                      </div>
                      <div className="bg-white/80 backdrop-blur-sm rounded-xl border border-slate-200/60 p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide">{t.k8s.JobDetail.branch}</span>
                        </div>
                        <p className="font-bold text-slate-900 text-lg">{selectedJob.branch}</p>
                      </div>
                      <div className="bg-white/80 backdrop-blur-sm rounded-xl border border-slate-200/60 p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                          <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide">{t.k8s.JobDetail.jobStatus}</span>
                        </div>
                        <span className={`inline-block px-4 py-2 rounded-xl font-bold text-sm ${getStatusStyle(selectedJob.status)}`}>{selectedJob.status}</span>
                      </div>
                      <div className="bg-white/80 backdrop-blur-sm rounded-xl border border-slate-200/60 p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="w-2 h-2 bg-indigo-500 rounded-full"></div>
                          <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide">{t.k8s.JobDetail.sandboxStatus}</span>
                        </div>
                        {(() => {
                          const sandboxStatus = getSandboxStatus(selectedJob.user_code, selectedJob.git_url, selectedJob.branch, selectedJob.name || '')
                          const statusKey = selectedJob.name || `${selectedJob.user_code}-${selectedJob.git_url}-${selectedJob.branch}`
                          const isLoading = statusLoading[statusKey]
                          return (
                            <div className="space-y-2">
                              {sandboxStatus ? (
                                <div className="flex items-center gap-2">
                                  <span className={`inline-block px-3 py-1 rounded-xl font-bold text-sm ${getSandboxStatusStyle(sandboxStatus.status)}`}>{getSandboxStatusDescription(sandboxStatus.status, language)}</span>
                                  <button onClick={() => querySandboxStatus(selectedJob.user_code, selectedJob.git_url, selectedJob.branch)} disabled={isLoading} className="text-xs text-blue-600 hover:text-blue-800 hover:underline transition-colors disabled:opacity-50">{isLoading ? t.k8s.JobDetail.querying : t.k8s.JobDetail.refresh}</button>
                                </div>
                              ) : (
                                <button onClick={() => querySandboxStatus(selectedJob.user_code, selectedJob.git_url, selectedJob.branch)} disabled={isLoading} className="text-xs text-blue-600 hover:text-blue-800 hover:underline transition-colors disabled:opacity-50">{isLoading ? t.k8s.JobDetail.querying : t.k8s.JobDetail.queryStatus}</button>
                              )}
                              {sandboxStatus && <div className="text-xs text-gray-500">{sandboxStatus.message}</div>}
                            </div>
                          )
                        })()}
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div className="bg-white/80 backdrop-blur-sm rounded-xl border border-slate-200/60 p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                          <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide">{t.k8s.JobDetail.creationTime}</span>
                        </div>
                        <p className="font-bold text-slate-900 text-lg">{formatTime(selectedJob.creation_time)}</p>
                      </div>
                      <div className="bg-white/80 backdrop-blur-sm rounded-xl border border-slate-200/60 p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="w-2 h-2 bg-indigo-500 rounded-full"></div>
                          <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide">{t.k8s.JobDetail.lastAccess}</span>
                        </div>
                        <p className="font-bold text-slate-900 text-lg">{formatTime(selectedJob.last_access_time) || '-'}</p>
                      </div>
                      <div className="bg-white/80 backdrop-blur-sm rounded-xl border border-slate-200/60 p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="w-2 h-2 bg-teal-500 rounded-full"></div>
                          <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide">{t.k8s.JobDetail.namespace}</span>
                        </div>
                        <p className="font-bold text-slate-900 text-lg">{selectedJob.namespace}</p>
                      </div>
                      <div className="bg-white/80 backdrop-blur-sm rounded-xl border border-slate-200/60 p-4 space-y-4">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-cyan-500 rounded-full"></div>
                          <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide">{t.k8s.JobDetail.selectPod}</span>
                        </div>
                        {selectedJob.pods && selectedJob.pods.length > 0 ? (
                          <div className="flex flex-col xl:flex-row gap-4">
                            <div className="flex-1 space-y-2">
                              <label className="text-xs text-slate-500 font-semibold">{t.k8s.JobDetail.selectPod}</label>
                              <select
                                value={selectedPodName}
                                onChange={(e) => handleSelectPod(e.target.value)}
                                className="w-full px-3 py-2 border border-slate-200 rounded-lg text-sm text-slate-800 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                              >
                                {selectedJob.pods.map(pod => (
                                  <option key={pod.name} value={pod.name}>{pod.name}</option>
                                ))}
                              </select>
                            </div>
                            <div className="flex-1 space-y-2">
                              <label className="text-xs text-slate-500 font-semibold">{t.k8s.JobDetail.selectContainer}</label>
                              <select
                                value={selectedContainerName}
                                onChange={(e) => setSelectedContainerName(e.target.value)}
                                className="w-full px-3 py-2 border border-slate-200 rounded-lg text-sm text-slate-800 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                              >
                                {containerOptions.map(option => (
                                  <option key={option} value={option}>{option}</option>
                                ))}
                              </select>
                            </div>
                            <div className="flex items-end">
                              <button
                                onClick={openPortalForContainer}
                                disabled={!selectedContainerName}
                                className={`px-4 py-2 rounded-lg text-sm font-semibold transition-colors ${selectedContainerName ? 'bg-cyan-100 text-cyan-700 hover:bg-cyan-200' : 'bg-slate-100 text-slate-400 cursor-not-allowed'}`}
                              >
                                {t.k8s.JobDetail.jumpContainer}
                              </button>
                            </div>
                          </div>
                        ) : (
                          <p className="text-sm text-slate-500">{t.k8s.JobDetail.noPodInfo}</p>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="mt-6 pt-6 border-t border-slate-200/60">
                    <div className="bg-white/80 backdrop-blur-sm rounded-xl border border-slate-200/60 p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="w-2 h-2 bg-slate-500 rounded-full"></div>
                        <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide">{t.k8s.JobDetail.gitRepoUrl}</span>
                      </div>
                      <p className="font-mono text-sm text-slate-900 break-all bg-slate-50 px-3 py-2 rounded-lg border border-slate-200">{selectedJob.git_url}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-slate-50 to-gray-50 rounded-2xl border border-slate-200/60 p-6 shadow-sm">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-2xl font-bold text-slate-900">{t.k8s.JobDetail.sandboxStatusTitle}</h4>
                      <p className="text-sm text-slate-500 mt-1">{t.k8s.JobDetail.sandboxStatusDescription}</p>
                    </div>
                  </div>
                  {(() => {
                    const sandboxStatus = getSandboxStatus(selectedJob.user_code, selectedJob.git_url, selectedJob.branch, selectedJob.name || '')
                    const statusKey = selectedJob.name || `${selectedJob.user_code}-${selectedJob.git_url}-${selectedJob.branch}`
                    const isLoading = statusLoading[statusKey]
                    return (
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            {sandboxStatus ? (
                              <span className={`inline-block px-4 py-2 rounded-xl font-bold text-sm ${getSandboxStatusStyle(sandboxStatus.status)}`}>{getSandboxStatusDescription(sandboxStatus.status, language)}</span>
                            ) : (
                              <span className="text-sm text-gray-500">{t.k8s.JobDetail.notQueried}</span>
                            )}
                          </div>
                          <button onClick={() => querySandboxStatus(selectedJob.user_code, selectedJob.git_url, selectedJob.branch)} disabled={isLoading} className={`px-4 py-2 rounded-xl text-sm font-semibold transition-all ${isLoading ? 'bg-gray-100 text-gray-500 cursor-not-allowed' : 'bg-blue-100 text-blue-700 hover:bg-blue-200'}`}>{isLoading ? t.k8s.JobDetail.querying : t.k8s.JobDetail.queryStatus}</button>
                        </div>
                        {sandboxStatus && (
                          <div className="space-y-4">
                            <div className="bg-white/80 backdrop-blur-sm rounded-xl border border-slate-200/60 p-4">
                              <div className="flex items-center gap-2 mb-2">
                                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide">{t.k8s.JobDetail.statusDescription}</span>
                              </div>
                              <p className="text-sm text-slate-900">{sandboxStatus.message}</p>
                            </div>
                            {sandboxStatus.pod_ip && (
                              <div className="bg-white/80 backdrop-blur-sm rounded-xl border border-slate-200/60 p-4">
                                <div className="flex items-center gap-2 mb-2">
                                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                  <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide">{t.k8s.JobDetail.podIp}</span>
                                </div>
                                <p className="font-mono text-sm text-slate-900">{sandboxStatus.pod_ip}</p>
                              </div>
                            )}
                            {sandboxStatus.api_response && (
                              <div className="bg-white/80 backdrop-blur-sm rounded-xl border border-slate-200/60 p-4">
                                <div className="flex items-center gap-2 mb-2">
                                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                                  <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide">{t.k8s.JobDetail.apiResponse}</span>
                                </div>
                                <p className="text-sm text-slate-900">{sandboxStatus.api_response}</p>
                              </div>
                            )}
                            <div className="bg-white/80 backdrop-blur-sm rounded-xl border border-slate-200/60 p-4">
                              <div className="flex items-center gap-2 mb-2">
                                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                                <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide">{t.k8s.JobDetail.updateTime}</span>
                              </div>
                              <p className="text-sm text-slate-900">{formatTime(sandboxStatus.timestamp)}</p>
                            </div>
                          </div>
                        )}
                      </div>
                    )
                  })()}
                </div>

                <div className="bg-gradient-to-br from-slate-50 to-gray-50 rounded-2xl border border-slate-200/60 p-6 shadow-sm">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-2xl font-bold text-slate-900">{t.k8s.JobDetail.labels}</h4>
                      <p className="text-sm text-slate-500 mt-1">{t.k8s.JobDetail.labelsDescription}</p>
                    </div>
                  </div>
                  <div className="max-h-60 overflow-y-auto">
                    {Object.entries(selectedJob.labels || {}).length > 0 ? (
                      <div className="space-y-3">
                        {Object.entries(selectedJob.labels).map(([key, value]) => (
                          <div key={key} className="bg-white/80 backdrop-blur-sm rounded-xl border border-slate-200/60 p-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                                <span className="text-sm font-semibold text-slate-700">{key}</span>
                              </div>
                              <span className="text-sm font-bold text-slate-900 bg-emerald-50 px-3 py-1 rounded-lg border border-emerald-200">{value}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <div className="w-16 h-16 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mx-auto mb-4">
                          <svg className="w-8 h-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                          </svg>
                        </div>
                        <h3 className="text-lg font-bold text-slate-700 mb-2">{t.k8s.JobDetail.noLabels}</h3>
                        <p className="text-slate-500 text-sm">{t.k8s.JobDetail.noLabelsDescription}</p>
                      </div>
                    )}
                  </div>
                </div>

                <div className="bg-gradient-to-br from-slate-50 to-gray-50 rounded-2xl border border-slate-200/60 p-6 shadow-sm">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center shadow-lg">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-2xl font-bold text-slate-900">{t.k8s.JobDetail.annotations}</h4>
                      <p className="text-sm text-slate-500 mt-1">{t.k8s.JobDetail.annotationsDescription}</p>
                    </div>
                  </div>
                  <div className="max-h-60 overflow-y-auto">
                    {Object.entries(selectedJob.annotations || {}).length > 0 ? (
                      <div className="space-y-3">
                        {Object.entries(selectedJob.annotations).map(([key, value]) => (
                          <div key={key} className="bg-white/80 backdrop-blur-sm rounded-xl border border-slate-200/60 p-4">
                            <div className="flex flex-col gap-3">
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                                <span className="text-sm font-semibold text-slate-700">{key}</span>
                              </div>
                              <span className="text-sm font-bold text-slate-900 bg-amber-50 px-3 py-2 rounded-lg border border-amber-200 break-all">{value}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <div className="w-16 h-16 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mx-auto mb-4">
                          <svg className="w-8 h-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                        <h3 className="text-lg font-bold text-slate-700 mb-2">{t.k8s.JobDetail.noAnnotations}</h3>
                        <p className="text-slate-500 text-sm">{t.k8s.JobDetail.noAnnotationsDescription}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <div className="bg-gradient-to-br from-slate-50 to-gray-50 rounded-2xl border border-slate-200/60 p-6 shadow-sm">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14-4H3m16 8H5m14-4H3" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-2xl font-bold text-slate-900">{t.k8s.JobDetail.podInfo}</h4>
                      <p className="text-sm text-slate-500 mt-1">{t.k8s.JobDetail.podInfoDescription}</p>
                    </div>
                  </div>
                  {selectedJob.pods && selectedJob.pods.length > 0 ? (
                    <div className="space-y-6 max-h-[70vh] overflow-y-auto">
                      {selectedJob.pods.map((pod, index) => (
                        <div key={index} className="bg-white/80 backdrop-blur-sm rounded-2xl border border-slate-200/60 p-6 shadow-sm">
                          <div className="flex items-center justify-between mb-6">
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 bg-gradient-to-br from-indigo-100 to-indigo-200 rounded-xl flex items-center justify-center">
                                <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                                </svg>
                              </div>
                              <div>
                                <h5 className="font-bold text-slate-900 text-xl">{pod.name}</h5>
                                <p className="text-sm text-slate-500">{t.k8s.JobDetail.podNumber.replace('{index}', String(index + 1))}</p>
                              </div>
                            </div>
                            <span className={`px-4 py-2 rounded-xl font-bold text-sm ${getStatusStyle(pod.status)}`}>{pod.status}</span>
                          </div>

                          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                            <div className="space-y-4">
                              <div className="bg-slate-50/80 rounded-xl border border-slate-200/60 p-4">
                                <div className="flex items-center gap-2 mb-2">
                                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                  <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide">{t.k8s.JobDetail.podIp}</span>
                                </div>
                                <p className="font-mono text-sm text-slate-900 font-semibold">{pod.pod_ip || '-'}</p>
                              </div>
                              <div className="bg-slate-50/80 rounded-xl border border-slate-200/60 p-4">
                                <div className="flex items-center gap-2 mb-2">
                                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                  <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide">{t.k8s.JobDetail.hostPath}</span>
                                </div>
                                <p className="font-mono text-sm text-slate-900 font-semibold">{pod.host_ip || '-'}</p>
                              </div>
                            </div>
                            <div className="space-y-4">
                              <div className="bg-slate-50/80 rounded-xl border border-slate-200/60 p-4">
                                <div className="flex items-center gap-2 mb-2">
                                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                                  <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide">{t.k8s.JobDetail.node}</span>
                                </div>
                                <p className="font-semibold text-slate-900">{pod.node_name || '-'}</p>
                              </div>
                              <div className="bg-slate-50/80 rounded-xl border border-slate-200/60 p-4">
                                <div className="flex items-center gap-2 mb-2">
                                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                                  <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide">{t.k8s.JobDetail.creationTime}</span>
                                </div>
                                <p className="font-semibold text-slate-900">{formatTime(pod.creation_time)}</p>
                              </div>
                            </div>
                          </div>

                          {pod.resources && (
                            <div className="mb-6 p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border border-blue-200/60">
                              <div className="flex items-center gap-3 mb-4">
                                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                  </svg>
                                </div>
                                <h6 className="text-lg font-bold text-blue-900">{t.k8s.JobDetail.resourceConfig}</h6>
                              </div>
                              <div className="grid grid-cols-2 gap-6">
                                <div className="bg-white/80 rounded-xl p-4 border border-blue-200/60">
                                  <div className="flex items-center gap-2 mb-3">
                                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                    <span className="text-sm font-semibold text-blue-800">{t.k8s.JobDetail.cpu}</span>
                                  </div>
                                  <div className="space-y-2">
                                    <div className="flex justify-between items-center">
                                      <span className="text-xs text-blue-600 font-medium">{t.k8s.JobDetail.request}:</span>
                                      <span className="text-sm font-bold text-slate-900">{pod.resources.total_requests.cpu}</span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                      <span className="text-xs text-blue-600 font-medium">{t.k8s.JobDetail.limit}:</span>
                                      <span className="text-sm font-bold text-slate-900">{pod.resources.total_limits.cpu}</span>
                                    </div>
                                  </div>
                                </div>
                                <div className="bg-white/80 rounded-xl p-4 border border-blue-200/60">
                                  <div className="flex items-center gap-2 mb-3">
                                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                    <span className="text-sm font-semibold text-green-800">{t.k8s.JobDetail.memory}</span>
                                  </div>
                                  <div className="space-y-2">
                                    <div className="flex justify-between items-center">
                                      <span className="text-xs text-green-600 font-medium">{t.k8s.JobDetail.request}:</span>
                                      <span className="text-sm font-bold text-slate-900">{pod.resources.total_requests.memory}</span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                      <span className="text-xs text-green-600 font-medium">{t.k8s.JobDetail.limit}:</span>
                                      <span className="text-sm font-bold text-slate-900">{pod.resources.total_limits.memory}</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}

                          {pod.container_statuses && pod.container_statuses.length > 0 && (
                            <div className="p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl border border-green-200/60">
                              <div className="flex items-center gap-3 mb-4">
                                <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                                  </svg>
                                </div>
                                <h6 className="text-lg font-bold text-green-900">{t.k8s.JobDetail.containerStatus}</h6>
                              </div>
                              <div className="space-y-3">
                                {pod.container_statuses.map((container, cIndex) => (
                                  <div key={cIndex} className="bg-white/80 rounded-xl p-4 border border-green-200/60">
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center gap-3">
                                        <div className={`w-3 h-3 rounded-full ${container.ready ? 'bg-green-500' : 'bg-red-500'}`}></div>
                                        <span className="font-semibold text-slate-900">{container.name}</span>
                                      </div>
                                      <div className="flex items-center gap-3">
                                        <span className={`text-xs font-bold px-3 py-1 rounded-full ${container.ready ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>{container.state}</span>
                                        <span className="text-xs text-slate-500 bg-slate-100 px-2 py-1 rounded-md">{t.k8s.JobDetail.restartCount}: {container.restart_count}</span>
                                      </div>
                                    </div>
                                    {container.container_id && (
                                      <div className="mt-3 text-xs text-slate-600 break-all">
                                        <span className="font-semibold text-slate-500 mr-2">{t.k8s.JobDetail.containerId}:</span>
                                        <span className="font-mono text-slate-800">{container.container_id}</span>
                                      </div>
                                    )}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-16">
                      <div className="w-20 h-20 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg className="w-10 h-10 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                        </svg>
                      </div>
                      <h3 className="text-xl font-bold text-slate-700 mb-2">{t.k8s.JobDetail.noPodInfo}</h3>
                      <p className="text-slate-500 text-sm">{t.k8s.JobDetail.noPodInfoDescription}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-slate-50 to-gray-50 rounded-2xl border border-slate-200/60 p-6 shadow-sm">
            <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between mb-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-cyan-600 rounded-xl flex items-center justify-center shadow-lg">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v4m0 8v4m4-12h4m-8 0H4m12 8h4m-8 0H4" />
                  </svg>
                </div>
                <div>
                  <h4 className="text-2xl font-bold text-slate-900">{t.k8s.JobDetail.metadataManagement}</h4>
                  <p className="text-sm text-slate-500 mt-1">{t.k8s.JobDetail.metadataDescription}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                {isEditingMetadata ? (
                  <>
                    <button
                      onClick={handleCancelMetadataEdit}
                      disabled={savingMetadata}
                      className="px-4 py-2 rounded-lg text-sm font-semibold bg-slate-100 text-slate-600 hover:bg-slate-200 transition-colors disabled:opacity-50"
                    >
                      {t.k8s.JobDetail.cancelEdit || t.common.cancel}
                    </button>
                    <button
                      onClick={handleSaveMetadata}
                      disabled={savingMetadata}
                      className={`px-4 py-2 rounded-lg text-sm font-semibold transition-colors ${savingMetadata ? 'bg-emerald-100 text-emerald-500 cursor-not-allowed' : 'bg-emerald-500 text-white hover:bg-emerald-600'}`}
                    >
                      {savingMetadata ? (t.common.saving || t.k8s.JobDetail.saveMetadata) : t.k8s.JobDetail.saveMetadata}
                    </button>
                  </>
                ) : (
                  <button
                    onClick={handleStartMetadataEdit}
                    className="px-4 py-2 rounded-lg text-sm font-semibold bg-emerald-100 text-emerald-700 hover:bg-emerald-200 transition-colors"
                  >
                    {t.k8s.JobDetail.editMetadata}
                  </button>
                )}
              </div>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                  <span className="text-sm font-semibold text-slate-700">{t.k8s.JobDetail.labels}</span>
                </div>
                {isEditingMetadata ? (
                  <div className="space-y-4 max-h-80 overflow-y-auto pr-1">
                    {labelRows.length > 0 ? labelRows.map((row, index) => (
                      <div key={row.id} className="bg-white/80 backdrop-blur-sm rounded-2xl border border-slate-200/60 p-5 space-y-4">
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                          <div>
                            <div className="text-xs font-semibold text-slate-500 uppercase tracking-wide">{t.k8s.JobDetail.metadataCurrentKey}</div>
                            <div className="text-sm font-semibold text-slate-800 break-all">{row.key || t.k8s.JobDetail.metadataNewKey}</div>
                          </div>
                          <button onClick={() => handleRemoveLabelRow(index)} className="text-xs text-rose-500 hover:text-rose-600">
                            {t.k8s.JobDetail.metadataDelete || t.common.delete}
                          </button>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <label className="text-xs font-semibold text-slate-500 uppercase tracking-wide">{t.k8s.JobDetail.metadataKeyLabel}</label>
                            <input
                              value={row.key}
                              onChange={(e) => handleLabelRowChange(index, 'key', e.target.value)}
                              placeholder={t.k8s.JobDetail.metadataKeyPlaceholder}
                              className="w-full px-3 py-2 border border-slate-200 rounded-lg bg-white text-sm text-slate-800 focus:outline-none focus:ring-2 focus:ring-emerald-400"
                            />
                          </div>
                          <div className="space-y-2">
                            <label className="text-xs font-semibold text-slate-500 uppercase tracking-wide">{t.k8s.JobDetail.metadataValueLabel}</label>
                            <input
                              value={row.value}
                              onChange={(e) => handleLabelRowChange(index, 'value', e.target.value)}
                              placeholder={t.k8s.JobDetail.metadataValuePlaceholder}
                              className="w-full px-3 py-2 border border-slate-200 rounded-lg bg-white text-sm text-slate-800 focus:outline-none focus:ring-2 focus:ring-emerald-400"
                            />
                          </div>
                        </div>
                      </div>
                    )) : (
                      <div className="text-sm text-slate-500 bg-white/60 border border-slate-200 rounded-xl p-4">{t.k8s.JobDetail.noLabelsDescription}</div>
                    )}
                    <button onClick={handleAddLabelRow} className="w-full px-4 py-2 rounded-lg text-sm font-semibold bg-emerald-50 text-emerald-700 hover:bg-emerald-100 transition-colors">{t.k8s.JobDetail.addLabel}</button>
                  </div>
                ) : (
                  <div className="space-y-3 max-h-60 overflow-y-auto">
                    {Object.entries(selectedJob.labels || {}).length > 0 ? (
                      Object.entries(selectedJob.labels).map(([key, value]) => (
                        <div key={key} className="bg-white/80 backdrop-blur-sm rounded-xl border border-slate-200/60 p-4">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-semibold text-slate-700">{key}</span>
                            <span className="text-sm font-bold text-slate-900 bg-emerald-50 px-3 py-1 rounded-lg border border-emerald-200">{value}</span>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-12">
                        <div className="w-16 h-16 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mx-auto mb-4">
                          <svg className="w-8 h-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                          </svg>
                        </div>
                        <h3 className="text-lg font-bold text-slate-700 mb-2">{t.k8s.JobDetail.noLabels}</h3>
                        <p className="text-slate-500 text-sm">{t.k8s.JobDetail.noLabelsDescription}</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                  <span className="text-sm font-semibold text-slate-700">{t.k8s.JobDetail.annotations}</span>
                </div>
                {isEditingMetadata ? (
                  <div className="space-y-4 max-h-80 overflow-y-auto pr-1">
                    {annotationRows.length > 0 ? annotationRows.map((row, index) => (
                      <div key={row.id} className="bg-white/80 backdrop-blur-sm rounded-2xl border border-slate-200/60 p-5 space-y-4">
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                          <div>
                            <div className="text-xs font-semibold text-slate-500 uppercase tracking-wide">{t.k8s.JobDetail.metadataCurrentKey}</div>
                            <div className="text-sm font-semibold text-slate-800 break-all">{row.key || t.k8s.JobDetail.metadataNewKey}</div>
                          </div>
                          <button onClick={() => handleRemoveAnnotationRow(index)} className="text-xs text-rose-500 hover:text-rose-600">
                            {t.k8s.JobDetail.metadataDelete || t.common.delete}
                          </button>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <label className="text-xs font-semibold text-slate-500 uppercase tracking-wide">{t.k8s.JobDetail.metadataKeyLabel}</label>
                            <input
                              value={row.key}
                              onChange={(e) => handleAnnotationRowChange(index, 'key', e.target.value)}
                              placeholder={t.k8s.JobDetail.metadataKeyPlaceholder}
                              className="w-full px-3 py-2 border border-slate-200 rounded-lg bg-white text-sm text-slate-800 focus:outline-none focus:ring-2 focus:ring-amber-400"
                            />
                          </div>
                          <div className="space-y-2">
                            <label className="text-xs font-semibold text-slate-500 uppercase tracking-wide">{t.k8s.JobDetail.metadataValueLabel}</label>
                            <input
                              value={row.value}
                              onChange={(e) => handleAnnotationRowChange(index, 'value', e.target.value)}
                              placeholder={t.k8s.JobDetail.metadataValuePlaceholder}
                              className="w-full px-3 py-2 border border-slate-200 rounded-lg bg-white text-sm text-slate-800 focus:outline-none focus:ring-2 focus:ring-amber-400"
                            />
                          </div>
                        </div>
                      </div>
                    )) : (
                      <div className="text-sm text-slate-500 bg-white/60 border border-slate-200 rounded-xl p-4">{t.k8s.JobDetail.noAnnotationsDescription}</div>
                    )}
                    <button onClick={handleAddAnnotationRow} className="w-full px-4 py-2 rounded-lg text-sm font-semibold bg-amber-50 text-amber-700 hover:bg-amber-100 transition-colors">{t.k8s.JobDetail.addAnnotation}</button>
                  </div>
                ) : (
                  <div className="space-y-3 max-h-60 overflow-y-auto">
                    {Object.entries(selectedJob.annotations || {}).length > 0 ? (
                      Object.entries(selectedJob.annotations).map(([key, value]) => (
                        <div key={key} className="bg-white/80 backdrop-blur-sm rounded-xl border border-slate-200/60 p-4">
                          <div className="flex flex-col gap-3">
                            <span className="text-sm font-semibold text-slate-700">{key}</span>
                            <span className="text-sm font-bold text-slate-900 bg-amber-50 px-3 py-2 rounded-lg border border-amber-200 break-all">{value}</span>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-12">
                        <div className="w-16 h-16 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mx-auto mb-4">
                          <svg className="w-8 h-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                        <h3 className="text-lg font-bold text-slate-700 mb-2">{t.k8s.JobDetail.noAnnotations}</h3>
                        <p className="text-slate-500 text-sm">{t.k8s.JobDetail.noAnnotationsDescription}</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default JobDetailsModal 
