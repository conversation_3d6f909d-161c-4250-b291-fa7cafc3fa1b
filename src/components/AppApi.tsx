import { useToast } from "@/contexts/ToastContext";
import { authFetch } from "@/utils/authFetch";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import React, { useEffect, useMemo, useState } from "react";
import { FaRegCopy, FaTimes } from "react-icons/fa";
import ApiBinding from "./ApiBinding";
import { useLanguage } from "@/contexts/LanguageContext";
import Popconfirm from "./Popconfirm";

interface Api {
  id: number;
  api_code: string;
  api_name: string;
  api_path: string;
  api_method: string;
  comments?: string;
  category?: string;
  state: boolean;
}

interface AppApiProps {
  app_primary_key: number;
  onClose: () => void;
}

const AppApi = (props: AppApiProps) => {
  const { app_primary_key, onClose } = props;

  const { addToast } = useToast();

  const [apiList, setApiList] = useState<Api[]>([]);
  const [showApiBinding, setShowApiBinding] = useState<boolean>(false);
  const { messages } = useLanguage();

  const fetchApiList = async () => {
    const response = await authFetch(`/api/apps/${app_primary_key}/api`);
    if (response && response.ok) {
      const data = await response.json();
      setApiList(data);
    }
  };

  useEffect(() => {
    fetchApiList();
  }, []);

  const columns: ColumnDef<Api>[] = useMemo(
    () => [
      {
        accessorKey: "api_name",
        header: messages.authApp.apiName,
        cell: (info) => (
          <div className="text-sm overflow-hidden whitespace-nowrap text-ellipsis" title={info.getValue() as string}>
            {info.getValue() as string}
          </div>
        ),
        size: 80,
      },
      {
        accessorKey: "api_code",
        header: messages.authApp.apiCode,
        cell: (info) => (
          <div className="text-sm overflow-hidden whitespace-nowrap text-ellipsis">
            {info.getValue() as string}
          </div>
        ),
        size: 80,
      },
      {
        accessorKey: "api_path",
        header: messages.authApp.apiPath,
        cell: (info) => (
          <div className="flex items-center">
            <div className="text-sm overflow-hidden whitespace-nowrap text-ellipsis">
              {info.getValue() as string}
            </div>
            <button
              className="ml-1 p-1 rounded hover:bg-[var(--hover-bg)] transition-colors"
              title={messages.authApp.copyPath}
              onClick={() => handleCopy(info.getValue() as string)}
              style={{ lineHeight: 0 }}
            >
              <FaRegCopy
                size={16}
                className="text-[var(--muted)] hover:text-[var(--primary)]"
              />
            </button>
          </div>
        ),
        size: 150,
      },
      {
        accessorKey: "api_method",
        header: messages.authApp.apiMethod,
        cell: (info) => (
          <div className="text-sm">{info.getValue() as string}</div>
        ),
        size: 50,
      },
      {
        accessorKey: "comments",
        header: messages.authApp.comments,
        cell: (info) => (
          <div
            className="text-sm overflow-hidden whitespace-nowrap text-ellipsis"
            title={info.getValue() as string}
          >
            {info.getValue() as string}
          </div>
        ),
        size: 100,
      },
      {
        accessorKey: "category",
        header: messages.authApp.category,
        cell: (info) => (
          <div className="text-sm overflow-hidden whitespace-nowrap text-ellipsis">
            {info.getValue() as string}
          </div>
        ),
        size: 50,
      },
      {
        accessorKey: "state",
        header: messages.authApp.state,
        cell: (info) => (
          <div
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-semibold ${
              info.getValue()
                ? "bg-green-100 text-green-800"
                : "bg-red-100 text-red-800"
            }`}
          >
            {info.getValue() == 1
              ? messages.authApp.enable
              : messages.authApp.disable}
          </div>
        ),
        size: 80,
      },
      {
        accessorKey: "operation",
        header: messages.authApp.operation,
        size: 80,
        cell: (info) => (
          <div className="flex items-center gap-x-2 relative">
            {info.row.original.state ? (
              <Popconfirm
                title={messages.authApp.disableApi}
                description={messages.authApp.confirmDisableApi}
                onConfirm={() => {
                  modifyApiState(
                    info.row.original.id,
                    !info.row.original.state
                  );
                }}
                onCancel={() => {}}
              >
                <div className="text-amber-500 text-sm cursor-pointer">
                  {messages.authApp.disable}
                </div>
              </Popconfirm>
            ) : (
              <Popconfirm
                title={messages.authApp.enableApi}
                description={messages.authApp.confirmEnableApi}
                onConfirm={() => {
                  modifyApiState(
                    info.row.original.id,
                    !info.row.original.state
                  );
                }}
                onCancel={() => {}}
              >
                <div className="text-green-500 text-sm cursor-pointer">
                  {messages.authApp.enable}
                </div>
              </Popconfirm>
            )}
            <Popconfirm
              title={messages.authApp.unbindApi}
              description={messages.authApp.confirmUnbindApi}
              onConfirm={() => {
                unbindApi(info.row.original.id);
              }}
              onCancel={() => {}}
            >
              <div className="text-red-500 text-sm cursor-pointer">
                {messages.authApp.unbind}
              </div>
            </Popconfirm>
          </div>
        ),
      },
    ],
    []
  );

  const table = useReactTable({
    data: apiList,
    columns: columns,
    getCoreRowModel: getCoreRowModel(),
  });

  const handleCopy = async (text?: string) => {
    if (!text) return;

    const showToast = (type: "success" | "error", message: string) => {
      addToast({
        type,
        title:
          type === "success"
            ? messages.authApp.copySuccess
            : messages.authApp.copyFail,
        message,
      });
    };

    try {
      // 优先使用 Clipboard API
      if (window.isSecureContext && navigator.clipboard) {
        await navigator.clipboard.writeText(text);
        showToast("success", messages.authApp.copyToClipboard);
        return;
      }

      // 降级方案
      const textarea = document.createElement("textarea");
      textarea.value = text;
      textarea.style.position = "fixed";
      textarea.style.opacity = "0";
      document.body.appendChild(textarea);
      textarea.select();

      try {
        const successful = document.execCommand("copy");
        if (!successful) throw new Error("execCommand failed");
        showToast("success", messages.authApp.copyToClipboard);
      } finally {
        document.body.removeChild(textarea);
      }
    } catch (error) {
      console.error(messages.authApp.copyFail, error);
      showToast(
        "error",
        `messages.authApp.copyFail: ${
          error instanceof Error ? error.message : messages.authApp.copyManual
        }`
      );
    }
  };

  const unbindApi = async (id: number) => {
    const response = await authFetch(`/api/apps/${app_primary_key}/api/${id}`, {
      method: "DELETE",
    });
    if (response && response.ok) {
      addToast({
        type: "success",
        title: messages.authApp.unbindApi,
        message: messages.authApp.unbindApiSuccess,
      });
      fetchApiList();
    } else {
      addToast({
        type: "error",
        title: messages.authApp.unbindApi,
        message: messages.authApp.unbindApiFail,
      });
    }
  };

  const modifyApiState = async (id: number, state: boolean) => {
    const response = await authFetch(
      `/api/apps/${app_primary_key}/api/${id}/state`,
      {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ state: state }),
      }
    );
    if (response && response.ok) {
      addToast({
        type: "success",
        title: messages.authApp.modifyApiState,
        message: messages.authApp.modifyApiStateSuccess,
      });
      fetchApiList();
    } else {
      addToast({
        type: "error",
        title: messages.authApp.modifyApiState,
        message: messages.authApp.modifyApiStateFail,
      });
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 bg-opacity-50 flex items-center justify-center z-50 p-4 backdrop-blur-sm text-start">
      <div className="bg-[var(--card-bg)] rounded-lg shadow-2xl border border-[var(--border-color)] w-full max-w-6xl max-h-[95vh] overflow-hidden">
        <div className="border-b border-[var(--border-color)] flex flex-col gap-y-6">
          {/* modal header */}
          <div className="flex items-center justify-between px-6 pt-6">
            <h3 className="text-lg font-semibold text-[var(--foreground)]">
              {messages.authApp.apiManagement}
            </h3>
            {onClose && (
              <button
                onClick={onClose}
                className="text-[var(--muted)] hover:text-[var(--foreground)] transition-colors"
              >
                <FaTimes size={16} />
              </button>
            )}
          </div>

          {/* modal body */}
          <div className="w-full max-h-[70vh] flex flex-col gap-y-5 px-6 pb-8">
            <div className="basis-auto flex justify-end">
              <button
                className="px-4 py-2 text-sm font-medium rounded-md border border-transparent bg-[var(--accent-primary)]/90 text-white hover:bg-[var(--accent-primary)] transition-colors disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
                onClick={() => setShowApiBinding(true)}
              >
                {messages.authApp.bindApi}
              </button>
            </div>

            {/* 表格 */}
            <div className="border border-[var(--border-color)] rounded-md flex-1 flex flex-col overflow-hidden">
              <div className="basis-auto">
                <table className="w-full h-full table-fixed">
                  <thead className="border-b border-[var(--border-color)] bg-[var(--accent-secondary)]">
                    {table.getHeaderGroups().map((headerGroup) => (
                      <tr key={headerGroup.id}>
                        {headerGroup.headers.map((header) => (
                          <th
                            key={header.id}
                            className="px-4 py-2 text-left text-sm font-medium text-[var(--foreground)]"
                            style={{ width: header.getSize() }}
                          >
                            {header.isPlaceholder
                              ? null
                              : flexRender(
                                  header.column.columnDef.header,
                                  header.getContext()
                                )}
                          </th>
                        ))}
                      </tr>
                    ))}
                  </thead>
                </table>
              </div>
              <div className="flex-1 overflow-auto">
                <table className="w-full table-fixed">
                  <tbody>
                    {table.getRowModel().rows.length > 0 ? (
                      table.getRowModel().rows.map((row) => (
                        <tr
                          key={row.original.id}
                          className={`border-b border-[var(--border-color)]`}
                        >
                          {row.getVisibleCells().map((cell) => (
                            <td
                              key={cell.id}
                              className="px-4 py-2"
                              style={{ width: cell.column.getSize() }}
                            >
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext()
                              )}
                            </td>
                          ))}
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td
                          colSpan={table.getAllColumns().length}
                          className="px-4 py-20 text-center"
                        >
                          <div className="flex flex-col items-center justify-center text-[var(--muted)]">
                            <p className="text-sm font-medium">
                              {messages.authApp.noData}
                            </p>
                          </div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      {showApiBinding && (
        <ApiBinding
          app_primary_key={app_primary_key}
          onClose={() => setShowApiBinding(false)}
          onFinish={() => {
            fetchApiList();
            setShowApiBinding(false);
          }}
        />
      )}
    </div>
  );
};

export default AppApi;
