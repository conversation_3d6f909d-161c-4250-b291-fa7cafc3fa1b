'use client';

import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { useTheme } from 'next-themes';
import { useLanguage } from '@/contexts/LanguageContext';
import PersonInfoModal from './PersonInfoModal';
import { useAuth } from '@/contexts/AuthContext';
import { useSettings } from '@/contexts/SettingsContext';
import { FaShareAlt, FaUser } from 'react-icons/fa';
import { authFetch } from '@/utils/authFetch';
import { Announcement } from '@/types/announcement';
import { MdLanguage } from 'react-icons/md';
import Logo from '../../public/Logo.png';
import { Language } from '@/const/chat';

import Marquee from 'react-fast-marquee';
import Image from 'next/image';

interface HeaderProps {
  onOpenSettings?: () => void;
  onOpenJobs?: () => void; // 添加 onOpenJobs
  onShare?: () => void;
  showJobs?: boolean;
  showProjectModeToggle?: boolean;
  shareDisabled?: boolean;
}

const ThemeToggle = () => {
  const { theme, setTheme } = useTheme();
  const { messages } = useLanguage();
  const themes = ['light', 'dark'];

  const handleThemeChange = () => {
    const currentIndex = themes.indexOf(theme || 'light');
    const nextIndex = (currentIndex + 1) % themes.length;
    setTheme(themes[nextIndex]);
  };

  const getIcon = () => {
    if (theme === 'light') {
      return <Image src="/dark.svg" alt='' width={20} height={20} />;
    }
    return <Image src="/light.svg" alt='' width={20} height={20} />;
  };

  return (
    <button
      onClick={handleThemeChange}
      className="p-2 rounded-md hover:bg-[var(--accent-primary)]/10 transition-colors"
      title={messages.components.header.toggle}
    >
      {getIcon()}
    </button>
  );
};

export default function Header({ onOpenSettings, onShare, onOpenJobs, showJobs = true, showProjectModeToggle = false, shareDisabled = false }: HeaderProps) {
  const { messages, language, setCookie } = useLanguage();
  // 获取必要参数
  const [isPersonInfoModalOpen, setIsPersonInfoModalOpen] = useState(false);
  const { isLogged, login, hasAdminPermission, userInfo } = useAuth();
  const { settings, setIsProjectMode } = useSettings();
  const personButtonRef = useRef<HTMLButtonElement>(null);
  const [personButtonPosition, setPersonButtonPosition] = useState<{ top: number; left: number; width: number; height: number } | undefined>();
  const [isLanguageDropdownOpen, setIsLanguageDropdownOpen] = useState(false);
  const languageButtonRef = useRef<HTMLButtonElement>(null);
  const languageDropdownRef = useRef<HTMLDivElement>(null);

  // 公告相关状态
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);

  const onOpenPersonInfo = () => {
    // 计算按钮位置
    if (personButtonRef.current) {
      const rect = personButtonRef.current.getBoundingClientRect();
      setPersonButtonPosition({
        top: rect.top,
        left: rect.left,
        width: rect.width,
        height: rect.height,
      });
    }
    setIsPersonInfoModalOpen(true);
  }

  // 处理项目/产品模式切换
  const handleModeToggle = (projectMode: boolean) => {
    setIsProjectMode(projectMode);
  };

  // 获取公告数据
  useEffect(() => {
    const loadAnnouncements = async () => {
      try {
        const response = await authFetch('/api/announcements');
        if (response?.ok) {
          const data = await response.json();
          setAnnouncements(data.announcements || []);
          console.log('Loaded announcements:', data);
        } else {
          throw new Error('Failed to fetch announcements');
        }
      } catch (error) {
        console.error('Failed to load announcements:', error);
      }
    };

    loadAnnouncements();
  }, []);

  // 点击外部关闭语言下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      const isInsideButton = languageButtonRef.current?.contains(target);
      const isInsideDropdown = languageDropdownRef.current?.contains(target);
      
      if (!isInsideButton && !isInsideDropdown) {
        console.log('Clicking outside, closing dropdown');
        setIsLanguageDropdownOpen(false);
      }
    };

    if (isLanguageDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isLanguageDropdownOpen]);

  // 语言选项配置
  const languageOptions = [
    { code: Language.Chinese, name: '简体中文' },
    { code: Language.English, name: 'English' }
  ];

  // 获取当前语言显示名称
  const getCurrentLanguageName = () => {
    const currentLang = languageOptions.find(lang => lang.code === language);
    return currentLang ? currentLang.name : '简体中文';
  };

  // 处理语言切换
  const handleLanguageChange = (langCode: string) => {
    if (langCode !== language) {
      try {
        setCookie('DEEPWIKI_LOCALE', langCode, 30);
        window.location.reload();
      } catch (error) {
        console.error('Error switching language:', error);
      }
    } else {
      console.log('Language is already set to', langCode);
    }
    setIsLanguageDropdownOpen(false);
  };

  return (
    <header className="w-full border-b border-[var(--border-color)] bg-[var(--card-bg)]/80 backdrop-blur-sm sticky top-0 z-40">
      <div className="container mx-auto px-4 py-2 flex items-center">
        {/* Left side: Logo and Title */}
        <div className="flex items-center gap-3 flex-shrink-0 min-w-fit">
          <Link href="/" className="flex items-center gap-3">
            <Image src={Logo} alt='' width={48} height={48} />
            <div>
              <h1 className="text-lg font-black text-[var(--foreground)]" style={{ fontSize: 22 }}>
                {messages.common?.appName || 'DeepWiki'}
              </h1>
            </div>
          </Link>
        </div>

        {/* Middle: Announcement */}
        <div className="flex-1 flex justify-center min-w-0">
          {announcements.length > 0 && settings.showAnnouncement && (
            <div className="flex items-center gap-1 min-w-0 max-w-full">
              {/* 左侧装饰图标 */}
              <div className="flex items-center justify-center w-6 h-6 rounded-lg text-[var(--head-text-color)] flex-shrink-0 ml-2">
              <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
                <title>{messages.components.header.helpDocument}</title>
                  <g id="页面-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                     <g id="DeepWiki首页浅色" transform="translate(-522, -20)" fill="currentColor" fillRule="nonzero">
                        <g id="想自主生成wiki？【点击】申请wiki备份" transform="translate(512, 13)">
                          <g id="编组" transform="translate(10, 7)">
                  <path d="M10.0093717,8.76387052e-16 L5.10947709,4.90471257 L1.63570719,4.90471257 C0.732334293,4.90471257 -4.38193526e-16,5.63704686 -4.38193526e-16,6.54041976 L-4.38193526e-16,13.0784305 C-4.38193526e-16,13.9818034 0.732334293,14.7141377 1.63570719,14.7141377 L5.10947709,14.7141377 L10.0117807,19.6188503 C10.9151536,19.6188503 11.6450789,18.886516 11.6450789,17.9831431 L11.6450789,1.63570719 C11.6450789,0.732334293 10.9127446,8.76387052e-16 10.0093717,8.76387052e-16 Z M10.0093717,17.9253272 L5.72136167,13.1940623 L1.63570719,13.1940623 L1.63570719,6.65605149 L5.72136167,6.65605149 L10.0093717,1.92237752 L10.0093717,17.9253272 Z M19.6983471,10.5248965 C19.8597497,10.5248965 19.9898354,10.2045002 19.9898354,9.80701615 C19.9898354,9.41194107 19.8597497,9.08913582 19.6983471,9.08913582 L17.6555199,9.08913582 C17.4941172,9.08913582 17.3640315,9.40953207 17.3640315,9.80701615 C17.3640315,10.2020912 17.4941172,10.5248965 17.6555199,10.5248965 L19.6983471,10.5248965 Z M19.9199746,1.25990406 C20.0596963,1.18040725 20.0115164,0.835921052 19.8139788,0.493843849 C19.6164413,0.151766646 19.3418159,-0.062633854 19.2020943,0.0192719551 L17.4314834,1.04068557 C17.2917617,1.12018239 17.3399416,1.46466859 17.5374791,1.80674579 C17.7350167,2.14882299 18.009642,2.36322349 18.1493637,2.28131768 L19.9199746,1.25990406 Z M19.9199746,18.3565372 C20.0596963,18.438443 20.0115164,18.7805202 19.8139788,19.1225974 C19.6164413,19.4646746 19.3418159,19.6790751 19.2020943,19.5971693 L17.4314834,18.5757557 C17.2917617,18.4938499 17.3399416,18.1517727 17.5374791,17.8096955 C17.7350167,17.4676183 18.009642,17.2532178 18.1493637,17.3351236 L19.9199746,18.3565372 Z M12.7941692,13.4807326 C12.6472205,13.0880665 12.6881734,12.7170814 13.0808395,12.5701327 C14.0709362,12.1124238 14.5455081,11.2235048 14.6659578,10.1057315 C14.8080885,8.80246549 14.1841589,7.60519528 13.1073385,7.11376042 C12.7556253,6.88731495 12.6086766,6.49464887 12.8351221,6.14293568 C13.0615676,5.7912225 13.4542336,5.64427385 13.8059468,5.87071932 C15.4007011,6.79577316 16.3546629,8.40498141 16.1523074,10.2671341 C15.9909047,11.7558926 15.0851228,13.1627453 13.7023601,13.7674029 C13.3819638,13.9504865 12.9483448,13.8300368 12.7941692,13.4807326 L12.7941692,13.4807326 Z" id="形状"></path>
                          </g>
                        </g>
                    </g>
                  </g>
                </svg>
              </div>

              <div
                className="flex items-center px-4 py-2 rounded-full bg-[var(--card-bg)] text-[var(--foreground)] overflow-hidden flex-1 min-w-0"
                style={{
                  fontWeight: 500,
                  fontSize: '0.92rem',
                  height: 32,
                  borderRadius: 9999,
                  maxWidth: 'min(800px, 40vw)',
                  paddingLeft: 6,
                  paddingRight: 16,
                  position: 'relative',
                }}
              >
                <Marquee
                  speed={50}
                  gradient={false}
                  pauseOnHover={true}
                  delay={1}
                  direction="left"
                  play={true}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    height: '100%',
                    background: 'transparent',
                    border: 'none',
                    outline: 'none',
                    overflow: 'hidden',
                  }}
                  className="!bg-transparent !border-none !overflow-hidden"
                >
                  {announcements.map((announcement) => (
                    <span
                      key={announcement.id}
                      className="cursor-pointer text-[#556A82] dark:text-[#1089F9] hover:underline transition-colors inline-flex items-center"
                      onClick={() => {
                        if (announcement.content) {
                          window.open(announcement.content, '_blank');
                        }
                      }}
                      style={{ marginRight: 8, fontWeight: 400 }}
                    >
                      <span
                        className="inline-flex items-center justify-center font-light text-[#556A82] dark:text-[#1089F9]"
                        style={{
                          marginRight: '4px',
                          fontSize: '40px',
                          lineHeight: '1'
                        }}
                      >
                        ·
                      </span>
                      {announcement.title}
                      <span
                        className="inline-flex items-center justify-center font-light"
                        style={{
                          color: '#434345',
                          marginLeft: '10px',
                          borderLeft: '1px solid #434345',
                          height: '14px',
                        }}
                      >

                      </span>
                    </span>
                  ))}
                </Marquee>

                {/* 只移除Marquee组件的滚动条 */}
                <style jsx global>{`
                  /* 只针对Marquee组件隐藏滚动条 */
                  .react-fast-marquee {
                    background: transparent !important;
                    border: none !important;
                    outline: none !important;
                    overflow: hidden !important;
                  }
                  
                  .react-fast-marquee * {
                    background: transparent !important;
                    border: none !important;
                  }
                  
                  /* 只针对Marquee内部元素隐藏滚动条 */
                  .react-fast-marquee .marquee-content {
                    overflow: hidden !important;
                  }
                  
                  .react-fast-marquee .marquee-content * {
                    overflow: hidden !important;
                  }
                  
                  /* 只针对Marquee的滚动条 */
                  .react-fast-marquee::-webkit-scrollbar {
                    display: none !important;
                    width: 0 !important;
                    height: 0 !important;
                  }
                  
                  .react-fast-marquee .marquee-content::-webkit-scrollbar {
                    display: none !important;
                    width: 0 !important;
                    height: 0 !important;
                  }
                  
                  /* 确保公告容器不显示滚动条 */
                  .announcement-container {
                    overflow: hidden !important;
                  }
                  
                  .announcement-container::-webkit-scrollbar {
                    display: none !important;
                  }
                `}</style>
              </div>
            </div>
          )}
        </div>

        {/* Right side: Buttons */}
        <div className="flex items-center gap-2 relative flex-shrink-0 min-w-fit">
          {/* 项目/产品切换按钮 */}
          {showProjectModeToggle && <div className="flex items-center bg-[var(--card-bg)] border border-[var(--border-color)] rounded-md p-0.5">
            <button
              onClick={() => handleModeToggle(false)}
              className={`px-2 py-1 text-xs font-medium rounded-sm transition-all duration-200 ${
                !settings.isProjectMode
                  ? 'bg-[var(--accent-primary)] text-white shadow-sm'
                  : 'text-[var(--muted)] hover:text-[var(--foreground)] hover:bg-[var(--accent-secondary)]'
              }`}
              title={messages.components.header.switchToProductMode}
            >
              {messages.components.header.product}
            </button>
            <button
              onClick={() => handleModeToggle(true)}
              className={`px-2 py-1 text-xs font-medium rounded-sm transition-all duration-200 ${
                settings.isProjectMode
                  ? 'bg-[var(--accent-primary)] text-white shadow-sm'
                  : 'text-[var(--muted)] hover:text-[var(--foreground)] hover:bg-[var(--accent-secondary)]'
              }`}
              title={messages.components.header.switchToProjectMode}
            >
              {messages.components.header.project}
            </button>
          </div>}

          {/* 分隔符 */}
          {showProjectModeToggle && <div className="w-px h-5 bg-[var(--border-color)]" />}

          <ThemeToggle />

          {/* 分隔符 */}
          <div className="w-px h-5 bg-[var(--border-color)]" />

          {/* Jobs Progress Button - 仅管理员可见 */}
          {hasAdminPermission() && showJobs && (
            <>
            <button
              onClick={onOpenJobs}
              className="hidden sm:flex items-center justify-center p-2 hover:bg-[var(--accent-primary)]/10 text-[var(--head-text-color)] rounded-lg transition-colors"
              title={messages.components.header.taskProgress}
            >
              <svg width="20px" height="20px" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                {/* 时钟轮廓 */}
                <circle cx="12" cy="12" r="9"></circle>
                {/* 时针 - 较短，指向12点方向 */}
                <line x1="12" y1="12" x2="12" y2="8" strokeWidth="2"></line>
                {/* 分针 - 较长，指向4点方向 */}
                <line x1="12" y1="12" x2="17" y2="11" strokeWidth="1.5"></line>
              </svg>
            </button>
             <div className="w-px h-5 bg-[var(--border-color)]" />
             </>
          )}

        
          <PersonInfoModal
            isOpen={isPersonInfoModalOpen}
            onClose={() => setIsPersonInfoModalOpen(false)}
            onOpenSettings={onOpenSettings}
            triggerRef={personButtonRef}
            triggerPosition={personButtonPosition}
          />

          {/* 问号图标，悬停显示"帮助文档"，点击弹出右侧悬浮框 */}
          <button
            className="p-2 rounded-md hover:bg-[var(--accent-primary)]/10 text-[var(--head-text-color)] transition-colors"
            title={messages.components.header.helpDocument}
            onClick={() => { window.open("https://docs.iwhalecloud.com/bidc7G6f9/index#/didxwypNNwG") }}
          >
            <svg width="20px" height="20px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
              <title>{messages.components.header.helpDocument}</title>
              <g id="页面-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                <g id="DeepWiki首页浅色" transform="translate(-1568, -18)" fill="currentColor" fillRule="nonzero">
                  <g id="编组-11" transform="translate(1370, 18)">
                      <g id="编组" transform="translate(198, 0)">
                        <path d="M12,0 C18.6274444,0 24,5.37255556 24,12 C24,18.6274444 18.6274444,24 12,24 C5.37255556,24 0,18.6274444 0,12 C0,5.37255556 5.37255556,0 12,0 Z M12,1.77777778 C6.35444444,1.77777778 1.77777778,6.35444444 1.77777778,12 C1.77777778,17.6455556 6.35444444,22.2222222 12,22.2222222 C17.6455556,22.2222222 22.2222222,17.6455556 22.2222222,12 C22.2222222,6.35444444 17.6455556,1.77777778 12,1.77777778 Z" id="形状"></path>
                        <path d="M13.0511111,15.2822222 L13.0511111,14.9422222 C13.0511111,14.4622222 13.1511111,14.0222222 13.3711111,13.6222222 C13.5511111,13.2622222 13.8311111,12.9022222 14.2111111,12.5822222 C15.1511111,11.7622222 15.7111111,11.2422222 15.8911111,11.0222222 C16.3911111,10.3822222 16.6511111,9.56222222 16.6511111,8.58222222 C16.6511111,7.38222222 16.2511111,6.42222222 15.4511111,5.72222222 C14.6511111,5.00222222 13.6111111,4.66222222 12.3311111,4.66222222 C10.8511111,4.66222222 9.69111111,5.08222222 8.85111111,5.94222222 C8.01111111,6.78222222 7.59111111,7.92222222 7.59111111,9.38222222 L9.69111111,9.38222222 C9.69111111,8.50222222 9.87111111,7.82222222 10.2311111,7.34222222 C10.6311111,6.76222222 11.2911111,6.48222222 12.1911111,6.48222222 C12.9111111,6.48222222 13.4911111,6.68222222 13.8911111,7.08222222 C14.2711111,7.48222222 14.4711111,8.02222222 14.4711111,8.72222222 C14.4711111,9.24222222 14.2711111,9.72222222 13.9111111,10.1822222 L13.5711111,10.5622222 C12.3311111,11.6622222 11.5711111,12.4822222 11.3111111,13.0422222 C11.0311111,13.5622222 10.9111111,14.2022222 10.9111111,14.9422222 L10.9111111,15.2822222 L13.0511111,15.2822222 L13.0511111,15.2822222 Z M11.9711111,19.2222222 C12.3711111,19.2222222 12.7311111,19.0822222 13.0111111,18.8222222 C13.2911111,18.5422222 13.4511111,18.2022222 13.4511111,17.7822222 C13.4511111,17.3622222 13.3111111,17.0222222 13.0311111,16.7622222 C12.7511111,16.4822222 12.3911111,16.3622222 11.9711111,16.3622222 C11.5711111,16.3622222 11.2111111,16.4822222 10.9311111,16.7622222 C10.6511111,17.0222222 10.5111111,17.3622222 10.5111111,17.7822222 C10.5111111,18.1822222 10.6511111,18.5222222 10.9311111,18.8022222 C11.2111111,19.0822222 11.5711111,19.2222222 11.9711111,19.2222222 L11.9711111,19.2222222 Z" id="形状"></path>
                      </g>
                    </g>
                  </g>
                </g>
              </svg>
          </button>

          {/* 分享按钮 */}
          {onShare && (
            <>
              <div className="w-px h-5 bg-[var(--border-color)]" />
              <button
                className="p-2 rounded-md hover:bg-[var(--accent-primary)]/10 transition-colors disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-transparent"
                title={messages.components.header.share}
                onClick={onShare}
                disabled={shareDisabled}
              >
                <FaShareAlt className="w-5 h-5 text-[var(--head-text-color)]" />
              </button>
            </>
          )}

                    {/* 分隔符 */}
                    <div className="w-px h-5 bg-[var(--border-color)]" />

          {!isLogged && (
            <button
              onClick={login}
              className="w-[88px] h-8 rounded-[4px] transition-colors flex items-center justify-center font-medium"
              style={{
                backgroundColor: 'var(--login-btn-bg)',
                color: 'var(--login-btn-text)',
                fontSize: '14px',
                fontWeight: '400'
              }}
            >
              {messages.components.header.login}
            </button>
          )}
          {isLogged && (
            <div className="flex items-center gap-1" >
              <button
                ref={personButtonRef}
                className="p-2 rounded-md hover:bg-[var(--accent-primary)]/10 transition-colors"
                onClick={onOpenPersonInfo}>
              <FaUser className="text-[var(--head-text-color)] text-sm w-5 h-5"  />
                </button>
              <div className="text-sm font-medium text-[var(--head-text-color)] cursor-pointer" onClick={onOpenPersonInfo}>
                {userInfo?.user_name || messages.components.header.user}</div>
            </div>
          )}
          {/* 分隔符 */}
          <div className="w-px h-5 bg-[var(--border-color)]" />

          {/* 语言选择下拉框 */}
          <div className="relative">
            <button
              ref={languageButtonRef}
              className="flex items-center gap-1 px-2 py-2 rounded-md hover:bg-[var(--accent-primary)]/10 text-[var(--head-text-color)] transition-colors text-sm font-medium"
              title={messages.components.header.language}
              onClick={() => setIsLanguageDropdownOpen(!isLanguageDropdownOpen)}
            >
              <MdLanguage className="w-5 h-5" />
              <span>{getCurrentLanguageName()}</span>
              <svg 
                className={`w-4 h-4 transition-transform ${isLanguageDropdownOpen ? 'rotate-180' : ''}`}
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            {/* 下拉菜单 */}
            {isLanguageDropdownOpen && (
              <div 
                ref={languageDropdownRef}
                className="absolute right-0 top-full mt-1 w-24 bg-[var(--card-bg)] border border-[var(--border-color)] rounded-md shadow-lg z-50"
              >
                {languageOptions.map((option) => (
                  <button
                    key={option.code}
                    className={`w-full px-3 py-2 text-left text-sm transition-colors first:rounded-t-md last:rounded-b-md ${
                      language === option.code
                        ? 'bg-[var(--accent-primary)]/20 text-[var(--accent-primary)]'
                        : 'text-[var(--head-text-color)] hover:bg-[var(--accent-primary)]/10'
                    }`}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleLanguageChange(option.code);
                    }}
                  >
                    {option.name}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}