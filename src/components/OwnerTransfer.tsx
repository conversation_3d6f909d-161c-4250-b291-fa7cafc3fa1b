import { useToast } from "@/contexts/ToastContext";
import { authFetch } from "@/utils/authFetch";
import {
  flexRender,
  getCoreRowModel,
  PaginationState,
  useReactTable,
} from "@tanstack/react-table";
import React, { useEffect, useMemo, useState } from "react";
import { FaSearch, FaTimes } from "react-icons/fa";
import DeleteConfirmModal from "./DeleteConfirmModal";
import { useLanguage } from "@/contexts/LanguageContext";

interface User {
  id: number;
  user_name: string;
  user_code: string;
  org?: string;
}

interface OwnerTransferProps {
  wiki_id: string;
  onClose: () => void;
  onFinish: () => void;
}

const OwnerTransfer = (props: OwnerTransferProps) => {
  const { wiki_id, onClose, onFinish } = props;

  const { addToast } = useToast();
  const { messages } = useLanguage();
  const [users, setUsers] = useState<User[]>([]);
  const [search, setSearch] = useState("");
  const [input, setInput] = useState("");
  const [pages, setPages] = useState<number>(0);
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [selectedUser, setSelectedUser] = useState<User>();
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const pageSizeOptions = [10, 20, 50];

  const fetchUsers = async () => {
    try {
      const response = await authFetch(
        `/api/owner/users?wiki_id=${wiki_id}&search=${search}&page=${pagination.pageIndex}&size=${pagination.pageSize}`
      );
      if (response && response.ok) {
        const data = await response.json();
        setPages(Math.ceil(data.total / pagination.pageSize));
        setUsers(data.users || []);
      }
    } catch (error) {
      console.error("加载用户失败:", error);
      addToast({
        type: "error",
        title: messages.components.ownerTransfer.loadUserFail,
        message: error instanceof Error ? error.message : messages.components.ownerTransfer.loadUserFail,
      });
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [wiki_id, pagination]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setSearch(input);
      setPagination((pre) => {
        return { ...pre, pageIndex: 0 };
      });
    }, 500);
    return () => clearTimeout(timer);
  }, [input]);

  const columns = useMemo(
    () => [
      {
        accessorKey: "user_name",
        header: messages.user.username,
        cell: ({ getValue }: { getValue: () => string }) => (
          <span className="text-sm">{getValue()}</span>
        ),
      },
      {
        accessorKey: "user_code",
        header: messages.user.userCode,
        cell: ({ getValue }: { getValue: () => string }) => (
          <span className="text-sm">{getValue()}</span>
        ),
      },
      {
        accessorKey: "org",
        header: messages.user.department,
        cell: ({ getValue }: { getValue: () => string }) => (
          <span className="text-sm">{getValue() || "-"}</span>
        ),
      },
    ],
    []
  );

  const table = useReactTable({
    data: users,
    columns: columns,
    pageCount: pages,
    state: {
      pagination: pagination,
    },
    manualPagination: true,
    getCoreRowModel: getCoreRowModel(),
    onPaginationChange: setPagination,
  });

  const onSubmit = async () => {
    if (!selectedUser) {
      addToast({
        type: "warning",
        title: messages.components.ownerTransfer.parameterVerification,
        message: messages.components.ownerTransfer.selectUser,
      });
      return;
    }
    setShowConfirmModal(true);
  };

  const handleOwnerTransfer = async () => {
    setShowConfirmModal(false);
    try {
      const data = {
        wiki_id: wiki_id,
        owner_id: selectedUser?.id,
      };
      const response = await authFetch("/api/owner", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });
      if (response && response.ok) {
        addToast({
          type: "success",
          title: messages.common.requestSuccess,
          message: messages.components.ownerTransfer.successModifyOwner,
        });
        onFinish();
      }
    } catch (error) {
      console.error(error);
      addToast({
        type: "error",
        title: messages.common.requestFail,
        message: messages.components.ownerTransfer.modifyOwnerFailed,
      });
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 bg-opacity-50 flex items-center justify-center z-50 p-4 backdrop-blur-sm text-start">
      <div className="bg-[var(--card-bg)] rounded-lg shadow-2xl border border-[var(--border-color)] w-full max-w-2xl max-h-[95vh] overflow-hidden">
        <div className="border-b border-[var(--border-color)] flex flex-col gap-y-6">
          {/* modal header */}
          <div className="flex items-center justify-between px-6 pt-6">
            <h3 className="text-lg font-semibold text-[var(--foreground)]">
              {messages.components.ownerTransfer.transferOwner}
            </h3>
            {onClose && (
              <button
                onClick={onClose}
                className="text-[var(--muted)] hover:text-[var(--foreground)] transition-colors"
              >
                <FaTimes size={16} />
              </button>
            )}
          </div>

          {/* modal body */}
          <div className="w-full max-h-[60vh] flex flex-col gap-y-5 px-6">
            <div className="basis-auto">
              <div className="w-full relative">
                <input
                  type="text"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder={messages.components.ownerTransfer.userSearchPlaceholder}
                  className="w-full p-2 pl-8 border border-[var(--border-color)] rounded-md bg-[var(--background)] text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)]"
                />
                <FaSearch className="absolute left-2 top-1/2 transform -translate-y-1/2 text-[var(--muted)] h-4 w-4" />
              </div>
            </div>

            {/* 表格 */}
            <div className="border border-[var(--border-color)] rounded-md flex-1 flex flex-col overflow-hidden">
              <div className="basis-auto">
                <table className="w-full h-full table-fixed">
                  <thead className="border-b border-[var(--border-color)] bg-[var(--accent-secondary)]">
                    {table.getHeaderGroups().map((headerGroup) => (
                      <tr key={headerGroup.id}>
                        {headerGroup.headers.map((header) => (
                          <th
                            key={header.id}
                            className="px-4 py-2 text-left text-sm font-medium text-[var(--foreground)]"
                          >
                            {header.isPlaceholder
                              ? null
                              : flexRender(
                                  header.column.columnDef.header,
                                  header.getContext()
                                )}
                          </th>
                        ))}
                      </tr>
                    ))}
                  </thead>
                </table>
              </div>
              <div className="flex-1 overflow-auto">
                <table className="w-full table-fixed">
                  <tbody>
                    {table.getRowModel().rows.length > 0 ? (
                      table.getRowModel().rows.map((row) => (
                        <tr
                          key={row.original.id}
                          className={`border-b border-[var(--border-color)] cursor-pointer ${
                            selectedUser?.id === row.original.id
                              ? "bg-[var(--accent-primary)] text-white"
                              : "hover:bg-gray-200"
                          }`}
                          onClick={() => setSelectedUser(row.original)}
                        >
                          {row.getVisibleCells().map((cell) => (
                            <td key={cell.id} className="px-4 py-2">
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext()
                              )}
                            </td>
                          ))}
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td
                          colSpan={table.getAllColumns().length}
                          className="px-4 py-16 text-center"
                        >
                          <div className="flex flex-col items-center justify-center text-[var(--muted)]">
                            <div className="w-10 h-10 rounded-full bg-[var(--accent-secondary)] flex items-center justify-center">
                              <svg
                                className="w-8 h-8"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={1.5}
                                  d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                                />
                              </svg>
                            </div>
                            <p className="text-sm font-medium">{messages.components.ownerTransfer.noUsers}</p>
                          </div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>

            {users.length > 0 && (
              <div className="flex flex-col items-end justify-between gap-2 basis-auto">
                <div className="flex items-center gap-2">
                  {/* 页面大小选择器 */}
                  <div className="flex items-center gap-2 text-sm text-[var(--muted)]">
                    <span>{messages.components.ownerTransfer.perPage}</span>
                    <select
                      value={pagination.pageSize}
                      onChange={(e) => {
                        setPagination((prev) => ({
                          ...prev,
                          pageSize: Number(e.target.value),
                          pageIndex: 0, // 切换页面大小时重置到第一页
                        }));
                      }}
                      className="px-2 py-1 text-xs border border-[var(--border-color)] rounded bg-[var(--background)] text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)]"
                    >
                      {pageSizeOptions.map((size) => (
                        <option key={size} value={size}>
                          {size}
                        </option>
                      ))}
                    </select>
                    <span>{messages.components.ownerTransfer.items}</span>
                  </div>
                  <button
                    onClick={() => table.previousPage()}
                    disabled={!table.getCanPreviousPage()}
                    className="px-3 py-1 text-sm border border-[var(--border-color)] rounded-md bg-[var(--background)] text-[var(--foreground)] hover:bg-[var(--accent-secondary)] disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
                  >
                    {messages.components.ownerTransfer.previousPage}
                  </button>

                  <span className="text-sm text-[var(--muted)]">
                    {messages.components.ownerTransfer.page.replace("{page}", (table.getState().pagination.pageIndex + 1).toString()).replace("{total}", table.getPageCount().toString())}
                  </span>

                  <button
                    onClick={() => table.nextPage()}
                    disabled={!table.getCanNextPage()}
                    className="px-3 py-1 text-sm border border-[var(--border-color)] rounded-md bg-[var(--background)] text-[var(--foreground)] hover:bg-[var(--accent-secondary)] disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
                  >
                    {messages.components.ownerTransfer.nextPage}
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* modal footer */}
          <div className="flex justify-end items-center px-6 py-3 border-t border-[var(--border-color)]">
            <div className="flex items-center gap-x-2">
              <button
                className="px-4 py-2 text-sm font-medium rounded-md border border-[var(--border-color)] text-[var(--accent-primary)] hover:bg-[var(--accent-primary)]/10 transition-colors cursor-pointer"
                onClick={onClose}
              >
                {messages.common.cancel}
              </button>
              <button
                className="px-4 py-2 text-sm font-medium rounded-md border border-transparent bg-[var(--accent-primary)]/90 text-white hover:bg-[var(--accent-primary)] transition-colors disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
                onClick={onSubmit}
              >
                {messages.common.submit}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 确认转移所有者的弹窗 */}
      <DeleteConfirmModal
        isOpen={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        onConfirm={handleOwnerTransfer}
        title={messages.components.ownerTransfer.transferOwnerConfirm}
        message={selectedUser ? messages.components.ownerTransfer.transferOwnerMessage.replace("{user}", selectedUser.user_name).replace("{userCode}", selectedUser.user_code) : ""}
      />
    </div>
  );
};

export default OwnerTransfer;
