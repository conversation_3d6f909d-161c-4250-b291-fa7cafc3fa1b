'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { useFileSearch, FileSearchSuggestion } from '@/hooks/useFileSearch';
import FileSearchSuggestions from './FileSearchSuggestions';
import { useLanguage } from '@/contexts/LanguageContext';
import FileIcon from './FileIcon';
import { WikiStructure } from '@/types/wiki/wikistructure';
import { TrashIcon } from '@heroicons/react/24/outline';
import { useToast } from '@/contexts/ToastContext';
import { fileToBase64 } from '@/utils/fileUtil';
import ImageViewer from './ImageViewer';

interface FileReference {
  id: string;
  path: string;
  isDirectory: boolean;
  name: string;
}

interface FileReferenceInputProps {
  wikiStructure?: WikiStructure;
  value: string;
  onChange: (value: string) => void;
  onFileReferencesChange: (files: FileReference[]) => void;
  placeholder?: string;
  disabled?: boolean;
  repoUrl?: string;
  branch?: string;
  userCode?: string;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  onSubmit?: (question?: string, commandParams?: { operation: string | null, param: string | null }, images?: Array<string>) => void;
  className?: string;
  style?: React.CSSProperties;
  enableFileReferences?: boolean;
  onCanSendChange?: (canSend: boolean) => void;
  onFullInputChange?: (fullInput: string) => void;
  resetCurrentInput?: boolean; // 用于触发清空 currentInput
  images: Array<ImageObj>;
  setImages: (value: React.SetStateAction<Array<ImageObj>>) => void;
  isImageSupported: boolean;
}

// 将文本分解为文本段和标签段
interface TextSegment {
  type: 'text' | 'tag';
  content: string;
  fileRef?: FileReference;
}

interface ImageObj {
  url: string,
  base64: string,
}

const FileReferenceInput: React.FC<FileReferenceInputProps> = ({
  wikiStructure,
  value,
  onChange,
  onFileReferencesChange,
  placeholder = '向AI询问关于代码库的问题...',
  disabled = false,
  repoUrl = '',
  branch = 'main',
  userCode = '',
  onKeyDown,
  onSubmit,
  className = '',
  style,
  enableFileReferences = true,
  onCanSendChange,
  onFullInputChange,
  resetCurrentInput = false,
  images,
  setImages,
  isImageSupported
}) => {
  const { messages } = useLanguage();
  const { addToast } = useToast();
  
  const [fileReferences, setFileReferences] = useState<FileReference[]>([]);
  const [showFilePicker, setShowFilePicker] = useState(false);
  const [activeSuggestionIndex, setActiveSuggestionIndex] = useState(0);
  const [suggestionMode, setSuggestionMode] = useState<'inline' | 'button' | null>(null);
  const [suggestionSearchQuery, setSuggestionSearchQuery] = useState('');
  const [isComposing, setIsComposing] = useState(false);
  const [currentInput, setCurrentInput] = useState(''); // 当前输入的文本
  
  // 新增 / 命令相关状态
  const [showCommandPicker, setShowCommandPicker] = useState(false);
  const [commandOptions] = useState<Array<{id: string, label: string, description: string}>>([
    {id: 'wiki_title', label: 'wiki', description: messages?.components?.fileReferenceInput?.generateWikiTitle || '优化wiki内容'},
  ]);
  const [activeCommandIndex, setActiveCommandIndex] = useState(0);
  const [selectedCommand, setSelectedCommand] = useState<{id: string, label: string} | null>(null);
  // 命令与二级标题搜索关键词（仅用于前端过滤，不写入真实文本）
  const [commandSearchQuery, setCommandSearchQuery] = useState('');
  const [wikiTitleSearchQuery, setWikiTitleSearchQuery] = useState('');
  
  // 二级选择框状态（wiki标题选择）
  const [showWikiTitlePicker, setShowWikiTitlePicker] = useState(false);
  const [activeWikiTitleIndex, setActiveWikiTitleIndex] = useState(0);
  const [selectedWikiTitle, setSelectedWikiTitle] = useState<{id: string, title: string} | null>(null);
  // const [cursorPosition, setCursorPosition] = useState(0); // 光标在整个文本中的位置
  
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const searchDebounceRef = useRef<NodeJS.Timeout | null>(null);
  const highlighterRef = useRef<HTMLDivElement>(null);
  const [textareaScrollTop, setTextareaScrollTop] = useState(0);
  const wikiListRef = useRef<HTMLUListElement>(null);
  const commandListRef = useRef<HTMLUListElement>(null);
  const [hoveredImage, setHoveredImage] = useState<string>('');
  const [viewImage, setViewImage] = useState<string | null | undefined>();

  const fileSearch = useFileSearch({
    repoUrl,
    branch,
    userCode,
    enabled: enableFileReferences && !!repoUrl && !!userCode,
  });

  // 打开建议框（前置声明，供依赖使用）
  const openSuggestions = useCallback((mode: 'inline' | 'button', initialQuery = '') => {
    if (!repoUrl || !userCode) return;

    setSuggestionMode(mode);
    setShowFilePicker(true);
    setActiveSuggestionIndex(0);

    const query = mode === 'inline' ? '/' : (initialQuery || '/');
    setSuggestionSearchQuery(query);

    if (searchDebounceRef.current) clearTimeout(searchDebounceRef.current);
    searchDebounceRef.current = setTimeout(() => {
      fileSearch.search(query);
    }, 200);
  }, [repoUrl, userCode, fileSearch]);

  const generateId = useCallback(() => `file-ref-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`, []);

  const escapeRegex = useCallback((str: string) => str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), []);
  const escapeHtml = useCallback((unsafe: string) => unsafe
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;')
  , []);

  // 构造命令高亮的正则（优先匹配含参数的精确命令，其次匹配裸命令）
  const buildCommandRegex = useCallback(() => {
    const labels = commandOptions.map(o => escapeRegex(o.label));
    const bareAlternation = labels.length ? labels.join('|') : '';
    const patterns: string[] = [];

    if (selectedCommand && selectedWikiTitle) {
      // 精确匹配当前已选命令 + 二级标题（命令与标题之间使用空格分隔）
      const full = `/${escapeRegex(selectedCommand.label)} ${escapeRegex(selectedWikiTitle.title)}`;
      patterns.push(full);
    }

    if (bareAlternation) {
      // 裸命令：/label
      patterns.push(`/(?:${bareAlternation})`);
    }

    if (!patterns.length) return null;
    return new RegExp(`(${patterns.join('|')})`, 'g');
  }, [commandOptions, selectedCommand, selectedWikiTitle, escapeRegex]);

  // 生成用于镜像层展示的 HTML：将命令片段包裹成 chip
  const highlightedInputHtml = React.useMemo(() => {
    const text = currentInput || '';
    const regex = buildCommandRegex();

    if (!regex) {
      return escapeHtml(text);
    }

    const resultParts: string[] = [];
    let lastIndex = 0;
    let match: RegExpExecArray | null;

    while ((match = regex.exec(text)) !== null) {
      const matchText = match[0];
      const start = match.index;
      const end = start + matchText.length;

      if (start > lastIndex) {
        resultParts.push(escapeHtml(text.slice(lastIndex, start)));
      }

      // 命令 chip 的 HTML：使用绝对定位的背景层，不增加文本布局宽度，避免与真实 caret 位置产生偏差
      const chipHtml =
        `<span style=\"position:relative; display:inline;\">` +
          `<span style=\"position:absolute; left:0; right:0; top:-2px; bottom:-2px; border-radius:4px; background: rgba(191,219,254,0.95); box-shadow: 0 0 0 1px rgb(191,219,254) inset; -webkit-box-decoration-break: clone; box-decoration-break: clone;\"></span>` +
          `<span style=\"position:relative; color: rgb(29,78,216); font-size:14px; line-height:1.5;\">${escapeHtml(matchText)}</span>` +
        `</span>`;
      resultParts.push(chipHtml);

      lastIndex = end;
    }

    if (lastIndex < text.length) {
      resultParts.push(escapeHtml(text.slice(lastIndex)));
    }

    // 空内容时显示占位符（用镜像层处理，避免透明文本导致 placeholder 不可见）
    if (resultParts.length === 0 || (resultParts.length === 1 && resultParts[0] === '')) {
      return escapeHtml('');
    }

    return resultParts.join('');
  }, [currentInput, buildCommandRegex, escapeHtml]);

  // 过滤后的命令与 wiki 页面列表
  const filteredCommandOptions = React.useMemo(() => {
    const q = commandSearchQuery.trim().toLowerCase();
    if (!q) return commandOptions;
    return commandOptions.filter(o =>
      o.label.toLowerCase().includes(q) || (o.description || '').toLowerCase().includes(q)
    );
  }, [commandOptions, commandSearchQuery]);

  const filteredWikiPages = React.useMemo(() => {
    const pages = wikiStructure?.pages || [];
    const q = wikiTitleSearchQuery.trim().toLowerCase();
    if (!q) return pages;
    return pages.filter(p => (p.title || '').toLowerCase().includes(q));
  }, [wikiStructure?.pages, wikiTitleSearchQuery]);

  // 解析文本为段落
  const parseTextToSegments = useCallback((text: string): TextSegment[] => {
    const segments: TextSegment[] = [];
    const parts = text.split(/(@[^\s@\n]+)/g);
      
    for (const part of parts) {
      if (part.startsWith('@') && part.length > 1) {
        const fileName = part.slice(1);
        const fileRef = fileReferences.find(ref => ref.name === fileName);
        segments.push({
          type: 'tag',
          content: fileName,
          fileRef
        });
      } else if (part) {
        // 保留换行符，将文本按行分割
        const lines = part.split('\n');
        for (let i = 0; i < lines.length; i++) {
          if (lines[i]) {
            segments.push({
              type: 'text',
              content: lines[i]
            });
          }
          // 在行之间添加换行符（除了最后一行）
          if (i < lines.length - 1) {
            segments.push({
              type: 'text',
              content: '\n'
            });
          }
        }
      }
    }
    
    return segments;
  }, [fileReferences]);

  // 从段落重建文本
  const segmentsToText = useCallback((segments: TextSegment[]): string => {
    return segments.map(seg => 
      seg.type === 'tag' ? `@${seg.content}` : seg.content
    ).join('');
  }, []);

  // 同步文件引用 - 保持现有引用，不基于文本内容清空
  const syncFileReferences = useCallback(() => {
    // 不再基于文本内容来同步文件引用
    // 文件引用应该独立于文本内容存在
    // 只有在用户明确删除时才移除引用
  }, []);

  useEffect(() => {
    syncFileReferences();
  }, [syncFileReferences]);

  // 删除文件引用
  const removeFileReference = useCallback((id: string) => {
    setFileReferences(prev => {
      const refToRemove = prev.find(f => f.id === id);
      const next = prev.filter(f => f.id !== id);
      // 先更新引用区
      if (next.length !== prev.length) {
        onFileReferencesChange(next);
      }
      // 同步文本，移除所有该引用的 @name（若存在）
      if (refToRemove) {
        const newText = value.replace(new RegExp(`@${refToRemove.name}(?=\\s|$)`, 'g'), '').replace(/\s{2,}/g, ' ').trim();
        if (newText !== value) {
          onChange(newText);
        }
      }
      return next;
    });
  }, [onFileReferencesChange, value, onChange]);

  // 处理普通输入
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setCurrentInput(newValue);
    if (onCanSendChange) {
      const canSend = (newValue.trim().length > 0) || (value.trim().length > 0);
      onCanSendChange(canSend);
    }
    
    // 获取光标位置和光标前的文本
    const cursorPos = e.target.selectionStart || 0;
    const textBeforeCursor = newValue.substring(0, cursorPos);
    
    // 检查"/"触发命令选择器
    if (newValue[cursorPos - 1] === '/') {
      const charBefore = cursorPos >= 2 ? newValue[cursorPos - 2] : '';
      const isValidSlashPosition = cursorPos === 1 || charBefore === ' ' || charBefore === '\n';
      
      if (isValidSlashPosition) {
        // 显示命令选择器
        setShowCommandPicker(true);
        setActiveCommandIndex(0);
        setCommandSearchQuery('');
        // 隐藏文件选择器（如果正在显示）
        setShowFilePicker(false);
        setSuggestionMode(null);
      }
    } 
    // 如果命令选择器已显示，检查是否需要关闭
    else if (showCommandPicker) {
      const lastSlashPos = textBeforeCursor.lastIndexOf('/');
      
      if (lastSlashPos >= 0) {
        const textAfterSlash = textBeforeCursor.substring(lastSlashPos + 1);
        // 如果/后面输入了空格或换行，关闭命令选择器
        if (/[\s\n]/.test(textAfterSlash)) {
          setShowCommandPicker(false);
          setCommandSearchQuery('');
        }
        else {
          // 将/后的文本作为前端过滤关键词
          setCommandSearchQuery(textAfterSlash);
          setActiveCommandIndex(0);
        }
      } else {
        // 如果删除了/，关闭命令选择器
        setShowCommandPicker(false);
        setCommandSearchQuery('');
      }
    }
    // 二级标题选择器打开时，使用 /wiki 后的内容作为过滤关键词
    else if (showWikiTitlePicker) {
      const cmd = '/wiki';
      const idx = textBeforeCursor.lastIndexOf(cmd);
      if (idx >= 0) {
        const q = textBeforeCursor.substring(idx + cmd.length);
        setWikiTitleSearchQuery(q.replace(/^\s+/, ''));
        setActiveWikiTitleIndex(0);
      }
    }
    
    // 检查@触发文件选择器
    else if (newValue[cursorPos - 1] === '@') {
      const charBefore = cursorPos >= 2 ? newValue[cursorPos - 2] : '';
      const isValidAtPosition = cursorPos === 1 || charBefore === ' ' || charBefore === '\n';
      
      if (isValidAtPosition && enableFileReferences && repoUrl && userCode) {
        setSuggestionSearchQuery('/');
        openSuggestions('inline', '/');
        // 确保命令选择器关闭
        setShowCommandPicker(false);
      }
    } 
    // 如果文件选择器已显示，处理文件搜索
    else if (showFilePicker && suggestionMode === 'inline') {
      const lastAtPos = textBeforeCursor.lastIndexOf('@');
      
      if (lastAtPos >= 0) {
        const searchTerm = textBeforeCursor.substring(lastAtPos + 1);
        
        if (/[\s\n]/.test(searchTerm)) {
          setShowFilePicker(false);
          setSuggestionMode(null);
          fileSearch.reset();
        } else {
          setSuggestionSearchQuery(searchTerm);
          if (searchDebounceRef.current) clearTimeout(searchDebounceRef.current);
          searchDebounceRef.current = setTimeout(() => {
            fileSearch.search(searchTerm || '/');
          }, 200);
        }
      } else {
        setShowFilePicker(false);
        setSuggestionMode(null);
        fileSearch.reset();
      }
    }
  }, [enableFileReferences, repoUrl, userCode, showFilePicker, suggestionMode, fileSearch, showCommandPicker, value, onCanSendChange, openSuggestions, showWikiTitlePicker]);


  // 打开建议框（移至上方前置声明后，此处删除重复定义）

  // 监听全局文件搜索事件
  useEffect(() => {
    const handler = () => {
      if (enableFileReferences && repoUrl && userCode) {
        openSuggestions('button', '/');
        // 聚焦到输入框，便于继续筛选
        requestAnimationFrame(() => inputRef.current?.focus());
      }
    };
    window.addEventListener('global-file-search', handler);
    return () => window.removeEventListener('global-file-search', handler);
  }, [openSuggestions, enableFileReferences, repoUrl, userCode]);

  // 处理建议选择
  const handleSuggestionSelect = useCallback((suggestion: FileSearchSuggestion) => {
    if (suggestion.isDirectory) {
      let selectedValue = suggestion.value;
      if (!selectedValue.endsWith('/')) selectedValue += '/';
      
        setSuggestionSearchQuery(selectedValue);
        if (searchDebounceRef.current) clearTimeout(searchDebounceRef.current);
        searchDebounceRef.current = setTimeout(() => {
          fileSearch.search(selectedValue);
       }, 200);
      setActiveSuggestionIndex(0);
    } else {
      const newFile: FileReference = {
        id: generateId(),
        path: suggestion.value,
        isDirectory: suggestion.isDirectory,
        name: suggestion.label.split('/').pop() || suggestion.label,
      };
      
      // 按钮模式：保持建议面板打开，支持多选
      if (suggestionMode !== 'inline') {
        // 不关闭建议框，仅更新引用区
        setFileReferences(prev => {
          if (prev.some(f => f.path === newFile.path)) return prev;
          const newRefs = [...prev, newFile];
          onFileReferencesChange(newRefs);
          return newRefs;
        });
        return;
      }
      
      // 内联模式：关闭建议框，替换@为标签并追加空格
      setShowFilePicker(false);
      setSuggestionMode(null);
      fileSearch.reset();
      
      // 添加文件引用
      setFileReferences(prev => {
        if (prev.some(f => f.path === newFile.path)) return prev;
        const newRefs = [...prev, newFile];
        onFileReferencesChange(newRefs);
        return newRefs;
      });
      
      if (suggestionMode === 'inline') {
        // 替换@搜索为标签，保留@后面的内容
        const cursorPos = inputRef.current?.selectionStart || 0;
        const textBeforeCursor = currentInput.substring(0, cursorPos);
        const textAfterCursor = currentInput.substring(cursorPos);
        
        const atPos = textBeforeCursor.lastIndexOf('@');
        if (atPos >= 0) {
          const beforeAt = currentInput.substring(0, atPos);
          // 查找@搜索词的结束位置
          // const searchStart = atPos + 1;
          // const searchText = textBeforeCursor.substring(searchStart);
          
          // 构建新的输入内容：前面部分 + 文件名 + 空格 + 后面部分
          const newInput = beforeAt + `${newFile.name} ` + textAfterCursor;
          setCurrentInput(newInput);
          
          // 设置光标位置到插入文件名后的空格之后
          const newCursorPos = beforeAt.length + newFile.name.length + 1;
          setTimeout(() => {
            if (inputRef.current) {
              inputRef.current.focus();
              inputRef.current.setSelectionRange(newCursorPos, newCursorPos);
            }
          }, 0);
        }
      }
    }
  }, [suggestionMode, currentInput, onFileReferencesChange, generateId, fileSearch]);

  // 处理目录选择
  const handleDirectorySelect = useCallback((suggestion: FileSearchSuggestion) => {
    const newDir: FileReference = {
      id: generateId(),
      path: suggestion.value,
      isDirectory: true,
      name: suggestion.label.split('/').filter(Boolean).pop() || suggestion.label,
    };
    
    // 按钮模式：保持建议面板打开，支持多选
    if (suggestionMode !== 'inline') {
      // 不关闭建议框，仅更新引用区
      setFileReferences(prev => {
        if (prev.some(f => f.path === newDir.path)) return prev;
        const newRefs = [...prev, newDir];
        onFileReferencesChange(newRefs);
        return newRefs;
      });
      return;
    }
    
    // 内联模式：关闭建议框，替换@为标签并追加空格
    setShowFilePicker(false);
    setSuggestionMode(null);
    fileSearch.reset();
    
    setFileReferences(prev => {
      if (prev.some(f => f.path === newDir.path)) return prev;
      const newRefs = [...prev, newDir];
      onFileReferencesChange(newRefs);
      return newRefs;
    });
    
    if (suggestionMode === 'inline') {
      // 替换@搜索为目录名，保留@后面的内容
      const cursorPos = inputRef.current?.selectionStart || 0;
      const textBeforeCursor = currentInput.substring(0, cursorPos);
      const textAfterCursor = currentInput.substring(cursorPos);
      
      const atPos = textBeforeCursor.lastIndexOf('@');
      if (atPos >= 0) {
        const beforeAt = currentInput.substring(0, atPos);
        // 构建新的输入内容：前面部分 + 目录名 + 空格 + 后面部分
        const newInput = beforeAt + `${newDir.name} ` + textAfterCursor;
        setCurrentInput(newInput);
        
        // 设置光标位置到插入目录名后的空格之后
        const newCursorPos = beforeAt.length + newDir.name.length + 1;
        setTimeout(() => {
          if (inputRef.current) {
            inputRef.current.focus();
            inputRef.current.setSelectionRange(newCursorPos, newCursorPos);
          }
        }, 0);
      }
    }
  }, [suggestionMode, currentInput, onFileReferencesChange, generateId, fileSearch]);

  // 处理命令选择
  const handleCommandSelect = useCallback((command: {id: string, label: string}) => {
    // 关闭命令选择器
    setShowCommandPicker(false);
    
    // 获取光标位置和文本
    const cursorPos = inputRef.current?.selectionStart || 0;
    const textBeforeCursor = currentInput.substring(0, cursorPos);
    const textAfterCursor = currentInput.substring(cursorPos);
    
    // 找到最后一个/的位置
    const lastSlashPos = textBeforeCursor.lastIndexOf('/');
    if (lastSlashPos >= 0) {
      // 替换/为选中的命令
      const beforeSlash = currentInput.substring(0, lastSlashPos);
      // 创建新的命令文本（带有特殊样式标记）
      const commandText = `/${command.label}`;
      
      // 设置选中的命令
      setSelectedCommand(command);
      
      // 更新输入内容
      const newInput = beforeSlash + commandText + ' ' + textAfterCursor; // 在命令后添加一个真实的空格
      setCurrentInput(newInput);
      
      // 设置光标位置到命令后面
      const newCursorPos = beforeSlash.length + commandText.length + 1; // 光标置于命令后空格处
      
      // 如果选择的是 wiki_title 命令，显示二级选择框
      if (command.id === 'wiki_title') {
        setShowWikiTitlePicker(true);
        setActiveWikiTitleIndex(0);
        setSelectedWikiTitle(null);
      }
      
      // 使用 requestAnimationFrame 确保 DOM 更新后再设置光标位置
      requestAnimationFrame(() => {
        if (inputRef.current) {
          inputRef.current.focus();
          inputRef.current.setSelectionRange(newCursorPos, newCursorPos);
        }
      });
    }
  }, [currentInput]);

  // 处理 wiki 标题选择
  const handleWikiTitleSelect = useCallback((wikiTitle: {id: string, title: string}) => {
    // 关闭 wiki 标题选择器
    setShowWikiTitlePicker(false);
    
    // 设置选中的 wiki 标题
    setSelectedWikiTitle(wikiTitle);
    
    // 获取光标位置和文本
    const cursorPos = inputRef.current?.selectionStart || 0;
    const textBeforeCursor = currentInput.substring(0, cursorPos);
    const textAfterCursor = currentInput.substring(cursorPos);
    
    // 找到 "wiki" 命令的位置（命令与标题以空格分隔）
    const commandText = '/wiki';
    const commandIndex = textBeforeCursor.lastIndexOf(commandText);
    
    if (commandIndex >= 0) {
      // 在命令后添加选中的标题
      const beforeCommand = currentInput.substring(0, commandIndex + commandText.length);
      
      // 构建新的输入内容：命令 + 空格 + 选中的标题 + 后面部分
      const newInput = beforeCommand + ` ${wikiTitle.title}` + ' ' + textAfterCursor; // 在二级标题后添加空格
      setCurrentInput(newInput);
      
      // 设置光标位置到新添加内容后面
      const newCursorPos = beforeCommand.length + ` ${wikiTitle.title}`.length + 1; // 光标置于空格处
      
      // 使用 requestAnimationFrame 确保 DOM 更新后再设置光标位置
      requestAnimationFrame(() => {
        if (inputRef.current) {
          inputRef.current.focus();
          inputRef.current.setSelectionRange(newCursorPos, newCursorPos);
        }
      });
    }
  }, [currentInput]);

  // 解析命令参数
  const parseCommandParams = useCallback((inputText: string) => {
    let operation = null;
    let param = null;
    let cleanText = inputText;

    if (selectedCommand) {
      operation = selectedCommand.label;
      
      if (selectedCommand.id === 'wiki_title' && selectedWikiTitle) {
        param = selectedWikiTitle.title;
        // 从文本中移除命令部分（命令与标题之间使用空格）
        const commandPattern = `/${selectedCommand.label} ${selectedWikiTitle.title}`;
        cleanText = inputText.replace(new RegExp(commandPattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), '').trim();
      } else {
        // 从文本中移除命令部分
        const commandPattern = `/${selectedCommand.label}`;
        cleanText = inputText.replace(new RegExp(commandPattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), '').trim();
      }
    }

    return { operation, param, cleanText };
  }, [selectedCommand, selectedWikiTitle]);

  // 优先级删除：当选中了一级命令（可能未选二级）时，Backspace 整块删除命令 token
  const tryDeleteSelectedCommandToken = useCallback((e: React.KeyboardEvent) => {
    if (!selectedCommand) return false;

    const cursorPos = inputRef.current?.selectionStart || 0;
    const commandText = `/${selectedCommand.label}`;
    let fullCommandText = commandText;

    if (selectedCommand.id === 'wiki_title' && selectedWikiTitle) {
      fullCommandText = `${commandText} ${selectedWikiTitle.title}`;
    }

    const tokenIndex = currentInput.lastIndexOf(fullCommandText, Math.max(cursorPos - 1, 0));
    if (tokenIndex < 0) return false;

    const charAfterToken = currentInput[tokenIndex + fullCommandText.length] || '';
    const tokenEndIncludingSpace = charAfterToken === ' ' ? (tokenIndex + fullCommandText.length + 1) : (tokenIndex + fullCommandText.length);

    if (cursorPos > 0 && cursorPos <= tokenEndIncludingSpace) {
      e.preventDefault();
      const newInput = currentInput.substring(0, tokenIndex) + currentInput.substring(tokenEndIncludingSpace);
      setCurrentInput(newInput);
      setSelectedCommand(null);
      setSelectedWikiTitle(null);
      setShowWikiTitlePicker(false);
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
          inputRef.current.setSelectionRange(tokenIndex, tokenIndex);
        }
      }, 0);
      return true;
    }

    return false;
  }, [selectedCommand, selectedWikiTitle, currentInput]);

  // 处理键盘事件
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (isComposing) return;

    // 在任何分支前先处理命令整块删除逻辑（无论是否打开二级选择器都生效）
    if (e.key === 'Backspace' && selectedCommand) {
      if (tryDeleteSelectedCommandToken(e)) return;
    }

    // Ctrl/Cmd + Enter => 强制插入换行
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      const ta = inputRef.current;
      const start = ta?.selectionStart ?? currentInput.length;
      const end = ta?.selectionEnd ?? currentInput.length;
      const before = currentInput.slice(0, start);
      const after = currentInput.slice(end);
      const newVal = `${before}\n${after}`;
      setCurrentInput(newVal);
      // 恢复光标位置到换行后
      requestAnimationFrame(() => {
        if (ta) {
          const pos = start + 1;
          try { ta.setSelectionRange(pos, pos); } catch {}
        }
      });
      return;
    }
    
    // 处理 wiki 标题选择器的键盘导航
    if (showWikiTitlePicker && filteredWikiPages.length > 0) {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setActiveWikiTitleIndex(prev => Math.min(prev + 1, filteredWikiPages.length - 1));
          requestAnimationFrame(() => {
            const list = wikiListRef.current;
            const idx = Math.min(activeWikiTitleIndex + 1, (filteredWikiPages.length || 1) - 1);
            const item = list?.children?.[idx] as HTMLElement | undefined;
            if (list && item) {
              const itemTop = item.offsetTop;
              const itemBottom = itemTop + item.offsetHeight;
              if (itemBottom > list.scrollTop + list.clientHeight) {
                list.scrollTop = itemBottom - list.clientHeight;
              } else if (itemTop < list.scrollTop) {
                list.scrollTop = itemTop;
              }
            }
          });
          break;
        case 'ArrowUp':
          e.preventDefault();
          setActiveWikiTitleIndex(prev => Math.max(prev - 1, 0));
          requestAnimationFrame(() => {
            const list = wikiListRef.current;
            const idx = Math.max(activeWikiTitleIndex - 1, 0);
            const item = list?.children?.[idx] as HTMLElement | undefined;
            if (list && item) {
              const itemTop = item.offsetTop;
              const itemBottom = itemTop + item.offsetHeight;
              if (itemTop < list.scrollTop) {
                list.scrollTop = itemTop;
              } else if (itemBottom > list.scrollTop + list.clientHeight) {
                list.scrollTop = itemBottom - list.clientHeight;
              }
            }
          });
          break;
        case 'Enter':
          // 选择当前 wiki 标题
          if (!(e.ctrlKey || e.metaKey)) {
            e.preventDefault();
            const currentWikiTitle = filteredWikiPages[activeWikiTitleIndex];
            if (currentWikiTitle) {
              handleWikiTitleSelect({id: currentWikiTitle.id, title: currentWikiTitle.title});
            }
          }
          break;
        case 'Escape':
          e.preventDefault();
          setShowWikiTitlePicker(false);
          break;
        default:
          if (onKeyDown) onKeyDown(e);
          return;
      }
    }
    // 处理命令选择器的键盘导航
    else if (showCommandPicker && filteredCommandOptions.length > 0) {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setActiveCommandIndex(prev => Math.min(prev + 1, filteredCommandOptions.length - 1));
          requestAnimationFrame(() => {
            const list = commandListRef.current;
            const idx = Math.min(activeCommandIndex + 1, filteredCommandOptions.length - 1);
            const item = list?.children?.[idx] as HTMLElement | undefined;
            if (list && item) {
              const itemTop = item.offsetTop;
              const itemBottom = itemTop + item.offsetHeight;
              if (itemBottom > list.scrollTop + list.clientHeight) {
                list.scrollTop = itemBottom - list.clientHeight;
              } else if (itemTop < list.scrollTop) {
                list.scrollTop = itemTop;
              }
            }
          });
          break;
        case 'ArrowUp':
          e.preventDefault();
          setActiveCommandIndex(prev => Math.max(prev - 1, 0));
          requestAnimationFrame(() => {
            const list = commandListRef.current;
            const idx = Math.max(activeCommandIndex - 1, 0);
            const item = list?.children?.[idx] as HTMLElement | undefined;
            if (list && item) {
              const itemTop = item.offsetTop;
              const itemBottom = itemTop + item.offsetHeight;
              if (itemTop < list.scrollTop) {
                list.scrollTop = itemTop;
              } else if (itemBottom > list.scrollTop + list.clientHeight) {
                list.scrollTop = itemBottom - list.clientHeight;
              }
            }
          });
          break;
        case 'Enter':
          // 选择当前命令
          if (!(e.ctrlKey || e.metaKey)) {
            e.preventDefault();
            const currentCommand = filteredCommandOptions[activeCommandIndex];
            if (currentCommand) {
              handleCommandSelect(currentCommand);
            }
          }
          break;
        case 'Escape':
          e.preventDefault();
          setShowCommandPicker(false);
          break;
        default:
          if (onKeyDown) onKeyDown(e);
          return;
      }
    }
    // 处理文件选择器的键盘导航
    else if (showFilePicker && fileSearch.suggestions.length > 0) {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setActiveSuggestionIndex(prev => Math.min(prev + 1, fileSearch.suggestions.length - 1));
          break;
        case 'ArrowUp':
          e.preventDefault();
          setActiveSuggestionIndex(prev => Math.max(prev - 1, 0));
          break;
        case 'Enter':
          // 当按下 Ctrl/Cmd+Enter 时，交给上一段逻辑处理换行；否则选择建议
          if (!(e.ctrlKey || e.metaKey)) {
            e.preventDefault();
            const currentSuggestion = fileSearch.suggestions[activeSuggestionIndex];
            if (currentSuggestion) {
              if (currentSuggestion.isDirectory && e.shiftKey) {
                // 用 Shift+Enter 也可选择目录（避免与换行冲突）
                handleDirectorySelect(currentSuggestion);
              } else {
                handleSuggestionSelect(currentSuggestion);
              }
            }
          }
          break;
        case 'Escape':
          e.preventDefault();
          setShowFilePicker(false);
          setSuggestionMode(null);
          break;
        default:
          if (onKeyDown) onKeyDown(e);
          return;
      }
    } else if (e.key === 'Enter') {
      if (e.ctrlKey || e.metaKey) {
        // Ctrl/Cmd + Enter => 换行（使用默认或前面的插入逻辑）
        return;
      }
      // 单独 Enter => 发送
      e.preventDefault();
      if (currentInput.trim()) {
        const newValue = value + (value ? ' ' : '') + currentInput;
        onChange(newValue);
        
        // 解析命令参数
        const { operation, param, cleanText } = parseCommandParams(newValue);
        
        // 重置命令状态
        setSelectedCommand(null);
        setSelectedWikiTitle(null);
        setCurrentInput('');
        setImages([]);

        if (onSubmit) {
          // 传递解析后的参数
          onSubmit(cleanText, { operation, param }, images.map(item => item.base64));
        }
      } else if (onSubmit) {
        onSubmit();
      }
    } else {
      // 处理选中命令的删除
      if (e.key === 'Backspace' && selectedCommand) {
        const cursorPos = inputRef.current?.selectionStart || 0;
        const commandText = `/${selectedCommand.label}`;
        let fullCommandText = commandText;

        // 如果选中的是 wiki_title 命令且有选中的 wiki 标题，使用完整路径
        if (selectedCommand.id === 'wiki_title' && selectedWikiTitle) {
          // 现在命令与标题之间使用空格
          fullCommandText = `${commandText} ${selectedWikiTitle.title}`;
        }

        // 查找靠近光标位置的命令片段起始索引
        const tokenIndex = currentInput.lastIndexOf(fullCommandText, Math.max(cursorPos - 1, 0));
        if (tokenIndex >= 0) {
          const charAfterToken = currentInput[tokenIndex + fullCommandText.length] || '';
          const tokenEndIncludingSpace = charAfterToken === ' ' ? (tokenIndex + fullCommandText.length + 1) : (tokenIndex + fullCommandText.length);

          // 如果光标位于命令片段内部或紧随其后（包括紧随其后的空格），整块删除
          if (cursorPos > 0 && cursorPos <= tokenEndIncludingSpace) {
            e.preventDefault();

            // 删除整个命令（包含尾随空格）
            const newInput =
              currentInput.substring(0, tokenIndex) +
              currentInput.substring(tokenEndIncludingSpace);
            setCurrentInput(newInput);

            // 重置选中的命令和 wiki 标题，并关闭二级选择器
            setSelectedCommand(null);
            setSelectedWikiTitle(null);
            setShowWikiTitlePicker(false);

            // 设置光标位置
            setTimeout(() => {
              if (inputRef.current) {
                inputRef.current.focus();
                inputRef.current.setSelectionRange(tokenIndex, tokenIndex);
              }
            }, 0);
            return;
          }
        }
      }

      // 输入框为空时，Backspace 删除上一段（文本或标签），并联动引用区
      if (e.key === 'Backspace' && currentInput.length === 0) {
        const segs = parseTextToSegments(value);
        if (segs.length > 0) {
          e.preventDefault();
          const last = segs[segs.length - 1];
          const nextSegs = segs.slice(0, -1);
          const newValueText = segmentsToText(nextSegs);
          onChange(newValueText.trimEnd());
          if (last.type === 'tag') {
            setFileReferences(prev => {
              const next = prev.filter(r => r.name !== last.content);
              if (next.length !== prev.length) onFileReferencesChange(next);
              return next;
            });
          }
          requestAnimationFrame(() => inputRef.current?.focus());
          return;
        }
      }
      if (onKeyDown) onKeyDown(e);
    }
  }, [isComposing, showFilePicker, fileSearch.suggestions, activeSuggestionIndex, handleSuggestionSelect, handleDirectorySelect, currentInput, onSubmit, onKeyDown, value, parseCommandParams, onChange, onFileReferencesChange, showCommandPicker, activeCommandIndex, handleCommandSelect, selectedCommand, selectedWikiTitle, showWikiTitlePicker, activeWikiTitleIndex, handleWikiTitleSelect, filteredCommandOptions, filteredWikiPages, tryDeleteSelectedCommandToken, parseTextToSegments, segmentsToText]);
  
  const segments = parseTextToSegments(value);

  // 在 value 或 currentInput 变化时，报告可发送状态和完整输入
  useEffect(() => {
    if (onCanSendChange) {
      onCanSendChange((currentInput.trim().length > 0) || (value.trim().length > 0));
    }
    if (onFullInputChange) {
      const fullInput = value + (value && currentInput ? ' ' : '') + currentInput;
      onFullInputChange(fullInput);
    }
  }, [currentInput, value, onCanSendChange, onFullInputChange]);

  // 监听 value 变化，如果 value 变化且 resetCurrentInput 为 true，说明需要覆盖
  // 这时应该将 value 的内容移到 currentInput 中
  useEffect(() => {
    if (resetCurrentInput && value) {
      // 将 value 的内容设置到 currentInput（流式更新）
      setCurrentInput(value);
      
      // 聚焦到输入框
      if (inputRef.current) {
        inputRef.current.focus();
        // 将光标移到末尾
        setTimeout(() => {
          if (inputRef.current) {
            const length = value.length;
            inputRef.current.setSelectionRange(length, length);
          }
        }, 0);
      }
    }
  }, [resetCurrentInput, value]);

  // 监听来自父级的发送请求
  useEffect(() => {
    const submitHandler = () => {
      if (disabled) return;
      if (currentInput.trim()) {
        const newValue = value + (value ? ' ' : '') + currentInput;
        onChange(newValue);
        
        // 解析命令参数
        const { operation, param, cleanText } = parseCommandParams(newValue);
        
        // 重置命令状态
        setSelectedCommand(null);
        setSelectedWikiTitle(null);
        setCurrentInput('');
        setImages([]);
        
        if (onSubmit) {
          // 传递解析后的参数
          onSubmit(cleanText, { operation, param }, images.map(item => item.base64));
        }
      } else {
        if (onSubmit) onSubmit();
      }
    };
    window.addEventListener('file-ref-submit', submitHandler);
    return () => window.removeEventListener('file-ref-submit', submitHandler);
  }, [currentInput, value, onChange, onSubmit, disabled, parseCommandParams, images]);

  const onTextAreaPaste = async (event: React.ClipboardEvent<HTMLTextAreaElement>) => {
    if (!isImageSupported) {
      return;
    }
    if (images.length >= 4) {
      addToast({
        type: 'warning',
        title: messages?.components?.fileReferenceInput.imageLimit || 'A maximum of 4 images is supported',
        message: ''
      });
      return;
    }
    if (event.clipboardData) {
      const items: DataTransferItemList = event.clipboardData.items;
      for (const item of items) {
        if (item.kind === 'file') {
          if (item.type.startsWith("image")) {
            const file = item.getAsFile();
            if (file) {
              if (file.size > 5242880) {
                addToast({
                  type: 'warning',
                  title: messages?.components?.fileReferenceInput.imageSizeLimit || 'The image size must not exceed 5MB.',
                  message: ''
                });
                return;
              }
              const imageUrl = URL.createObjectURL(file);
              const base64 = await fileToBase64(file) as string;
              setImages(pre => [...pre, {
                url: imageUrl,
                base64: base64
              }]);
            }
          } else {
            addToast({
              type: 'warning',
              title: messages?.components?.fileReferenceInput.imageOnly || 'Only image file types are supported.',
              message: ''
            });
            return;
          }
        }
      }
    }
  }

  return (
    <div 
      className={`relative ${className} border border-gray-200/50 rounded-xl focus-within:ring-2 focus-within:ring-blue-500/20 focus-within:border-blue-500/40`} 
      style={style}
      ref={containerRef}
    >
      {enableFileReferences && (
        <div className="flex items-center justify-between gap-2 p-2 border-b border-gray-200/50">
          <div className="flex items-center flex-wrap gap-2 flex-1">
            {/* @ 按钮 */}
            <button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                openSuggestions('button', '/');
              }}
              disabled={disabled}
              className={`flex-shrink-0 h-7 px-2 flex items-center justify-center rounded-md transition-all duration-200 ${
                disabled
                  ? 'text-gray-400 cursor-not-allowed bg-gray-50'
                  : 'text-blue-600 hover:text-blue-700 hover:bg-blue-50 border-blue-200 bg-blue-50/30'
              }`}
              title={messages?.components?.fileReferenceInput?.addFileReference || "添加文件引用 (@)"}
              style={{ userSelect: 'none' }}
            >
              <span className="text-sm font-bold leading-none" style={{ userSelect: 'none' }}>@</span>
            </button>

            {fileReferences.map((file) => (
              <span 
                key={file.id}
                className={`inline-flex items-center gap-1 px-1.5 py-0.5 border rounded text-xs font-medium transition-all duration-200 cursor-pointer group ${
                  file.isDirectory 
                    ? 'bg-green-100/95 border-green-200 text-green-700 hover:bg-green-200/95' 
                    : 'bg-blue-100/95 border-blue-200 text-blue-700 hover:bg-blue-200/95'
                }`}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    removeFileReference(file.id);
                  }}
                  title={`${messages?.components?.fileReferenceInput?.removeFileReference || "删除文件引用"}: ${file.path}`}
                  style={{ userSelect: 'none' }}
                >
                  <span className="flex-shrink-0 group-hover:hidden">
                    <FileIcon fileName={file.name} isDirectory={file.isDirectory} className="w-3 h-3" />
                  </span>
                  <svg 
                    className="w-3 h-3 flex-shrink-0 hidden group-hover:block text-red-500" 
                    fill="none" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    viewBox="0 0 24 24"
                    onClick={(e) => {
                      e.stopPropagation();
                      removeFileReference(file.id);
                    }}
                  >
                    <path d="M6 18L18 6M6 6l12 12"/>
                  </svg>
                  <span className="truncate max-w-[120px] select-none">
                    {file.name}
                  </span>
                </span>
              ))}
          </div>
        </div>
      )}

      <div className="relative">
        {/* 显示已确认的内容 */}
        <div 
          className="min-h-[40px] overflow-y-auto p-3 pr-14 text-sm bg-transparent"
          onMouseDown={(e) => {
            const ta = inputRef.current;
            // 如果直接点击在 textarea 上，则允许原生光标定位行为
            if (ta && e.target === ta) {
              return;
            }
            // 仅当点击在展示区域时，才拦截并将焦点移到 textarea
            e.preventDefault();
            e.stopPropagation();
            if (ta) {
              const len = ta.value?.length || 0;
              setTimeout(() => {
                ta.focus();
                try {
                  ta.setSelectionRange(len, len);
                } catch {}
              }, 0);
            }
          }}
          style={{ userSelect: 'none' }}
        >
          <div className="w-full overflow-hidden" style={{ lineHeight: '1.5', userSelect: 'none' }}>
            {/* 已确认的内容区域 - 在流式更新期间隐藏，避免闪烁 */}
            {!resetCurrentInput && (
              <div 
                className="break-words"
                style={{ 
                  wordBreak: 'break-word',
                  overflowWrap: 'break-word',
                  whiteSpace: 'pre-wrap',
                  maxWidth: '100%'
                }}
              >
                {segments.map((segment, index) => (
                  <React.Fragment key={index}>
                    {segment.type === 'tag' && segment.fileRef ? (
                      <span 
                        className={`inline-flex items-center gap-1 px-1.5 py-0.5 text-xs font-medium rounded cursor-pointer transition-colors group ${
                          segment.fileRef.isDirectory 
                            ? 'bg-green-100 text-green-700 hover:bg-green-200' 
                            : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                        }`}
                        onClick={() => removeFileReference(segment.fileRef!.id)}
                      >
                        <FileIcon fileName={segment.fileRef.name} isDirectory={segment.fileRef.isDirectory} className="w-2.5 h-2.5 file-icon" />
                        <span className="file-name">{segment.fileRef.name}</span>
                        <svg 
                          className="w-2.5 h-2.5 delete-icon opacity-0 group-hover:opacity-100 text-red-500" 
                          fill="none" 
                          stroke="currentColor" 
                          strokeWidth="2" 
                          viewBox="0 0 24 24"
                          onClick={(e) => {
                            e.stopPropagation();
                            removeFileReference(segment.fileRef!.id);
                          }}
                        >
                          <path d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                      </span>
                    ) : segment.type === 'tag' ? (
                      // 显示未找到文件引用的@标签，但仍然高亮
                      <span className="px-1 py-0.5 bg-yellow-100 text-yellow-800 rounded text-xs font-medium">
                        @{segment.content}
                      </span>
                    ) : (
                      <span style={{ wordBreak: 'break-word', overflowWrap: 'break-word' }}>
                        {segment.content === '\n' ? <br /> : segment.content}
                      </span>
                    )}
                  </React.Fragment>
                ))}
              </div>
            )}
            
            
            {/* 当前输入镜像高亮层 + 输入框（支持多行） */}
            <div className="relative">
              {/* 高亮镜像层：仅渲染视觉效果，不接管输入 */}
              <div
                ref={highlighterRef}
                className="absolute inset-0 pointer-events-none whitespace-pre-wrap break-words text-sm"
                style={{
                  lineHeight: '1.5',
                  fontSize: '14px',
                  fontFamily: 'inherit',
                  color: (!value && !currentInput) ? 'var(--muted)' : 'inherit',
                  transform: `translateY(-${textareaScrollTop}px)`,
                  zIndex: 0
                }}
                // 渲染高亮后的 HTML
                dangerouslySetInnerHTML={{ __html: highlightedInputHtml || (!value && !currentInput ? escapeHtml(placeholder || '') : '') }}
              />
              <textarea
                ref={inputRef}
                value={currentInput}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                onScroll={(e) => setTextareaScrollTop((e.target as HTMLTextAreaElement).scrollTop)}
                onCompositionStart={() => setIsComposing(true)}
                onCompositionEnd={() => setIsComposing(false)}
                onPaste={onTextAreaPaste}
                disabled={disabled}
                placeholder={''}
                className="w-full bg-transparent border-none outline-none text-sm mt-0 resize-none"
                style={{ 
                  lineHeight: '1.5',
                  wordBreak: 'break-word',
                  overflowWrap: 'anywhere',
                  minHeight: '3em', // 至少两行的高度
                  maxHeight: '12em', // 增加最大高度，可显示约8行
                  overflow: 'auto',
                  caretColor: 'var(--accent-primary, #3b82f6)', // 确保光标颜色可见
                  fontSize: '14px', // 明确指定字体大小
                  fontFamily: 'inherit', // 继承字体
                  color: 'transparent', // 隐藏原文本，仅显示镜像层
                  position: 'relative', // 确保文本区域在最上层
                  zIndex: 1 // 确保可以接收用户输入
                }}
                spellCheck={false}
                autoComplete="off"
                rows={2}
              />
            </div>

            {/* 图片列表 */}
            {images.length > 0 && (
              <div className="w-full flex gap-2">
                {images.map((imageObj) => (
                  <div
                    key={imageObj.url}
                    className="w-16 h-16 rounded-sm relative border-blue-300 border"
                    onMouseEnter={() => setHoveredImage(imageObj.url)}
                    onMouseLeave={() => setHoveredImage('')}
                  >
                    <img
                      className="w-full h-full rounded-sm object-cover cursor-pointer"
                      src={imageObj.url}
                      onClick={() => {
                        setViewImage(imageObj.base64);
                      }}
                    />
                    {hoveredImage === imageObj.url && (
                      <button
                        className="absolute top-1 right-1 p-0.5 bg-[#1e1e1e] rounded-sm cursor-pointer"
                        onClick={() => {
                          const filtered = images.filter(item => item.url !== imageObj.url);
                          setImages(filtered);
                        }}>
                        <TrashIcon className="text-gray-400 size-4" />
                      </button>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {enableFileReferences && showFilePicker && (
          <FileSearchSuggestions
            suggestions={fileSearch.suggestions}
            activeIndex={activeSuggestionIndex}
            isLoading={fileSearch.isLoading}
            onSelect={handleSuggestionSelect}
            onHover={setActiveSuggestionIndex}
            showSearchBox={suggestionMode === 'button' || suggestionMode === 'inline'}
            searchQuery={suggestionSearchQuery}
            keepOpenWhenEmpty={true}
            onSearchQueryChange={(query) => {
              setSuggestionSearchQuery(query);
              if (searchDebounceRef.current) clearTimeout(searchDebounceRef.current);
              searchDebounceRef.current = setTimeout(() => {
                fileSearch.search(query);
              }, 300);
            }}
            onSelectDirectory={handleDirectorySelect}
            onRequestClose={() => {
              setShowFilePicker(false);
              setSuggestionMode(null);
              fileSearch.reset();
              inputRef.current?.focus();
            }}
          />
        )}
        
        {/* Wiki 标题选择器 */}
        {showWikiTitlePicker && (
          <div className="absolute z-50 mb-1 w-80 rounded-md shadow-lg border border-gray-200 overflow-hidden"
               style={{ 
                 bottom: '100%', 
                 left: '0',
                 backgroundColor: 'var(--card-bg)',
                 borderColor: 'var(--border-color)'
               }}
               onMouseDown={(e) => { e.preventDefault(); }}
          >
            <div className="px-3 py-2 text-xs font-medium text-gray-500 border-b border-gray-200">
              {messages?.components?.fileReferenceInput?.wikiTitle || '选择 Wiki 页面标题'}
            </div>
            <ul ref={wikiListRef} className="py-1 max-h-60 overflow-auto">
              {filteredWikiPages.length === 0 ? (
                <li className="px-3 py-3 text-xs text-[var(--muted)] italic select-none">
                  {messages?.components?.fileReferenceInput?.noResults || '无匹配结果'}
                  {wikiTitleSearchQuery ? `：${wikiTitleSearchQuery}` : ''}
                </li>
              ) : (
                filteredWikiPages.map((page, index) => (
                  <li
                    key={page.id}
                    className={`px-3 py-2 text-sm cursor-pointer flex items-center ${
                      index === activeWikiTitleIndex
                        ? 'bg-[#f6f8fa] dark:bg-gray-700 text-blue-900'
                        : 'text-gray-700'
                    }`}
                    style={{
                      // backgroundColor: index === activeWikiTitleIndex ? 'var(--accent-secondary)' : undefined,
                      color: index === activeWikiTitleIndex ? 'var(--accent-primary)' : 'var(--foreground)'
                    }}
                    onClick={() => handleWikiTitleSelect({id: page.id, title: page.title})}
                    onMouseEnter={() => setActiveWikiTitleIndex(index)}
                  >
                    <span className="font-medium truncate">{page.title}</span>
                  </li>
                ))
              )}
            </ul>
          </div>
        )}
        
        {/* 命令选择器 */}
        {showCommandPicker && (
          <div className="absolute z-50 mb-1 rounded-md shadow-lg border border-gray-200 overflow-hidden"
               style={{ 
                 bottom: '100%', 
                 left: '0',
                 backgroundColor: 'var(--card-bg)',
                 borderColor: 'var(--border-color)'
               }}
               onMouseDown={(e) => { e.preventDefault(); }}
          >
            <ul ref={commandListRef} className="py-1 max-h-60 overflow-auto">
              {filteredCommandOptions.length === 0 ? (
                <li className="px-3 py-3 text-xs text-[var(--muted)] italic select-none">
                  {messages?.components?.fileReferenceInput?.noResults || '无匹配结果'}
                  {commandSearchQuery ? `：${commandSearchQuery}` : ''}
                </li>
              ) : (
                filteredCommandOptions.map((option, index) => (
                  <li
                    key={option.id}
                    className={`px-3 py-2 text-sm cursor-pointer flex items-center ${
                      index === activeCommandIndex
                        ? 'bg-[#f6f8fa] dark:bg-gray-700 text-blue-900'
                        : 'text-gray-700'
                    }`}
                    style={{
                      // backgroundColor: index === activeCommandIndex ? 'var(--accent-secondary)' : undefined,
                      color: index === activeCommandIndex ? 'var(--accent-primary)' : 'var(--foreground)'
                    }}
                    onClick={() => handleCommandSelect(option)}
                    onMouseEnter={() => setActiveCommandIndex(index)}
                  >
                    <span className="font-medium">{option.label}</span>
                    <span className="ml-20 truncate">{option.description}</span>
                  </li>
                ))
              )}
            </ul>
          </div>
        )}
      </div>

      {viewImage &&
        <ImageViewer data={viewImage} onClose={() => setViewImage(null)} />}
    </div>
  );
};

export default FileReferenceInput;