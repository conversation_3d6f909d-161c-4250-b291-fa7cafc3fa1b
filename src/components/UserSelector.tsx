'use client';

import React, { useState, useEffect } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { fetchModelConfig } from '@/utils/modelConfigCache';
import { Provider, ModelConfig } from '@/types/modelConfig';

interface ModelSelectorProps {
  provider: string;
  setProvider: (value: string) => void;
  model: string;
  setModel: (value: string) => void;
  isCustomModel: boolean;
  setIsCustomModel: (value: boolean) => void;
  customModel: string;
  setCustomModel: (value: string) => void;

  // File filter configuration
  showFileFilters?: boolean;
  excludedDirs?: string;
  setExcludedDirs?: (value: string) => void;
  excludedFiles?: string;
  setExcludedFiles?: (value: string) => void;
  includedDirs?: string;
  setIncludedDirs?: (value: string) => void;
  includedFiles?: string;
  setIncludedFiles?: (value: string) => void;
}

export default function UserSelector({
  provider,
  setProvider,
  model,
  setModel,
  isCustomModel,
  setIsCustomModel,
  customModel,
  setCustomModel,

  // File filter configuration
  showFileFilters = false,
  excludedDirs = '',
  setExcludedDirs,
  excludedFiles = '',
  setExcludedFiles,
  includedDirs = '',
  setIncludedDirs,
  includedFiles = '',
  setIncludedFiles
}: ModelSelectorProps) {
  // State to manage the visibility of the filters modal and filter section
  const [isFilterSectionOpen, setIsFilterSectionOpen] = useState(false);
  // State to manage filter mode: 'exclude' or 'include'
  const [filterMode, setFilterMode] = useState<'exclude' | 'include'>('exclude');
  const { messages: t } = useLanguage();

  // State for model configurations from backend
  const [modelConfig, setModelConfig] = useState<ModelConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for viewing default values
  const [showDefaultDirs, setShowDefaultDirs] = useState(false);
  const [showDefaultFiles, setShowDefaultFiles] = useState(false);

  // Fetch model configurations from the backend
  useEffect(() => {
    const loadModelConfig = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const data = await fetchModelConfig();
        setModelConfig(data);

        // Initialize provider and model with defaults from API if not already set
        if (!provider && data.defaultProvider) {
          setProvider(data.defaultProvider);

          // Find the default provider and set its default model
          const selectedProvider = data.providers.find((p: Provider) => p.id === data.defaultProvider);
          if (selectedProvider && selectedProvider.models.length > 0) {
            setModel(selectedProvider.models[0].id);
          }
        }
      } catch (err) {
        console.error('Failed to fetch model configurations:', err);
        setError('Failed to load model configurations. Using default options.');
      } finally {
        setIsLoading(false);
      }
    };

    loadModelConfig();
  }, [provider, setModel, setProvider]);

  // Handler for changing provider
  const handleProviderChange = (newProvider: string) => {
    setProvider(newProvider);
    setTimeout(() => {
      // Reset custom model state when changing providers
      setIsCustomModel(false);

      // Set default model for the selected provider
      if (modelConfig) {
        const selectedProvider = modelConfig.providers.find((p: Provider) => p.id === newProvider);
        if (selectedProvider && selectedProvider.models.length > 0) {
          setModel(selectedProvider.models[0].id);
        }
      }
    }, 10);
  };

  // Default excluded directories from config.py
  const defaultExcludedDirs =
`./.venv/
./venv/
./env/
./virtualenv/
./node_modules/
./bower_components/
./jspm_packages/
./.git/
./.svn/
./.hg/
./.bzr/
./__pycache__/
./.pytest_cache/
./.mypy_cache/
./.ruff_cache/
./.coverage/
./dist/
./build/
./out/
./target/
./bin/
./obj/
./docs/
./_docs/
./site-docs/
./_site/
./.idea/
./.vscode/
./.vs/
./.eclipse/
./.settings/
./logs/
./log/
./tmp/
./temp/
./.eng`;

  // Default excluded files from config.py
  const defaultExcludedFiles =
`yarn.lock
pnpm-lock.yaml
npm-shrinkwrap.json
poetry.lock
Pipfile.lock
requirements.txt.lock
Cargo.lock
composer.lock
.lock
.DS_Store
Thumbs.db
desktop.ini
*.lnk
.env
.env.*
*.env
*.cfg
*.ini
.flaskenv
.gitignore
.gitattributes
.gitmodules
.github
.gitlab-ci.yml
.prettierrc
.eslintrc
.eslintignore
.stylelintrc
.editorconfig
.jshintrc
.pylintrc
.flake8
mypy.ini
pyproject.toml
tsconfig.json
webpack.config.js
babel.config.js
rollup.config.js
jest.config.js
karma.conf.js
vite.config.js
next.config.js
*.min.js
*.min.css
*.bundle.js
*.bundle.css
*.map
*.gz
*.zip
*.tar
*.tgz
*.rar
*.pyc
*.pyo
*.pyd
*.so
*.dll
*.class
*.exe
*.o
*.a
*.jpg
*.jpeg
*.png
*.gif
*.ico
*.svg
*.webp
*.mp3
*.mp4
*.wav
*.avi
*.mov
*.webm
*.csv
*.tsv
*.xls
*.xlsx
*.db
*.sqlite
*.sqlite3
*.pdf
*.docx
*.pptx`;

  // Display loading state
  if (isLoading) {
    return (
      <div className="flex flex-col gap-2">
        <div className="text-sm text-[var(--muted)]">Loading model configurations...</div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-3">
      <div className="space-y-4">
        {error && (
          <div className="text-sm text-red-500 mb-2">{error}</div>
        )}

        {/* Provider Selection */}
        <div>
          <label htmlFor="provider-dropdown" className="block text-xs font-medium text-[var(--foreground)] mb-1.5">
            {t.form?.modelProvider || 'Model Provider'}
          </label>
          <select
            id="provider-dropdown"
            value={provider}
            onChange={(e) => handleProviderChange(e.target.value)}
            className="input-japanese block w-full px-2.5 py-1.5 text-sm rounded-md bg-transparent text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)]"
          >
            <option value="" disabled>{t.form?.selectProvider || 'Select Provider'}</option>
            {modelConfig?.providers.filter((providerOption) => providerOption.id.toLowerCase() === 'whalecloud').map((providerOption) => (
              <option key={providerOption.id} value={providerOption.id}>
                {t.form?.[`provider${providerOption.id.charAt(0).toUpperCase() + providerOption.id.slice(1)}`] || providerOption.name}
              </option>
            ))}
          </select>
        </div>

        {/* Model Selection - consistent height regardless of type */}
        <div>
          <label htmlFor="model-dropdown" className="block text-xs font-medium text-[var(--foreground)] mb-1.5">
            {t.form?.model || 'Model'}
          </label>
          <div className="space-y-2">
            {/* Predefined Model Option */}
            <div className="flex items-center space-x-2">
              <input
                type="radio"
                id="predefined-model"
                name="model-type"
                checked={!isCustomModel}
                onChange={() => setIsCustomModel(false)}
                className="text-[var(--accent-primary)]"
              />
              <label htmlFor="predefined-model" className="text-sm text-[var(--foreground)]">
                {t.form?.predefinedModel || 'Predefined Model'}
              </label>
            </div>
            
            {!isCustomModel && (
              <select
                id="model-dropdown"
                value={model}
                onChange={(e) => setModel(e.target.value)}
                className="input-japanese block w-full px-2.5 py-1.5 text-sm rounded-md bg-transparent text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)]"
                disabled={!modelConfig?.providers.find(p => p.id === provider)?.models.length}
              >
                <option value="" disabled>{t.form?.selectModel || 'Select Model'}</option>
                {modelConfig?.providers.find(p => p.id === provider)?.models.map((modelOption) => (
                  <option key={modelOption.id} value={modelOption.id}>
                    {modelOption.name}
                  </option>
                ))}
              </select>
            )}

            {/* Custom Model Option */}
            {modelConfig?.providers.find(p => p.id === provider)?.supportsCustomModel && (
              <>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="custom-model"
                    name="model-type"
                    checked={isCustomModel}
                    onChange={() => setIsCustomModel(true)}
                    className="text-[var(--accent-primary)]"
                  />
                  <label htmlFor="custom-model" className="text-sm text-[var(--foreground)]">
                    {t.form?.customModel || 'Custom Model'}
                  </label>
                </div>
                
                {isCustomModel && (
                  <input
                    type="text"
                    value={customModel}
                    onChange={(e) => setCustomModel(e.target.value)}
                    placeholder={t.form?.enterCustomModel || 'Enter custom model name'}
                    className="input-japanese block w-full px-2.5 py-1.5 text-sm rounded-md bg-transparent text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)]"
                  />
                )}
              </>
            )}
          </div>
        </div>

        {/* File Filters Section */}
        {showFileFilters && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-[var(--foreground)]">
                {t.form?.fileFilters || 'File Filters'}
              </h3>
              <button
                type="button"
                onClick={() => setIsFilterSectionOpen(!isFilterSectionOpen)}
                className="text-xs text-[var(--accent-primary)] hover:text-[var(--accent-primary)]/80 transition-colors"
              >
                {isFilterSectionOpen ? t.common?.hide || 'Hide' : t.common?.show || 'Show'}
              </button>
            </div>

            {isFilterSectionOpen && (
              <div className="space-y-4 p-4 bg-[var(--background)]/50 rounded-md border border-[var(--border-color)]">
                {/* Filter Mode Toggle */}
                <div className="flex items-center space-x-4">
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      value="exclude"
                      checked={filterMode === 'exclude'}
                      onChange={(e) => setFilterMode(e.target.value as 'exclude' | 'include')}
                      className="text-[var(--accent-primary)]"
                    />
                    <span className="text-sm text-[var(--foreground)]">
                      {t.form?.excludeFiles || 'Exclude Files'}
                    </span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      value="include"
                      checked={filterMode === 'include'}
                      onChange={(e) => setFilterMode(e.target.value as 'exclude' | 'include')}
                      className="text-[var(--accent-primary)]"
                    />
                    <span className="text-sm text-[var(--foreground)]">
                      {t.form?.includeFiles || 'Include Files'}
                    </span>
                  </label>
                </div>

                {/* Directory Filters */}
                <div>
                  <label className="block text-xs font-medium text-[var(--foreground)] mb-1.5">
                    {filterMode === 'exclude' 
                      ? (t.form?.excludeDirectories || 'Exclude Directories')
                      : (t.form?.includeDirectories || 'Include Directories')
                    }
                  </label>
                  <div className="space-y-2">
                    <textarea
                      value={filterMode === 'exclude' ? excludedDirs : includedDirs}
                      onChange={(e) => {
                        if (filterMode === 'exclude') {
                          setExcludedDirs?.(e.target.value);
                        } else {
                          setIncludedDirs?.(e.target.value);
                        }
                      }}
                      placeholder={filterMode === 'exclude' 
                        ? (t.form?.excludeDirectoriesPlaceholder || 'Enter directory patterns to exclude (one per line)')
                        : (t.form?.includeDirectoriesPlaceholder || 'Enter directory patterns to include (one per line)')
                      }
                      className="input-japanese block w-full px-2.5 py-1.5 text-sm rounded-md bg-transparent text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)] resize-none"
                      rows={4}
                    />
                    <button
                      type="button"
                      onClick={() => {
                        if (filterMode === 'exclude') {
                          setShowDefaultDirs(!showDefaultDirs);
                        }
                      }}
                      className="text-xs text-[var(--accent-primary)] hover:text-[var(--accent-primary)]/80 transition-colors"
                    >
                      {showDefaultDirs ? t.common?.hide || 'Hide' : t.common?.show || 'Show'} {t.form?.defaultExcludedDirs || 'Default Excluded Directories'}
                    </button>
                    {showDefaultDirs && filterMode === 'exclude' && (
                      <div className="p-2 bg-[var(--background)]/30 rounded text-xs text-[var(--muted)] font-mono whitespace-pre-wrap max-h-32 overflow-y-auto">
                        {defaultExcludedDirs}
                      </div>
                    )}
                  </div>
                </div>

                {/* File Filters */}
                <div>
                  <label className="block text-xs font-medium text-[var(--foreground)] mb-1.5">
                    {filterMode === 'exclude' 
                      ? (t.form?.excludeFiles || 'Exclude Files')
                      : (t.form?.includeFiles || 'Include Files')
                    }
                  </label>
                  <div className="space-y-2">
                    <textarea
                      value={filterMode === 'exclude' ? excludedFiles : includedFiles}
                      onChange={(e) => {
                        if (filterMode === 'exclude') {
                          setExcludedFiles?.(e.target.value);
                        } else {
                          setIncludedFiles?.(e.target.value);
                        }
                      }}
                      placeholder={filterMode === 'exclude' 
                        ? (t.form?.excludeFilesPlaceholder || 'Enter file patterns to exclude (one per line)')
                        : (t.form?.includeFilesPlaceholder || 'Enter file patterns to include (one per line)')
                      }
                      className="input-japanese block w-full px-2.5 py-1.5 text-sm rounded-md bg-transparent text-[var(--foreground)] focus:outline-none focus:border-[var(--accent-primary)] resize-none"
                      rows={4}
                    />
                    <button
                      type="button"
                      onClick={() => {
                        if (filterMode === 'exclude') {
                          setShowDefaultFiles(!showDefaultFiles);
                        }
                      }}
                      className="text-xs text-[var(--accent-primary)] hover:text-[var(--accent-primary)]/80 transition-colors"
                    >
                      {showDefaultFiles ? t.common?.hide || 'Hide' : t.common?.show || 'Show'} {t.form?.defaultExcludedFiles || 'Default Excluded Files'}
                    </button>
                    {showDefaultFiles && filterMode === 'exclude' && (
                      <div className="p-2 bg-[var(--background)]/30 rounded text-xs text-[var(--muted)] font-mono whitespace-pre-wrap max-h-32 overflow-y-auto">
                        {defaultExcludedFiles}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
