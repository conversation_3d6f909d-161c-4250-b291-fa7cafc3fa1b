'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSettings } from '@/contexts/SettingsContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { FaQuestionCircle, FaRegCopy, FaSave, FaTimes } from 'react-icons/fa';
import { FaEye, FaEyeSlash } from 'react-icons/fa';
import { FaRegEdit } from 'react-icons/fa';
import { FaSyncAlt, FaTrash, FaCodeBranch, FaLink, FaServer, FaClock } from 'react-icons/fa';
import { FaPlus, FaSearch, FaTags } from 'react-icons/fa';
import {useToast} from "@/contexts/ToastContext"; // 使用 react-icons 库
import { BiSolidLock, BiSolidLockOpen } from 'react-icons/bi';
import { authFetch } from '@/utils/authFetch';
import { useAuth } from '@/contexts/AuthContext';
import TagEditModal from './TagEditModal';
import DeleteConfirmModal from './DeleteConfirmModal';
import Alert from './Alert';
import TokenCreateModal from './TokenCreateModal';
import { Tag, TagCreateRequest, TagUpdateRequest, TAG_TYPE } from '@/types/tag';
import type { ModelConfig as FetchedModelConfig } from '@/types/modelConfig';
import Popconfirm from './Popconfirm';
import { fetchModelConfig } from '@/utils/modelConfigCache';

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface SandboxItem {
  name?: string;
  git_url?: string;
  branch?: string;
  annotations?: Record<string, string>;
  creation_time?: string;
  status?: string;
  detailed_status?: string;
  detailed_status_description?: string;
  detailed_message?: string;
  pod_ip?: string;
}

export default function SettingsModal({ isOpen, onClose }: SettingsModalProps) {
  const { settings, setApiKey, setWhaleDevCloudToken, updateGlobalApiKeyAndToken, updateGlobalModelConfig, setShowAnnouncement } = useSettings();
  const { messages: t } = useLanguage();
  const [activeTab, setActiveTab] = useState('userInfo');
  const [localSettings, setLocalSettings] = useState(settings);
  const [modelConfig, setModelConfig] = useState<FetchedModelConfig | null>(null);
  const [isLoadingModels, setIsLoadingModels] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [showWhaleToken, setShowWhaleToken] = useState(false); // 新增状态控制whaleDevCloudToken显示
  const { addToast } = useToast();
  const { userInfo, userRoles, hasSuperAdminPermission } = useAuth();
  
  // 本地校验状态（不落盘、不更新context）
  const [localApiKeyValid, setLocalApiKeyValid] = useState<boolean | null>(null);
  const [localWhaleTokenValid, setLocalWhaleTokenValid] = useState<boolean | null>(null);
  const [isValidatingApiKey, setIsValidatingApiKey] = useState(false);
  const [isValidatingWhaleToken, setIsValidatingWhaleToken] = useState(false);
  
  // 配置变更检测状态
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showCloseAlert, setShowCloseAlert] = useState(false);

  // Sandbox tab state
  const [sandboxLoading, setSandboxLoading] = useState(false);
  const [sandboxes, setSandboxes] = useState<SandboxItem[]>([]);
  const [quota, setQuota] = useState<{ per_user_max: number; used: number; remaining: number | null } | null>(null);
  const [deletingKey, setDeletingKey] = useState<string | null>(null);

  // Quota percent for modern progress bar visualization
  const quotaPercent = React.useMemo(() => {
    const max = quota?.per_user_max ?? 0;
    const used = quota?.used ?? 0;
    if (!max || max <= 0) return 0;
    const pct = Math.round((used / max) * 100);
    return Math.max(0, Math.min(100, pct));
  }, [quota]);

  // Tag management state
  const [tags, setTags] = useState<Tag[]>([]);
  const [tagLoading, setTagLoading] = useState(false);
  const [tagSearchTerm, setTagSearchTerm] = useState('');
  const [tagStatusFilter, setTagStatusFilter] = useState<string>('');
  const [tagTypeFilter, setTagTypeFilter] = useState<string>('');
  const [showTagEditModal, setShowTagEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [editingTag, setEditingTag] = useState<Tag | null>(null);
  const [deletingTag, setDeletingTag] = useState<Tag | null>(null);
  const [isDeletingTag, setIsDeletingTag] = useState(false);
  const [isLoadingSettings, setIsLoadingSettings] = useState(false);
  const [fetchedSettings, setFetchedSettings] = useState({ apiKey: '', whaleDevCloudToken: '' });

  // Token management state
  const [tokens, setTokens] = useState<Array<{
    id: string;
    name: string;
    created_date: string;
    effective_at?: string;
    expires_at?: string;
    use_type?: string;
    last_used_time?: string;
  }>>([]);
  const [tokenLoading, setTokenLoading] = useState(false);
  const [showTokenCreateModal, setShowTokenCreateModal] = useState(false);
  const [deletingTokenId, setDeletingTokenId] = useState<string | null>(null);
  const [newlyCreatedToken, setNewlyCreatedToken] = useState<{
    name: string;
    token: string;
  } | null>(null);

  // 处理标签页切换
  const handleTabChange = (newTab: string) => {
    // 清除校验状态和提示，但保留用户修改的值
    setLocalApiKeyValid(null);
    setLocalWhaleTokenValid(null);
    setIsValidatingApiKey(false);
    setIsValidatingWhaleToken(false);
    
    // 清除新Token提示
    setNewlyCreatedToken(null);
    
    // 切换标签页
    setActiveTab(newTab);
  };

  // 每次打开弹框时从数据库获取最新配置
  useEffect(() => {
    const fetchLatestSettings = async () => {
      if (!isOpen) return;
      
      setIsLoadingSettings(true);
      try {
        const response = await authFetch('/api/config/settings');
        if (response && response.ok) {
          const data = await response.json();
          // 解码API Key和Token
          const apiKey = data.ai_api_key ? atob(data.ai_api_key) : '';
          const whaleDevCloudToken = data.dev_cloud_token ? atob(data.dev_cloud_token) : '';
          
          // 更新获取到的配置状态
          setFetchedSettings({ apiKey, whaleDevCloudToken });
          
          // 同步更新全局状态
          updateGlobalApiKeyAndToken(apiKey, whaleDevCloudToken);
          
          // 更新本地设置
          setLocalSettings(prev => ({
            ...prev,
            apiKey: apiKey,
            defaultWikiModel: settings.defaultWikiModel,
            defaultChatModel: settings.defaultChatModel,
            whaleDevCloudToken: whaleDevCloudToken,
          }));
        } else {
          // 如果获取失败，使用全局设置作为fallback
          setFetchedSettings({ 
            apiKey: settings.apiKey, 
            whaleDevCloudToken: settings.whaleDevCloudToken 
          });
          setLocalSettings(prev => ({
            ...prev,
            apiKey: settings.apiKey,
            defaultWikiModel: settings.defaultWikiModel,
            defaultChatModel: settings.defaultChatModel,
            whaleDevCloudToken: settings.whaleDevCloudToken,
          }));
        }
      } catch (error) {
        console.error('获取用户配置失败:', error);
        // 出错时使用全局设置作为fallback
        setFetchedSettings({ 
          apiKey: settings.apiKey, 
          whaleDevCloudToken: settings.whaleDevCloudToken 
        });
        setLocalSettings(prev => ({
          ...prev,
          apiKey: settings.apiKey,
          defaultWikiModel: settings.defaultWikiModel,
          defaultChatModel: settings.defaultChatModel,
          whaleDevCloudToken: settings.whaleDevCloudToken,
        }));
      } finally {
        setIsLoadingSettings(false);
        setShowPassword(false);
        setShowWhaleToken(false); // 重置whaleDevCloudToken显示状态
        setHasUnsavedChanges(false); // 重置未保存变更状态
        
        // 重置校验状态
        setLocalApiKeyValid(null);
        setLocalWhaleTokenValid(null);
        setIsValidatingApiKey(false);
        setIsValidatingWhaleToken(false);
      }
    };
    
    fetchLatestSettings();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, settings.defaultWikiModel, settings.defaultChatModel]);

  // 检测配置变更
  useEffect(() => {
    const hasApiKeyChanges = localSettings.apiKey !== fetchedSettings.apiKey;
    const hasModelChanges = 
      localSettings.defaultWikiModel !== settings.defaultWikiModel ||
      localSettings.defaultChatModel !== settings.defaultChatModel;
    const hasWhaleTokenChanges = localSettings.whaleDevCloudToken !== fetchedSettings.whaleDevCloudToken;
    
    // 根据当前tab显示对应的变更状态
    let hasChanges = false;
    if (activeTab === 'model') {
      hasChanges = hasApiKeyChanges || hasModelChanges;
    } else if (activeTab === 'whaleDevCloud') {
      hasChanges = hasWhaleTokenChanges;
    } else {
      hasChanges = hasApiKeyChanges || hasModelChanges || hasWhaleTokenChanges;
    }
    
    setHasUnsavedChanges(hasChanges);
  }, [localSettings, fetchedSettings, settings, isValidatingApiKey, activeTab]);

  // 初始化API Key校验状态
  useEffect(() => {
    if (!localSettings.apiKey) {
      setLocalApiKeyValid(null);
      setIsValidatingApiKey(false);
    }
  }, [localSettings.apiKey]);

  // 初始化WhaleDevCloud Token校验状态
  useEffect(() => {
    if (!localSettings.whaleDevCloudToken) {
      setLocalWhaleTokenValid(null);
      setIsValidatingWhaleToken(false);
    }
  }, [localSettings.whaleDevCloudToken]);

  useEffect(() => {
    const loadModelConfig = async () => {
      if (isOpen) {
        setIsLoadingModels(true);
        try {
          const data = await fetchModelConfig();
          setModelConfig(data);
        } catch (error) {
          console.error('Error fetching model configuration:', error);
        } finally {
          setIsLoadingModels(false);
        }
      }
    };
    loadModelConfig();
  }, [isOpen]);

  // Fetch sandbox quota and list when opening the sandbox tab
  useEffect(() => {
    const fetchSandboxData = async () => {
      if (!isOpen || activeTab !== 'sandbox') return;
      setSandboxLoading(true);
      try {
        const [quotaResp, listResp] = await Promise.all([
          authFetch('/api/k8s/sandbox/me/quota'),
          authFetch('/api/k8s/sandbox/me/list'),
        ]);
        const quotaData = quotaResp && quotaResp.ok ? await quotaResp.json() : { success: false };
        const listData = listResp && listResp.ok ? await listResp.json() : { success: false };

        if (quotaData.success) {
          setQuota(quotaData.data);
        } else {
          setQuota(null);
        }

        if (listData.success) {
          setSandboxes(Array.isArray(listData.data) ? listData.data : []);
        } else {
          setSandboxes([]);
        }
      } catch (err) {
        console.error('获取个人沙箱信息失败:', err);
        setQuota(null);
        setSandboxes([]);
      } finally {
        setSandboxLoading(false);
      }
    };
    fetchSandboxData();
  }, [isOpen, activeTab]);

  // Fetch tags when opening the tag management tab
  useEffect(() => {
    const fetchTags = async () => {
      if (!isOpen || activeTab !== 'tagManagement') return;
      setTagLoading(true);
      try {
        const response = await authFetch('/api/tags/tag');
        if (response && response.ok) {
          const data = await response.json();
          setTags(data.data || []);
        } else {
          setTags([]);
        }
      } catch (err) {
        console.error('获取标签列表失败:', err);
        setTags([]);
      } finally {
        setTagLoading(false);
      }
    };
    fetchTags();
  }, [isOpen, activeTab]);

  // Fetch tokens when opening the token management tab
  useEffect(() => {
    const fetchTokens = async () => {
      if (!isOpen || activeTab !== 'tokenManagement') return;
      setTokenLoading(true);
      try {
        const response = await authFetch('/api/user_token');
        if (response && response.ok) {
          const data = await response.json();
          setTokens(data || []);
        } else {
          setTokens([]);
        }
      } catch (err) {
        console.error('获取Token列表失败:', err);
        setTokens([]);
      } finally {
        setTokenLoading(false);
      }
    };
    fetchTokens();
  }, [isOpen, activeTab]);

  const refreshSandboxData = async () => {
    try {
      setSandboxLoading(true);
      const [quotaResp, listResp] = await Promise.all([
        authFetch('/api/k8s/sandbox/me/quota'),
        authFetch('/api/k8s/sandbox/me/list'),
      ]);
      const quotaData = quotaResp && quotaResp.ok ? await quotaResp.json() : { success: false };
      const listData = listResp && listResp.ok ? await listResp.json() : { success: false };
      if (quotaData.success) setQuota(quotaData.data);
      if (listData.success) setSandboxes(Array.isArray(listData.data) ? listData.data : []);
    } catch {
      // ignore
    } finally {
      setSandboxLoading(false);
    }
  };

  const getStatusBadgeClass = (status: string) => {
    const s = (status || '').toUpperCase();
    if (s.includes('READY') || s.includes('RUNNING')) return 'text-green-400 bg-green-400/10 border-green-400/30';
    if (s.includes('CREATE') || s.includes('INITIAL')) return 'text-amber-400 bg-amber-400/10 border-amber-400/30';
    if (s.includes('FAIL') || s.includes('ERROR')) return 'text-rose-400 bg-rose-400/10 border-rose-400/30';
    return 'text-[var(--muted)] bg-transparent border-[var(--border-color)]';
  };

  const handleDeleteSandboxItem = async (item: SandboxItem) => {
    const gitUrl = item?.git_url ?? item?.annotations?.['git.url'] ?? '';
    const branch = item?.branch ?? item?.annotations?.['git.branch'] ?? 'main';
    if (!gitUrl) {
      addToast?.({ type: 'info', title: '', message: t.settings.cannotDelete });
      return;
    }
    const key = `${gitUrl}#${branch}`;
    setDeletingKey(key);
    try {
      const resp = await authFetch(`/api/k8s/sandbox/me/delete`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          wiki_id: item.annotations?.['wiki.id']
        }),
      });
      const data = resp && resp.ok ? await resp.json() : { success: false };
      if (data.success) {
        addToast?.({ type: 'info', title: '', message: t.settings.deleteSuccess });
        await refreshSandboxData();
      } else {
        addToast?.({ type: 'info', title: '', message: data.error || t.settings.deleteFailed });
      }
    } catch {
      addToast?.({ type: 'info', title: '', message: t.settings.deleteFailedNetwork });
    } finally {
      setDeletingKey(null);
    }
  };



  // 单独保存API Key
  const handleSaveApiKey = useCallback(async () => {
    setIsSaving(true);
    try {
      const cleanedApiKey = localSettings.apiKey?.replace(/●/g, '') || '';
      await setApiKey(cleanedApiKey, localApiKeyValid === true);
      
      // 同时保存模型配置到前端全局状态
      updateGlobalModelConfig(localSettings.defaultWikiModel, localSettings.defaultChatModel);
      
      // 更新fetchedSettings以同步状态
      setFetchedSettings(prev => ({
        ...prev,
        apiKey: cleanedApiKey
      }));
      
      setHasUnsavedChanges(false);
      addToast?.({
        type: 'success',
        title: '',
        message: t.settings.saveSuccess
      })
    } catch (error) {
      console.error('保存API Key失败:', error);
      addToast?.({
        type: 'error',
        title: '',
        message: t.settings.apiSaveFailed || '保存失败'
      })
    } finally {
      setIsSaving(false);
    }
  }, [localSettings.apiKey, localSettings.defaultWikiModel, localSettings.defaultChatModel, localApiKeyValid, setApiKey, updateGlobalModelConfig, addToast, t.settings.saveSuccess, t.settings.apiSaveFailed]);

  // 单独保存WhaleDevCloud Token
  const handleSaveWhaleToken = useCallback(async () => {
    setIsSaving(true);
    try {
      const cleanedToken = localSettings.whaleDevCloudToken?.replace(/●/g, '') || '';
      await setWhaleDevCloudToken(cleanedToken, localWhaleTokenValid === true);
      
      // 更新fetchedSettings以同步状态
      setFetchedSettings(prev => ({
        ...prev,
        whaleDevCloudToken: cleanedToken
      }));
      
      setHasUnsavedChanges(false);
      addToast?.({
        type: 'success',
        title: '',
        message: t.settings.saveSuccess
      })
    } catch (error) {
      console.error('保存WhaleDevCloud Token失败:', error);
      addToast?.({
        type: 'error',
        title: '',
        message: t.settings.whaleCloudSaveFailed || '保存失败'
      })
    } finally {
      setIsSaving(false);
    }
  }, [localSettings.whaleDevCloudToken, localWhaleTokenValid, setWhaleDevCloudToken, addToast, t.settings.saveSuccess, t.settings.whaleCloudSaveFailed]);

  // 处理关闭设置界面
  const handleClose = () => {
    // 清除新Token提示
    setNewlyCreatedToken(null);
    
    if (hasUnsavedChanges) {
      setShowCloseAlert(true);
    } else {
      onClose();
    }
  };

  const handleTestWhaleDevCloudToken = async () => {
    // 过滤掉token中的●符号
    const cleanedToken = localSettings.whaleDevCloudToken?.replace(/●/g, '') || '';
    const response = await authFetch('/api/whaleDevCloud/verifyToken', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: cleanedToken,
      }),
    });

    if (response && !response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response?.json();

    if (data.success) {
      // 本地更新校验状态
      setLocalWhaleTokenValid(true);
    } else {
      // 本地更新校验状态
      setLocalWhaleTokenValid(false);
    }
  };

  const handleTestWhaleDevCloudTokenWithErrorHandling = async () => {
    try {
      setIsValidatingWhaleToken(true);
      await handleTestWhaleDevCloudToken();
    } catch {
      // 本地置为失败
      setLocalWhaleTokenValid(false);
    } finally {
      setIsValidatingWhaleToken(false);
    }
  };

  // API Key测试函数
  const handleTestApiKey = async () => {
    const cleanedApiKey = localSettings.apiKey?.replace(/●/g, '') || '';
    const response = await authFetch('/api/models/testConnection', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: localSettings.defaultWikiModel,
        authorization: cleanedApiKey,
      }),
    });

    if (response && !response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response?.json();

    if (data.success) {
      // 本地更新校验状态
      setLocalApiKeyValid(true);
    } else {
      // 本地更新校验状态
      setLocalApiKeyValid(false);
    }
  };

  const handleTestApiKeyWithErrorHandling = async () => {
    try {
      setIsValidatingApiKey(true);
      await handleTestApiKey();
    } catch {
      // 本地置为失败
      setLocalApiKeyValid(false);
    } finally {
      setIsValidatingApiKey(false);
    }
  };

  // Tag management functions
  const handleCreateTag = async (tagData: TagCreateRequest) => {
    try {
      const response = await authFetch('/api/tags/tag', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(tagData),
      });

      if (response && response.ok) {
        const data = await response.json();
        if (data.code === '200') {
          addToast?.({
            type: 'info',
            title: '',
            message: t.tagManagement.tagCreated
          });
          // Refresh tags
          if (activeTab === 'tagManagement') {
            const refreshResponse = await authFetch('/api/tags/tag');
            if (refreshResponse && refreshResponse.ok) {
              const refreshData = await refreshResponse.json();
              setTags(refreshData.data || []);
            }
          }
          
        } else {
          throw new Error(data.message || '创建标签失败');
        }
      } else {
        throw new Error('创建标签失败');
      }
    } catch (error) {
      console.error('创建标签失败:', error);
      addToast?.({
        type: 'info',
        title: '',
        message: t.tagManagement.errorCreatingTag
      });
      throw error;
    }
  };

  const handleUpdateTag = async (tagData: TagUpdateRequest) => {
    if (!editingTag) return;
    
    try {
      const response = await authFetch(`/api/tags/tag/${editingTag.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(tagData),
      });

      if (response && response.ok) {
        const data = await response.json();
        if (data.code === '200') {
          addToast?.({
            type: 'info',
            title: '',
            message: t.tagManagement.tagUpdated
          });
          // Refresh tags
          if (activeTab === 'tagManagement') {
            const refreshResponse = await authFetch('/api/tags/tag');
            if (refreshResponse && refreshResponse.ok) {
              const refreshData = await refreshResponse.json();
              setTags(refreshData.data || []);
            }
          }
        } else {
          throw new Error(data.message || '更新标签失败');
        }
      } else {
        throw new Error('更新标签失败');
      }
    } catch (error) {
      console.error('更新标签失败:', error);
      addToast?.({
        type: 'info',
        title: '',
        message: t.tagManagement.errorUpdatingTag
      });
      throw error;
    }
  };

  const handleDeleteTag = async () => {
    if (!deletingTag) return;
    
    setIsDeletingTag(true);
    try {
      const response = await authFetch(`/api/tags/tag/${deletingTag.id}`, {
        method: 'DELETE',
      });

      if (response && response.ok) {
        const data = await response.json();
        if (data.code === '200') {
          addToast?.({
            type: 'info',
            title: '',
            message: t.tagManagement.tagDeleted
          });
          // Refresh tags
          if (activeTab === 'tagManagement') {
            const refreshResponse = await authFetch('/api/tags/tag');
            if (refreshResponse && refreshResponse.ok) {
              const refreshData = await refreshResponse.json();
              setTags(refreshData.data || []);
            }
          }
        } else {
          throw new Error(data.message || '删除标签失败');
        }
      } else {
        throw new Error('删除标签失败');
      }
    } catch (error) {
      console.error('删除标签失败:', error);
      addToast?.({
        type: 'info',
        title: '',
        message: t.tagManagement.errorDeletingTag
      });
    } finally {
      setIsDeletingTag(false);
      setShowDeleteModal(false);
      setDeletingTag(null);
    }
  };

  const handleDisableTag = async (tag: Tag) => {
    try {
      const response = await authFetch(`/api/tags/tag/${tag.id}/disable`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response && response.ok) {
        const data = await response.json();
        if (data.code === '200') {
          addToast?.({
            type: 'info',
            title: '',
            message: t.settings.tagDisableSuccess
          });
          // Refresh tags
          if (activeTab === 'tagManagement') {
            const refreshResponse = await authFetch('/api/tags/tag');
            if (refreshResponse && refreshResponse.ok) {
              const refreshData = await refreshResponse.json();
              setTags(refreshData.data || []);
            }
          }
        } else {
          throw new Error(data.message || t.settings.tagDisableFailed);
        }
      } else {
        throw new Error(t.settings.tagDisableFailed);
      }
    } catch (error) {
      console.error('禁用标签失败:', error);
      addToast?.({
        type: 'info',
        title: '',
        message: t.settings.tagDisableFailed + (error instanceof Error ? error.message : '未知错误')
      });
    }
  };

  const handleEnableTag = async (tag: Tag) => {
    try {
      const response = await authFetch(`/api/tags/tag/${tag.id}/enable`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response && response.ok) {
        const data = await response.json();
        if (data.code === '200') {
          addToast?.({
            type: 'info',
            title: '',
            message: t.settings.tagEnableSuccess
          });
          // Refresh tags
          if (activeTab === 'tagManagement') {
            const refreshResponse = await authFetch('/api/tags/tag');
            if (refreshResponse && refreshResponse.ok) {
              const refreshData = await refreshResponse.json();
              setTags(refreshData.data || []);
            }
          }
        } else {
          throw new Error(data.message || t.settings.tagEnableFailed);
        }
      } else {
        throw new Error(t.settings.tagEnableFailed);
      }
    } catch (error) {
      console.error('启用标签失败:', error);
      addToast?.({
        type: 'info',
        title: '',
        message: t.settings.tagEnableFailed + (error instanceof Error ? error.message : '未知错误')
      });
    }
  };

  const handleSaveTag = async (tagData: TagCreateRequest | TagUpdateRequest) => {
    if (editingTag) {
      await handleUpdateTag(tagData as TagUpdateRequest);
    } else {
      await handleCreateTag(tagData as TagCreateRequest);
    }
  };

  // Token management functions
  const handleCreateToken = () => {
    setShowTokenCreateModal(true);
  };

  const handleTokenCreateSuccess = async (tokenData?: { name: string; token: string }) => {
    // 设置新创建的Token信息用于显示
    if (tokenData) {
      setNewlyCreatedToken(tokenData);
    }
    
    // Refresh tokens after creation
    if (activeTab === 'tokenManagement') {
      setTokenLoading(true);
      try {
        const response = await authFetch('/api/user_token');
        if (response && response.ok) {
          const data = await response.json();
          setTokens(data || []);
        }
      } catch (error) {
        console.error('刷新Token列表失败:', error);
      } finally {
        setTokenLoading(false);
      }
    }
  };

  const handleDeleteToken = async (tokenId: string) => {
    setDeletingTokenId(tokenId);
    try {
      const response = await authFetch(`/api/user_token/${tokenId}`, {
        method: 'DELETE',
      });

      if (response && response.ok) {
          addToast?.({
            type: 'success',
            title: '',
            message: t.settings.tokenDeleted || 'Token删除成功'
          });
          // Refresh tokens
          if (activeTab === 'tokenManagement') {
            const refreshResponse = await authFetch('/api/user_token');
            if (refreshResponse && refreshResponse.ok) {
              const refreshData = await refreshResponse.json();
              setTokens(refreshData || []);
            }
          }
      } else {
        throw new Error('删除Token失败');
      }
    } catch (error) {
      console.error('删除Token失败:', error);
      addToast?.({
        type: 'error',
        title: '',
        message: t.settings.tokenDeleteFailed || '删除Token失败'
      });
    } finally {
      setDeletingTokenId(null);
    }
  };

  const openTagEditModal = (tag?: Tag) => {
    setEditingTag(tag || null);
    setShowTagEditModal(true);
  };

  const openDeleteModal = (tag: Tag) => {
    setDeletingTag(tag);
    // setShowDeleteModal(true);
  };

  const filteredTags = React.useMemo(() => {
    return tags.filter(tag => {
      // 搜索词筛选
      const matchesSearch = !tagSearchTerm || 
        tag.name.toLowerCase().includes(tagSearchTerm.toLowerCase()) ||
        (tag.comments && tag.comments.toLowerCase().includes(tagSearchTerm.toLowerCase()));
      
      // 状态筛选
      const matchesStatus = !tagStatusFilter || tag.state?.toString() === tagStatusFilter;
      
      // 类型筛选
      const matchesType = !tagTypeFilter || tag.type.toString() === tagTypeFilter;
      
      return matchesSearch && matchesStatus && matchesType;
    });
  }, [tags, tagSearchTerm, tagStatusFilter, tagTypeFilter]);

  if (!isOpen) {
    return null;
  }
  
  return (
    <>
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm">
        <div className="relative w-full max-w-4xl bg-[var(--card-bg)] rounded-xl shadow-2xl border border-[var(--border-color)]">
          {/* Header */}
          <div className="flex items-center justify-between p-5 border-b border-[var(--border-color)]">
            <h3 className="text-xl font-semibold text-[var(--foreground)]">{t.settings.title}</h3>
            <button
                onClick={handleClose}
                className="p-2 rounded-full text-[var(--muted)] hover:bg-[var(--accent-secondary)] hover:text-[var(--foreground)] transition-colors"
            >
              <FaTimes />
            </button>
          </div>

          <div className="flex">
            {/* Sidebar for Tabs */}
            <nav className="w-45 p-3 border-r border-[var(--border-color)]">
              <button
                  onClick={() => handleTabChange('userInfo')}
                  className={`w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      activeTab === 'userInfo'
                          ? 'bg-[var(--accent-primary)]/10 text-[var(--accent-primary)]'
                          : 'text-[var(--muted)] hover:bg-[var(--accent-secondary)]'
                  }`}
              >
                {t.settings.userInfo}
              </button>
              <button
                  onClick={() => handleTabChange('model')}
                  className={`w-full text-left mt-2 px-3 py-2 rounded-md text-sm font-medium transition-colors relative ${
                      activeTab === 'model'
                          ? 'bg-[var(--accent-primary)]/10 text-[var(--accent-primary)]'
                          : 'text-[var(--muted)] hover:bg-[var(--accent-secondary)]'
                  }`}
              >
                {t.settings.modelTab}
                {(localSettings.apiKey !== fetchedSettings.apiKey || 
                  localSettings.defaultWikiModel !== settings.defaultWikiModel || 
                  localSettings.defaultChatModel !== settings.defaultChatModel) && (
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                )}
              </button>
              <button
                  onClick={() => handleTabChange('whaleDevCloud')}
                  className={`w-full text-left mt-2 px-3 py-2 rounded-md text-sm font-medium transition-colors relative ${
                      activeTab === 'whaleDevCloud'
                          ? 'bg-[var(--accent-primary)]/10 text-[var(--accent-primary)]'
                          : 'text-[var(--muted)] hover:bg-[var(--accent-secondary)]'
                  }`}
              >
                {t.settings.whaleDevCloudTab}
                {localSettings.whaleDevCloudToken !== fetchedSettings.whaleDevCloudToken && (
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                )}
              </button>
              <button
                  onClick={() => handleTabChange('sandbox')}
                  className={`w-full text-left mt-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      activeTab === 'sandbox'
                          ? 'bg-[var(--accent-primary)]/10 text-[var(--accent-primary)]'
                          : 'text-[var(--muted)] hover:bg-[var(--accent-secondary)]'
                  }`}
              >
                {t.settings.personalSandbox}
              </button>
              <button
                  onClick={() => handleTabChange('tagManagement')}
                  className={`w-full text-left mt-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      activeTab === 'tagManagement'
                          ? 'bg-[var(--accent-primary)]/10 text-[var(--accent-primary)]'
                          : 'text-[var(--muted)] hover:bg-[var(--accent-secondary)]'
                  }`}
              >
                {t.settings.tagManagementTab}
              </button>
              <button
                  onClick={() => handleTabChange('tokenManagement')}
                  className={`w-full text-left mt-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      activeTab === 'tokenManagement'
                          ? 'bg-[var(--accent-primary)]/10 text-[var(--accent-primary)]'
                          : 'text-[var(--muted)] hover:bg-[var(--accent-secondary)]'
                  }`}
              >
                {t.settings.tokenManagementTab || 'Token管理'}
              </button>
              <button
                  onClick={() => handleTabChange('systemSettings')}
                  className={`w-full text-left mt-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      activeTab === 'systemSettings'
                          ? 'bg-[var(--accent-primary)]/10 text-[var(--accent-primary)]'
                          : 'text-[var(--muted)] hover:bg-[var(--accent-secondary)]'
                  }`}
              >
                {t.settings.systemSettings}
              </button>
            </nav>

            {/* Main Content */}
            <div className="flex-1 p-8" style={{ height: '450px' }}>
              {activeTab === 'userInfo' && (
                <div className="space-y-4">
                  {/* 姓名和工号 - 两列布局 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-[var(--muted)] mb-2 flex items-center">
                        {t.settings.name}
                      </label>
                      <div className="text-[var(--foreground)] text-base tracking-wide truncate">
                        {userInfo?.user_name || <span className="text-[var(--muted)] italic">{t.settings.notRetrieved}</span>}
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-[var(--muted)] mb-2 flex items-center">
                        {t.settings.employeeId}
                      </label>
                      <div className="text-[var(--foreground)] text-base tracking-wide font-mono truncate">
                        {userInfo?.user_code || <span className="text-[var(--muted)] italic">{t.settings.notRetrieved}</span>}
                      </div>
                    </div>
                  </div>
                  
                  {/* 部门 - 单列布局 */}
                  <div>
                    <label className="block text-sm font-medium text-[var(--muted)] mb-2 flex items-center">
                      {t.settings.department}
                    </label>
                    <div className="text-[var(--foreground)] text-base tracking-wide truncate">
                      {(userInfo?.dept && userInfo?.org) ? `${userInfo.dept}-${userInfo.org}` : <span className="text-[var(--muted)] italic">{t.settings.notRetrieved}</span>}
                    </div>
                  </div>
                  
                  {/* 职位和角色 - 两列布局 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-[var(--muted)] mb-2 flex items-center">
                        {t.settings.position}
                      </label>
                      <div className="text-[var(--foreground)] text-base tracking-wide truncate">
                        {userInfo?.job || <span className="text-[var(--muted)] italic">{t.settings.notRetrieved}</span>}
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-[var(--muted)] mb-2">
                        {t.settings.role}
                      </label>
                      <div className="text-[var(--foreground)] text-base tracking-wide break-words">
                        {userRoles?.map(role => role.role_name).join(', ') || <span className="text-[var(--muted)] italic">{t.settings.notRetrieved}</span>}
                      </div>
                    </div>
                  </div>
                </div>
              )}
              {activeTab === 'sandbox' && (
                <div className="space-y-4 h-full overflow-y-auto pr-2">
                  <div className="rounded-xl border border-[var(--border-color)] bg-[var(--card-bg)] p-4 shadow-sm">
                    <div className="flex items-center justify-between gap-4">
                      <div className="flex items-center gap-3 min-w-0">
                        <span className="text-sm text-[var(--muted)]">{t.settings.quota}</span>
                        {quota ? (
                          <div className="flex items-center gap-3 flex-wrap">
                            <span className="inline-flex items-center text-[var(--foreground)] text-sm">
                              <span className="font-mono font-semibold">{quota.used ?? 0}</span>
                              <span className="px-1 text-[var(--muted)]">/</span>
                              <span className="font-mono">{quota.per_user_max ?? '-'}</span>
                            </span>
                            {quota.remaining !== null && quota.remaining !== undefined && (
                              <span className="text-xs text-[var(--muted)]">{t.settings.available} {quota.remaining}</span>
                            )}
                            <div className="h-2 w-28 md:w-40 bg-[var(--accent-secondary)]/40 rounded-full overflow-hidden">
                              <div className="h-full bg-[var(--accent-primary)]" style={{ width: `${quotaPercent}%` }} />
                            </div>
                          </div>
                        ) : (
                          <span className="text-[var(--muted)]">{t.settings.gettingQuota}</span>
                        )}
                      </div>
                      <button
                        onClick={refreshSandboxData}
                        title={t.settings.refresh}
                        className="inline-flex items-center justify-center h-9 w-9 rounded-full border border-[var(--border-color)] text-[var(--muted)] hover:text-[var(--foreground)] hover:bg-[var(--accent-secondary)] transition-colors"
                      >
                        <FaSyncAlt className={sandboxLoading ? 'animate-spin' : ''} />
                      </button>
                    </div>
                  </div>

                  <div>
                    <div className="text-sm text-[var(--muted)] mb-2">{t.settings.mySandbox}</div>
                    {sandboxLoading ? (
                      <div className="space-y-3">
                        {[1,2].map(i => (
                          <div key={i} className="rounded-xl border border-[var(--border-color)] bg-[var(--card-bg)]/60 p-4 animate-pulse">
                            <div className="h-4 w-32 bg-[var(--accent-secondary)]/60 rounded mb-2"></div>
                            <div className="h-3 w-64 bg-[var(--accent-secondary)]/50 rounded mb-1"></div>
                            <div className="h-3 w-40 bg-[var(--accent-secondary)]/50 rounded"></div>
                          </div>
                        ))}
                      </div>
                    ) : sandboxes.length === 0 ? (
                      <div className="rounded-lg border border-dashed border-[var(--border-color)] p-6 text-center text-[var(--muted)]">{t.settings.noSandbox}</div>
                    ) : (
                      <div className="grid grid-cols-1 gap-4">
                        {sandboxes.map((item, idx) => {
                          const name = item?.name || `#${idx+1}`;
                          const gitUrl = item?.git_url || item?.annotations?.['git.url'] || '-';
                          const branch = item?.branch || item?.annotations?.['git.branch'] || 'main';
                          const createdAt = item?.creation_time ? new Date(item.creation_time).toLocaleString() : '-';
                          const key = `${gitUrl}#${branch}`;
                          const detailedStatus = item?.detailed_status || item?.status || '-';
                          const detailedDesc = item?.detailed_status_description || '';
                          const detailedMsg = item?.detailed_message || '';
                          const gitUrlForDisplay = gitUrl;
                          const branchForDisplay = branch;
                          return (
                            <div
                              key={key}
                              className="group rounded-xl border border-[var(--border-color)] bg-[var(--card-bg)]/80 hover:border-[var(--accent-primary)]/40 hover:bg-[var(--accent-secondary)]/30 transition-all duration-300 shadow-sm hover:shadow-lg p-5"
                            >
                              <div className="flex flex-col gap-3">
                                <div className="flex items-start justify-between gap-3">
                                  <div className="text-[var(--foreground)] font-semibold truncate text-base">{name}</div>
                                  <span className={`inline-flex items-center gap-1.5 px-2.5 py-1 text-xs font-medium rounded-full border ${getStatusBadgeClass(detailedStatus)}`}>
                                    <FaServer className="opacity-80" /> {detailedStatus}
                                  </span>
                                </div>

                                <div className="space-y-2 text-xs text-[var(--muted)]">
                                  <div className="flex items-center gap-2 truncate">
                                    <FaLink className="opacity-70 flex-shrink-0" />
                                    <span className="truncate" title={gitUrlForDisplay}>{gitUrlForDisplay}</span>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <FaCodeBranch className="opacity-70 flex-shrink-0" />
                                    <span>{branchForDisplay}</span>
                                  </div>
                                </div>

                                <div className="flex items-center gap-4 text-xs">
                                  <div className="inline-flex items-center gap-1.5 text-[var(--muted)]">
                                    <FaClock /> {createdAt}
                                  </div>
                                </div>

                                {(detailedDesc || detailedMsg) && (
                                  <div className="text-xs text-[var(--muted)] leading-relaxed bg-[var(--accent-secondary)]/40 border border-[var(--border-color)] rounded-lg p-3">
                                    <div className="font-medium text-[var(--foreground)]/80 mb-1">{t.settings.detailedInfo}</div>
                                    {detailedDesc && <div className="mb-1 break-words">{detailedDesc}</div>}
                                    {detailedMsg && <div className="break-words">{detailedMsg}</div>}
                                  </div>
                                )}

                                <div className="flex justify-end pt-1">
                                  <button
                                    className="btn-secondary text-xs inline-flex items-center gap-1.5"
                                    onClick={() => handleDeleteSandboxItem(item)}
                                    disabled={deletingKey === key}
                                  >
                                    {deletingKey === key ? <FaSyncAlt className="animate-spin" /> : <FaTrash className="opacity-80" />}
                                    {deletingKey === key ? t.settings.deleting : t.settings.delete}
                                  </button>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                </div>
              )}
              {activeTab === 'model' && (
                  <div className="space-y-6">
                    <div style={{ position: 'relative', flex: 1 }}>
                      <label className="flex items-center gap-2 text-sm font-medium text-[var(--muted)] mb-2">
                        {t.settings.apiKey}
                        <FaQuestionCircle
                          className="text-[var(--accent-primary)]"
                          title={t.settings.configHint}
                        />
                      </label>
                      <div className="flex items-center gap-3">
                        <div className="flex-1 relative">
                          <input
                              type={showPassword ? 'text' : 'password'}
                              value={localSettings.apiKey}
                              onChange={e => setLocalSettings({ ...localSettings, apiKey: e.target.value })}
                              className="input-base w-full"
                              placeholder={isLoadingSettings ? t.common.loading : t.settings.apiKey}
                              disabled={isLoadingSettings}
                              style={{ paddingRight: '40px' }}
                          />
                          <button
                              type="button"
                              onClick={() => setShowPassword(!showPassword)}
                              style={{
                                position: 'absolute',
                                right: '10px',
                                top: '50%',
                                transform: 'translateY(-50%)',
                                background: 'none',
                                border: 'none',
                                cursor: 'pointer',
                              }}
                          >
                            {showPassword ? <FaEye /> : <FaEyeSlash />}
                          </button>
                        </div>
                        <button 
                          onClick={handleTestApiKeyWithErrorHandling}
                          className="btn-secondary whitespace-nowrap"
                          disabled={isValidatingApiKey}
                        >
                          {isValidatingApiKey ? (
                            <>
                              <FaSyncAlt className="mr-2 animate-spin" />
                              {t.settings.apiValidating}
                            </>
                          ) : (
                            t.settings.testConnection
                          )}
                        </button>
                      </div>
                    </div>
                    
                    {/* 显示校验状态提示 - 只在点击校验按钮后显示 */}
                    {localSettings.apiKey && (isValidatingApiKey || localApiKeyValid !== null) && (
                      <div className={`text-sm px-3 py-2 rounded-md ${
                        isValidatingApiKey 
                          ? 'bg-yellow-500/10 text-yellow-600 border border-yellow-500/20'
                          : localApiKeyValid === true
                            ? 'bg-green-500/10 text-green-600 border border-green-500/20' 
                            : 'bg-red-500/10 text-red-600 border border-red-500/20'
                      }`}>
                        {isValidatingApiKey
                          ? t.settings.apiValidating
                          : localApiKeyValid === true
                            ? t.settings.apiCorrect
                            : t.settings.apiError}
                      </div>
                    )}
                    
                    <div>
                      <label className="block text-sm font-medium text-[var(--muted)] mb-2">{t.settings.defaultWikiModel}</label>
                      <select
                          value={localSettings.defaultWikiModel}
                          onChange={e => setLocalSettings({ ...localSettings, defaultWikiModel: e.target.value })}
                          className="input-base"
                          disabled={isLoadingModels}
                      >
                        {isLoadingModels ? (
                            <option>{t.common.loading}</option>
                        ) : (
                            modelConfig?.providers.filter(provider => provider.name.toLowerCase() !== 'gemini-cli').map(provider => (
                                <optgroup key={provider.id} label={provider.name}>
                                    {provider.models.map(model => (
                                        <option key={model.id} value={model.id}>{model.name}</option>
                                    ))}
                                </optgroup>
                            ))
                        )}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-[var(--muted)] mb-2">{t.settings.defaultChatModel}</label>
                      <select
                          value={localSettings.defaultChatModel}
                          onChange={e => setLocalSettings({ ...localSettings, defaultChatModel: e.target.value })}
                          className="input-base"
                          disabled={isLoadingModels}
                      >
                        {isLoadingModels ? (
                            <option>{t.common.loading}</option>
                        ) : (
                            modelConfig?.providers.map(provider => (
                                <optgroup key={provider.id} label={provider.name}>
                                    {provider.models.map(model => (
                                        <option key={model.id} value={model.id}>{model.name}</option>
                                    ))}
                                </optgroup>
                            ))
                        )}
                      </select>
                    </div>
                    

                  </div>
              )}
              {activeTab === 'whaleDevCloud' && (
                  <div className="space-y-6">
                    <div style={{ position: 'relative', flex: 1 }}>
                      <label className="flex items-center gap-2 text-sm font-medium text-[var(--muted)] mb-2">{t.settings.whalecloudTitle}
                        <FaQuestionCircle
                          className="text-[var(--accent-primary)]"
                          title={t.settings.whalecloudConfigHint}
                        />
                      </label>
                      <div className="flex items-center gap-3">
                        <div className="flex-1 relative">
                          <input
                              type={showWhaleToken ? 'text' : 'password'}
                              value={localSettings.whaleDevCloudToken}
                              onChange={e => setLocalSettings({ ...localSettings, whaleDevCloudToken: e.target.value })}
                              className="input-base w-full"
                              placeholder={isLoadingSettings ? t.common.loading : t.settings.whaleDevCloudToken}
                              disabled={isLoadingSettings}
                          />
                          <button
                              type="button"
                              onClick={() => setShowWhaleToken(!showWhaleToken)}
                              style={{
                                position: 'absolute',
                                right: '10px',
                                top: '50%',
                                transform: 'translateY(-50%)',
                                background: 'none',
                                border: 'none',
                                cursor: 'pointer',
                              }}
                          >
                            {showWhaleToken ? <FaEye /> : <FaEyeSlash />}
                          </button>
                        </div>
                        <button 
                          onClick={handleTestWhaleDevCloudTokenWithErrorHandling}
                          className="btn-secondary whitespace-nowrap"
                          disabled={isValidatingWhaleToken}
                        >
                          {isValidatingWhaleToken ? (
                            <>
                              <FaSyncAlt className="mr-2 animate-spin" />
                              {t.settings.whalecloudValidating}
                            </>
                          ) : (
                            t.settings.verifyWhaleDevCloudToken
                          )}
                        </button>
                      </div>
                    </div>
                    
                    {/* 显示校验状态提示 - 只在点击校验按钮后显示 */}
                    {localSettings.whaleDevCloudToken && (isValidatingWhaleToken || localWhaleTokenValid !== null) && (
                      <div className={`text-sm px-3 py-2 rounded-md ${
                        isValidatingWhaleToken 
                          ? 'bg-yellow-500/10 text-yellow-600 border border-yellow-500/20'
                          : localWhaleTokenValid 
                            ? 'bg-green-500/10 text-green-600 border border-green-500/20' 
                            : 'bg-red-500/10 text-red-600 border border-red-500/20'
                      }`}>
                        {isValidatingWhaleToken
                          ? t.settings.whalecloudValidating
                          : localWhaleTokenValid
                            ? t.settings.whalecloudCorrect
                            : t.settings.whalecloudError}
                      </div>
                    )}
                    

                  </div>
              )}
              {activeTab === 'tagManagement' && (
                <div className="space-y-6">
                  {/* Header with search, filters and add button */}
                  <div className="flex items-center justify-between gap-4">
                    <div className="flex items-center gap-3 flex-1 w-full">
                      <div className="relative" style={{ width: '100%' }}>
                         <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                           <FaSearch className="h-4 w-4 text-[var(--muted)]" />
                         </div>
                         <input
                            type="text"
                            placeholder={t.tagManagement.searchPlaceholder}
                            value={tagSearchTerm}
                            onChange={(e) => setTagSearchTerm(e.target.value)}
                            className="input-base w-65 pl-10"
                            style={{ paddingLeft: '30px' }}
                          />
                      </div>
                      <div className="flex items-center gap-3" style={{ width: '420px' }}>
                        <select 
                          className="input-base" 
                          value={tagStatusFilter}
                          onChange={(e) => setTagStatusFilter(e.target.value)}
                        >
                            <option value="">{t.settings.status}</option>
                            <option value="1">{t.settings.active}</option>
                            <option value="0">{t.settings.disabled}</option>
                        </select>
                        <select 
                          className="input-base" 
                          value={tagTypeFilter}
                          onChange={(e) => setTagTypeFilter(e.target.value)}
                        >
                            <option value="">{t.settings.type}</option>
                            <option value="1">{t.settings.system}</option>
                            <option value="2">{t.settings.user}</option>
                        </select>
                      </div>
                    </div>
                    <button
                      onClick={() => openTagEditModal()}
                      className="btn-primary inline-flex items-center gap-2"
                      title={!hasSuperAdminPermission() ? t.settings.onlyAdminCanCreateSystemTag : ""}
                    >
                      <FaPlus className="h-4 w-4" />
                      {t.tagManagement.addNewTag}
                    </button>
                  </div>

                  {/* Tags table */}
                  <div className="border border-[var(--border-color)] rounded-lg overflow-hidden">
                    {tagLoading ? (
                      <div className="p-8 text-center">
                        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--accent-primary)]"></div>
                        <p className="mt-2 text-[var(--muted)]">{t.tagManagement.loadingTags}</p>
                      </div>
                    ) : filteredTags.length === 0 ? (
                      <div className="p-8 text-center text-[var(--muted)]">
                        <FaTags className="mx-auto h-12 w-12 mb-4 opacity-50" />
                        <p className="text-lg font-medium">{t.tagManagement.noTags}</p>
                        <p className="text-sm">{t.settings.clickButtonToCreateFirstTag}</p>
                      </div>
                    ) : (
                      <div className="max-h-80 overflow-y-auto border border-[var(--border-color)] rounded-lg">
                        <table className="w-full table-fixed">
                          <thead className="bg-[var(--card-bg)] border-b border-[var(--border-color)] sticky top-0 z-10 shadow-sm">
                            <tr>
                              <th className="px-4 py-2 text-left text-sm font-medium text-[var(--muted)]">
                                {t.settings.name}
                              </th>
                              <th className="px-4 py-2 text-left text-sm font-medium text-[var(--muted)]">
                                {t.settings.description}
                              </th>
                              <th className="px-4 py-2 text-left text-sm font-medium text-[var(--muted)] w-20">
                                 {t.settings.type}
                               </th>
                              <th className="px-4 py-2 text-left text-sm font-medium text-[var(--muted)] w-20">
                                 {t.settings.status}
                               </th>
                              <th className="px-10 py-2 text-left text-sm font-medium text-[var(--muted)] w-32">
                                {t.settings.actions}
                              </th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-[var(--border-color)]">
                            {filteredTags.map((tag) => (
                              <tr key={tag.id} className="hover:bg-[var(--accent-secondary)]/20 transition-colors">
                                <td className="px-4 py-1 w-32">
                                  <div className="flex items-center gap-2">
                                    <span 
                                      className="px-1 py-0.5 rounded text-[var(--foreground)] font-medium truncate text-sm"
                                      style={{ backgroundColor: tag.color, border: `1px solid ${tag.color}40`, color: '#fff' }}
                                      title={tag.name}
                                    >
                                      {tag.name}
                                    </span>
                                  </div>
                                </td>
                                <td className="px-4 py-2 w-80">
                                  <span 
                                    className="text-[var(--muted)] text-sm truncate block"
                                    title={tag.comments || '-'}
                                  >
                                    {tag.comments || '-'}
                                  </span>
                                </td>
                                <td className="px-4 py-2 w-20">
                                  <span className="text-[var(--muted)] text-sm">
                                    {tag.type === TAG_TYPE.SYSTEM ? t.settings.system : t.settings.user}
                                  </span>
                                </td>
                                <td className="px-4 py-2 w-20">
                                  <span className={`inline-flex items-center px-1 py-0.5 text-xs font-medium rounded-full ${
                                    tag.state === 1 
                                      ? 'text-green-400 bg-green-400/10 border border-green-400/30' 
                                      : 'text-red-500 bg-red-500/10 border border-red-500/30'
                                  }`}>
                                    {tag.state === 1 ? t.settings.active : t.settings.disabled}
                                  </span>
                                </td>
                                <td className="px-6 py-2 w-32">
                                  <div className="flex items-center gap-1">
                                    {(hasSuperAdminPermission() || tag.type === TAG_TYPE.USER) && (
                                    <>
                                    {tag.state === 1 && (
                                      <button
                                        onClick={() => handleDisableTag(tag)}
                                        className="text-[var(--muted)] hover:text-red-500 hover:bg-red-500/10 px-2 py-1 rounded text-sm transition-colors"
                                        title={t.settings.disable}
                                      >
                                        <BiSolidLock />
                                      </button>
                                    )}
                                    {tag.state === 0 && (
                                      <button
                                        onClick={() => handleEnableTag(tag)}
                                        className="text-[var(--muted)] hover:text-green-500 hover:bg-green-500/10 px-2 py-1 rounded text-sm transition-colors"
                                        title={t.settings.enable}
                                      >
                                        <BiSolidLockOpen />
                                      </button>
                                    )}
                                      <button
                                        onClick={() => openTagEditModal(tag)}
                                        className="text-[var(--muted)] hover:text-[var(--foreground)] hover:bg-[var(--accent-secondary)] px-2 py-1 rounded text-sm transition-colors"
                                        title={t.settings.edit}
                                      >
                                        <FaRegEdit />
                                      </button>
                                      {tag.module_type !== 2 && (
                                      <Popconfirm
                                        title={t.settings.delete}
                                        description={t.tagManagement.deleteConfirmMessage.replace('{name}', tag.name)}
                                        onConfirm={() => handleDeleteTag()}
                                        style={{ maxWidth: '300px' }}
                                      >
                                   
                                      <button
                                        onClick={() => openDeleteModal(tag)}
                                        className="text-[var(--muted)] hover:text-red-500 hover:bg-red-500/10 px-2 py-1 rounded text-sm transition-colors"
                                        title={t.tagManagement.deleteTag}
                                      >
                                        <FaTrash />
                                      </button>
                                      </Popconfirm>
                                      )}
                                    </>
                                    )}
                                  </div>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    )}
                  </div>
                </div>
              )}
              {activeTab === 'tokenManagement' && (
                <div className="space-y-6">
                  {/* 创建Token按钮 */}
                  <div className="flex justify-start">
                    <button
                      onClick={handleCreateToken}
                      className="btn-primary inline-flex items-center gap-2 px-6 py-3"
                    >
                      <FaPlus className="h-4 w-4" />
                      {t.settings.createToken || '创建Token'}
                    </button>
                  </div>

                  {/* 新Token生成成功提示 */}
                  {newlyCreatedToken && (
                    <div className="space-y-2">
                      {/* 成功提示 */}
                      <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-3">
                        <p className="text-sm font-medium text-green-600 text-center">
                          {t.settings.tokenCreatedSuccess}
                        </p>
                      </div>

                      {/* Token内容显示 */}
                      <div className="bg-amber-500/10 border border-amber-500/30 rounded-lg p-3">
                        <div className="flex items-center gap-2">
                          <div className="font-mono text-xs text-[var(--foreground)] break-all text-center flex-1">
                            {newlyCreatedToken.token}
                          </div>
                            <FaRegCopy
                              size={16}
                              className="text-[var(--muted)] hover:text-[var(--primary)] cursor-pointer bg-amber-500/10"
                              onClick={async () => {
                                try {
                                  await navigator.clipboard.writeText(newlyCreatedToken.token);
                                  addToast?.({
                                    type: 'success',
                                    title: '',
                                    message: t.authApp.copyToClipboard
                                  });
                                } catch (error) {
                                  console.error('复制失败:', error);
                                  addToast?.({
                                    type: 'error',
                                    title: '',
                                    message: t.authApp.copyFail
                                  });
                                }
                              }}
                            />
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Token管理区域 */}
                  <div className="border border-[var(--border-color)] rounded-lg bg-[var(--card-bg)] overflow-hidden">
                    {/* 标题 */}
                    <div className="flex gap-2 items-center px-3 py-3 border-b border-[var(--border-color)] bg-[var(--accent-secondary)]/10">
                      <h3 className="text-sm font-medium text-[var(--foreground)]">
                        {t.settings.tokenManagement}
                      </h3>
                      <FaQuestionCircle
                          className="text-[var(--accent-primary)] h-4 w-4"
                          title={t.settings.tokenConfigHint}
                        />
                    </div>
                    
                    {/* Token列表内容 */}
                    <div className={`overflow-y-auto ${newlyCreatedToken ? 'h-[160px]' : 'h-[280px]'}`}>
                      {tokenLoading ? (
                        <div className="space-y-0">
                          {[1, 2, 3].map(i => (
                            <div key={i} className="flex items-center gap-4 p-4 animate-pulse border-b border-[var(--border-color)]/30">
                              <div className="h-8 w-8 bg-[var(--accent-secondary)]/60 rounded-full"></div>
                              <div className="flex-1">
                                <div className="h-4 w-32 bg-[var(--accent-secondary)]/60 rounded mb-2"></div>
                                <div className="h-3 w-48 bg-[var(--accent-secondary)]/50 rounded"></div>
                              </div>
                              <div className="h-8 w-8 bg-[var(--accent-secondary)]/60 rounded"></div>
                            </div>
                          ))}
                        </div>
                      ) : tokens.length === 0 ? (
                        <div className="flex items-center justify-center h-full text-[var(--muted)]">
                          <div className="text-center">
                            <div className="h-12 w-12 rounded-full bg-[var(--accent-secondary)]/20 flex items-center justify-center mx-auto mb-3">
                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                                <polyline points="10,17 15,12 10,7"></polyline>
                                <line x1="15" y1="12" x2="3" y2="12"></line>
                              </svg>
                            </div>
                            <p className="text-sm font-medium">{t.settings.noTokens || '暂无Token'}</p>
                            <p className="text-xs mt-1">{t.settings.clickButtonToCreateFirstToken || '点击上方按钮创建第一个Token'}</p>
                          </div>
                        </div>
                      ) : (
                        <div className="divide-y divide-[var(--border-color)]/30">
                          {tokens.map((token) => (
                            <div
                              key={token.id}
                              className="group flex items-center gap-4 p-3 hover:bg-[var(--accent-secondary)]/20 transition-colors duration-200"
                            >
                              {/* Token图标 */}
                              <div className="flex-shrink-0">
                                  <div className="h-8 w-8 rounded-full bg-[var(--accent-primary)]/10 flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                      <path d="M3 12l18-9-9 18-2-8-7 7z"></path>
                                    </svg>
                                  </div>
                              </div>
                              
                              {/* Token信息 */}
                              <div className="flex-1 min-w-0">
                                <div className="mb-1">
                                  <h4 className="text-sm font-medium text-[var(--foreground)] truncate">
                                    {token.name}
                                  </h4>
                                </div>
                                <div className="text-xs text-[var(--muted)] italic space-y-1">
                                  <div>
                                    {token.last_used_time ? (
                                      <>
                                        {t.settings.addedAt.replace('{new Date(token.created_date).toLocaleString()}', new Date(token.created_date).toLocaleString()).replace('{new Date(token.last_used_time).toLocaleString()}', new Date(token.last_used_time).toLocaleString())}
                                      </>
                                    ) : (
                                      <>
                                        {t.settings.addedAtNoActivity.replace('{new Date(token.created_date).toLocaleString()}', new Date(token.created_date).toLocaleString())}
                                      </>
                                    )}
                                  </div>
                                  <div className="flex gap-4">
                                    {token.effective_at && (
                                      <span>
                                        {t.settings.effectiveTime}: {new Date(token.effective_at).toLocaleString()}
                                      </span>
                                    )}
                                    {token.expires_at && (
                                      <span>
                                        {t.settings.expireTime}: {new Date(token.expires_at).toLocaleString()}
                                      </span>
                                    )}
                                  </div>
                                </div>
                              </div>
                              
                              {/* 失效标识和删除按钮 */}
                              <div className="flex-shrink-0 flex items-center gap-2">
                                {/* 失效标识 */}
                                {token.expires_at && new Date(token.expires_at) < new Date() && (
                                  <span className="px-2 py-1 text-xs font-medium text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-400 rounded-md">
                                    已失效
                                  </span>
                                )}
                                
                                {/* 删除按钮 */}
                                <Popconfirm
                                  title={t.settings.delete}
                                  description={t.settings.deleteConfirmMessage.replace('{name}', token.name)}
                                  onConfirm={() => handleDeleteToken(token.id)}
                                  style={{ maxWidth: '300px' }}
                                >
                                <button
                                  disabled={deletingTokenId === token.id}
                                  className="text-[var(--muted)] hover:text-red-500 hover:bg-red-500/10 p-1.5 rounded-md transition-colors disabled:opacity-50"
                                >
                                  {deletingTokenId === token.id ? (
                                    <FaSyncAlt className="h-3.5 w-3.5 animate-spin" />
                                  ) : (
                                    <FaTrash className="h-3.5 w-3.5" />
                                  )}
                                </button>
                                </Popconfirm>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
              {activeTab === 'systemSettings' && (
                <div className="space-y-6">
                  {/* 公告显示设置 */}
                  <div className="rounded-xl border border-[var(--border-color)] bg-[var(--card-bg)] p-6 shadow-sm">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-[var(--foreground)] mb-2">
                          {t.settings.announcementDisplay}
                        </h3>
                        <p className="text-sm text-[var(--muted)] mb-4">
                          {t.settings.announcementDisplayDescription}
                        </p>
                      </div>
                      <div className="flex items-center gap-3">
                        <span className={`text-sm font-medium ${settings.showAnnouncement ? 'text-[var(--foreground)]' : 'text-[var(--muted)]'}`}>
                          {settings.showAnnouncement ? t.settings.show : t.settings.hide}
                        </span>
                        <button
                          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)]/20 focus:ring-offset-2 ${
                            settings.showAnnouncement 
                              ? 'bg-[var(--accent-primary)]' 
                              : 'bg-[var(--border-color)]'
                          }`}
                          onClick={() => setShowAnnouncement(!settings.showAnnouncement)}
                          title={settings.showAnnouncement ? t.settings.hideAnnouncement : t.settings.showAnnouncement}
                        >
                          <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out ${
                              settings.showAnnouncement ? 'translate-x-6' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* 固定在右下角的保存按钮 - 只在相关界面显示 */}
          {activeTab === 'model' && (
            <div className="absolute bottom-5 right-5">
              <button 
                onClick={handleSaveApiKey} 
                className="btn-primary shadow-lg"
                disabled={
                  isSaving ||
                  !localSettings.apiKey
                }
                title={
                  !localSettings.apiKey
                    ? t.settings.apiKeyNotSet
                    : ""
                }
              >
                {isSaving ? (
                  <>
                    <FaSyncAlt className="mr-2 animate-spin" />
                    {t.settings.saving}
                  </>
                ) : (
                  <>
                    <FaSave className="mr-2" />
                    {t.settings.save}
                  </>
                )}
              </button>
            </div>
          )}
          {activeTab === 'whaleDevCloud' && (
            <div className="absolute bottom-5 right-5">
              <button 
                onClick={handleSaveWhaleToken} 
                className="btn-primary shadow-lg"
                disabled={
                  isSaving ||
                  !localSettings.whaleDevCloudToken
                }
                title={
                  !localSettings.whaleDevCloudToken
                    ? t.settings.whalecloudNotSet
                    : ""
                }
              >
                {isSaving ? (
                  <>
                    <FaSyncAlt className="mr-2 animate-spin" />
                    {t.settings.saving}
                  </>
                ) : (
                  <>
                    <FaSave className="mr-2" />
                    {t.settings.save}
                  </>
                )}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Tag Edit Modal */}
      <TagEditModal
        isOpen={showTagEditModal}
        onClose={() => {
          setShowTagEditModal(false);
          setEditingTag(null);
        }}
        onSave={handleSaveTag}
        tag={editingTag || undefined}
        isAdmin={hasSuperAdminPermission() || false}
      />

      {/* Delete Confirm Modal */}
      <DeleteConfirmModal
        isOpen={showDeleteModal}
        onClose={() => {
          setShowDeleteModal(false);
          setDeletingTag(null);
        }}
        onConfirm={handleDeleteTag}
        title={t.tagManagement.confirmDelete}
        message={t.tagManagement.deleteConfirmMessage}
        itemName={deletingTag?.name}
        isDeleting={isDeletingTag}
      />

      {/* Close Alert */}
      <Alert
        isOpen={showCloseAlert}
        title={t.settings.unsavedChanges}
        message={t.settings.unsavedChangesMessage}
        confirmText={t.settings.closeAnyway}
        cancelText={t.settings.returnToEdit}
        onConfirm={() => {
          setShowCloseAlert(false);
          setHasUnsavedChanges(false);
          setNewlyCreatedToken(null);
          onClose();
        }}
        onCancel={() => setShowCloseAlert(false)}
      />

      {/* Token Create Modal */}
      <TokenCreateModal
        isOpen={showTokenCreateModal}
        onClose={() => setShowTokenCreateModal(false)}
        onSuccess={handleTokenCreateSuccess}
      />
    </>
  );
}
