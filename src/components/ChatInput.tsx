'use client';

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { authFetch } from '@/utils/authFetch';
import { useSettings } from '@/contexts/SettingsContext';
import { getProviderModels } from '@/utils/modelConfigCache';
import FileReferenceInput from './FileReferenceInput';
import { useLanguage } from '@/contexts/LanguageContext';
import { WikiStructure } from '@/types/wiki/wikistructure';
import { getServerPublicUrl } from '@/utils/config';
import { FaWandMagicSparkles } from 'react-icons/fa6';
import { useParams } from 'next/navigation';
import { useToast } from '@/contexts/ToastContext';
import { isClearSessionContext, Message } from '@/hooks/useChat';
import { v4 as uuidv4 } from 'uuid';
import { ImImage } from 'react-icons/im';
import { RiTerminalBoxLine } from 'react-icons/ri';
import { fileToBase64 } from '@/utils/fileUtil';

interface Model {
  id: string;
  name: string;
}

interface Provider {
  id: string;
  name: string;
  models: Model[];
  supportsCustomModel?: boolean;
}

interface FileReference {
  id: string;
  path: string;
  isDirectory: boolean;
  name: string;
}

interface ImageObj {
  url: string,
  base64: string,
}

interface SandboxPortalResponse {
  success?: boolean;
  data?: {
    url?: string;
  };
  error?: string;
}

interface ChatInputProps {
  wikiInfoId?: string;
  wikiStructure?: WikiStructure;
  question: string;
  setQuestion: (question: string) => void;
  handleSubmit: (question: string, fileReferences?: FileReference[], commandParams?: { operation: string | null, param: string | null }, images?: Array<string>) => void;
  isLoading: boolean;
  clearConversation: () => void;
  deepResearch: boolean;
  setDeepResearch: (deepResearch: boolean) => void;
  modelType: 'whalecloud' | 'gemini-cli';
  setModelType: (type: 'whalecloud' | 'gemini-cli') => void;
  selectedProvider: string;
  setSelectedProvider: (provider: string) => void;
  selectedModel: string;
  setSelectedModel: (model: string) => void;
  providers: Provider[];
  availableModels: Model[];
  // 新增文件搜索相关属性
  repoUrl?: string;
  branch?: string;
  userCode?: string;
  onFileManagerOpen?: () => void;
  setMessages?: (value: React.SetStateAction<Message[]>) => void
}

const ChatInput: React.FC<ChatInputProps> = ({
  wikiInfoId,
  wikiStructure: wikiStructureProp,
  question,
  setQuestion,
  handleSubmit,
  isLoading,
  deepResearch,
  setDeepResearch,
  modelType,
  setModelType,
  selectedProvider,
  setSelectedProvider,
  selectedModel,
  setSelectedModel,
  providers,
  availableModels,
  repoUrl = '',
  branch = 'main',
  userCode = '',
  onFileManagerOpen,
  setMessages
}) => {
  const { settings } = useSettings();
  const { messages:t } = useLanguage();
  const { addToast } = useToast();

  const params = useParams();
  const sessionId = params.session_id as string;

  const fileInputRef = useRef<HTMLInputElement>(null);
  // 登录容器按钮及确认弹层的引用，方便控制位置与点击范围
  const portalTriggerRef = useRef<HTMLDivElement>(null);
  const portalConfirmRef = useRef<HTMLDivElement>(null);
  const [images, setImages] = useState<Array<ImageObj>>([]);
  const [isImageSupported, setIsImageSupported] = useState<boolean>(true);
  const [isOpeningContainer, setIsOpeningContainer] = useState(false);
  // 记录待确认的容器地址与确认弹层显隐状态
  const [pendingPortalUrl, setPendingPortalUrl] = useState<string | null>(null);
  const [isPortalConfirmVisible, setIsPortalConfirmVisible] = useState(false);
  const [isContainerPortalEnabled, setIsContainerPortalEnabled] = useState(false);
  
  // wiki structure state
  const [wikiStructure, setWikiStructure] = React.useState<WikiStructure | undefined>(wikiStructureProp);

  React.useEffect(() => {
    setWikiStructure(wikiStructureProp);
  }, [wikiStructureProp]);

  useEffect(() => {
    setIsImageSupported(selectedProvider === 'gemini-cli' &&
      (selectedModel === 'gemini-2.5-flash' || selectedModel === 'gemini-2.5-pro'));
    setImages([]);
  }, [selectedProvider, selectedModel]);

  useEffect(() => {
    // Lazy fetch wiki structure from file service when missing
    if (wikiStructure) return;
    if (!repoUrl || !branch || !userCode) return;
    const loadData = async () => {
      // Try loading from server-side cache first
      try {
        const params = new URLSearchParams({
          repo_url: repoUrl,
          branch: branch,
          virtual_file_path: '/i-doc/wiki/wiki_structure.json',
          user_code: userCode || ''
        });
        const res = await authFetch(`/api/wiki/file_content?${params.toString()}`);
        if (res && res.ok) {
          const data = await res.json();
          const remoteWikiStructure = JSON.parse(data?.content);
          setWikiStructure(remoteWikiStructure);
        }
      } catch (error) {
        setWikiStructure(undefined);
        console.error("Error loading from server cache:", error);
      }
    };

    loadData();
  }, [wikiStructure, repoUrl, branch, userCode]);

  // 获取K8s配置，决定是否展示登录容器按钮
  useEffect(() => {
    let cancelled = false;
    const fetchPortalConfig = async () => {
      try {
        const resp = await authFetch('/api/k8s/config');
        if (!resp || !resp.ok) {
          return;
        }
        const body = await resp.json();
        const cfg = body?.data || body;
        if (!cancelled) {
          setIsContainerPortalEnabled(Boolean(cfg?.enable_container_portal_button));
        }
      } catch (error) {
        console.error('加载K8s配置失败', error);
        if (!cancelled) {
          setIsContainerPortalEnabled(false);
        }
      }
    };
    fetchPortalConfig();
    return () => {
      cancelled = true;
    };
  }, []);

  // 当配置关闭时清理弹窗状态
  useEffect(() => {
    if (!isContainerPortalEnabled) {
      setIsPortalConfirmVisible(false);
      setPendingPortalUrl(null);
    }
  }, [isContainerPortalEnabled]);

  // 文件引用状态
  const [fileReferences, setFileReferences] = useState<FileReference[]>([]);
  // 发送按钮可用性：来自 FileReferenceInput 的 typing 状态
  const [canSendFromInput, setCanSendFromInput] = useState(false);
  // 当前完整的输入内容（包括 question 和 currentInput）
  const [fullInputText, setFullInputText] = useState('');
  // 控制是否重置 FileReferenceInput 的 currentInput
  const [shouldResetInput, setShouldResetInput] = useState(false);
  // 优化查询的加载状态
  const [isOptimizing, setIsOptimizing] = useState(false);
  
  // 使用缓存接口获取 gemini-cli 模型
  const [geminiModels, setGeminiModels] = React.useState<Model[]>([]);

  React.useEffect(() => {
    if (modelType === 'gemini-cli') {
      getProviderModels('gemini-cli')
        .then((models) => setGeminiModels(models || []))
        .catch(() => setGeminiModels([]));
    }
  }, [modelType]);

  // 全局键盘事件监听 - 支持 Ctrl+Shift+F 快捷键（只在 gemini-cli 模式下有效）
  React.useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      // 处理 Ctrl+Shift+F 快捷键唤起文件搜索（只在 gemini-cli 模式下有效）
      if (e.ctrlKey && e.shiftKey && e.key === 'F') {
        e.preventDefault();
        // 只有在 gemini-cli 模式下且有 FileReferenceInput 组件时，才触发文件搜索
        if (modelType === 'gemini-cli' && repoUrl && userCode) {
          // 通过模拟点击@按钮来触发文件搜索
          const atButton = document.querySelector('[data-file-search-trigger]') as HTMLButtonElement;
          if (atButton) {
            atButton.click();
          }
        }
      }
    };

    document.addEventListener('keydown', handleGlobalKeyDown);
    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown);
    };
  }, [modelType, repoUrl, userCode]);

  React.useEffect(() => {
    if (modelType === 'gemini-cli' && geminiModels.length > 0) {
      // 若当前选择的模型不在返回列表内，则默认选第一个
      if (!selectedModel || !geminiModels.some(m => m.id === selectedModel)) {
        setSelectedModel(geminiModels[0].id);
      }
    }
  }, [modelType, geminiModels, selectedModel, setSelectedModel]);

  // 处理文件引用变化
  const handleFileReferencesChange = useCallback((files: FileReference[]) => {
    setFileReferences(files);
  }, []);

  // 监听容器确认弹层外部点击，自动关闭确认框
  useEffect(() => {
    if (!isPortalConfirmVisible) return;
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      if (portalConfirmRef.current?.contains(target) || portalTriggerRef.current?.contains(target)) {
        return;
      }
      setIsPortalConfirmVisible(false);
      setPendingPortalUrl(null);
    };
    const timer = window.setTimeout(() => {
      document.addEventListener('mousedown', handleClickOutside);
    }, 0);
    return () => {
      window.clearTimeout(timer);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isPortalConfirmVisible]);

  // 处理登录容器逻辑：先查询接口，成功后显示确认弹窗
  const handleOpenContainerPortal = useCallback(async () => {
    const toastTitle = t?.components?.chatInput?.openContainer || '登录容器';
    if (isOpeningContainer) {
      return;
    }

    if (!isContainerPortalEnabled) {
      return;
    }

    if (isPortalConfirmVisible) {
      setIsPortalConfirmVisible(false);
      setPendingPortalUrl(null);
    }

    if (!wikiInfoId && !repoUrl) {
      addToast({
        type: 'error',
        title: toastTitle,
        message: t?.components?.chatInput?.containerContextMissing || '当前缺少Wiki或仓库信息，无法跳转容器'
      });
      return;
    }

    try {
      setIsOpeningContainer(true);

      const params = new URLSearchParams();
      if (wikiInfoId) {
        params.append('wiki_id', wikiInfoId);
      } else if (repoUrl) {
        params.append('git_url', repoUrl);
        if (branch) {
          params.append('branch', branch);
        }
      }

      const query = params.toString();
      const response = await authFetch(`/api/k8s/sandbox/me/portal${query ? `?${query}` : ''}`);
      if (!response) {
        throw new Error('NO_RESPONSE');
      }

      let body: SandboxPortalResponse | null = null;
      try {
        body = await response.json() as SandboxPortalResponse;
      } catch (parseError) {
        console.error('解析容器门户返回数据失败', parseError);
      }

      const portalSuccess = response.ok && Boolean(body?.success) && Boolean(body?.data?.url);
      if (!portalSuccess) {
        const errorMessage = body?.error || t?.components?.chatInput?.containerUnavailable || '暂未查询到沙箱或容器信息';
        addToast({
          type: 'error',
          title: toastTitle,
          message: errorMessage
        });
        return;
      }

      const targetUrl = body?.data?.url ?? '';
      if (!targetUrl) {
        addToast({
          type: 'error',
          title: toastTitle,
          message: t?.components?.chatInput?.containerUnavailable || '暂未查询到沙箱或容器信息'
        });
        return;
      }

      setPendingPortalUrl(targetUrl);
      setIsPortalConfirmVisible(true);
    } catch (error) {
      console.error('打开容器门户失败', error);
      setIsPortalConfirmVisible(false);
      setPendingPortalUrl(null);
      addToast({
        type: 'error',
        title: t?.components?.chatInput?.openContainer || '登录容器',
        message: t?.components?.chatInput?.containerOpenFailed || '容器跳转失败，请稍后重试'
      });
    } finally {
      setIsOpeningContainer(false);
    }
  }, [addToast, branch, isContainerPortalEnabled, isOpeningContainer, isPortalConfirmVisible, repoUrl, t, wikiInfoId]);

  // 确认打开容器窗口
  const handleConfirmOpenContainer = useCallback(() => {
    if (!pendingPortalUrl || !isContainerPortalEnabled) return;
    const openedWindow = window.open(pendingPortalUrl, '_blank');
    if (openedWindow) {
      try {
        openedWindow.opener = null;
      } catch (error) {
        console.warn('无法重置新窗口的 opener', error);
      }
    } else {
      addToast({
        type: 'warning',
        title: t?.components?.chatInput?.openContainer || '登录容器',
        message: t?.components?.chatInput?.containerPopupBlocked || '浏览器拦截了弹窗，请允许新窗口打开'
      });
    }
    setIsPortalConfirmVisible(false);
    setPendingPortalUrl(null);
  }, [addToast, isContainerPortalEnabled, pendingPortalUrl, t]);

  // 取消打开容器窗口
  const handleCancelOpenContainer = useCallback(() => {
    setIsPortalConfirmVisible(false);
    setPendingPortalUrl(null);
  }, []);


  // 处理表单提交
  const handleSubmitForm = useCallback((overrideQuestion?: string, commandParams?: { operation: string | null, param: string | null }, images?: Array<string>) => {
    const questionToSend = overrideQuestion || question;
    if (isLoading || (!questionToSend.trim() && fileReferences.length === 0)) return;
    
    let sendQuestion = questionToSend;
    
    if (deepResearch && modelType === 'whalecloud') {
      sendQuestion = `[DEEP RESEARCH] ${sendQuestion}`;
    }

    console.log(images);

    handleSubmit(sendQuestion, fileReferences, commandParams, images);
    
    // 提交后只清空输入，保留文件引用
    setQuestion('');
  }, [isLoading, question, fileReferences, deepResearch, modelType, handleSubmit]);
  
  const handleModelTypeChange = (type: 'whalecloud' | 'gemini-cli') => {
    setModelType(type);
    if (type === 'gemini-cli') {
      setDeepResearch(false);
      setSelectedProvider('gemini-cli');
      // 默认模型由接口返回结果决定，这里先清空，等待 useEffect 设置
      setSelectedModel('');
    } else {
      // 切换到WhaleCloud模式时，智能选择一个可用的provider
      // 优先选择'openai',然后是第一个非'gemini-cli'的provider,最后是任意一个
      const whaleProvider = providers.find(p => p.id === 'openai') || 
                            providers.find(p => p.id !== 'gemini-cli') || 
                            (providers.length > 0 ? providers[0] : null);
      
      if (whaleProvider) {
        setSelectedProvider(whaleProvider.id);
        const defaultChatModel = settings.defaultChatModel;
        if (whaleProvider.models.some(m => m.id === defaultChatModel)) {
          setSelectedModel(defaultChatModel);
        } else if (whaleProvider.models.length > 0) {
          setSelectedModel(whaleProvider.models[0].id);
        } else {
          setSelectedModel('');
        }
      } else {
        // 没有可用的provider
        setSelectedProvider('');
        setSelectedModel('');
      }
    }
  };

  const handleMetaPrompt = async () => {
    // 使用完整的输入内容（question + currentInput）
    const queryToOptimize = fullInputText.trim() || question.trim();
    
    if (!queryToOptimize) {
      console.error('No input to optimize');
      return;
    }
    
    setIsOptimizing(true);
    
    // 先清空输入并触发重置
    setQuestion('');
    setShouldResetInput(true);
    
    let optimizedText = '';
    let sseConnection: { close: () => void } | null = null;
    let lastUpdateTime = Date.now();
    const UPDATE_THROTTLE = 50; // 节流：每50ms最多更新一次UI
    try {
      const response = await authFetch(`${getServerPublicUrl()}/api/optimize/question`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache',
        },
        body: JSON.stringify({
          model: selectedModel,
          original_query: queryToOptimize
        })
      });
      
      if (!response || !response.ok) {
        throw new Error(`HTTP error! status: ${response?.status}`);
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error('No response body reader available');
      }
      
      // 使用类似 sseClient 的流式解析逻辑
      let buffer = '';
      let isClosed = false;
      
      const cleanup = () => {
        if (isClosed) return;
        isClosed = true;
        reader.cancel();
      };
      
      sseConnection = { close: cleanup };

      while (!isClosed) {
        const { done, value } = await reader.read();
        
        if (done) {
          break;
        }

        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;
        
        // 按行分割处理
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // 保留最后一个不完整的行

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim();
            
            if (data === '[DONE]') {
              cleanup();
              break;
            }
            
            if (data) {
              try {
                const parsed = JSON.parse(data);
                if (parsed.content) {
                  optimizedText += parsed.content;
                  
                  const currentTime = Date.now();
                  // 节流更新UI：每50ms或积累一定字数后才更新
                  if (currentTime - lastUpdateTime >= UPDATE_THROTTLE || optimizedText.length % 10 === 0) {
                    setQuestion(optimizedText);
                    lastUpdateTime = currentTime;
                  }
                } else if (parsed.error) {
                  throw new Error(parsed.error);
                }
              } catch (e) {
                // 忽略解析错误，继续处理
                console.error('Error in handleMetaPrompt:', e);
              }
            }
          } else if (line.startsWith('event: ')) {
            // 忽略事件类型行
            continue;
          } else if (line.startsWith('id: ')) {
            // 忽略 ID 行
            continue;
          }
        }
      }
      
      setQuestion(optimizedText);
      
      // 流式传输完成后，清空 question 并关闭 resetCurrentInput 标志
      // 此时 currentInput 已经包含完整的优化文本
      setTimeout(() => {
        setQuestion(''); // 清空外层的 question，内容已经在 currentInput 中
        setShouldResetInput(false);
      }, 100);
      
    } catch (error) {
      console.error('Error in handleMetaPrompt:', error);
      // 发生错误时也要清空 question 并关闭 resetCurrentInput
      setTimeout(() => {
        setQuestion('');
        setShouldResetInput(false);
      }, 100);
    } finally {
      setIsOptimizing(false);
      if (sseConnection) {
        sseConnection.close();
      }
    }
  };

  // 清空会话
  const clearChatSession = async () => {
    const response = await authFetch(`/api/chat/session/context?session_id=${sessionId}&provider=${selectedProvider}&model=${selectedModel}&wiki_id=${wikiInfoId}`, {
      method: 'DELETE'
    });
    if (response && response.ok) {
      const body = await response.json();
      if (body && body.success) {
        addToast({
          type: 'success',
          title: t?.components?.chatInput?.clearChatSession,
          message: t?.components?.chatInput?.clearChatSessionSuccess
        });
        if (setMessages) {
          setMessages(value => {
            const filterd = value.filter(item => !isClearSessionContext(item));
            return [...filterd, {
              id: uuidv4(),
              role: 'system',
              content: "",
              provider: selectedProvider as 'gemini-cli' | 'whalecloud',
              msg_data: JSON.stringify({clear_session_context: true})
            }];
          });
        }
      } else {
        if (body.errCode === 'APP_0001') {
          addToast({
            type: 'error',
            title: t?.components?.chatInput?.clearChatSession,
            message: t?.components?.chatInput?.sandboxNotReady
          });
        } else {
          addToast({
            type: 'error',
            title: t?.components?.chatInput?.clearChatSession,
            message: t?.components?.chatInput?.clearChatSessionFailed
          });
        }
      }
    } else {
      console.error(response);
      addToast({
        type: 'error',
        title: t?.components?.chatInput?.clearChatSession,
        message: t?.components?.chatInput?.clearChatSessionFailed
      });
    }
  }

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const file = event.target.files[0];
      if (file) {
        if (file.type.startsWith('image/')) {
          if (file.size > 5242880) {
            addToast({
              type: 'warning',
              title: t?.components?.fileReferenceInput.imageSizeLimit || 'The image size must not exceed 5MB.',
              message: ''
            });
            return;
          }
          // 创建本地预览URL
          const imageUrl = URL.createObjectURL(file);
          const base64 = await fileToBase64(file) as string;
          setImages(pre => [...pre, {
            url: imageUrl,
            base64: base64
          }]);
        } else {
          addToast({
            type: 'warning',
            title: t?.components?.fileReferenceInput.imageOnly || 'Only image file types are supported.',
            message: ''
          });
          return;
        }
      }
    }
  };

  return (
    <div className="backdrop-blur-xl rounded-2xl border p-4 shadow-lg shadow-black/10 glass-effect">
        {/* 模型选择和操作按钮 */}
        <div className="flex items-center justify-between mb-4 px-1">
          {/* 左侧 - 文件管理器 + 模型类型选择器 */}
          <div className="flex items-center gap-2">
            {repoUrl && userCode && (
              <button
                type="button"
                onClick={onFileManagerOpen}
                disabled={isLoading || isOptimizing}
                className={`inline-flex items-center justify-center h-11 w-11 rounded-lg border border-gray-300/70 transition-all duration-200 ${
                  isLoading || isOptimizing
                    ? 'cursor-not-allowed'
                    : 'hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-emerald-500/30'
                }`}
                style={isLoading || isOptimizing ? {
                  backgroundColor: 'var(--accent-secondary)',
                  color: 'var(--muted)'
                } : {
                  backgroundColor: 'var(--card-bg)',
                  color: 'var(--foreground)'
                }}
                title="文件管理器"
              >
                <svg className="w-7 h-7 text-gray-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M3 7v10a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-6L9 5H5a2 2 0 0 0-2 2z"/>
                  <circle cx="12" cy="13" r="1"/>
                  <path d="M12 10v6"/>
                  <path d="M9 13h6"/>
                </svg>
              </button>
            )}

            <div className="bg-gray-50/40 backdrop-blur-sm rounded-xl p-1.5 inline-flex items-center border border-gray-200/40" style={{ backgroundColor: 'var(--accent-secondary)', borderColor: 'var(--border-color)' }}>
            <button
              onClick={() => handleModelTypeChange('gemini-cli')}
              disabled={isOptimizing}
                            className={`text-xs px-4 py-2 rounded-lg transition-all duration-200 font-medium ${
                isOptimizing
                  ? 'cursor-not-allowed opacity-50'
                  : modelType === 'gemini-cli' 
                  ? 'bg-white shadow-lg shadow-black/5 text-emerald-600 border border-gray-200/50' 
                    : 'text-gray-600 hover:text-gray-800 hover:bg-white/50'
                }`}
              style={modelType === 'gemini-cli' ? {
                backgroundColor: 'var(--card-bg)',
                color: 'var(--accent-primary)',
                borderColor: 'var(--border-color)'
              } : {
                color: 'var(--muted)'
              }}
            >
              Gemini-CLI
            </button>
            <button
              onClick={() => handleModelTypeChange('whalecloud')}
              disabled={isOptimizing}
                            className={`text-xs px-4 py-2 rounded-lg transition-all duration-200 font-medium ${
                isOptimizing
                  ? 'cursor-not-allowed opacity-50'
                  : modelType === 'whalecloud' 
                  ? 'bg-white shadow-lg shadow-black/5 text-blue-600 border border-gray-200/50' 
                    : 'text-gray-600 hover:text-gray-800 hover:bg-white/50'
              }`}
              style={modelType === 'whalecloud' ? {
                backgroundColor: 'var(--card-bg)',
                color: 'var(--accent-primary)',
                borderColor: 'var(--border-color)'
              } : {
                color: 'var(--muted)'
              }}
            >
              Model-Ask
            </button>
            </div>
          </div>
          
          {/* 右侧 - 操作按钮组 */}
          <div className="flex items-center space-x-4">
            {/* 登录容器按钮，放在上传图片按钮左侧 */}
            {isContainerPortalEnabled && (
            <div className="relative group" ref={portalTriggerRef}>
              {/* 容器跳转确认弹层 */}
              {isPortalConfirmVisible && pendingPortalUrl && (
                <div
                  ref={portalConfirmRef}
                  onClick={(e) => e.stopPropagation()}
                  className="absolute bottom-full left-1/2 -translate-x-1/2 mb-3 rounded-xl shadow-xl border border-gray-200/70 bg-white/95 backdrop-blur-sm px-4 py-3 w-64 z-20"
                  style={{ backgroundColor: 'var(--card-bg)', color: 'var(--foreground)', borderColor: 'var(--border-color)' }}
                >
                  <div className="text-sm font-semibold mb-2">{t?.components?.chatInput?.openContainerConfirmTitle || '确认跳转容器？'}</div>
                  <p className="text-xs text-gray-500 leading-relaxed mb-3" style={{ color: 'var(--muted)' }}>
                    {t?.components?.chatInput?.openContainerConfirmDesc || '确认后将在新窗口打开沙箱容器。'}
                  </p>
                  <div className="flex justify-end gap-2">
                    <button
                      type="button"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleCancelOpenContainer();
                      }}
                      className="px-3 py-1.5 text-xs rounded-lg border border-gray-200/70 text-gray-600 hover:bg-gray-50 transition-colors"
                      style={{ borderColor: 'var(--border-color)', color: 'var(--muted)' }}
                    >
                      {t?.components?.chatInput?.openContainerConfirmCancel || '取消'}
                    </button>
                    <button
                      type="button"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleConfirmOpenContainer();
                      }}
                      className="px-3 py-1.5 text-xs rounded-lg bg-emerald-500 text-white hover:bg-emerald-600 transition-colors shadow-sm"
                    >
                      {t?.components?.chatInput?.openContainerConfirmAccept || '确认跳转'}
                    </button>
                  </div>
                </div>
              )}
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleOpenContainerPortal();
                }}
                disabled={isLoading || isOptimizing || isOpeningContainer}
                className={`relative flex items-center justify-center h-9 w-9 rounded-lg transition-all duration-300 transform mr-1 ${
                  (isLoading || isOptimizing || isOpeningContainer)
                    ? 'text-gray-500 cursor-not-allowed bg-gray-50'
                    : 'focus:outline-none border-gray-300/70 hover:cursor-pointer hover:text-emerald-500'
                }`}
                title={isOpeningContainer ? (t?.components?.chatInput?.openingContainer || '正在打开容器...') : (t?.components?.chatInput?.openContainer || '登录容器')}
                style={{ userSelect: 'none' }}
              >
                {isOpeningContainer ? (
                  <div className="w-5 h-5 rounded-full border-2 border-t-transparent border-current animate-spin" />
                ) : (
                  <RiTerminalBoxLine className="text-lg" />
                )}
              </button>
              {!isPortalConfirmVisible && (
                <div
                  className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:block bg-white/95 backdrop-blur-sm text-gray-600 text-xs rounded-xl px-3 py-2 z-10 shadow-lg border border-gray-200/60 whitespace-nowrap"
                  style={{ backgroundColor: 'var(--card-bg)', color: 'var(--foreground)', borderColor: 'var(--border-color)' }}
                >
                  {t?.components?.chatInput?.openContainerTooltip || '登录沙箱容器'}
                </div>
              )}
            </div>
            )}
            {/* 图片按钮 */}
            {isImageSupported &&
            <>
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  if (fileInputRef.current) {
                    fileInputRef.current.click();
                  }
                }}
                disabled={isLoading || isOptimizing}
                className={`relative flex items-center justify-center h-9 w-9 rounded-lg transition-all duration-300 transform mr-1 ${
                  (isLoading || isOptimizing)
                    ? 'text-gray-500 cursor-not-allowed bg-gray-50'
                    : 'focus:outline-none border-gray-300/70 hover:cursor-pointer hover:text-blue-500'
                }`}
                title={t?.components?.chatInput?.uploadImage}
                style={{ userSelect: 'none' }}
              >
                <ImImage className='text-lg' />
              </button>
              <input
                type="file"
                accept="image/*"
                ref={fileInputRef}
                style={{ display: 'none' }}
                onChange={handleFileChange}
              />
            </>}

            {/* 清空会话按钮 */}
            {sessionId && <button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                clearChatSession();
              }}
              disabled={isLoading || isOptimizing}
              className={`relative flex items-center justify-center h-9 w-9 rounded-lg transition-all duration-300 transform mr-1 ${
                (isLoading || isOptimizing)
                  ? 'text-gray-500 cursor-not-allowed bg-gray-50'
                  : 'focus:outline-none border-gray-300/70 hover:cursor-pointer hover:text-blue-500'
              }`}
              title={t?.components?.chatInput?.clearChatSession}
              style={{ userSelect: 'none' }}
            >
              <svg viewBox="64 64 896 896" focusable="false" data-icon="clear" width="1.3em" height="1.3em" fill="currentColor" aria-hidden="true"><defs><style></style></defs>
                <path d="M899.1 869.6l-53-305.6H864c14.4 0 26-11.6 26-26V346c0-14.4-11.6-26-26-26H618V138c0-14.4-11.6-26-26-26H432c-14.4 0-26 11.6-26 26v182H160c-14.4 0-26 11.6-26 26v192c0 14.4 11.6 26 26 26h17.9l-53 305.6a25.95 25.95 0 0025.6 30.4h723c1.5 0 3-.1 4.4-.4a25.88 25.88 0 0021.2-30zM204 390h272V182h72v208h272v104H204V390zm468 440V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H416V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H202.8l45.1-260H776l45.1 260H672z"></path>
              </svg>
            </button>}

            {/* 优化提示词按钮 */}
            <div className="relative group" title={t?.components?.chatInput?.optimizePrompt}>
              <button
                onClick={handleMetaPrompt}
                disabled={isLoading || isOptimizing || (question.trim().length === 0 && !canSendFromInput)}
                className={`relative flex items-center justify-center h-9 w-9 rounded-lg transition-all duration-300 transform ${
                  isLoading || isOptimizing || (question.trim().length === 0 && !canSendFromInput)
                    ? 'cursor-not-allowed'
                    : 'focus:outline-none focus:ring-2 focus:ring-purple-500/30 border-gray-300/70 hover:cursor-pointer hover:text-blue-500'
                }`}
              >
                <FaWandMagicSparkles 
                  className={`w-5 h-5 transition-all duration-300 ${
                    isLoading || isOptimizing || (question.trim().length === 0 && !canSendFromInput)
                      ? 'text-gray-500' 
                      : ''
                  }`}
                />
              </button>
            </div>

            {modelType === 'whalecloud' && (
              <>
                <div className="group relative">
                  <label className="flex items-center cursor-pointer">
                    <div className="relative">
                      <input
                        type="checkbox"
                        checked={deepResearch}
                        onChange={() => setDeepResearch(!deepResearch)}
                        className="sr-only peer"
                        disabled={isLoading || isOptimizing}
                      />
                      <div className={`w-11 h-6 rounded-full transition-all duration-200 ${
                        isLoading || isOptimizing
                          ? 'bg-gray-300 cursor-not-allowed' 
                          : 'bg-gray-200 peer-checked:bg-gradient-to-r peer-checked:from-blue-500 peer-checked:to-purple-500'
                      } peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all after:shadow-sm`} 
                      style={isLoading || isOptimizing ? {
                        backgroundColor: 'var(--muted)'
                      } : {
                        backgroundColor: 'var(--border-color)'
                      }}></div>
                    </div>
                    <span className={`ml-3 text-sm font-medium transition-colors ${
                      isLoading || isOptimizing
                        ? 'text-gray-400' 
                        : 'text-gray-700'
                    }`} style={{ color: (isLoading || isOptimizing) ? 'var(--muted)' : 'var(--foreground)' }}>{t.components.chatInput.deepResearch}</span>
                  </label>
                  <div className="absolute bottom-full right-0 mb-2 hidden group-hover:block bg-white/95 backdrop-blur-sm text-gray-600 text-xs rounded-xl p-4 w-80 z-10 shadow-2xl border border-gray-200/50" style={{ backgroundColor: 'var(--card-bg)', color: 'var(--foreground)', borderColor: 'var(--border-color)' }}>
                    <div className="font-semibold text-sm mb-2 text-gray-800" style={{ color: 'var(--foreground)' }}>{t.components.chatInput.deepResearchMode}</div>
                    <p className="text-gray-500 leading-relaxed" style={{ color: 'var(--muted)' }}>{t.components.chatInput.deepResearchModeDescription}</p>
                  </div>
                </div>

                <select
                  value={selectedModel}
                  onChange={(e) => setSelectedModel(e.target.value)}
                  className={`text-sm px-3 py-2 rounded-lg border transition-all duration-200 font-medium min-w-[120px] ${
                    isLoading || isOptimizing
                      ? 'border-gray-200 bg-gray-50 text-gray-400 cursor-not-allowed'
                      : 'border-gray-200/40 bg-white/60 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500/30 focus:border-blue-500/50 hover:bg-white/80'
                  }`}
                  style={isLoading || isOptimizing ? {
                    borderColor: 'var(--border-color)',
                    backgroundColor: 'var(--accent-secondary)',
                    color: 'var(--muted)'
                  } : {
                    borderColor: 'var(--border-color)',
                    backgroundColor: 'var(--card-bg)',
                    color: 'var(--foreground)'
                  }}
                  disabled={availableModels.length === 0 || isLoading || isOptimizing}
                >
                  {availableModels.length > 0 ? (
                    availableModels.map((m) => (
                      <option key={m.id} value={m.id}>
                        {m.name}
                      </option>
                    ))
                  ) : (
                    <option value="">{t.components.chatInput.noModels}</option>
                  )}
                </select>
              </>
            )}
            
            {modelType === 'gemini-cli' && (
              <select
                value={selectedModel}
                onChange={(e) => setSelectedModel(e.target.value)}
                className={`text-sm px-3 py-2 rounded-lg border transition-all duration-200 font-medium min-w-[140px] ${
                  isLoading || isOptimizing
                    ? 'border-gray-200 bg-gray-50 text-gray-400 cursor-not-allowed'
                    : 'border-gray-200/40 bg-white/60 text-gray-700 focus:outline-none focus:ring-2 focus:ring-emerald-500/30 focus:border-emerald-500/50 hover:bg-white/80'
                }`}
                style={isLoading || isOptimizing ? {
                  borderColor: 'var(--border-color)',
                  backgroundColor: 'var(--accent-secondary)',
                  color: 'var(--muted)'
                } : {
                  borderColor: 'var(--border-color)',
                  backgroundColor: 'var(--card-bg)',
                  color: 'var(--foreground)'
                }}
                disabled={isLoading || isOptimizing}
              >
                {geminiModels.map((m) => (
                  <option key={m.id} value={m.id}>
                    {m.name}
                  </option>
                ))}
              </select>
            )}
          </div>
        </div>

        <div className="relative">
        {/* 文件管理器按钮已移动到顶部左侧 */}

        {/* 文件引用输入组件 */}
        <div className="relative">
          <FileReferenceInput
            wikiStructure={wikiStructure}
            value={question}
            onChange={setQuestion}
            onFileReferencesChange={handleFileReferencesChange}
            placeholder={isLoading ? t.components.chatInput.processing : isOptimizing ? '正在优化...' : t.components.chatInput.askQuestion}
            disabled={isLoading || isOptimizing}
            repoUrl={repoUrl}
            branch={branch}
            userCode={userCode}
            onSubmit={(question, commandParams, images) => handleSubmitForm(question, commandParams, images)}
            className={`w-full bg-transparent`}
            style={{
              borderColor: 'var(--border-color)',
              backgroundColor: 'var(--card-bg)'
            }}
            enableFileReferences={modelType === 'gemini-cli'} // 只有在 gemini-cli 模式下才启用文件引用
            onCanSendChange={setCanSendFromInput}
            onFullInputChange={setFullInputText}
            resetCurrentInput={shouldResetInput}
            images={images}
            setImages={setImages}
            isImageSupported={isImageSupported}
          />
          
          {/* 提交按钮 */}
          <button
            type="button"
            onClick={() => {
              // 避免双重提交：根据输入状态选择合适的提交方式
              if (canSendFromInput) {
                // FileReferenceInput 有输入内容，让它处理提交（包括合并 currentInput 和 value）
                window.dispatchEvent(new CustomEvent('file-ref-submit'));
              } else {
                // 只有外层 question，直接提交
                handleSubmitForm(undefined);
              }
            }}
            disabled={isLoading || isOptimizing || (!(question.trim().length > 0 || canSendFromInput) && fileReferences.length === 0)}
            className={`absolute right-3 bottom-3 p-2.5 rounded-xl transition-all duration-200 ${
              isLoading || isOptimizing || (!(question.trim().length > 0 || canSendFromInput) && fileReferences.length === 0)
                ? 'bg-gray-300 cursor-not-allowed text-gray-500'
                : ((question.trim().length > 0 || canSendFromInput) || fileReferences.length > 0)
                ? 'bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white shadow-lg shadow-blue-500/25 hover:shadow-xl hover:shadow-blue-500/40 transform hover:scale-105'
                : 'bg-gray-300 cursor-not-allowed text-gray-500'
            }`}
            style={isLoading || isOptimizing || (!question.trim() && fileReferences.length === 0) ? {
              backgroundColor: 'var(--muted)',
              color: '#FFFFFF'
            } : {}}
            title={isLoading ? t.components.chatInput.processing : isOptimizing ? '正在优化...' : t.components.chatInput.sendMessage}
          >
            {isLoading ? (
              <div className="w-5 h-5 rounded-full border-2 border-t-transparent border-current animate-spin" />
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round">
                <path d="M5 12h14" />
                <path d="M12 5l7 7-7 7" />
              </svg>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatInput;
