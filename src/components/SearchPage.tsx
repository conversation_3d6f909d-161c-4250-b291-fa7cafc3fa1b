'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import Header from '@/components/Header';
import ChatInput from '@/components/ChatInput';
import { authFetch } from '@/utils/authFetch';
import { useChat } from '@/hooks/useChat';
import RepoInfo from '@/types/repoinfo';
import SandboxStatusDisplay from './SandboxStatusDisplay';
import SettingsModal from '@/components/SettingsModal';
import { ToolExecution } from '@/hooks/useChat'
import FileManager from './FileManager';
import { FaShareAlt } from 'react-icons/fa';
import { v4 as uuidv4 } from 'uuid';

// 沙盒状态比对辅助函数
const isStatusMatch = (status1: string, status2: string): boolean => {
  return status1?.toString().trim().toUpperCase() === status2?.toString().trim().toUpperCase();
};

type Message = {
  role: 'user' | 'assistant' | 'system';
  content: string;
  id?: string;
  createdAt?: number; // ms timestamp
  tool_calls?: string;
  error?: string;
  provider?: 'gemini-cli' | 'whalecloud';
  file_references?: string; // backend JSON string
  command_params?: string | { operation: string | null, param: string | null };
  msg_data?: string;
};

interface ToolCall {
  id: string;
  function: Function;
  type: string;
}

interface ToolCalls {
  status: string;
  timestamp: number;
  tool_calls: ToolCall[]
}

interface Function {
  name: string;
  arguments: string;
}

import Markdown from './Markdown';
import {home} from "@/utils/auth";
import {useToast} from "@/contexts/ToastContext";
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { useSettings } from '@/contexts/SettingsContext';
import ImageViewer from './ImageViewer';

const SearchPage = () => {
  // Feature flags
  const ENABLE_REFERENCE_TAB = process.env.NEXT_PUBLIC_ENABLE_REFERENCE_TAB === '1';
  const params = useParams();
  const searchParams = useSearchParams();
  const sessionId = params.session_id as string;
  const isShareMode = searchParams.get('share') === '1';
  // 从sessionStorage获取初始问题，避免URL过长问题
  const [initialQuestion, setInitialQuestion] = useState<string | null>(null);
  const [repoInfo, setRepoInfo] = useState<RepoInfo | null>(null);
  const { userInfo } = useAuth();
  // 直接管理provider和model状态，不再依赖复杂的modelType逻辑
  const [initialProvider, setInitialProvider] = useState<string>('gemini-cli');
  const [initialModel, setInitialModel] = useState<string>('gemini-2.5-flash');
  const [initialDeepResearch, setInitialDeepResearch] = useState<boolean>(false);
  const [initialCommandParams, setInitialCommandParams] = useState<{ operation: string | null, param: string | null }>({ operation: null, param: null });
  // 添加一个标记来指示是否已经应用过初始设置
  const [hasAppliedInitialSettings, setHasAppliedInitialSettings] = useState(false);
  // 添加一个标记来指示是否已完成初始配置加载
  const [isConfigLoaded, setIsConfigLoaded] = useState(false);

  // 设置弹窗
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  // 错误详情弹窗
  const [isErrorModalOpen, setIsErrorModalOpen] = useState(false);
  const [errorContent, setErrorContent] = useState('');
  const errorModalRef = useRef<HTMLDivElement>(null);
  // 分享弹窗
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [shareExpireDay, setShareExpireDay] = useState<string>('1');
  const shareModalRef = useRef<HTMLDivElement>(null);
  const { addToast } = useToast();
  const [showFileManager, setShowFileManager] = useState(false);
  // 从wiki页带入的首条消息文件引用
  type FileReference = { id: string; path: string; isDirectory: boolean; name: string };
  const [initialFileReferences, setInitialFileReferences] = useState<FileReference[] | undefined>(undefined);
  const [initialImages, setInitialImages] = useState<Array<string>>([]);
  const [viewImage, setViewImage] = useState<string | null | undefined>();

  // 兜底函数：当 state 尚未设定时，从 localStorage 读取 fileReferences
  const getInitialFileReferences = useCallback((): FileReference[] | undefined => {
    if (!sessionId) return initialFileReferences;
    if (initialFileReferences && Array.isArray(initialFileReferences)) return initialFileReferences;
    try {
      const paramsKey = `params_${sessionId}`;
      const raw = localStorage.getItem(paramsKey);
      if (!raw) return initialFileReferences;
      const parsed = JSON.parse(raw);
      if (parsed && Array.isArray(parsed.fileReferences)) {
        return parsed.fileReferences as FileReference[];
      }
    } catch {
      // ignore parse errors
    }
    return initialFileReferences;
  }, [sessionId, initialFileReferences]);
  const { messages: tMessages } = useLanguage();
  const { isSettingsLoaded } = useSettings();

  // 尝试解析和格式化JSON内容
  const tryFormatJSON = (content: string) => {
    try {
      // 如果是API错误，提取冒号后的内容
      let jsonContent = content;
      if (content.startsWith('API错误')) {
        jsonContent = content.split(':').slice(1).join(':').trim();
      }

      // 尝试解析JSON
      const parsed = JSON.parse(jsonContent);
      return JSON.stringify(parsed, null, 2);
    } catch {
      // 如果解析失败，返回原始内容
      return content;
    }
  };

  // 显示错误详情
  const showErrorDetails = (content: string) => {
    setErrorContent(content);
    setIsErrorModalOpen(true);
  };

  // 处理弹窗失去焦点时关闭
  useEffect(() => {
    if (!isErrorModalOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (errorModalRef.current && !errorModalRef.current.contains(event.target as Node)) {
        setIsErrorModalOpen(false);
      }
    };

    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsErrorModalOpen(false);
      }
    };

    // 添加事件监听器
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleEscapeKey);

    // 清理事件监听器
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isErrorModalOpen]);

  // 关闭分享弹窗并重置
  const closeShareModal = () => {
    setIsShareModalOpen(false);
    setShareExpireDay('1'); // 重置为初始值
  };

  // 处理分享弹窗失去焦点时关闭
  useEffect(() => {
    if (!isShareModalOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (shareModalRef.current && !shareModalRef.current.contains(event.target as Node)) {
        closeShareModal();
      }
    };

    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        closeShareModal();
      }
    };

    // 添加事件监听器
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleEscapeKey);

    // 清理事件监听器
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isShareModalOpen]);

  // 初始化 BroadcastChannel
  useEffect(() => {
    if (sessionId && typeof window !== 'undefined' && !isShareMode) {
      const channel = new BroadcastChannel('chat-session-status');

      // 发送会话打开消息
      channel.postMessage({
        type: 'SESSION_OPEN',
        sessionId: sessionId,
        timestamp: Date.now()
      });

      // 监听页面关闭事件
      const handleBeforeUnload = () => {
        channel.postMessage({
          type: 'SESSION_CLOSE',
          sessionId: sessionId,
          timestamp: Date.now()
        });
      };

      window.addEventListener('beforeunload', handleBeforeUnload);

      return () => {
        // 组件卸载时发送关闭消息
        channel.postMessage({
          type: 'SESSION_CLOSE',
          sessionId: sessionId,
          timestamp: Date.now()
        });
        window.removeEventListener('beforeunload', handleBeforeUnload);
        channel.close();
      };
    }
  }, [sessionId, isShareMode]);

  useEffect(() => {
    if (sessionId && !isShareMode) {
      console.log('SearchPage初始化，sessionId:', sessionId);

      // 检查是否有sessionStorage缓存（从wiki页面跳转过来的情况）
      const paramsKey = `params_${sessionId}`;
      const initialParams = localStorage.getItem(paramsKey);
      if (initialParams) {
        // 第一次从wiki页面跳转过来，使用sessionStorage中的数据
        console.log('检测到sessionStorage缓存，这是从wiki页面跳转的首次访问');
        // 解析并应用Session参数
        try {
          const parsedParams = JSON.parse(initialParams);

          // 从sessionStorage获取初始问题
          setInitialQuestion(parsedParams.question);

          // 设置初始provider和model
          setInitialProvider(parsedParams.provider);
          setInitialModel(parsedParams.model);
          setInitialDeepResearch(!!parsedParams.deepResearch);
          setInitialCommandParams(parsedParams.commandParams);

          if (parsedParams.images) {
            setInitialImages(parsedParams.images);
          }

          // 处理文件引用参数
          if (parsedParams.fileReferences && Array.isArray(parsedParams.fileReferences)) {
            console.log('SearchPage获取到文件引用:', parsedParams.fileReferences);
            setInitialFileReferences(parsedParams.fileReferences);
          }

          // 直接使用缓存的参数设置repoInfo，不调用API
          setRepoInfo({
            owner: parsedParams.owner || '',
            repo: parsedParams.repo || '',
            type: parsedParams.type || 'WhaleDevCloud',
            token: parsedParams.token || null,
            localPath: null,
            repoUrl: parsedParams.repoUrl || '',
            branch: parsedParams.branch || '',
            subRepos: [],
            wiki_id: parsedParams.wikiId
          });

          // 标记配置已加载完成
          setIsConfigLoaded(true);
        } catch (error) {
          console.error('解析sessionStorage参数失败:', error);
          // 解析失败时使用默认值，不调用API
          setRepoInfo({
            owner: '',
            repo: '',
            type: 'WhaleDevCloud',
            token: null,
            localPath: null,
            repoUrl: '',
            branch: 'master',
            subRepos: [],
            wiki_id: ''
          });
          // 使用默认provider和model
          setInitialProvider('gemini-cli');
          setInitialModel('gemini-2.5-flash');
          setInitialDeepResearch(false);
          setIsConfigLoaded(true);
        }
      } else {
        // 没有sessionStorage缓存，说明是后续直接访问或刷新页面
        // 这种情况下需要调用API获取会话信息（如果会话已存在）
        fetchSessionInfo();
      }
    } else {
      // 没有sessionId的情况，使用默认设置
      setRepoInfo({
        owner: '',
        repo: '',
        type: 'WhaleDevCloud',
        token: null,
        localPath: null,
        repoUrl: '',
        branch: 'master',
        subRepos: [],
        wiki_id: ''
      });
      setInitialProvider('gemini-cli');
      setInitialModel('gemini-2.5-flash');
      setInitialDeepResearch(false);
      setIsConfigLoaded(true);
    }
  }, [sessionId, isShareMode]);

  const fetchSessionInfo = async () => {
    try {
      console.log('尝试获取会话信息，session_id:', sessionId);
      const response = await authFetch(`/api/wiki/info?session_id=${sessionId}&is_share=${isShareMode}`);
      console.log('Session info response status:', response?.status);

      if (response && response.ok) {
        const sessionInfo = await response.json();
        console.log('API返回的会话信息:', sessionInfo);

        // 检查返回的状态
        if (sessionInfo.status === 'not_found' ||
            sessionInfo.status === 'wiki_not_found' ||
            sessionInfo.status === 'wiki_id_missing' ||
            sessionInfo.status === 'error' ||
            sessionInfo.status === 'forbidden' ||
            sessionInfo.status === 'not_current_user') {
          console.log('会话或Wiki信息不存在，status:', sessionInfo.status, '使用默认值');
          setRepoInfo({
            owner: '',
            repo: '',
            type: 'WhaleDevCloud',
            token: null,
            localPath: null,
            repoUrl: '',
            branch: 'master',
            subRepos: [],
            wiki_id: ''
          });
          // 不再需要设置initialProvider和initialModel，因为它们已经从sessionStorage获取
          setIsConfigLoaded(true);
          let errorDesc = tMessages.components.searchPage.sessionOrWikiInfoNotExist;
          switch (sessionInfo.status) {
            case 'not_found':
              errorDesc = tMessages.components.searchPage.sessionNotExist;
              break;
            case 'wiki_not_found':
              errorDesc = tMessages.components.searchPage.wikiInfoNotExist;
              break;
            case 'wiki_id_missing':
              errorDesc = tMessages.components.searchPage.sessionNotRelatedToWiki;
              break;
            case 'forbidden':
              errorDesc = tMessages.components.searchPage.noPermissionToDialogWiki;
              break;
            case 'not_current_user':
              errorDesc = tMessages.components.searchPage.sessionNotBelongToCurrentUser;
              break;
            default:
              break
          }
          addToast({
                type: 'error',
                title: errorDesc,
                message: '',
                duration: 3000,
              });
          setTimeout(() => home(), 3000); // 延迟3秒跳转
          return;
        }

        console.log('成功获取有效的会话信息');

        // 从repo_url中提取owner和repo
        const repoUrl = sessionInfo.repo_url;
        let owner = '';
        let repo = '';

        if (repoUrl) {
          // 处理不同的URL格式
          if (repoUrl.includes('git-nj.iwhalecloud.com')) {
            // whaleDevCloud格式: https://git-nj.iwhalecloud.com/owner/repo.git
            const match = repoUrl.match(/git-nj\.iwhalecloud\.com\/([^\/]+)\/([^\/]+)\.git/);
            if (match) {
              owner = match[1];
              repo = match[2];
            }
          } else if (repoUrl.includes('github.com')) {
            // GitHub格式: https://github.com/owner/repo.git
            const match = repoUrl.match(/github\.com\/([^\/]+)\/([^\/]+)\.git/);
            if (match) {
              owner = match[1];
              repo = match[2];
            }
          } else {
            // 其他格式，尝试从URL中提取
            const urlParts = repoUrl.split('/');
            if (urlParts.length >= 2) {
              owner = urlParts[urlParts.length - 2];
              repo = urlParts[urlParts.length - 1].replace('.git', '');
            }
          }
        }

        console.log('解析的仓库信息:', { owner, repo, repoUrl, branch: sessionInfo.branch });

        setRepoInfo({
          owner: owner,
          repo: repo,
          type: sessionInfo.repo_type || 'WhaleDevCloud',
          token: null,
          localPath: null,
          repoUrl: sessionInfo.repo_url || '',
          branch: sessionInfo.branch || 'master',
          subRepos: [],
          wiki_id: sessionInfo.wiki_id
        });

        // 根据modelType或model信息确定provider，确保一致性
        let determinedModelType: 'gemini-cli' | 'whalecloud' = 'whalecloud'; // 默认值

        // 优先检查API返回的modelType字段
        if (sessionInfo.modelType) {
          determinedModelType = sessionInfo.modelType;
          console.log('从API获取modelType:', sessionInfo.modelType);
        } else if (sessionInfo.provider) {
          // 如果没有modelType字段，通过provider推断
          determinedModelType = sessionInfo.provider === 'gemini-cli' ? 'gemini-cli' : 'whalecloud';
          console.log('通过provider推断modelType:', { provider: sessionInfo.provider, determinedModelType });
        } else if (sessionInfo.model) {
          // 如果没有provider字段，通过model名称推断
          if (sessionInfo.model === 'gemini-2.5-flash' || sessionInfo.model === 'gemini-2.5-pro') {
            determinedModelType = 'gemini-cli';
          } else {
            determinedModelType = 'whalecloud';
          }
          console.log('通过model名称推断modelType:', { model: sessionInfo.model, determinedModelType });
        }

        // 根据确定的modelType设置provider和model
        if (determinedModelType === 'gemini-cli') {
          setInitialProvider('gemini-cli');
          setInitialModel(sessionInfo.model || 'gemini-2.5-flash');
        } else {
          setInitialProvider('whalecloud');
          setInitialModel(sessionInfo.model || '');
        }

        console.log('从API最终确定的模型设置:', {
          modelType: determinedModelType,
          provider: determinedModelType,
          model: sessionInfo.model || (determinedModelType === 'gemini-cli' ? 'gemini-2.5-flash' : '')
        });

        // 标记配置加载完成
        setIsConfigLoaded(true);
      } else if (response?.status === 404) {
        // 404表示会话不存在，这是正常情况（第一次访问或会话未创建）
        console.log('会话信息不存在（404），这可能是首次访问，使用默认值');
        setRepoInfo({
          owner: '',
          repo: '',
          type: 'WhaleDevCloud',
          token: null,
          localPath: null,
          repoUrl: '',
          branch: 'master',
          subRepos: [],
          wiki_id: ''
        });
        setInitialProvider('gemini-cli');
        setInitialModel('gemini-2.5-flash');
        setIsConfigLoaded(true);
      } else {
        // 其他错误
        console.warn('获取会话信息失败，状态码:', response?.status, '使用默认值');
        setRepoInfo({
          owner: '',
          repo: '',
          type: 'WhaleDevCloud',
          token: null,
          localPath: null,
          repoUrl: '',
          branch: 'master',
          subRepos: [],
          wiki_id: ''
        });
        setInitialProvider('gemini-cli');
        setInitialModel('gemini-2.5-flash');
        setIsConfigLoaded(true);
      }

      // 获取会话信息后，加载聊天历史
      await loadChatHistory();
    } catch (error) {
      // 网络错误或其他异常
      console.log('获取会话信息时发生错误（这可能是正常的）:', error);
      setRepoInfo({
        owner: '',
        repo: '',
        type: 'WhaleDevCloud',
        token: null,
        localPath: null,
        repoUrl: '',
        branch: 'master',
        subRepos: [],
        wiki_id: ''
      });
      setInitialProvider('gemini-cli');
      setInitialModel('gemini-2.5-flash');
      setIsConfigLoaded(true);

      // 即使获取会话信息失败，也尝试加载聊天历史
      await loadChatHistory();
    }
  };

  const loadChatHistory = async () => {
    if (sessionId) {
      try {
        console.log('开始加载聊天历史，session_id:', sessionId);
        const response = await authFetch(`/api/chat/history?session_id=${sessionId}`);
        if (response && response.ok) {
          const historyData = await response.json();
          // 处理新的API响应格式
          if (historyData.messages && historyData.messages.length) {
            console.log('加载聊天历史成功，消息数量:', historyData.messages.length);
            setMessages(historyData.messages);

            // 从聊天历史中恢复工具调用信息
            const allToolExecutions: ToolExecution[] = [];
            historyData.messages.forEach((msg: Message) => {
              // 检查消息是否包含工具调用信息
              if (msg.tool_calls) {
                try {
                  const toolCallsData = msg.tool_calls || msg.tool_calls;
                  if (typeof toolCallsData === 'string') {
                    const toolCalls = JSON.parse(toolCallsData);
                    if (Array.isArray(toolCalls)) {
                      toolCalls.forEach((toolCall: ToolCalls) => {
                        if (Array.isArray(toolCall.tool_calls)) {
                          toolCall.tool_calls.forEach((tool: ToolCall) => {
                            // 创建工具执行记录
                            const toolExecution: ToolExecution = {
                              id: tool.id || '',
                              name: tool.function?.name || '工具调用',
                              arguments: tool.function?.arguments || '{}',
                              status: (toolCall.status as 'completed' | 'executing' | 'failed') || 'completed',
                              timestamp: toolCall.timestamp || Date.now(),
                              conversationId: `conv_history_${msg.id}`, // 使用消息ID作为对话ID
                              msgId: msg.id // 关联到消息ID
                            };
                            allToolExecutions.push(toolExecution);
                          })
                        }
                      });
                    }
                  }
                } catch (error) {
                  console.warn('解析工具调用信息失败:', error, '原始数据:', msg.tool_calls);
                }
              }
            });

            // 如果有工具调用信息，设置到状态中
            if (allToolExecutions.length > 0) {
              console.log('从聊天历史恢复工具调用信息:', allToolExecutions);
              // 注意：这里需要等待useChat初始化完成后再设置
              // 使用setTimeout确保在下一个事件循环中执行
              setTimeout(() => {
                // 通过useChat返回的setToolExecutions来设置工具调用状态
                if (typeof setToolExecutions === 'function') {
                  setToolExecutions(allToolExecutions);
                }
              }, 100);
            }

            // 从聊天历史中获取模型信息并设置
            if (historyData.model && historyData.provider) {
              console.log('从聊天历史中获取模型设置:', {
                model: historyData.model,
                provider: historyData.provider
              });

              // 根据modelType逻辑确定正确的provider
              let historyModelType: 'gemini-cli' | 'whalecloud' = 'whalecloud';

              // 优先使用返回的modelType字段
              if (historyData.modelType) {
                historyModelType = historyData.modelType;
              } else if (historyData.provider === 'gemini-cli') {
                historyModelType = 'gemini-cli';
              } else if (historyData.model === 'gemini-2.5-flash' || historyData.model === 'gemini-2.5-pro') {
                historyModelType = 'gemini-cli';
              } else {
                historyModelType = 'whalecloud';
              }

              console.log('从聊天历史确定的modelType:', historyModelType);
              // 1) 更新初始设置，供首次初始化使用
              setInitialProvider(historyModelType);
              setInitialModel(historyData.model);

              // 2) 立刻同步到前端当前选择，避免已完成初始化后不再应用的问题
              console.log('应用聊天历史到当前选择(覆盖前端状态):', {
                applyProvider: historyModelType,
                applyModel: historyData.model,
                hasAppliedInitialSettings
              });
              setSelectedProvider(historyModelType);
              setModelType(historyModelType);

              if (historyModelType === 'gemini-cli') {
                const fixedGeminiModel = (historyData.model === 'gemini-2.5-flash' || historyData.model === 'gemini-2.5-pro')
                  ? historyData.model
                  : 'gemini-2.5-flash';
                setSelectedModel(fixedGeminiModel);
              } else {
                // whalecloud: 直接设置历史模型，useChat内部会在provider模型列表加载后进行纠正
                setSelectedModel(historyData.model || '');
              }
            }
          } else if (Array.isArray(historyData) && historyData.length > 0 && messages.length === 0) {
            // 兼容旧的API响应格式
            console.log('加载聊天历史成功（旧格式），消息数量:', historyData.length);
            setMessages(historyData);
          } else {
            console.log('聊天历史为空或已加载');
          }
        } else {
          console.log('聊天历史API响应失败，状态码:', response?.status);
        }
      } catch (error) {
        console.error("加载会话历史失败:", error);
      }
    }
  };

  const {
    messages,
    question,
    setQuestion,
    isLoading,
    deepResearch,
    setDeepResearch,
    modelType,
    setModelType,
    selectedProvider,
    setSelectedProvider,
    selectedModel,
    setSelectedModel,
    providers,
    availableModels,
    handleSubmit,
    clearConversation,
    researchStages,
    setMessages,
    researchProgress,
    isInitialSetupComplete,
    setToolExecutions, // 获取setToolExecutions函数
    // 分组后的工具调用数据
    groupedToolExecutions,
    // 沙盒状态
    sandboxStatus,
    sandboxPolling,
    sandboxReady,
    sandboxError,
    // 沙盒控制方法
    startSandboxPolling,
    stopSandboxPolling,
    // 紧急修复方法
    manualCheckSandboxStatus,
    forceSandboxReady
  } = useChat({
    repoInfo: repoInfo || { owner: '', repo: '', type: 'WhaleDevCloud', token: null, localPath: null, repoUrl: '', branch: 'master', subRepos: [], wiki_id: '' },
    sessionId,
    initialProvider, // 直接传递初始provider
    initialModel // 直接传递初始model
  });

  const [activeStageIndex, setActiveStageIndex] = useState(0);
  const [isRightPanelOpen, setIsRightPanelOpen] = useState(true);
  const [rightPanelTab, setRightPanelTab] = useState<'research' | 'tools' | 'references'>('research');
  const [expandedTools, setExpandedTools] = useState<Set<string>>(new Set());
  const [userHasCollapsedPanel, setUserHasCollapsedPanel] = useState(false);
  // 确保首次自动提交仅执行一次
  const hasAutoSubmittedRef = useRef<boolean>(false);
  // 追踪是否是用户手动切换到gemini-cli模式（而非页面初始加载）
  const userSwitchedToGeminiRef = useRef<boolean>(false);
  // 追踪是否已经清除过paramsKey
  const hasCleanedParamsRef = useRef<boolean>(false);
  const token  = searchParams.get('token');

  // 分享模式下的聊天内容state
  type ResearchStage = { title: string; content: string; iteration: number; type: string };
  const [shareResearchStages, setShareResearchStages] = useState<ResearchStage[]>([]);
  const [shareMessages, setShareMessages] = useState<Message[]>([]);

  // =============== 引用文档（Reference Files） ===============
  type ReferenceItem = {
    id: string;
    rawPath: string; // 原始匹配到的 /code/... 或 /docs/...
    virtualPath: string; // /data/workspace/code/... 或 /data/workspace/docs/...
    lineStart?: number;
    lineEnd?: number;
    ranges?: Array<{ start: number; end: number }>;
    hasFull?: boolean;
    fileName: string;
    status: 'idle' | 'loading' | 'loaded' | 'error';
    content?: string;
    error?: string;
    msgId?: string; // 归属的用户消息id
  };
  type ReferenceGroup = {
    conversationId: string;
    userQuestion?: string;
    refs: ReferenceItem[];
    stats: { total: number; loading: number; loaded: number };
  };
  const [referenceGroups, setReferenceGroups] = useState<ReferenceGroup[]>([]);
  const [expandedReferences, setExpandedReferences] = useState<Set<string>>(new Set());

  const toggleToolExpansion = (toolId: string) => {
    setExpandedTools(prev => {
      const newSet = new Set(prev);
      if (newSet.has(toolId)) {
        newSet.delete(toolId);
      } else {
        newSet.add(toolId);
      }
      return newSet;
    });
  };

  const showResearchPanel = deepResearch && researchStages.length > 0;
  const showToolPanel = groupedToolExecutions.length > 0; // 基于分组后的工具判断
  const showReferencePanel = ENABLE_REFERENCE_TAB && referenceGroups.length > 0;
  const showRightPanel = showResearchPanel || showToolPanel || showReferencePanel;

  // 在useChat初始化完成后应用初始设置（只执行一次）
  useEffect(() => {
    // 只在首次应用初始设置时执行，避免覆盖用户的手动选择
    if (isConfigLoaded && isInitialSetupComplete && providers.length > 0 && initialProvider && !hasAppliedInitialSettings && !isShareMode) {
      console.log('SearchPage开始应用初始设置:', {
        initialProvider,
        initialModel,
        当前selectedProvider: selectedProvider,
        当前modelType: modelType
      });

      // 检查provider是否存在于当前可用的providers中
      const provider = providers.find(p => p.id === initialProvider);
      if (provider) {
        console.log('找到匹配的provider:', provider);
        setSelectedProvider(initialProvider);

        // 根据provider设置正确的modelType
        if (initialProvider === 'gemini-cli') {
          console.log('设置modelType为gemini-cli');
          setModelType('gemini-cli');
          // gemini-cli的模型验证
          // if (initialModel === 'gemini-2.5-flash' || initialModel === 'gemini-2.5-pro') {
          setSelectedModel(initialModel);
          // } else {
          //   setSelectedModel('gemini-2.5-flash');
          // }
        } else {
          console.log('设置modelType为whalecloud');
          setModelType('whalecloud');
          // 检查model是否存在于该provider的models中
          const modelExists = provider.models.find(m => m.id === initialModel);
          if (modelExists) {
            setSelectedModel(initialModel);
          } else if (provider.models.length > 0) {
            setSelectedModel(provider.models[0].id);
          }
        }

        console.log('SearchPage成功应用初始设置:', {
          provider: initialProvider,
          model: initialModel,
          最终modelType: initialProvider === 'gemini-cli' ? 'gemini-cli' : 'whalecloud'
        });
      } else {
        console.warn('初始provider不存在:', initialProvider, '可用providers:', providers.map(p => p.id));
      }

      // 标记已经应用过初始设置，之后不再干预
      setHasAppliedInitialSettings(true);
    }
  }, [isConfigLoaded, isInitialSetupComplete, providers, initialProvider, initialModel, hasAppliedInitialSettings, selectedProvider, modelType, setSelectedProvider, setSelectedModel, setModelType, isShareMode]);

  // 应用初始的 deepResearch 设置（只执行一次）
  const [hasAppliedInitialDeepResearch, setHasAppliedInitialDeepResearch] = useState(false);
  useEffect(() => {
    if (isConfigLoaded && isInitialSetupComplete && !hasAppliedInitialDeepResearch && !isShareMode) {
      console.log('应用初始deepResearch设置:', initialDeepResearch);
      setDeepResearch(initialDeepResearch);
      setHasAppliedInitialDeepResearch(true);
    }
  }, [isConfigLoaded, isInitialSetupComplete, initialDeepResearch, hasAppliedInitialDeepResearch, setDeepResearch, isShareMode]);

  // 监听modelType变化，标记用户是否手动切换到gemini-cli
  // 使用ref来跟踪上一次的modelType，只有真正发生变化时才认为是用户切换
  const prevModelTypeRef = useRef<string | null>(null);

  useEffect(() => {
    // 只有在初始设置完成后，且modelType确实发生了变化才算用户手动切换
    if (isInitialSetupComplete && hasAppliedInitialSettings && prevModelTypeRef.current !== null && !isShareMode) {
      const prevModelType = prevModelTypeRef.current;
      if (modelType === 'gemini-cli' && prevModelType !== 'gemini-cli') {
        userSwitchedToGeminiRef.current = true;
        console.log('检测到用户手动切换到gemini-cli模式');
      } else if (modelType !== 'gemini-cli' && prevModelType === 'gemini-cli') {
        userSwitchedToGeminiRef.current = false;
        console.log('检测到用户手动切换离开gemini-cli模式');
      }
    }

    // 更新prevModelTypeRef，但只有在初始设置完成后才开始跟踪
    if (isInitialSetupComplete && hasAppliedInitialSettings && !isShareMode) {
      prevModelTypeRef.current = modelType;
    }
  }, [modelType, isInitialSetupComplete, hasAppliedInitialSettings, isShareMode]);

  useEffect(() => {
    if (isShareMode && token) {
      fetch(`/api/share_snapshot?token=${token}`)
        .then(res => res.json())
        .then(data => {
          setShareResearchStages(data.researchStages || []);
          setShareMessages(data.messages || []);
        });
    }
  }, [isShareMode, token]);

  useEffect(() => {
    if (showResearchPanel) {
      setActiveStageIndex(researchStages.length - 1);
      // 只有在用户没有手动折叠面板时才自动打开
      if (!userHasCollapsedPanel) {
      setIsRightPanelOpen(true);
      }
      setRightPanelTab('research');
    }
  }, [researchStages, showResearchPanel, userHasCollapsedPanel]);

  useEffect(() => {
    // 只有在用户没有手动折叠面板且没有研究面板时，才自动打开工具面板
    if (groupedToolExecutions.length > 0 && !showResearchPanel && !userHasCollapsedPanel) {
      setIsRightPanelOpen(true);
      setRightPanelTab('tools');
    }
  }, [groupedToolExecutions, showResearchPanel, userHasCollapsedPanel]);

  // 当有引用文档且无研究、工具面板时，自动打开并切到引用Tab
  useEffect(() => {
    if (showReferencePanel && !showResearchPanel && !showToolPanel && !userHasCollapsedPanel) {
      setIsRightPanelOpen(true);
      setRightPanelTab('references');
    }
  }, [showReferencePanel, showResearchPanel, showToolPanel, userHasCollapsedPanel]);

  useEffect(() => {
    // This effect is to handle the edge case of deep research where we want to clear the initial user message.
    if (deepResearch && researchStages.length === 1 && researchStages[0].iteration === 1) {
      // 移除这段代码，不要清除用户消息历史
      // setMessages(prev => prev.slice(0, 1));
    }
  }, [deepResearch, researchStages, setMessages]);

  // 使用useCallback优化自动提交函数，避免依赖不稳定
  const autoSubmitInitialQuestion = useCallback(() => {
    // 仅在非 gemini-cli 模式下走通用自动提交；gemini-cli 交由沙盒就绪监听处理
    if (
      initialQuestion &&
      messages.length === 0 &&
      !isLoading &&
      isInitialSetupComplete &&
      hasAppliedInitialSettings &&
      modelType !== 'gemini-cli' &&
      !hasAutoSubmittedRef.current
    ) {
      hasAutoSubmittedRef.current = true;
      handleSubmit(initialQuestion, getInitialFileReferences(), initialCommandParams, initialImages);
    }
  }, [
    initialQuestion,
    messages.length,
    isLoading,
    isInitialSetupComplete,
    hasAppliedInitialSettings,
    handleSubmit,
    modelType,
    getInitialFileReferences,
    initialCommandParams
  ]);

  useEffect(() => {
    // 如果有初始问题，自动提交
    if (!isShareMode && isSettingsLoaded) {
      autoSubmitInitialQuestion();
    }
  }, [autoSubmitInitialQuestion, isShareMode, isSettingsLoaded]);

  // 监听消息变化，在第一条消息成功添加时清除paramsKey
  useEffect(() => {
    if (sessionId && messages.length > 0 && !hasCleanedParamsRef.current && initialQuestion && !isShareMode) {
      // 第一条消息已经添加，说明会话请求已经开始，可以安全地清除paramsKey
      const paramsKey = `params_${sessionId}`;
      localStorage.removeItem(paramsKey);
      hasCleanedParamsRef.current = true;
      console.log('第一条消息已发送，清除sessionStorage中的参数:', paramsKey);
    }
  }, [messages.length, sessionId, initialQuestion, isShareMode]);

  // 监听沙盒状态变化，当沙盒从未就绪变为就绪时，自动提交初始问题
  useEffect(() => {
    console.log('SearchPage沙盒状态监听useEffect触发:', {
      modelType,
      sandboxReady,
      initialQuestion: initialQuestion ? '存在' : '不存在',
      messagesLength: messages.length,
      isLoading,
      sandboxStatus
    });

    if (
      modelType === 'gemini-cli' &&
      sandboxReady &&
      initialQuestion &&
      messages.length === 0 &&
      !isLoading &&
      !hasAutoSubmittedRef.current &&
      !isShareMode
    ) {
      console.log('满足自动提交条件，准备提交初始问题:', {
        initialQuestion: initialQuestion.substring(0, 50) + '...',
        将在500ms后提交: true
      });
      setTimeout(() => {
        console.log('执行延迟提交初始问题');
        if (!hasAutoSubmittedRef.current) {
          hasAutoSubmittedRef.current = true;
          handleSubmit(initialQuestion, getInitialFileReferences(), initialCommandParams, initialImages);
        }
      }, 500); // 稍微延迟一下确保所有状态都已更新
    }
  }, [modelType, sandboxReady, initialQuestion, messages.length, isLoading, handleSubmit, isShareMode, getInitialFileReferences, sandboxStatus, initialCommandParams]);

  // 沙盒调试快捷键 + 全局 Ctrl+Shift+F 唤起文件搜索
  useEffect(() => {
    if (!isShareMode) {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+Shift+S: 手动检查沙盒状态
      if (e.ctrlKey && e.shiftKey && e.key === 'S') {
        e.preventDefault();
        console.log('触发手动检查沙盒状态快捷键');
        manualCheckSandboxStatus().then(status => {
          console.log('手动检查结果:', status);
        }).catch(error => {
          console.error('手动检查失败:', error);
        });
      }

      // Ctrl+Shift+F: 全局唤起输入框的文件搜索（无论焦点在哪里）
      if (e.ctrlKey && e.shiftKey && e.key === 'F') {
        e.preventDefault();
        console.log('触发全局文件搜索快捷键');

        // 在开发环境下，如果沙盒未就绪，可以强制设置沙盒就绪
        if (process.env.NODE_ENV === 'development' && !sandboxReady) {
          console.log('开发环境：强制设置沙盒就绪');
          forceSandboxReady();
        }

        // 触发文件搜索事件
        const ev = new CustomEvent('global-file-search');
        window.dispatchEvent(ev);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
    }
  }, [manualCheckSandboxStatus, forceSandboxReady, isShareMode]);

  // 解析助手消息中的引用路径 /code/... 或 /docs/...，支持 #Lx-y 行范围，并按最近的用户消息分组
  const parseReferencesFromMessages = useCallback((allMessages: Message[]): ReferenceGroup[] => {
    const regexDirect = /(\/(code|docs)\/[^\s#\)]+)(?:#L(\d+)(?:-(\d+))?)?/g;
    const regexApiLink = /\((\/api\/file-action\?[^\)\s]+)[^\)]*\)/g; // 匹配 markdown 链接中的 api href
    const groupMap: Record<string, ReferenceGroup> = {};
    for (let i = 0; i < allMessages.length; i++) {
      const m = allMessages[i] as Message;
      if (m.role !== 'assistant' || !m.content) continue;
      // 找到最近上一条用户消息
      let userMsg: Message | undefined;
      for (let j = i - 1; j >= 0; j--) {
        if ((allMessages[j] as Message).role === 'user') { userMsg = allMessages[j] as Message; break; }
      }
      const convId = (userMsg?.id && `conv_${userMsg.id}`) || `conv_idx_${i}`;
      if (!groupMap[convId]) {
        groupMap[convId] = {
          conversationId: convId,
          userQuestion: userMsg?.content || undefined,
          refs: [],
          stats: { total: 0, loading: 0, loaded: 0 }
        };
      }
      let match: RegExpExecArray | null;
      // 1) 直接 /code|/docs 引用
      while ((match = regexDirect.exec(m.content)) !== null) {
        const rawPath = match[1];
        const lineStart = match[3] ? parseInt(match[3], 10) : undefined;
        const lineEnd = match[4] ? parseInt(match[4], 10) : undefined;
        const virtualPath = `/data/workspace${rawPath}`;
        const fileName = rawPath.split('/').pop() || rawPath;
        const existing = groupMap[convId].refs.find(r => r.virtualPath === virtualPath);
        if (!existing) {
          const key = `${convId}:${virtualPath}`;
          const ranges = (lineStart ? [{ start: lineStart, end: (lineEnd && lineEnd >= lineStart ? lineEnd : lineStart) }] : undefined);
          groupMap[convId].refs.push({ id: key, rawPath, virtualPath, lineStart, lineEnd, ranges, hasFull: !lineStart, fileName, status: 'idle', msgId: userMsg?.id });
        } else {
          if (!lineStart) {
            existing.hasFull = true;
            existing.ranges = undefined;
            existing.lineStart = undefined;
            existing.lineEnd = undefined;
          } else if (!existing.hasFull) {
            const segStart = lineStart;
            const segEnd = (lineEnd && lineEnd >= lineStart) ? lineEnd : lineStart;
            existing.ranges = existing.ranges || [];
            existing.ranges.push({ start: segStart, end: segEnd });
            const all = existing.ranges;
            const minStart = Math.min(...all.map(s => s.start));
            const maxEnd = Math.max(...all.map(s => s.end));
            existing.lineStart = minStart;
            existing.lineEnd = maxEnd;
          }
        }
      }
      // 2) /api/file-action 链接
      while ((match = regexApiLink.exec(m.content)) !== null) {
        const href = match[1];
        try {
          const url = new URL(href.startsWith('http') ? href : `http://local${href.startsWith('/') ? '' : '/'}${href}`);
          const filePath = url.searchParams.get('filePath') || '';
          const linesParam = url.searchParams.get('lines') || '';
          if (!filePath || !(filePath.startsWith('code/') || filePath.startsWith('docs/'))) continue;
          const rawPath = `/${filePath}`;
          const virtualPath = `/data/workspace/${filePath}`;
          const fileName = filePath.split('/').pop() || filePath;
          let lineStart: number | undefined;
          let lineEnd: number | undefined;
          if (linesParam) {
            const m2 = /^(\d+)(?:-(\d+))?$/.exec(linesParam);
            if (m2) {
              lineStart = parseInt(m2[1], 10);
              lineEnd = m2[2] ? parseInt(m2[2], 10) : undefined;
            }
          }
          const existing = groupMap[convId].refs.find(r => r.virtualPath === virtualPath);
          if (!existing) {
            const key = `${convId}:${virtualPath}`;
            const ranges = (lineStart ? [{ start: lineStart, end: (lineEnd && lineEnd >= lineStart ? lineEnd : lineStart) }] : undefined);
            groupMap[convId].refs.push({ id: key, rawPath, virtualPath, lineStart, lineEnd, ranges, hasFull: !lineStart, fileName, status: 'idle', msgId: userMsg?.id });
          } else {
            if (!lineStart) {
              existing.hasFull = true;
              existing.ranges = undefined;
              existing.lineStart = undefined;
              existing.lineEnd = undefined;
            } else if (!existing.hasFull) {
              const segStart = lineStart;
              const segEnd = (lineEnd && lineEnd >= lineStart) ? lineEnd : lineStart;
              existing.ranges = existing.ranges || [];
              existing.ranges.push({ start: segStart, end: segEnd });
              const all = existing.ranges;
              const minStart = Math.min(...all.map(s => s.start));
              const maxEnd = Math.max(...all.map(s => s.end));
              existing.lineStart = minStart;
              existing.lineEnd = maxEnd;
            }
          }
        } catch {
          // ignore parse errors
        }
      }
    }
    const groups = Object.values(groupMap).map(g => ({
      ...g,
      stats: {
        total: g.refs.length,
        loading: g.refs.filter(r => r.status === 'loading').length,
        loaded: g.refs.filter(r => r.status === 'loaded').length,
      }
    }));
    return groups;
  }, []);

  // 监听消息变化，提取引用并分组
  useEffect(() => {
    if (!ENABLE_REFERENCE_TAB) {
      setReferenceGroups([]);
      return;
    }
    const sourceMessages = isShareMode ? shareMessages : messages;
    const groups = parseReferencesFromMessages(sourceMessages);
    setReferenceGroups(groups);
  }, [messages, shareMessages, isShareMode, parseReferencesFromMessages, ENABLE_REFERENCE_TAB]);

  // 拉取指定引用的文件内容（通过id）
  const fetchReferenceContentById = useCallback(async (refId: string) => {
    let ref: ReferenceItem | undefined;
    let gIndex = -1; let rIndex = -1;
    for (let i = 0; i < referenceGroups.length; i++) {
      const j = referenceGroups[i].refs.findIndex(r => r.id === refId);
      if (j >= 0) { ref = referenceGroups[i].refs[j]; gIndex = i; rIndex = j; break; }
    }
    if (!ref) return;
    if (!repoInfo?.repoUrl || !repoInfo?.branch || !userInfo?.user_code) return;
    // 已经加载过或正在加载时不重复
    if (ref.status === 'loaded' || ref.status === 'loading') return;

    setReferenceGroups(prev => {
      const next = [...prev];
      if (gIndex >= 0 && rIndex >= 0) {
        const group = { ...next[gIndex] };
        const refs = [...group.refs];
        refs[rIndex] = { ...refs[rIndex], status: 'loading', error: undefined };
        group.refs = refs;
        group.stats = { ...group.stats, loading: group.stats.loading + 1 };
        next[gIndex] = group;
      }
      return next;
    });

    try {
      const params = new URLSearchParams({
        repo_url: repoInfo.repoUrl,
        branch: repoInfo.branch || 'main',
        virtual_path: ref.virtualPath,
        user_code: userInfo.user_code || ''
      });
      const res = await fetch(`/api/file/read?${params.toString()}`);
      if (!res.ok) {
        const text = await res.text();
        throw new Error(text || `HTTP ${res.status}`);
      }
      const data = await res.json();
      const fullContent: string = data?.data?.content || '';
      let sliced = fullContent;
      if (!ref.hasFull && (ref.ranges && ref.ranges.length > 0)) {
        const lines = fullContent.split(/\r?\n/);
        const parts: string[] = [];
        // 合并相邻或重叠区间
        const sorted = [...ref.ranges].sort((a, b) => a.start - b.start);
        const merged: Array<{ start: number; end: number }> = [];
        for (const seg of sorted) {
          if (merged.length === 0) { merged.push({ ...seg }); continue; }
          const last = merged[merged.length - 1];
          if (seg.start <= last.end + 1) { last.end = Math.max(last.end, seg.end); }
          else { merged.push({ ...seg }); }
        }
        merged.forEach((seg) => {
          const start = Math.max(1, seg.start);
          const end = Math.min(lines.length, seg.end);
          const block = lines.slice(start - 1, end).join('\n');
          parts.push(block);
        });
        sliced = parts.join('\n\n');
      } else if (ref.lineStart && !ref.hasFull) {
        const lines = fullContent.split(/\r?\n/);
        const start = Math.max(1, ref.lineStart);
        const end = Math.min(lines.length, ref.lineEnd && ref.lineEnd >= ref.lineStart ? ref.lineEnd : start);
        sliced = lines.slice(start - 1, end).join('\n');
      }
      setReferenceGroups(prev => {
        const next = [...prev];
        if (gIndex >= 0 && rIndex >= 0) {
          const group = { ...next[gIndex] };
          const refs = [...group.refs];
          refs[rIndex] = { ...refs[rIndex], status: 'loaded', content: sliced };
          group.refs = refs;
          group.stats = {
            total: group.refs.length,
            loading: group.refs.filter(r => r.status === 'loading').length,
            loaded: group.refs.filter(r => r.status === 'loaded').length,
          };
          next[gIndex] = group;
        }
        return next;
      });
    } catch (e: unknown) {
      setReferenceGroups(prev => {
        const next = [...prev];
        if (gIndex >= 0 && rIndex >= 0) {
          const group = { ...next[gIndex] };
          const refs = [...group.refs];
          refs[rIndex] = { ...refs[rIndex], status: 'error', error: (e as Error)?.message || '加载失败' };
          group.refs = refs;
          group.stats = {
            total: group.refs.length,
            loading: group.refs.filter(r => r.status === 'loading').length,
            loaded: group.refs.filter(r => r.status === 'loaded').length,
          };
          next[gIndex] = group;
        }
        return next;
      });
    }
  }, [referenceGroups, repoInfo, userInfo]);

  // const toggleReferenceExpansion = (refId: string) => {
  //   setExpandedReferences(prev => {
  //     const next = new Set(prev);
  //     if (next.has(refId)) {
  //       next.delete(refId);
  //     } else {
  //       next.add(refId);
  //       setTimeout(() => fetchReferenceContentById(refId), 0);
  //     }
  //     return next;
  //   });
  // };

  // 更兼容的复制函数
  const copyToClipboard = async (text: string) => {
    // 回退方案：创建临时 textarea
    const textarea = document.createElement('textarea');
    textarea.value = text;
    textarea.style.position = 'fixed';
    textarea.style.opacity = '0';
    document.body.appendChild(textarea);
    textarea.focus();
    textarea.select();
    try {
      const successful = document.execCommand('copy');
      document.body.removeChild(textarea);
      return successful;
    } catch {
      document.body.removeChild(textarea);
      return false;
    }
  };

  // 打开分享弹窗
  const handleShare = () => {
    if (isLoading) {
      return; // 如果正在加载，不执行分享操作
    }
    setIsShareModalOpen(true);
  };

  // 确认分享并发送请求
  const confirmShare = async () => {
    try {
      const expireHours = parseInt(shareExpireDay);
      if ((isNaN(expireHours) || expireHours <= 0) && shareExpireDay !== '') {
        addToast({
          type: 'error',
          title: tMessages.components.searchPage.invalidExpireTime || '请输入有效的过期时长',
          message: ''
        });
        return;
      }

      const lastMessageId = messages[messages.length - 1].id;
      const res = await fetch('/api/share_snapshot', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId,
          lastMessageId,
          expires_in: expireHours
        }),
      });
      const data = await res.json();
      const url = typeof window !== 'undefined'
        ? `${window.location.origin}${window.location.pathname}?share=1&token=${data.token}`
        : '';
      const success = await copyToClipboard(url);

      // 关闭弹窗
      closeShareModal();

      if (success) {
        addToast({
          type: 'success',
          title: tMessages.components.searchPage.shareSuccess,
          message: tMessages.components.searchPage.shareSuccessMessage
        });
      } else {
        addToast({
          type: 'error',
          title: tMessages.components.searchPage.shareFailedMessage,
          message: ''
        });
      }
    } catch {
      addToast({
        type: 'error',
        title: tMessages.components.searchPage.shareFailed,
        message: ''
      });
    }
  };

  const renderRightPanelContent = () => {
    if (rightPanelTab === 'research' && showResearchPanel) {
      const currentResearchStages = isShareMode ? shareResearchStages : researchStages;

      return (
        <>
          <div className="flex overflow-x-auto custom-scrollbar">
            {currentResearchStages.map((stage, index) => (
              <button
                key={index}
                onClick={() => setActiveStageIndex(index)}
                className={`py-2 px-4 text-sm font-medium transition-colors whitespace-nowrap ${
                  activeStageIndex === index
                    ? 'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'text-gray-500 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
              >
                {stage.title}
              </button>
            ))}
            {!isShareMode && isLoading && (
              <div className="flex items-center px-4 py-2">
                <div className="typing-indicator">
                    <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                    <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                    <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                  </div>
              </div>
            )}
          </div>
          {/* 研究进度条 - 在分享模式下隐藏 */}
          {!isShareMode && (
            <div className="px-4 py-2">
              <div className="flex items-center mb-1">
                <span className="text-xs font-medium text-blue-700 dark:text-blue-500">
                  {tMessages.components.searchPage.researchProgress}: {Math.round((researchProgress.current / researchProgress.total) * 100)}%
                </span>
                <span className="ml-auto text-xs text-gray-500">
                  {researchProgress.current}/{researchProgress.total} {tMessages.components.searchPage.iteration}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-500 ease-out"
                  style={{
                    width: `${Math.round((researchProgress.current / researchProgress.total) * 100)}%`
                  }}
                ></div>
              </div>
            </div>
          )}
          <div className="flex-1 overflow-y-auto pt-4 custom-scrollbar pb-50">
            <div className="prose prose-sm dark:prose-invert max-w-none px-4">
              <Markdown content={currentResearchStages[activeStageIndex]?.content || ''} sessionId={sessionId}/>
            </div>
          </div>
        </>
      );
    }
    return null;
  };

  // 如果是gemini-cli模式且沙盒未就绪，显示沙盒状态
  // 显示全屏沙盒状态页面的条件：
  // 1. 页面初始加载时（非用户手动切换）且沙盒未就绪
  // 2. 有严重错误需要用户处理
  if (modelType === 'gemini-cli' && !sandboxReady && !isShareMode && (
    // 页面初始加载时（非用户手动切换到gemini-cli）
    // 只要沙盒未就绪就应该显示，直到READY状态
    !userSwitchedToGeminiRef.current ||
    // 严重错误状态，无论如何都需要显示
    isStatusMatch(sandboxStatus, 'FAILED') ||
    isStatusMatch(sandboxStatus, 'QUERY_FAILED') ||
    isStatusMatch(sandboxStatus, 'QUOTA_EXCEEDED') ||
    (sandboxError && (sandboxError.includes('轮询超时') || sandboxError.includes('配额已满')))
  )) {
    console.log('显示沙盒状态页面，当前状态:', {
      modelType,
      sandboxReady,
      sandboxPolling,
      sandboxStatus,
      sandboxError,
      userSwitchedToGemini: userSwitchedToGeminiRef.current,
      repoInfoReady: !!(repoInfo?.repoUrl && repoInfo?.branch)
    });
    return (
      <>
      <SandboxStatusDisplay
        sandboxStatus={sandboxStatus}
        sandboxPolling={sandboxPolling}
        sandboxError={sandboxError}
        isShareMode={isShareMode}
        onShare={handleShare}
        onOpenSettings={() => setIsSettingsModalOpen(true)}
        onRetry={() => {
          // 重新启动沙盒轮询
          console.log('用户触发沙盒状态重试，重新启动轮询');
          stopSandboxPolling();
          setTimeout(() => {
            startSandboxPolling();
          }, 1000); // 延迟1秒重新启动，确保之前的轮询完全停止
        }}
      />
      <SettingsModal isOpen={isSettingsModalOpen} onClose={() => setIsSettingsModalOpen(false)} />
      </>
    );
  }

  const isClearSessionContext = (msg: Message) => {
    if (msg && msg.role === 'system' && msg.msg_data) {
      try {
        const msg_data = JSON.parse(msg.msg_data);
        return !!msg_data.clear_session_context;
      } catch (error) {
        console.error(error);
        return false;
      }
    }
    return false;
  }

  return (
      <div className="flex flex-col h-screen bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-gray-200">
      {/* 如果配置还没有加载完成，显示加载状态 */}
      {!isConfigLoaded && !isSettingsLoaded ? (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">{tMessages.components.searchPage.initializing}</p>
          </div>
        </div>
      ) : (
        <>
          {!isShareMode && <Header onShare={handleShare} showJobs={false} onOpenSettings={() => setIsSettingsModalOpen(true)} shareDisabled={isLoading} />}

          <div className="flex-1 flex overflow-hidden">
            <div className="flex-1 flex flex-col w-full mx-auto px-4 sm:px-6 lg:px-16 xl:px-24 2xl:px-32">
              {/* Main chat area */}
              <div className="flex-1 flex pt-4 gap-4 overflow-hidden">
                {/* Left panel for chat history */}
                <div className={`transition-all duration-300 ${showRightPanel && isRightPanelOpen ? 'w-2/3' : 'w-full'}`}>
                  <div className="flex-1 overflow-y-auto px-4 pb-56 custom-scrollbar h-full">


                    {(isShareMode ? shareMessages : messages).map((msg, index) => {
                      // msg 断言为 Message 类型
                      const m = msg as Message;
                      // 仅当消息具备实际的 createdAt 才显示时间戳
                      let timeText: string | null = null;
                      let images: Array<string> = [];
                      if (m.createdAt) {
                        const ts = new Date(m.createdAt);
                        const now = new Date();
                        const isToday = ts.getFullYear() === now.getFullYear() && ts.getMonth() === now.getMonth() && ts.getDate() === now.getDate();
                        timeText = isToday
                          ? ts.toLocaleTimeString('zh', { hour: '2-digit', minute: '2-digit', second: '2-digit' })
                          : ts.toLocaleString('zh', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit' });
                      }
                      if (m.msg_data) {
                        try {
                          const msg_data_json = JSON.parse(m.msg_data);
                          if (msg_data_json.images) {
                            images = msg_data_json.images;
                          }
                        }
                        catch (error) {
                          console.error(error);
                        }
                      }
                      return (
                        <div key={index} className={`my-6`}>
                          {/* 展示清除上下文的提示 */}
                          {isClearSessionContext(m) ? (
                            <div className='w-full flex justify-center items-center border-y-gray-400 border-1 p-1.5' style={{maskImage: "linear-gradient(90deg, transparent, #000, transparent)"}}>
                              <div className='text-xs'>{tMessages.components.chatHistory.contextCleared}</div>
                            </div>
                            ) :
                            m.role === 'user' ? (
                              <div className="flex flex-col gap-1">
                                <div className="flex items-center gap-3 mb-0">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                                  </svg>
                                  {/* 快速命令徽标 */}
                                  {(() => {
                                    const raw = m.command_params as string | { operation?: string | null; param?: string | null } | undefined;
                                    let cmd: { operation?: string | null; param?: string | null } | null = null;
                                    if (raw) {
                                      if (typeof raw === 'string') {
                                        try { cmd = JSON.parse(raw); } catch { cmd = null; }
                                      } else {
                                        cmd = raw;
                                      }
                                    }
                                    if (!cmd || !cmd.operation) return null;
                                    return (
                                      <div className="flex items-center gap-2">
                                        <span className="inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs border border-[var(--border-color)] bg-[var(--background)]/60 text-[var(--muted)]">
                                          <span className="font-mono">/{cmd.operation}</span>
                                          {cmd.param ? <span className="truncate max-w-[12rem]" title={cmd.param || ''}>{cmd.param}</span> : null}
                                        </span>
                                      </div>
                                    );
                                  })()}
                                  <h3 className="text-lg font-medium text-blue-600 dark:text-blue-400">
                                    {m.content?.replace('[DEEP RESEARCH]', '').trim()}
                                  </h3>
                                </div>
                                {/* 图片列表 */}
                                {images.length > 0 && (
                                  <div className="flex w-full mt-1 gap-2">
                                    {images.map((image) => (
                                      <img
                                        key={uuidv4()}
                                        className="w-20 h-20 border-blue-300 border rounded-sm object-cover cursor-pointer"
                                        src={image}
                                        onClick={() => setViewImage(image)}
                                      />
                                    ))}
                                  </div>
                                )}
                                {/* 文件/目录引用 chips（发送时立即显示） */}
                                {m.file_references && (() => {
                                  try {
                                    const refs = JSON.parse(m.file_references || '[]') as Array<{ path: string; isDirectory: boolean; name: string }>;
                                    if (!Array.isArray(refs) || refs.length === 0) return null;
                                    return (
                                      <div className="pl-1 mt-1 mb-1 flex flex-wrap gap-2">
                                        {refs.map((r, idx) => (
                                          <span key={idx} className={`inline-flex items-center gap-1 px-1.5 py-0.5 rounded text-xs border ${r.isDirectory ? 'bg-blue-50 text-blue-700 border-blue-200' : 'bg-emerald-50 text-emerald-700 border-emerald-200'}`}>
                                            <svg className="w-3 h-3" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                                              {r.isDirectory ? (
                                                <path d="M3 7v10a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-6L9 5H5a2 2 0 0 0-2 2z"/>
                                              ) : (
                                                <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5.586a1 1 0 0 1 .707.293l5.414 5.414a1 1 0 0 1 .293.707V19a2 2 0 0 1-2 2z"/>
                                              )}
                                            </svg>
                                            <span>{r.name || r.path}</span>
                                          </span>
                                        ))}
                                      </div>
                                    );
                                  } catch {
                                    return null;
                                  }
                                })()}

                                {timeText && (
                                  <div className="pl-1 text-left"><span className="text-[10px] leading-none text-gray-400">{timeText}</span></div>
                                )}
                              </div>
                            ) : (
                              <div className="flex flex-col gap-1">
                                <div className="prose prose-sm dark:prose-invert max-w-none pl-1 [&_*:last-child]:mb-0">
                                  <div className="flex items-end gap-2">
                                    <div className="min-w-0">
                                      <Markdown content={m.content || ''} sessionId={sessionId}/>
                                    </div>
                                    {m.error && (
                                      <button
                                        onClick={() => showErrorDetails(m.error || '')}
                                        className="text-[14px] leading-none text-red-500 hover:text-red-700 underline cursor-pointer transition-colors whitespace-nowrap flex-shrink-0 pb-[5px]"
                                        title={tMessages.components.searchPage.errorDetails}
                                      >
                                        {tMessages.components.searchPage.errorDetails}
                                      </button>
                                    )}
                                  </div>
                                </div>
                                {timeText && (
                                  <div className="pl-1 text-left mt-1 flex items-center gap-4">
                                    <span className="text-[11px] leading-none text-gray-400">{timeText}</span>
                                    {m.provider && (
                                      <span className="text-[11px] leading-none text-gray-400">
                                        {m.provider}
                                      </span>
                                    )}
                                    {
                                      selectedModel && (
                                        <span className="text-[11px] leading-none text-gray-400">
                                          {selectedModel}
                                        </span>
                                      )
                                    }
                                  </div>
                                )}
                              </div>
                            )
                          }
                        </div>
                      );
                    })}
                    {isLoading && messages.length > 0 && messages[messages.length - 1].role === 'user' && (
                      <div className="flex flex-col gap-2 my-4">
                        <div className="flex items-center pl-1">
                          <div className="typing-indicator">
                            <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                            <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                            <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Loading indicator for assistant response */}
                    {isLoading && (
                      <div className="flex items-center my-4 animate-fadeIn">
                        <div className="ml-3">
                          <div className="flex items-center gap-2">
                            <div className="typing-indicator text-blue-500">
                              <span></span>
                              <span></span>
                              <span></span>
                            </div>
                            <span className="text-sm text-gray-600 dark:text-gray-400">{tMessages.components.searchPage.aiThinking}</span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Right panel for research stages and tool calls */}
                {showRightPanel && isRightPanelOpen && (
                  <div className="w-1/3 flex flex-col rounded-2xl shadow-lg h-[calc(100vh-330px)]" style={{ backgroundColor: 'var(--panel-bg)', backgroundClip: 'padding-box', border: '1px solid var(--border-color)', boxShadow: '0 2px 10px -3px rgba(0,0,0,0.35), 0 4px 6px -4px rgba(0,0,0,0.35)', overflow: 'hidden' }}>
                    {/* Tab navigation and panel content - complete implementation */}
                    <div className="flex justify-between items-center border-b px-4 py-3 rounded-t-2xl" style={{ borderColor: 'var(--tab-border)', backgroundColor: 'var(--tab-bg)' }}>
                      <div className="flex">
                        {showResearchPanel && (
                          <button
                            onClick={() => setRightPanelTab('research')}
                            className={`py-2 px-4 text-sm font-medium transition-colors border-b-2 ${
                              rightPanelTab === 'research'
                                ? 'border-blue-500'
                                : 'border-transparent'
                            }`}
                            style={rightPanelTab === 'research' ? {
                              borderColor: 'var(--tab-border-active)',
                              color: 'var(--tab-text-active)'
                            } : {
                              color: 'var(--tab-text)'
                            }}
                          >
                            {tMessages.components.searchPage.deepResearch}
                            {researchStages.length > 0 && (
                              <span className="ml-1 px-1.5 py-0.5 text-xs rounded-full" style={{ backgroundColor: 'var(--badge-blue-bg)', color: 'var(--badge-blue-text)' }}>
                                {researchStages.length}
                              </span>
                            )}
                          </button>
                        )}
                        {showToolPanel && (
                          <button
                            onClick={() => setRightPanelTab('tools')}
                            className={`py-2 px-4 text-sm font-medium transition-colors border-b-2 ${
                              rightPanelTab === 'tools'
                                ? 'border-blue-500'
                                : 'border-transparent'
                            }`}
                            style={rightPanelTab === 'tools' ? {
                              borderColor: 'var(--tab-border-active)',
                              color: 'var(--tab-text-active)'
                            } : {
                              color: 'var(--tab-text)'
                            }}
                          >
                            {tMessages.components.searchPage.toolCall}
                            {/* {groupedToolExecutions.length > 0 && (
                              <span className="ml-1 px-1.5 py-0.5 text-xs rounded-full" style={{ backgroundColor: 'var(--badge-teal-bg)', color: 'var(--badge-teal-text)' }}>
                                {groupedToolExecutions.length}
                              </span>
                            )} */}
                          </button>
                        )}
                        {ENABLE_REFERENCE_TAB && showReferencePanel && (
                          <button
                            onClick={() => setRightPanelTab('references')}
                            className={`py-2 px-4 text-sm font-medium transition-colors border-b-2 ${
                              rightPanelTab === 'references'
                                ? 'border-blue-500'
                                : 'border-transparent'
                            }`}
                            style={rightPanelTab === 'references' ? {
                              borderColor: 'var(--tab-border-active)',
                              color: 'var(--tab-text-active)'
                            } : {
                              color: 'var(--tab-text)'
                            }}
                          >
                            {tMessages.components.searchPage.referenceFiles || '引用文档'}
                            {referenceGroups.length > 0 && (
                              <span className="ml-1 px-1.5 py-0.5 text-xs rounded-full" style={{ backgroundColor: 'var(--badge-gray-bg)', color: 'var(--badge-gray-text)' }}>
                                {referenceGroups.reduce((sum, g) => sum + g.refs.length, 0)}
                              </span>
                            )}
                          </button>
                        )}
                      </div>

                      {/* 折叠按钮 - 在分享模式下隐藏 */}
                      {!isShareMode && (
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => {
                              setIsRightPanelOpen(false);
                              setUserHasCollapsedPanel(true);
                            }}
                            className="group p-2 rounded-lg transition-all duration-200 border border-transparent"
                            style={{ borderColor: 'transparent' }}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: 'var(--tab-text)' }}>
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          </button>
                        </div>
                      )}
                    </div>

                    {/* Panel content */}
                    {rightPanelTab === 'research' ? renderRightPanelContent() : rightPanelTab === 'tools' ? (
                       <div className="flex-1 overflow-y-auto custom-scrollbar">
                         {groupedToolExecutions.length === 0 ? (
                           <div className="flex flex-col items-center justify-center h-full p-12" style={{ color: 'var(--empty-state-text)' }}>
                             <div className="relative mb-6">
                               <div className="w-16 h-16 rounded-2xl flex items-center justify-center border" style={{ backgroundColor: 'var(--empty-state-bg)', borderColor: 'var(--empty-state-border)' }}>
                                 <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: 'var(--empty-state-icon)' }}>
                                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                 </svg>
                               </div>
                               <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                                 <svg className="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                                 </svg>
                               </div>
                             </div>
                             <div className="text-center">
                               <p className="text-base font-medium mb-2" style={{ color: 'var(--empty-state-text)' }}>{tMessages.components.searchPage.noToolCall}</p>
                               <p className="text-sm max-w-64 leading-relaxed" style={{ color: 'var(--empty-state-desc)' }}>{tMessages.components.searchPage.noToolCallDescription}</p>
                             </div>
                           </div>
                         ) : (
                           <div className="p-4 space-y-4">
                             {/* 工具执行统计 */}
                             <div className="mb-6">
                               <div className="flex items-center justify-between">
                                 <div className="flex items-center gap-3">
                                   <div className="w-1 h-6 bg-gradient-to-b from-blue-500 to-teal-500 rounded-full"></div>
                                   <h3 className="text-lg font-semibold" style={{ color: 'var(--foreground)' }}>{tMessages.components.searchPage.toolExecution}</h3>
                                 </div>
                                 <div className="flex items-center gap-6">
                                   {groupedToolExecutions.some(group => group.stats.executing > 0) && (
                                     <div className="flex items-center gap-2">
                                       <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                       <span className="text-sm font-medium" style={{ color: 'var(--tool-blue-text)' }}>
                                         {groupedToolExecutions.reduce((sum, group) => sum + group.stats.executing, 0)} {tMessages.components.searchPage.executing}
                                       </span>
                                     </div>
                                   )}
                                   {groupedToolExecutions.some(group => group.stats.completed > 0) && (
                                     <div className="flex items-center gap-2">
                                       <div className="w-2 h-2 bg-teal-500 rounded-full"></div>
                                       <span className="text-sm font-medium" style={{ color: 'var(--tool-teal-text)' }}>
                                         {groupedToolExecutions.reduce((sum, group) => sum + group.stats.completed, 0)} {tMessages.components.searchPage.completed}
                                       </span>
                                     </div>
                                   )}
                                 </div>
                               </div>
                             </div>

                             {/* 对话组列表 */}
                             <div className="space-y-6">
                               {groupedToolExecutions.map((group, groupIndex) => (
                                 <div key={group.conversationId} className="space-y-4">
                                   {/* 对话组标题和统计 */}
                                   <div className="flex items-center justify-between p-4 rounded-xl border" style={{ backgroundColor: 'var(--tool-group-header-bg)', borderColor: 'var(--tab-border)' }}>
                                     <div className="flex items-center gap-3">
                                       <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-teal-500 rounded-full"></div>
                                       <div>
                                         <h4 className="font-semibold text-sm" style={{ color: 'var(--foreground)' }}>
                                         {group.userQuestion
                                             ? (group.userQuestion.length > 60
                                                ? group.userQuestion.substring(0, 60) + '...'
                                                : group.userQuestion)
                                             : `${tMessages.components.searchPage.dialogRound} ${groupIndex + 1}`}
                                         </h4>
                                       </div>
                                     </div>
                                     <div className="flex items-center gap-4 text-xs">
                                       <span className="px-2 py-1 rounded-md" style={{ backgroundColor: 'var(--badge-gray-bg)', color: 'var(--badge-gray-text)' }}>
                                         {tMessages.components.searchPage.total} {group.stats.total}
                                       </span>
                                     </div>
                                   </div>

                                   {/* 该对话组的工具调用列表 */}
                                   <div className="space-y-3 pl-4">
                                     {group.tools.map((tool, toolIndex) => (
                                       <div
                                         key={tool.id}
                                         className={`group relative overflow-hidden transition-all duration-300 ${
                                           expandedTools.has(tool.id) ? 'mb-4' : ''
                                         }`}
                                       >
                                         {/* 主卡片 */}
                                         <div
                                           className={`relative backdrop-blur-sm rounded-2xl border transition-all duration-300 cursor-pointer ${
                                             tool.status === 'executing' 
                                               ? 'shadow-sm'
                                               : 'shadow-sm hover:shadow-md'
                                           } ${
                                             expandedTools.has(tool.id) 
                                               ? 'shadow-lg' 
                                               : 'hover:shadow-md'
                                           }`}
                                           style={{
                                             backgroundColor: 'var(--tool-card-bg)',
                                             borderColor: tool.status === 'executing' ? 'var(--tool-blue-border)' : 'var(--tool-card-border)',
                                             boxShadow: expandedTools.has(tool.id) ? 'var(--tool-card-shadow)' : undefined
                                           }}
                                           onClick={() => toggleToolExpansion(tool.id)}
                                         >
                                           {/* 状态指示条 */}
                                           <div className={`absolute left-0 top-4 bottom-4 w-1 rounded-r-full`} style={{ backgroundColor: tool.status === 'executing' ? 'var(--tool-status-executing)' : 'var(--tool-status-completed)' }}></div>

                                           <div className="p-5 pl-8">
                                             <div className="flex items-center justify-between">
                                               <div className="flex items-center gap-4 flex-1">
                                                 {/* 图标 */}
                                                 <div className={`relative flex-shrink-0 w-10 h-10 rounded-2xl flex items-center justify-center border`} style={{
                                                   backgroundColor: tool.status === 'executing' ? 'var(--tool-blue-bg)' : 'var(--tool-teal-bg)',
                                                   borderColor: tool.status === 'executing' ? 'var(--tool-blue-border)' : 'var(--tool-teal-border)'
                                                 }}>
                                                 {tool.status === 'executing' ? (
                                                     <div className="w-5 h-5 rounded-full border-2 border-blue-500 border-t-transparent animate-spin"></div>
                                                   ) : (
                                                     <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: 'var(--tool-teal-text)' }}>
                                                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                                     </svg>
                                                 )}
                                               </div>

                                                 {/* 内容 */}
                                                 <div className="flex-1 min-w-0">
                                                   <div className="flex items-center gap-3 mb-1">
                                                     <h4 className="font-semibold truncate" style={{ color: 'var(--foreground)' }}>
                                                     {tool.name}
                                                   </h4>
                                                     <span className="text-xs px-2 py-0.5 rounded-md" style={{ color: 'var(--tab-text)', backgroundColor: 'var(--tab-bg)' }}>
                                                     #{toolIndex + 1}
                                                   </span>
                                                   </div>
                                                   <div className="flex items-center gap-4 text-sm" style={{ color: 'var(--tab-text)' }}>
                                                     <span>{new Date(tool.msgId ? tool.timestamp * 1000 : tool.timestamp).toLocaleTimeString('zh', { hour: '2-digit', minute: '2-digit', second: '2-digit' })}</span>
                                                     <span className={`inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium`} style={{
                                                       backgroundColor: tool.status === 'executing' ? 'var(--badge-blue-bg)' : 'var(--badge-teal-bg)',
                                                       color: tool.status === 'executing' ? 'var(--badge-blue-text)' : 'var(--badge-teal-text)'
                                                     }}>
                                                       {tool.status === 'executing' ? tMessages.components.searchPage.executing : tMessages.components.searchPage.completed}
                                                   </span>
                                                   </div>
                                                 </div>
                                               </div>

                                               {/* 展开箭头 */}
                                               <div className="flex-shrink-0 ml-4">
                                                 <svg
                                                   className={`w-5 h-5 transition-transform duration-200 ${
                                                     expandedTools.has(tool.id) ? 'rotate-180' : ''
                                                   }`}
                                                   fill="none"
                                                   viewBox="0 0 24 24"
                                                   stroke="currentColor"
                                                   style={{ color: 'var(--tool-arrow)' }}
                                                 >
                                                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                                 </svg>
                                               </div>
                                             </div>
                                           </div>
                                         </div>

                                         {/* 展开内容 */}
                                         {expandedTools.has(tool.id) && (
                                           <div className="mt-3 backdrop-blur-sm rounded-xl border p-5 animate-fadeIn" style={{ backgroundColor: 'var(--tool-expanded-bg)', borderColor: 'var(--tool-expanded-border)' }}>
                                             <div className="space-y-5">
                                               {/* 参数 */}
                                               <div>
                                                 <div className="flex items-center gap-2 mb-3">
                                                   <svg className="w-4 h-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                     <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                                                   </svg>
                                                   <h5 className="text-sm font-medium" style={{ color: 'var(--tool-param-text)' }}>{tMessages.components.searchPage.toolCallParameter}</h5>
                                                 </div>
                                                 <div className="rounded-lg border p-4 overflow-hidden" style={{ backgroundColor: 'var(--tool-param-bg)', borderColor: 'var(--tool-param-border)' }}>
                                                   <pre className="text-xs font-mono leading-relaxed overflow-x-auto" style={{ color: 'var(--tool-param-text)' }}>
                                                     {(() => {
                                                       // 处理双重转义的JSON字符串
                                                       let parsed;
                                                       try {
                                                         // 首先尝试直接解析
                                                         parsed = JSON.parse(tool.arguments);
                                                       } catch (firstError) {
                                                         try {
                                                           // 如果失败，尝试处理双重转义的情况
                                                           // 将 \\" 替换为 "，然后再次解析
                                                           const unescaped = tool.arguments.replace(/\\"/g, '"');
                                                           parsed = JSON.parse(unescaped);
                                                           console.log('firstError', firstError);
                                                         } catch (secondError) {
                                                           // 如果仍然失败，检查是否是JSON格式的字符串
                                                           console.log('secondError', secondError);
                                                           if (tool.arguments.startsWith('{') || tool.arguments.startsWith('[')) {
                                                             return `${tMessages.components.searchPage.originalParameter}: ${tool.arguments}`;
                                                           } else {
                                                             return tool.arguments;
                                                           }
                                                         }
                                                       }
                                                       return JSON.stringify(parsed, null, 2);
                                                     })()}
                                                   </pre>
                                                 </div>
                                               </div>

                                               {/* 结果 */}
                                               {tool.result && (
                                                 <div>
                                                   <div className="flex items-center gap-2 mb-3">
                                                     <svg className="w-4 h-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                                     </svg>
                                                     <h5 className="text-sm font-medium" style={{ color: 'var(--tool-result-text)' }}>{tMessages.components.searchPage.toolExecutionResult}</h5>
                                                   </div>
                                                   <div className="rounded-lg border p-4 overflow-hidden" style={{ backgroundColor: 'var(--tool-param-bg)', borderColor: 'var(--tool-param-border)' }}>
                                                     <div className="text-xs font-mono leading-relaxed whitespace-pre-wrap" style={{ color: 'var(--tool-result-text)' }}>
                                                       {tool.result}
                                                     </div>
                                                   </div>
                                                 </div>
                                               )}
                                             </div>
                                           </div>
                                         )}
                                       </div>
                                     ))}
                                   </div>
                                 </div>
                               ))}
                             </div>
                           </div>
                         )}
                       </div>
                    ) : ENABLE_REFERENCE_TAB ? (
                      // references tab content (grouped by user message)
                      <div className="flex-1 overflow-y-auto custom-scrollbar">
                        {referenceGroups.length === 0 ? (
                          <div className="flex flex-col items-center justify-center h-full p-12" style={{ color: 'var(--empty-state-text)' }}>
                            <div className="text-center">
                              <p className="text-base font-medium mb-2" style={{ color: 'var(--empty-state-text)' }}>{tMessages.components.searchPage.noReferenceFiles || '没有引用文档'}</p>
                              <p className="text-sm max-w-64 leading-relaxed" style={{ color: 'var(--empty-state-desc)' }}>{tMessages.components.searchPage.noReferenceFilesDescription || '当回答中包含 /code 或 /docs 的文件引用时，将在此处展示。'}</p>
                            </div>
                          </div>
                        ) : (
                          <div className="p-4 space-y-6">
                            {referenceGroups.map((group, groupIndex) => (
                              <div key={group.conversationId} className="space-y-3">
                                <div className="flex items-center justify-between p-4 rounded-xl border" style={{ backgroundColor: 'var(--tool-group-header-bg)', borderColor: 'var(--tab-border)' }}>
                                  <div className="flex items-center gap-3">
                                    <div className="w-2 h-2 bg-gradient-to-r from-gray-500 to-blue-500 rounded-full"></div>
                                    <div>
                                      <h4 className="font-semibold text-sm" style={{ color: 'var(--foreground)' }}>
                                        {group.userQuestion
                                          ? (group.userQuestion.length > 60 ? group.userQuestion.substring(0, 60) + '...' : group.userQuestion)
                                          : `${tMessages.components.searchPage.dialogRound} ${groupIndex + 1}`}
                                      </h4>
                                    </div>
                                  </div>
                                  <div className="flex items-center gap-4 text-xs">
                                    <span className="px-2 py-1 rounded-md" style={{ backgroundColor: 'var(--badge-gray-bg)', color: 'var(--badge-gray-text)' }}>
                                      {tMessages.components.searchPage.total} {group.stats.total}
                                    </span>
                                  </div>
                                </div>
                                <div className="space-y-3 pl-4">
                                  {group.refs.map((ref, idx) => (
                                    <div key={ref.id} className={`group relative overflow-hidden transition-all duration-300 ${expandedReferences.has(ref.id) ? 'mb-4' : ''}`}>
                                      <div
                                        className={`relative backdrop-blur-sm rounded-2xl border transition-all duration-300 cursor-pointer ${ref.status === 'loading' ? 'shadow-sm' : 'shadow-sm hover:shadow-md'} ${expandedReferences.has(ref.id) ? 'shadow-lg' : 'hover:shadow-md'}`}
                                        style={{
                                          backgroundColor: 'var(--tool-card-bg)',
                                          borderColor: ref.status === 'loading' ? 'var(--tool-blue-border)' : 'var(--tool-card-border)',
                                          boxShadow: expandedReferences.has(ref.id) ? 'var(--tool-card-shadow)' : undefined
                                        }}
                                        onClick={() => {
                                          setExpandedReferences(prev => {
                                            const next = new Set(prev);
                                            if (next.has(ref.id)) next.delete(ref.id); else next.add(ref.id);
                                            return next;
                                          });
                                          if (!expandedReferences.has(ref.id)) {
                                            setTimeout(() => fetchReferenceContentById(ref.id), 0);
                                          }
                                        }}
                                      >
                                        <div className={`absolute left-0 top-4 bottom-4 w-1 rounded-r-full`} style={{ backgroundColor: ref.status === 'loading' ? 'var(--tool-status-executing)' : 'var(--tool-status-completed)' }}></div>
                                        <div className="p-5 pl-8">
                                          <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-4 flex-1">
                                              <div className={`relative flex-shrink-0 w-10 h-10 rounded-2xl flex items-center justify-center border`} style={{
                                                backgroundColor: ref.status === 'loading' ? 'var(--tool-blue-bg)' : 'var(--tool-teal-bg)',
                                                borderColor: ref.status === 'loading' ? 'var(--tool-blue-border)' : 'var(--tool-teal-border)'
                                              }}>
                                                {ref.status === 'loading' ? (
                                                  <div className="w-5 h-5 rounded-full border-2 border-blue-500 border-t-transparent animate-spin"></div>
                                                ) : (
                                                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: 'var(--tool-teal-text)' }}>
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                                  </svg>
                                                )}
                                              </div>
                                              <div className="flex-1 min-w-0">
                                                <div className="flex items-center gap-3 mb-1">
                                                  <h4 className="font-semibold truncate" style={{ color: 'var(--foreground)' }}>{ref.fileName}</h4>
                                                  <span className="text-xs px-2 py-0.5 rounded-md" style={{ color: 'var(--tab-text)', backgroundColor: 'var(--tab-bg)' }}>#{idx + 1}</span>
                                                  {ref.lineStart && (
                                                    <span className="text-[10px] px-1 py-0.5 rounded-md border" style={{ color: 'var(--tab-text)', backgroundColor: 'var(--background)', borderColor: 'var(--border-color)' }}>L{ref.lineStart}{ref.lineEnd ? `-${ref.lineEnd}` : ''}</span>
                                                  )}
                                                </div>
                                                <div className="text-xs text-[var(--muted)] truncate">{ref.rawPath}</div>
                                              </div>
                                            </div>
                                            <div className="flex-shrink-0 ml-4">
                                              <svg className={`w-5 h-5 transition-transform duration-200 ${expandedReferences.has(ref.id) ? 'rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: 'var(--tool-arrow)' }}>
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                              </svg>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                      {expandedReferences.has(ref.id) && (
                                        <div className="mt-3 backdrop-blur-sm rounded-xl border p-5 animate-fadeIn" style={{ backgroundColor: 'var(--tool-expanded-bg)', borderColor: 'var(--tool-expanded-border)' }}>
                                          {ref.status === 'error' && (
                                            <div className="text-sm" style={{ color: 'var(--destructive)' }}>
                                              {tMessages.common?.loadFailed || '加载失败'}: {ref.error}
                                            </div>
                                          )}
                                          {ref.status !== 'error' && (
                                            <div className="my-2 rounded-md overflow-hidden text-sm shadow-sm border border-[var(--border-color)]">
                                              <div className="bg-gray-800 text-gray-200 px-5 py-2 text-sm flex justify-between items-center">
                                                <span className="font-medium truncate">{ref.rawPath}</span>
                                                <button onClick={() => navigator.clipboard.writeText(ref.content || '')} className="text-gray-400 hover:text-white transition-colors" title={tMessages.components.Markdown.copyCode}>
                                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" /></svg>
                                                </button>
                                              </div>
                                              <pre className="m-0 p-4 overflow-x-auto text-[13px] leading-relaxed" style={{ backgroundColor: '#1f2937', color: '#e5e7eb' }}>
{ref.content || (tMessages.common?.loading || '加载中...')}
                                              </pre>
                                            </div>
                                          )}
                                        </div>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="flex-1 overflow-y-auto custom-scrollbar pb-50"></div>
                    )}
                  </div>
                )}

                {showRightPanel && !isRightPanelOpen && (
                  <div className="fixed top-1/2 right-4 transform -translate-y-1/2 z-20">
                    <div className="group">
                      <button
                        onClick={() => {
                          setIsRightPanelOpen(true);
                          setUserHasCollapsedPanel(false);
                        }}
                        className="backdrop-blur-sm p-3 rounded-2xl shadow-xl border transition-all duration-300 hover:scale-105 hover:shadow-2xl" style={{ backgroundColor: 'var(--collapse-btn-bg)', borderColor: 'var(--collapse-btn-border)' }}
                        title={tMessages.components.searchPage.expandPanel}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: 'var(--collapse-btn-text)' }}>
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                        </svg>
                      </button>

                      {/* 工具数量指示器 */}
                      {rightPanelTab === 'tools' && groupedToolExecutions.length > 0 && (
                        <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-teal-500 to-blue-500 text-white text-xs font-bold rounded-full flex items-center justify-center shadow-lg">
                          {groupedToolExecutions.length}
                        </div>
                      )}

                      {/* 研究阶段指示器 */}
                      {rightPanelTab === 'research' && researchStages.length > 0 && (
                        <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs font-bold rounded-full flex items-center justify-center shadow-lg">
                          {researchStages.length}
                        </div>
                      )}
                      {/* 引用数量指示器 */}
                      {rightPanelTab === 'references' && referenceGroups.length > 0 && (
                        <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-gray-500 to-blue-500 text-white text-xs font-bold rounded-full flex items-center justify-center shadow-lg">
                          {referenceGroups.reduce((sum, g) => sum + g.refs.length, 0)}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {!isShareMode && (
            <div className="fixed bottom-0 left-0 right-0 z-10 flex justify-center p-4 pointer-events-none">
              <div className={`pointer-events-auto min-w-124 transition-all duration-300 ${
                showRightPanel && isRightPanelOpen ? 'w-2/3' : 'w-3/5'
              }`}>
                <ChatInput
                  wikiInfoId={repoInfo?.wiki_id || ''}
                  question={question}
                  setQuestion={setQuestion}
                  handleSubmit={handleSubmit}
                  isLoading={isLoading}
                  clearConversation={clearConversation}
                  deepResearch={deepResearch}
                  setDeepResearch={setDeepResearch}
                  modelType={modelType}
                  setModelType={setModelType}
                  selectedProvider={selectedProvider}
                  setSelectedProvider={setSelectedProvider}
                  selectedModel={selectedModel}
                  setSelectedModel={setSelectedModel}
                  providers={providers}
                  availableModels={availableModels}
                  repoUrl={repoInfo?.repoUrl || ''}
                  branch={repoInfo?.branch || 'main'}
                  userCode={userInfo?.user_code || ''}
                  onFileManagerOpen={() => setShowFileManager(true)}
                  setMessages={setMessages}
                />
              </div>
            </div>
          )}
          <SettingsModal isOpen={isSettingsModalOpen} onClose={() => setIsSettingsModalOpen(false)} />

                     {/* 错误详情弹窗 */}
           {isErrorModalOpen && (
             <div className="fixed inset-0 z-50 flex justify-end">
               {/* 右侧滑出面板 */}
               <div ref={errorModalRef} className="relative bg-white dark:bg-gray-800 w-full max-w-md h-full shadow-2xl transform transition-transform duration-300 ease-in-out">
                <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">{tMessages.components.searchPage.errorDetails}</h3>
                  <button
                    onClick={() => setIsErrorModalOpen(false)}
                    className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                  <div className="p-4 overflow-y-auto h-[calc(100%-8rem)]">
                   <div className="prose prose-sm dark:prose-invert max-w-none">
                      {(() => {
                        const formattedContent = tryFormatJSON(errorContent);
                        const isAPIError = errorContent.startsWith('API错误');

                        return (
                          <div className="space-y-3">
                            {isAPIError && (
                              <p className="text-sm text-red-600 dark:text-red-400 font-bold">
                                {errorContent.split(':')[0]}:
                              </p>
                            )}
                              <pre className="whitespace-pre-wrap break-words text-sm bg-gray-100 dark:bg-red-100 p-4 overflow-x-auto shadow-sm border border-gray-200 dark:border-red-300">
                                <code className="language-json text-gray-800 dark:text-red-200">{formattedContent}</code>
                              </pre>
                          </div>
                        );
                      })()}
                   </div>
                 </div>
              </div>
            </div>
          )}

          {/* 分享弹窗 */}
          {isShareModalOpen && (
            <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm">
              <div ref={shareModalRef} className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-md mx-4 transform transition-all duration-300 ease-in-out">
                {/* 弹窗头部 */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-center gap-3">
                    <FaShareAlt className="w-5 h-5 text-[var(--head-text-color)]" />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {tMessages.components.searchPage.shareDialog || '分享对话'}
                    </h3>
                  </div>
                  <button
                    onClick={closeShareModal}
                    className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                {/* 弹窗内容 */}
                <div className="p-6 space-y-5">
                  <div>
                    <label htmlFor="expireHours" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {tMessages.components.searchPage.shareExpireTime || '分享有效期'}
                    </label>
                    <div className="relative">
                      <input
                        id="expireDay"
                        type="text"
                        inputMode="numeric"
                        min="1"
                        max="8760"
                        value={shareExpireDay}
                        onChange={(e) => {
                          const value = e.target.value;
                          // 只允许输入数字
                          if (value === '' || /^\d+$/.test(value)) {
                            setShareExpireDay(value);
                          }
                        }}
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white transition-all"
                        placeholder={tMessages.components.searchPage.enterExpireTime || '请输入有效期（天）'}
                      />
                      <span className="absolute right-4 top-1/2 -translate-y-1/2 text-sm text-gray-500 dark:text-gray-400">
                        {tMessages.components.searchPage.day || '天'}
                      </span>
                    </div>
                    <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                      {tMessages.components.searchPage.shareExpireHint || '分享链接将在指定时长后失效'}
                    </p>
                  </div>

                  {/* 快捷选择 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {tMessages.components.searchPage.quickSelect || '快捷选择'}
                    </label>
                    <div className="grid grid-cols-4 gap-2">
                      {[
                        { label: '1天', value: '1' },
                        { label: '7天', value: '7' },
                        { label: '30天', value: '30' },
                        { label: '3个月', value: '90' }
                      ].map((option) => (
                        <button
                          key={option.value}
                          onClick={() => setShareExpireDay(option.value)}
                          style={{ backgroundColor: shareExpireDay === option.value ? 'rgb(18, 47, 96)' : 'rgb(249, 250, 251)' }}
                          className={`px-3 py-2 text-sm rounded-lg border transition-all ${
                            shareExpireDay === option.value
                              ? 'bg-[rgb(18, 47, 96)] text-white'
                              : 'bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:border-blue-400'
                          }`}
                        >
                          {option.label}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>

                {/* 弹窗底部按钮 */}
                <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-700">
                  <button
                    onClick={closeShareModal}
                    className="px-5 py-2.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                  >
                    {tMessages.common?.cancel || '取消'}
                  </button>
                  <button
                    onClick={confirmShare}
                    className="px-5 py-2.5 text-sm font-medium text-white rounded-lg transition-colors flex items-center gap-2"
                    style={{ backgroundColor: 'rgb(18, 47, 96)' }}
                    onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgb(28, 57, 106)'}
                    onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'rgb(18, 47, 96)'}
                  >
                    <FaShareAlt className="w-4 h-4" />
                    {tMessages.components.searchPage.confirmShare || '确认分享'}
                  </button>
                </div>
              </div>
            </div>
          )}
        </>
      )}
      {isConfigLoaded && repoInfo && (
        <FileManager
            isOpen={showFileManager}
            onClose={() => setShowFileManager(false)}
            repoUrl={repoInfo?.repoUrl || ''}
            branch={repoInfo?.branch || 'main'}
            userCode={userInfo?.user_code || ''}
        />
      )}

      {viewImage &&
        <ImageViewer data={viewImage} onClose={() => setViewImage(null)} />}
    </div>
  );
};

export default SearchPage;
