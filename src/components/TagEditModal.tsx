'use client';

import React, { useState, useEffect, useRef } from 'react';
import { FaTimes, FaSave, FaPalette } from 'react-icons/fa';
import { Tag, TagCreateRequest, TagUpdateRequest, TAG_TYPE } from '@/types/tag';
import { useLanguage } from '@/contexts/LanguageContext';

interface TagEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (tag: TagCreateRequest | TagUpdateRequest) => Promise<void>;
  tag?: Tag; // 如果提供，则为编辑模式；否则为新建模式
  isAdmin: boolean;
}

const PRESET_COLORS = [
  "#FF6B6B",
  "#4ECDC4",
  "#45B7D1",
  "#96CEB4",
  "#DDA0DD",
  "#98D8C8",
  "#F7DC6F",
  "#BB8FCE",
  "#E74C3C",
  "#D7BDE2",
  "#F9E79F",
  "#D5A6BD",
  "#A9CCE3",
  "#FAD7A0",
  "#D2B4DE",
  "#2C3E50",
  "#34495E",
  "#8B4513",
  "#4A235A",
  "#1B4F72",
];

export default function TagEditModal({ isOpen, onClose, onSave, tag, isAdmin }: TagEditModalProps) {
  const { messages: t } = useLanguage();
  const [formData, setFormData] = useState<TagCreateRequest>({
    name: '',
    type: TAG_TYPE.USER,
    color: '#4ECDC4',
    comments: '',
    module_type: 1,
    state: 1
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const colorPickerRef = useRef<HTMLDivElement>(null);
  const colorButtonRef = useRef<HTMLButtonElement>(null);

  // 检查是否有权限操作系统标签
  const canManageSystemTags = isAdmin;
  
  // 检查是否可以创建系统标签（新建模式且为管理员）
  const canCreateSystemTag = !tag && canManageSystemTags;
  
  // 检查是否可以编辑当前标签（编辑模式）
  const canEditCurrentTag = tag ? (
    tag.type === TAG_TYPE.USER || canManageSystemTags
  ) : true;

  useEffect(() => {
    if (isOpen) {
      if (tag) {
        // 编辑模式
        setFormData({
          name: tag.name,
          type: tag.type,
          color: tag.color,
          comments: tag.comments || '',
          module_type: tag.module_type,
          state: tag.state
        });
      } else {
        // 新建模式 - 只有管理员才能创建系统标签
        setFormData({
          name: '',
          type: canCreateSystemTag ? TAG_TYPE.SYSTEM : TAG_TYPE.USER,
          color: '#4ECDC4',
          comments: '',
          module_type: 1,
          state: 1
        });
      }
      setIsSubmitting(false);
      setShowColorPicker(false);
    }
  }, [isOpen, tag, canCreateSystemTag]);

  // 点击外部关闭颜色选择器
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      if (
        showColorPicker &&
        colorPickerRef.current &&
        colorButtonRef.current &&
        !colorPickerRef.current.contains(event.target as Node) &&
        !colorButtonRef.current.contains(event.target as Node)
      ) {
        setShowColorPicker(false);
      }
    };

    if (showColorPicker) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [showColorPicker]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      return;
    }

    // 权限检查：非管理员不能创建系统标签
    if (!canManageSystemTags && formData.type === TAG_TYPE.SYSTEM) {
      console.error('权限不足：只有管理员才能创建系统标签');
      return;
    }

    setIsSubmitting(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error('保存标签失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleColorSelect = (color: string) => {
    setFormData(prev => ({ ...prev, color }));
    setShowColorPicker(false);
  };

  const handleClose = () => {
    setShowColorPicker(false);
    onClose();
  };

  // 如果没有权限编辑当前标签，直接返回null
  if (!isOpen || !canEditCurrentTag) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm">
      <div className="relative w-full max-w-md bg-[var(--card-bg)] rounded-xl shadow-2xl border border-[var(--border-color)]">
        {/* Header */}
        <div className="flex items-center justify-between p-5 border-b border-[var(--border-color)]">
          <h3 className="text-xl font-semibold text-[var(--foreground)]">
            {tag ? t.tagManagement.editTag : t.tagManagement.addNewTag}
          </h3>
          <button
            onClick={handleClose}
            className="p-2 rounded-full text-[var(--muted)] hover:bg-[var(--accent-secondary)] hover:text-[var(--foreground)] transition-colors"
          >
            <FaTimes />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-5 space-y-4">
          {/* 标签名称 */}
          <div>
            <label className="block text-sm font-medium text-[var(--muted)] mb-2">
              {t.tagManagement.tagName} *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="input-base w-full"
              placeholder={t.tagManagement.tagNamePlaceholder}
              maxLength={60}
              required
            />
          </div>


          {/* 标签类型 */}
          {!tag && (
          <div>
            <label className="block text-sm font-medium text-[var(--muted)] mb-2">
              {t.tagManagement.tagType}
            </label>
            {/* 当只有一个选项时显示文字，否则显示下拉框 */}
            {(() => {
              // 检查可用的选项数量
              const availableOptions = [];
              if (canManageSystemTags) {
                availableOptions.push(TAG_TYPE.SYSTEM);
              }
              availableOptions.push(TAG_TYPE.USER);
              
              // 如果只有一个选项，显示文字
              if (availableOptions.length === 1) {
                const optionType = availableOptions[0];
                return (
                  <div className="input-base w-full bg-[var(--muted-bg)] text-[var(--muted)] cursor-not-allowed">
                    {optionType == TAG_TYPE.SYSTEM ? t.tagManagement.systemTag : t.tagManagement.userTag}
                  </div>
                );
              }
              
              // 如果有多个选项，显示下拉框
              return (
                <select
                  value={formData.type}
                  onChange={(e) => setFormData(prev => ({ ...prev, type: parseInt(e.target.value) }))}
                  className="input-base w-full"
                >
                  <option value={TAG_TYPE.SYSTEM} disabled={!canManageSystemTags}>
                    {t.tagManagement.systemTag}
                    {!canManageSystemTags && ' (仅管理员)'}
                  </option>
                  <option value={TAG_TYPE.USER}>{t.tagManagement.userTag}</option>
                </select>
              );
            })()}
            {!canManageSystemTags && (
              <p className="text-xs text-[var(--muted)] mt-1">
                  {t.tagManagement.onlyAdminCanCreateSystemTag}
                </p>
              )}
            </div>
          )}


          {/* 标签颜色 */}
          <div>
            <label className="block text-sm font-medium text-[var(--muted)] mb-2">
              {t.tagManagement.tagColor} *
            </label>
            <div className="flex items-center gap-3">
              <div className="relative">
                <button
                  ref={colorButtonRef}
                  type="button"
                  onClick={() => setShowColorPicker(!showColorPicker)}
                  className="w-12 h-10 rounded-lg border border-[var(--border-color)] flex items-center justify-center hover:border-[var(--accent-primary)] transition-colors"
                  style={{ backgroundColor: formData.color }}
                >
                  <FaPalette className="text-white text-lg" />
                </button>
                
                {showColorPicker && (
                  <div 
                    ref={colorPickerRef}
                    className="absolute top-full left-0 mt-2 p-3 bg-[var(--card-bg)] border border-[var(--border-color)] rounded-lg shadow-2xl z-[9999] min-w-[280px] max-h-[400px] overflow-y-auto"
                    style={{
                      left: '0',
                      right: 'auto',
                      minWidth: '280px'
                    }}
                  >
                    <div className="grid grid-cols-5 gap-2">
                      {PRESET_COLORS.map((color) => (
                        <button
                          key={color}
                          type="button"
                          onClick={() => handleColorSelect(color)}
                          className="w-8 h-8 rounded border-2 border-[var(--border-color)] hover:border-[var(--accent-primary)] hover:scale-110 transition-all duration-200"
                          style={{ backgroundColor: color }}
                          title={color}
                        />
                      ))}
                    </div>
                    <div className="mt-3">
                      <label className="block text-xs text-[var(--muted)] mb-2">
                        {t.tagManagement.customColor}
                      </label>
                      <input
                        type="color"
                        value={formData.color}
                        onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                        className="w-full h-10 rounded border border-[var(--border-color)] cursor-pointer"
                      />
                    </div>
                  </div>
                )}
              </div>
              {/* <input
                type="text"
                disabled={true}
                value={formData.color}
                onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                className="input-base flex-1 font-mono"
                placeholder="#FFFFFF"
                maxLength={7}
                required
              /> */}
            </div>
          </div>

          {/* 标签描述 */}
          <div>
            <label className="block text-sm font-medium text-[var(--muted)] mb-2">
              {t.tagManagement.tagDescription}
            </label>
            <textarea
              value={formData.comments}
              onChange={(e) => setFormData(prev => ({ ...prev, comments: e.target.value }))}
              className="input-base w-full resize-none"
              placeholder={t.tagManagement.tagDescriptionPlaceholder}
              rows={3}
              maxLength={255}
            />
          </div>
        </form>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-5 border-t border-[var(--border-color)]">
          <button
            onClick={handleClose}
            className="btn-secondary"
            disabled={isSubmitting}
          >
            {t.common.cancel}
          </button>
          <button
            onClick={handleSubmit}
            className="btn-primary"
            disabled={isSubmitting || !formData.name.trim()}
          >
            <FaSave className="mr-2" />
            {isSubmitting ? t.settings.saving : t.settings.save}
          </button>
        </div>
      </div>
    </div>
  );
}
