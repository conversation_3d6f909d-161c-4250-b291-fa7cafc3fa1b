import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import rehypeSlug from 'rehype-slug';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/cjs/styles/prism';
import Mermaid from './Mermaid';
import { useLanguage } from '@/contexts/LanguageContext';

interface MarkdownProps {
  content: string;
  className?: string;
  branch?: string;
  repo_url?: string;
  sessionId?: string;
}

const Markdown: React.FC<MarkdownProps> = ({ content, className, branch, repo_url, sessionId }) => {
  const { messages:t } = useLanguage();
  // Define markdown components
  const MarkdownComponents: React.ComponentProps<typeof ReactMarkdown>['components'] = {
    img: ({ src, alt, ...restProps }) => { // 更改为 restProps 以避免与外部 props 混淆
      let finalSrc = src; // 默认使用原始src
      debugger

      // 处理以 /data/workspace/ 开头的图片路径
      if (src && typeof src === 'string' && src.startsWith('/data/workspace/')) {
        // 确保 window.location.origin 在浏览器环境中可用
        // 如果你的应用可能在SSR环境运行，需要额外处理 window 对象不存在的情况
        if (typeof window !== 'undefined') { // 增加一个 window 检查，以防SSR
          // 检查路径是否已经加密（通常加密后的字符串不包含路径分隔符）
          const isEncrypted = !src.includes('/') || src.length > 100; // 简单判断：无路径分隔符或长度异常可能是加密的
          if (isEncrypted) {
            // 如果已经加密，直接使用
            finalSrc = `${window.location.origin}/api/file/image/${sessionId}?file_path=${src}`;
          } else {
            // 如果未加密，进行URL编码
            finalSrc = `${window.location.origin}/api/file/image/${sessionId}?file_path=${encodeURIComponent(src)}`;
          }
        } else {
          // 在非浏览器环境下，可能需要提供一个备用路径或处理逻辑
          console.warn("window object not available, cannot construct dynamic image URL.");
          // finalSrc = `/api/file/image/${sessionId}?file_path=${encodeURIComponent(src)}`; // 仅作为示例，可能不适用于所有SSR场景
        }
      }

        // 统一渲染图片，避免重复代码
        return (
            <img
                src={finalSrc}
                alt={alt}
                className="max-w-full h-auto rounded-md shadow-sm my-4"
                onError={(e) => {
                    // 图片加载失败时隐藏元素
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                }}
                {...restProps} // 传递所有其他props，例如title, width, height, loading等
            />
        );
    },
    p({ children, ...props }: { children?: React.ReactNode }) {
      return <p className="mb-3 text-sm leading-relaxed text-[var(--foreground)]" {...props}>{children}</p>;
    },
    h1({ children, ...props }: { children?: React.ReactNode }) {
      // 不再手动设置id，由rehype-slug插件处理
      return <h1 className="text-xl font-bold mt-6 mb-3 text-[var(--foreground)]" {...props}>{children}</h1>;
    },
    h2({ children, ...props }: { children?: React.ReactNode }) {
      // 不再手动设置id，由rehype-slug插件处理

      // Special styling for ReAct headings
      if (children && typeof children === 'string') {
        const text = children.toString();
        if (text.includes('Thought') || text.includes('Action') || text.includes('Observation') || text.includes('Answer')) {
          return (
            <h2
              className={`text-base font-bold mt-5 mb-3 p-2 rounded ${
                text.includes('Thought') ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300' :
                text.includes('Action') ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' :
                text.includes('Observation') ? 'bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300' :
                text.includes('Answer') ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300' :
                'text-[var(--foreground)]'
              }`}
              {...props}
            >
              {children}
            </h2>
          );
        }
      }
      return <h2 className="text-lg font-bold mt-5 mb-3 text-[var(--foreground)]" {...props}>{children}</h2>;
    },
    h3({ children, ...props }: { children?: React.ReactNode }) {
      // 不再手动设置id，由rehype-slug插件处理
      return <h3 className="text-base font-semibold mt-4 mb-2 text-[var(--foreground)]" {...props}>{children}</h3>;
    },
    h4({ children, ...props }: { children?: React.ReactNode }) {
      // 不再手动设置id，由rehype-slug插件处理
      return <h4 className="text-sm font-semibold mt-3 mb-2 text-[var(--foreground)]" {...props}>{children}</h4>;
    },
    ul({ children, ...props }: { children?: React.ReactNode }) {
      return <ul className="list-disc pl-6 mb-4 text-sm text-[var(--foreground)] space-y-2" {...props}>{children}</ul>;
    },
    ol({ children, ...props }: { children?: React.ReactNode }) {
      return <ol className="list-decimal pl-6 mb-4 text-sm text-[var(--foreground)] space-y-2" {...props}>{children}</ol>;
    },
    li({ children, ...props }: { children?: React.ReactNode }) {
      return <li className="mb-2 text-sm leading-relaxed text-[var(--foreground)]" {...props}>{children}</li>;
    },
    a({ children, href, ...props }: { children?: React.ReactNode; href?: string }) {
      // 处理文件引用链接
      if (href) {
        try {
          // 处理API文件操作链接 (格式: /api/file-action?filePath=...&lines=...&owner=...&repo=...&action=view)
          if (href.includes('/api/file-action') || href.includes('filePath=')) {
            const url = new URL(href.startsWith('http') ? href : `http://example.com${href.startsWith('/') ? '' : '/'}${href}`);
            const filePath = url.searchParams.get('filePath');
            const lines = url.searchParams.get('lines');
            const owner = url.searchParams.get('owner');
            const repo = url.searchParams.get('repo');

            // 构建Git仓库URL + 文件路径的链接
            let gitFileUrl = '';
            if (owner && repo && filePath) {
              if (repo_url) {
                // 直接使用repo_url，移除.git后缀，然后拼接文件路径
                gitFileUrl = `${repo_url.replace('.git', '')}/src/branch/${branch || 'main'}/${filePath}`;
              } else {
                // 如果没有repo_url，使用owner/repo构建GitHub格式
                gitFileUrl = `https://github.com/${owner}/${repo}/blob/${branch || 'main'}/${filePath}`;
              }

              // 添加行号信息到URL
              if (lines) {
                gitFileUrl += `#L${lines}`;
              }
            }

            // 从文件路径中提取文件名
            const fileName = filePath ? filePath.split('/').pop() : (typeof children === 'string' ? children : '文件');

            // 格式化行数显示
            let lineDisplay = '';
            if (lines) {
              lineDisplay = `:${lines}`;
            }

            // 构建显示文本
            const displayText = fileName;
            const locationInfo = owner && repo ? `${owner}/${repo}` : '';

            return (
              <a
                href={gitFileUrl}
                className="inline-flex items-center bg-[var(--accent-secondary)] px-3 py-1.5 rounded-md text-sm my-1 mr-2 border border-[var(--border-color)] hover:bg-[var(--accent-secondary)]/70 transition-colors shadow-sm"
                target="_blank"
                rel="noopener noreferrer"
                title={`${t.components.Markdown.viewFile}: ${filePath}${lineDisplay}${locationInfo ? ` (${locationInfo})` : ''}`}
                {...props}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mr-2 text-[var(--muted)]"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                <span className="font-medium text-[var(--foreground)]">{displayText}</span>
                {lineDisplay && (
                  <span className="ml-1 px-1.5 py-0.5 bg-[var(--background)] text-[var(--muted)] text-xs rounded font-mono border border-[var(--border-color)]">
                    {lineDisplay}
                  </span>
                )}
              </a>
            );
          }

          // 处理其他可能的文件引用格式
          else if (href.includes('=') && (href.includes('.java') || href.includes('.js') || href.includes('.ts') || href.includes('.py') || href.includes('.md'))) {
            // 尝试解析查询参数格式的链接
            const params = new URLSearchParams(href.includes('?') ? href.split('?')[1] : href);
            const filePath = params.get('filePath') || params.get('file');
            const lines = params.get('lines') || params.get('line');
            const owner = params.get('owner');
            const repo = params.get('repo');

            if (filePath && owner && repo) {
              // 构建Git仓库URL + 文件路径的链接
              let gitFileUrl = '';
              if (repo_url) {
                // 直接使用repo_url，移除.git后缀，然后拼接文件路径
                gitFileUrl = `${repo_url.replace('.git', '')}/src/branch/${branch || 'main'}/${filePath}`;
              } else {
                // 如果没有repo_url，使用owner/repo构建GitHub格式
                gitFileUrl = `https://github.com/${owner}/${repo}/blob/${branch || 'main'}/${filePath}`;
              }

              // 添加行号信息到URL
              if (lines) {
                gitFileUrl += `#L${lines}`;
              }

              const fileName = filePath.split('/').pop();
              const lineDisplay = lines ? `:${lines}` : '';

              return (
                <a
                  href={gitFileUrl}
                  className="inline-flex items-center bg-[var(--accent-secondary)] px-3 py-1.5 rounded-md text-sm my-1 mr-2 border border-[var(--border-color)] hover:bg-[var(--accent-secondary)]/70 transition-colors shadow-sm"
                  target="_blank"
                  rel="noopener noreferrer"
                  title={`${t.components.Markdown.viewFile}: ${filePath}${lineDisplay}`}
                  {...props}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 mr-2 text-[var(--muted)]"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                  <span className="font-medium text-[var(--foreground)]">{fileName}</span>
                  {lineDisplay && (
                    <span className="ml-1 px-1.5 py-0.5 bg-[var(--background)] text-[var(--muted)] text-xs rounded font-mono border border-[var(--border-color)]">
                      {lineDisplay}
                    </span>
                  )}
                </a>
              );
            }
          }
        } catch (error) {
          console.error('Error parsing file reference URL:', error);
        }

        // 处理可能是文件引用的其他链接
        if (
          (typeof children === 'string' &&
            (children.endsWith('.java') ||
             children.endsWith('.xml') ||
             children.endsWith('.properties') ||
             children.endsWith('.md') ||
             children.endsWith('.js') ||
             children.endsWith('.ts') ||
             children.endsWith('.tsx') ||
             children.endsWith('.css') ||
             children.endsWith('.json') ||
             children.endsWith('.py') ||
             children.endsWith('.c') ||
             children.endsWith('.cpp') ||
             children.endsWith('.h'))) ||
          (href.endsWith('.java') ||
           href.endsWith('.xml') ||
           href.endsWith('.properties') ||
           href.endsWith('.md') ||
           href.endsWith('.js') ||
           href.endsWith('.ts') ||
           href.endsWith('.tsx') ||
           href.endsWith('.css') ||
           href.endsWith('.json') ||
           href.endsWith('.py') ||
           href.endsWith('.c') ||
           href.endsWith('.cpp') ||
           href.endsWith('.h'))
        ) {
          // 提取文件名
          const fileName = href.split('/').pop() || (typeof children === 'string' ? children : '文件');

          // 检查是否包含行号信息 (格式如 file.java:10-20)
          let lineInfo = '';
          const fileNameParts = fileName.split(':');
          if (fileNameParts.length > 1) {
            lineInfo = `:${fileNameParts.slice(1).join(':')}`;
          }

          // 构建Git仓库URL + 文件路径的链接
          let gitFileUrl = '';
          if (repo_url) {
            // 直接使用repo_url，移除.git后缀，然后拼接文件路径
            gitFileUrl = `${repo_url.replace('.git', '')}/src/branch/${branch || 'main'}/${fileNameParts[0]}`;

            // 添加行号信息到URL
            if (lineInfo) {
              gitFileUrl += `#L${lineInfo.replace(':', '')}`;
            }
          }

          return (
            <a
              href={gitFileUrl || href}
              className="inline-flex items-center bg-[var(--accent-secondary)] px-3 py-1.5 rounded-md text-sm my-1 mr-2 border border-[var(--border-color)] hover:bg-[var(--accent-secondary)]/70 transition-colors shadow-sm"
              target="_blank"
              rel="noopener noreferrer"
              title={`${t.components.Markdown.viewFile}: ${fileName}`}
              {...props}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 mr-2 text-[var(--muted)]"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <span className="font-medium text-[var(--foreground)]">{fileNameParts[0]}</span>
              {lineInfo && (
                <span className="ml-1 px-1.5 py-0.5 bg-[var(--background)] text-[var(--muted)] text-xs rounded font-mono border border-[var(--border-color)]">
                  {lineInfo}
                </span>
              )}
            </a>
          );
        }
      }

      // 普通链接渲染
      return (
        <a
          href={href}
          className="text-[var(--link-color)] hover:underline font-medium"
          target="_blank"
          rel="noopener noreferrer"
          {...props}
        >
          {children}
        </a>
      );
    },
    blockquote({ children, ...props }: { children?: React.ReactNode }) {
      return (
        <blockquote
          className="border-l-4 border-[var(--border-color)] pl-4 py-1 text-[var(--muted)] italic my-4 text-sm"
          {...props}
        >
          {children}
        </blockquote>
      );
    },
    table({ children, ...props }: { children?: React.ReactNode }) {
      return (
        <div className="overflow-x-auto my-6 rounded-md">
          <table className="min-w-full text-sm border-collapse" {...props}>
            {children}
          </table>
        </div>
      );
    },
    thead({ children, ...props }: { children?: React.ReactNode }) {
      return <thead className="bg-[var(--accent-secondary)]" {...props}>{children}</thead>;
    },
    tbody({ children, ...props }: { children?: React.ReactNode }) {
      return <tbody className="divide-y divide-[var(--border-color)]" {...props}>{children}</tbody>;
    },
    tr({ children, ...props }: { children?: React.ReactNode }) {
      return <tr className="hover:bg-[var(--background)]/50" {...props}>{children}</tr>;
    },
    th({ children, ...props }: { children?: React.ReactNode }) {
      return (
        <th
          className="px-4 py-3 text-left font-medium text-[var(--foreground)]"
          {...props}
        >
          {children}
        </th>
      );
    },
    td({ children, ...props }: { children?: React.ReactNode }) {
      return <td className="px-4 py-3 border-t border-[var(--border-color)]" {...props}>{children}</td>;
    },
    code(props: {
      inline?: boolean;
      className?: string;
      children?: React.ReactNode;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      [key: string]: any; // Using any here as it's required for ReactMarkdown components
    }) {
      const { inline, className, children, ...otherProps } = props;
      const match = /language-(\w+)/.exec(className || '');
      const codeContent = children ? String(children).replace(/\n$/, '') : '';

      // Handle Mermaid diagrams
      if (!inline && match && match[1] === 'mermaid') {
        return (
          <div className="my-8 bg-[var(--accent-secondary)]/50 rounded-md overflow-hidden shadow-sm">
            <Mermaid
              chart={codeContent}
              className="w-full max-w-full"
              zoomingEnabled={false}
            />
          </div>
        );
      }

      // Handle code blocks
      if (!inline && match) {
        return (
          <div className="my-6 rounded-md overflow-hidden text-sm shadow-sm border border-[var(--border-color)]">
            <div className="bg-gray-800 text-gray-200 px-5 py-2 text-sm flex justify-between items-center">
              <span className="font-medium">{match[1]}</span>
              <button
                onClick={() => {
                  navigator.clipboard.writeText(codeContent);
                }}
                className="text-gray-400 hover:text-white transition-colors"
                title={t.components.Markdown.copyCode}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </svg>
              </button>
            </div>
            <SyntaxHighlighter
              language={match[1]}
              style={tomorrow}
              className="!text-sm"
              customStyle={{
                margin: 0,
                borderRadius: '0 0 0.375rem 0.375rem',
                padding: '1rem',
                fontSize: '0.875rem',
                lineHeight: '1.5'
              }}
              showLineNumbers={true}
              wrapLines={true}
              wrapLongLines={true}
              lineNumberStyle={{
                minWidth: '3em',
                paddingRight: '1em',
                textAlign: 'right',
                userSelect: 'none',
                color: '#6b7280',
                fontSize: '0.75rem'
              }}
              {...otherProps}
            >
              {codeContent}
            </SyntaxHighlighter>
          </div>
        );
      }

      // 修改行内代码块样式
      return (
        <code
          className={`${className} font-mono inline-block bg-[var(--accent-secondary)]/60 px-2 py-0.5 rounded text-sm font-medium text-[var(--foreground)] border border-[var(--border-color)]/30`}
          {...otherProps}
        >
          {children}
        </code>
      );
    },
  };

  // const options = {
  //   remarkPlugins: [remarkGfm],
  //   rehypePlugins: [rehypeRaw, rehypeSlug],  // 确保rehypeSlug正确添加
  // };

  return (
    <div className={`prose dark:prose-invert max-w-none ${className}`}>
      <ReactMarkdown
        components={MarkdownComponents}
        rehypePlugins={[rehypeRaw, rehypeSlug]}  // 直接在这里也添加插件，确保被应用
        remarkPlugins={[remarkGfm]}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

export default Markdown;
