'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { FaExclamationTriangle } from 'react-icons/fa';

export interface PopconfirmProps {
  title: string;
  description?: string;
  onConfirm: () => void | Promise<void>;
  onCancel?: () => void | Promise<void>;
  confirmText?: string;
  cancelText?: string;
  placement?: 'top' | 'bottom' | 'left' | 'right';
  children: React.ReactNode;
  disabled?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

const Popconfirm: React.FC<PopconfirmProps> = (props: PopconfirmProps) => {
  const { messages } = useLanguage();
  const {
    title,
    description,
    onConfirm,
    onCancel,
    confirmText = messages.user.confirm,
    cancelText = messages.common.cancel,
    placement = 'top',
    children,
    disabled = false,
    className = '',
    style = {},
  } = props;
  const [isVisible, setIsVisible] = useState(false);
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);
  const [popupPosition, setPopupPosition] = useState<{ top: number; left: number }>({ top: 0, left: 0 });
  const [isReady, setIsReady] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const triggerRef = useRef<HTMLDivElement>(null);
  const popupRef = useRef<HTMLDivElement>(null);
  const isTriggeredByClick = useRef(false);

  // 创建 Portal 容器
  useEffect(() => {
    if (isVisible && typeof window !== 'undefined') {
      const container = document.createElement('div');
      container.style.position = 'fixed';
      container.style.top = '0';
      container.style.left = '0';
      container.style.width = '100%';
      container.style.height = '100%';
      container.style.pointerEvents = 'none';
      container.style.zIndex = '9999';
      document.body.appendChild(container);
      setPortalContainer(container);

      return () => {
        if (container.parentNode) {
          container.parentNode.removeChild(container);
        }
      };
    }
  }, [isVisible]);

  // 计算弹窗位置
  const calculatePosition = useCallback(() => {
    if (!triggerRef.current || !portalContainer || !popupRef.current) return;

    // 获取触发元素的位置和尺寸
    const triggerRect = triggerRef.current.getBoundingClientRect();
    
    // 获取弹窗的实际尺寸
    const popupRect = popupRef.current.getBoundingClientRect();
    const popupWidth = popupRect.width;
    const popupHeight = popupRect.height;
    
    // 弹窗与触发元素的间距
    const offset = 8; // 设置为0，让箭头直接贴着按钮
    
    let top = 0;
    let left = 0;

    switch (placement) {
      case 'top':
        top = triggerRect.top - popupHeight - offset;
        left = triggerRect.left + (triggerRect.width / 2) - (popupWidth / 2);
        break;
      case 'bottom':
        top = triggerRect.bottom + offset;
        left = triggerRect.left + (triggerRect.width / 2) - (popupWidth / 2);
        break;
      case 'left':
        top = triggerRect.top + (triggerRect.height / 2) - (popupHeight / 2);
        left = triggerRect.left - popupWidth - offset;
        break;
      case 'right':
        top = triggerRect.top + (triggerRect.height / 2) - (popupHeight / 2);
        left = triggerRect.right + offset;
        break;
    }

    // 确保弹窗不超出视窗边界
    if (left < 10) {
      left = 10;
    } else if (left + popupWidth > window.innerWidth - 10) {
      left = window.innerWidth - popupWidth - 10;
    }
    
    if (top < 10) {
      top = 10;
    } else if (top + popupHeight > window.innerHeight - 10) {
      top = window.innerHeight - popupHeight - 10;
    }

    setPopupPosition({ top, left });
    setIsReady(true);
  }, [placement, portalContainer]);

  // 监听窗口大小变化和滚动
  useEffect(() => {
    if (!isVisible) return;

    const handleResize = () => {
      calculatePosition();
    };
    const handleScroll = () => {
      calculatePosition();
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleScroll, true);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleScroll, true);
    };
  }, [isVisible, calculatePosition]);

  // 初始位置计算
  useEffect(() => {
    if (isVisible && portalContainer && popupRef.current) {
      // 使用 setTimeout 确保弹窗已渲染
      setTimeout(() => {
        calculatePosition();
      }, 0);
    }
  }, [isVisible, portalContainer, calculatePosition]);

  // 点击外部关闭
  useEffect(() => {
    if (!isVisible) return;

    const handleClickOutside = (event: MouseEvent) => {
      // 如果是触发器点击引起的事件，忽略
      if (isTriggeredByClick.current) {
        return;
      }
      
      const target = event.target as Node;
      
      // 确保所有 refs 存在
      if (!triggerRef.current || !popupRef.current) return;
      
      // 如果点击的是触发元素或弹窗内部，不关闭
      if (
        triggerRef.current.contains(target) ||
        popupRef.current.contains(target)
      ) {
        return;
      }
      

      setIsVisible(false);
      setIsReady(false);
    };

    // 使用 click 事件而不是 mousedown，避免干扰按钮点击
    setTimeout(() => {
      document.addEventListener('click', handleClickOutside);
    }, 100); // 增加延迟时间
    
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [isVisible]);

  const handleTriggerClick = (e: React.MouseEvent) => {
    if (!disabled) {
      e.stopPropagation(); // 阻止冒泡
      
      // 设置标记，表示这是触发器点击
      isTriggeredByClick.current = true;
      
      // 切换显示状态
      setIsVisible(!isVisible);
      setIsReady(false);
      
      // 清除标记，避免影响后续的外部点击检测
      setTimeout(() => {
        isTriggeredByClick.current = false;
      }, 0);
    }
  };

  const handleConfirm = useCallback(async (e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡
    e.preventDefault(); // 阻止默认行为
    
    // 阻止事件继续传播到父级元素
    e.nativeEvent.stopImmediatePropagation();
    
    setIsLoading(true);
    
    // 先执行回调函数，再更新状态
    try {
      const result = onConfirm();
      // 如果回调函数返回 Promise，等待它完成
      if (result && typeof result.then === 'function') {
        await result;
      }
    } catch (error) {
      console.error('Error in onConfirm callback:', error);
    } finally {
      setIsLoading(false);
      // 确保回调函数完全执行后再更新状态
      setIsVisible(false);
      setIsReady(false);
    }
  }, [onConfirm]);

  const handleCancel = useCallback(async (e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡
    e.preventDefault(); // 阻止默认行为
    
    // 阻止事件继续传播到父级元素
    e.nativeEvent.stopImmediatePropagation();
    
    // 先执行回调函数，再更新状态
    try {
      const result = onCancel?.();
      // 如果回调函数返回 Promise，等待它完成
      if (result && typeof result.then === 'function') {
        await result;
      }
    } catch (error) {
      console.error('Error in onCancel callback:', error);
    }
    
    // 确保回调函数完全执行后再更新状态
    setIsVisible(false);
    setIsReady(false);
  }, [onCancel]);

  // 获取箭头样式
  const getArrowStyle = (): React.CSSProperties => {
    const style: React.CSSProperties = {
      position: 'absolute',
      width: 0,
      height: 0,
      border: '8px solid transparent',
    };

    // 检测当前主题，动态设置箭头颜色
    const isDark = document.documentElement.classList.contains('dark');
    const arrowColor = isDark ? '#374151' : '#ffffff'; // dark:border-gray-700 : white

    switch (placement) {
      case 'top':
        style.bottom = '-16px';
        style.left = '50%';
        style.transform = 'translateX(-50%)';
        style.borderTopColor = arrowColor;
        break;
      case 'bottom':
        style.top = '-16px';
        style.left = '50%';
        style.transform = 'translateX(-50%)';
        style.borderBottomColor = arrowColor;
        break;
      case 'left':
        style.right = '-16px';
        style.top = '50%';
        style.transform = 'translateY(-50%)';
        style.borderLeftColor = arrowColor;
        break;
      case 'right':
        style.left = '-16px';
        style.top = '50%';
        style.transform = 'translateY(-50%)';
        style.borderRightColor = arrowColor;
        break;
    }

    return style;
  };

  // 渲染弹窗内容
  const renderPopup = () => {
    if (!portalContainer || !isVisible) return null;

    const popupStyle: React.CSSProperties = {
      position: 'fixed',
      top: popupPosition.top,
      left: popupPosition.left,
      pointerEvents: 'auto',
      zIndex: 9999,
      opacity: isReady ? 1 : 0,
      transition: 'opacity 0.1s ease-out',
      visibility: isReady ? 'visible' : 'hidden',
      ...style,
    };

    return createPortal(
      <div style={popupStyle} ref={popupRef}>
        {/* Arrow */}
        <div style={getArrowStyle()} />
        
        {/* Popup Content */}
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 min-w-64">
          {/* Header */}
          <div className="flex items-start mb-3">
            <FaExclamationTriangle className="flex-shrink-0 w-5 h-5 text-yellow-500 mt-0.5" />
            <div className="ml-2 flex-1">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                {title}
              </h4>
              {description && (
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-300">
                  {description}
                </p>
              )}
            </div>
          </div>
          
          {/* Actions */}
          <div className="flex justify-end space-x-2">
            <button
              onClick={handleCancel}
              onMouseDown={(e) => e.stopPropagation()}
              onMouseUp={(e) => e.stopPropagation()}
              className="px-3 py-1.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500 rounded-md transition-all duration-200"
            >
              {cancelText}
            </button>
            <button
              onClick={handleConfirm}
              onMouseDown={(e) => e.stopPropagation()}
              onMouseUp={(e) => e.stopPropagation()}
              disabled={isLoading}
              className="px-3 py-1.5 text-sm font-medium text-white bg-[#2C7FFF] hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed rounded-md transition-colors duration-200 flex items-center space-x-1"
            >
              {isLoading && (
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              )}
              <span>{isLoading ? '处理中...' : confirmText}</span>
            </button>
          </div>
        </div>
      </div>,
      portalContainer
    );
  };

  return (
    <div className={`relative inline-block ${className}`} ref={triggerRef} onClick={handleTriggerClick}>
      {children}
      {renderPopup()}
    </div>
  );
};

export default Popconfirm;
