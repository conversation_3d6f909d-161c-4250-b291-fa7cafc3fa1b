import { useToast } from "@/contexts/ToastContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { authFetch } from "@/utils/authFetch";
import React, { useEffect, useState } from "react";
import { FaTimes } from "react-icons/fa";
import { HiOutlineBookOpen } from "react-icons/hi";
import Popconfirm from "./Popconfirm";
import WikiBinding from "./WikiBinding";

interface AppWikiProps {
  app_primary_key: number;
  onClose: () => void;
}

interface WikiItem {
  id: number;
  wiki_id: string;
  name?: string;
  description?: string;
  repo_owner?: string;
  repo_name?: string;
  branch?: string;
}

const AppWiki = ({ app_primary_key, onClose }: AppWikiProps) => {
  const { addToast } = useToast();
  const { messages } = useLanguage();
  const [wikiList, setWikiList] = useState<WikiItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [showBinding, setShowBinding] = useState<boolean>(false);

  const fetchWikiList = async () => {
    setLoading(true);
    try {
      const response = await authFetch(`/api/apps/${app_primary_key}/wikis`);
      if (response && response.ok) {
        const data = await response.json();
        setWikiList(data || []);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchWikiList();
  }, []);

  const handleUnbind = async (wikiId: number) => {
    const remainingIds = wikiList.filter((item) => item.id !== wikiId).map((item) => item.id);
    const response = await authFetch(`/api/apps/${app_primary_key}/wikis`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ wiki_list: remainingIds }),
    });
    if (response && response.ok) {
      addToast({
        type: "success",
        title: messages.authApp.unbindWiki,
        message: messages.authApp.unbindWikiSuccess,
      });
      fetchWikiList();
    } else {
      addToast({
        type: "error",
        title: messages.authApp.unbindWiki,
        message: messages.authApp.unbindWikiFail,
      });
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 bg-opacity-100 flex items-center justify-center z-50 p-4 backdrop-blur-sm text-start">
      <div className="bg-[var(--card-bg)] rounded-lg shadow-2xl border border-[var(--border-color)] w-full max-w-4xl max-h-[95vh] overflow-hidden flex flex-col">
        <div className="p-6 border-b border-[var(--border-color)] flex items-center justify-between">
          <div className="flex items-center gap-2">
            <HiOutlineBookOpen className="text-[var(--accent-primary)]" size={20} />
            <h3 className="text-lg font-semibold text-[var(--foreground)]">
              {messages.authApp.wikiManagement}
            </h3>
          </div>
          <button
            onClick={onClose}
            className="text-[var(--muted)] hover:text-[var(--foreground)] transition-colors"
          >
            <FaTimes size={16} />
          </button>
        </div>

        <div className="p-6 flex-1 overflow-y-auto">
          <div className="flex justify-between items-center mb-4">
            <p className="text-sm text-[var(--muted)]">
              {messages.authApp.wikiBindingHint}
            </p>
            <div className="flex items-center gap-2">
              <button
                className="px-3 py-1.5 text-sm font-medium rounded-md border border-[var(--border-color)] text-[var(--accent-primary)] hover:bg-[var(--accent-primary)]/10 transition-colors cursor-pointer"
                onClick={() => fetchWikiList()}
              >
                {messages.authApp.refresh}
              </button>
              <button
                className="px-3 py-1.5 text-sm font-medium rounded-md border border-transparent bg-[var(--accent-primary)]/90 text-white hover:bg-[var(--accent-primary)] transition-colors cursor-pointer"
                onClick={() => setShowBinding(true)}
              >
                {messages.authApp.bindWiki}
              </button>
            </div>
          </div>

          <div className="border border-[var(--border-color)] rounded-lg overflow-hidden">
            <table className="w-full text-sm">
              <thead className="bg-[var(--background)]/60 border-b border-[var(--border-color)] text-[var(--muted)]">
                <tr>
                  <th className="px-4 py-2 text-left font-medium w-48">
                    {messages.authApp.wikiName}
                  </th>
                  <th className="px-4 py-2 text-left font-medium">
                    {messages.authApp.wikiRepo}
                  </th>
                  <th className="px-4 py-2 text-left font-medium w-32">
                    {messages.authApp.wikiBranch}
                  </th>
                  <th className="px-4 py-2 text-left font-medium w-28">
                    {messages.authApp.operation}
                  </th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan={4} className="px-4 py-6 text-center text-[var(--muted)]">
                      {messages.common.loading}
                    </td>
                  </tr>
                ) : wikiList.length ? (
                  wikiList.map((wiki) => (
                    <tr key={wiki.id} className="border-b border-[var(--border-color)]">
                      <td className="px-4 py-2">
                        <div className="flex flex-col">
                          <span className="font-medium text-[var(--foreground)]">
                            {wiki.name || wiki.repo_name || wiki.wiki_id}
                          </span>
                          {wiki.description ? (
                            <span className="text-xs text-[var(--muted)] truncate">
                              {wiki.description}
                            </span>
                          ) : null}
                        </div>
                      </td>
                      <td className="px-4 py-2">
                        <span className="text-[var(--foreground)]">
                          {wiki.repo_owner ? `${wiki.repo_owner}/${wiki.repo_name}` : wiki.repo_name || "-"}
                        </span>
                      </td>
                      <td className="px-4 py-2 text-[var(--foreground)]">
                        {wiki.branch || "main"}
                      </td>
                      <td className="px-4 py-2">
                        <Popconfirm
                          title={messages.authApp.unbindWiki}
                          description={messages.authApp.confirmUnbindWiki}
                          onConfirm={() => handleUnbind(wiki.id)}
                          onCancel={() => undefined}
                        >
                          <button className="text-red-500 hover:text-red-600 text-sm">
                            {messages.authApp.unbindWiki}
                          </button>
                        </Popconfirm>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={4} className="px-4 py-6 text-center text-[var(--muted)]">
                      {messages.authApp.noWikiData}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {showBinding && (
        <WikiBinding
          app_primary_key={app_primary_key}
          onClose={() => setShowBinding(false)}
          onFinish={() => {
            setShowBinding(false);
            fetchWikiList();
          }}
        />
      )}
    </div>
  );
};

export default AppWiki;
