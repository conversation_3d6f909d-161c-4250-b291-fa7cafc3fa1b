uv run -m api.main#!/bin/bash

# =============================================================================
# DeepWiki 容器启动脚本
# =============================================================================

set -e  # 遇到错误时退出

# 颜色定义（用于日志输出）
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# =============================================================================
# 环境变量加载和验证
# =============================================================================

log_info "启动 DeepWiki 应用..."

# 加载环境变量文件（如果存在）
if [ -f .env ]; then
    log_info "加载 .env 文件中的环境变量"
    export $(grep -v "^#" .env | xargs -r)
else
    log_info "未找到 .env 文件，使用默认环境变量"
fi

# =============================================================================
# 环境变量信息打印
# =============================================================================

log_info "=== 环境变量配置 ==="
echo "NODE_ENV: ${NODE_ENV:-未设置}"
echo "PORT: ${PORT:-8001}"
echo "SERVER_BASE_URL: ${SERVER_BASE_URL:-未设置}"
echo "NEXT_PUBLIC_SERVER_BASE_URL: ${NEXT_PUBLIC_SERVER_BASE_URL:-未设置}"
echo "PYTHONUNBUFFERED: ${PYTHONUNBUFFERED:-未设置}"

# API密钥检查（不打印实际值，只显示是否设置）
echo -n "OPENAI_API_KEY: "
if [ -n "$OPENAI_API_KEY" ]; then
    echo "✓ 已设置 (${#OPENAI_API_KEY} 字符)"
else
    echo "✗ 未设置"
fi

echo "=========================="

# =============================================================================
# 必需环境变量检查
# =============================================================================

MISSING_VARS=()

if [ -z "$OPENAI_API_KEY" ]; then
    MISSING_VARS+=("OPENAI_API_KEY")
fi

if [ ${#MISSING_VARS[@]} -gt 0 ]; then
    log_warn "以下环境变量未设置: ${MISSING_VARS[*]}"
    log_warn "这些变量对于 DeepWiki 正常运行是必需的"
    log_warn "您可以通过挂载 .env 文件或在运行容器时设置环境变量来提供这些值"
    echo
fi

# =============================================================================
# 服务启动
# =============================================================================

log_info "启动后端 API 服务器（端口: ${PORT:-8001}）"

# 使用Gunicorn启动API服务器
# 设置工作进程数量，根据CPU核心数或设置一个合理的值
WORKERS=${API_WORKERS:-4}
python -m api.main --port ${PORT:-8001} &
API_PID=$!

# 等待后端服务启动
sleep 2

if kill -0 $API_PID 2>/dev/null; then
    log_success "后端 API 服务器启动成功 (PID: $API_PID)"
else
    log_error "后端 API 服务器启动失败"
    exit 1
fi

log_info "启动前端服务器（端口: 3000）"
PORT=3000 HOSTNAME=0.0.0.0 node server.js &
FRONTEND_PID=$!

# 等待前端服务启动
sleep 2

if kill -0 $FRONTEND_PID 2>/dev/null; then
    log_success "前端服务器启动成功 (PID: $FRONTEND_PID)"
else
    log_error "前端服务器启动失败"
    kill $API_PID 2>/dev/null || true
    exit 1
fi

log_success "DeepWiki 应用启动完成！"
log_info "前端访问地址: http://localhost:3000"
log_info "后端 API 地址: http://localhost:${PORT:-8001}"

# =============================================================================
# 进程监控和清理
# =============================================================================

# 清理函数
cleanup() {
    log_info "正在停止服务..."
    kill $API_PID $FRONTEND_PID 2>/dev/null || true
    wait $API_PID $FRONTEND_PID 2>/dev/null || true
    log_info "服务已停止"
    exit 0
}

# 设置信号处理
trap cleanup SIGTERM SIGINT

# 等待任一进程退出
wait -n

# 如果到达这里，说明有进程异常退出
log_error "检测到服务异常退出"
cleanup