"""Standalone FastAPI application exposing wiki sync APIs."""
from __future__ import annotations

import logging
from typing import Optional

from fastapi import Fast<PERSON><PERSON>, HTTPException, Body
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

from api.cache.redis.manager import redis_manager
from api.wiki.sync_index_service import get_sync_index_service

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

app = FastAPI(title="DeepWiki Sync Worker", version="0.1.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class SyncRequest(BaseModel):
    strategy: str = "main_topic"
    user_id: Optional[int] = None


class CancelRequest(BaseModel):
    job_id: Optional[str] = None
    reason: Optional[str] = None


@app.on_event("startup")
async def startup_components() -> None:
    await redis_manager.startup()
    service = get_sync_index_service()
    await service.job_manager.start()


@app.on_event("shutdown")
async def shutdown_components() -> None:
    service = get_sync_index_service()
    try:
        await service.job_manager.stop()
    except Exception as exc:  # pragma: no cover
        logger.warning("Failed to stop sync job manager cleanly: %s", exc)
    await redis_manager.shutdown()


@app.get("/healthz", tags=["health"])
async def healthcheck() -> dict[str, str]:
    service = get_sync_index_service()
    status = "running" if service.job_manager else "not-initialised"
    return {"status": "ok", "job_manager": status}


@app.get("/wiki/{wiki_id}/index-progress", tags=["sync"])
async def get_index_progress(wiki_id: str) -> dict:
    service = get_sync_index_service()
    try:
        return service.calculate_index_progress(wiki_id)
    except Exception as exc:
        logger.exception("Failed to calculate index progress for %s", wiki_id)
        raise HTTPException(status_code=500, detail=str(exc)) from exc


@app.post("/wiki/{wiki_id}/sync-index", tags=["sync"])
async def trigger_index_sync(wiki_id: str, payload: SyncRequest) -> dict:
    service = get_sync_index_service()
    try:
        return await service.start_sync_index(wiki_id, payload.strategy, payload.user_id)
    except ValueError as exc:
        raise HTTPException(status_code=404, detail=str(exc)) from exc
    except Exception as exc:  # pragma: no cover
        logger.exception("Failed to start sync index for %s", wiki_id)
        raise HTTPException(status_code=500, detail=str(exc)) from exc


@app.get("/wiki/{wiki_id}/sync-job/{job_id}", tags=["sync"])
async def get_sync_job_status(wiki_id: str, job_id: str) -> dict:
    service = get_sync_index_service()
    try:
        status = service.get_sync_job_status(job_id)
        if not status:
            raise HTTPException(status_code=404, detail="job not found")
        return status
    except HTTPException:
        raise
    except Exception as exc:
        logger.exception("Failed to fetch job status %s for %s", job_id, wiki_id)
        raise HTTPException(status_code=500, detail=str(exc)) from exc


@app.get("/wiki/{wiki_id}/sync-job/latest", tags=["sync"])
async def get_latest_job(wiki_id: str) -> dict:
    service = get_sync_index_service()
    job_id = service._get_latest_job_id(wiki_id)  # pylint: disable=protected-access
    if not job_id:
        raise HTTPException(status_code=404, detail="no job")
    status = service.get_sync_job_status(job_id)
    return {"job_id": job_id, "status": status}


@app.get("/sync/overview", tags=["sync"])
async def get_sync_overview() -> dict:
    """返回同步队列与运行中任务的整体视图。"""
    service = get_sync_index_service()
    return service.get_queue_snapshot()


@app.post("/wiki/{wiki_id}/sync-job/{job_id}/cancel", tags=["sync"])
async def cancel_sync_job(wiki_id: str, job_id: str, payload: CancelRequest = Body(default_factory=CancelRequest)) -> dict:
    service = get_sync_index_service()
    try:
        return await service.cancel_sync_job(wiki_id, job_id, payload.reason)
    except ValueError as exc:
        raise HTTPException(status_code=404, detail=str(exc)) from exc
    except Exception as exc:
        logger.exception("Failed to cancel job %s for %s", job_id, wiki_id)
        raise HTTPException(status_code=500, detail=str(exc)) from exc


@app.post("/wiki/{wiki_id}/sync-index/cancel", tags=["sync"])
async def cancel_latest_job(wiki_id: str, payload: CancelRequest = Body(default_factory=CancelRequest)) -> dict:
    service = get_sync_index_service()
    try:
        return await service.cancel_sync_job(wiki_id, payload.job_id, payload.reason)
    except ValueError as exc:
        raise HTTPException(status_code=404, detail=str(exc)) from exc
    except Exception as exc:
        logger.exception("Failed to cancel latest job for %s", wiki_id)
        raise HTTPException(status_code=500, detail=str(exc)) from exc


if __name__ == "__main__":  # pragma: no cover
    import uvicorn

    uvicorn.run("sync_worker.main:app", host="0.0.0.0", port=8011, reload=False)
