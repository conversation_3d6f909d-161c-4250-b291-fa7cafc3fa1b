FROM hub-nj.iwhalecloud.com/ptdev01/python3.12-node20-uv-base:v2 AS builder

ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    UV_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/ \
    UV_TRUSTED_HOST=mirrors.aliyun.com,pypi.org,files.pythonhosted.org \
    PYTHONHTTPSVERIFY=0

WORKDIR /app

RUN apt-get update \
    && apt-get install -y --no-install-recommends git build-essential \
    && rm -rf /var/lib/apt/lists/*

COPY pyproject.toml uv.lock ./
COPY api ./api
COPY sync_worker ./sync_worker

RUN uv sync -i https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com

FROM hub-nj.iwhalecloud.com/ptdev01/python3.12-node20-uv-base:v2

ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1

WORKDIR /app

COPY --from=builder /app/.venv /app/.venv
ENV PATH="/app/.venv/bin:$PATH"

COPY api ./api
COPY sync_worker ./sync_worker

ENV PYTHONPATH=/app

EXPOSE 8001

CMD ["uvicorn", "sync_worker.main:app", "--host", "0.0.0.0", "--port", "8001"]
