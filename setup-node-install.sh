#!/bin/bash

# Node.js 和 npm 依赖安装脚本
# 用于解决 Node.js 版本兼容性问题

set -e  # 遇到错误立即退出

echo "=== Node.js 环境设置脚本 ==="

# 检查是否已安装 nvm
if ! command -v nvm &> /dev/null; then
    echo "正在安装 nvm..."
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
    
    # 重新加载 nvm
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
    
    echo "nvm 安装完成"
else
    echo "nvm 已安装"
fi

# 安装并使用 Node.js 20
echo "正在安装 Node.js 20..."
nvm install 20
nvm use 20

# 验证 Node.js 版本
NODE_VERSION=$(node --version)
NPM_VERSION=$(npm --version)

echo "Node.js 版本: $NODE_VERSION"
echo "npm 版本: $NPM_VERSION"

# 检查版本是否符合要求
if [[ "$NODE_VERSION" =~ ^v2[012]\. ]]; then
    echo "✅ Node.js 版本检查通过"
else
    echo "❌ Node.js 版本不符合要求 (需要 20+)"
    exit 1
fi

# 清理可能的缓存和旧依赖
echo "正在清理缓存..."
npm cache clean --force

# 删除 node_modules 和 package-lock.json（如果存在）
if [ -d "node_modules" ]; then
    echo "正在删除旧的 node_modules..."
    rm -rf node_modules
fi

if [ -f "package-lock.json" ]; then
    echo "正在删除旧的 package-lock.json..."
    rm -f package-lock.json
fi

# 安装依赖
echo "正在安装 npm 依赖..."
npm install