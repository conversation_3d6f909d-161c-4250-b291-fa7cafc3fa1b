-- ai_dw_api_def definition

CREATE TABLE `ai_dw_api_def` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `api_code` varchar(30) NOT NULL COMMENT 'API编码，全局唯一',
  `api_name` varchar(60) NOT NULL COMMENT 'API名称',
  `api_path` varchar(60) NOT NULL COMMENT 'API路径',
  `api_method` varchar(20) NOT NULL COMMENT 'HTTP方法：GET/POST/PUT/DELETE',
  `comments` varchar(255) DEFAULT NULL COMMENT 'API描述',
  `category` varchar(30) DEFAULT NULL COMMENT 'API分类',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_api_code` (`api_code`),
  KEY `idx_adad_state` (`state`)
) COMMENT='API接口定义表';


-- ai_dw_app definition

CREATE TABLE `ai_dw_app` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` varchar(60) NOT NULL COMMENT '应用ID，全局唯一',
  `app_secret` varchar(120) NOT NULL COMMENT '应用密钥(加密存储)',
  `app_code` varchar(60) NOT NULL COMMENT '应用编码，全局唯一',
  `app_name` varchar(60) NOT NULL COMMENT '应用名称',
  `comments` varchar(255) DEFAULT NULL COMMENT '应用描述',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '创建人工号',
  `created_date` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '更新人工号',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_ada_app_id` (`app_id`),
  UNIQUE KEY `uk_ada_app_code` (`app_code`),
  KEY `idx_ada_state` (`state`)
) COMMENT='应用信息表';


-- ai_dw_app_access_token definition

CREATE TABLE `ai_dw_app_access_token` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `token` varchar(255) NOT NULL COMMENT 'Access Token',
  `app_id` int(10) unsigned NOT NULL COMMENT '应用ID',
  `token_type` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT 'Token类型：1-系统级，2-用户级 用户级要求调用接口时必须携带用户信息',
  `effective_at` datetime DEFAULT NULL COMMENT 'token生效时间，为空表示立即生效',
  `expires_at` datetime DEFAULT NULL COMMENT '过期时间，NULL表示永久有效',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：0-无效，1-有效',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录创建者id',
  `created_date` datetime NOT NULL COMMENT '记录创建时间',
  `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者ID',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_adat_token` (`token`),
  KEY `idx_asat_app_id` (`app_id`)
) COMMENT='Token管理表';


-- ai_dw_app_api_rel definition

CREATE TABLE `ai_dw_app_api_rel` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` int(10) unsigned NOT NULL COMMENT '应用ID',
  `api_id` int(10) unsigned NOT NULL COMMENT 'API ID',
  `state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '授权人ID',
  `created_date` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者ID',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_api` (`app_id`,`api_id`)
) COMMENT='应用API权限关联表';


-- ai_dw_app_approval_records definition

CREATE TABLE `ai_dw_app_approval_records` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` int(10) unsigned NOT NULL COMMENT '应用主键ID',
  `applicant_id` bigint(20) unsigned NOT NULL COMMENT '申请人主键标识',
  `approver_id` bigint(20) unsigned DEFAULT NULL COMMENT '审批人主键标识',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '审批状态：0-待审批，1-通过，2-拒绝',
  `comments` varchar(255) DEFAULT NULL COMMENT '审批意见',
  `applied_time` datetime NOT NULL COMMENT '申请时间',
  `approved_time` datetime DEFAULT NULL COMMENT '审批时间',
  PRIMARY KEY (`id`),
  KEY `idx_adaar_app_id` (`app_id`)
) COMMENT='应用审批记录表';