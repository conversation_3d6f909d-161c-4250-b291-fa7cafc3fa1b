-- 文件引用
ALTER TABLE `ai_dw_chat_history` ADD COLUMN `file_references` TEXT NULL COMMENT '文件/目录引用(JSON)' AFTER `tool_calls`;

-- 项目相关表结构
-- ai_dw_project definition

CREATE TABLE `ai_dw_project` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '项目表主键',
  `wiki_id` bigint(20) unsigned NOT NULL COMMENT 'wiki_info表主键id',
  `project_code` varchar(60) NOT NULL COMMENT '项目编码',
  `project_name` varchar(120) NOT NULL COMMENT '项目名称',
  `pm_id` bigint(20) unsigned DEFAULT NULL COMMENT '项目负责人ID',
  `pm_name` varchar(120) NOT NULL COMMENT '项目负责人名称',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '记录创建者主键ID',
  `created_date` datetime NOT NULL COMMENT '记录创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者主键ID',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新者',
  PRIMARY KEY (`id`),
  KEY `idx_adp_wiki_id` (`wiki_id`)
) COMMENT='项目表';

-- ai_dw_app_token_user_rel definition
CREATE TABLE `ai_dw_app_token_user_rel` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'token与user关联关系表主键',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户表主键',
  `token_id` int(10) unsigned NOT NULL COMMENT '应用token表主键',
  `created_by` int(10) unsigned NOT NULL COMMENT '记录创建者标识',
  `created_date` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_adatur_tid_uid` (`token_id`,`user_id`)
) COMMENT='用户级token与用户关联关系表';

-- wiki添加类型字段
ALTER TABLE wiki_info ADD type1 TINYINT UNSIGNED DEFAULT 1 NULL COMMENT 'wiki类型: 1:产品  2:项目';


-- 应用管理操作日志
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(18, 'APP_CREATE', 'APP_OPERATION', 'DEEPWIKI', 1, '创建应用');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(19, 'APP_UPDATE', 'APP_OPERATION', 'DEEPWIKI', 1, '更新应用');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(20, 'APP_DELETE', 'APP_OPERATION', 'DEEPWIKI', 1, '删除应用');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(21, 'APP_STATE', 'APP_OPERATION', 'DEEPWIKI', 1, '修改应用状态');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(22, 'TOKEN_CREATE', 'TOKEN_OPERATION', 'DEEPWIKI', 1, '创建TOKEN');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(23, 'TOKEN_DELETE', 'TOKEN_OPERATION', 'DEEPWIKI', 1, '删除TOKEN');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(24, 'TOKEN_UPDATE', 'TOKEN_OPERATION', 'DEEPWIKI', 1, '更新TOKEN');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(25, 'TOKEN_STATE', 'TOKEN_OPERATION', 'DEEPWIKI', 1, '修改TOKEN状态');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(26, 'API_BIND', 'APP_OPERATION', 'DEEPWIKI', 1, '绑定接口');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(27, 'API_UNBIND', 'APP_OPERATION', 'DEEPWIKI', 1, '解绑接口');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(28, 'APP_API_STATE', 'APP_OPERATION', 'DEEPWIKI', 1, '修改绑定接口状态');

-- 为 wiki_job 添加复合索引
ALTER TABLE `wiki_job`
  ADD INDEX `idx_wiki_job_wikiid_createdtime` (`wiki_info_id`, `created_time` DESC);

-- 如数据库版本不支持 DESC 索引，可使用非 DESC 版本
-- ALTER TABLE `wiki_job`
--   ADD INDEX `idx_wiki_job_wikiid_createdtime` (`wiki_info_id`, `created_time`);
