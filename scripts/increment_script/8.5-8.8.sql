-- 数据权限

-- ai_dw_wiki_user_role definition
CREATE TABLE `ai_dw_wiki_user_role` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'wiki-user-role主键',
  `wiki_id` bigint(20) unsigned NOT NULL COMMENT 'wiki标识',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户标识',
  `role_id` bigint(20) unsigned NOT NULL COMMENT '角色标识',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '记录创建者',
  `created_date` datetime NOT NULL COMMENT '记录创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_adwur_uid_wid_rid` (`user_id`,`wiki_id`,`role_id`)
) COMMENT='wiki用户角色表';

-- 新增两个角色
INSERT INTO ai_dw_role (id, role_name, role_code, comments) VALUES(5, 'wiki授权', 'wiki_grant', '可以将wiki授权给用户');
INSERT INTO ai_dw_role (id, role_name, role_code, comments) VALUES(6, 'wiki访问用户', 'wiki_access', '前端界面用户可以看到wiki');


-- 割接wiki创建者数据, 绑定角色5
INSERT INTO ai_dw_wiki_user_role (wiki_id, user_id, role_id,created_by,created_date)
SELECT 
    id AS wiki_id, 
    created_by as user_id, 
    5 AS role_id,
    0 AS created_by,
    created_time as created_date 
FROM wiki_info
WHERE NOT EXISTS (
    SELECT 1 FROM ai_dw_wiki_user_role 
    WHERE wiki_id = wiki_info.id 
    AND user_id = wiki_info.created_by 
    AND role_id = 5
);

-- 权限表
-- ai_dw_priv definition
CREATE TABLE `ai_dw_priv` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '权限主键标识',
  `priv_type` char(1) NOT NULL COMMENT '权限类型,S:服务权限,C:组件权限',
  `priv_code` varchar(30) NOT NULL COMMENT '权限编码',
  `priv_name` varchar(120) NOT NULL COMMENT '权限名称',
  `priv_el` varchar(1000) DEFAULT NULL COMMENT '权限表达式',
  `state` tinyint(3) unsigned NOT NULL COMMENT '权限状态:0: 失效 1: 有效 ',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '记录创建者',
  `created_date` datetime NOT NULL COMMENT '记录创建日期',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新日期',
  PRIMARY KEY (`id`)
) COMMENT='deepwiki权限表';

-- 初始化权限数据
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(1, 'S', 'GET_HEALTH', '健康检查GET请求', 'GET-/health', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(2, 'S', 'GET_ROOT', '根路径GET请求', 'GET-/', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(3, 'S', 'POST_FILE_ACTION', '文件操作POST请求', 'POST-/api/file-action', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(4, 'S', 'ALL_CONFIG', '配置相关所有请求', 'ALL-/api/config/.*', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(5, 'S', 'POST_TEST_CONNECTION', '测试连接POST请求', 'POST-/api/models/testConnection', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(6, 'S', 'POST_VERIFY_TOKEN', '验证研发云Token POST请求', 'POST-/api/whaleDevCloud/verifyToken', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(7, 'S', 'ALL_SHARE_SNAPSHOT', '共享会话所有请求', 'ALL-/api/share_snapshot', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(8, 'S', 'GET_LOCAL_REPO_STRUCTURE', '本地仓库信息GET请求', 'GET-/api/local_repo/structure', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(9, 'S', 'GET_WHALE_DEV_CLOUD_STRUCTURE', '研发云仓库信息GET请求', 'GET-/api/whaleDevCloud/structure', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(10, 'S', 'ALL_WIKI', 'wiki相关所有请求', 'ALL-/api/wiki/.*', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(11, 'S', 'POST_REPO_BRANCHES', '仓库分支POST请求', 'POST-/api/repo/branches', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(12, 'S', 'POST_CHAT_STREAM', '问答接口POST请求', 'POST-/api/chat/stream', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(13, 'S', 'ALL_WIKI_CACHE', '查看wiki详情的所有请求', 'ALL-/api/wiki_cache', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(14, 'C', 'CREATE_WIKI', '创建wiki', NULL, 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(15, 'C', 'DELETE_WIKI', '删除wiki', NULL, 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(16, 'C', 'REFRESH_WIKI', '刷新wiki', NULL, 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(17, 'C', 'EXPORT_WIKI', '导出wiki', NULL, 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(18, 'S', 'GET_PROJECTS_WITH_ROLE', '查询登录后关联的项目列表', 'GET-/api/wiki/projects-with-role', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(20, 'S', 'GET_CHAT_HISTORY', '查询会话历史', 'GET-/api/chat/history', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(21, 'S', 'ALL_CHAT', '会话相关的所有请求', 'ALL-/api/chat/.*', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(23, 'S', 'POST_K8S_SNADBOX', '用户k8s沙箱所有请求', 'ALL-/api/k8s/sandbox/me/.*', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(24, 'S', 'GET_WIKI_INFO', '查询wiki信息请求', 'GET-/api/wiki/info.*', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(25, 'S', 'ALL_WIKI_GRANT', '授权用户wiki权限所有请求', 'ALL-/api/wiki/grant.*', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(26, 'S', 'ALL_SESSION_HISTORY', '会话历史所有请求', 'ALL-/api/chat/session/.*', 1, 0, NOW(), NULL, NULL);

-- 角色权限表
-- 新增列
ALTER TABLE ai_dw_role_priv ADD priv_id INT UNSIGNED NOT NULL COMMENT '权限主键标识';

-- 割接脚本：将ai_dw_priv的id更新到ai_dw_role_priv的priv_id（通过priv_el等值连接）
UPDATE ai_dw_role_priv rp
JOIN ai_dw_priv p
  ON rp.priv_el IS NOT NULL AND p.priv_el IS NOT NULL AND rp.priv_el = p.priv_el
SET rp.priv_id = p.id;

-- 删除列
ALTER TABLE ai_dw_role_priv DROP COLUMN priv_el;

-- 新增服务权限
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(29, 4, 18);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(31, 4, 20);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(33, 4, 23);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(34, 3, 18);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(37, 2, 23);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(42, 4, 24);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(43, 3, 24);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(44, 4, 25);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(45, 3, 25);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(46, 4, 26);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(47, 3, 26);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(48, 4, 27);
-- 组件权限
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(38, 2, 14);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(39, 2, 15);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(40, 2, 16);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(41, 2, 17);

-- 修改角色为2的会话相关的服务权限
UPDATE ai_dw_role_priv SET priv_id=21 WHERE id=19;

-- 会话历史
ALTER TABLE ai_dw_chat_history ADD parent_id BIGINT UNSIGNED NULL COMMENT '助手消息关联的用户消息主键';

-- 割接脚本


-- 操作日志
-- ai_dw_serv_log definition

CREATE TABLE `ai_dw_serv_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志记录唯一标识，自增主键',
  `event_src` varchar(60)  NOT NULL DEFAULT 'deepwiki' COMMENT '事件来源系统,默认deepwiki',
  `event_type` varchar(255)  NOT NULL COMMENT '事件类型分类',
  `event_code` varchar(255)  NOT NULL COMMENT '事件编码/操作编码',
  `party_type` varchar(30)  NOT NULL COMMENT '被操作对象的类型(user,wiki)',
  `party_code` varchar(255)  DEFAULT NULL COMMENT '被操作对象的唯一编码',
  `party_name` varchar(60)  DEFAULT NULL COMMENT '被操作对象的名称',
  `party_id` varchar(60)  NOT NULL COMMENT '被操作对象的ID(如:用户ID或者wiki id)',
  `oper_id` bigint(20) unsigned NOT NULL COMMENT '操作人ID',
  `dept_id` int(10) unsigned DEFAULT NULL COMMENT '操作人所属部门ID',
  `dept_name` varchar(120)  DEFAULT NULL COMMENT '操作人所属部门名称',
  `oper_data` text  DEFAULT NULL COMMENT '操作数据',
  `src_ip` varchar(60)  DEFAULT NULL COMMENT '来源IP地址',
  `server_ip` varchar(60)  DEFAULT NULL COMMENT '服务器IP地址',
  `is_success` tinyint(3) unsigned NOT NULL COMMENT '是否成功(1成功/0失败)',
  `log_date` datetime NOT NULL COMMENT '日志记录时间',
  `comments` varchar(1000)  DEFAULT NULL COMMENT '操作备注/详细说明',
  PRIMARY KEY (`id`)
) COMMENT='系统服务操作日志表';

-- ai_dw_event_code definition

CREATE TABLE `ai_dw_event_code` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `event_code` varchar(255)  NOT NULL COMMENT '事件唯一编码',
  `event_type` varchar(255)  DEFAULT NULL COMMENT '事件类型分类',
  `event_src_code` varchar(60)  DEFAULT 'deepwiki' COMMENT '事件来源系统编码',
  `is_audit` tinyint(3) unsigned DEFAULT NULL COMMENT '是否需要审计(1是/0否)',
  `comments` varchar(255)  DEFAULT NULL COMMENT '事件描述说明',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_ad_ec_event_code` (`event_code`)
) COMMENT='事件编码配置表';

-- 初始化数据
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(1, 'WIKI_CREATE', 'WIKI_OPERATION', 'DEEPWIKI', 1, '创建新的Wiki文档');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(2, 'WIKI_DELETE', 'WIKI_OPERATION', 'DEEPWIKI', 1, '删除Wiki文档');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(3, 'WIKI_UPDATE', 'WIKI_OPERATION', 'DEEPWIKI', 1, '更新已有Wiki文档');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(4, 'WIKI_EXPORT', 'WIKI_OPERATION', 'DEEPWIKI', 1, '导出Wiki文档内容');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(5, 'TASK_RETRY', 'SYSTEM_OPERATION', 'DEEPWIKI', 1, '重新执行失败的任务');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(6, 'USER_LOGIN', 'AUTHENTICATION', 'DEEPWIKI', 1, '用户登录系统');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(7, 'USER_LOGOUT', 'AUTHENTICATION', 'DEEPWIKI', 1, '用户登出系统');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(8, 'WIKI_GRANT', 'WIKI_OPERATION', 'DEEPWIKI', 1, '授权Wiki');