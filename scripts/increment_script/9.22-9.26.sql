-- 添加job_type字段（tinyint unsigned类型）
ALTER TABLE `wiki_job` ADD COLUMN `job_type` TINYINT UNSIGNED DEFAULT 0 COMMENT '任务类型：0(首次生成), 1(刷新更新), 2(同步索引)';
UPDATE `wiki_job` SET `job_type` = 0 WHERE `job_type` IS NULL;

-- 创建应用与WIKI的关系表
CREATE TABLE `ai_dw_app_wiki_rela` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` BIGINT UNSIGNED NOT NULL COMMENT '应用主键ID，关联 ai_dw_app.id',
  `wiki_id` BIGINT UNSIGNED NOT NULL COMMENT 'Wiki 主键ID，关联 ai_dw_wiki_info.id',
  `created_by` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `created_date` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT UNSIGNED DEFAULT NULL COMMENT '更新人ID',
  `update_date` DATETIME DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uix_ai_dw_app_wiki_rela` (`app_id`, `wiki_id`),
  KEY `idx_ai_dw_app_wiki_rela_app_id` (`app_id`),
  KEY `idx_ai_dw_app_wiki_rela_wiki_id` (`wiki_id`)
) COMMENT='应用与Wiki绑定关系表';

-- wiki_info表拆分
-- 1) Wiki 基本信息
CREATE TABLE IF NOT EXISTS `ai_dw_wiki_info` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `wiki_id` VARCHAR(64) NOT NULL COMMENT 'Wiki唯一标识（外部可见ID/UUID）',
  `name` VARCHAR(255) NOT NULL COMMENT 'Wiki名称（展示名）',
  `description` TEXT NULL COMMENT 'Wiki描述',
  `wiki_type` TINYINT NOT NULL DEFAULT 1 COMMENT 'Wiki类型: 1=产品 2=项目',
  `project_topic_id` VARCHAR(255) NULL COMMENT '项目级DocChain主题ID（项目topic）',
  `provider` VARCHAR(64) NOT NULL DEFAULT 'google' COMMENT '默认模型提供商',
  `model` VARCHAR(128) NOT NULL DEFAULT 'gemini-pro' COMMENT '默认模型名称',
  `language` VARCHAR(16) NOT NULL DEFAULT 'zh' COMMENT '默认语言',
  `comprehensive` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否生成全面Wiki（1=是 0=否）',
  `excluded_dirs` TEXT NULL COMMENT '全局排除目录，逗号/分号分隔',
  `excluded_files` TEXT NULL COMMENT '全局排除文件，逗号/分号分隔',
  `included_dirs` TEXT NULL COMMENT '全局包含目录，逗号/分号分隔',
  `included_files` TEXT NULL COMMENT '全局包含文件，逗号/分号分隔',
  `visibility` TINYINT UNSIGNED NOT NULL DEFAULT 2 COMMENT '可见性: 1=全员可见 2=仅授权/创建人可见',
  `status` VARCHAR(32) NOT NULL DEFAULT 'active' COMMENT '状态: active/archived/deleted',
  `repo_url` VARCHAR(512) NULL COMMENT '主仓库URL（冗余字段，便于查询）',
  `repo_owner` VARCHAR(255) NULL COMMENT '主仓库所有者（冗余字段）',
  `repo_name` VARCHAR(255) NULL COMMENT '主仓库名称（冗余字段）',
  `branch` VARCHAR(128) NULL COMMENT '主仓库分支（冗余字段）',
  `repo_type` VARCHAR(64) NULL COMMENT '主仓库类型（冗余字段）',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` INT NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `updated_by` INT NOT NULL DEFAULT 0 COMMENT '更新人ID',
  `owner_id` INT NOT NULL DEFAULT 0 COMMENT '拥有者ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_ai_dw_wiki_info_wiki_id` (`wiki_id`),
  UNIQUE KEY `uk_ai_dw_wiki_repo_branch_lang` (`repo_owner`, `repo_name`, `branch`, `language`)
) COMMENT='Wiki 基本信息表（项目/产品级配置与Topic）';

-- 2) Git 仓库信息
CREATE TABLE IF NOT EXISTS `ai_dw_git_repository` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `repo_url` VARCHAR(512) NOT NULL COMMENT '仓库URL（含host/owner/repo.git）',
  `branch` VARCHAR(128) NOT NULL DEFAULT 'master' COMMENT '分支',
  `repo_owner` VARCHAR(255) NOT NULL COMMENT '仓库所有者',
  `repo_name` VARCHAR(255) NOT NULL COMMENT '仓库名称',
  `repo_type` VARCHAR(64) NOT NULL DEFAULT 'whaleDevCloud' COMMENT '仓库类型: github/gitlab/bitbucket/whaleDevCloud 等',
  `description` TEXT NULL COMMENT '仓库描述',
  `is_private` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否私有仓库（1=是 0=否）',
  `code_topic_id` VARCHAR(255) NULL COMMENT 'DocChain 代码主题ID（代码topic）',
  `last_sync_time` DATETIME NULL COMMENT '最后同步DocChain时间',
  `file_count` INT NOT NULL DEFAULT 0 COMMENT '仓库文件数（用于与DocChain索引数比对）',
  `doc_total_files` INT NOT NULL DEFAULT 0 COMMENT 'DocChain Topic文件总数（缓存）',
  `doc_pending_files` INT NOT NULL DEFAULT 0 COMMENT 'DocChain Topic待处理文件数（缓存）',
  `status` VARCHAR(32) NOT NULL DEFAULT 'active' COMMENT '状态: active/archived',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_ai_dw_git_repo_url_branch` (`repo_url`, `branch`)
) COMMENT='Git 仓库信息表（承载代码topic，可被多个Wiki复用）';

-- 3) Wiki 与 仓库 关联
CREATE TABLE IF NOT EXISTS `ai_dw_wiki_repository_relation` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `wiki_id` VARCHAR(64) NOT NULL COMMENT 'Wiki唯一标识（引用逻辑层面的 ai_dw_wiki_info.wiki_id）',
  `repository_id` INT UNSIGNED NOT NULL COMMENT 'Git仓库ID（引用逻辑层面的 ai_dw_git_repository.id）',
  `is_main_repo` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否主仓库（1=主 0=子）',
  `excluded_dirs` TEXT NULL COMMENT '该仓库排除目录（覆盖Wiki级配置）',
  `excluded_files` TEXT NULL COMMENT '该仓库排除文件（覆盖Wiki级配置）',
  `included_dirs` TEXT NULL COMMENT '该仓库包含目录（覆盖Wiki级配置）',
  `included_files` TEXT NULL COMMENT '该仓库包含文件（覆盖Wiki级配置）',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_ai_dw_wiki_repo_relation` (`wiki_id`, `repository_id`),
  KEY `idx_ai_dw_wrr_wiki_main` (`wiki_id`, `is_main_repo`)
) COMMENT='Wiki 与 Git 仓库关联表（主/子仓库、多仓库复用。无外键约束，业务侧保证一致性）';

-- 4) Wiki 内容
CREATE TABLE IF NOT EXISTS `ai_dw_wiki_content` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `wiki_id` VARCHAR(64) NOT NULL COMMENT 'Wiki唯一标识（与 ai_dw_wiki_info.wiki_id 一致）',
  `wiki_structure` JSON NULL COMMENT 'Wiki结构数据（XML/LLM解析结果映射）',
  `wiki_pages` JSON NULL COMMENT 'Wiki页面内容（page_id -> content）',
  `version` INT NOT NULL DEFAULT 1 COMMENT '内容版本号（预留）',
  `generation_info` JSON NULL COMMENT '生成元信息（模型/参数等）',
  `total_pages` INT NOT NULL DEFAULT 0 COMMENT '总页面数（冗余统计）',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_ai_dw_content_wiki_id` (`wiki_id`)
) COMMENT='Wiki 内容表（结构与页面JSON大字段独立存储。无外键约束）';


-- 1. 添加job_type字段（tinyint unsigned类型）
ALTER TABLE wiki_job ADD COLUMN job_type TINYINT UNSIGNED DEFAULT 0 COMMENT '任务类型：0(首次生成), 1(刷新更新)';


-- 2. 为现有数据设置默认值（所有现有任务都是generation类型，即0）
UPDATE wiki_job SET job_type = 0 WHERE job_type IS NULL;

-- 3. 为仓库表补充DocChain文件统计缓存字段
ALTER TABLE ai_dw_git_repository
  ADD COLUMN doc_total_files INT NOT NULL DEFAULT 0 COMMENT 'DocChain Topic文件总数（缓存）' AFTER file_count,
  ADD COLUMN doc_pending_files INT NOT NULL DEFAULT 0 COMMENT 'DocChain Topic待处理文件数（缓存）' AFTER doc_total_files,
  ADD COLUMN last_sync_commit VARCHAR(64) DEFAULT NULL COMMENT '最后同步到DocChain的commit哈希' AFTER last_sync_time;

UPDATE ai_dw_git_repository SET doc_total_files = COALESCE(doc_total_files, 0), doc_pending_files = COALESCE(doc_pending_files, 0);

-- 日志表添加索引
CREATE INDEX idx_adsl_oper_id_log_date USING BTREE ON ai_dw_serv_log (oper_id,log_date);

-- 会话来源
ALTER TABLE ai_dw_chat_session ADD qa_src TINYINT UNSIGNED DEFAULT 1 NOT NULL COMMENT '问答来源 1: deepwiki 2: mcp tool 3: open api';
ALTER TABLE ai_dw_chat_session ADD app_id INT UNSIGNED NULL COMMENT '应用ID';

-- 服务权限
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(45, 'S', 'GET_WIKI_DETAIL', '查看wiki详情', 'GET-/api/wiki_detail', 1, 0, NOW(), NULL, NULL);

INSERT INTO ai_dw_role_priv(role_id, priv_id) VALUES(2, 45);
INSERT INTO ai_dw_role_priv(role_id, priv_id) VALUES(3, 45);
INSERT INTO ai_dw_role_priv(role_id, priv_id) VALUES(4, 45);

INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(46, 'S', 'SYNC_PROGRESS', '查看wiki详情', 'ALL-/api/wiki/.+/index-progress', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_role_priv(role_id, priv_id) VALUES(2, 46);
INSERT INTO ai_dw_role_priv(role_id, priv_id) VALUES(3, 46);
INSERT INTO ai_dw_role_priv(role_id, priv_id) VALUES(4, 46);