-- 权限相关sql
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(38, 'S', 'TAG_ACTION', '标签相关请求', 'ALL-/api/tags.*', 1, 0, NOW(), NULL, NULL);

INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(50, 4, 38);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(51, 3, 38);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(52, 2, 38);

-- 标签相关表
-- ai_dw_tag definition

CREATE TABLE `ai_dw_tag` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `name` varchar(60)  NOT NULL COMMENT '标签名称',
  `type` tinyint(3) unsigned NOT NULL COMMENT '标签类型（1:系统 2:用户）',
  `color` varchar(16)  NOT NULL COMMENT '标签颜色（如#FF0000）',
  `comments` varchar(255)  DEFAULT NULL COMMENT '标签描述',
  `module_type` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '标签归属模块, 1:deepwiki',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '标签状态, 1:有效 0:失效',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '创建人ID',
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '修改人ID',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_adt_created_by_name` (`created_by`,`name`)
) COMMENT='标签表';

-- ai_dw_wiki_tag definition

CREATE TABLE `ai_dw_wiki_tag` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '关联关系ID',
  `wiki_id` bigint(20) unsigned NOT NULL COMMENT 'wiki主键ID',
  `tag_id` bigint(20) unsigned NOT NULL COMMENT '标签ID',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '创建人ID',
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '更新人ID',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_wiki_id` (`wiki_id`),
  KEY `idx_tag_id` (`tag_id`)
) COMMENT='wiki与标签多对多关联表';

-- ai_dw_wiki_dc_ext_1 definition

CREATE TABLE `ai_dw_wiki_dc_ext_1` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'wiki扩展信息表主键标识',
  `wiki_id` bigint(20) unsigned NOT NULL COMMENT 'wiki标识',
  `dc_repo_id` bigint(20) unsigned DEFAULT NULL COMMENT '研发云仓库ID',
  `dc_project_id` int(10) unsigned DEFAULT NULL COMMENT '研发云项目ID',
  `branch_version_id` int(10) unsigned DEFAULT NULL COMMENT '分支版本ID',
  `branch_version_name` varchar(1000)  DEFAULT NULL COMMENT '分支版本名称',
  `product_version_id` int(10) unsigned DEFAULT NULL COMMENT '产品版本ID',
  `product_version_code` varchar(1000)  DEFAULT NULL COMMENT '产品版本编码',
  `product_name` varchar(1000)  DEFAULT NULL COMMENT '产品名称',
  `product_id` bigint(20) unsigned DEFAULT NULL COMMENT '产品ID',
  `product_line_id` bigint(20) unsigned DEFAULT NULL COMMENT '产品线ID',
  `product_line_name` varchar(1000)  DEFAULT NULL COMMENT '产品线名称',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '记录创建者',
  `created_date` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_adwe_wiki_id` (`wiki_id`)
) COMMENT='wiki仓库关联的研发云扩展信息';


-- ai_dw_wiki_dc_ext_2 definition

CREATE TABLE `ai_dw_wiki_dc_ext_2` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'wiki扩展信息表主键标识',
  `wiki_id` bigint(20) unsigned NOT NULL COMMENT 'wiki标识',
  `release_pkg_id` int(10) unsigned DEFAULT NULL COMMENT '发布包ID',
  `release_pkg_code` varchar(1000)  DEFAULT NULL COMMENT '发布包编码',
  `solution_name` varchar(1000)  DEFAULT NULL COMMENT '解决方案名称',
  `solution_id` bigint(20) unsigned DEFAULT NULL COMMENT '解决方案ID',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '记录创建者',
  `created_date` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新者',
  PRIMARY KEY (`id`),
  KEY `idx_adwdce_wiki_id` (`wiki_id`)
) COMMENT='wiki仓库关联的研发云扩展信息:发布包解决方案';