-- 个人沙盒

-- wiki关联仓库linux用户组
ALTER TABLE ai_dw_git_repository ADD linux_gid INT UNSIGNED NULL COMMENT '仓库对应的gid';
ALTER TABLE ai_dw_git_repository ADD linux_group_name varchar(32) NULL COMMENT '仓库对应的linux用户组名';
ALTER TABLE ai_dw_git_repository ADD linux_code_file_perm tinyint(1) NOT NULL DEFAULT '0' COMMENT '代码文件权限是否设置成功';
ALTER TABLE ai_dw_git_repository ADD linux_pw_file_perm tinyint(1) NOT NULL DEFAULT '0' COMMENT '项目工作空间文件权限是否设置成功';
ALTER TABLE ai_dw_git_repository ADD linux_uw_file_perm tinyint(1) NOT NULL DEFAULT '0' COMMENT '用户空间文件权限是否设置成功';

-- Deepwiki用户 linux用户组
ALTER TABLE ai_dw_user_ext ADD linux_gid INT UNSIGNED NULL COMMENT '用户对应linux gid' AFTER sandbox_quota;
ALTER TABLE ai_dw_user_ext ADD linux_group_name varchar(32) NULL COMMENT '用户对应的linux用户组名,使用工号即可' AFTER linux_gid;

-- 沙盒wct-cli api
INSERT INTO ai_dw_api_def (id, api_code, api_name, api_path, api_method, comments, category, state, created_time, update_time) VALUES(43, 'check_sandbox_perssion', '校验用户是否拥有沙箱权限', '/api/open/sandbox/permission/check', 'GET', '校验用户是否拥有沙箱权限', 'wiki', 1, NOW(), NULL);