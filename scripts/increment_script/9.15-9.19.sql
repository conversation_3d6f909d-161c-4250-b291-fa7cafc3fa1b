CREATE TABLE `ai_dw_user_access_token` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户Access Token表主键',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户主键标识',
  `name` varchar(120) COLLATE utf8mb4_bin NOT NULL COMMENT 'token名称',
  `token` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '用户token, 需加密',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT 'Token状态：0: 禁用 1: 有效',
  `use_type` varchar(60) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Token使用类型,如cherry/cursor/qshell等',
  `effective_at` datetime DEFAULT NULL COMMENT 'Token生效日期,为空表示立即生效',
  `expires_at` datetime DEFAULT NULL COMMENT 'Token过期时间,为空表示永久有效',
  `created_by` bigint(20) unsigned NOT NULL COMMENT 'Token创建者主键标识',
  `created_date` datetime NOT NULL COMMENT 'Token创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT 'Token更新者主键标识',
  `update_date` datetime DEFAULT NULL COMMENT 'Token更新时间',
  `last_used_time` datetime DEFAULT NULL COMMENT 'Token最后使用时间',
  PRIMARY KEY (`id`),
  KEY `idx_aduat_user_id` (`user_id`),
  KEY `idx_aduat_token` (`token`)
) COMMENT='用户Access Token表';

---------------------------------------------------------------------------------------------

INSERT INTO ai_dw_event_code
(event_code, event_type, event_src_code, is_audit, comments)
VALUES('USER_TOKEN_CREATE', 'USER_TOKEN_OPERATION', 'DEEPWIKI', 1, '创建用户级别token');
INSERT INTO ai_dw_event_code
(event_code, event_type, event_src_code, is_audit, comments)
VALUES('USER_TOKEN_DELETE', 'USER_TOKEN_OPERATION', 'DEEPWIKI', 1, '删除用户级别token');
INSERT INTO ai_dw_event_code
(event_code, event_type, event_src_code, is_audit, comments)
VALUES('USER_TOKEN_DISABLE', 'USER_TOKEN_OPERATION', 'DEEPWIKI', 1, '禁用用户级别token');
INSERT INTO ai_dw_event_code
(event_code, event_type, event_src_code, is_audit, comments)
VALUES('USER_TOKEN_ENABLE', 'USER_TOKEN_OPERATION', 'DEEPWIKI', 1, '启用用户级别token');

---------------------------------------------------------------------------------------------

INSERT INTO ai_dw_priv
(id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date)
VALUES(42, 'S', 'USER_TOKEN_ACTION', '用户级别accessToken请求', 'ALL-/api/user_token.*', 1, 0, '2025-08-06 14:36:00', NULL, NULL);

INSERT INTO ai_dw_role_priv
(role_id, priv_id)
VALUES(2, 42);

INSERT INTO ai_dw_role_priv
(role_id, priv_id)
VALUES(3, 42);

INSERT INTO ai_dw_role_priv
(role_id, priv_id)
VALUES(4, 42);
