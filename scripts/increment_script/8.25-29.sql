-- 授权转移需求
ALTER TABLE wiki_info ADD owner_id BIGINT UNSIGNED NOT NULL COMMENT 'wiki所有者' AFTER updated_by;

CREATE INDEX idx_wiki_info_owner_id ON wiki_info (owner_id);

-- 割接脚本：将 wiki_info 表的 created_by 字段数据迁移到 owner_id 字段
-- 执行前请确保 owner_id 字段已存在且类型为 int

UPDATE wiki_info
SET owner_id = created_by;

-- 接口权限
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(28, 'S', 'ALL_OWNER', 'wiki所有者相关请求', 'ALL-/api/owner.*', 1, 0, NOW(), NULL, NULL);

INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(57, 4, 40);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(58, 2, 40);

-- wiki访问操作日志
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(17, 'WIKI_ACCESS', 'WIKI_OPERATION', 'DEEPWIKI', 1, '访问wiki');

-- 问答接口调用来源
ALTER TABLE ai_dw_chat_history ADD qa_src TINYINT UNSIGNED DEFAULT 1 NOT NULL COMMENT '问答来源 1: deepwiki 2: mcp tool' AFTER `state`;

-- 添加wiki_info、wiki_job索引
CREATE INDEX idx_wiki_info_wiki_id ON wiki_info(wiki_id);
CREATE INDEX idx_wiki_job_status ON wiki_job(status);
CREATE index idx_wiki_job_wiki_info_id ON wiki_job(wiki_info_id);