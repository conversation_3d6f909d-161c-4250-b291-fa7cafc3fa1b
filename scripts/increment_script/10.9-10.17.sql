-- 命令行参数
ALTER TABLE `ai_dw_chat_history` ADD COLUMN `command_params` MEDIUMTEXT  NULL COMMENT '命令参数(JSON)' AFTER `file_references`;

-- 美化用户输入
INSERT INTO ai_dw_priv
(priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date)
VALUES('S', 'OPTIMIZE_QUERY', '优化提示词请求', 'POST-/api/optimize/question', 1, 0, NOW(), NULL, NULL);

INSERT INTO ai_dw_role_priv
(role_id, priv_id)
VALUES(2, 109);
INSERT INTO ai_dw_role_priv
(role_id, priv_id)
VALUES(4, 109);

-- 修复ai_dw_chat_session中wiki_id字段类型问题
ALTER TABLE ai_dw_chat_session MODIFY COLUMN wiki_id INT UNSIGNED NOT NULL COMMENT 'wiki主键标识';