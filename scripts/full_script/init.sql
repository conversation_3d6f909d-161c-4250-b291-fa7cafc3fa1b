-- 角色表初始化数据
INSERT INTO ai_dw_role (id, role_name, role_code, role_type, access_level, comments) VALUES(1, '超级管理员', 'super_admin', 'S', 4, '具备网站的所有权限');
INSERT INTO ai_dw_role (id, role_name, role_code, role_type, access_level, comments) VALUES(2, 'wiki管理员', 'repo_owner', 'S', 3, '具备代码仓库的管理权限，可以生成、删除、更新wiki');
INSERT INTO ai_dw_role (id, role_name, role_code, role_type, access_level, comments) VALUES(3, '普通用户', 'user', 'S', 1, '可以查看wiki');
INSERT INTO ai_dw_role (id, role_name, role_code, role_type, access_level, comments) VALUES(4, 'wiki问答用户', 'ai_qa', 'S', 2, '可以查看wiki，并支持问答');
INSERT INTO ai_dw_role (id, role_name, role_code, role_type, access_level, comments) VALUES(5, 'wiki授权', 'wiki_grant', 'D', 2, '可以将wiki授权给用户');
INSERT INTO ai_dw_role (id, role_name, role_code, role_type, access_level, comments) VALUES(6, 'wiki访问用户', 'wiki_access', 'D', 1, '前端界面用户可以看到wiki');

-- 权限表初始化数据
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(1, 'S', 'GET_HEALTH', '健康检查GET请求', 'GET-/health', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(2, 'S', 'GET_ROOT', '根路径GET请求', 'GET-/', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(3, 'S', 'POST_FILE_ACTION', '文件操作POST请求', 'POST-/api/file-action', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(4, 'S', 'ALL_CONFIG', '配置相关所有请求', 'ALL-/api/config/.*', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(5, 'S', 'POST_TEST_CONNECTION', '测试连接POST请求', 'POST-/api/models/testConnection', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(6, 'S', 'POST_VERIFY_TOKEN', '验证研发云Token POST请求', 'POST-/api/whaleDevCloud/verifyToken', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(7, 'S', 'ALL_SHARE_SNAPSHOT', '共享会话所有请求', 'ALL-/api/share_snapshot', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(8, 'S', 'GET_LOCAL_REPO_STRUCTURE', '本地仓库信息GET请求', 'GET-/api/local_repo/structure', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(9, 'S', 'GET_WHALE_DEV_CLOUD_STRUCTURE', '研发云仓库信息GET请求', 'GET-/api/whaleDevCloud/structure', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(10, 'S', 'ALL_WIKI', 'wiki相关所有请求', 'ALL-/api/wiki/.*', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(11, 'S', 'POST_REPO_BRANCHES', '仓库分支POST请求', 'POST-/api/repo/branches', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(12, 'S', 'POST_CHAT_STREAM', '问答接口POST请求', 'POST-/api/chat/stream', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(13, 'S', 'ALL_WIKI_CACHE', '查看wiki详情的所有请求', 'ALL-/api/wiki_cache', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(14, 'C', 'CREATE_WIKI', '创建wiki', NULL, 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(15, 'C', 'DELETE_WIKI', '删除wiki', NULL, 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(16, 'C', 'REFRESH_WIKi', '刷新wiki', NULL, 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(17, 'C', 'EXPORT_WIKI', '导出wiki', NULL, 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(18, 'S', 'GET_PROJECTS_WITH_ROLE', '查询登录后关联的项目列表', 'GET-/api/wiki/projects-with-role', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(19, 'S', 'GET_CHAT_HISTORY', '查询会话历史', 'GET-/api/chat/history', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(20, 'S', 'ALL_CHAT', '会话相关的所有请求', 'ALL-/api/chat/.*', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(21, 'S', 'POST_K8S_SNADBOX', '用户k8s沙箱所有请求', 'ALL-/api/k8s/sandbox/me/.*', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(22, 'S', 'GET_WIKI_INFO', '查询wiki信息请求', 'GET-/api/wiki/info.*', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(23, 'S', 'ALL_WIKI_GRANT', '授权用户wiki权限所有请求', 'ALL-/api/wiki/grant.*', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(24, 'S', 'ALL_SESSION_HISTORY', '会话历史所有请求', 'ALL-/api/chat/session.*', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(25, 'S', 'GET_WIKI_CACHE', '查看wiki详情GET请求', 'GET-/api/wiki_cache', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(26, 'S', 'ALL_USER', '用户相关请求', 'ALL-/api/users.*', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(27, 'S', 'TAG_ACTION', '标签相关请求', 'ALL-/api/tags/.*', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(28, 'S', 'ALL_OWNER', 'wiki所有者相关请求', 'ALL-/api/owner.*', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(29, 'S', 'FILE_SYSTEM', '沙箱文件操作', 'ALL-/api/file.*', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(30, 'S', 'USER_TOKEN_ACTION', '用户级别accessToken请求', 'ALL-/api/user_token.*', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(31, 'S', 'GET_WIKI_DETAIL', '查看wiki详情', 'GET-/api/wiki_detail', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(32, 'S', 'SYNC_PROGRESS', 'wiki知识库同步接口', 'ALL-/api/wiki/.+/index-progress', 1, 0, NOW(), NULL, NULL);
INSERT INTO ai_dw_priv (id, priv_type, priv_code, priv_name, priv_el, state, created_by, created_date, update_by, update_date) VALUES(33, 'S', 'OPTIMIZE_QUERY', '优化提示词请求', 'POST-/api/optimize/question', 1, 0, NOW(), NULL, NULL);


-- 角色权限表初始化数据
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(1, 3, 1);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(2, 3, 2);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(3, 3, 3);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(4, 3, 4);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(5, 3, 5);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(6, 3, 6);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(7, 3, 7);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(8, 2, 1);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(9, 2, 2);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(10, 2, 3);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(11, 2, 4);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(12, 2, 5);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(13, 2, 6);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(14, 2, 7);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(15, 2, 8);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(16, 2, 9);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(17, 2, 10);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(18, 2, 11);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(19, 2, 20);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(20, 2, 13);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(21, 4, 1);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(22, 4, 2);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(23, 4, 3);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(24, 4, 4);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(25, 4, 5);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(26, 4, 6);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(27, 4, 7);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(28, 4, 12);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(29, 4, 18);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(30, 4, 19);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(31, 4, 21);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(32, 3, 18);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(33, 2, 21);
-- 组件权限
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(34, 2, 14);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(35, 2, 15);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(36, 2, 16);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(37, 2, 17);

INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(38, 4, 22);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(39, 3, 22);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(40, 4, 23);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(41, 3, 23);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(42, 4, 24);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(43, 3, 24);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(44, 3, 25);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(45, 4, 25);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(46, 4, 27);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(47, 3, 27);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(48, 2, 27);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(49, 4, 28);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(50, 2, 28);

-- 沙盒文件操作权限
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(51, 4, 29);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(52, 2, 29);

-- 用户级别token权限
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(53, 4, 30);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(54, 3, 30);
INSERT INTO ai_dw_role_priv (id, role_id, priv_id) VALUES(55, 2, 30);

-- wiki详情服务
INSERT INTO ai_dw_role_priv(id, role_id, priv_id) VALUES(56, 2, 31);
INSERT INTO ai_dw_role_priv(id, role_id, priv_id) VALUES(57, 3, 31);
INSERT INTO ai_dw_role_priv(id, role_id, priv_id) VALUES(58, 4, 31);

-- wiki 知识库同步
INSERT INTO ai_dw_role_priv(id, role_id, priv_id) VALUES(59, 2, 32);
INSERT INTO ai_dw_role_priv(id, role_id, priv_id) VALUES(60, 3, 32);
INSERT INTO ai_dw_role_priv(id, role_id, priv_id) VALUES(61, 4, 32);

-- 美化用户输入
INSERT INTO ai_dw_role_priv(id, role_id, priv_id) VALUES(62, 2, 33);
INSERT INTO ai_dw_role_priv(id, role_id, priv_id) VALUES(63, 4, 33);

-- 日志类型表初始化数据
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(1, 'WIKI_CREATE', 'WIKI_OPERATION', 'DEEPWIKI', 1, '创建新的Wiki文档');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(2, 'WIKI_DELETE', 'WIKI_OPERATION', 'DEEPWIKI', 1, '删除Wiki文档');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(3, 'WIKI_UPDATE', 'WIKI_OPERATION', 'DEEPWIKI', 1, '更新已有Wiki文档');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(4, 'WIKI_EXPORT', 'WIKI_OPERATION', 'DEEPWIKI', 1, '导出Wiki文档内容');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(5, 'TASK_RETRY', 'SYSTEM_OPERATION', 'DEEPWIKI', 1, '重新执行失败的任务');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(6, 'USER_LOGIN', 'AUTHENTICATION', 'DEEPWIKI', 1, '用户登录系统');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(7, 'USER_LOGOUT', 'AUTHENTICATION', 'DEEPWIKI', 1, '用户登出系统');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(8, 'WIKI_GRANT', 'WIKI_OPERATION', 'DEEPWIKI', 1, '授权Wiki');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(9, 'WIKI_GRANT_DXP', 'WIKI_OPERATION', 'DEEPWIKI', 1, '授权WiKi(DXP)');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(10, 'USER_ROLE', 'USER_OPERATION', 'DEEPWIKI', 1, '修改用户角色');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(11, 'TASK_CANCEL', 'SYSTEM_OPERATION', 'DEEPWIKI', 1, '取消Wiki生成任务');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(12, 'TASK_DELETE', 'SYSTEM_OPERATION', 'DEEPWIKI', 1, '删除已取消任务及其关联的Wiki记录');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(13, 'TASK_RESTART', 'SYSTEM_OPERATION', 'DEEPWIKI', 1, '重新开始Wiki生成任务');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(14, 'CONFIG_SETTINGS', 'CONFIG_OPERATION', 'DEEPWIKI', 1, '修改个人设置');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(15, 'CHAT_UPDATE', 'CHAT_OPERATION', 'DEEPWIKI', 1, '修改会话标题');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(16, 'CHAT_DELETE', 'CHAT_OPERATION', 'DEEPWIKI', 1, '删除会话');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(17, 'WIKI_ACCESS', 'WIKI_OPERATION', 'DEEPWIKI', 1, '访问wiki');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(18, 'APP_CREATE', 'APP_OPERATION', 'DEEPWIKI', 1, '创建应用');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(19, 'APP_UPDATE', 'APP_OPERATION', 'DEEPWIKI', 1, '更新应用');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(20, 'APP_DELETE', 'APP_OPERATION', 'DEEPWIKI', 1, '删除应用');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(21, 'APP_STATE', 'APP_OPERATION', 'DEEPWIKI', 1, '修改应用状态');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(22, 'TOKEN_CREATE', 'TOKEN_OPERATION', 'DEEPWIKI', 1, '创建TOKEN');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(23, 'TOKEN_DELETE', 'TOKEN_OPERATION', 'DEEPWIKI', 1, '删除TOKEN');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(24, 'TOKEN_UPDATE', 'TOKEN_OPERATION', 'DEEPWIKI', 1, '更新TOKEN');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(25, 'TOKEN_STATE', 'TOKEN_OPERATION', 'DEEPWIKI', 1, '修改TOKEN状态');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(26, 'API_BIND', 'APP_OPERATION', 'DEEPWIKI', 1, '绑定接口');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(27, 'API_UNBIND', 'APP_OPERATION', 'DEEPWIKI', 1, '解绑接口');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(28, 'APP_API_STATE', 'APP_OPERATION', 'DEEPWIKI', 1, '修改绑定接口状态');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(29, 'FILE_UPLOAD', 'SANDBOX_OPERATION', 'DEEPWIKI', 1, '上传文件');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(30, 'FILE_DOWNLOAD', 'SANDBOX_OPERATION', 'DEEPWIKI', 1, '下载文件');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(31, 'DIR_CREATE', 'SANDBOX_OPERATION', 'DEEPWIKI', 1, '创建文件夹');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(32, 'DIR_DOWNLOAD', 'SANDBOX_OPERATION', 'DEEPWIKI', 1, '下载文件夹');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(33, 'USER_TOKEN_CREATE', 'USER_TOKEN_OPERATION', 'DEEPWIKI', 1, '创建用户级别token');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(34, 'USER_TOKEN_DELETE', 'USER_TOKEN_OPERATION', 'DEEPWIKI', 1, '删除用户级别token');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(35, 'USER_TOKEN_DISABLE', 'USER_TOKEN_OPERATION', 'DEEPWIKI', 1, '禁用用户级别token');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(36, 'USER_TOKEN_ENABLE', 'USER_TOKEN_OPERATION', 'DEEPWIKI', 1, '启用用户级别token');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(37, 'APP_SANDBOX_STOP', 'APP_SANDBOX', 'DEEPWIKI', 1, '停用应用沙箱');
INSERT INTO ai_dw_event_code (id, event_code, event_type, event_src_code, is_audit, comments) VALUES(38, 'APP_SANDBOX_START', 'APP_SANDBOX', 'DEEPWIKI', 1, '启用应用沙箱');

-- 对外开放API定义
INSERT INTO ai_dw_api_def (id, api_code, api_name, api_path, api_method, comments, category, state, created_time, update_time) VALUES(1, 'get_wiki_list', '获取wiki列表', '/api/open/wiki_list', 'GET', '获取用户有权限访问的wiki列表', 'wiki', 1, NOW(), NULL);
INSERT INTO ai_dw_api_def (id, api_code, api_name, api_path, api_method, comments, category, state, created_time, update_time) VALUES(2, 'wiki_qa', 'wiki问答接口', '/api/open/v1/chat/completions', 'POST', 'wiki问答接口', 'wiki', 1, NOW(), NULL);
INSERT INTO ai_dw_api_def (id, api_code, api_name, api_path, api_method, comments, category, state, created_time, update_time) VALUES(3, 'projects_with_role', '详细的wiki列表', '/api/open/projects-with-role', 'GET', '获取详细的wiki列表', 'wiki', 1, NOW(), NULL);
INSERT INTO ai_dw_api_def (id, api_code, api_name, api_path, api_method, comments, category, state, created_time, update_time) VALUES(4, 'check_sandbox_perssion', '校验用户是否拥有沙箱权限', '/api/open/sandbox/permission/check', 'GET', '校验用户是否拥有沙箱权限', 'wiki', 1, NOW(), NULL);