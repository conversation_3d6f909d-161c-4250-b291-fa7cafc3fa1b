DELIMITER //

CREATE PROCEDURE `CleanupExpiredLocks`()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 删除过期的锁
    DELETE FROM `job_lock` WHERE `expires_at` < NOW();
    
    -- 标记超过5分钟没有心跳的实例为崩溃状态
    UPDATE `job_manager_instance` 
    SET `status` = 'crashed' 
    WHERE `status` = 'active' 
    AND `last_heartbeat` < DATE_SUB(NOW(), INTERVAL 5 MINUTE);
    
    -- 删除崩溃实例的锁（通过外键约束自动删除）
    DELETE FROM `job_manager_instance` 
    WHERE `status` = 'crashed' 
    AND `last_heartbeat` < DATE_SUB(NOW(), INTERVAL 1 HOUR);
    
    COMMIT;
    
    SELECT 
        ROW_COUNT() as affected_rows,
        NOW() as cleanup_time,
        'Cleanup completed successfully' as message;
END //


CREATE PROCEDURE `GetDistributedLockStats`()
BEGIN
    SELECT 
        'Instance Stats' as category,
        COUNT(*) as total_instances,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_instances,
        SUM(CASE WHEN status = 'crashed' THEN 1 ELSE 0 END) as crashed_instances,
        SUM(current_jobs) as total_current_jobs,
        AVG(max_concurrent_jobs) as avg_max_jobs
    FROM job_manager_instance

    UNION ALL

    SELECT 
        'Lock Stats' as category,
        COUNT(*) as total_locks,
        SUM(CASE WHEN expires_at > NOW() THEN 1 ELSE 0 END) as active_locks,
        SUM(CASE WHEN expires_at <= NOW() THEN 1 ELSE 0 END) as expired_locks,
        0 as unused1,
        0 as unused2
    FROM job_lock

    UNION ALL

    SELECT 
        'Global Job Stats' as category,
        COUNT(*) as total_jobs,
        SUM(CASE WHEN global_status = 'processing' THEN 1 ELSE 0 END) as processing_jobs,
        SUM(CASE WHEN global_status = 'completed' THEN 1 ELSE 0 END) as completed_jobs,
        SUM(CASE WHEN global_status = 'failed' THEN 1 ELSE 0 END) as failed_jobs,
        SUM(CASE WHEN global_status = 'available' THEN 1 ELSE 0 END) as available_jobs
    FROM global_job_state;
END //

DELIMITER ;