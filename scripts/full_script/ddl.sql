-- ai_dw_announce definition

CREATE TABLE `ai_dw_announce` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '公告表主键标识',
  `title` varchar(255) NOT NULL COMMENT '公告标题',
  `type` varchar(30) NOT NULL DEFAULT 'link' COMMENT '公告类型: link超链接模式',
  `content` varchar(1000) NOT NULL COMMENT '公告内容,如链接地址',
  `seq` smallint(5) unsigned NOT NULL COMMENT '公告排列顺序,从1开始,依次加1',
  `state` tinyint(3) unsigned NOT NULL COMMENT '公告状态,1:上架 2:下架',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '公告记录创建者',
  `created_date` datetime NOT NULL COMMENT '公告记录创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '公告记录更新者',
  `update_date` datetime DEFAULT NULL COMMENT '公告记录更新时间',
  PRIMARY KEY (`id`)
) COMMENT='deepwiki公告表';


-- ai_dw_role definition

CREATE TABLE `ai_dw_role` (
  `id` smallint(5) unsigned NOT NULL AUTO_INCREMENT COMMENT '角色标识',
  `role_name` varchar(60) NOT NULL COMMENT '角色名称',
  `role_code` varchar(30) NOT NULL COMMENT '角色编码',
  `role_type` char(1) NOT NULL COMMENT '角色类型 S:系统角色 D:数据角色',
  `access_level` tinyint(3) unsigned NOT NULL COMMENT '权限级别: 数值越大,权限越大',
  `comments` varchar(255) DEFAULT NULL COMMENT '角色描述',
  PRIMARY KEY (`id`)
) COMMENT='deepwiki角色表';


-- ai_dw_role_priv definition

CREATE TABLE `ai_dw_role_priv` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '角色权限唯一标识',
  `role_id` smallint unsigned NOT NULL COMMENT '角色标识',
  `priv_id` int unsigned NOT NULL COMMENT '权限主键标识',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_adrp_rid_pid` (`role_id`,`priv_id`)
) COMMENT='角色服务权限';


-- ai_dw_user definition

CREATE TABLE `ai_dw_user` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户表主键',
  `wcp_user_id` bigint(20) unsigned NOT NULL COMMENT '鲸加用户标识',
  `user_code` varchar(30) NOT NULL COMMENT '用户鲸加工号',
  `user_name` varchar(255) NOT NULL COMMENT '用户姓名',
  `email` varchar(255) DEFAULT NULL COMMENT '用户邮箱',
  `phone` varchar(30) DEFAULT NULL COMMENT '用户电话号码',
  `dept` varchar(120) DEFAULT NULL COMMENT '用户隶属部门',
  `org` varchar(100) DEFAULT NULL COMMENT '用户隶属组织',
  `job` varchar(120) DEFAULT NULL COMMENT '用户职位',
  `dept_id` int(10) unsigned DEFAULT NULL COMMENT '用户隶属部门标识',
  `org_id` int(10) unsigned DEFAULT NULL COMMENT '用户隶属组织标识',
  `job_id` int(10) unsigned DEFAULT NULL COMMENT '用户职位标识',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '用户是否有效,1:有效 0:无效',
  `last_login_date` datetime DEFAULT NULL COMMENT '上次登录时间',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录创建者',
  `created_date` datetime DEFAULT NULL COMMENT '记录创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_adu_wcp_user_id_` (`wcp_user_id`),
  UNIQUE KEY `idx_adu_user_code` (`user_code`)
) COMMENT='deepwiki用户表';


-- ai_dw_user_ext definition

CREATE TABLE `ai_dw_user_ext` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键标识',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户标识',
  `ai_api_key` varchar(255) DEFAULT NULL COMMENT '公司大模型token',
  `dev_cloud_token` varchar(255) DEFAULT NULL COMMENT '研发云token',
  `sandbox_quota` int(10) unsigned DEFAULT NULL COMMENT '个性化沙盒并发配额',
  `linux_gid` int(10) unsigned DEFAULT NULL COMMENT '用户对应linux gid',
  `linux_group_name` varchar(32) DEFAULT NULL COMMENT '用户对应的linux用户组名,使用工号即可',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '记录创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_ai_dw_user_ext_user_id` (`user_id`)
) COMMENT='用户信息扩展表';


-- ai_dw_user_role definition

CREATE TABLE `ai_dw_user_role` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户角色标识',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户标识',
  `role_id` smallint(5) unsigned NOT NULL COMMENT '角色标识',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_ai_dw_user_role_uid_rid` (`user_id`,`role_id`)
) COMMENT='用户角色关联关系表';


-- ai_dw_chat_history definition
CREATE TABLE `ai_dw_chat_history` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键,自增1',
  `msg_sid` varchar(63) DEFAULT NULL COMMENT '大模型生成的id',
  `chat_id` bigint(20) unsigned NOT NULL COMMENT '会话id',
  `role` varchar(30) NOT NULL DEFAULT '' COMMENT '角色',
  `content` longtext COMMENT '消息内容',
  `provider` varchar(60) NOT NULL COMMENT '问答提供商',
  `model` varchar(255) DEFAULT NULL COMMENT 'ai大模型',
  `msg_data` varchar(1000) DEFAULT '' COMMENT 'json格式的额外数据',
  `deep_research` tinyint(3) unsigned DEFAULT '0' COMMENT '是否启用深度搜索,0:否 1:是',
  `deep_research_iter` varchar(30)  DEFAULT NULL COMMENT '深度搜索迭代步骤标识 第一条:start 最后一条:end',
  `parent_id` bigint(20) unsigned DEFAULT NULL COMMENT '助手消息关联的用户消息主键',
  `tool_calls` mediumtext COMMENT '大模型工具调用信息',
  `file_references` TEXT NULL COMMENT '文件/目录引用(JSON)',
  `command_params` mediumtext COMMENT '命令参数(JSON)',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '0:失效 1:有效',
  `qa_src` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '问答来源 1: deepwiki 2: mcp tool',
  `error_code` varchar(30) DEFAULT NULL COMMENT '问答错误码',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '消息归属者',
  `created_date` datetime NOT NULL COMMENT '生成时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '消息更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_adch_msg_sid` (`msg_sid`),
  KEY `idx_adch_chat_id` (`chat_id`)
) COMMENT='会话聊天历史表';


-- ai_dw_chat_session definition
CREATE TABLE `ai_dw_chat_session` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键,自增1',
  `chat_sid` varchar(60) NOT NULL COMMENT '前端使用的会话id',
  `title` varchar(60) DEFAULT NULL COMMENT '会话标题',
  `wiki_id` varchar(32) NOT NULL DEFAULT '' COMMENT 'wiki标识',
  `ip` char(15)  DEFAULT '' COMMENT '用户所在ip',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '0/1/2,删除/创建但无消息/创建已存在消息',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '记录创建者',
  `created_date` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `qa_src` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '问答来源 1: deepwiki 2: mcp tool 3: open api',
  `app_id` int(10) unsigned DEFAULT NULL COMMENT '应用ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_chat_sid` (`chat_sid`),
  KEY `idx_adcs_cid_wid` (`created_by`,`wiki_id`)
) COMMENT='会话表';


-- ai_dw_chat_user_feedback definition
CREATE TABLE `ai_dw_chat_user_feedback` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键,自增1',
  `msg_sid` varchar(63) DEFAULT NULL COMMENT '大模型生成的id',
  `evaluate_type` tinyint(3) unsigned DEFAULT '0' COMMENT '0:NEUTRAL,GOOD:1,BAD:2',
  `feedback` varchar(255) DEFAULT NULL COMMENT '用户反馈',
  `category` varchar(255) DEFAULT NULL COMMENT '点踩时用户选择的选项,多个逗号分隔',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '记录创建者',
  `created_date` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_msg_sid` (`msg_sid`)
) COMMENT='用户反馈表';


-- global_pending_tasks definition

CREATE TABLE `global_pending_tasks` (
  `task_id` varchar(100) NOT NULL,
  `job_id` varchar(100) NOT NULL,
  `priority` int(11) NOT NULL DEFAULT '3',
  `preferred_instance_id` varchar(100) DEFAULT NULL,
  `job_context_json` text NOT NULL,
  `submit_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `retry_count` int(11) DEFAULT '0',
  `last_retry_time` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`task_id`),
  KEY `idx_priority_submit_time` (`priority`,`submit_time`)
) COMMENT='挂起的任务';


-- job_manager_instance definition

CREATE TABLE `job_manager_instance` (
  `instance_id` varchar(100) NOT NULL COMMENT '实例ID,UUID格式',
  `hostname` varchar(255) NOT NULL COMMENT '主机名',
  `pid` int(11) NOT NULL COMMENT '进程ID',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '实例状态：active(活跃), stopped(已停止), crashed(崩溃)',
  `max_concurrent_jobs` int(11) NOT NULL DEFAULT '3' COMMENT '最大并发任务数',
  `current_jobs` int(11) NOT NULL DEFAULT '0' COMMENT '当前处理的任务数',
  `last_heartbeat` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后心跳时间',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`instance_id`),
  KEY `idx_instance_last_heartbeat` (`last_heartbeat`),
  KEY `idx_instance_status` (`status`),
  KEY `idx_hostname_pid` (`hostname`,`pid`),
  KEY `idx_instance_status_heartbeat` (`status`,`last_heartbeat`)
) COMMENT='JobManager实例注册表,用于分布式部署管理';


-- wiki_info definition

-- 1) Wiki 基本信息
CREATE TABLE `ai_dw_wiki_info` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `wiki_id` VARCHAR(64) NOT NULL COMMENT 'Wiki唯一标识（外部可见ID/UUID）',
  `name` VARCHAR(255) NOT NULL COMMENT 'Wiki名称（展示名）',
  `description` TEXT NULL COMMENT 'Wiki描述',
  `wiki_type` TINYINT NOT NULL DEFAULT 1 COMMENT 'Wiki类型: 1=产品 2=项目',
  `project_topic_id` VARCHAR(255) NULL COMMENT '项目级DocChain主题ID（项目topic）',
  `provider` VARCHAR(64) NOT NULL DEFAULT 'google' COMMENT '默认模型提供商',
  `model` VARCHAR(128) NOT NULL DEFAULT 'gemini-pro' COMMENT '默认模型名称',
  `language` VARCHAR(16) NOT NULL DEFAULT 'zh' COMMENT '默认语言',
  `comprehensive` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否生成全面Wiki（1=是 0=否）',
  `excluded_dirs` TEXT NULL COMMENT '全局排除目录，逗号/分号分隔',
  `excluded_files` TEXT NULL COMMENT '全局排除文件，逗号/分号分隔',
  `included_dirs` TEXT NULL COMMENT '全局包含目录，逗号/分号分隔',
  `included_files` TEXT NULL COMMENT '全局包含文件，逗号/分号分隔',
  `visibility` TINYINT UNSIGNED NOT NULL DEFAULT 2 COMMENT '可见性: 1=全员可见 2=仅授权/创建人可见',
  `status` VARCHAR(32) NOT NULL DEFAULT 'active' COMMENT '状态: active/archived/deleted',
  `repo_url` VARCHAR(512) NULL COMMENT '主仓库URL（冗余字段，便于查询）',
  `repo_owner` VARCHAR(255) NULL COMMENT '主仓库所有者（冗余字段）',
  `repo_name` VARCHAR(255) NULL COMMENT '主仓库名称（冗余字段）',
  `branch` VARCHAR(128) NULL COMMENT '主仓库分支（冗余字段）',
  `repo_type` VARCHAR(64) NULL COMMENT '主仓库类型（冗余字段）',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` INT NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `updated_by` INT NOT NULL DEFAULT 0 COMMENT '更新人ID',
  `owner_id` INT NOT NULL DEFAULT 0 COMMENT '拥有者ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_ai_dw_wiki_info_wiki_id` (`wiki_id`),
  UNIQUE KEY `uk_ai_dw_wiki_repo_branch_lang` (`repo_owner`, `repo_name`, `branch`, `language`)
) COMMENT='Wiki 基本信息表（项目/产品级配置与Topic）';

-- 2) Git 仓库信息
CREATE TABLE `ai_dw_git_repository` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `repo_url` VARCHAR(512) NOT NULL COMMENT '仓库URL（含host/owner/repo.git）',
  `branch` VARCHAR(128) NOT NULL DEFAULT 'master' COMMENT '分支',
  `repo_owner` VARCHAR(255) NOT NULL COMMENT '仓库所有者',
  `repo_name` VARCHAR(255) NOT NULL COMMENT '仓库名称',
  `repo_type` VARCHAR(64) NOT NULL DEFAULT 'whaleDevCloud' COMMENT '仓库类型: github/gitlab/bitbucket/whaleDevCloud 等',
  `description` TEXT NULL COMMENT '仓库描述',
  `is_private` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否私有仓库（1=是 0=否）',
  `code_topic_id` VARCHAR(255) NULL COMMENT 'DocChain 代码主题ID（代码topic）',
  `last_sync_time` DATETIME NULL COMMENT '最后同步DocChain时间',
  `last_sync_commit` VARCHAR(64) NULL COMMENT '最后同步到DocChain的commit哈希',
  `file_count` INT NOT NULL DEFAULT 0 COMMENT '仓库文件数（用于与DocChain索引数比对）',
  `doc_total_files` INT NOT NULL DEFAULT 0 COMMENT 'DocChain Topic文件总数（缓存）',
  `doc_pending_files` INT NOT NULL DEFAULT 0 COMMENT 'DocChain Topic待处理文件数（缓存）',
  `status` VARCHAR(32) NOT NULL DEFAULT 'active' COMMENT '状态: active/archived',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `linux_gid` int(10) unsigned DEFAULT NULL,
  `linux_group_name` varchar(32) DEFAULT NULL,
  `linux_code_file_perm` tinyint(1) NOT NULL DEFAULT '0',
  `linux_pw_file_perm` tinyint(1) NOT NULL DEFAULT '0',
  `linux_uw_file_perm` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_ai_dw_git_repo_url_branch` (`repo_url`, `branch`)
) COMMENT='Git 仓库信息表（承载代码topic，可被多个Wiki复用）';

-- 3) Wiki 与 仓库 关联
CREATE TABLE `ai_dw_wiki_repository_relation` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `wiki_id` VARCHAR(64) NOT NULL COMMENT 'Wiki唯一标识（引用逻辑层面的 ai_dw_wiki_info.wiki_id）',
  `repository_id` INT UNSIGNED NOT NULL COMMENT 'Git仓库ID（引用逻辑层面的 ai_dw_git_repository.id）',
  `is_main_repo` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否主仓库（1=主 0=子）',
  `excluded_dirs` TEXT NULL COMMENT '该仓库排除目录（覆盖Wiki级配置）',
  `excluded_files` TEXT NULL COMMENT '该仓库排除文件（覆盖Wiki级配置）',
  `included_dirs` TEXT NULL COMMENT '该仓库包含目录（覆盖Wiki级配置）',
  `included_files` TEXT NULL COMMENT '该仓库包含文件（覆盖Wiki级配置）',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_ai_dw_wiki_repo_relation` (`wiki_id`, `repository_id`),
  KEY `idx_ai_dw_wrr_wiki_main` (`wiki_id`, `is_main_repo`)
) COMMENT='Wiki 与 Git 仓库关联表（主/子仓库、多仓库复用。无外键约束，业务侧保证一致性）';

-- 4) Wiki 内容
CREATE TABLE `ai_dw_wiki_content` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `wiki_id` VARCHAR(64) NOT NULL COMMENT 'Wiki唯一标识（与 ai_dw_wiki_info.wiki_id 一致）',
  `wiki_structure` JSON NULL COMMENT 'Wiki结构数据（XML/LLM解析结果映射）',
  `wiki_pages` JSON NULL COMMENT 'Wiki页面内容（page_id -> content）',
  `version` INT NOT NULL DEFAULT 1 COMMENT '内容版本号（预留）',
  `generation_info` JSON NULL COMMENT '生成元信息（模型/参数等）',
  `total_pages` INT NOT NULL DEFAULT 0 COMMENT '总页面数（冗余统计）',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_ai_dw_content_wiki_id` (`wiki_id`)
) COMMENT='Wiki 内容表（结构与页面JSON大字段独立存储。无外键约束）';

-- wiki_job definition

CREATE TABLE `wiki_job` (
  `id` varchar(36) NOT NULL COMMENT '任务ID,UUID格式',
  `job_type` TINYINT UNSIGNED DEFAULT 0 COMMENT '任务类型：0(首次生成), 1(刷新更新), 2(同步索引)',
  `status` varchar(50) NOT NULL DEFAULT 'pending' COMMENT '任务状态：pending(待处理), pending_resume(等待恢复), processing(处理中), resuming(恢复中), completed(已完成), failed(失败), cancelled(已取消), timeout(超时)',
  `stage` varchar(50) DEFAULT NULL COMMENT '任务阶段：init(初始化), download(下载), upload(上传), structure(结构生成), pages(页面生成), completed(已完成)',
  `stage_progress` int(11) DEFAULT '0' COMMENT '当前阶段的进度百分比(0-100)',
  `stage_message` varchar(255) DEFAULT NULL COMMENT '当前阶段的状态信息',
  `progress` int(11) DEFAULT '0' COMMENT '整体进度百分比(0-100)',
  `total_files` int(11) DEFAULT NULL COMMENT '总文件数',
  `processed_files` int(11) DEFAULT NULL COMMENT '已处理文件数',
  `error_message` text COMMENT '错误信息',
  `repo_url` varchar(255) NOT NULL COMMENT '代码仓库URL',
  `branch` varchar(100) DEFAULT 'main' COMMENT '代码仓库分支',
  `token` varchar(255) DEFAULT NULL COMMENT '访问令牌(如果需要认证)',
  `language` varchar(10) DEFAULT 'zh' COMMENT 'Wiki语言',
  `comprehensive` tinyint(1) DEFAULT '1' COMMENT '是否生成全面的Wiki(1:是,0:否)',
  `excluded_dirs` text COMMENT '排除的目录列表,逗号分隔',
  `excluded_files` text COMMENT '排除的文件列表,逗号分隔',
  `included_dirs` text COMMENT '包含的目录列表,逗号分隔',
  `included_files` text COMMENT '包含的文件列表,逗号分隔',
  `topic_id` varchar(100) DEFAULT NULL COMMENT '兼容旧版本的单个topic ID',
  `topic_id_code` varchar(100) DEFAULT NULL COMMENT '代码部分的topic ID',
  `topic_id_doc` varchar(100) DEFAULT NULL COMMENT '文档部分的topic ID',
  `wiki_info_id` varchar(36) DEFAULT NULL COMMENT '关联的WikiInfo记录ID',
  `model_settings` json DEFAULT NULL COMMENT '模型配置信息,包括提供商、模型名称等',
  `result` json DEFAULT NULL COMMENT '任务结果数据',
  `created_by` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `updated_by` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新人ID',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `sub_repos` varchar(255) DEFAULT NULL COMMENT '子仓库信息',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_repo_url` (`repo_url`),
  KEY `idx_created_time` (`created_time`)
) COMMENT='Wiki生成任务表';


-- global_job_state definition

CREATE TABLE `global_job_state` (
  `job_id` varchar(100) NOT NULL COMMENT '任务ID',
  `processing_instance_id` varchar(100) DEFAULT NULL COMMENT '当前处理该任务的实例ID',
  `global_status` varchar(50) NOT NULL COMMENT '全局状态：available(可用), locked(已锁定), processing(处理中), completed(已完成), failed(失败)',
  `last_processing_instance` varchar(100) DEFAULT NULL COMMENT '最后处理该任务的实例ID',
  `job_metadata` text COMMENT '元数据,JSON格式',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`job_id`),
  KEY `idx_global_job_state_status` (`global_status`),
  KEY `idx_processing_instance` (`processing_instance_id`),
  KEY `fk_global_job_last_instance` (`last_processing_instance`),
  KEY `idx_global_status_instance` (`global_status`,`processing_instance_id`),
  CONSTRAINT `fk_global_job_last_instance` FOREIGN KEY (`last_processing_instance`) REFERENCES `job_manager_instance` (`instance_id`) ON DELETE SET NULL,
  CONSTRAINT `fk_global_job_processing_instance` FOREIGN KEY (`processing_instance_id`) REFERENCES `job_manager_instance` (`instance_id`) ON DELETE SET NULL
) COMMENT='全局任务状态表,用于跨实例状态同步';


-- job_lock definition

CREATE TABLE `job_lock` (
  `job_id` varchar(100) NOT NULL COMMENT '任务ID',
  `instance_id` varchar(100) NOT NULL COMMENT '持有锁的实例ID',
  `hostname` varchar(255) NOT NULL COMMENT '持有锁的主机名',
  `operation` varchar(50) NOT NULL COMMENT '操作类型：submit(提交), process(处理), recover(恢复), cancel(取消)',
  `acquired_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获取锁的时间',
  `expires_at` datetime NOT NULL COMMENT '锁过期时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`job_id`),
  KEY `idx_job_lock_expires_at` (`expires_at`),
  KEY `idx_job_lock_instance_id` (`instance_id`),
  KEY `idx_job_lock_hostname` (`hostname`),
  KEY `idx_job_lock_expires_instance` (`expires_at`,`instance_id`),
  CONSTRAINT `fk_job_lock_instance` FOREIGN KEY (`instance_id`) REFERENCES `job_manager_instance` (`instance_id`) ON DELETE CASCADE
) COMMENT='任务锁表,用于分布式任务管理';

-- 8.7
CREATE TABLE `ai_dw_wiki_user_role` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'wiki-user-role主键',
  `wiki_id` bigint(20) unsigned NOT NULL COMMENT 'wiki标识',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户标识',
  `role_id` bigint(20) unsigned NOT NULL COMMENT '角色标识',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '记录创建者',
  `created_date` datetime NOT NULL COMMENT '记录创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_adwur_uid_wid_rid` (`user_id`,`wiki_id`,`role_id`)
) COMMENT='wiki用户角色表';

-- 权限表
-- ai_dw_priv definition
CREATE TABLE `ai_dw_priv` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '权限主键标识',
  `priv_type` char(1) NOT NULL COMMENT '权限类型,S:服务权限,C:组件权限',
  `priv_code` varchar(30) NOT NULL COMMENT '权限编码',
  `priv_name` varchar(120) NOT NULL COMMENT '权限名称',
  `priv_el` varchar(1000) DEFAULT NULL COMMENT '权限表达式',
  `state` tinyint(3) unsigned NOT NULL COMMENT '权限状态:0: 失效 1: 有效 ',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '记录创建者',
  `created_date` datetime NOT NULL COMMENT '记录创建日期',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新日期',
  PRIMARY KEY (`id`)
) COMMENT='deepwiki权限表';

-- 操作日志
-- ai_dw_serv_log definition

CREATE TABLE `ai_dw_serv_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志记录唯一标识，自增主键',
  `event_src` varchar(60)  NOT NULL DEFAULT 'deepwiki' COMMENT '事件来源系统,默认deepwiki',
  `event_type` varchar(255)  NOT NULL COMMENT '事件类型分类',
  `event_code` varchar(255)  NOT NULL COMMENT '事件编码/操作编码',
  `party_type` varchar(30)  NOT NULL COMMENT '被操作对象的类型(user,wiki)',
  `party_code` varchar(255)  DEFAULT NULL COMMENT '被操作对象的唯一编码',
  `party_name` varchar(60)  DEFAULT NULL COMMENT '被操作对象的名称',
  `party_id` varchar(60)  NOT NULL COMMENT '被操作对象的ID(如:用户ID或者wiki id)',
  `oper_id` bigint(20) unsigned NOT NULL COMMENT '操作人ID',
  `dept_id` int(10) unsigned DEFAULT NULL COMMENT '操作人所属部门ID',
  `dept_name` varchar(120)  DEFAULT NULL COMMENT '操作人所属部门名称',
  `oper_data` text  DEFAULT NULL COMMENT '操作数据',
  `src_ip` varchar(60)  DEFAULT NULL COMMENT '来源IP地址',
  `server_ip` varchar(60)  DEFAULT NULL COMMENT '服务器IP地址',
  `is_success` tinyint(3) unsigned NOT NULL COMMENT '是否成功(1成功/0失败)',
  `log_date` datetime NOT NULL COMMENT '日志记录时间',
  `comments` varchar(1000)  DEFAULT NULL COMMENT '操作备注/详细说明',
  PRIMARY KEY (`id`),
  KEY `idx_adsl_oper_id_log_date` (`oper_id`,`log_date`)
) COMMENT='系统服务操作日志表';

-- ai_dw_event_code definition

CREATE TABLE `ai_dw_event_code` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `event_code` varchar(255)  NOT NULL COMMENT '事件唯一编码',
  `event_type` varchar(255)  DEFAULT NULL COMMENT '事件类型分类',
  `event_src_code` varchar(60)  DEFAULT 'deepwiki' COMMENT '事件来源系统编码',
  `is_audit` tinyint(3) unsigned DEFAULT NULL COMMENT '是否需要审计(1是/0否)',
  `comments` varchar(255)  DEFAULT NULL COMMENT '事件描述说明',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_ad_ec_event_code` (`event_code`)
) COMMENT='事件编码配置表';

-- ai_dw_tag definition

CREATE TABLE `ai_dw_tag` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `name` varchar(60)  NOT NULL COMMENT '标签名称',
  `type` tinyint(3) unsigned NOT NULL COMMENT '标签类型（1:系统 2:用户）',
  `color` varchar(16)  NOT NULL COMMENT '标签颜色（如#FF0000）',
  `comments` varchar(255)  DEFAULT NULL COMMENT '标签描述',
  `module_type` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '标签归属模块, 1:deepwiki',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '标签状态, 1:有效 0:失效',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '创建人ID',
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '修改人ID',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_adt_created_by_name` (`created_by`,`name`)
) COMMENT='标签表';

-- ai_dw_wiki_tag definition

CREATE TABLE `ai_dw_wiki_tag` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '关联关系ID',
  `wiki_id` bigint(20) unsigned NOT NULL COMMENT 'wiki主键ID',
  `tag_id` bigint(20) unsigned NOT NULL COMMENT '标签ID',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '创建人ID',
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '更新人ID',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_wiki_id` (`wiki_id`),
  KEY `idx_tag_id` (`tag_id`)
) COMMENT='wiki与标签多对多关联表';

-- ai_dw_wiki_dc_ext_1 definition

CREATE TABLE `ai_dw_wiki_dc_ext_1` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'wiki扩展信息表主键标识',
  `wiki_id` bigint(20) unsigned NOT NULL COMMENT 'wiki标识',
  `dc_repo_id` bigint(20) unsigned DEFAULT NULL COMMENT '研发云仓库ID',
  `dc_project_id` int(10) unsigned DEFAULT NULL COMMENT '研发云项目ID',
  `branch_version_id` int(10) unsigned DEFAULT NULL COMMENT '分支版本ID',
  `branch_version_name` varchar(1000)  DEFAULT NULL COMMENT '分支版本名称',
  `product_version_id` int(10) unsigned DEFAULT NULL COMMENT '产品版本ID',
  `product_version_code` varchar(1000)  DEFAULT NULL COMMENT '产品版本编码',
  `product_name` varchar(1000)  DEFAULT NULL COMMENT '产品名称',
  `product_id` bigint(20) unsigned DEFAULT NULL COMMENT '产品ID',
  `product_line_id` bigint(20) unsigned DEFAULT NULL COMMENT '产品线ID',
  `product_line_name` varchar(1000)  DEFAULT NULL COMMENT '产品线名称',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '记录创建者',
  `created_date` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_adwe_wiki_id` (`wiki_id`)
) COMMENT='wiki仓库关联的研发云扩展信息';


-- ai_dw_wiki_dc_ext_2 definition

CREATE TABLE `ai_dw_wiki_dc_ext_2` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'wiki扩展信息表主键标识',
  `wiki_id` bigint(20) unsigned NOT NULL COMMENT 'wiki标识',
  `release_pkg_id` int(10) unsigned DEFAULT NULL COMMENT '发布包ID',
  `release_pkg_code` varchar(1000)  DEFAULT NULL COMMENT '发布包编码',
  `solution_name` varchar(1000)  DEFAULT NULL COMMENT '解决方案名称',
  `solution_id` bigint(20) unsigned DEFAULT NULL COMMENT '解决方案ID',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '记录创建者',
  `created_date` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新者',
  PRIMARY KEY (`id`),
  KEY `idx_adwdce_wiki_id` (`wiki_id`)
) COMMENT='wiki仓库关联的研发云扩展信息:发布包解决方案';


-- ai_dw_api_def definition

CREATE TABLE `ai_dw_api_def` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `api_code` varchar(30) NOT NULL COMMENT 'API编码，全局唯一',
  `api_name` varchar(60) NOT NULL COMMENT 'API名称',
  `api_path` varchar(60) NOT NULL COMMENT 'API路径',
  `api_method` varchar(20) NOT NULL COMMENT 'HTTP方法：GET/POST/PUT/DELETE',
  `comments` varchar(255) DEFAULT NULL COMMENT 'API描述',
  `category` varchar(30) DEFAULT NULL COMMENT 'API分类',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_api_code` (`api_code`),
  KEY `idx_adad_state` (`state`)
) COMMENT='API接口定义表';


-- ai_dw_app definition

CREATE TABLE `ai_dw_app` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` varchar(60) NOT NULL COMMENT '应用ID，全局唯一',
  `app_secret` varchar(120) NOT NULL COMMENT '应用密钥(加密存储)',
  `app_code` varchar(60) NOT NULL COMMENT '应用编码，全局唯一',
  `app_name` varchar(60) NOT NULL COMMENT '应用名称',
  `comments` varchar(255) DEFAULT NULL COMMENT '应用描述',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '创建人工号',
  `created_date` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '更新人工号',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_ada_app_id` (`app_id`),
  UNIQUE KEY `uk_ada_app_code` (`app_code`),
  KEY `idx_ada_state` (`state`)
) COMMENT='应用信息表';


-- ai_dw_app_access_token definition

CREATE TABLE `ai_dw_app_access_token` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `token` varchar(255) NOT NULL COMMENT 'Access Token',
  `app_id` int(10) unsigned NOT NULL COMMENT '应用ID',
  `token_type` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT 'Token类型：1-系统级，2-用户级 用户级要求调用接口时必须携带用户信息',
  `effective_at` datetime DEFAULT NULL COMMENT 'token生效时间，为空表示立即生效',
  `expires_at` datetime DEFAULT NULL COMMENT '过期时间，NULL表示永久有效',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：0-无效，1-有效',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录创建者id',
  `created_date` datetime NOT NULL COMMENT '记录创建时间',
  `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者ID',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_adat_token` (`token`),
  KEY `idx_asat_app_id` (`app_id`)
) COMMENT='Token管理表';


-- ai_dw_app_api_rel definition

CREATE TABLE `ai_dw_app_api_rel` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` int(10) unsigned NOT NULL COMMENT '应用ID',
  `api_id` int(10) unsigned NOT NULL COMMENT 'API ID',
  `state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '授权人ID',
  `created_date` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者ID',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_api` (`app_id`,`api_id`)
) COMMENT='应用API权限关联表';


-- ai_dw_app_approval_records definition

CREATE TABLE `ai_dw_app_approval_records` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` int(10) unsigned NOT NULL COMMENT '应用主键ID',
  `applicant_id` bigint(20) unsigned NOT NULL COMMENT '申请人主键标识',
  `approver_id` bigint(20) unsigned DEFAULT NULL COMMENT '审批人主键标识',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '审批状态：0-待审批，1-通过，2-拒绝',
  `comments` varchar(255) DEFAULT NULL COMMENT '审批意见',
  `applied_time` datetime NOT NULL COMMENT '申请时间',
  `approved_time` datetime DEFAULT NULL COMMENT '审批时间',
  PRIMARY KEY (`id`),
  KEY `idx_adaar_app_id` (`app_id`)
) COMMENT='应用审批记录表';

-- ai_dw_project definition

CREATE TABLE `ai_dw_project` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '项目表主键',
  `wiki_id` bigint(20) unsigned NOT NULL COMMENT 'wiki_info表主键id',
  `project_code` varchar(60) NOT NULL COMMENT '项目编码',
  `project_name` varchar(120) NOT NULL COMMENT '项目名称',
  `pm_id` bigint(20) unsigned DEFAULT NULL COMMENT '项目负责人ID',
  `pm_name` varchar(120) NOT NULL COMMENT '项目负责人名称',
  `created_by` bigint(20) unsigned NOT NULL COMMENT '记录创建者主键ID',
  `created_date` datetime NOT NULL COMMENT '记录创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT '记录更新者主键ID',
  `update_date` datetime DEFAULT NULL COMMENT '记录更新者',
  PRIMARY KEY (`id`),
  KEY `idx_adp_wiki_id` (`wiki_id`)
) COMMENT='项目表';

-- ai_dw_app_token_user_rel definition
CREATE TABLE `ai_dw_app_token_user_rel` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'token与user关联关系表主键',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户表主键',
  `token_id` int(10) unsigned NOT NULL COMMENT '应用token表主键',
  `created_by` int(10) unsigned NOT NULL COMMENT '记录创建者标识',
  `created_date` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_adatur_tid_uid` (`token_id`,`user_id`)
) COMMENT='用户级token与用户关联关系表';

-- ai_dw_user_access_token definition
CREATE TABLE `ai_dw_user_access_token` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户Access Token表主键',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户主键标识',
  `name` varchar(120) NOT NULL COMMENT 'token名称',
  `token` varchar(255) NOT NULL COMMENT '用户token, 需加密',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT 'Token状态：0: 禁用 1: 有效',
  `use_type` varchar(60) DEFAULT NULL COMMENT 'Token使用类型,如cherry/cursor/qshell等',
  `effective_at` datetime DEFAULT NULL COMMENT 'Token生效日期,为空表示立即生效',
  `expires_at` datetime DEFAULT NULL COMMENT 'Token过期时间,为空表示永久有效',
  `created_by` bigint(20) unsigned NOT NULL COMMENT 'Token创建者主键标识',
  `created_date` datetime NOT NULL COMMENT 'Token创建时间',
  `update_by` bigint(20) unsigned DEFAULT NULL COMMENT 'Token更新者主键标识',
  `update_date` datetime DEFAULT NULL COMMENT 'Token更新时间',
  `last_used_time` datetime DEFAULT NULL COMMENT 'Token最后使用时间',
  PRIMARY KEY (`id`),
  KEY `idx_aduat_user_id` (`user_id`),
  KEY `idx_aduat_token` (`token`)
) COMMENT='用户Access Token表';

CREATE TABLE `ai_dw_app_wiki_rela` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` BIGINT UNSIGNED NOT NULL COMMENT '应用主键ID，关联 ai_dw_app.id',
  `wiki_id` BIGINT UNSIGNED NOT NULL COMMENT 'Wiki 主键ID，关联 ai_dw_wiki_info.id',
  `created_by` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `created_date` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT UNSIGNED DEFAULT NULL COMMENT '更新人ID',
  `update_date` DATETIME DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uix_ai_dw_app_wiki_rela` (`app_id`, `wiki_id`),
  KEY `idx_ai_dw_app_wiki_rela_app_id` (`app_id`),
  KEY `idx_ai_dw_app_wiki_rela_wiki_id` (`wiki_id`)
) COMMENT='应用与Wiki绑定关系表';
