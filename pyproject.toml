[project]
name = "deepwiki-open"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
  "fastapi>=0.103.0",
  "uvicorn[standard]>=0.21.1",
  "websockets>=11.0.3",
  "pydantic>=2.0.0",
  "google-generativeai>=0.3.0",
  "tiktoken>=0.5.0",
  "adalflow>=0.1.0",
  "numpy>=1.24.0",
  "faiss-cpu>=1.7.4",
  "langid>=1.1.6",
  "requests>=2.28.0",
  "jinja2>=3.1.2",
  "python-dotenv>=1.0.0",
  "openai>=1.76.2",
  "langfuse>=2.39.0",
  "ollama>=0.4.8",
  "aiohttp>=3.13.0",
  "boto3>=1.34.0",
  "pytest>=7.0.0", # 添加httpx依赖，用于gemini-cli集成
  "httpx>=0.25.0",
  "pyjwt>=2.10.1",
  "pyyaml>=6.0",
  "sqlmodel==0.0.24",
  "pymysql==1.1.2",
  "cryptography>=45.0.5",
  "fastapi-mcp>=0.3.7",
  "setuptools>=80.9.0",
  "kubernetes>=29.0.0",
  "redis>=5.0.0"
]

[[tool.uv.index]]
url = "https://mirrors.aliyun.com/pypi/simple"
default = true
