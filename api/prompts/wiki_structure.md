Analyze this GitHub repository $owner/$repo and create a wiki structure for it.

1. The complete file tree of the project:
<file_tree>
$fileTree
</file_tree>

2. The README file of the project:
<readme>
$readme
</readme>

I want to create a wiki for this repository. Determine the most logical structure for a wiki based on the repository's content.

IMPORTANT ANALYSIS GUIDELINES:  
- When analyzing the README file, pay special attention to ALL major functional modules or platforms mentioned in sections like "核心功能" (Core Features)  
- Ensure each distinct functional area  gets its own dedicated page  
- If the README describes multiple independent platforms/centers, create separate pages for each one  
- Do not merge or omit any major functional areas mentioned in the documentation  
- Look for module lists and component hierarchies to ensure comprehensive coverage  

IMPORTANT: The wiki content will be generated in $language language.

When designing the wiki structure, include pages that would benefit from visual diagrams, such as:
- Architecture overviews
- Data flow descriptions
- Component relationships
- Process workflows
- State machines
- Class hierarchies

{%if comprehensive%}
Create a structured wiki with the following main sections:
- Overview (general information about the project)
- System Architecture (how the system is designed)
- Core Features (key functionality)
- Data Management/Flow: If applicable, how data is stored, processed, accessed, and managed (e.g., database schema, data pipelines, state management).
- Frontend Components (UI elements, if applicable.)
- Backend Systems (server-side components)
- Model Integration (AI model connections)
- Deployment/Infrastructure (how to deploy, what's the infrastructure like)
- Extensibility and Customization: If the project architecture supports it, explain how to extend or customize its functionality (e.g., plugins, theming, custom modules, hooks).

Each section should contain relevant pages. For example, the "Frontend Components" section might include pages for "Home Page", "Repository Wiki Page", "Ask Component", etc.  
  
IMPORTANT ANALYSIS GUIDELINES:    
- When analyzing the README file, pay special attention to ALL major functional modules or platforms mentioned in sections like "核心功能" (Core Features)    
- For the Core Features section specifically, ensure each major functional module mentioned in the README gets its own dedicated page  
- If the README describes multiple independent platforms/centers, create separate pages for each one    
- Do not merge or omit any major functional areas mentioned in the documentation    
- Look for module lists and component hierarchies to ensure comprehensive coverage  
- If the prompt contains <readme></readme> tags, the Core Features section structure should be primarily based on the README file content and supplemented with relevant details from the file tree  

Return your analysis in the following XML format:

<wiki_structure>
  <title>[Overall title for the wiki]</title>
  <description>[Brief description of the repository]</description>
  <sections>
    <section id="section-1">
      <title>[Section title]</title>
      <pages>
        <page_ref>page-1</page_ref>
        <page_ref>page-2</page_ref>
      </pages>
      <subsections>
        <section_ref>section-2</section_ref>
      </subsections>
    </section>
    <!-- More sections as needed -->
  </sections>
  <pages>
    <page id="page-1">
      <title>[Page title]</title>
      <description>[Brief description of what this page will cover]</description>
      <importance>high|medium|low</importance>
      <relevant_files>
        <file_path>[Path to a relevant file from the file tree]</file_path>
        <file_path>[Another relevant file path]</file_path>
        <!-- Add 8-10 relevant file paths that are essential for this page content -->
      </relevant_files>
      <related_pages>
        <related>page-2</related>
        <!-- More related page IDs as needed -->
      </related_pages>
      <parent_section>section-1</parent_section>
    </page>
    <!-- More pages as needed -->
  </pages>
</wiki_structure>
{%else%}
Return your analysis in the following XML format:

<wiki_structure>
  <title>[Overall title for the wiki]</title>
  <description>[Brief description of the repository]</description>
  <pages>
    <page id="page-1">
      <title>[Page title]</title>
      <description>[Brief description of what this page will cover]</description>
      <importance>high|medium|low</importance>
      <relevant_files>
        <file_path>[Path to a relevant file from the file tree]</file_path>
        <file_path>[Another relevant file path]</file_path>
        <!-- Add 8-10 relevant file paths that are essential for this page content -->
      </relevant_files>
      <related_pages>
        <related>page-2</related>
        <!-- More related page IDs as needed -->
      </related_pages>
    </page>
    <!-- More pages as needed -->
  </pages>
</wiki_structure>
{%endif%}

CRITICAL FILE CONSTRAINT:  
The relevant_files should ONLY contain actual files from the provided file_tree. DO NOT infer, guess, or create file paths that are not explicitly listed in the <file_tree> section.
Before finalizing your XML output, verify that every <file_path> entry exists in the provided <file_tree> section.

IMPORTANT FILE SELECTION GUIDELINES:
- For each page, select 10-20 files that are most relevant to the page content
- Prioritize source code files, configuration files, and documentation files that directly relate to the page topic
- Include main implementation files, interfaces, and key dependencies
- For architecture pages: include main entry points, configuration files, and core modules
- For feature pages: include the specific implementation files for that feature
- For API pages: include the API definition files, handlers, and related models

IMPORTANT FORMATTING INSTRUCTIONS:
- Return ONLY the valid XML structure specified above
- DO NOT wrap the XML in markdown code blocks (no ``` or ```xml)
- DO NOT include any explanation text before or after the XML
- Ensure the XML is properly formatted and valid
- Start directly with <wiki_structure> and end with </wiki_structure>

IMPORTANT:
1. Create $pageCount pages that would make a $viewType wiki for this repository
2. Each page should focus on a specific aspect of the codebase (e.g., architecture, key features, setup)
3. The relevant_files should be actual files from the repository that would be used to generate that page
4. **CRITICAL**: For each page, carefully select the MOST RELEVANT 10-20 files from the file_tree that contain the core information needed for that page topic
5. Ensure file paths are complete and accurate as they appear in the file_tree
6. Return ONLY valid XML with the structure specified above, with no markdown code block delimiters
7. The generated content MUST NOT contain any of the following terms: ["ZTE", "ZTE Soft", "中兴软创"] 