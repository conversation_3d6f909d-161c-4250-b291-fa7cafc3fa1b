{"common": {"success": "success", "error": "error", "message": "message", "failed": "failed"}, "api": {"errors": {"noRepoUrl": "No repo_url provided. Please provide a 'repo_url' query parameter.", "invalidWhaleDevCloudUrl": "Invalid whaleDevCloud URL format", "errorProcessingWhaleDevCloud": "Error processing whaleDevCloud repository", "noPathProvided": "No path provided. Please provide a 'path' query parameter.", "directoryNotFound": "Directory not found: {path}", "errorProcessingLocalRepo": "Error processing local repository", "errorInFileAction": "Error in file action", "errorExportingWiki": "Error exporting wiki", "errorRetrievingWikiData": "Error retrieving wiki data from database", "failedToSaveWikiCache": "Failed to save wiki cache", "languageNotSupported": "Language is not supported", "authorizationCodeInvalid": "Authorization code is invalid", "failedToDeleteWikiCache": "Failed to delete wiki cache file", "wikiCacheNotFound": "Wiki cache not found in file system or database", "gitUrlFormatIncorrect": "Git URL format incorrect: {message}", "gitCloneFailed": "Repository clone failed: {reason}", "gitBranchNotFound": "Remote branch {branch} does not exist. Confirm the branch name or create it on the remote.", "gitAuthFailed": "Repository authentication failed. Please check access token or credentials.", "gitRepoNotFound": "Repository not found. Please verify the repository URL or your access permissions.", "gitPermissionDenied": "Access to the repository was denied. Ensure your token has the required permissions or the repo is visible.", "gitResolveHostFailed": "Could not resolve repository host. Check network or repository address.", "gitConnectFailed": "Failed to connect to repository. Check your network connection or try again later.", "gitTimeout": "Connection to repository timed out. Please retry after verifying your network.", "gitUnknownError": "An unknown error occurred while downloading the repository. Details: {detail}", "noPermissionToGenerateWiki": "You don't have permission to generate wiki, super admin or repo admin role required", "noPermissionToExportWiki": "You don't have permission to export this wiki, repo admin role required", "noPermissionToRefreshWiki": "You don't have permission to refresh this wiki, must be wiki owner and repo admin", "noPermissionToAccessWiki": "You don't have permission to access this wiki", "noPermissionToDeleteProject": "You don't have permission to delete this project", "noPermissionToEditWiki": "You don't have permission to edit this wiki, must be wiki owner and repo admin", "authenticationRequired": "Authentication required", "wikiNotExist": "Wiki does not exist, ID: {wiki_id}", "jobManagerNotStarted": "Job manager not started", "taskSubmissionFailed": "Task submission failed, system may be busy", "wikiNotFound": "Wiki not found", "projectNotFound": "Project not found", "chatSessionNotFound": "Chat session not found", "failedToGetChatHistory": "Failed to get chat history", "sessionIdRequired": "session_id parameter is required", "failedToUpdateChatSessionTitle": "Failed to update chat session title", "failedToDeleteChatSession": "Failed to delete chat session", "taskSubmitToJobManagerFailed": "Task submission to JobManager failed", "errorStartingWikiGeneration": "Error starting wiki generation", "errorStartingWikiRefresh": "Error starting wiki refresh", "failedToGetWikiInfo": "Failed to get wiki information", "failedToGetWikiProjects": "Failed to get wiki project list", "failedToDeleteWikiProject": "Failed to delete wiki project", "failedToGetUserList": "Failed to get user list", "failedToUpdateUserState": "Failed to update user status", "failedToGetRoleList": "Failed to get role list", "failedToGetDepartmentList": "Failed to get department list", "failedToGetUserRoles": "Failed to get user role list", "failedToModifyUserRoles": "Failed to modify user roles", "failedToGrantBatch": "Batch authorization failed", "authorizationFailed": "Authorization failed", "failedToModifyUserRole": "Failed to modify user role", "failedToQueryRepoCount": "Failed to query deepwiki integrated repository count", "failedToUpdateWikiInfo": "Failed to update wiki information", "failedToGetTransferableUsers": "Failed to get transferable user list", "notWikiOwner": "You are not the current wiki owner, cannot perform 'transfer ownership' operation", "failedToModifyWikiOwner": "Failed to modify wiki owner, cannot find wiki by wiki_id[{wiki_id}]", "failedToModifyWikiOwnerGeneral": "Failed to modify wiki owner", "failedToGetGrantedUsers": "Failed to get granted user list", "sharePageError": "Share page error", "tokenErrorWrongPage": "Token error, accessed wrong page", "authorizationFailedNoCode": "Authorization failed: No authorization code obtained", "userInvalidOrDisabled": "User is invalid or disabled", "getSSOTokenFailed": "Failed to get SSO token", "unknownError": "Unknown error", "internalErrorContactAdmin": "Internal Error: Please contact administrator", "generatorConfigNotLoaded": "Generator configuration could not be loaded", "internalServerErrorModelConfig": "Internal server error while fetching model configuration", "failedToGetActiveJobs": "Failed to get active jobs list", "failedToGetPendingJobs": "Failed to get pending jobs list", "failedToGetFailedJobs": "Failed to get failed jobs list", "taskSubmittedForRetry": "Task submitted for retry, JobManager will handle automatically", "taskRetryFailed": "Task retry failed, please check task status", "retryTaskFailed": "Retry task failed", "failedToGetJobStatus": "Failed to get job status", "taskCancelled": "Task cancelled", "cancelTaskFailed": "Cancel task failed", "failedToGetCancelledJobs": "Failed to get cancelled jobs list", "taskPaused": "Task paused", "pauseTaskFailed": "Pause task failed", "taskResumed": "Task resumed", "resumeTaskFailed": "Resume task failed", "taskRestarted": "Task restarted", "restartTaskFailed": "Restart task failed", "failedToGetGlobalTaskStatus": "Failed to get global task status", "deadProcessInstancesCleared": "Dead process instances cleared", "failedToClearDeadInstances": "Failed to clear dead instances", "instanceNotExists": "Instance does not exist", "instanceForceDeleted": "Instance {instance_id} has been force deleted", "failedToForceDeleteInstance": "Failed to force delete instance", "failedToGetCleanupStatus": "Failed to get cleanup status", "taskNotExists": "Task {job_id} does not exist", "taskCannotBeDeleted": "Can only delete tasks with status {allowed_statuses}, current status: {status}", "taskIsActiveCannotDelete": "Task is running or paused, cannot delete", "taskDeletedSuccessfully": "Task {job_id} deleted successfully", "relatedWikiInfoRetained": "Related WikiInfo record is retained, only task execution record deleted", "deleteTaskFailed": "Delete task failed", "taskOnlyCancelledOrFailedCanDelete": "Can only delete cancelled or failed tasks, current status: {status}", "taskAndWikiDeletedSuccessfully": "Task {job_id} and its related Wiki deleted successfully", "deleteTaskAndWikiFailed": "Delete task and Wiki failed", "taskForceDeleted": "Task {job_id} has been force deleted", "adminOperationForceDeleted": "This is an admin operation, task record has been force deleted", "forceDeleteTaskFailed": "Force delete task failed", "failedToGetDeletableJobs": "Failed to get deletable jobs list", "failedToGetReassignmentStatus": "Failed to get task reassignment status", "taskGlobalStateNotExists": "Global state for task {job_id} does not exist", "taskCompletedCannotReassign": "Task {job_id} is {status}, cannot reassign", "adminForceReassignment": "Admin force reassignment", "taskForceReassigned": "Task {job_id} has been force reassigned", "taskWillBeReassignedNextHeartbeat": "Task will be picked up by other instances in the next heartbeat cycle", "forceReassignTaskFailed": "Force reassign task failed", "failedToGetManagerElectionStatus": "Failed to get manager election status", "failedToGetJobManagerStatus": "Failed to get JobManager status", "validateTaskAtomicityFailed": "Validate task atomicity failed", "validateInstanceAtomicityFailed": "Validate instance atomicity failed", "noActiveInstancesForElection": "No active instances available for election", "electionFailed": "Election failed, could not elect a manager instance", "electionCompleted": "Election completed", "forceElectionFailed": "Force election failed"}, "messages": {"fileOpenedSuccessfully": "File opened successfully.", "connectionSuccessful": "Connection successful", "verifySuccessful": "Verify successful", "verifyFailed": "Verify failed", "wikiCacheSavedSuccessfully": "Wiki cache saved successfully", "welcomeToStreamingApi": "Welcome to Streaming API", "wikiAlreadyGenerated": "Wiki for this repository has already been generated", "taskAlreadyProcessing": "This repository already has a task being processed", "newTaskCreated": "New task created, starting Wiki generation", "wikiRefreshTaskProcessing": "This Wiki already has a refresh task being processed", "wikiProjectDeletedSuccessfully": "Wiki project deleted successfully", "chatSessionTitleUpdatedSuccessfully": "Chat session title updated successfully", "chatSessionDeletedSuccessfully": "Chat session deleted successfully", "wikiPartialRefreshSubmitted": "Wiki partial refresh task submitted (refresh pages: {pages})", "wikiForceRefreshSubmitted": "Wiki force refresh task submitted", "wikiRefreshSubmitted": "Wiki refresh task submitted (only update when code changes detected)", "deletedWikiSuccessfully": "Deleted wiki for {owner}/{repo} ({language}) successfully. DB records deleted: {count}."}, "responses": {"wikiInfoNotFound": "Wiki information not found", "repoInfoMismatch": "The applied repository name, branch, and project do not match the wiki information, please check.", "applicantOrApproverNotFound": "Applicant or approver information not found", "approverNoPermission": "The approver does not have approval permission", "userAlreadyWikiAdmin": "The user is already a wiki administrator", "applicantInfoNotFound": "Applicant information not found"}, "status": {"healthy": "healthy", "completed": "completed", "pending": "pending", "processing": "processing", "failed": "failed", "exists": "exists", "refresh": "refresh"}, "tag": {"messages": {"tagCreatedSuccessfully": "Tag created successfully", "tagUpdatedSuccessfully": "Tag updated successfully", "tagDeletedSuccessfully": "Tag deleted successfully"}, "errors": {"failedToGetTagList": "Failed to get tag list", "tagNameExists": "Tag name already exists", "failedToCreateTag": "Failed to create tag", "tagNotExists": "Tag not exists", "failedToUpdateTag": "Failed to update tag", "failedToDeleteTag": "Failed to delete tag", "failedToDisableTag": "Failed to disable tag", "failedToEnableTag": "Failed to enable tag", "wikiTagAlreadyExists": "Tag already exists", "failedToAddWikiTag": "Failed to add wiki tag", "wikiTagNotExists": "Tag not exists", "failedToDeleteWikiTag": "Failed to delete wiki tag"}}, "zcm": {"errors": {"invalidRepoUrl": "Please provide a valid repository URL", "failedToGetRepoMetadata": "Failed to get repository metadata", "failedToGetProductLines": "Failed to get product line list", "failedToGetProducts": "Failed to get product list", "failedToGetProductVersions": "Failed to get product version list", "failedToGetDistributions": "Failed to get distribution list", "failedToGetSolutions": "Failed to get solution list"}}}, "sandbox": {"status": {"description": {"NOT_CREATED": "Not Created", "CREATING": "Allocated Creating", "INITIALIZING": "Initializing Agent", "READY": "Created", "FAILED": "Failed", "QUERY_FAILED": "Query Failed", "QUOTA_EXCEEDED": "Personal Quota Exceeded", "TOTAL_CAPACITY_FULL": "System Total Capacity Full"}, "message": {"NOT_CREATED": "Job not found", "CREATING": "Job created but Po<PERSON> not started", "INITIALIZING": "Pod running but container process not fully started", "READY": "Container service fully started", "FAILED": "Pod start failed", "QUERY_FAILED": "Status query exception", "QUOTA_EXCEEDED": "Personal quota exceeded, please release and try again", "TOTAL_CAPACITY_FULL": "System total capacity full, please try again later"}, "normal": "Normal", "podStarting": "Pod starting", "podStatusUnknown": "Pod status unknown", "containerNotReady": "Container started but not fully ready", "apiStartingWithCode": "Container service starting, API returned status code: {code}", "httpStatus": "HTTP {code}", "apiConnectionFailed": "Container service starting, unable to connect to API", "apiTimeout": "Container service starting, API request timeout", "requestTimeout": "Request timeout", "apiException": "Container service starting, API request exception: {error}", "exception": "Exception: {error}", "podNoIp": "Pod running but unable to get IP address", "queryException": "Status query exception: {error}", "unknown": "Unknown", "quotaExceeded": "Personal quota exceeded, please release and try again"}, "k8s": {"errors": {"unauthenticated": "User not authenticated, please log in first", "userInfoMissing": "User information missing", "wikiNotFound": "Wiki information not found", "repoInfoMissing": "Repository information missing, cannot locate sandbox", "jobNotReady": "Sandbox job not created or already cleaned up", "podStarting": "Sandbox is starting, please try again later", "containerNotReady": "Container not ready yet, cannot get container information", "containerInfoIncomplete": "Container information incomplete, please try again later", "portalNotConfigured": "Container portal not configured, please contact administrator"}}}, "auth": {"invalidScheme": "Invalid authentication scheme.", "authFailed": "Authentication failed", "sessionAuthenticationRequired": "Session authentication required", "sessionExpiredOrInvalid": "Session expired or invalid", "authenticationRequired": "Authentication required", "userTokenAuthNotInclude": "Current interface does not support using user token authentication"}, "role": {"insufficientPermissions": "Insufficient permissions", "insufficientPermissionsDescription": "You don't have permission to access this resource", "internalServerError": "Internal server error"}, "user_token": {"errors": {"userAccessTokenAlreadyExists": "User access token already exists", "effectiveAtMustBeBeforeExpiresAt": "Effective at must be before expires at", "userAccessTokenNotFound": "User access token not found"}}}