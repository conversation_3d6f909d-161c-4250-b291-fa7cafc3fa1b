{"common": {"success": "成功", "error": "错误", "message": "消息", "failed": "失败"}, "api": {"errors": {"noRepoUrl": "未提供repo_url参数，请提供'repo_url'查询参数。", "invalidWhaleDevCloudUrl": "无效的whaleDevCloud URL格式", "errorProcessingWhaleDevCloud": "处理whaleDevCloud仓库时出错", "noPathProvided": "未提供路径参数，请提供'path'查询参数。", "directoryNotFound": "目录未找到: {path}", "errorProcessingLocalRepo": "处理本地仓库时出错", "errorInFileAction": "文件操作出错", "errorExportingWiki": "导出wiki时出错", "errorRetrievingWikiData": "从数据库获取wiki数据时出错", "failedToSaveWikiCache": "保存wiki缓存失败", "languageNotSupported": "不支持该语言", "authorizationCodeInvalid": "授权码无效", "failedToDeleteWikiCache": "删除wiki缓存文件失败", "wikiCacheNotFound": "在文件系统或数据库中未找到wiki缓存", "gitUrlFormatIncorrect": "Git URL格式不正确: {message}", "gitCloneFailed": "代码仓库克隆失败：{reason}", "gitBranchNotFound": "远程分支 {branch} 不存在，请确认分支名称或在远端创建该分支", "gitAuthFailed": "仓库认证失败，请检查访问令牌或凭证配置", "gitRepoNotFound": "未找到目标仓库，请确认仓库地址或访问权限", "gitPermissionDenied": "访问仓库被拒绝，请确认令牌权限或仓库可见性", "gitResolveHostFailed": "无法解析仓库域名，请检查网络或仓库地址", "gitConnectFailed": "连接仓库失败，请检查网络连接或稍后重试", "gitTimeout": "连接仓库超时，请确认网络环境后重试", "gitUnknownError": "代码仓库下载过程中发生未知错误，请稍后重试。详细信息: {detail}", "noPermissionToGenerateWiki": "您没有权限生成wiki，需要超级管理员或仓库管理员权限", "noPermissionToExportWiki": "您没有权限导出此wiki，需要仓库管理员权限", "noPermissionToRefreshWiki": "您没有权限刷新此wiki，需要是wiki所有者且为仓库管理员", "noPermissionToAccessWiki": "您没有权限访问该Wiki", "noPermissionToDeleteProject": "您没有权限删除此项目", "noPermissionToEditWiki": "您没有权限编辑此wiki，需要是wiki所有者且为仓库管理员", "authenticationRequired": "需要身份验证", "wikiNotExist": "Wiki不存在，ID: {wiki_id}", "jobManagerNotStarted": "Job管理器未启动", "taskSubmissionFailed": "任务提交失败，可能是系统繁忙", "wikiNotFound": "Wiki未找到", "projectNotFound": "项目未找到", "chatSessionNotFound": "聊天会话未找到", "failedToGetChatHistory": "获取聊天历史失败", "sessionIdRequired": "session_id参数是必需的", "failedToUpdateChatSessionTitle": "更新聊天会话标题失败", "failedToDeleteChatSession": "删除聊天会话失败", "taskSubmitToJobManagerFailed": "任务提交到JobManager失败", "errorStartingWikiGeneration": "启动wiki生成时出错", "errorStartingWikiRefresh": "启动wiki刷新时出错", "failedToGetWikiInfo": "获取Wiki信息失败", "failedToGetWikiProjects": "获取Wiki项目列表失败", "failedToDeleteWikiProject": "删除Wiki项目失败", "failedToGetUserList": "获取用户列表失败", "failedToUpdateUserState": "更新用户状态失败", "failedToGetRoleList": "获取角色列表失败", "failedToGetDepartmentList": "获取部门列表失败", "failedToGetUserRoles": "获取用户角色列表失败", "failedToModifyUserRoles": "修改用户角色失败", "failedToGrantBatch": "批量授权失败", "authorizationFailed": "授权失败", "failedToModifyUserRole": "修改用户角色失败", "failedToQueryRepoCount": "查询deepwiki接入仓库数量失败", "failedToUpdateWikiInfo": "更新wiki信息失败", "failedToGetTransferableUsers": "获取可授权转移的用户列表失败", "notWikiOwner": "你不是当前wiki的所有者，无法执行转移所有者操作", "failedToModifyWikiOwner": "修改wiki所有者失败，无法根据wiki_id[{wiki_id}]找到wiki", "failedToModifyWikiOwnerGeneral": "修改wiki所有者失败", "failedToGetGrantedUsers": "获取授权用户列表失败", "sharePageError": "分享页面报错", "tokenErrorWrongPage": "token错误，访问了错误的页面", "authorizationFailedNoCode": "授权失败：未获取到授权码", "userInvalidOrDisabled": "用户无效或已被禁用", "getSSOTokenFailed": "获取SSO token失败", "unknownError": "未知错误", "internalErrorContactAdmin": "Internal Error: 请联系管理员", "generatorConfigNotLoaded": "生成器配置无法加载", "internalServerErrorModelConfig": "获取模型配置时服务器内部错误", "failedToGetActiveJobs": "获取进行中任务列表失败", "failedToGetPendingJobs": "获取排队中任务列表失败", "failedToGetFailedJobs": "获取失败任务列表失败", "taskSubmittedForRetry": "任务已提交重试，JobManager将自动处理", "taskRetryFailed": "任务重试失败，请检查任务状态", "retryTaskFailed": "重试任务失败", "failedToGetJobStatus": "获取job状态失败", "taskCancelled": "任务已取消", "cancelTaskFailed": "取消任务失败", "failedToGetCancelledJobs": "获取取消任务列表失败", "taskPaused": "任务已暂停", "pauseTaskFailed": "暂停任务失败", "taskResumed": "任务已继续", "resumeTaskFailed": "继续任务失败", "taskRestarted": "任务已重新开始", "restartTaskFailed": "重新开始任务失败", "failedToGetGlobalTaskStatus": "获取全局任务状态失败", "deadProcessInstancesCleared": "死进程实例清理完成", "failedToClearDeadInstances": "清理死进程实例失败", "instanceNotExists": "实例不存在", "instanceForceDeleted": "实例 {instance_id} 已被强制删除", "failedToForceDeleteInstance": "强制删除实例失败", "failedToGetCleanupStatus": "获取清理状态失败", "taskNotExists": "任务 {job_id} 不存在", "taskCannotBeDeleted": "只能删除状态为 {allowed_statuses} 的任务，当前状态: {status}", "taskIsActiveCannotDelete": "任务正在执行中或已暂停，无法删除", "taskDeletedSuccessfully": "任务 {job_id} 已成功删除", "relatedWikiInfoRetained": "关联的WikiInfo记录仍然保留，只删除了任务执行记录", "deleteTaskFailed": "删除任务失败", "taskOnlyCancelledOrFailedCanDelete": "只能删除已取消或失败的任务，当前状态: {status}", "taskAndWikiDeletedSuccessfully": "任务 {job_id} 及其关联的Wiki已成功删除", "deleteTaskAndWikiFailed": "删除任务及Wiki失败", "taskForceDeleted": "任务 {job_id} 已被强制删除", "adminOperationForceDeleted": "这是管理员操作，已强制删除任务记录", "forceDeleteTaskFailed": "强制删除任务失败", "failedToGetDeletableJobs": "获取可删除任务列表失败", "failedToGetReassignmentStatus": "获取任务重新分配状态失败", "taskGlobalStateNotExists": "任务 {job_id} 的全局状态不存在", "taskCompletedCannotReassign": "任务 {job_id} 已{status}，无法重新分配", "adminForceReassignment": "管理员强制重新分配", "taskForceReassigned": "任务 {job_id} 已被强制重新分配", "taskWillBeReassignedNextHeartbeat": "任务将在下一个心跳周期内被其他实例接手", "forceReassignTaskFailed": "强制重新分配任务失败", "failedToGetManagerElectionStatus": "获取管理实例选举状态失败", "failedToGetJobManagerStatus": "获取JobManager状态失败", "validateTaskAtomicityFailed": "验证任务原子性失败", "validateInstanceAtomicityFailed": "验证实例原子性失败", "noActiveInstancesForElection": "没有活跃实例可供选举", "electionFailed": "选举失败，未能选出管理实例", "electionCompleted": "选举完成", "forceElectionFailed": "强制选举失败"}, "messages": {"fileOpenedSuccessfully": "文件打开成功。", "connectionSuccessful": "连接成功", "verifySuccessful": "验证成功", "verifyFailed": "验证失败", "wikiCacheSavedSuccessfully": "Wiki缓存保存成功", "welcomeToStreamingApi": "欢迎使用流式API", "wikiAlreadyGenerated": "此仓库的Wiki已经生成完成", "taskAlreadyProcessing": "此仓库已有任务正在处理", "newTaskCreated": "已创建新任务，开始生成Wiki", "wikiRefreshTaskProcessing": "此Wiki已有刷新任务正在处理", "wikiProjectDeletedSuccessfully": "Wiki项目已成功删除", "chatSessionTitleUpdatedSuccessfully": "聊天会话标题更新成功", "chatSessionDeletedSuccessfully": "聊天会话删除成功", "wikiPartialRefreshSubmitted": "Wiki部分刷新任务已提交（刷新页面: {pages}）", "wikiForceRefreshSubmitted": "Wiki强制刷新任务已提交", "wikiRefreshSubmitted": "Wiki刷新任务已提交（仅在检测到代码变更时更新）", "deletedWikiSuccessfully": "已成功删除{owner}/{repo}({language})的wiki。删除数据库记录数: {count}。"}, "responses": {"wikiInfoNotFound": "未找到wiki信息", "repoInfoMismatch": "申请的仓库名、分支、项目与wiki信息不符合，请检查。", "applicantOrApproverNotFound": "未找到申请人或者审批人信息", "approverNoPermission": "该审批人没有审批权限", "userAlreadyWikiAdmin": "该用户已经是wiki管理员", "applicantInfoNotFound": "未找到申请人信息"}, "status": {"healthy": "健康", "completed": "已完成", "pending": "待处理", "processing": "处理中", "failed": "失败", "exists": "已存在", "refresh": "刷新"}, "tag": {"messages": {"tagCreatedSuccessfully": "标签创建成功", "tagUpdatedSuccessfully": "标签更新成功", "tagDeletedSuccessfully": "标签删除成功"}, "errors": {"failedToGetTagList": "获取标签列表失败", "tagNameExists": "标签名称已存在", "failedToCreateTag": "创建标签失败", "tagNotExists": "标签不存在", "failedToUpdateTag": "更新标签失败", "failedToDeleteTag": "删除标签失败", "failedToDisableTag": "禁用标签失败", "failedToEnableTag": "启用标签失败", "wikiTagAlreadyExists": "标签已存在", "failedToAddWikiTag": "添加wiki标签失败", "wikiTagNotExists": "标签不存在", "failedToDeleteWikiTag": "删除wiki标签失败"}}, "zcm": {"errors": {"invalidRepoUrl": "请传入有效的仓库地址", "failedToGetRepoMetadata": "获取仓库元数据失败", "failedToGetProductLines": "获取产品线列表失败", "failedToGetProducts": "获取产品列表失败", "failedToGetProductVersions": "获取产品版本列表失败", "failedToGetDistributions": "获取发布包列表失败", "failedToGetSolutions": "获取解决方案列表失败"}}}, "sandbox": {"status": {"description": {"NOT_CREATED": "未创建", "CREATING": "已分配创建中", "INITIALIZING": "初始化智能体", "READY": "创建完成", "FAILED": "创建失败", "QUERY_FAILED": "状态查询失败", "QUOTA_EXCEEDED": "个人沙箱配额已满", "TOTAL_CAPACITY_FULL": "系统总资源已满"}, "message": {"NOT_CREATED": "Job不存在", "CREATING": "Job已创建但Pod尚未启动", "INITIALIZING": "Pod运行中但容器内进程未完全启动", "READY": "容器内服务完全启动", "FAILED": "Pod启动失败", "QUERY_FAILED": "状态查询异常", "QUOTA_EXCEEDED": "个人沙箱配额已满，请释放后再试", "TOTAL_CAPACITY_FULL": "系统总资源已满，请稍后再试"}, "normal": "正常", "podStarting": "Pod正在启动中", "podStatusUnknown": "Pod状态未知", "containerNotReady": "容器已启动但未完全就绪", "apiStartingWithCode": "容器内服务启动中，API返回状态码: {code}", "httpStatus": "HTTP {code}", "apiConnectionFailed": "容器内服务启动中，无法连接到API", "apiTimeout": "容器内服务启动中，API请求超时", "requestTimeout": "请求超时", "apiException": "容器内服务启动中，API请求异常: {error}", "exception": "异常: {error}", "podNoIp": "Pod运行中但无法获取IP地址", "queryException": "状态查询异常: {error}", "unknown": "未知", "quotaExceeded": "已达到最大容器数量限制，无法创建新的Job"}, "k8s": {"errors": {"unauthenticated": "用户未认证，请先登录", "userInfoMissing": "无法获取用户信息", "wikiNotFound": "未找到对应的Wiki信息", "repoInfoMissing": "缺少仓库信息，无法定位沙箱", "jobNotReady": "沙箱未创建或已被清理", "podStarting": "沙箱正在启动，请稍后再试", "containerNotReady": "容器尚未就绪，无法获取容器信息", "containerInfoIncomplete": "容器信息不完整，请稍后再试", "portalNotConfigured": "未配置容器门户地址，请联系管理员"}}}, "auth": {"invalidScheme": "无效的认证方案。", "authFailed": "认证失败", "sessionAuthenticationRequired": "需要会话认证", "sessionExpiredOrInvalid": "会话已过期或无效", "authenticationRequired": "需要认证", "userTokenAuthNotInclude": "当前接口不支持使用用户token方式认证"}, "role": {"insufficientPermissions": "权限不足", "insufficientPermissionsDescription": "您没有权限访问此资源", "internalServerError": "内部服务器错误"}, "user_token": {"errors": {"userAccessTokenAlreadyExists": "用户级别token已存在", "effectiveAtMustBeBeforeExpiresAt": "生效日期必须早于过期日期", "userAccessTokenNotFound": "用户级别token不存在"}}}