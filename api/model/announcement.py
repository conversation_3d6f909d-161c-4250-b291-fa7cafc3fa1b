from typing import Optional
from datetime import datetime

from sqlalchemy import Column, func
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, VARCHAR, TEXT, DATETIME
from sqlmodel import Field

from api.model.base import SQLModelSerializable


class Announcement(SQLModelSerializable, table=True):
    __tablename__ = 'ai_dw_announce'

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(unsigned=True), primary_key=True, autoincrement=True, nullable=False, comment="公告表主键")
    )
    title: str = Field(
        max_length=255,
        nullable=False,
        sa_column_kwargs={"comment": "公告标题"}
    )
    type: str = Field(
        max_length=50,
        nullable=False,
        sa_column_kwargs={"comment": "公告类型"}
    )
    content: str = Field(
        sa_column=Column(TEXT, nullable=False, comment="公告内容")
    )
    seq: int = Field(
        default=1,
        sa_column=Column(INTEGER(unsigned=True), nullable=False, comment="公告顺序，从1开始")
    )
    state: int = Field(
        default=1,
        sa_column=Column(INTEGER(unsigned=True), nullable=False, comment="公告状态：1-上架，2-下架")
    )
    created_by: Optional[str] = Field(
        default=None,
        max_length=100,
        sa_column_kwargs={"comment": "创建人"}
    )
    created_date: Optional[datetime] = Field(
        default_factory=datetime.now,
        sa_column=Column(DATETIME, nullable=False, comment="创建时间")
    )
    update_by: Optional[str] = Field(
        default=None,
        max_length=100,
        sa_column_kwargs={"comment": "更新人"}
    )
    update_date: Optional[datetime] = Field(
        default_factory=datetime.now,
        sa_column=Column(DATETIME, nullable=False, onupdate=func.now(), comment="更新时间")
    ) 