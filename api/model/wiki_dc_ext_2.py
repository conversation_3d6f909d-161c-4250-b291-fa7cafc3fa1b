from typing import Optional
from datetime import datetime
from sqlmodel import Field
from api.model.base import SQLModelSerializable

class WikiDcExt2(SQLModelSerializable, table=True):
    """
    ai_dw_wiki_dc_ext_2 表模型
    """
    __tablename__ = "ai_dw_wiki_dc_ext_2"

    id: Optional[int] = Field(
        default=None,
        primary_key=True,
        description="主键，自增"
    )
    wiki_id: int = Field(
        description="wiki标识"
    )
    release_pkg_id: Optional[int] = Field(
        default=None,
        description="发布包ID"
    )
    release_pkg_code: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="发布包编码"
    )
    solution_name: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="解决方案名称"
    )
    solution_id: Optional[int] = Field(
        default=None,
        description="解决方案ID"
    )
    created_by: int = Field(
        description="创建人ID"
    )
    created_date: datetime = Field(
        description="创建时间"
    )
    update_by: Optional[int] = Field(
        default=None,
        description="更新人ID"
    )
    update_date: Optional[datetime] = Field(
        default=None,
        description="更新时间"
    )