from typing import Optional
from datetime import datetime
from enum import Enum

from sqlalchemy import Column
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, VARCHAR, CHAR, TINYINT, DATETIME
from sqlmodel import Field

from api.model.base import SQLModelSerializable

class PrivState(Enum):
    """权限状态枚举"""
    INVALID = 0  # 失效
    VALID = 1    # 有效

class Priv(SQLModelSerializable, table=True):
    """
    权限表 ai_dw_priv
    """
    __tablename__ = "ai_dw_priv"

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(comment="权限主键标识", primary_key=True)
    )
    priv_type: str = Field(
        sa_column=Column(comment="权限类型,S:服务权限,C:组件权限")
    )
    priv_code: str = Field(
        sa_column=Column(comment="权限编码")
    )
    priv_name: str = Field(
        sa_column=Column(comment="权限名称")
    )
    priv_el: Optional[str] = Field(
        default=None,
        sa_column=Column(comment="权限表达式")
    )
    state: int = Field(
        sa_column=Column(comment="权限状态:0: 失效 1: 有效 ")
    )
    created_by: int = Field(
        sa_column=Column(comment="记录创建者")
    )
    created_date: datetime = Field(
        sa_column=Column(comment="记录创建日期")
    )
    update_by: Optional[int] = Field(
        default=None,
        sa_column=Column(comment="记录更新者")
    )
    update_date: Optional[datetime] = Field(
        default=None,
        sa_column=Column(comment="记录更新日期")
    )