from api.model.base import SQLModelSerializable
from typing import Optional
from datetime import datetime
from enum import Enum

from sqlalchemy import Column
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, VARCHAR, CHAR, TINYINT, DATETIME
from sqlmodel import Field

from api.model.base import SQLModelSerializable


class AppAccessToken(SQLModelSerializable, table=True):
    """
    Token管理表
    """
    __tablename__ = "ai_dw_app_access_token"

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(unsigned=True), primary_key=True, autoincrement=True, nullable=False, comment="主键ID")
    )
    token: str = Field(
        sa_column=Column(comment="Access Token")
    )
    app_id: str = Field(
        sa_column=Column(comment="应用ID")
    )
    token_type: int = Field(
        sa_column=Column(comment="Token类型：1-系统级，2-用户级 用户级要求调用接口时必须携带用户信息")
    )
    effective_at: Optional[datetime] = Field(
        sa_column=Column(comment="token生效时间，为空表示立即生效")
    )
    expires_at: Optional[datetime] = Field(
        sa_column=Column(comment="过期时间，NULL表示永久有效")
    )
    last_used_time: Optional[datetime] = Field(
        sa_column=Column(comment="最后使用时间")
    )
    state: int = Field(
        sa_column=Column(comment="状态：0-禁用，1-启用")
    )
    created_by: int = Field(
        sa_column=Column(comment="创建人工号")
    )
    created_date: datetime = Field(
        sa_column=Column(comment="创建时间")
    )
    update_by: Optional[int] = Field(
        default=None,
        sa_column=Column(comment="更新人工号")
    )
    update_date: Optional[datetime] = Field(
        default=None,
        sa_column=Column(comment="更新时间")
    )