from datetime import datetime
from typing import Optional

from sqlalchemy import DATETIME, Column
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, BOOLEAN
from sqlmodel import Field
from sqlalchemy.sql import func

from api.model.base import SQLModelSerializable


class ServLog(SQLModelSerializable, table=True):
    __tablename__ = 'ai_dw_serv_log'

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(unsigned=True), primary_key=True, autoincrement=True, nullable=False, comment="日志记录唯一标识，自增主键")
    )
    event_src: Optional[str] = Field(
        max_length=60,
        sa_column_kwargs={"comment": "事件来源系统，默认deepwiki"}
    )
    event_type: Optional[str] = Field(
        max_length=255,
        sa_column_kwargs={"comment": "事件类型分类"}
    )
    event_code: Optional[str] = Field(
        max_length=255,
        sa_column_kwargs={"comment": "事件编码/操作编码"}
    )
    party_type: Optional[str] = Field(
        max_length=30,
        sa_column_kwargs={"comment": "参与方类型(用户/系统/服务等)"}
    )
    party_code: Optional[str] = Field(
        max_length=255,
        sa_column_kwargs={"nullable": True, "comment": "参与方唯一编码"}
    )
    party_name: Optional[str] = Field(
        max_length=60,
        sa_column_kwargs={"nullable": True, "comment": "参与方名称"}
    )
    party_id: Optional[str] = Field(
        max_length=60,
        sa_column_kwargs={"comment": "参与方ID"}
    )
    oper_id: Optional[int] = Field(
        sa_column=Column(BIGINT(unsigned=True), comment="操作人ID")
    )
    dept_id: Optional[int] = Field(
        sa_column=Column(BIGINT(unsigned=True), nullable=True, comment="操作人所属部门ID")
    )
    dept_name: Optional[str] = Field(
        max_length=120,
        sa_column_kwargs={"nullable": True, "comment": "操作人所属部门名称"}
    )
    oper_data: Optional[str] = Field(
        max_length=1000,
        sa_column_kwargs={"nullable": True, "comment": "操作数据"}
    )
    src_ip: Optional[str] = Field(
        max_length=60,
        sa_column_kwargs={"nullable": True, "comment": "来源IP地址"}
    )
    server_ip: Optional[str] = Field(
        max_length=60,
        sa_column_kwargs={"nullable": True, "comment": "服务器IP地址"}
    )
    is_success: Optional[bool] = Field(
        sa_column=Column(BOOLEAN, comment="是否成功(1成功/0失败)")
    )
    log_date: datetime = Field(
        default_factory=datetime.now,
        sa_column_kwargs={"server_default": func.now(), "comment": "日志记录时间"}
    )
    comments: Optional[str] = Field(
        max_length=1000,
        sa_column_kwargs={"nullable": True, "comment": "操作备注/详细说明"}
    )