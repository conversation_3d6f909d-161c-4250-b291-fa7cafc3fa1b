from api.model.base import SQLModelSerializable
from typing import Optional
from datetime import datetime
from enum import Enum

from sqlalchemy import Column
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, VARCHAR, CHAR, TINYINT, DATETIME
from sqlmodel import Field

from api.model.base import SQLModelSerializable


class AppApiRel(SQLModelSerializable, table=True):
    """
    应用API权限关联表
    """
    __tablename__ = "ai_dw_app_api_rel"

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(unsigned=True), primary_key=True, autoincrement=True, nullable=False, comment="主键ID")
    )
    app_id: int = Field(
        sa_column=Column(comment="应用ID")
    )
    api_id: int = Field(
        sa_column=Column(comment="API ID")
    )
    state: int = Field(
        sa_column=Column(comment="状态：0-禁用，1-启用")
    )
    created_by: int = Field(
        sa_column=Column(comment="授权人ID")
    )
    created_date: datetime = Field(
        sa_column=Column(comment="创建时间")
    )
    update_by: Optional[int] = Field(
        default=None,
        sa_column=Column(comment="记录更新者ID")
    )
    update_date: Optional[datetime] = Field(
        default=None,
        sa_column=Column(comment="记录更新时间")
    )