from __future__ import annotations

from typing import Optional, List, TYPE_CHECKING
from datetime import datetime

from sqlalchemy import UniqueConstraint
from sqlmodel import SQLModel, Field, Relationship

if TYPE_CHECKING:
    from .wiki_repository_relation import AiDwWikiRepositoryRelation


class AiDwGitRepository(SQLModel, table=True):
    """
    Git仓库信息表（新表），存储所有Git仓库的元数据
    """
    __tablename__ = "ai_dw_git_repository"

    id: Optional[int] = Field(default=None, primary_key=True, sa_column_kwargs={"autoincrement": True}, description="主键ID")
    repo_url: str = Field(index=True, description="Git仓库URL")
    branch: str = Field(default="master", description="Git分支")
    repo_owner: str = Field(index=True, description="仓库所有者")
    repo_name: str = Field(index=True, description="仓库名称")
    repo_type: str = Field(default="whaleDevCloud", index=True, description="仓库类型")

    # 仓库元信息
    description: Optional[str] = Field(default=None, description="仓库描述")
    is_private: bool = Field(default=False, description="是否私有仓库")

    # DocChain集成和同步信息
    code_topic_id: Optional[str] = Field(default=None, description="DocChain代码主题ID")
    last_sync_time: Optional[datetime] = Field(default=None, description="最后同步时间")
    last_sync_commit: Optional[str] = Field(default=None, description="最后同步到DocChain的commit哈希")
    file_count: int = Field(default=0, description="仓库文件数量")
    doc_total_files: int = Field(default=0, description="DocChain记录的文件总数（缓存）")
    doc_pending_files: int = Field(default=0, description="DocChain待同步文件数（缓存）")

    # linux用户组和权限信息
    linux_gid: Optional[int] = Field(default=None, description="Linux用户组ID")
    linux_group_name: Optional[str] = Field(default=None, description="Linux用户组名称")
    linux_code_file_perm: bool = Field(default=False, description="代码文件权限")
    linux_pw_file_perm: bool = Field(default=False, description="密码文件权限")
    linux_uw_file_perm: bool = Field(default=False, description="不可写文件权限")

    # 状态
    status: str = Field(default="active", description="状态: active:活跃 archived:归档")

    created_time: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_time: datetime = Field(default_factory=datetime.utcnow, description="更新时间")

    # wiki_relations: List["AiDwWikiRepositoryRelation"] = Relationship(back_populates="repository")

    __table_args__ = (
        UniqueConstraint("repo_url", "branch", name="uix_ai_dw_git_repository_url_branch"),
    )
