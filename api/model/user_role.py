from typing import Optional

from sqlalchemy import Column
from sqlalchemy.dialects.mysql import BIGINT, SMALLINT, INTEGER, VARCHAR, TINYINT
from sqlmodel import Field

from api.model.base import SQLModelSerializable


class UserRole(SQLModelSerializable, table=True):
    """用户角色关联表"""
    __tablename__ = 'ai_dw_user_role'

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(unsigned=True), primary_key=True, autoincrement=True, nullable=False, comment="用户角色标识")
    )
    user_id: int = Field(
        sa_column=Column(BIGINT(unsigned=True), nullable=False, comment="用户标识")
    )
    role_id: int = Field(
        sa_column=Column(SMALLINT(unsigned=True), nullable=False, comment="角色标识")
    )


class Role(SQLModelSerializable, table=True):
    """角色表"""
    __tablename__ = 'ai_dw_role'

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(SMALLINT(unsigned=True), primary_key=True, autoincrement=True, nullable=False, comment="角色标识")
    )
    role_name: str = Field(
        max_length=60,
        nullable=False,
        sa_column_kwargs={"comment": "角色名称"}
    )
    role_code: str = Field(
        max_length=30,
        nullable=False,
        sa_column_kwargs={"comment": "角色编码"}
    )
    comments: Optional[str] = Field(
        default=None,
        max_length=255,
        sa_column_kwargs={"comment": "角色描述"}
    )
    access_level: int = Field(
        sa_column=Column(TINYINT(unsigned=True), nullable=False, comment="权限级别")
    )
    role_type: str = Field(
        max_length=1,
        nullable=False,
        sa_column_kwargs={"comment": "角色类型 S:系统角色 D:数据角色"}
    )


class RolePriv(SQLModelSerializable, table=True):
    """角色权限表"""
    __tablename__ = 'ai_dw_role_priv'

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(INTEGER(unsigned=True), primary_key=True, autoincrement=True, nullable=False, comment="角色权限唯一标识")
    )
    role_id: int = Field(
        sa_column=Column(SMALLINT(unsigned=True), nullable=False, comment="角色标识")
    )
    priv_id: int = Field(
        sa_column=Column(INTEGER(unsigned=True), nullable=False, comment="权限主键标识")
    )
