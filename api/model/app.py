from typing import List, Optional, TYPE_CHECKING
from datetime import datetime
from enum import Enum

from sqlalchemy import Column
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, VARCHAR, CHAR, TINYINT, DATETIME
from sqlmodel import Field, Relationship

from api.model.base import SQLModelSerializable

if TYPE_CHECKING:
    from api.model.app_wiki_rel import AppWikiRel


class App(SQLModelSerializable, table=True):
    """
    应用信息表
    """
    __tablename__ = "ai_dw_app"

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(unsigned=True), primary_key=True, autoincrement=True, nullable=False, comment="主键ID")
    )
    app_id: str = Field(
        sa_column=Column(comment="应用ID，全局唯一")
    )
    app_secret: str = Field(
        sa_column=Column(comment="应用密钥(加密存储)")
    )
    app_code: str = Field(
        sa_column=Column(comment="应用编码，全局唯一")
    )
    app_name: str = Field(
        sa_column=Column(comment="应用名称")
    )
    comments: Optional[str] = Field(
        default=None,
        sa_column=Column(comment="应用描述")
    )
    state: int = Field(
        sa_column=Column(comment="状态：0-禁用，1-启用")
    )
    created_by: int = Field(
        sa_column=Column(comment="创建人工号")
    )
    created_date: datetime = Field(
        sa_column=Column(comment="创建时间")
    )
    update_by: Optional[int] = Field(
        default=None,
        sa_column=Column(comment="更新人工号")
    )
    update_date: Optional[datetime] = Field(
        default=None,
        sa_column=Column(comment="更新时间")
    )

    # wiki_relations: List["AppWikiRel"] = Relationship(back_populates="app")
