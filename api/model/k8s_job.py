from typing import Optional
from datetime import datetime
from sqlmodel import SQLModel, Field
from sqlalchemy.sql import func


class K8sJob(SQLModel, table=True):
    """k8s pod表，管理Kubernetes Job的生命周期"""
    __tablename__ = "ai_dw_k8s_job"

    id: Optional[int] = Field(
        default=None,
        primary_key=True,
        sa_column_kwargs={"comment": "k8s job主键"}
    )
    job_name: str = Field(
        max_length=32,
        sa_column_kwargs={"comment": "k8s job的名称"}
    )
    job_uid: int = Field(
        sa_column_kwargs={"comment": "job启动时linux uid,从40001开始"}
    )
    job_username: str = Field(
        max_length=32,
        sa_column_kwargs={"comment": "k8s job linux用户名"}
    )
    running_status: Optional[int] = Field(
        default=None,
        sa_column_kwargs={"comment": "k8s job的执行状态:1 running 2 succeeded 3 failed 4 pending"}
    )
    user_id: Optional[int] = Field(
        default=None,
        sa_column_kwargs={"comment": "用户主键标识,job被分配给哪个用户"}
    )
    wiki_id: Optional[int] = Field(
        default=None,
        sa_column_kwargs={"comment": "k8s  job分配的wiki主键标识"}
    )
    is_available: int = Field(
        default=1,
        sa_column_kwargs={"comment": "k8s job是否可分配:1 是 0 否"}
    )
    created_by: int = Field(
        sa_column_kwargs={"comment": "记录创建者"}
    )
    created_date: datetime = Field(
        default_factory=datetime.now,
        sa_column_kwargs={"server_default": func.now(), "comment": "k8s job创建时间"}
    )
    update_by: Optional[int] = Field(
        default=None,
        sa_column_kwargs={"comment": "记录更新者"}
    )
    update_date: Optional[datetime] = Field(
        default=None,
        sa_column_kwargs={"onupdate": func.now(), "comment": "记录更新时间"}
    )

    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "job_name": self.job_name,
            "job_uid": self.job_uid,
            "job_username": self.job_username,
            "running_status": self.running_status,
            "user_id": self.user_id,
            "wiki_id": self.wiki_id,
            "is_available": self.is_available,
            "created_by": self.created_by,
            "created_date": self.created_date.strftime("%Y-%m-%d %H:%M:%S") if self.created_date else None,
            "update_by": self.update_by,
            "update_date": self.update_date.strftime("%Y-%m-%d %H:%M:%S") if self.update_date else None,
        }

