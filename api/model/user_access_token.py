from datetime import datetime
from typing import Optional

from sqlalchemy import Column
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, VARCHAR, TINYINT, DATETIME
from sqlmodel import Field

from api.model.base import SQLModelSerializable


class UserAccessToken(SQLModelSerializable, table=True):
    """
    用户Access Token表
    """
    __tablename__ = "ai_dw_user_access_token"

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(INTEGER(unsigned=True), primary_key=True, autoincrement=True, nullable=False, comment="用户Access Token表主键")
    )
    user_id: int = Field(
        sa_column=Column(BIGINT(unsigned=True), nullable=False, comment="用户主键标识")
    )
    name: str = Field(
        max_length=120,
        nullable=False,
        sa_column_kwargs={"comment": "token名称"}
    )
    token: str = Field(
        max_length=255,
        nullable=False,
        sa_column_kwargs={"comment": "用户token, 需加密"}
    )
    state: int = Field(
        default=1,
        sa_column=Column(TINYINT(unsigned=True), nullable=False, comment="Token状态：0: 禁用 1: 有效")
    )
    use_type: Optional[str] = Field(
        default=None,
        max_length=60,
        sa_column_kwargs={"comment": "Token使用类型,如cherry/cursor/qshell等"}
    )
    effective_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DATETIME, comment="Token生效日期,为空表示立即生效")
    )
    expires_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DATETIME, comment="Token过期时间,为空表示永久有效")
    )
    created_by: int = Field(
        sa_column=Column(BIGINT(unsigned=True), nullable=False, comment="Token创建者主键标识")
    )
    created_date: datetime = Field(
        sa_column=Column(DATETIME, nullable=False, comment="Token创建时间")
    )
    update_by: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(unsigned=True), comment="Token更新者主键标识")
    )
    update_date: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DATETIME, comment="Token更新时间")
    )
    last_used_time: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DATETIME, comment="Token最后使用时间")
    )
