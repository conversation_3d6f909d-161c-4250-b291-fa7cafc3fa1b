from typing import Optional
from datetime import datetime
from sqlmodel import SQLModel, Field, Column
from sqlalchemy.dialects.mysql import JSON as MySQLJSON
from sqlalchemy.sql import func
import uuid
from sqlalchemy import String, ForeignKey, TEXT

class WikiJob(SQLModel, table=True):
    """Wiki生成任务表，记录所有的Wiki生成任务"""
    __tablename__ = "wiki_job"

    id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        primary_key=True,
        sa_column_kwargs={"comment": "任务ID，UUID格式"}
    )
    status: str = Field(
        default="pending",
        max_length=50,
        sa_column_kwargs={"comment": "任务状态：pending(待处理), pending_resume(等待恢复), processing(处理中), resuming(恢复中), paused(已暂停), completed(已完成), failed(失败), cancelled(已取消), timeout(超时)"}
    )
    job_type: int = Field(
        default=0,
        sa_column_kwargs={"comment": "任务类型：0(首次生成), 1(刷新更新), 2(同步索引)"}
    )
    stage: Optional[str] = Field(
        default=None,
        max_length=50,
        sa_column_kwargs={"comment": "任务阶段：init(初始化), download(下载), upload(上传), structure(结构生成), pages(页面生成), paused(已暂停), completed(已完成)"}
    )
    stage_progress: int = Field(
        default=0,
        sa_column_kwargs={"comment": "当前阶段的进度百分比(0-100)"}
    )
    stage_message: Optional[str] = Field(
        default=None,
        max_length=255,
        sa_column_kwargs={"comment": "当前阶段的状态信息"}
    )
    progress: int = Field(
        default=0,
        sa_column_kwargs={"comment": "整体进度百分比(0-100)"}
    )
    total_files: Optional[int] = Field(
        default=None,
        sa_column_kwargs={"comment": "总文件数"}
    )
    processed_files: Optional[int] = Field(
        default=None,
        sa_column_kwargs={"comment": "已处理文件数"}
    )
    error_message: Optional[str] = Field(
        default=None,
        sa_column=Column(TEXT, comment="错误信息")
    )
    
    repo_url: str = Field(
        max_length=255,
        sa_column_kwargs={"comment": "代码仓库URL"}
    )
    branch: str = Field(
        default="main",
        max_length=100,
        sa_column_kwargs={"comment": "代码仓库分支"}
    )
    token: Optional[str] = Field(
        default=None,
        max_length=255,
        sa_column_kwargs={"comment": "访问令牌(如果需要认证)"}
    )
    language: str = Field(
        default="zh",
        max_length=10,
        sa_column_kwargs={"comment": "Wiki语言"}
    )
    comprehensive: bool = Field(
        default=True,
        sa_column_kwargs={"comment": "是否生成全面的Wiki(1:是，0:否)"}
    )
    sub_repos: Optional[str] = Field(
        default=None,
        sa_column_kwargs={"comment": "子仓库信息列表"}
    )
    excluded_dirs: Optional[str] = Field(
        default=None,
        sa_column=Column(TEXT, comment="排除的目录列表，逗号分隔")
    )
    excluded_files: Optional[str] = Field(
        default=None,
        sa_column=Column(TEXT, comment="排除的文件列表，逗号分隔")
    )
    included_dirs: Optional[str] = Field(
        default=None,
        sa_column=Column(TEXT, comment="包含的目录列表，逗号分隔")
    )
    included_files: Optional[str] = Field(
        default=None,
        sa_column=Column(TEXT, comment="包含的文件列表，逗号分隔")
    )
    
    topic_id: Optional[str] = Field(
        default=None,
        max_length=100,
        sa_column_kwargs={"comment": "兼容旧版本的单个topic ID"}
    )
    topic_id_code: Optional[str] = Field(
        default=None,
        max_length=100,
        sa_column_kwargs={"comment": "代码部分的topic ID"}
    )
    topic_id_doc: Optional[str] = Field(
        default=None,
        max_length=100,
        sa_column_kwargs={"comment": "文档部分的topic ID"}
    )
    
    wiki_info_id: Optional[str] = Field(
        default=None,
        sa_column=Column(String(36), comment="关联的WikiInfo记录ID")
    )
    
    model_settings: Optional[dict] = Field(
        default=None,
        sa_column=Column(MySQLJSON, comment="模型配置信息，包括提供商、模型名称等")
    )
    result: Optional[dict] = Field(
        default=None,
        sa_column=Column(MySQLJSON, comment="任务结果数据")
    )
    
    created_time: datetime = Field(
        default_factory=datetime.now,
        sa_column_kwargs={"server_default": func.now(), "comment": "创建时间"}
    )
    updated_time: datetime = Field(
        default_factory=datetime.now,
        sa_column_kwargs={"server_default": func.now(), "onupdate": func.now(), "comment": "更新时间"}
    )
    created_by: Optional[int] = Field(
        default=None,
        sa_column_kwargs={"comment": "创建人ID"}
    )
    updated_by: Optional[int] = Field(
        default=None,
        sa_column_kwargs={"comment": "更新人ID"}
    )
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "status": self.status,
            "job_type": self.job_type,
            "stage": self.stage,
            "stage_progress": self.stage_progress,
            "stage_message": self.stage_message,
            "progress": self.progress,
            "total_files": self.total_files,
            "processed_files": self.processed_files,
            "error_message": self.error_message,
            "repo_url": self.repo_url,
            "branch": self.branch,
            "language": self.language,
            "comprehensive": self.comprehensive,
            "sub_repos": self.sub_repos,
            "excluded_dirs": self.excluded_dirs,
            "excluded_files": self.excluded_files,
            "included_dirs": self.included_dirs,
            "included_files": self.included_files,
            "topic_id": self.topic_id,
            "topic_id_code": self.topic_id_code,
            "topic_id_doc": self.topic_id_doc,
            "wiki_info_id": self.wiki_info_id,
            "model_settings": self.model_settings,
            "result": self.result,
            "created_time": self.created_time.strftime("%Y-%m-%d %H:%M:%S") if self.created_time else None,
            "updated_time": self.updated_time.strftime("%Y-%m-%d %H:%M:%S") if self.updated_time else None,
            "created_by": self.created_by,
            "updated_by": self.updated_by
        } 