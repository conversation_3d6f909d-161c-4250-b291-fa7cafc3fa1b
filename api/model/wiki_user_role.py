from typing import Optional
from datetime import datetime
from sqlmodel import SQLModel, Field
from sqlalchemy import UniqueConstraint


class WikiUserRole(SQLModel, table=True):
    """
    Wiki用户角色关联表，存储用户与Wiki的权限关系
    """
    __tablename__ = "ai_dw_wiki_user_role"

    id: Optional[int] = Field(
        default=None,
        primary_key=True,
        sa_column_kwargs={
            "autoincrement": True,
            "comment": "wiki-user-role主键"
        }
    )
    wiki_id: int = Field(
        nullable=False,
        sa_column_kwargs={"comment": "wiki标识"}
    )
    user_id: int = Field(
        nullable=False,
        sa_column_kwargs={"comment": "用户标识"}
    )
    role_id: int = Field(
        nullable=False,
        sa_column_kwargs={"comment": "角色标识"}
    )
    created_by: int = Field(
        nullable=False,
        sa_column_kwargs={"comment": "记录创建者"}
    )
    created_date: datetime = Field(
        default_factory=datetime.utcnow,
        nullable=False,
        sa_column_kwargs={"comment": "记录创建时间"}
    )
    update_by: Optional[int] = Field(
        default=None,
        sa_column_kwargs={"comment": "记录更新者"}
    )
    update_date: Optional[datetime] = Field(
        default=None,
        sa_column_kwargs={"comment": "记录更新时间"}
    )

    # 添加复合索引（SQLModel 的表级配置）
    __table_args__ = (
        # 复合索引（对应原表的 idx_ai_dw_wur_wid_uid_rid）
        UniqueConstraint("wiki_id", "user_id", "role_id", name="idx_ai_dw_wur_wid_uid_rid"),
        # 表注释
        {"comment": "Wiki用户角色关联表"}
    )