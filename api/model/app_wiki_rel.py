from __future__ import annotations

from datetime import datetime
from typing import Optional, TYPE_CHECKING

from sqlalchemy import UniqueConstraint
from sqlmodel import Field

from api.model.base import SQLModelSerializable

if TYPE_CHECKING:
    from api.model.app import App
    from api.model.wiki_info import WikiInfo


class AppWikiRel(SQLModelSerializable, table=True):
    """应用与Wiki的绑定关系表"""

    __tablename__ = "ai_dw_app_wiki_rela"

    id: Optional[int] = Field(
        default=None,
        primary_key=True,
        sa_column_kwargs={"autoincrement": True},
        description="主键ID",
    )
    app_id: int = Field(
        foreign_key="ai_dw_app.id",
        description="应用主键ID",
        index=True,
    )
    wiki_id: int = Field(
        foreign_key="ai_dw_wiki_info.id",
        description="Wiki主键ID",
        index=True,
    )
    created_by: int = Field(default=0, description="创建人ID")
    created_date: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    update_by: Optional[int] = Field(default=None, description="更新人ID")
    update_date: Optional[datetime] = Field(default=None, description="更新时间")

    # app: Optional["App"] = Relationship(back_populates="wiki_relations")
    # wiki_info: Optional["WikiInfo"] = Relationship(back_populates="app_relations")

    __table_args__ = (
        UniqueConstraint("app_id", "wiki_id", name="uix_ai_dw_app_wiki_rela"),
    )
