from typing import Optional
from datetime import datetime
from sqlmodel import SQLModel, Field, Column
from sqlalchemy.sql import func
import uuid
from sqlalchemy import String, TEXT

class JobManagerInstance(SQLModel, table=True):
    """JobManager实例注册表，用于分布式部署管理"""
    __tablename__ = "job_manager_instance"

    instance_id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        primary_key=True,
        sa_column_kwargs={"comment": "实例ID，UUID格式"}
    )
    hostname: str = Field(
        max_length=255,
        sa_column_kwargs={"comment": "主机名"}
    )
    pid: int = Field(
        sa_column_kwargs={"comment": "进程ID"}
    )
    status: str = Field(
        default="active",
        max_length=20,
        sa_column_kwargs={"comment": "实例状态：active(活跃), stopped(已停止), crashed(崩溃)"}
    )
    max_concurrent_jobs: int = Field(
        default=3,
        sa_column_kwargs={"comment": "最大并发任务数"}
    )
    current_jobs: int = Field(
        default=0,
        sa_column_kwargs={"comment": "当前处理的任务数"}
    )
    last_heartbeat: datetime = Field(
        default_factory=datetime.now,
        sa_column_kwargs={"comment": "最后心跳时间"}
    )
    created_time: datetime = Field(
        default_factory=datetime.now,
        sa_column_kwargs={"comment": "创建时间"}
    )
    updated_time: datetime = Field(
        default_factory=datetime.now,
        sa_column_kwargs={"onupdate": func.current_timestamp(), "comment": "更新时间"}
    )

class JobLock(SQLModel, table=True):
    """任务锁表，用于分布式任务管理"""
    __tablename__ = "job_lock"

    job_id: str = Field(
        primary_key=True,
        max_length=100,
        sa_column_kwargs={"comment": "任务ID"}
    )
    instance_id: str = Field(
        max_length=100,
        sa_column_kwargs={"comment": "持有锁的实例ID"}
    )
    hostname: str = Field(
        max_length=255,
        sa_column_kwargs={"comment": "持有锁的主机名"}
    )
    operation: str = Field(
        max_length=50,
        sa_column_kwargs={"comment": "操作类型：submit(提交), process(处理), recover(恢复), cancel(取消)"}
    )
    acquired_time: datetime = Field(
        default_factory=datetime.now,
        sa_column_kwargs={"comment": "获取锁的时间"}
    )
    expires_at: datetime = Field(
        sa_column_kwargs={"comment": "锁过期时间"}
    )
    updated_time: datetime = Field(
        default_factory=datetime.now,
        sa_column_kwargs={"onupdate": func.current_timestamp(), "comment": "更新时间"}
    )

class GlobalJobState(SQLModel, table=True):
    """全局任务状态表，用于跨实例状态同步"""
    __tablename__ = "global_job_state"

    job_id: str = Field(
        primary_key=True,
        max_length=100,
        sa_column_kwargs={"comment": "任务ID"}
    )
    processing_instance_id: Optional[str] = Field(
        default=None,
        max_length=100,
        sa_column_kwargs={"comment": "当前处理该任务的实例ID"}
    )
    global_status: str = Field(
        max_length=50,
        sa_column_kwargs={"comment": "全局状态：available(可用), locked(已锁定), processing(处理中), completed(已完成), failed(失败)"}
    )
    last_processing_instance: Optional[str] = Field(
        default=None,
        max_length=100,
        sa_column_kwargs={"comment": "最后处理该任务的实例ID"}
    )
    job_metadata: Optional[str] = Field(
        default=None,
        sa_column=Column(TEXT, comment="任务元数据，JSON格式")
    )
    created_time: datetime = Field(
        default_factory=datetime.now,
        sa_column_kwargs={"comment": "创建时间"}
    )
    updated_time: datetime = Field(
        default_factory=datetime.now,
        sa_column_kwargs={"onupdate": func.current_timestamp(), "comment": "更新时间"}
    ) 