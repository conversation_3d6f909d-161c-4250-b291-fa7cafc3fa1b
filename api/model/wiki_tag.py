from datetime import datetime
from typing import Optional

from sqlalchemy import Column, Index, UniqueConstraint
from sqlalchemy.dialects.mysql import BIGINT, DATETIME, NUMERIC
from sqlmodel import Field

from api.model.base import SQLModelSerializable


class WikiTag(SQLModelSerializable, table=True):
    """
    wiki与标签多对多关联表，存储wiki和标签的关联关系
    COMMENT ON TABLE ai_dw_wiki_tag IS 'wiki与标签多对多关联表';
    """
    __tablename__ = 'ai_dw_wiki_tag'

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(unsigned=True), primary_key=True, autoincrement=True, nullable=False, comment="关联关系ID")
    )
    wiki_id: int = Field(
        sa_column=Column(BIGINT(unsigned=True), nullable=False, comment="wiki主键ID")
    )
    tag_id: int = Field(
        sa_column=Column(BIGINT(unsigned=True), nullable=False, comment="标签ID")
    )
    seq: int = Field(
        sa_column=Column(NUMERIC(unsigned=True), nullable=False, comment="wiki关联标签排列顺序,从1开始递增")
    )
    created_by: int = Field(
        sa_column=Column(BIGINT(unsigned=True), nullable=False, comment="创建人ID")
    )
    update_by: int = Field(
        sa_column=Column(BIGINT(unsigned=True), comment="更新人ID")
    )
    created_date: datetime = Field(
        default_factory=datetime.utcnow,
        sa_column=Column(DATETIME, nullable=False, comment="创建时间")
    )
    update_date: datetime = Field(
        default=None,
        sa_column=Column(DATETIME, comment="更新时间")
    )

    # 添加SQLModel的表级别索引和唯一约束
    __table_args__ = (
        Index("idx_wiki_id", "wiki_id"),
        Index("idx_tag_id", "tag_id"),
        UniqueConstraint("wiki_id", "tag_id", "seq", name="ux_adwt_wid_tid_seqai_dw_wiki_tag"),
    )
