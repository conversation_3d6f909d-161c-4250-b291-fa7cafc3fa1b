from __future__ import annotations

from typing import Optional, TYPE_CHECKING
from datetime import datetime

from sqlalchemy import UniqueConstraint
from sqlmodel import SQLModel, Field, Relationship

if TYPE_CHECKING:
    from .git_repository import AiDwGitRepository


class AiDwWikiRepositoryRelation(SQLModel, table=True):
    """
    Wiki与Git仓库关联表（新表，简化版本）
    """
    __tablename__ = "ai_dw_wiki_repository_relation"

    id: Optional[int] = Field(default=None, primary_key=True, sa_column_kwargs={"autoincrement": True}, description="主键ID")
    wiki_id: str = Field(foreign_key="ai_dw_wiki_info.wiki_id", index=True, description="Wiki唯一标识")
    repository_id: int = Field(foreign_key="ai_dw_git_repository.id", index=True, description="Git仓库ID")

    # 核心关联属性
    is_main_repo: bool = Field(default=True, description="是否为主仓库")

    # 仓库特定配置（可覆盖Wiki级别配置）
    excluded_dirs: Optional[str] = Field(default=None, description="该仓库排除的目录")
    excluded_files: Optional[str] = Field(default=None, description="该仓库排除的文件")
    included_dirs: Optional[str] = Field(default=None, description="该仓库包含的目录")
    included_files: Optional[str] = Field(default=None, description="该仓库包含的文件")

    created_time: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_time: datetime = Field(default_factory=datetime.utcnow, description="更新时间")

    # wiki_info: Optional["WikiInfo"] = Relationship(back_populates="repository_relations")
    # repository: Optional["AiDwGitRepository"] = Relationship(back_populates="wiki_relations")

    __table_args__ = (
        UniqueConstraint("wiki_id", "repository_id", name="uix_ai_dw_wiki_repo_relation"),
    )

