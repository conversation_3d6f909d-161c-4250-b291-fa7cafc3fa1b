from datetime import datetime
from typing import Optional

from sqlalchemy import Column, Index
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, VARCHAR, DATETIME
from sqlmodel import Field

from api.model.base import SQLModelSerializable


class Project(SQLModelSerializable, table=True):
    """
    项目信息表
    """
    __tablename__ = "ai_dw_project"

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(INTEGER(unsigned=True), primary_key=True, autoincrement=True, nullable=False, comment="项目表主键")
    )
    wiki_id: int = Field(
        sa_column=Column(BIGINT(unsigned=True), nullable=False, comment="wiki_info表主键id")
    )
    project_code: str = Field(
        max_length=60,
        nullable=False,
        sa_column_kwargs={"comment": "项目编码"}
    )
    project_name: str = Field(
        max_length=120,
        nullable=False,
        sa_column_kwargs={"comment": "项目名称"}
    )
    pm_id: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(unsigned=True), comment="项目负责人ID")
    )
    pm_name: str = Field(
        max_length=120,
        nullable=False,
        sa_column_kwargs={"comment": "项目负责人名称"}
    )
    created_by: int = Field(
        sa_column=Column(BIGINT(unsigned=True), nullable=False, comment="记录创建者主键ID")
    )
    created_date: datetime = Field(
        sa_column=Column(DATETIME, nullable=False, comment="记录创建时间")
    )
    update_by: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(unsigned=True), comment="记录更新者主键ID")
    )
    update_date: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DATETIME, comment="记录更新者")
    )

    # 添加唯一索引
    __table_args__ = (
        Index("idx_adp_wiki_id", "wiki_id", unique=True),
    )
