from datetime import datetime
from typing import Optional
from sqlmodel import Field
from api.model.base import SQLModelSerializable

class WikiDcExt1(SQLModelSerializable, table=True):
    """
    ai_dw_wiki_dc_ext_1 表模型
    """
    __tablename__ = "ai_dw_wiki_dc_ext_1"

    id: Optional[int] = Field(
        default=None,
        primary_key=True,
        description="主键，自增"
    )
    wiki_id: int = Field(
        description="wiki标识，唯一"
    )
    dc_repo_id: Optional[int] = Field(
        default=None,
        description="代码仓库ID"
    )
    dc_project_id: Optional[int] = Field(
        default=None,
        description="项目ID"
    )
    branch_version_id: Optional[int] = Field(
        default=None,
        description="分支版本ID"
    )
    branch_version_name: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="分支版本名称"
    )
    product_version_id: Optional[int] = Field(
        default=None,
        description="产品版本ID"
    )
    product_version_code: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="产品版本编码"
    )
    product_name: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="产品名称"
    )
    product_id: Optional[int] = Field(
        default=None,
        description="产品ID"
    )
    product_line_id: Optional[int] = Field(
        default=None,
        description="产品线ID"
    )
    product_line_name: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="产品线名称"
    )
    created_by: int = Field(
        description="创建人ID"
    )
    created_date: datetime = Field(
        description="创建时间"
    )
    update_by: Optional[int] = Field(
        default=None,
        description="更新人ID"
    )
    update_date: Optional[datetime] = Field(
        default=None,
        description="更新时间"
    )