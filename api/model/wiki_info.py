from __future__ import annotations

from typing import Optional, List, TYPE_CHECKING
from datetime import datetime

from sqlalchemy import UniqueConstraint
from sqlmodel import SQLModel, Field, Relationship

if TYPE_CHECKING:
    from .wiki_repository_relation import AiDwWikiRepositoryRelation
    from .wiki_content import AiDwWikiContent
    from .app_wiki_rel import AppWikiRel


class WikiInfo(SQLModel, table=True):
    """
    Wiki信息表（新表），存储Wiki的基本信息和配置
    """
    __tablename__ = "ai_dw_wiki_info"

    id: Optional[int] = Field(default=None, primary_key=True, sa_column_kwargs={"autoincrement": True}, description="主键ID")
    wiki_id: str = Field(index=True, unique=True, description="Wiki唯一标识")
    name: str = Field(description="Wiki名称")
    description: Optional[str] = Field(default=None, description="Wiki描述")

    # Wiki分类：项目或产品
    wiki_type: int = Field(default=1, description="Wiki类型: 1:产品 2:项目")
    project_topic_id: Optional[str] = Field(default=None, description="项目级DocChain主题ID")

    # 生成配置
    provider: str = Field(default="google", description="模型提供商")
    model: str = Field(default="gemini-pro", description="模型名称")
    language: str = Field(default="zh", description="语言")
    comprehensive: bool = Field(default=True, description="是否生成全面Wiki")

    # 过滤配置
    excluded_dirs: Optional[str] = Field(default=None, description="排除的目录，逗号分隔")
    excluded_files: Optional[str] = Field(default=None, description="排除的文件，逗号分隔")
    included_dirs: Optional[str] = Field(default=None, description="包含的目录，逗号分隔")
    included_files: Optional[str] = Field(default=None, description="包含的文件，逗号分隔")

    # 权限和状态
    visibility: int = Field(default=2, description="可见性: 1:全员可见 2:仅创建人可见")
    status: str = Field(default="active", description="状态: active:活跃 archived:归档 deleted:删除")

    # 主仓库冗余字段（来自主仓库，便于查询和判重）
    repo_url: Optional[str] = Field(default=None, description="主仓库URL")
    repo_owner: Optional[str] = Field(default=None, description="主仓库所有者")
    repo_name: Optional[str] = Field(default=None, description="主仓库名称")
    branch: Optional[str] = Field(default=None, description="主仓库分支")
    repo_type: Optional[str] = Field(default=None, description="主仓库类型")

    # 时间和用户信息
    created_time: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_time: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    created_by: int = Field(default=0, description="创建人ID")
    updated_by: int = Field(default=0, description="更新人ID")
    owner_id: int = Field(default=0, description="拥有者ID")

    # 关系占位（避免循环依赖，真正的关系在其它模型里声明）
    # repository_relations: List["AiDwWikiRepositoryRelation"] = Relationship(back_populates="wiki_info")
    # wiki_content: Optional["AiDwWikiContent"] = Relationship(back_populates="wiki_info")
    # app_relations: List["AppWikiRel"] = Relationship(back_populates="wiki_info")

    # 表约束
    __table_args__ = (
        UniqueConstraint("repo_owner", "repo_name", "branch", "language", name="uix_wiki_repo_branch_lang"),
    )

