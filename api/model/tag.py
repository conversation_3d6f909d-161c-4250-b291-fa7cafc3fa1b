from datetime import datetime
from typing import Optional

from sqlalchemy import Column, UniqueConstraint
from sqlalchemy.dialects.mysql import BIGINT, TINYINT, VARCHAR, DATETIME
from sqlmodel import Field

from api.model.base import SQLModelSerializable


class Tag(SQLModelSerializable, table=True):
    """
    标签表，存储系统标签和用户自定义标签
    COMMENT ON TABLE ai_dw_tag IS '标签表';
    """
    __tablename__ = 'ai_dw_tag'

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(unsigned=True), primary_key=True, autoincrement=True, nullable=False, comment="标签ID")
    )
    name: str = Field(
        max_length=60,
        nullable=False,
        sa_column_kwargs={"comment": "标签名称"}
    )
    type: int = Field(
        sa_column=Column(TINYINT(unsigned=True), nullable=False, comment="标签类型（1:系统 2:用户）")
    )
    color: str = Field(
        max_length=16,
        nullable=False,
        sa_column_kwargs={"comment": "标签颜色（如#FF0000）"}
    )
    comments: Optional[str] = Field(
        default=None,
        max_length=255,
        sa_column_kwargs={"comment": "标签描述"}
    )
    module_type: int = Field(
        default=1,
        sa_column=Column(TINYINT(unsigned=True), nullable=False, comment="标签归属模块, 1:deepwiki")
    )
    state: int = Field(
        default=1,
        sa_column=Column(TINYINT(unsigned=True), nullable=False, comment="标签状态, 1:有效 0:失效")
    )
    created_by: int = Field(
        sa_column=Column(BIGINT(unsigned=True), nullable=False, comment="创建人ID")
    )
    created_date: datetime = Field(
        default_factory=datetime.utcnow,
        sa_column=Column(DATETIME, nullable=False, comment="创建时间")
    )
    update_by: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(unsigned=True), comment="修改人ID")
    )
    update_date: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DATETIME, comment="更新时间")
    )

    # 添加SQLModel的表级别唯一性约束
    __table_args__ = (
        UniqueConstraint("created_by", "name", name="ux_adt_created_by_name"),
    )
