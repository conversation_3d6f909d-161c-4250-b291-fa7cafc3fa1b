from datetime import datetime
from typing import Optional

from sqlalchemy import Column, Index, UniqueConstraint
from sqlalchemy.dialects.mysql import BIGINT, VARCHAR, CHAR, TINYINT, DATETIME
from sqlmodel import Field

from api.model.base import SQLModelSerializable


class ChatSession(SQLModelSerializable, table=True):
    __tablename__ = 'ai_dw_chat_session'

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(20, unsigned=True), primary_key=True, autoincrement=True, nullable=False, comment="主键，自增1")
    )
    chat_sid: str = Field(
        max_length=100,
        sa_column=Column(VARCHAR(60), nullable=False, comment="前端使用的会话id")
    )
    title: Optional[str] = Field(
        default=None,
        max_length=60,
        sa_column_kwargs={"comment": "会话标题"}
    )
    wiki_id: int = Field(
        sa_column=Column(nullable=False, comment="wiki标识")
    )
    ip: str = Field(
        max_length=15,
        default='',
        sa_column=Column(CHAR(15), nullable=False, comment="用户所在ip")
    )
    state: int = Field(
        default=1,
        sa_column=Column(TINYINT(3, unsigned=True), nullable=False, comment="0/1/2，删除/创建但无消息/创建已存在消息")
    )
    created_by: int = Field(
        sa_column=Column(BIGINT(20, unsigned=True), nullable=False, comment="用户id")
    )
    created_date: datetime = Field(
        sa_column=Column(DATETIME, nullable=False, comment="创建时间")
    )
    update_by: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(20, unsigned=True), nullable=True)
    )
    update_date: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DATETIME, nullable=True, comment="更新时间")
    )
    qa_src: Optional[int] = Field(
        default=1,
        sa_column=Column(TINYINT(3, unsigned=True), nullable=False, comment="问答来源 1: deepwiki 2: mcp tool")
    )
    app_id: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(10, unsigned=True), nullable=True, comment="应用id")
    )
