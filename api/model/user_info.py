from datetime import datetime
from typing import Optional

from sqlalchemy import Column
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, DATETIME
from sqlmodel import BOOLEAN, Field

from api.model.base import SQLModelSerializable


class UserInfo(SQLModelSerializable, table=True):
    __tablename__ = 'ai_dw_user'

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(unsigned=True), primary_key=True, autoincrement=True, nullable=False, comment="用户表主键")
    )
    wcp_user_id: int = Field(
        sa_column=Column(BIGINT(unsigned=True), nullable=False, comment="鲸加用户标识")
    )
    user_code: str = Field(
        max_length=30,
        nullable=False,
        sa_column_kwargs={"comment": "用户鲸加工号"}
    )
    user_name: str = Field(
        max_length=255,
        nullable=False,
        sa_column_kwargs={"comment": "用户姓名"}
    )
    email: Optional[str] = Field(
        default=None,
        max_length=255,
        sa_column_kwargs={"comment": "用户邮箱"}
    )
    phone: Optional[str] = Field(
        default=None,
        max_length=30,
        sa_column_kwargs={"comment": "用户电话号码"}
    )
    dept: Optional[str] = Field(
        default=None,
        max_length=120,
        sa_column_kwargs={"comment": "用户隶属部门"}
    )
    org: Optional[str] = Field(
        default=None,
        max_length=100,
        sa_column_kwargs={"comment": "用户隶属组织"}
    )
    job: Optional[str] = Field(
        default=None,
        max_length=120,
        sa_column_kwargs={"comment": "用户职位"}
    )
    dept_id: Optional[int] = Field(
        default=None,
        sa_column=Column(INTEGER(unsigned=True), comment="用户隶属部门标识")
    )
    org_id: Optional[int] = Field(
        default=None,
        sa_column=Column(INTEGER(unsigned=True), comment="用户隶属组织标识")
    )
    job_id: Optional[int] = Field(
        default=None,
        sa_column=Column(INTEGER(unsigned=True), comment="用户职位标识")
    )
    last_login_date: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DATETIME, comment="用户最后登录时间")
    )
    created_by: int = Field(
        sa_column=Column(BIGINT(unsigned=True), nullable=True)
    )
    created_date: Optional[datetime] = Field(
        sa_column=Column(DATETIME, nullable=True)
    )
    update_by: int = Field(
        sa_column=Column(BIGINT(unsigned=True), nullable=True)
    )
    update_date: Optional[datetime] = Field(
        sa_column=Column(DATETIME, nullable=True)
    )
    state: bool = Field(
        sa_column=Column(BOOLEAN, comment="用户是否有效,1:有效 0:无效")
    )
