from datetime import datetime
from typing import Optional

from sqlalchemy import DATETIME, Column
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, BOOLEAN
from sqlmodel import Field

from api.model.base import SQLModelSerializable


class EventCode(SQLModelSerializable, table=True):
    __tablename__ = 'ai_dw_event_code'

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(unsigned=True), primary_key=True, autoincrement=True, nullable=False, comment="自增主键ID")
    )
    event_code: str = Field(
        max_length=255,
        default="DEEPWIKI",
        sa_column_kwargs={"comment": "事件唯一编码"}
    )
    event_type: Optional[str] = Field(
        max_length=255,
        sa_column_kwargs={"comment": "事件类型分类"}
    )
    event_src_code: Optional[str] = Field(
        max_length=60,
        sa_column_kwargs={"comment": "事件来源系统编码"}
    )
    is_audit: Optional[bool] = Field(
        sa_column=Column(BOOLEAN, comment="是否需要审计(1是/0否)")
    )
    comments: Optional[str] = Field(
        max_length=255,
        sa_column_kwargs={"comment": "事件描述说明"}
    )