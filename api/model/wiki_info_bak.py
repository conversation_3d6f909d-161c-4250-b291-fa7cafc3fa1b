from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import Field
from sqlmodel import SQLModel, Field
from sqlalchemy import JSON, UniqueConstraint

class WikiInfoBak(SQLModel, table=True):
    """
    Wiki信息表，存储Wiki生成的相关信息
    COMMENT ON TABLE wiki_info IS 'Wiki信息表，存储Wiki生成的相关信息';

CREATE TABLE `wiki_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `wiki_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Wiki唯一标识',
  `repo_url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Git仓库路径',
  `branch` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'master' COMMENT 'Git分支',
  `repo_owner` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '仓库所有者',
  `repo_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '仓库名称',
  `repo_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'whaleDevCloud' COMMENT '仓库类型(github、gitlab等)',
  `topic_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '公司大模型团队DocChain主题ID',
  `topic_id_code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'DocChain代码主题ID',
  `topic_id_doc` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'DocChain文档主题ID 废弃',
  `provider` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'google' COMMENT '模型提供商',
  `model` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'gemini-pro' COMMENT '模型名称',
  `language` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'zh' COMMENT '语言',
  `excluded_dirs` text COLLATE utf8mb4_unicode_ci CEOTENT '排除的目录 (使用 TEXT 存储,逗号分隔)',
  `excluded_files` text COLLATE utf8mb4_unicode_ci COMMENT '排除的文件 (使用 TEXT 存储,逗号分隔)',
  `included_dirs` text COLLATE utf8mb4_unicode_ci COMMENT '包含的目录 (使用 TEXT 存储,逗号分隔)',
  `included_files` text COLLATE utf8mb4_unicode_ci COMMENT '包含的文件 (使用 TEXT 存储,逗号分隔)',
  `comprehensive` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否生成全面Wiki',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` int(11) NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `updated_by` int(11) NOT NULL DEFAULT '0' COMMENT '更新人ID',
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '状态：pending、processing、completed、failed',
  `error_message` text COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
  `wiki_data` json DEFAULT NULL COMMENT 'Wiki数据，包含结构和页面内容 (使用 JSON 或 TEXT)',
  `sub_repos` text COLLATE utf8mb4_unicode_ci COMMENT '子仓库信息[{"url":"","branch":""}]',
  `visibility` tinyint(3) unsigned NOT NULL DEFAULT '2' COMMENT 'wiki可见性, 1:全员可见 2: 未授权其他人则仅创建人可见',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uix_wiki_info_repo_branch` (`repo_url`,`branch`)
) ENGINE=InnoDB AUTO_INCREMENT=1255 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Wiki信息表';

    -- 为常用查询字段创建索引
    CREATE INDEX ix_wiki_info_wiki_id ON wiki_info (wiki_id);
    CREATE INDEX ix_wiki_info_repo_url ON wiki_info (repo_url);
    CREATE INDEX ix_wiki_info_repo_owner ON wiki_info (repo_owner);
    CREATE INDEX ix_wiki_info_repo_name ON wiki_info (repo_name);
    CREATE INDEX ix_wiki_info_repo_type ON wiki_info (repo_type);
    CREATE INDEX ix_wiki_info_status ON wiki_info (status);
    """
    __tablename__ = "wiki_info"

    id: Optional[int] = Field(default=None, primary_key=True, sa_column_kwargs={"autoincrement": True}, description="主键ID")
    wiki_id: str = Field(index=True, description="Wiki唯一标识")
    repo_url: str = Field(index=True, description="Git仓库路径")
    branch: str = Field(default="master", description="Git分支")
    repo_owner: str = Field(index=True, description="仓库所有者")
    repo_name: str = Field(index=True, description="仓库名称")
    repo_type: str = Field(default="whaleDevCloud", index=True, description="仓库类型(github、gitlab等)")
    sub_repos: Optional[str] = Field(default=None, description="子仓库信息")
    
    # DocChain相关信息
    topic_id: Optional[str] = Field(default=None, description="DocChain主题ID")
    topic_id_code: Optional[str] = Field(default=None, description="DocChain代码主题ID")
    topic_id_doc: Optional[str] = Field(default=None, description="DocChain文档主题ID")
    
    # 生成模型信息
    provider: str = Field(default="google", description="模型提供商")
    model: str = Field(default="gemini-pro", description="模型名称")
    language: str = Field(default="zh", description="语言")
    
    # 高级配置
    excluded_dirs: Optional[str] = Field(default=None, description="排除的目录")
    excluded_files: Optional[str] = Field(default=None, description="排除的文件")
    included_dirs: Optional[str] = Field(default=None, description="包含的目录")
    included_files: Optional[str] = Field(default=None, description="包含的文件")
    comprehensive: bool = Field(default=True, description="是否生成全面Wiki")
    
    # 时间和用户信息
    created_time: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_time: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    created_by: int = Field(default=0, description="创建人ID")
    updated_by: int = Field(default=0, description="更新人ID")
    
    # 状态信息
    status: str = Field(default="pending", description="状态：pending、processing、completed、failed")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    
    # JSON字段，存储Wiki结构和页面内容
    wiki_data: Optional[Dict[str, Any]] = Field(default=None, description="Wiki数据，包含结构和页面内容", sa_type=JSON)

    visibility: Optional[int] = Field(default=2, description="wiki可见性, 1:全员可见 2: 未授权其他人则仅创建人可见")

    comments: Optional[str] = Field(default=None, max_length=255, description="仓库描述")

    owner_id: int = Field(default=0, description="wiki拥有者ID")

    type: int = Field(default=1, description="wiki类型: 1:产品  2:项目")
    
    # 添加SQLModel的表级别唯一性约束
    __table_args__ = (
        UniqueConstraint("repo_url", "branch", name="uix_wiki_info_repo_branch"),
    ) 