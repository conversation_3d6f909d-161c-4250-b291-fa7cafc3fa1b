from datetime import datetime
from typing import Optional

from sqlalchemy import DATETIME, Column
from sqlalchemy.dialects.mysql import BIGINT, INTEGER
from sqlmodel import Field

from api.model.base import SQLModelSerializable


class UserExt(SQLModelSerializable, table=True):
    __tablename__ = 'ai_dw_user_ext'

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(unsigned=True), primary_key=True, autoincrement=True, nullable=False, comment="主键")
    )
    user_id: int = Field(
        sa_column=Column(BIGINT(unsigned=True), nullable=False)
    )
    ai_api_key: str = Field(
        max_length=400,
        sa_column_kwargs={"comment": "公司大模型token"}
    )
    dev_cloud_token: str = Field(
        max_length=400,
        sa_column_kwargs={"comment": "dev_cloud_token"}
    )
    sandbox_quota: Optional[int] = Field(
        default=None,
        sa_column=Column(INTEGER(unsigned=True), nullable=True, comment="个性化沙盒并发配额")
    )

    linux_gid: Optional[int] = Field(default=None, description="Linux用户组ID")
    linux_group_name: Optional[str] = Field(default=None, description="Linux用户组名称")

    created_by: int = Field(
        sa_column=Column(BIGINT(unsigned=True), nullable=True)
    )
    created_date: Optional[datetime] = Field(
        sa_column=Column(DATETIME, nullable=True)
    )
    update_by: int = Field(
        sa_column=Column(BIGINT(unsigned=True), nullable=True)
    )
    update_date: Optional[datetime] = Field(
        sa_column=Column(DATETIME, nullable=True)
    )
