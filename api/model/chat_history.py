from datetime import datetime
from typing import Optional

from sqlalchemy import Column
from sqlalchemy.dialects.mysql import BIGINT, VARCHAR, LONGTEXT, TINYINT, DATETIME, MEDIUMTEXT
from sqlmodel import Field

from api.model.base import SQLModelSerializable


class ChatHistory(SQLModelSerializable, table=True):
    __tablename__ = 'ai_dw_chat_history'

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(20, unsigned=True), primary_key=True, autoincrement=True, nullable=False, comment="主键，自增1")
    )
    msg_sid: Optional[str] = Field(
        default=None,
        max_length=63,
        sa_column_kwargs={"comment": "大模型生成的id"}
    )
    chat_id: int = Field(
        sa_column=Column(BIGINT(20, unsigned=True), nullable=False, comment="会话id")
    )
    role: str = Field(
        max_length=30,
        default='',
        sa_column_kwargs={"comment": "角色"}
    )
    content: Optional[str] = Field(
        default=None,
        sa_column=Column(LONGTEXT(), comment="消息内容")
    )
    model: Optional[str] = Field(
        default=None,
        max_length=255,
        sa_column_kwargs={"comment": "ai大模型"}
    )
    parent_id: Optional[int] = Field(
        default=None,
        sa_column_kwargs={"comment": "ai消息对应的问题记录id"}
    )
    msg_data: str = Field(
        max_length=1000,
        default='',
        sa_column_kwargs={"comment": "json格式的额外数据"}
    )
    state: int = Field(
        default=1,
        sa_column=Column(TINYINT(3, unsigned=True), nullable=False, comment="0:失效 1:有效")
    )
    error_code: Optional[str] = Field(
        default=None,
        max_length=30,
        sa_column_kwargs={"comment": "问答错误码"}
    )
    deep_research: int = Field(
        default=0,
        sa_column_kwargs={"comment": "是否为深度研究"}
    )
    deep_research_iter: Optional[str] = Field(
        default=None,
        sa_column_kwargs={"comment": "深度研究迭代次数标识"}
    )
    provider: str = Field(
        default="gemini-cli",
        max_length=20
    )
    created_by: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(20, unsigned=True), nullable=True, comment="消息归属者")
    )
    created_date: datetime = Field(
        sa_column=Column(DATETIME, nullable=False, comment="生成时间")
    )
    update_by: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(20, unsigned=True), nullable=True, comment="消息更新者")
    )
    update_date: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DATETIME, nullable=True, comment="更新时间")
    )
    tool_calls: Optional[str] = Field(
        default=None,
        sa_column=Column(MEDIUMTEXT(), comment="工具调用信息")
    )
    file_references: Optional[str] = Field(
        default=None,
        sa_column=Column(MEDIUMTEXT(collation='utf8mb4_bin'), comment="文件/目录引用(JSON)")
    )
    command_params: Optional[str] = Field(
        default=None,
        sa_column=Column(MEDIUMTEXT(collation='utf8mb4_bin'), comment="命令参数(JSON)")
    )
    qa_src: Optional[int] = Field(
        default=1,
        sa_column=Column(TINYINT(3, unsigned=True), nullable=False, comment="问答来源 1: deepwiki 2: mcp tool")
    )
