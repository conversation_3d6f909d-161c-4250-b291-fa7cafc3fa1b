from __future__ import annotations

from typing import Optional, Dict, Any, TYPE_CHECKING
from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON>
from sqlmodel import SQLModel, Field

if TYPE_CHECKING:
    from .ai_dw_wiki_info import WikiInfo


class AiDwWikiContent(SQLModel, table=True):
    """
    Wiki内容表（新表），存储Wiki的结构和页面内容
    """
    __tablename__ = "ai_dw_wiki_content"

    id: Optional[int] = Field(default=None, primary_key=True, sa_column_kwargs={"autoincrement": True}, description="主键ID")
    wiki_id: str = Field(unique=True, index=True, foreign_key="ai_dw_wiki_info.wiki_id", description="Wiki唯一标识")

    # 内容数据
    wiki_structure: Optional[Dict[str, Any]] = Field(default=None, description="Wiki结构数据", sa_type=JSON)
    wiki_pages: Optional[Dict[str, Any]] = Field(default=None, description="Wiki页面内容", sa_type=JSON)

    # 元数据
    version: int = Field(default=1, description="内容版本号")
    generation_info: Optional[Dict[str, Any]] = Field(default=None, description="生成信息（模型、参数等）", sa_type=JSON)

    # 统计信息
    total_pages: int = Field(default=0, description="总页面数")

    created_time: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_time: datetime = Field(default_factory=datetime.utcnow, description="更新时间")

    # 关系定义已移除，避免循环依赖和ORM复杂性

