from api.model.base import SQLModelSerializable
from typing import Optional
from datetime import datetime
from enum import Enum

from sqlalchemy import Column
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, VARCHAR, CHAR, TINYINT, DATETIME
from sqlmodel import Field

from api.model.base import SQLModelSerializable


class ApiDef(SQLModelSerializable, table=True):
    """
    API接口定义表
    """
    __tablename__ = "ai_dw_api_def"

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(unsigned=True), primary_key=True, autoincrement=True, nullable=False, comment="主键ID")
    )
    api_code: str = Field(
        sa_column=Column(comment="API编码，全局唯一")
    )
    api_name: str = Field(
        sa_column=Column(comment="API名称")
    )
    api_path: str = Field(
        sa_column=Column(comment="API路径")
    )
    api_method: str = Field(
        sa_column=Column(comment="HTTP方法：GET/POST/PUT/DELETE")
    )
    comments: Optional[str] = Field(
        default=None,
        sa_column=Column(comment="API描述")
    )
    category: str = Field(
        sa_column=Column(comment="API分类")
    )
    state: int = Field(
        sa_column=Column(comment="状态：0-禁用，1-启用")
    )
    created_time: datetime = Field(
        sa_column=Column(comment="创建时间")
    )
    update_time: Optional[datetime] = Field(
        default=None,
        sa_column=Column(comment="更新时间")
    )