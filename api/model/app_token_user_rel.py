from api.model.base import SQLModelSerializable
from typing import Optional
from datetime import datetime
from enum import Enum

from sqlalchemy import Column
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, VARCHAR, CHAR, TINYINT, DATETIME
from sqlmodel import Field

from api.model.base import SQLModelSerializable


class AppTokenUserRel(SQLModelSerializable, table=True):
    """
    应用API权限关联表
    """
    __tablename__ = "ai_dw_app_token_user_rel"

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(BIGINT(unsigned=True), primary_key=True, autoincrement=True, nullable=False, comment="token与user关联关系表主键")
    )
    user_id: int = Field(
        sa_column=Column(comment="用户表主键")
    )
    token_id: int = Field(
        sa_column=Column(comment="应用token表主键")
    )
    created_by: int = Field(
        sa_column=Column(comment="记录创建者标识")
    )
    created_date: datetime = Field(
        sa_column=Column(comment="创建时间")
    )