"""
同步索引服务模块
负责处理Wiki索引同步的业务逻辑
"""
import logging
import os
import time
import threading
import json
import atexit
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, Any, List, Optional, Tuple, Set
from datetime import datetime, timezone
from sqlmodel import select

from api.database.base import session_scope
from api.model.wiki_info import WikiInfo
from api.model.wiki_repository_relation import AiDwWikiRepositoryRelation
from api.model.git_repository import AiDwGitRepository
from api.model.user_ext import UserExt
from api.database.service import DatabaseService
from api.docchain.manager import DocChainManager
from api.utils import git_utils
from api.utils.aes_utils import AESUtils
from api.cache.redis.manager import redis_manager
from api.config import configs
from api.wiki.sync_index_job_manager import (
    init_sync_job_manager,
    SyncTask,
)
from api.wiki.sync_worker_service import get_sync_worker_service
import uuid

logger = logging.getLogger(__name__)

class SyncIndexService:
    """同步索引服务的门面层。

    负责：
    * 提供启动同步任务的入口（start_sync_index）。
    * 聚合缓存的同步进度（calculate_index_progress），避免接口阻塞。

    真正耗时的 Git 拉取、DocChain 对比、上传等操作全部委托给
    :class:`SyncIndexJobManager` 的后台线程处理。
    """
    def __init__(self, db_service: DatabaseService, docchain_manager: DocChainManager):
        self.db_service = db_service
        self.docchain_manager = docchain_manager
        self._redis = None
        try:
            self._redis = redis_manager.get_client()
        except Exception:
            self._redis = None

        self.sync_worker_service = get_sync_worker_service()
        self.job_manager = init_sync_job_manager(db_service, docchain_manager)

        self._summary_cache: Dict[str, Tuple[float, Dict[str, Any]]] = {}
        self._summary_refreshing: Set[str] = set()
        self._summary_lock = threading.RLock()
        # 预先初始化AES工具，用于解密用户扩展表中的令牌
        self._aes = AESUtils()

        self._memory_cache_only = self._redis is None

        summary_workers = None
        sync_config = configs.get("sync_worker", {}) if configs else {}
        if isinstance(sync_config, dict):
            summary_workers = sync_config.get("summary_refresh_workers")
        try:
            summary_workers_int = int(summary_workers) if summary_workers is not None else 4
        except (TypeError, ValueError):
            summary_workers_int = 4
        if summary_workers_int <= 0:
            summary_workers_int = 1

        raw_summary_ttl = None
        if isinstance(sync_config, dict):
            raw_summary_ttl = sync_config.get("summary_cache_ttl_seconds")
        try:
            summary_cache_ttl = int(raw_summary_ttl) if raw_summary_ttl is not None else 900
        except (TypeError, ValueError):
            summary_cache_ttl = 900
        if summary_cache_ttl < 60:
            summary_cache_ttl = 60
        self._summary_cache_ttl = summary_cache_ttl

        fallback_token = None
        if isinstance(sync_config, dict):
            fallback_token = sync_config.get("default_repo_token")
        if not fallback_token:
            fallback_token = os.getenv("DEFAULT_REPO_ACCESS_TOKEN", "e9b2a8bba44e140684bb837e1ca2416bff4fb490")
        self._default_repo_token = fallback_token

        self._summary_executor = ThreadPoolExecutor(
            max_workers=summary_workers_int,
            thread_name_prefix="wiki-summary",
        )

        atexit.register(self._summary_executor.shutdown, wait=False)

    def get_queue_snapshot(self) -> Dict[str, Any]:
        """返回同步任务队列快照，供观测或仪表盘使用。"""
        return self.job_manager.get_queue_snapshot()

    async def start_sync_index(self, wiki_id: str, strategy: str = 'main_topic', user_id: int = None) -> Dict[str, Any]:
        """
        启动同步索引任务
        
        Args:
            wiki_id: Wiki ID
            strategy: 同步策略 ('main_topic' 或 'branch_topic')
            user_id: 用户ID
            
        Returns:
            包含job_id的字典
        """
        if not self.docchain_manager:
            raise ValueError("DocChain manager is not configured")

        try:
            # 若已有执行中的任务则直接返回，避免重复调度
            existing_job = self.job_manager.get_active_job(wiki_id)
            if existing_job:
                return {
                    "job_id": existing_job.get("job_id"),
                    "message": "已有同步任务排队或执行中",
                    "status": existing_job.get("status"),
                }

            with session_scope() as session:
                # 获取Wiki信息
                wiki_info = session.exec(select(WikiInfo).where(WikiInfo.wiki_id == wiki_id)).first()
                if not wiki_info:
                    raise ValueError(f"Wiki not found: {wiki_id}")

                # 获取仓库关系
                relations = session.exec(
                    select(AiDwWikiRepositoryRelation)
                    .where(AiDwWikiRepositoryRelation.wiki_id == wiki_id)
                    .order_by(AiDwWikiRepositoryRelation.created_time.asc(), AiDwWikiRepositoryRelation.id.asc())
                ).all()
                
                if not relations:
                    raise ValueError(f"No repositories found for wiki: {wiki_id}")

                # 获取主仓库信息
                main_relation = next((r for r in relations if r.is_main_repo), relations[0])
                main_repo = session.exec(select(AiDwGitRepository).where(AiDwGitRepository.id == main_relation.repository_id)).first()
                
                if not main_repo:
                    raise ValueError(f"Main repository not found for wiki: {wiki_id}")

                # 构建子仓库信息
                sub_repos = []
                for relation in relations:
                    if not relation.is_main_repo:
                        repo = session.exec(select(AiDwGitRepository).where(AiDwGitRepository.id == relation.repository_id)).first()
                        if repo:
                            sub_repos.append({
                                "url": repo.repo_url,
                                "branch": repo.branch
                            })

                tasks: List[SyncTask] = []
                repos: List[AiDwGitRepository] = []
                namespaces: Dict[int, str] = {}
                for relation in relations:
                    repo = session.exec(select(AiDwGitRepository).where(AiDwGitRepository.id == relation.repository_id)).first()
                    if not repo:
                        continue
                    repos.append(repo)
                    owner = repo.repo_owner
                    repo_name = repo.repo_name
                    if not owner or not repo_name:
                        extracted_owner, extracted_repo, _ = git_utils.extract_repo_info(repo.repo_url)
                        owner = owner or extracted_owner
                        repo_name = repo_name or extracted_repo
                    namespace = git_utils.build_repo_namespace(owner, repo_name, repo.branch)
                    namespaces[repo.id] = namespace
                self._ensure_repo_topics(repos, session, namespaces)

                repo_access_token = self._default_repo_token
                # 优先尝试使用创建人的个性化仓库令牌
                creator_token = self._resolve_repo_token(session, wiki_info)
                if creator_token:
                    repo_access_token = creator_token

                job_id = f"sync-{uuid.uuid4()}"
                repo_map = {repo.id: repo for repo in repos}
                for relation in relations:
                    repo = repo_map.get(relation.repository_id)
                    if not repo:
                        continue
                    namespace = namespaces.get(repo.id)
                    tasks.append(
                        SyncTask(
                            job_id=job_id,
                            wiki_id=wiki_id,
                            repo_id=repo.id,
                            repo_url=repo.repo_url,
                            repo_type=repo.repo_type,
                            branch=repo.branch,
                            topic_id=repo.code_topic_id,
                            strategy=strategy,
                            namespace=namespace,
                            access_token=repo_access_token,
                        )
                    )

                if not tasks:
                    raise ValueError("No repositories to sync")

                await self.job_manager.start()

                created_at = datetime.utcnow().isoformat()
                job_record = {
                    "job_id": job_id,
                    "wiki_id": wiki_id,
                    "strategy": strategy,
                    "status": "pending",
                    "created_at": created_at,
                    "updated_at": created_at,
                    "progress": 0,
                    "processed_units": 0,
                    "total_units": len(tasks),
                    "tasks": [
                        {
                            "repo_id": repo.id,
                            "repo_url": repo.repo_url,
                            "branch": repo.branch,
                            "status": "pending",
                            "progress": 0,
                            "message": "等待同步",
                            "namespace": namespaces.get(repo.id),
                        }
                        for repo in repos
                    ],
                }

                self.job_manager.register_job(job_record)
                await self.job_manager.submit_tasks(job_id, tasks)

                self._store_latest_job(wiki_id, job_id)

                return {
                    "job_id": job_id,
                    "message": "同步索引任务已创建",
                    "strategy": strategy,
                }

        except Exception as e:
            logger.error(f"启动同步索引失败: {e}", exc_info=True)
            raise

    def _resolve_repo_token(self, session, wiki_info: WikiInfo) -> Optional[str]:
        """根据Wiki创建人获取Git访问令牌，失败时返回None."""
        if not wiki_info or not getattr(wiki_info, "created_by", None):
            return None
        try:
            user_ext = session.exec(
                select(UserExt).where(UserExt.user_id == wiki_info.created_by)
            ).first()
        except Exception as exc:
            logger.debug("查询Wiki创建者扩展信息失败: %s", exc)
            return None
        if not user_ext or not user_ext.dev_cloud_token:
            return None
        try:
            decrypted = self._aes.decrypt(user_ext.dev_cloud_token)
        except Exception as exc:
            logger.warning("解密Wiki创建者令牌失败，将回退默认令牌: %s", exc)
            return None
        return decrypted.strip() or None

    def _has_valid_sync_data(self, summary: Dict[str, Any]) -> bool:
        if not summary:
            return False
        status = (summary.get("status") or "").lower()
        if status == "ready":
            return True
        if summary.get("total_files", 0) > 0:
            return True
        if summary.get("pending_files", 0) > 0:
            return True
        if summary.get("indexed_files", 0) > 0:
            return True
        repositories = summary.get("repositories") or []
        return any((repo.get("docchain_total_files") or repo.get("total_files") or 0) > 0 for repo in repositories)

    def _merge_job_runtime_summary(
        self,
        summary: Dict[str, Any],
        job_record: Optional[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """将正在执行的任务信息融合进仓库同步汇总，确保前端能看到最新进度。"""
        if not job_record:
            return summary or {}

        tasks = job_record.get("tasks") or []
        if not tasks:
            return summary or {}

        def _to_int(value: Any) -> int:
            try:
                return max(int(value), 0)
            except (TypeError, ValueError):
                return 0

        base_summary = dict(summary or {})
        base_repos = []
        if base_summary.get("repositories"):
            base_repos = [dict(repo) for repo in base_summary["repositories"] if isinstance(repo, dict)]

        misc_repos: List[Dict[str, Any]] = [repo for repo in base_repos if repo.get("repo_id") is None]
        repo_map: Dict[int, Dict[str, Any]] = {
            repo.get("repo_id"): repo
            for repo in base_repos
            if repo.get("repo_id") is not None
        }

        merged_repos: List[Dict[str, Any]] = []

        for task in tasks:
            repo_id = task.get("repo_id")
            entry = repo_map.pop(repo_id, None)
            if entry is None:
                entry = {
                    "repo_id": repo_id,
                    "repo_url": task.get("repo_url"),
                    "branch": task.get("branch"),
                    "namespace": task.get("namespace"),
                }
            else:
                entry = dict(entry)
                entry.setdefault("namespace", task.get("namespace"))

            snapshot_raw = task.get("resume_snapshot")
            resume_total = _to_int(snapshot_raw.get("total_files")) if isinstance(snapshot_raw, dict) else 0

            job_total = max(
                _to_int(task.get("total_files")),
                resume_total,
            )
            cached_total = max(
                _to_int(entry.get("total_files")),
                _to_int(entry.get("expected_files")),
            )
            total_candidate = max(cached_total, job_total)

            job_pending = max(
                _to_int(task.get("pending_files")),
                _to_int(task.get("missing_files")),
            )
            cached_pending = _to_int(entry.get("pending_files"))
            pending_candidate = job_pending if job_pending else cached_pending

            if total_candidate and pending_candidate > total_candidate:
                pending_candidate = min(pending_candidate, total_candidate)

            synced_from_pending = (total_candidate - pending_candidate) if total_candidate else 0
            if synced_from_pending < 0:
                synced_from_pending = 0

            uploaded_files = _to_int(task.get("uploaded_files"))
            indexed_candidate = max(
                _to_int(entry.get("indexed_files")),
                uploaded_files,
                synced_from_pending,
            )

            if total_candidate == 0 and (indexed_candidate or pending_candidate):
                total_candidate = indexed_candidate + pending_candidate

            if total_candidate and indexed_candidate > total_candidate:
                indexed_candidate = total_candidate

            unexpected_candidate = max(
                _to_int(task.get("unexpected_files")),
                _to_int(entry.get("unexpected_files")),
            )

            entry.update({
                "total_files": total_candidate,
                "expected_files": max(_to_int(entry.get("expected_files")), total_candidate),
                "pending_files": pending_candidate,
                "indexed_files": indexed_candidate,
                "unexpected_files": unexpected_candidate,
                "missing_files": max(_to_int(task.get("missing_files")), _to_int(entry.get("missing_files"))),
                "job_total_files": job_total,
                "job_pending_files": job_pending,
                "job_status": (task.get("status") or entry.get("job_status") or "").lower() or None,
                "job_message": task.get("message") or entry.get("job_message"),
                "mode": task.get("mode") or entry.get("mode"),
                "data_source": "job-runtime",
            })
            entry["difference"] = _to_int(entry.get("indexed_files")) - _to_int(entry.get("expected_files"))

            merged_repos.append(entry)

        merged_repos.extend(repo_map.values())
        merged_repos.extend(misc_repos)

        total_files = sum(_to_int(repo.get("total_files")) or _to_int(repo.get("expected_files")) for repo in merged_repos)
        pending_files = sum(_to_int(repo.get("pending_files")) for repo in merged_repos)
        indexed_files = sum(_to_int(repo.get("indexed_files")) for repo in merged_repos)
        unexpected_files = sum(_to_int(repo.get("unexpected_files")) for repo in merged_repos)

        base_summary.update({
            "repositories": merged_repos,
            "total_files": total_files,
            "pending_files": pending_files,
            "indexed_files": min(indexed_files, total_files) if total_files else indexed_files,
            "unexpected_files": unexpected_files,
            "data_source": "job-runtime",
        })

        return base_summary

    def _ensure_repo_topics(
        self,
        repos: List[AiDwGitRepository],
        session,
        namespaces: Optional[Dict[int, str]] = None,
    ) -> None:
        if not repos or not self.docchain_manager:
            return

        for repo in repos:
            if repo.code_topic_id:
                continue
            owner = repo.repo_owner
            repo_name = repo.repo_name
            if not owner or not repo_name:
                extracted_owner, extracted_repo, _ = git_utils.extract_repo_info(repo.repo_url)
                owner = owner or extracted_owner
                repo_name = repo_name or extracted_repo
            namespace = namespaces.get(repo.id) if namespaces else git_utils.build_repo_namespace(owner, repo_name, repo.branch)
            try:
                topic_id = self.docchain_manager.get_or_create_topic(
                    repo.repo_url,
                    namespace,
                    namespace
                )
                if topic_id and repo.code_topic_id != topic_id:
                    repo.code_topic_id = topic_id
                    repo.updated_time = datetime.utcnow()
                    session.add(repo)
            except Exception as exc:
                logger.warning(f"确保仓库 {repo.id} Topic 时出错: {exc}")

    def calculate_index_progress(self, wiki_id: str) -> Dict[str, Any]:
        """
        计算索引进度

        Returns:
            包含进度信息的字典
        """
        try:
            repo_sync_summary = dict(self._build_cached_repo_summary(wiki_id) or {})
            summary_ready = self._has_valid_sync_data(repo_sync_summary)
            if not summary_ready:
                repo_sync_summary["status"] = repo_sync_summary.get("status") or "refreshing"
                repo_sync_summary["stage_message"] = repo_sync_summary.get("stage_message") or "refreshing"
            if not summary_ready:
                self._schedule_repo_summary_refresh(wiki_id)
                repo_sync_summary.setdefault("repositories", repo_sync_summary.get("repositories") or [])
                repo_sync_summary.setdefault("pending_files", repo_sync_summary.get("pending_files", 0))
                repo_sync_summary.setdefault("total_files", repo_sync_summary.get("total_files", 0))
                repo_sync_summary.setdefault("indexed_files", repo_sync_summary.get("indexed_files", 0))
                repo_sync_summary["status"] = repo_sync_summary.get("status") or "refreshing"
                repo_sync_summary["stage_message"] = repo_sync_summary.get("stage_message") or "refreshing"

            latest_job_id = self._get_latest_job_id(wiki_id)
            if not latest_job_id:
                return {
                    "processed_files": 0,
                    "total_files": 0,
                    "progress": 0,
                    "status": repo_sync_summary.get("status", "idle"),
                    "repositories": [],
                    "repo_sync": repo_sync_summary,
                    "pending_sync_files": repo_sync_summary.get("pending_files", 0),
                    "total_sync_files": repo_sync_summary.get("total_files", 0),
                    "indexed_sync_files": repo_sync_summary.get("indexed_files", 0),
                    "stage_message": repo_sync_summary.get("stage_message"),
                }

            record = self.job_manager.get_job_status(latest_job_id)
            if not record:
                return {
                    "processed_files": 0,
                    "total_files": 0,
                    "progress": 0,
                    "status": repo_sync_summary.get("status", "unknown"),
                    "repositories": [],
                    "job_id": latest_job_id,
                    "repo_sync": repo_sync_summary,
                    "pending_sync_files": repo_sync_summary.get("pending_files", 0),
                    "total_sync_files": repo_sync_summary.get("total_files", 0),
                    "indexed_sync_files": repo_sync_summary.get("indexed_files", 0),
                    "stage_message": repo_sync_summary.get("stage_message"),
                }

            repo_sync_summary = self._merge_job_runtime_summary(repo_sync_summary, record)
            summary_ready = self._has_valid_sync_data(repo_sync_summary)

            response = {
                "job_id": latest_job_id,
                "status": record.get("status", "pending"),
                "progress": record.get("progress", 0),
                "processed_files": record.get("processed_units", 0),
                "total_files": record.get("total_units", 0),
                "repositories": record.get("tasks", []),
                "stage_message": record.get("tasks", [{}])[-1].get("message") if record.get("tasks") else "",
            }

            response["repo_sync"] = repo_sync_summary
            response["pending_sync_files"] = repo_sync_summary.get("pending_files", 0)
            response["total_sync_files"] = repo_sync_summary.get("total_files", 0)
            response["indexed_sync_files"] = repo_sync_summary.get("indexed_files", 0)

            if not summary_ready and record.get("status") not in {"running", "pending", "processing"}:
                response["repo_sync"]["status"] = "refreshing"

            return response

        except Exception as e:
            logger.error(f"计算索引进度失败: {e}", exc_info=True)
            return {
                "processed_files": 0,
                "total_files": 0,
                "progress": 0,
                "repositories": [],
                "status": "error",
                "repo_sync": {
                    "pending_files": 0,
                    "total_files": 0,
                    "indexed_files": 0,
                    "repositories": [],
                },
                "pending_sync_files": 0,
                "total_sync_files": 0,
                "indexed_sync_files": 0,
            }

    def get_sync_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        return self.job_manager.get_job_status(job_id)

    async def cancel_sync_job(self, wiki_id: str, job_id: Optional[str] = None, reason: Optional[str] = None) -> Dict[str, Any]:
        """取消指定或最近一次同步任务."""
        target_job_id = job_id or self._get_latest_job_id(wiki_id)
        if not target_job_id:
            raise ValueError("no_active_job")
        success = await self.job_manager.cancel_job(target_job_id, reason or "请求取消")
        if not success:
            raise ValueError("job_not_found_or_finished")
        status = self.job_manager.get_job_status(target_job_id)
        return {
            "job_id": target_job_id,
            "status": status.get("status") if status else "cancelling",
            "job": status,
        }

    def _calculate_repo_sync_backlog(self, wiki_id: str) -> Dict[str, Any]:
        try:
            with session_scope() as session:
                relations = session.exec(
                    select(AiDwWikiRepositoryRelation)
                    .where(AiDwWikiRepositoryRelation.wiki_id == wiki_id)
                    .order_by(AiDwWikiRepositoryRelation.created_time.asc())
                ).all()

                if not relations:
                    return {
                        "pending_files": 0,
                        "total_files": 0,
                        "indexed_files": 0,
                        "unexpected_files": 0,
                        "repositories": [],
                    }

                repo_ids = [relation.repository_id for relation in relations]
                repo_rows = session.exec(
                    select(AiDwGitRepository).where(AiDwGitRepository.id.in_(repo_ids))
                ).all()
                repo_map = {repo.id: repo for repo in repo_rows}
                self._ensure_repo_topics(repo_rows, session)
                ordered_repos = [repo_map[relation.repository_id] for relation in relations if repo_map.get(relation.repository_id)]
                repo_stats, pending_total, total_files, indexed_total, unexpected_total = self._evaluate_repo_sync_counts(ordered_repos)
                return {
                    "pending_files": pending_total,
                    "total_files": total_files,
                    "indexed_files": indexed_total,
                    "unexpected_files": unexpected_total,
                    "repositories": repo_stats,
                }
        except Exception as exc:
            logger.error(f"计算Wiki {wiki_id} 仓库同步进度失败: {exc}", exc_info=True)
            return {
                "pending_files": 0,
                "total_files": 0,
                "indexed_files": 0,
                "unexpected_files": 0,
                "repositories": [],
            }

    def _evaluate_repo_sync_counts(
        self, repos: List[AiDwGitRepository]
    ) -> Tuple[List[Dict[str, Any]], int, int, int, int]:
        if not repos:
            return [], 0, 0, 0, 0

        repo_stats: List[Dict[str, Any]] = []
        total_pending = 0
        total_expected = 0
        total_indexed = 0
        total_unexpected = 0
        update_payload: List[Tuple[int, int, int, Optional[int]]] = []
        docchain_available = self.docchain_manager is not None

        for repo in repos:
            cached_total = max(repo.doc_total_files or 0, 0)
            cached_pending = max(repo.doc_pending_files or 0, 0)
            breakdown: Dict[str, int] = {}
            data_source = "cached"

            fetched_total = cached_total
            fetched_pending = cached_pending

            status_data: Optional[Dict[str, Any]] = None
            if docchain_available and repo.code_topic_id:
                status_data = self._fetch_topic_status_with_retry(repo.code_topic_id)

                if status_data:
                    fetched_total, fetched_pending, breakdown = self._extract_topic_counts(status_data)
                    fetched_total = max(fetched_total, 0)
                    fetched_pending = max(fetched_pending, 0)
                    data_source = "docchain-status"
                elif repo.file_count and fetched_total == 0:
                    fetched_total = max(repo.file_count, 0)
                    fetched_pending = fetched_total
            else:
                if repo.file_count:
                    fetched_total = max(fetched_total, repo.file_count)
                    fetched_pending = fetched_total

            owner = repo.repo_owner
            repo_name = repo.repo_name
            if not owner or not repo_name:
                extracted_owner, extracted_repo, _ = git_utils.extract_repo_info(repo.repo_url)
                owner = owner or extracted_owner
                repo_name = repo_name or extracted_repo
            namespace = git_utils.build_repo_namespace(owner, repo_name, repo.branch)

            manifest: List[Dict[str, Any]] = []
            manifest_count: Optional[int] = None
            local_path = git_utils.get_repo_cache_path(owner, repo_name, repo.branch)
            if docchain_available and local_path and os.path.isdir(local_path):
                namespace_map = {local_path: namespace}
                resolved_path = os.path.realpath(local_path)
                if resolved_path and resolved_path != local_path:
                    namespace_map[resolved_path] = namespace
                try:
                    manifest = self.docchain_manager.build_local_manifest([local_path], namespace_map)
                except Exception as exc:
                    logger.debug(
                        "Failed to collect repo manifest for repo %s: %s",
                        repo.id,
                        exc,
                    )
                    manifest = []
                manifest_names = {
                    item.get("docchain_name")
                    for item in manifest
                    if item.get("docchain_name")
                }
                if manifest_names:
                    manifest_count = len(manifest_names)

            summary: Optional[Dict[str, Any]] = None
            if docchain_available and repo.code_topic_id and hasattr(self.docchain_manager, "summarize_repo_documents"):
                try:
                    summary = self.docchain_manager.summarize_repo_documents(
                        repo.code_topic_id,
                        namespace,
                        expected_manifest=manifest if manifest else None,
                    )
                except Exception as exc:
                    logger.debug(
                        "Failed to summarize documents for repo %s topic %s: %s",
                        repo.id,
                        repo.code_topic_id,
                        exc,
                    )

            indexed_files = max(cached_total - cached_pending, 0)
            docchain_total = fetched_total
            unexpected_files = 0
            summary_missing: Optional[int] = None
            duplicate_files = 0

            if summary:
                docchain_total = summary.get("docchain_total", docchain_total)
                indexed_files = summary.get("canonical_total", indexed_files)
                unexpected_files = summary.get("unexpected_total", 0)
                summary_missing = summary.get("missing_total")
                duplicate_files = int(summary.get("duplicate_total", 0) or 0)
                if manifest_count is None:
                    expected_total = summary.get("expected_total")
                    if expected_total is not None:
                        manifest_count = expected_total
                data_source = "docchain-normalized"

            if status_data and fetched_total is not None and fetched_pending is not None:
                completed_from_status = max(fetched_total - fetched_pending, 0)
                if completed_from_status > indexed_files:
                    indexed_files = completed_from_status

            if docchain_total is not None and indexed_files > docchain_total:
                indexed_files = docchain_total

            expected_files = repo.file_count or 0
            if manifest_count is not None:
                expected_files = int(manifest_count)
            elif expected_files <= 0:
                expected_files = docchain_total or indexed_files

            pending_from_counts = max(expected_files - min(indexed_files, expected_files), 0)
            pending_files = pending_from_counts
            if fetched_pending is not None:
                pending_files = max(pending_files, min(fetched_pending, expected_files))
            if summary_missing is not None:
                pending_files = max(pending_files, int(summary_missing))

            repo_stats.append({
                "repo_id": repo.id,
                "repo_url": repo.repo_url,
                "branch": repo.branch,
                "topic_id": repo.code_topic_id,
                "pending_files": int(pending_files or 0),
                "total_files": int(expected_files or 0),
                "indexed_files": int(indexed_files or 0),
                "docchain_total_files": int(docchain_total or 0),
                "unexpected_files": int(unexpected_files or 0),
                "status_breakdown": breakdown,
                "data_source": data_source,
                "last_sync_time": repo.last_sync_time.isoformat() if repo.last_sync_time else None,
                "expected_files": int(expected_files or 0),
                "difference": int((indexed_files or 0) - (expected_files or 0)),
                "missing_files": int(summary_missing or 0),
                "duplicate_files": duplicate_files,
            })

            total_pending += int(pending_files or 0)
            total_expected += int(expected_files or 0)
            total_indexed += int(indexed_files or 0)
            total_unexpected += int(unexpected_files or 0)

            file_count_hint: Optional[int] = None
            if manifest_count is not None:
                file_count_hint = int(manifest_count)
            update_payload.append((
                repo.id,
                int(indexed_files or 0),
                int(pending_files or 0),
                file_count_hint,
            ))

        self._persist_repo_counts(update_payload)
        return repo_stats, total_pending, total_expected, total_indexed, total_unexpected

    def _extract_topic_counts(self, status_data: Dict[str, Any]) -> Tuple[int, int, Dict[str, int]]:
        if not status_data:
            return 0, 0, {}

        numeric_status: Dict[str, int] = {}
        for key, value in status_data.items():
            if isinstance(value, (int, float)):
                numeric_status[key] = int(value)
                continue
            try:
                numeric_status[key] = int(str(value))
            except (ValueError, TypeError):
                continue

        total = numeric_status.get("total")
        success = None
        for candidate in ("success", "finished", "completed"):
            if candidate in numeric_status:
                success = numeric_status[candidate]
                break

        pending_components = sum(
            value for key, value in numeric_status.items()
            if key not in {"total", "success", "finished", "completed"}
        )

        if total is None:
            base = success or 0
            total = base + pending_components

        if success is None:
            success = max(total - pending_components, 0)

        pending = max(total - success, pending_components)

        breakdown = {
            key: numeric_status[key]
            for key in numeric_status
            if key != "total"
        }

        return max(total, 0), max(pending, 0), breakdown

    def _persist_repo_counts(self, updates: List[Tuple[int, int, int, Optional[int]]]):
        if not updates:
            return

        try:
            with session_scope() as session:
                changed = False
                for repo_id, total, pending, file_count in updates:
                    repo = session.get(AiDwGitRepository, repo_id)
                    if not repo:
                        continue
                    normalized_total = int(total or 0)
                    normalized_pending = int(pending or 0)
                    normalized_file_count = int(file_count) if file_count is not None else None
                    if (
                        repo.doc_total_files != normalized_total
                        or repo.doc_pending_files != normalized_pending
                        or (
                            normalized_file_count is not None
                            and repo.file_count != normalized_file_count
                        )
                    ):
                        repo.doc_total_files = normalized_total
                        repo.doc_pending_files = normalized_pending
                        if normalized_file_count is not None:
                            repo.file_count = normalized_file_count
                        session.add(repo)
                        changed = True
                if changed:
                    session.commit()
        except Exception as exc:
            logger.warning(f"保存仓库DocChain文件统计失败: {exc}")

    def _repo_summary_cache_key(self, wiki_id: str) -> str:
        return f"wiki:sync:summary:{wiki_id}"

    def _refresh_flag_cache_key(self, wiki_id: str) -> str:
        return f"wiki:sync:summary:refreshing:{wiki_id}"

    def _cache_repo_summary(self, wiki_id: str, summary: Dict[str, Any], ttl: Optional[int] = None) -> None:
        if not summary:
            return

        prepared = self._prepare_summary_payload(summary)

        ttl = ttl or self._summary_cache_ttl

        if not self._memory_cache_only and self._redis:
            try:
                payload = json.dumps(prepared, ensure_ascii=False)
                key = self._repo_summary_cache_key(wiki_id)
                self._redis.set(key, payload)
                try:
                    self._redis.expire(key, ttl)
                except Exception:
                    self._redis.expire(key, self._summary_cache_ttl)
                return
            except Exception as exc:  # pragma: no cover
                logger.warning("缓存Wiki %s 同步统计到Redis失败: %s，切换为本地缓存", wiki_id, exc)
                self._memory_cache_only = True
                self._redis = None

        expires_at = time.time() + max(ttl, 30)
        with self._summary_lock:
            self._summary_cache[wiki_id] = (expires_at, prepared)

    def _get_cached_repo_summary(self, wiki_id: str) -> Optional[Dict[str, Any]]:
        now = time.time()
        if not self._memory_cache_only and self._redis:
            try:
                cached_text = self._redis.get(self._repo_summary_cache_key(wiki_id))
                if cached_text:
                    if isinstance(cached_text, bytes):
                        cached_text = cached_text.decode("utf-8")
                    summary = json.loads(cached_text)
                    return dict(summary)
                return None
            except Exception as exc:  # pragma: no cover
                logger.warning("读取Redis缓存 Wiki %s 失败: %s，切换为本地缓存", wiki_id, exc)
                self._memory_cache_only = True
                self._redis = None

        with self._summary_lock:
            cached = self._summary_cache.get(wiki_id)
            if cached:
                expires_at, payload = cached
                if expires_at > now:
                    return dict(payload)
                self._summary_cache.pop(wiki_id, None)
        return None

    def _prepare_summary_payload(self, summary: Dict[str, Any]) -> Dict[str, Any]:
        prepared = dict(summary or {})
        if "status" not in prepared:
            prepared["status"] = "ready"
        ts = datetime.now(timezone.utc).isoformat()
        prepared["refreshed_at"] = ts
        prepared.setdefault("pending_files", 0)
        prepared.setdefault("total_files", 0)
        prepared.setdefault("indexed_files", 0)
        repos = prepared.get("repositories")
        if isinstance(repos, list):
            prepared["repositories"] = [dict(repo) for repo in repos]
        else:
            prepared["repositories"] = []
        return prepared

    def _schedule_repo_summary_refresh(self, wiki_id: str) -> None:
        with self._summary_lock:
            if wiki_id in self._summary_refreshing:
                return
            self._summary_refreshing.add(wiki_id)

        flag_key = self._refresh_flag_cache_key(wiki_id)
        flag_registered = False
        if self._redis:
            try:
                granted = self._redis.setnx(flag_key, int(time.time()))
                if granted:
                    try:
                        self._redis.expire(flag_key, 300)
                    except Exception:
                        self._redis.delete(flag_key)
                        granted = False
                if not granted:
                    with self._summary_lock:
                        self._summary_refreshing.discard(wiki_id)
                    return
                flag_registered = True
            except Exception as exc:  # pragma: no cover
                logger.warning("设置Wiki %s 同步统计刷新标记失败: %s", wiki_id, exc)
                self._memory_cache_only = True
                self._redis = None

        def cleanup() -> None:
            if flag_registered and self._redis:
                try:
                    self._redis.delete(flag_key)
                except Exception:  # pragma: no cover
                    pass
            with self._summary_lock:
                self._summary_refreshing.discard(wiki_id)

        def worker() -> None:
            try:
                summary = self._calculate_repo_sync_backlog(wiki_id)
                if summary:
                    summary.setdefault("status", "ready")
                    self._cache_repo_summary(wiki_id, summary)
            except Exception as exc:  # pragma: no cover
                logger.debug("刷新Wiki %s 同步统计失败: %s", wiki_id, exc)
            finally:
                cleanup()

        try:
            self._summary_executor.submit(worker)
        except Exception as exc:  # pragma: no cover
            logger.warning("调度Wiki %s 同步统计刷新失败: %s", wiki_id, exc)
            cleanup()

    def _fetch_topic_status_with_retry(self, topic_id: Optional[str]) -> Optional[Dict[str, Any]]:
        if not topic_id or not self.docchain_manager:
            return None
        last_error: Optional[Exception] = None
        for attempt in range(3):
            try:
                status = self.docchain_manager.get_topic_status(topic_id)
                if status:
                    return status
            except Exception as exc:
                last_error = exc
                logger.debug(f"Topic {topic_id} status fetch attempt {attempt + 1} failed: {exc}")
            time.sleep(0.4)
        if last_error:
            logger.warning(f"获取Topic {topic_id} 状态多次失败: {last_error}")
        return None

    def _store_latest_job(self, wiki_id: str, job_id: str):
        if self._redis:
            try:
                self._redis.set(f"wiki:sync:latest:{wiki_id}", job_id, expiration=48 * 3600)
                return
            except Exception:
                pass
        # fallback
        setattr(self, "_latest_job_cache", getattr(self, "_latest_job_cache", {}))
        self._latest_job_cache[wiki_id] = job_id

    def _get_latest_job_id(self, wiki_id: str) -> Optional[str]:
        if self._redis:
            try:
                job_id = self._redis.get(f"wiki:sync:latest:{wiki_id}")
                if isinstance(job_id, bytes):
                    job_id = job_id.decode('utf-8')
                return job_id
            except Exception:
                pass
        cache = getattr(self, "_latest_job_cache", {})
        return cache.get(wiki_id)

    def _build_cached_repo_summary(self, wiki_id: str) -> Dict[str, Any]:
        cached_summary = self._get_cached_repo_summary(wiki_id)
        if cached_summary is not None:
            return cached_summary

        try:
            with session_scope() as session:
                relations = session.exec(
                    select(AiDwWikiRepositoryRelation)
                    .where(AiDwWikiRepositoryRelation.wiki_id == wiki_id)
                    .order_by(AiDwWikiRepositoryRelation.created_time.asc())
                ).all()

                if not relations:
                    return {
                        "pending_files": 0,
                        "total_files": 0,
                        "indexed_files": 0,
                        "unexpected_files": 0,
                        "repositories": [],
                    }

                repo_ids = [relation.repository_id for relation in relations]
                repo_rows = session.exec(
                    select(AiDwGitRepository).where(AiDwGitRepository.id.in_(repo_ids))
                ).all()

                repo_snapshots: Dict[int, Dict[str, Any]] = {}
                for repo in repo_rows:
                    repo_snapshots[repo.id] = {
                        "id": repo.id,
                        "repo_url": repo.repo_url,
                        "branch": repo.branch,
                        "code_topic_id": repo.code_topic_id,
                        "doc_total_files": repo.doc_total_files or 0,
                        "doc_pending_files": repo.doc_pending_files or 0,
                        "file_count": repo.file_count or 0,
                        "last_sync_time": repo.last_sync_time,
                        "last_sync_commit": getattr(repo, "last_sync_commit", None),
                        "repo_owner": repo.repo_owner,
                        "repo_name": repo.repo_name,
                    }

                ordered_repos = [repo_snapshots.get(rel.repository_id) for rel in relations if repo_snapshots.get(rel.repository_id)]

            repo_stats: List[Dict[str, Any]] = []
            total_pending = 0
            total_expected = 0
            total_indexed = 0

            for repo in ordered_repos:
                indexed = max(int(repo.get("doc_total_files", 0)), 0)
                pending = max(int(repo.get("doc_pending_files", 0)), 0)
                expected = int(repo.get("file_count", 0))
                if expected <= 0:
                    expected = indexed + pending
                if expected < indexed:
                    expected = indexed

                # 计算仓库的命名空间、缓存路径以及本地/上次同步的commit
                repo_owner = repo.get("repo_owner")
                repo_name = repo.get("repo_name")
                branch = repo.get("branch")
                namespace = git_utils.build_repo_namespace(repo_owner, repo_name, branch)
                cache_path = git_utils.get_repo_cache_path(repo_owner, repo_name, branch)
                head_commit = git_utils.get_head_commit(cache_path)
                last_sync_commit = repo.get("last_sync_commit")
                commit_status = "unknown"
                if head_commit and last_sync_commit:
                    commit_status = "synced" if head_commit == last_sync_commit else "diverged"
                elif head_commit:
                    commit_status = "head-only"
                elif last_sync_commit:
                    commit_status = "historic"

                if commit_status == "diverged" and expected > 0:
                    # 本地HEAD领先于上次同步，临时把所有文件视为待处理，提示前端尽快触发同步
                    pending = expected
                    indexed = max(expected - pending, 0)
                else:
                    indexed = max(expected - pending, 0)

                repo_stats.append({
                    "repo_id": repo.get("id"),
                    "repo_url": repo.get("repo_url"),
                    "branch": repo.get("branch"),
                    "topic_id": repo.get("code_topic_id"),
                    "pending_files": int(pending),
                    "total_files": int(expected),
                    "indexed_files": int(indexed),
                    "docchain_total_files": int(indexed + pending),
                    "unexpected_files": 0,
                    "status_breakdown": {},
                    "data_source": "cached-db",
                    "last_sync_time": repo.get("last_sync_time").isoformat() if repo.get("last_sync_time") else None,
                    "expected_files": int(expected),
                    "difference": int(indexed - expected),
                    "missing_files": int(pending),
                    "duplicate_files": 0,
                    "last_sync_commit": last_sync_commit,
                    "head_commit": head_commit,
                    "commit_status": commit_status,
                    "namespace": namespace,
                })

                total_pending += int(pending)
                total_expected += int(expected)
                total_indexed += int(indexed)

            result = {
                "pending_files": total_pending,
                "total_files": total_expected,
                "indexed_files": total_indexed,
                "unexpected_files": 0,
                "repositories": repo_stats,
            }

            if self._has_valid_sync_data(result):
                self._cache_repo_summary(wiki_id, result)
            return result

        except Exception as exc:
            logger.error(f"加载Wiki {wiki_id} 已缓存仓库统计失败: {exc}", exc_info=True)
            return {
                "pending_files": 0,
                "total_files": 0,
                "indexed_files": 0,
                "unexpected_files": 0,
                "repositories": [],
            }


# 单例实例
_sync_index_service = None

def get_sync_index_service() -> SyncIndexService:
    """获取同步索引服务单例"""
    global _sync_index_service
    if _sync_index_service is None:
        from api.database.base import get_db_service
        from api.docchain.manager import docchain_manager
        
        db_service = get_db_service()
        _sync_index_service = SyncIndexService(db_service, docchain_manager)
    
    return _sync_index_service
