"""同步 worker 客户端服务，负责与独立同步进程通信。"""
from __future__ import annotations

import asyncio
import logging
from typing import Any, Dict, Optional

import httpx
from fastapi import HTTPException

from api.config import get_sync_worker_base_url, configs


logger = logging.getLogger(__name__)


class SyncWorkerService:
    """封装与 Sync Worker 的交互，统一转发与容错逻辑."""

    _DEFAULT_TOTAL = 30.0
    _DEFAULT_CONNECT = 5.0
    _DEFAULT_TIMEOUT = httpx.Timeout(_DEFAULT_TOTAL, connect=_DEFAULT_CONNECT)
    _SKIP_HEADERS = {"host", "content-length", "connection"}

    def __init__(
        self,
        base_url: Optional[str] = None,
        timeout: Optional[httpx.Timeout] = None,
    ) -> None:
        config = configs.get("sync_worker", {}) if configs else {}

        resolved_base = base_url if base_url is not None else config.get("base_url")
        self._base_url = (resolved_base if resolved_base is not None else get_sync_worker_base_url()) or ""

        verify_setting = config.get("verify_ssl") if config else None
        self._verify = self._coerce_bool(verify_setting, default=True)

        await_timeout = config.get("await_timeout_seconds") if config else None
        effective_total = None

        if timeout is not None:
            self._timeout = timeout
            effective_total = getattr(timeout, "read", None)
            if not effective_total:
                effective_total = getattr(timeout, "write", None)
        else:
            total_seconds = config.get("http_timeout_seconds")
            connect_seconds = config.get("http_connect_timeout_seconds")
            if total_seconds is not None or connect_seconds is not None:
                total = float(total_seconds) if total_seconds is not None else self._DEFAULT_TOTAL
                connect = float(connect_seconds) if connect_seconds is not None else self._DEFAULT_CONNECT
                self._timeout = httpx.Timeout(timeout=total, connect=connect)
                effective_total = total
            else:
                self._timeout = self._DEFAULT_TIMEOUT
                effective_total = self._DEFAULT_TOTAL

        if await_timeout is not None:
            try:
                self._await_timeout = float(await_timeout)
            except (TypeError, ValueError):
                self._await_timeout = effective_total or self._DEFAULT_TOTAL
        else:
            self._await_timeout = effective_total or self._DEFAULT_TOTAL

    @property
    def base_url(self) -> Optional[str]:
        """返回 Sync Worker 基础地址，未配置时返回 ``None``."""

        return self._base_url or None

    @property
    def timeout(self) -> httpx.Timeout:
        return self._timeout

    @property
    def verify(self) -> bool:
        return self._verify

    @property
    def await_timeout(self) -> Optional[float]:
        return self._await_timeout

    def is_enabled(self) -> bool:
        """当前是否已配置 Sync Worker."""

        return bool(self.base_url)

    async def get_index_progress(
        self,
        wiki_id: str,
        *,
        headers: Optional[Dict[str, str]] = None,
        query: Optional[str] = None,
        raise_on_error: bool = True,
    ) -> Optional[Dict[str, Any]]:
        return await self._request(
            "GET",
            f"/wiki/{wiki_id}/index-progress",
            headers=headers,
            query=query,
            raise_on_error=raise_on_error,
        )

    async def get_queue_overview(
        self,
        *,
        headers: Optional[Dict[str, str]] = None,
        raise_on_error: bool = True,
    ) -> Optional[Dict[str, Any]]:
        """获取同步队列与运行任务的整体快照。"""
        return await self._request(
            "GET",
            "/sync/overview",
            headers=headers,
            raise_on_error=raise_on_error,
        )

    async def trigger_sync_index(
        self,
        wiki_id: str,
        *,
        strategy: str = "main_topic",
        user_id: Optional[int] = None,
        headers: Optional[Dict[str, str]] = None,
        raise_on_error: bool = True,
    ) -> Optional[Dict[str, Any]]:
        payload: Dict[str, Any] = {"strategy": strategy or "main_topic"}
        if user_id is not None:
            payload["user_id"] = user_id

        return await self._request(
            "POST",
            f"/wiki/{wiki_id}/sync-index",
            json=payload,
            headers=headers,
            raise_on_error=raise_on_error,
        )

    async def cancel_sync_job(
        self,
        wiki_id: str,
        *,
        job_id: Optional[str] = None,
        reason: Optional[str] = None,
        headers: Optional[Dict[str, str]] = None,
        raise_on_error: bool = True,
    ) -> Optional[Dict[str, Any]]:
        payload: Dict[str, Any] = {}
        if job_id:
            payload["job_id"] = job_id
        if reason:
            payload["reason"] = reason

        return await self._request(
            "POST",
            f"/wiki/{wiki_id}/sync-index/cancel",
            json=payload,
            headers=headers,
            raise_on_error=raise_on_error,
        )

    async def _request(
        self,
        method: str,
        path: str,
        *,
        json: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        query: Optional[str] = None,
        raise_on_error: bool = True,
    ) -> Optional[Dict[str, Any]]:
        """核心请求逻辑，负责构造 URL、过滤头信息并处理异常."""

        if not self.is_enabled():
            return None

        base = self.base_url.rstrip("/") if self.base_url else ""
        url = f"{base}{path}"
        if query:
            url = f"{url}?{query}"

        forward_headers = self._filter_headers(headers)

        try:
            async with httpx.AsyncClient(
                timeout=self._timeout,
                follow_redirects=True,
                verify=self._verify,
            ) as client:
                request_coro = client.request(method, url, json=json, headers=forward_headers)
                if self._await_timeout and self._await_timeout > 0:
                    response = await asyncio.wait_for(request_coro, timeout=self._await_timeout)
                else:
                    response = await request_coro
        except asyncio.TimeoutError:
            logger.warning(
                "SyncWorker request timed out (%s %s) after %.1fs",
                method,
                url,
                self._await_timeout or 0,
            )
            return None
        except httpx.RequestError as exc:
            logger.warning("SyncWorker request failed (%s %s): %s", method, url, exc)
            return None

        if response.status_code >= 400:
            detail = response.text or response.reason_phrase
            if raise_on_error:
                raise HTTPException(status_code=response.status_code, detail=detail)
            logger.warning(
                "SyncWorker responded with error (%s %s): %s - %s",
                method,
                url,
                response.status_code,
                detail,
            )
            return None

        if not response.content:
            return {}

        try:
            return response.json()
        except ValueError:
            return {}

    def _filter_headers(self, headers: Optional[Dict[str, str]]) -> Optional[Dict[str, str]]:
        if not headers:
            return None
        filtered = {
            key: value
            for key, value in headers.items()
            if key.lower() not in self._SKIP_HEADERS
        }
        return filtered or None

    @staticmethod
    def _coerce_bool(value: Any, *, default: bool) -> bool:
        if value is None:
            return default
        if isinstance(value, bool):
            return value
        if isinstance(value, (int, float)):
            return bool(value)
        if isinstance(value, str):
            lowered = value.strip().lower()
            if lowered in {"false", "0", "no", "off", "n"}:
                return False
            if lowered in {"true", "1", "yes", "on", "y"}:
                return True
            return default
        return default


_sync_worker_service: Optional[SyncWorkerService] = None


def get_sync_worker_service() -> SyncWorkerService:
    """获取 SyncWorkerService 单例."""

    global _sync_worker_service
    if _sync_worker_service is None:
        _sync_worker_service = SyncWorkerService()
    return _sync_worker_service
