import logging
import os
import re
from typing import Dict, Any, Optional, List, Callable, TypeVar, Union, Tuple
import shutil
import json
import asyncio
import time
import functools
from pathlib import Path
from datetime import datetime

from api.database.service import DatabaseService
from api.database.base import session_scope
from api.model.wiki_info import WikiInfo
from api.service.linux_user_group_service import get_linux_user_group_service
from api.service.wiki_service import update_job, get_job
from api.service.wiki_info_service import update_wiki_info, get_wiki_info
from api.data_pipeline import download_repo
from api.docchain.manager import DocChainManager
from api.openai_client import OpenAIClient
from api.openrouter_client import OpenRouterClient
from api.gemini_cli_client import GeminiCliClient
from adalflow.components.model_client.ollama_client import OllamaClient
from adalflow.core.types import ModelType
import google.generativeai as genai
from xml.etree import ElementTree as ET
from pathlib import Path
from pydantic import BaseModel
from api.model.base import SubRepoInfo
from api.utils import git_utils
from api.model.wiki_content import AiDwWikiContent
from api.utils.job_redis_utils import get_wiki_group_id
from api.wiki.sync_worker_service import get_sync_worker_service
from api.langfuse_utils import (
    bind_langfuse_span,  # 绑定Langfuse跨度到固定context，避免上下文错位
    build_langfuse_metadata,
    create_langfuse_span,
    flush_langfuse,
    get_langfuse_context,
)

# 定义一个返回类型变量用于装饰器
T = TypeVar('T')

# 重试装饰器，支持同步和异步函数
def retry(max_retries=3, delay=2, backoff=2, exceptions=(Exception,)):
    """
    重试装饰器，可用于同步和异步函数
    
    参数:
        max_retries: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff: 延迟时间的倍数（指数增长）
        exceptions: 需要重试的异常类型
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            retry_count = 0
            current_delay = delay
            
            while True:
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    retry_count += 1
                    if retry_count > max_retries:
                        logging.error(f"函数 {func.__name__} 重试 {max_retries} 次后仍失败: {e}")
                        raise
                    
                    logging.warning(f"函数 {func.__name__} 执行失败 (尝试 {retry_count}/{max_retries}): {e}")
                    logging.info(f"等待 {current_delay} 秒后重试...")
                    time.sleep(current_delay)
                    current_delay *= backoff
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            retry_count = 0
            current_delay = delay
            
            while True:
                try:
                    return await func(*args, **kwargs)
                except exceptions as e:
                    retry_count += 1
                    if retry_count > max_retries:
                        logging.error(f"异步函数 {func.__name__} 重试 {max_retries} 次后仍失败: {e}")
                        raise
                    
                    logging.warning(f"异步函数 {func.__name__} 执行失败 (尝试 {retry_count}/{max_retries}): {e}")
                    logging.info(f"等待 {current_delay} 秒后重试...")
                    await asyncio.sleep(current_delay)
                    current_delay *= backoff
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        return sync_wrapper
    
    return decorator

# 不从api.api导入，避免循环导入问题
class WikiCacheData(BaseModel):
    """
    Model for the data to be stored in the wiki cache.
    """
    wiki_structure: Dict
    generated_pages: Dict
    repo: Dict
    provider: str
    model: str

logger = logging.getLogger(__name__)

def _format_user_identifier(
    user_name: Optional[str],
    user_code: Optional[str],
    fallback_user_id: Optional[Any] = None,
) -> Tuple[Optional[str], Optional[str], Optional[str]]:
    """返回 (user_id_for_trace, user_code, user_label)。"""

    effective_code = user_code
    if not effective_code and fallback_user_id is not None:
        effective_code = str(fallback_user_id)

    user_label = None
    if user_name and effective_code:
        user_label = f"{user_name}[{effective_code}]"
    elif effective_code:
        user_label = str(effective_code)
    elif user_name:
        user_label = user_name

    user_id_for_trace = user_label or (str(fallback_user_id) if fallback_user_id is not None else None)
    return user_id_for_trace, effective_code, user_label


def _sanitize_message(message: Optional[str], max_length: int = 200) -> str:
    """清理并截断状态消息，避免超出数据库长度限制。"""
    if not message:
        return ""
    compact = " ".join(str(message).split())
    if len(compact) <= max_length:
        return compact
    return f"{compact[: max_length - 3]}..."


def _truncate_for_langfuse(value: Optional[Any], limit: int = 2000) -> Optional[str]:
    """截断文本以防止向Langfuse提交超出限制的内容。"""

    if value is None:
        return None

    text = str(value)
    if len(text) <= limit:
        return text

    truncated = text[:limit]
    return f"{truncated}… (截断 {len(text) - limit} 字符)"

class WikiGenerator:
    # 定义状态常量
    STATUS_PENDING = "pending"
    STATUS_PROCESSING = "processing"
    STATUS_DOWNLOADING = "downloading"
    STATUS_UPLOADING = "uploading"
    STATUS_GENERATING = "generating"
    STATUS_COMPLETED = "completed"
    STATUS_FAILED = "failed"
    
    # 定义阶段常量
    STAGE_INIT = "init"
    STAGE_DOWNLOAD = "download"
    STAGE_REFRESH = "refresh"  # 新增refresh阶段
    STAGE_UPLOAD = "upload"
    STAGE_STRUCTURE = "structure"
    STAGE_PAGES = "pages"
    STAGE_COMPLETED = "completed"
    
    def __init__(self, db_service: DatabaseService, docchain_manager: Optional[DocChainManager] = None):
        self.db_service = db_service
        self.docchain_manager = docchain_manager
        self._job_manager = None  # 将在需要时设置

    def set_job_manager(self, job_manager):
        """设置JobManager引用，用于安全状态更新"""
        self._job_manager = job_manager

    def _ensure_job_type(self, job_id: str, expected_type: int, force: bool = False) -> None:
        """保证任务类型字段正确，避免旧任务缺失类型导致前端显示异常"""
        try:
            with session_scope() as session:
                job = get_job(session, job_id)
                if not job:
                    return

                current_type = getattr(job, "job_type", None)
                if force:
                    if current_type != expected_type:
                        update_job(session, job_id, job_type=expected_type)
                else:
                    if current_type is None:
                        update_job(session, job_id, job_type=expected_type)
        except Exception as ensure_err:
            logger.debug(f"更新job_type失败(job_id={job_id}): {ensure_err}")

    async def start(
        self, 
        job_id: str, 
        wiki_id: str,
        repo_url: str, 
        repo_type: str, 
        branch: str, 
        token: Optional[str], 
        language: str, 
        model_settings: Dict[str, Any],
        comprehensive: bool = True,
        job_context=None,  # 新增：job上下文，用于安全状态更新
        sub_repos_str: Optional[str] = None,  # 新增子仓库参数，字符串
        fast_mode: bool = True,
        **kwargs
    ):
        """
        启动完整的Wiki生成流程（支持智能阶段恢复）
        """
        # 检查是否是refresh模式
        is_refresh = kwargs.get('is_refresh', False)

        if is_refresh:
            # 强制标记刷新任务类型，避免旧数据缺失
            self._ensure_job_type(job_id, expected_type=1, force=True)
            # 调用refresh方法
            return await self.refresh(
                job_id=job_id,
                wiki_id=wiki_id,
                repo_url=repo_url,
                repo_type=repo_type,
                branch=branch,
                token=token,
                language=language,
                model_settings=model_settings,
                comprehensive=comprehensive,
                job_context=job_context,
                sub_repos_str=sub_repos_str,
                **kwargs
            )
        # 确定任务类型（通过数据库查询）
        is_refresh_job = False
        job_record: Optional[Any] = None
        job_creator_id: Optional[Any] = None
        try:
            with session_scope() as session:
                job_record = get_job(session, job_id)
                if job_record and hasattr(job_record, 'job_type'):
                    is_refresh_job = (job_record.job_type == 1)  # 1表示刷新任务
                    if not is_refresh_job:
                        # 如果任务类型缺失，补齐为生成任务
                        self._ensure_job_type(job_id, expected_type=0)
                if job_record:
                    job_creator_id = getattr(job_record, "created_by", None)
        except Exception as e:
            logger.warning(f"无法获取任务类型，默认为生成任务: {e}")

        job_kwargs = getattr(job_context, "kwargs", {}) if job_context else {}
        user_code_ctx = job_kwargs.get("user_code")
        user_name_ctx = job_kwargs.get("user_name")
        user_id_ctx = job_kwargs.get("user_id")
        session_id_ctx = job_kwargs.get("session_id") or job_id

        # 兼容上下文中直接传入的任务创建者信息，避免缺失字段导致异常
        context_job_creator = job_kwargs.get("job_creator") or job_kwargs.get("created_by")
        if context_job_creator is not None:
            job_creator_id = context_job_creator

        langfuse_ctx = get_langfuse_context()
        langfuse_enabled = bool(langfuse_ctx.get("enabled"))
        langfuse_env = langfuse_ctx.get("environment")

        conversation_type = "wiki_refresh" if is_refresh_job else "wiki_generation"

        fallback_user_id = user_id_ctx if user_id_ctx is not None else job_creator_id
        user_id_for_trace, effective_code, user_label = _format_user_identifier(
            user_name_ctx,
            user_code_ctx,
            fallback_user_id,
        )

        labels: List[str] = []

        def _append_label(value: Optional[str]) -> None:
            if value and value not in labels:
                labels.append(value)

        _append_label(user_label)
        _append_label(str(wiki_id) if wiki_id else None)
        if repo_url:
            _append_label(f"repo:{repo_url}")
        if branch:
            _append_label(f"branch:{branch}")

        root_base_metadata: Dict[str, Any] = {
            "session_id": session_id_ctx,
            "job_id": job_id,
            "wiki_id": wiki_id,
            "repo_url": repo_url,
            "branch": branch,
            "language": language,
            "environment": langfuse_env,
            "labels": labels,
        }

        if user_id_for_trace:
            root_base_metadata["user_id"] = user_id_for_trace
        if effective_code:
            root_base_metadata["user_code"] = effective_code
        if user_name_ctx:
            root_base_metadata["user_name"] = user_name_ctx
        if user_label:
            root_base_metadata["user_label"] = user_label

        if isinstance(model_settings, dict):
            provider_val = model_settings.get("provider")
            model_val = model_settings.get("model")
            if provider_val:
                root_base_metadata["provider"] = provider_val
            if model_val:
                root_base_metadata["model"] = model_val

        root_metadata = build_langfuse_metadata(
            root_base_metadata,
            conversation_type,
            stage="init",
        )

        # 绑定Langfuse根跨度到当前context，确保进入与退出在同一上下文中执行
        root_span_handle = bind_langfuse_span(
            create_langfuse_span(
                langfuse_enabled,
                conversation_type,
                root_metadata,
            )
        )
        root_span = root_span_handle.enter()
        root_span_closed = not bool(root_span)

        def _close_root_span(exc_type=None, exc_val=None, exc_tb=None):
            nonlocal root_span_closed
            if not root_span_closed:
                root_span_handle.exit(exc_type, exc_val, exc_tb)
                root_span_closed = True

        if root_span:
            safe_model_settings = {}
            if isinstance(model_settings, dict):
                safe_model_settings = {
                    key: value
                    for key, value in model_settings.items()
                    if key != "api_key"
                }
            root_span.update(
                input={
                    "repo_url": repo_url,
                    "branch": branch,
                    "language": language,
                    "model_settings": safe_model_settings,
                    "refresh_request": is_refresh_job,
                }
            )
            if langfuse_enabled:
                flush_langfuse()

        langfuse_stage_settings = {
            "enabled": langfuse_enabled,
            "base_metadata": root_base_metadata,
            "conversation_type": conversation_type,
        }
        # 提取root跨度的trace上下文，传递给子阶段，确保所有阶段归属同一trace
        root_trace_context = None
        if root_span and hasattr(root_span, "trace_id"):
            try:
                root_trace_context = {"trace_id": str(getattr(root_span, "trace_id", ""))}
                if hasattr(root_span, "id"):
                    root_trace_context["parent_span_id"] = getattr(root_span, "id", None)
            except Exception:
                root_trace_context = None
        langfuse_stage_settings["trace_context"] = root_trace_context

        def _make_status_updater(sync_wiki: bool):
            def _updater(status, progress, stage, stage_progress, stage_message, **kw):
                processed = kw.get("processed_files")
                total = kw.get("total_files")
                error_message = kw.get("error_message")
                clean_stage_message = _sanitize_message(stage_message)
                clean_error_message = _sanitize_message(error_message, 500) if error_message else None

                # 如果任务被请求暂停/取消，避免用processing等状态覆盖已取消状态
                try:
                    if job_context and getattr(job_context, 'pause_requested', False):
                        # 取消/暂停时不再推进状态，直接返回
                        return
                except Exception:
                    pass

                if job_context and self._job_manager:
                    self._job_manager._safe_update_job_status(
                        job_context,
                        status,
                        progress,
                        stage,
                        stage_progress,
                        clean_stage_message,
                        processed_files=processed,
                        total_files=total,
                        error_message=clean_error_message,
                    )
                else:
                    self._update_job_status(
                        job_id,
                        status,
                        progress,
                        stage,
                        stage_progress,
                        clean_stage_message,
                        processed_files=processed,
                        total_files=total,
                        error_message=clean_error_message,
                    )

                if sync_wiki:
                    self._update_wiki_info_status(wiki_id, status, clean_stage_message)

            return _updater

        update_status_func = _make_status_updater(sync_wiki=not is_refresh_job)

        # 查询任务中是否包含子仓库，并解析   
        sub_repos = self._parse_sub_repos(sub_repos_str)
        
        # 检查当前任务的阶段状态，决定从哪个阶段开始
        start_stage = self._determine_start_stage(job_id, job_context)
        logger.info(f"任务 {job_id} 将从阶段 '{start_stage}' 开始执行")
        
        update_status_func("processing", 0, start_stage, 0, f"从阶段 '{start_stage}' 开始Wiki生成流程")

        span_exc_info: Tuple[Optional[type], Optional[BaseException], Optional[Any]] = (None, None, None)

        try:
            # 从repo_url提取owner和repo
            owner_name, repo_name, host = git_utils.extract_repo_info(repo_url)
            
            # 初始化变量
            repo_path = None
            structure = None
            generated_pages = {}
            custom_instructions = kwargs.get('custom_instructions')
            
            # 阶段1: 下载代码仓库
            if self._should_execute_stage(start_stage, self.STAGE_DOWNLOAD):
                repo_path = await self._execute_download_stage(
                    job_id, wiki_id, repo_url, repo_type, branch, token, update_status_func, job_context, sub_repos
                )
                # 明确进入上传阶段前追加一次状态更新
                update_status_func(self.STATUS_PROCESSING, 30, self.STAGE_UPLOAD, 0, "代码仓库下载完成，准备分析仓库内容")
            else:
                # 跳过下载阶段，生成本地路径
                repo_path = self._get_local_repo_path(owner_name, repo_name, branch)
                logger.info(f"跳过下载阶段，使用已存在的代码仓库: {repo_path}")
                update_status_func("processing", 30, self.STAGE_DOWNLOAD, 100, "使用已存在的代码仓库")

            # 检查暂停
            if job_context and job_context.pause_requested:
                if job_context.pause_event:
                    job_context.pause_event.wait()

            await self._trigger_index_sync_if_available(wiki_id, job_context)

            # 获取到所有的本地代码仓库路径
            repo_paths = []
            # 父仓库路径
            repo_paths.append(repo_path)
            # 子仓库路径
            if sub_repos:
                for sub_repo in sub_repos:
                    local_sub_repo_path = self._get_local_sub_repo_path(owner_name, repo_name, branch, sub_repo)
                    repo_paths.append(local_sub_repo_path)
            
            # 阶段2: 仓库分析（预处理仓库内容）
            if self._should_execute_stage(start_stage, self.STAGE_UPLOAD):
                # 从kwargs中提取并移除已经作为位置参数传递的参数，避免重复传递
                upload_kwargs = kwargs.copy()
                existing_topic_id = upload_kwargs.pop('existing_topic_id', None)
                existing_topic_id_code = upload_kwargs.pop('existing_topic_id_code', None)
                
                await self._execute_upload_stage(
                    job_id, 
                    wiki_id, 
                    repo_url, 
                    repo_name, 
                    owner_name,
                    branch,
                    repo_paths, 
                    existing_topic_id, 
                    existing_topic_id_code,
                    update_status_func,
                    job_context,
                    fast_mode=fast_mode,
                    **upload_kwargs
                )
                
                # 创建或更新Git仓库记录和关系
                with session_scope() as session:
                    try:
                        self._create_or_update_git_repositories(
                            session, 
                            wiki_id, 
                            repo_url, 
                            branch, 
                            existing_topic_id_code,
                            sub_repos, 
                            None
                        )
                        session.commit()
                        logger.info(f"已创建/更新Git仓库记录和关系，wiki_id: {wiki_id}")
                    except Exception as e:
                        logger.error(f"创建Git仓库记录失败: {e}")
                        session.rollback()
                # 清理分支名（移除特殊字符，使其可以作为目录名）
                import re
                branch_clean = branch
                
                from api.config import get_kubernetes_config
                container_base_path = get_kubernetes_config().get("k8s_config", {}).get("base_path", "/root/.adalflow")
                container_project_workspace_path = f"{container_base_path.rstrip('/')}/project_workspace/{owner_name}/{repo_name}-{branch_clean}/"

                result = self._make_sure_directory_permissions(os.path.dirname(repo_path), container_project_workspace_path, wiki_id)
                if not result:
                    logger.error(f"设置目录权限失败: {repo_path}")
            else:
                logger.info("跳过仓库分析阶段，继续使用现有的本地数据")
                update_status_func("processing", 30, self.STAGE_UPLOAD, 100, "仓库分析已完成")
            
            # 检查暂停
            if job_context and job_context.pause_requested:
                if job_context.pause_event:
                    job_context.pause_event.wait()

            # 阶段3: 生成Wiki结构
            if self._should_execute_stage(start_stage, self.STAGE_STRUCTURE):
                structure = await self._execute_structure_stage(
                    job_id,
                    wiki_id,
                    repo_paths,
                    language,
                    model_settings,
                    comprehensive,
                    update_status_func=update_status_func,
                    job_context=job_context,
                    user_instructions=custom_instructions,
                    langfuse_settings=langfuse_stage_settings,
                    **kwargs,
                )
                # 结构生成完成后，准备进入页面阶段
                update_status_func(self.STATUS_PROCESSING, 40, self.STAGE_PAGES, 0, "Wiki结构生成完成，准备生成页面内容")
            else:
                # 跳过结构生成阶段，从WikiInfo中获取已有结构
                structure = self._get_existing_structure(wiki_id)
                logger.info(f"跳过结构生成阶段，使用已存在的结构，包含 {len(structure.get('pages', []))} 个页面")
                update_status_func("processing", 50, self.STAGE_STRUCTURE, 100, "使用已存在的Wiki结构")

            # 检查暂停
            if job_context and job_context.pause_requested:
                if job_context.pause_event:
                    job_context.pause_event.wait()

            # 阶段4: 生成Wiki页面
            if self._should_execute_stage(start_stage, self.STAGE_PAGES):
                generated_pages = await self._execute_pages_stage(
                    job_id,
                    wiki_id,
                    structure,
                    model_settings,
                    repo_paths,
                    language,
                    update_status_func=update_status_func,
                    job_context=job_context,
                    fast_mode=fast_mode,
                    user_instructions=custom_instructions,
                    langfuse_settings=langfuse_stage_settings,
                )
            else:
                # 跳过页面生成阶段，从WikiInfo中获取已有页面
                generated_pages = self._get_existing_pages(wiki_id)
                logger.info(f"跳过页面生成阶段，使用已存在的 {len(generated_pages)} 个页面")
                update_status_func("processing", 100, self.STAGE_PAGES, 100, "使用已存在的Wiki页面")
            
            # 5. 更新最终状态
            update_status_func("completed", 100, "completed", 100, "Wiki生成成功")
            
            # 先保存到wiki_job的result字段
            wiki_data = {}
            if structure:
                wiki_data["wiki_structure"] = structure
            if generated_pages:
                wiki_data["generated_pages"] = generated_pages

            with session_scope() as session:
                existing_result: Dict[str, Any] = {}
                current_job = get_job(session, job_id)
                if current_job and getattr(current_job, "result", None):
                    try:
                        if isinstance(current_job.result, dict):
                            existing_result = dict(current_job.result)
                    except Exception as result_err:
                        logger.debug(f"读取job结果失败，忽略并重写: {result_err}")

                existing_result.update(
                    {
                        "generation_time": datetime.now().isoformat(),
                        "generation_type": "full_generation",
                        "wiki_data": wiki_data,
                    }
                )

                update_job(session, job_id, result=existing_result)
                logger.info(f"已保存Wiki生成结果到job的result字段，job_id: {job_id}")

            # 最后成功时，将数据写入wiki_info表
            with session_scope() as session:
                job_wiki_data = wiki_data
                
                # 不再写入旧表大字段，仅更新状态；内容落到 ai_dw_wiki_content
                update_wiki_info(
                    session, 
                    wiki_id, 
                    status="completed"
                )
                logger.info(f"已更新WikiInfo状态为completed，wiki_id: {wiki_id}")
                # 同步写入 ai_dw_wiki_content（最终全量写入）
                try:
                    structure_data = job_wiki_data.get("wiki_structure") if job_wiki_data else None
                    pages_data = job_wiki_data.get("generated_pages") if job_wiki_data else None
                    self._upsert_ai_dw_content(session, wiki_id, structure=structure_data, pages=pages_data)

                    # 将wiki内容保存到文件里
                    save_wiki_content(session, wiki_id, structure_data, pages_data)
                except Exception as e:
                    logger.warning(f"写入ai_dw_wiki_content失败(完成阶段)：{e}")

            if root_span:
                root_span.update(
                    output={
                        "status": "completed",
                        "structure_pages": len(structure.get("pages", [])) if structure else 0,
                        "generated_pages": len(generated_pages) if generated_pages else 0,
                    }
                )
                if langfuse_enabled:
                    flush_langfuse()

        except Exception as e:
            error_message = f"Wiki生成失败: {str(e)}"
            logger.error(error_message, exc_info=True)
            span_exc_info = (type(e), e, e.__traceback__)
            
            # 检查是否因暂停导致的异常
            if job_context and job_context.pause_requested:
                logger.info(f"检测到暂停请求，不更新为失败状态")
                return
            
            update_status_func("failed", 0, "failed", 0, error_message)
            # 同时更新WikiInfo的状态
            with session_scope() as session:
                update_wiki_info(session, wiki_id, status="failed", error_message=error_message)

            if root_span:
                try:
                    root_span.update(level="ERROR", status_message=str(e))
                except Exception as span_error:
                    logger.debug(f"更新Langfuse根span失败: {span_error}")
                if langfuse_enabled:
                    flush_langfuse()

            # 重新抛出异常，确保上层能够感知失败并执行统一的失败处理逻辑
            raise
        finally:
            _close_root_span(*span_exc_info)

    def _determine_start_stage(self, job_id: str, job_context=None) -> str:
        """
        根据任务当前状态和数据库记录，决定从哪个阶段开始执行
        """
        try:
            # 优先从job_context获取阶段信息
            if job_context and job_context.current_stage:
                current_stage = job_context.current_stage
                logger.info(f"从JobContext获取当前阶段: {current_stage}")
                
                # 如果任务是从某个阶段中断的，需要从该阶段重新开始
                stage_mapping = {
                    "init": self.STAGE_DOWNLOAD,
                    "download": self.STAGE_DOWNLOAD,
                    "upload": self.STAGE_UPLOAD, 
                    "structure": self.STAGE_STRUCTURE,
                    "pages": self.STAGE_PAGES,
                    "completed": self.STAGE_COMPLETED
                }
                
                return stage_mapping.get(current_stage, self.STAGE_DOWNLOAD)
            
            # 从数据库获取任务信息
            with session_scope() as session:
                job = get_job(session, job_id)
                if job and job.stage:
                    current_stage = job.stage
                    logger.info(f"从数据库获取当前阶段: {current_stage}")
                    
                    # 新增：检查文件处理进度，如果文件已处理完成，尝试推断实际阶段
                    if job.total_files and job.processed_files and job.total_files > 0:
                        file_progress_percent = (job.processed_files / job.total_files) * 100
                        if file_progress_percent >= 100:
                            logger.info(f"任务 {job_id} 文件处理完成 ({job.processed_files}/{job.total_files})，检查阶段状态")
                            
                            # 根据当前阶段和文件进度推断实际应该执行的阶段
                            if current_stage in ["init", "download"]:
                                # 文件处理完成，说明下载阶段已完成，应该进入上传阶段
                                logger.info(f"任务 {job_id} 推断进入上传阶段")
                                return self.STAGE_UPLOAD
                            elif current_stage == "upload" and job.stage_progress >= 100:
                                # 上传阶段已完成，进入结构生成阶段
                                logger.info(f"任务 {job_id} 推断进入结构生成阶段")
                                return self.STAGE_STRUCTURE
                            elif current_stage == "structure" and job.stage_progress >= 100:
                                # 结构生成阶段已完成，进入页面生成阶段
                                logger.info(f"任务 {job_id} 推断进入页面生成阶段")
                                return self.STAGE_PAGES
                            elif current_stage == "pages" and job.stage_progress >= 100:
                                # 页面生成阶段已完成，进入完成阶段
                                logger.info(f"任务 {job_id} 推断进入完成阶段")
                                return self.STAGE_COMPLETED
                    
                    # 根据进度判断实际需要重新执行的阶段
                    if current_stage == "upload" and job.stage_progress < 100:
                        # upload阶段未完成，从upload重新开始
                        return self.STAGE_UPLOAD
                    elif current_stage == "structure" and job.stage_progress < 100:
                        # structure阶段未完成，从structure重新开始  
                        return self.STAGE_STRUCTURE
                    elif current_stage == "pages" and job.stage_progress < 100:
                        # pages阶段未完成，从pages重新开始
                        return self.STAGE_PAGES
                    elif current_stage in ["upload", "structure", "pages"] and job.stage_progress >= 100:
                        # 当前阶段已完成，执行下一阶段
                        next_stage_mapping = {
                            "upload": self.STAGE_STRUCTURE,
                            "structure": self.STAGE_PAGES, 
                            "pages": self.STAGE_COMPLETED
                        }
                        return next_stage_mapping.get(current_stage, self.STAGE_DOWNLOAD)
                        
        except Exception as e:
            logger.error(f"确定开始阶段时出错: {e}")
        
        # 默认从下载阶段开始
        return self.STAGE_DOWNLOAD

    def _should_execute_stage(self, start_stage: str, target_stage: str) -> bool:
        """
        判断是否应该执行指定的阶段
        """
        stage_order = [
            self.STAGE_DOWNLOAD,
            self.STAGE_REFRESH,
            self.STAGE_UPLOAD, 
            self.STAGE_STRUCTURE,
            self.STAGE_PAGES,
            self.STAGE_COMPLETED
        ]
        
        try:
            start_index = stage_order.index(start_stage)
            target_index = stage_order.index(target_stage)
            return target_index >= start_index
        except ValueError:
            # 如果阶段不在列表中，默认执行
            return True

    def _parse_sub_repos(self, sub_repos_str):
        """
        解析sub_repos_str,结构"[{"url":"xxx","branch":"xxx"},{"url":"xxx","branch":"xxx"}]"
        """
        if sub_repos_str:
            try:
                sub_repos_data = json.loads(sub_repos_str)
            except json.JSONDecodeError:
                sub_repos_data = []
        else:
            sub_repos_data = []
        # 转成 Pydantic 对象列表
        return [SubRepoInfo(**item) for item in sub_repos_data]


    def _get_existing_structure(self, wiki_id: str) -> Dict:
        """
        从WikiInfo获取已存在的Wiki结构
        """
        try:
            from sqlmodel import select
            with session_scope() as session:
                content = session.exec(select(AiDwWikiContent.wiki_structure).where(AiDwWikiContent.wiki_id == wiki_id)).first()
                if content:
                    return content
        except Exception as e:
            logger.error(f"获取已存在的Wiki结构时出错: {e}")
        
        # 返回空结构
        return {"pages": [], "sections": []}

    def _get_existing_pages(self, wiki_id: str) -> Dict:
        """
        从WikiInfo获取已存在的Wiki页面
        """
        try:
            from sqlmodel import select
            with session_scope() as session:
                content = session.exec(select(AiDwWikiContent.wiki_pages).where(AiDwWikiContent.wiki_id == wiki_id)).first()
                if content:
                    return content
        except Exception as e:
            logger.error(f"获取已存在的Wiki页面时出错: {e}")
        
        return {}

    def _get_existing_wiki_data(self, wiki_id: str) -> Dict[str, Any]:
        """Convenience wrapper returning the current structure/pages snapshot."""
        data: Dict[str, Any] = {}
        structure = self._get_existing_structure(wiki_id)
        if structure:
            data["wiki_structure"] = structure
        pages = self._get_existing_pages(wiki_id)
        if pages:
            data["generated_pages"] = pages
        return data

    async def _execute_download_stage(self, job_id: str, wiki_id: str, repo_url: str, repo_type: str, 
                                    branch: str, token: Optional[str], update_status_func, job_context=None, sub_repos: Optional[List[SubRepoInfo]]=None) -> str:
        """执行下载阶段"""
        # 从repo_url提取owner和repo
        owner_name, repo_name, host = git_utils.extract_repo_info(repo_url)
        
        # 生成本地仓库路径
        local_repo_path = self._get_local_repo_path(owner_name, repo_name, branch)
        
        logger.info(f"执行下载阶段: {repo_url} -> {local_repo_path}")
        
        # 更新阶段状态
        update_status_func(self.STATUS_PROCESSING, 5, self.STAGE_DOWNLOAD, 0, "准备下载代码仓库")
        
        try:
            # 检查暂停
            if job_context and job_context.pause_requested:
                if job_context.pause_event:
                    job_context.pause_event.wait()
            
            # 检查本地是否已存在仓库
            if os.path.exists(local_repo_path) and os.path.isdir(local_repo_path):
                # 检查是否是有效的Git仓库
                git_dir = os.path.join(local_repo_path, ".git")
                if os.path.exists(git_dir) and os.path.isdir(git_dir):
                    logger.info(f"本地已存在有效的仓库，跳过下载: {local_repo_path}")
                    update_status_func(self.STATUS_PROCESSING, 30, self.STAGE_DOWNLOAD, 100, "使用已存在的代码仓库")
                    return local_repo_path
                else:
                    # 存在但不是有效Git仓库，删除重新下载
                    logger.info(f"本地存在无效仓库，删除后重新下载: {local_repo_path}")
                    shutil.rmtree(local_repo_path)
            
            # 下载代码
            download_repo(repo_url, local_repo_path, repo_type, token, branch)
            logger.info(f"父代码仓库下载完成: {local_repo_path}")
            # 更新进度和任务状态
            update_status_func(self.STATUS_PROCESSING, 20, self.STAGE_DOWNLOAD, 60, "父代码仓库下载完成")
            # 如果子仓库地址存在，则下载子仓库代码
            
            if sub_repos:
                for sub_repo in sub_repos:
                    local_sub_repo_path = self._get_local_sub_repo_path(owner_name, repo_name, branch, sub_repo)
                    download_repo(sub_repo.url, local_sub_repo_path, repo_type, token, sub_repo.branch)
                    logger.info(f"子代码仓库{sub_repo.url}_{sub_repo.branch}下载完成: {local_sub_repo_path}")
                update_status_func(self.STATUS_PROCESSING, 30, self.STAGE_DOWNLOAD, 100, "子代码仓库下载完成")
            else:
                update_status_func(self.STATUS_PROCESSING, 30, self.STAGE_DOWNLOAD, 100, "无子代码仓库，无需下载，仓库下载完成")
            
            return local_repo_path
        except Exception as e:
            logger.error(f"下载阶段失败: {str(e)}", exc_info=True)
            update_status_func(self.STATUS_FAILED, 0, self.STAGE_DOWNLOAD, 0, f"代码仓库下载失败: {str(e)}")
            raise

    async def _trigger_index_sync_if_available(self, wiki_id: str, job_context=None) -> None:
        """代码下载完成后触发索引同步，优先委托 Sync Worker."""

        user_id: Optional[int] = None
        if job_context and getattr(job_context, "kwargs", None):
            user_id = job_context.kwargs.get("user_id")

        sync_worker = get_sync_worker_service()
        if sync_worker.is_enabled():
            try:
                result = await sync_worker.trigger_sync_index(
                    wiki_id,
                    strategy="main_topic",
                    user_id=user_id,
                    raise_on_error=False,
                )
                if result is not None:
                    logger.info("已委托 Sync Worker 触发 Wiki %s 的代码索引同步", wiki_id)
                    return
            except Exception as exc:
                logger.warning("调用 Sync Worker 同步索引失败，准备回退到本地执行: %s", exc)

        try:
            from api.wiki.sync_index_service import get_sync_index_service

            sync_service = get_sync_index_service()
            await sync_service.start_sync_index(wiki_id, "main_topic", user_id)
            logger.info("已在当前进程触发 Wiki %s 的代码索引同步", wiki_id)
        except Exception as exc:  # pragma: no cover - 避免生成流程因索引失败完全终止
            logger.warning("本地触发 Wiki %s 索引同步失败: %s", wiki_id, exc)

    async def _execute_upload_stage(
        self,
        job_id: str,
        wiki_id: str,
        repo_url: str,
        repo_name: str,
        owner_name: str,
        branch: str,
        local_repo_paths: List[str],
        existing_topic_id: Optional[str] = None,
        existing_topic_id_code: Optional[str] = None,
        update_status_func=None,
        job_context=None,
        fast_mode: bool = None,
        **kwargs,
    ) -> tuple[None, List[str]]:
        """分析仓库内容并准备元数据。

        此阶段分析仓库结构和内容，为后续的Wiki生成做准备。
        向量索引同步现在由单独的流程管理。
        """

        if update_status_func:
            update_status_func(
                self.STATUS_PROCESSING,
                30,
                self.STAGE_UPLOAD,
                0,
                "分析仓库内容",
            )

        if job_context and job_context.pause_requested and job_context.pause_event:
            job_context.pause_event.wait()

        if update_status_func:
            update_status_func(
                self.STATUS_PROCESSING,
                30,
                self.STAGE_UPLOAD,
                100,
                "仓库分析完成",
            )

        return None, []

    async def _execute_structure_stage(
        self,
        job_id: str,
        wiki_id: str,
        repo_paths: List[str],
        language: str,
        model_settings: Dict[str, Any],
        comprehensive: bool,
        update_status_func=None,
        job_context=None,
        user_instructions: Optional[str] = None,
        langfuse_settings: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict:
        """
        执行生成Wiki结构的阶段
        """
        langfuse_cfg = langfuse_settings or {}
        langfuse_enabled = bool(langfuse_cfg.get("enabled"))
        base_metadata = langfuse_cfg.get("base_metadata", {})
        conversation_type = langfuse_cfg.get("conversation_type", "wiki_generation")

        structure_span_metadata = build_langfuse_metadata(
            base_metadata,
            conversation_type,
            stage="structure_stage",
            extra={
                "repo_path_count": len(repo_paths),
                "comprehensive": comprehensive,
            },
        )
        trace_ctx = langfuse_cfg.get("trace_context")
        # 绑定结构阶段跨度，保证上下文一致
        structure_span_handle = bind_langfuse_span(
            create_langfuse_span(
                langfuse_enabled,
                f"{conversation_type}.structure_stage",
                structure_span_metadata,
                trace_context=trace_ctx,
            )
        )
        structure_span = structure_span_handle.enter()
        structure_span_closed = not bool(structure_span)

        def _close_structure_span(exc_type=None, exc_val=None, exc_tb=None):
            nonlocal structure_span_closed
            if not structure_span_closed:
                structure_span_handle.exit(exc_type, exc_val, exc_tb)
                structure_span_closed = True

        # 确保progress递增，结构阶段从35%开始
        base_progress = 30
        range_progress = 20
        update_status_func("processing", base_progress, "structure", 0, "开始生成Wiki结构...")

        try:
            logger.info(f"开始结构生成阶段，job_id: {job_id}, wiki_id: {wiki_id}")
            
            # 检查暂停
            if job_context and job_context.pause_requested:
                if job_context.pause_event:
                    job_context.pause_event.wait()
            
            model_client = self._create_model_client(model_settings)
            
            # 构建文件树
            update_status_func("processing", base_progress + 2, "structure", 10, "正在分析代码文件结构...")
            file_tree = self._build_file_tree(
                repo_paths,
                excluded_dirs=kwargs.get('excluded_dirs'),
                excluded_files=kwargs.get('excluded_files'),
                included_dirs=kwargs.get('included_dirs'),
                included_files=kwargs.get('included_files')
            )
            
            # 按估算token对file_tree进行截断，最大80万token（约等于字符数/3）
            try:
                max_tokens = 800_000
                estimated_tokens = int(len(file_tree) / 3)
                if estimated_tokens > max_tokens:
                    max_chars = max_tokens * 3
                    current_chars = 0
                    pieces: List[str] = []
                    for line in file_tree.splitlines(True):  # 保留换行
                        if current_chars + len(line) > max_chars:
                            break
                        pieces.append(line)
                        current_chars += len(line)
                    file_tree = "".join(pieces)
                    logger.info(
                        f"file_tree 过大(约{estimated_tokens} tokens)，已按大小截断至约{int(current_chars/4)} tokens"
                    )
            except Exception as e:
                logger.warning(f"file_tree 截断过程中出现异常: {e}")
            
            # 检查暂停
            if job_context and job_context.pause_requested:
                if job_context.pause_event:
                    job_context.pause_event.wait()
            
            # 尝试读取README文件
            update_status_func("processing", base_progress + 4, "structure", 20, "正在读取项目文档...")
            readme_content = ""
            readme_paths = ["README.md", "Readme.md", "readme.md"]
            # 父子仓库目录
            for repo_path in repo_paths:
                for readme_path in readme_paths:
                    full_path = os.path.join(repo_path, readme_path)
                    if os.path.exists(full_path):
                        try:
                            with open(full_path, 'r', encoding='utf-8') as f:
                                readme_content = f.read()
                            break
                        except Exception as e:
                            logger.warning(f"读取README文件失败: {e}")
            
            # 从repo_url提取owner和repo
            try:
                repo_url_safe = getattr(job_context, 'repo_url', '') if job_context else ''
                if repo_url_safe:
                    owner, repo, _host = git_utils.extract_repo_info(repo_url_safe)
                else:
                    owner, repo = 'unknown', 'unknown'
            except Exception:
                owner, repo = 'unknown', 'unknown'
            
            # 检查暂停
            if job_context and job_context.pause_requested:
                if job_context.pause_event:
                    job_context.pause_event.wait()
            
            # 使用现有的_generate_wiki_structure方法生成Wiki结构
            update_status_func("processing", base_progress + 10, "structure", 50, "正在调用AI生成Wiki结构...")
            logger.info(f"开始调用AI生成Wiki结构，repo: {owner}/{repo}")
            
            structure = await self._generate_wiki_structure(
                file_tree=file_tree,
                readme=readme_content,
                repo_url=getattr(job_context, 'repo_url', ''),
                language=language,
                model_settings=model_settings,
                model_client=model_client,
                comprehensive=comprehensive,
                job_id=job_id,
                job_context=job_context,
                user_instructions=user_instructions,
                langfuse_settings=langfuse_settings,
            )
            
            logger.info(f"Wiki结构生成完成，包含 {len(structure.get('pages', []))} 个页面")

            # 检查暂停
            if job_context and job_context.pause_requested:
                if job_context.pause_event:
                    job_context.pause_event.wait()

            # 将生成的结构保存到WikiInfo
            update_status_func("processing", base_progress + 16, "structure", 90, "正在保存Wiki结构...")
            with session_scope() as session:
                # 先保存到wiki_job的result字段
                current_result = {}
                try:
                    job = get_job(session, job_id)
                    if job and job.result:
                        current_result = job.result
                except Exception as e:
                    logger.warning(f"获取job的result失败: {e}")
                
                # 更新result中的wiki_data
                if "wiki_data" not in current_result:
                    current_result["wiki_data"] = {}
                current_result["wiki_data"]["wiki_structure"] = structure
                
                # 保存到job的result字段
                from api.service.wiki_service import update_job
                update_job(session, job_id, result=current_result)
                logger.info(f"已保存Wiki结构到job的result字段，job_id: {job_id}")
                # 双写至 ai_dw_wiki_content（结构）
                try:
                    self._upsert_ai_dw_content(session, wiki_id, structure=structure)
                except Exception as e:
                    logger.warning(f"写入ai_dw_wiki_content失败(结构阶段)：{e}")
                
                # 暂时不更新wiki_info，等所有阶段完成后再更新
            
            # 结构阶段完成，progress到40%
            update_status_func(
                "processing",
                base_progress + range_progress,
                "structure",
                100,
                "Wiki结构生成完成",
            )
            logger.info(f"结构生成阶段完成，job_id: {job_id}")
            
            if structure_span:
                structure_span.update(
                    output={
                        "page_count": len(structure.get("pages", [])),
                    }
                )
                if langfuse_enabled:
                    flush_langfuse()
            _close_structure_span(None, None, None)
            return structure
        except Exception as e:
            logger.error(f"生成Wiki结构失败，job_id: {job_id}, error: {e}")
            # 新增：在出现异常时，立即更新job状态为失败并记录错误信息
            try:
                if update_status_func:
                    update_status_func("failed", 0, "structure", 0, f"生成Wiki结构失败: {e}")
            except Exception as _e:
                logger.warning(f"更新job状态为失败时出错: {_e}")
            if structure_span:
                try:
                    structure_span.update(level="ERROR", status_message=str(e))
                except Exception:
                    pass
                if langfuse_enabled:
                    flush_langfuse()
            _close_structure_span(type(e), e, e.__traceback__)
            raise Exception(f"生成Wiki结构失败: {e}")

    async def _execute_pages_stage(
        self,
        job_id: str,
        wiki_id: str,
        structure: Dict,
        model_settings: Dict[str, Any],
        repo_paths: List[str],
        language: str,
        update_status_func=None,
        job_context=None,
        fast_mode: bool = None,
        force_regenerate: bool = False,  # 新增：是否强制重新生成所有页面（用于刷新模式）
        specific_pages: List[str] = None,  # 新增：指定要重新生成的页面ID列表
        user_instructions: Optional[str] = None,
        langfuse_settings: Optional[Dict[str, Any]] = None,
    ) -> Dict:
        """
        执行生成Wiki页面的阶段

        参数:
            force_regenerate: 是否强制重新生成所有页面（用于刷新模式）
            specific_pages: 指定要重新生成的页面ID列表，仅在force_regenerate=True时有效
        """
        langfuse_cfg = langfuse_settings or {}
        langfuse_enabled = bool(langfuse_cfg.get("enabled"))
        base_metadata = langfuse_cfg.get("base_metadata", {})
        conversation_type = langfuse_cfg.get("conversation_type", "wiki_generation")

        pages_stage_metadata = build_langfuse_metadata(
            base_metadata,
            conversation_type,
            stage="pages_stage",
            extra={
                "force_regenerate": force_regenerate,
                "specific_pages": specific_pages,
            },
        )
        trace_ctx = langfuse_cfg.get("trace_context")
        # 绑定页面阶段跨度，保证上下文一致
        pages_stage_handle = bind_langfuse_span(
            create_langfuse_span(
                langfuse_enabled,
                f"{conversation_type}.pages_stage",
                pages_stage_metadata,
                trace_context=trace_ctx,
            )
        )
        pages_stage_span = pages_stage_handle.enter()
        pages_stage_closed = not bool(pages_stage_span)

        def _close_pages_stage(exc_type=None, exc_val=None, exc_tb=None):
            nonlocal pages_stage_closed
            if not pages_stage_closed:
                pages_stage_handle.exit(exc_type, exc_val, exc_tb)
                pages_stage_closed = True

        base_progress = 50
        generated_pages = self._get_existing_pages(wiki_id)

        try:
            # 检查暂停
            if job_context and job_context.pause_requested:
                if job_context.pause_event:
                    job_context.pause_event.wait()
            
            model_client = self._create_model_client(model_settings)
            
            # 从repo_url提取owner和repo
            (owner_name, repo_name, host) = git_utils.extract_repo_info(getattr(job_context, "repo_url", ""))
            
            # 筛选未处理的页面
            pages_to_generate = []
            for page_info in structure.get("pages", []):
                page_id = page_info.get("id")
                if not page_id:
                    continue
                
                # 判断是否需要生成该页面
                should_generate = False
                
                if specific_pages and page_id in specific_pages:
                    should_generate = True
                    if force_regenerate:
                        logger.info(f"指定重新生成页面: {page_info.get('title', page_id)}")
                elif force_regenerate:
                    # 强制重新生成所有页面
                    should_generate = True
                    logger.info(f"强制重新生成页面: {page_info.get('title', page_id)}")
                else:
                    # 正常模式（断点续传）
                    if page_id not in generated_pages:
                        should_generate = True
                    else:
                        logger.info(f"使用已生成的页面内容: {page_info.get('title', page_id)}")
                
                if should_generate:
                    pages_to_generate.append(page_info)

            pages_to_generate_count = len(pages_to_generate)
            update_status_func(
                "processing",
                base_progress,
                "pages",
                0,
                f"开始生成Wiki页面，共{pages_to_generate_count}个页面",
                total_files=pages_to_generate_count,
                processed_files=0,
            )

            # 处理未生成的页面
            for i, page_info in enumerate(pages_to_generate):
                # 检查暂停
                if job_context and job_context.pause_requested:
                    if job_context.pause_event:
                        job_context.pause_event.wait()
                
                page_id = page_info.get("id")
                if not page_id:
                    continue
                
                # 在开始生成页面前写入一次状态，便于前端展示细粒度进度
                # 页面生成阶段占总进度的50%，当前页面在该阶段中的进度百分比
                stage_progress = int(i / max(1, len(pages_to_generate)) * 100)
                # 整体进度 = base_progress + (当前页面索引 / 总页面数) * 50%
                overall_progress = base_progress + int(i / max(1, len(pages_to_generate)) * 50)
                update_status_func(
                    "processing",
                    overall_progress,
                    "pages",
                    stage_progress,
                    f"准备生成页面: {page_info.get('title', page_id)}",
                    total_files=pages_to_generate_count,
                    processed_files=i,
                )
                
                # 检查暂停
                if job_context and job_context.pause_requested:
                    if job_context.pause_event:
                        job_context.pause_event.wait()
                
                # 使用现有的_generate_page_content方法生成页面内容
                page_content = await self._generate_page_content(
                    page_details=page_info,
                    repo_url=getattr(job_context, 'repo_url', ''),
                    language=language,
                    model_settings=model_settings,
                    model_client=model_client,
                    context="",  # 传递空上下文，让_generate_page_content自己检索
                    job_id=job_id,
                    job_context=job_context,
                    fast_mode=fast_mode,
                    repo_paths=repo_paths,
                    user_instructions=user_instructions,
                    langfuse_settings=langfuse_settings,
                )
                
                # 增量添加生成的页面内容
                generated_pages[page_id] = page_content
                
                # 更新进度
                processed_count = i + 1
                # 页面生成阶段占总进度的50%，已完成页面数/总页面数 * 50% 就是当前进度
                overall_progress = base_progress + int(processed_count / len(pages_to_generate) * 50)
                stage_progress = int(processed_count / len(pages_to_generate) * 100)
                update_status_func(
                    "processing",
                    overall_progress, 
                    "pages",
                    stage_progress,
                    f"已生成页面: {page_info.get('title', page_id)} ({processed_count}/{len(pages_to_generate)})",
                    total_files=pages_to_generate_count,
                    processed_files=processed_count,
                )

                # 每生成一个页面就保存一次
                with session_scope() as session:
                    # 先保存到wiki_job的result字段
                    current_result = {}
                    try:
                        job = get_job(session, job_id)
                        if job and job.result:
                            current_result = job.result
                    except Exception as e:
                        logger.warning(f"获取job的result失败: {e}")
                    
                    # 更新result中的wiki_data
                    if "wiki_data" not in current_result:
                        current_result["wiki_data"] = {}
                    if "generated_pages" not in current_result["wiki_data"]:
                        current_result["wiki_data"]["generated_pages"] = {}
                    
                    current_result["wiki_data"]["generated_pages"][page_id] = page_content
                    
                    # 保存到job的result字段
                    from api.service.wiki_service import update_job
                    update_job(session, job_id, result=current_result)
                    logger.debug(f"已保存页面 {page_id} 到job的result字段")
                    # 双写至 ai_dw_wiki_content（页面增量）
                    try:
                        self._upsert_ai_dw_content(session, wiki_id, pages_increment={page_id: page_content})
                    except Exception as e:
                        logger.warning(f"写入ai_dw_wiki_content失败(页面阶段)：{e}")
                    
                    # 暂时不更新wiki_info，等所有阶段完成后再更新
                    # existing_data["generated_pages"] = generated_pages
                    # update_wiki_info(session, wiki_id, wiki_data=existing_data)
            
            if pages_to_generate:
                update_status_func(
                    "processing",
                    base_progress + 50,  # 页面生成阶段完成，进度为base_progress + 50%
                    "pages",
                    100,
                    "Wiki页面生成完成",
                    total_files=pages_to_generate_count,
                    processed_files=pages_to_generate_count,
                )
            else:
                update_status_func(
                    "processing",
                    base_progress + 50,  # 页面生成阶段完成，进度为base_progress + 50%
                    "pages",
                    100,
                    "无需生成新的Wiki页面",
                    total_files=pages_to_generate_count,
                    processed_files=pages_to_generate_count,
                )

            if pages_stage_span:
                pages_stage_span.update(
                    output={
                        "generated_pages": len(generated_pages),
                        "regenerated_count": pages_to_generate_count,
                    }
                )
                if langfuse_enabled:
                    flush_langfuse()
            _close_pages_stage(None, None, None)
            return generated_pages
        except Exception as e:
            # 新增：在页面生成阶段出现异常时，立即更新job状态为失败并记录错误信息
            try:
                if update_status_func:
                    update_status_func("failed", 0, "pages", 0, f"生成Wiki页面失败: {e}")
            except Exception as _e:
                logger.warning(f"更新job状态为失败时出错: {_e}")
            if pages_stage_span:
                try:
                    pages_stage_span.update(level="ERROR", status_message=str(e))
                except Exception:
                    pass
                if langfuse_enabled:
                    flush_langfuse()
            _close_pages_stage(type(e), e, e.__traceback__)
            raise Exception(f"生成Wiki页面失败: {e}")

    def _get_local_repo_path(self, owner_name: str, repo_name: str, branch: str) -> str:
        """生成本地仓库路径"""
        # Root path for data storage，使用 /owner/repo-branch/repo-branch 格式，支持子仓库
        root_path = os.path.expanduser(os.path.join("~", ".adalflow"))
        # 使用 repo-branch 格式作为目录名，便于支持多分支
        branch_dir = f"{repo_name}-{branch}"
        return os.path.join(root_path, "repos", owner_name, branch_dir, branch_dir)
    
    def _get_local_sub_repo_path(self, owner_name: str, repo_name: str, branch: str, sub_repo: SubRepoInfo) -> str:
        """生成本地子仓库路径"""
        sub_repo_owner, sub_repo_name, host = git_utils.extract_repo_info(sub_repo.url)
        sub_repo_branch = sub_repo.branch
        # Root path for data storage，使用 /owner/repo-branch/subrepo 格式，支持子仓库
        root_path = os.path.expanduser(os.path.join("~", ".adalflow"))
        # 使用 repo-branch 格式作为父目录名，便于支持多分支
        father_branch_dir = f"{repo_name}-{branch}"
        # 子仓库文件路径拼接分支名
        sub_branch_dir = f"{sub_repo_name}-{sub_repo_branch}"
        return os.path.join(root_path, "repos", owner_name, father_branch_dir, sub_branch_dir)

    def _make_sure_directory_permissions(self, repo_path: str, container_project_workspace_path: str, wiki_id: str) -> bool:
        session = None
        try:
            # 创建wiki用户组
            from api.model.wiki_repository_relation import AiDwWikiRepositoryRelation
            from sqlmodel import select
            
            # 创建wiki用户组
            wiki_group_id = get_wiki_group_id()
            
            from api.sandbox.kubernetes_service import get_kubernetes_service
            group_name = get_kubernetes_service().generate_linux_group_name(id=wiki_group_id - 40000)
            linux_service = get_linux_user_group_service()
            result, group_name = linux_service.create_deepwiki_group(
                gid=wiki_group_id, 
                group_name=group_name
            )
            if not result:
                logger.error(f"创建wiki用户组失败: {group_name}")
                return False

            # 查询主仓库关系
            from api.service.git_repository_service import update_repository
            with session_scope() as session:
                wiki_repo_relation = session.exec(
                    select(AiDwWikiRepositoryRelation).where(
                        AiDwWikiRepositoryRelation.wiki_id == wiki_id,
                        AiDwWikiRepositoryRelation.is_main_repo == True  # noqa: E712
                    )
                ).first()
                
                if not wiki_repo_relation:
                    logger.error(f"未找到wiki_id={wiki_id}的主仓库关系")
                    return False
                
                repo_id = wiki_repo_relation.repository_id
            
            # 仓库目录
            # container_repo_path = f"{container_base_path.rstrip('/')}/repos/{owner_name}/{repo_name}-{branch_clean}/"
            result = linux_service.set_directory_permissions(
                code_path=repo_path,
                group_name=group_name
            )
            if not result:
                logger.error(f"设置仓库目录权限失败: {repo_path}")
            else:
                # 更新数据库：仓库目录权限已设置
                with session_scope() as session:
                    update_repository(
                        repo_id=repo_id,
                        linux_gid=wiki_group_id,
                        linux_group_name=group_name,
                        linux_code_file_perm=True,
                        session=session
                    )
                    session.commit()
                    logger.info(f"已设置仓库目录权限并更新数据库: {repo_path}, linux_code_file_perm=True")
            
            result = linux_service.change_directory_permissions(
                code_path=repo_path,
                mod="750"
            )
            if not result:
                logger.error(f"修改仓库目录权限失败: {repo_path}, mod=750")
            else:
                logger.info(f"已修改仓库目录权限: {repo_path}, mod=750")
            
            # 项目工作空间目录
            get_kubernetes_service().ensure_project_workspace(project_workspace_path=container_project_workspace_path)
            # result = linux_service.set_facl_permissions(code_path=container_project_workspace_path)
            # if not result:
            #     logger.error(f"设置项目工作空间目录acl权限失败: {container_project_workspace_path}")
            #     return False
            result = linux_service.set_directory_permissions(
                code_path=container_project_workspace_path,
                group_name=group_name
            )

            if not result:
                logger.error(f"设置项目工作空间目录权限失败: {container_project_workspace_path}")
            else:
                # 更新数据库：工作空间目录权限已设置
                with session_scope() as session:
                    update_repository(
                        repo_id=repo_id,
                        linux_gid=wiki_group_id,
                        linux_group_name=group_name,
                        linux_pw_file_perm=True,
                        session=session
                    )
                    session.commit()
                    logger.info(f"已设置工作空间目录权限并更新数据库: {container_project_workspace_path}, linux_pw_file_perm=True")

            result = linux_service.change_directory_permissions(
                code_path=container_project_workspace_path,
                mod="777",
                if_recursive=True
            )
            if not result:
                logger.error(f"修改项目工作空间目录权限失败: {container_project_workspace_path}, mod=770")
            else:
                logger.info(f"已修改项目工作空间目录权限: {container_project_workspace_path}, mod=770")
                result = linux_service.change_directory_permissions(
                code_path=container_project_workspace_path,
                mod="770",
                if_recursive=False
            )
            
            return True

        except Exception as e:
            logger.error(f"设置目录权限失败: {e}")
            if session:
                try:
                    session.rollback()
                except Exception as rollback_err:
                    logger.debug(f"会话回滚失败: {rollback_err}")
            raise e

    def _update_job_status(
        self,
        job_id: str,
        status: str,
        progress: int,
        stage: str,
        stage_progress: int,
        stage_message: str,
        processed_files: int = None,
        total_files: int = None,
        error_message: Optional[str] = None,
    ) -> None:
        """仅更新任务（wiki_job）状态。"""
        with session_scope() as session:
            try:
                clean_stage_message = _sanitize_message(stage_message)
                clean_error_message = _sanitize_message(error_message, 500) if error_message else None
                # 构建更新参数
                update_params = {
                    "status": status, 
                    "progress": progress, 
                    "stage": stage, 
                    "stage_progress": stage_progress, 
                    "stage_message": clean_stage_message
                }
                
                if status == self.STATUS_FAILED:
                    update_params['error_message'] = clean_error_message or clean_stage_message

                # 添加可选参数
                if processed_files is not None:
                    update_params["processed_files"] = processed_files
                if total_files is not None:
                    update_params["total_files"] = total_files
                if clean_error_message and status != self.STATUS_FAILED:
                    update_params["error_message"] = clean_error_message
                    
                # 更新任务状态
                update_job(session, job_id, **update_params)
            except Exception as e:
                logger.error(f"Error updating job status: {e}")

    def _update_wiki_info_status(
        self,
        wiki_id: Optional[str],
        status: str,
        stage_message: str,
    ) -> None:
        """更新 wiki_info 状态，仅用于生成任务。"""
        if not wiki_id:
            return

        try:
            with session_scope() as session:
                update_params = {"status": status}
                if status == self.STATUS_FAILED:
                    update_params["error_message"] = stage_message
                update_result = update_wiki_info(session, wiki_id, **update_params)
                if not update_result:
                    logger.warning(f"Failed to update wiki {wiki_id} status to {status}")
        except Exception as exc:
            logger.error(f"Error updating wiki_info status: {exc}")

    def _complete_task_successfully(
        self, job_id: str, wiki_id: Optional[str], 
        structure: Dict[str, Any], generated_pages: Dict[str, Any]
    ):
        """完成任务，标记为成功"""
        with session_scope() as session:
            try:
                # 更新任务状态为已完成
                update_job(session, job_id, status=self.STATUS_COMPLETED, progress=100, 
                          stage=self.STAGE_COMPLETED, stage_progress=100,
                          stage_message="Wiki生成已完成")
                
                # 更新WikiInfo状态和数据
                if wiki_id:
                    update_result = update_wiki_info(
                        session, 
                        wiki_id, 
                        status=self.STATUS_COMPLETED, 
                        wiki_data={
                            "wiki_structure": structure,
                            "generated_pages": generated_pages
                        }
                    )
                    if not update_result:
                        logger.warning(f"Failed to update wiki_id {wiki_id} with completion data")
                    else:
                        logger.info(f"已更新WikiInfo(ID:{wiki_id})状态为已完成")
            except Exception as e:
                logger.error(f"Error updating completion status: {e}")
                # 尽管状态更新失败，任务实际上已完成

    def _handle_task_failure(
        self, job_id: str, wiki_id: Optional[str], 
        error: Exception, local_repo_path: str
    ):
        """处理任务失败的情况"""
        logger.error(f"Error processing job {job_id}: {error}", exc_info=True)
        try:
            with session_scope() as session:
                # 更新任务状态为失败
                logger.info(f"更新任务 {job_id} 状态为失败")
                update_job(session, job_id, status=self.STATUS_FAILED, error_message=str(error))
                
                # 如果有wiki_id，更新其状态和错误信息
                if wiki_id:
                    logger.info(f"更新Wiki {wiki_id} 状态为失败")
                    update_result = update_wiki_info(
                        session,
                        wiki_id,
                        status=self.STATUS_FAILED,
                        error_message=str(error)
                    )
                    if not update_result:
                        logger.warning(f"Failed to update wiki_id {wiki_id} with error status")
        except Exception as inner_e:
            # 回滚过程中出错，记录详细信息
            logger.error(f"更新失败状态时出错: {inner_e}", exc_info=True)
        finally:
            # 这里不删除代码库，保留以便调试或手动恢复
            pass

    @retry(max_retries=3, delay=2, backoff=2, exceptions=(Exception,))
    def _create_model_client(self, model_settings: Dict[str, Any]):
        """根据配置创建模型客户端"""
        provider = model_settings.get("provider", "default_provider")
        model_name = model_settings.get("model", "default_model")
        api_key = model_settings.get("api_key")
        model_kwargs = model_settings.get("model_kwargs", {})
        
        if provider == "openai" or provider == "whalecloud":
            # 参考 websocket_wiki.py 中的用法
            # 过滤掉api_key中的●符号
            cleaned_api_key = api_key.replace('●', '') if api_key else ''
            client = OpenAIClient(
                api_key=cleaned_api_key,
                env_api_key_name="_UNUSED_ENV_VAR_NAME_"  # 使用一个不可能存在的环境变量名，确保使用传入的 api_key
            )
            # 不需要保存 model_name，因为会在 convert_inputs_to_api_kwargs 时通过 model_kwargs 传入
            return client
        elif provider == "gemini":
            # 假设GeminiClient的初始化方式
            return GeminiCliClient(model_name=model_name, **model_kwargs)
        elif provider == "ollama":
            # 假设OllamaClient的初始化方式
            # 注意：Ollama通常不需要API Key
            return OllamaClient(model=model_name, **model_kwargs)
        elif provider == "openrouter":
            # 假设OpenRouterClient的初始化方式
            return OpenRouterClient(api_key=api_key, model=model_name, **model_kwargs)
        # 添加其他提供商...
        else:
            # 默认或备用客户端
            # 这里可以根据您的配置选择一个默认的客户端
            logger.warning(f"未知的模型提供商: {provider}, 将使用默认客户端。")
            # 例如，使用OpenAI作为默认
            client = OpenAIClient(
                api_key=api_key,
                env_api_key_name="_UNUSED_ENV_VAR_NAME_"
            )
            return client

    async def _read_files_directly(self, file_paths: List[str], repo_paths: List[str]) -> str:
        """直接读取文件内容作为上下文"""
        if not file_paths or not repo_paths:
            return ""
        
        context_parts = []
        
        for file_path in file_paths:
            # 在所有仓库路径中查找文件
            file_found = False
            for repo_path in repo_paths:
                full_file_path = os.path.join(repo_path, file_path)
                if os.path.exists(full_file_path) and os.path.isfile(full_file_path):
                    try:
                        # 检查文件扩展名，跳过二进制文件
                        _, ext = os.path.splitext(full_file_path)
                        binary_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.pdf', '.zip', '.tar', '.gz', '.exe', '.dll', '.so', '.dylib'}
                        if ext.lower() in binary_extensions:
                            logger.info(f"跳过二进制文件: {full_file_path}")
                            context_parts.append(f"## 文件: {file_path}\n\n*二进制文件，已跳过*")
                            file_found = True
                            break
                        
                        with open(full_file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # 如果文件太大，只读取前40000个字符
                        if len(content) > 40000:
                            content = content[:40000] + "\n\n... (文件内容过长，已截断)"
                        
                        context_parts.append(f"## 文件: {file_path}\n\n```\n{content}\n```")
                        file_found = True
                        logger.debug(f"成功读取文件: {full_file_path}")
                        break  # 找到文件后跳出内层循环
                    except UnicodeDecodeError:
                        logger.info(f"跳过无法解码的文件: {full_file_path}")
                        context_parts.append(f"## 文件: {file_path}\n\n*文件编码无法解析，已跳过*")
                        file_found = True
                        break
                    except Exception as e:
                        logger.warning(f"读取文件 {full_file_path} 失败: {e}")
            
            if not file_found:
                logger.warning(f"未找到文件: {file_path}")
                context_parts.append(f"## 文件: {file_path}\n\n*文件未找到或无法读取*")
        
        return "\n\n".join(context_parts)

    @retry(max_retries=3, delay=2, backoff=2, exceptions=(Exception,))

    def _build_file_tree(self, local_repo_paths: List[str], excluded_dirs=None, excluded_files=None, included_dirs=None, included_files=None) -> str:
        """
        Build a file tree string with filtering options
        """
        included_paths = []
        # 父子仓库的路径遍历
        for local_repo_path in local_repo_paths:
            for path in Path(local_repo_path).rglob("*"):
                if not path.is_file():
                    continue
                    
                rel_path = str(path.relative_to(local_repo_path))
                
                # Skip hidden files and directories
                if any(part.startswith('.') for part in rel_path.split('/')):
                    continue
                    
                # Apply exclusion filters
                if excluded_dirs and any(excluded in rel_path for excluded in excluded_dirs):
                    continue
                    
                if excluded_files and any(rel_path.endswith(excluded) for excluded in excluded_files):
                    continue
                    
                # Apply inclusion filters if specified
                if included_dirs:
                    if not any(rel_path.startswith(included) for included in included_dirs):
                        continue
                        
                if included_files:
                    if not any(rel_path.endswith(included) for included in included_files):
                        continue
                        
                included_paths.append(rel_path)
            
        return "\n".join(sorted(included_paths))

    def _save_complete_wiki_to_file(self, structure: Dict[str, Any], generated_pages: Dict[str, Any], owner: str, repo: str, repo_type: str, language: str):
        """将完整的Wiki保存到文件中"""
        
        # 获取base目录路径
        base_dir = Path(__file__).parent.parent.parent  # /api/
        wikis_dir = base_dir / "output" / "wikis" / f"{owner}_{repo}_{language}" 
        
        # 确保目录存在
        wikis_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存结构（简化版）
        structure_file = wikis_dir / f"wiki_structure_{owner}_{repo}.json"
        simplified_structure = {
            "pages": structure.get("pages", []),
            "summary": f"Generated {len(structure.get('pages', []))} pages for {owner}/{repo}",
            "total_pages": len(structure.get("pages", [])),
            "repo_info": {
                "owner": owner,
                "repo": repo,
                "repo_type": repo_type,
                "language": language
            }
        }
        
        with open(structure_file, 'w', encoding='utf-8') as f:
            json.dump(simplified_structure, f, ensure_ascii=False, indent=2)
        logger.info(f"Wiki结构已保存到 {structure_file}")
        
        # 保存所有页面到单个文件
        all_pages_file = wikis_dir / f"wiki_pages_{owner}_{repo}.json"
        with open(all_pages_file, 'w', encoding='utf-8') as f:
            json.dump(generated_pages, f, ensure_ascii=False, indent=2)
        logger.info(f"所有Wiki页面已保存到 {all_pages_file}")
        
        # 生成Markdown格式的完整文档
        full_wiki_file = wikis_dir / f"wiki_full_{owner}_{repo}.md"
        with open(full_wiki_file, 'w', encoding='utf-8') as f:
            # 写入标题
            f.write(f"# {owner}/{repo} - 完整Wiki文档\n\n")
            
            # 写入目录
            f.write("## 目录\n\n")
            for page in structure.get("pages", []):
                page_id = page.get("id")
                page_title = page.get("title", "未命名页面")
                f.write(f"- [{page_title}](#{page_id})\n")
            f.write("\n---\n\n")
            
            # 写入页面内容
            for page in structure.get("pages", []):
                page_id = page.get("id")
                page_title = page.get("title", "未命名页面")
                page_content = generated_pages.get(page_id, "暂无内容")
                
                f.write(f"## {page_title} {{#{page_id}}}\n\n")
                f.write(f"{page_content}\n\n")
                f.write("---\n\n")
        
        logger.info(f"完整Wiki文档已保存到 {full_wiki_file}")


    @retry(max_retries=3, delay=3, backoff=2, exceptions=(ConnectionError, TimeoutError, ValueError))
    async def _generate_wiki_structure(
        self, 
        file_tree: str, 
        readme: str, 
        repo_url: str, 
        language: str, 
        model_settings: Dict[str, Any], 
        model_client,
        comprehensive: bool = True,
        job_id: Optional[str] = None,
        job_context=None,
        user_instructions: Optional[str] = None,
        langfuse_settings: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """使用LLM生成Wiki结构"""
        langfuse_cfg = langfuse_settings or {}
        langfuse_enabled = bool(langfuse_cfg.get("enabled"))
        base_metadata = langfuse_cfg.get("base_metadata", {})
        conversation_type = langfuse_cfg.get("conversation_type", "wiki_generation")

        generation_metadata = build_langfuse_metadata(
            base_metadata,
            conversation_type,
            stage="structure_llm",
        )
        trace_ctx = langfuse_cfg.get("trace_context")
        # 绑定结构LLM生成跨度，保证上下文一致
        generation_span_handle = bind_langfuse_span(
            create_langfuse_span(
                langfuse_enabled,
                f"{conversation_type}.structure_llm",
                generation_metadata,
                as_generation=True,
                trace_context=trace_ctx,
            )
        )
        generation_span = generation_span_handle.enter()
        generation_span_closed = not bool(generation_span)

        def _close_generation_span(exc_type=None, exc_val=None, exc_tb=None):
            nonlocal generation_span_closed
            if not generation_span_closed:
                generation_span_handle.exit(exc_type, exc_val, exc_tb)
                generation_span_closed = True

        # 检查暂停
        if job_context and job_context.pause_requested:
            if job_context.pause_event:
                job_context.pause_event.wait()
        
        # 读取Prompt模板
        with open("api/prompts/wiki_structure.md", "r", encoding="utf-8") as f:
            prompt_template = f.read()

        # 处理comprehensive条件部分
        if comprehensive:
            prompt_template = prompt_template.replace("{%if comprehensive%}", "").replace("{%else%}", "<!-- ELSE SECTION REMOVED -->")
            parts = prompt_template.split("<!-- ELSE SECTION REMOVED -->")
            if len(parts) > 1:
                endif_pos = parts[1].find("{%endif%}")
                if endif_pos != -1:
                    prompt_template = parts[0] + parts[1][endif_pos + 9:]
                else:
                    prompt_template = parts[0]
        else:
            prompt_template = prompt_template.replace("{%if comprehensive%}", "<!-- IF SECTION REMOVED -->").replace("{%endif%}", "")
            parts = prompt_template.split("<!-- IF SECTION REMOVED -->")
            if len(parts) > 1:
                else_pos = parts[1].find("{%else%}")
                if else_pos != -1:
                    prompt_template = parts[0] + parts[1][else_pos + 8:]
                else:
                    prompt_template = parts[0]

        # 从repo_url提取owner和repo
        owner, repo, host = git_utils.extract_repo_info(repo_url)
        
        # 添加默认的页数和视图类型，避免可能的模板变量缺失
        page_count = "15-20" if comprehensive else "5-10"
        view_type = "comprehensive" if comprehensive else "focused"
        
        # 格式化提示词
        from string import Template
        template = Template(prompt_template)
        formatted_prompt = template.safe_substitute(
            repo_url=repo_url,
            owner=owner,
            repo=repo,
            fileTree=file_tree,
            readme=readme,
            language=language,
            pageCount=page_count,
            view_type=view_type
        )

        if user_instructions:
            formatted_prompt += (
                "\n\nADDITIONAL USER REQUIREMENTS:\n" + user_instructions.strip()
            )

        # 根据不同提供商调用模型API
        provider = model_settings.get("provider", "openai")
        model_name = model_settings.get("model", "gemini-2.5-flash")
        api_key = model_settings.get("api_key")
        
        logger.info(f"开始生成Wiki结构，使用提供商: {provider}，模型: {model_name}，复杂度: {'详细' if comprehensive else '简洁'}")

        if generation_span:
            generation_span.update(
                input={
                    "prompt": _truncate_for_langfuse(formatted_prompt, 4000),
                    "repo_url": repo_url,
                    "comprehensive": comprehensive,
                },
                metadata={"provider": provider, "model": model_name},
                model=model_name,
            )
            if langfuse_enabled:
                flush_langfuse()

        try:
            xml_response = ""
            if provider == "openai" or provider == "whalecloud":
                # 检查暂停
                if job_context and job_context.pause_requested:
                    if job_context.pause_event:
                        job_context.pause_event.wait()
                
                # 构建模型参数，参考 websocket_wiki.py 中的用法
                model_kwargs = {
                    "model": model_name,
                    "stream": False,  # 非流式，等待完整结果
                    "temperature": 0.7,
                    "top_p": 0.95
                }
                
                # 转换为API参数
                api_kwargs = model_client.convert_inputs_to_api_kwargs(
                    input=formatted_prompt,
                    model_kwargs=model_kwargs,
                    model_type=ModelType.LLM
                )
                
                # 确保API密钥被正确传递
                # 过滤掉api_key中的●符号
                cleaned_api_key = api_key.replace('●', '') if api_key else ''
                api_kwargs["api_key"] = cleaned_api_key
                
                # 调用API
                response = await model_client.acall(api_kwargs=api_kwargs, model_type=ModelType.LLM)
                # 检查响应类型并相应处理
                if hasattr(response, "__aiter__"):
                    # 如果是异步迭代器，使用async for
                    async for chunk in response:
                        # 检查暂停
                        if job_context and job_context.pause_requested:
                            if job_context.pause_event:
                                job_context.pause_event.wait()
                        
                        choices = getattr(chunk, "choices", [])
                        if choices and len(choices) > 0:
                            delta = getattr(choices[0], "delta", None)
                            if delta is not None:
                                text = getattr(delta, "content", None)
                                if text is not None:
                                    xml_response += text
                else:
                    # 如果是直接返回的对象（如ChatCompletion），直接提取内容
                    content = ""
                    if hasattr(response, "choices") and response.choices:
                        # 标准OpenAI响应格式
                        if hasattr(response.choices[0], "message"):
                            content = response.choices[0].message.content
                        elif hasattr(response.choices[0], "text"):
                            content = response.choices[0].text
                    xml_response = content or str(response)
                
            elif provider == "openrouter":
                # 类似的处理逻辑，添加暂停检查
                if job_context and job_context.pause_requested:
                    if job_context.pause_event:
                        job_context.pause_event.wait()
                
                model_kwargs = {
                    "model": model_name,
                    "stream": False,
                    "temperature": 0.5,
                    "top_p": 0.95
                }
                
                api_kwargs = model_client.convert_inputs_to_api_kwargs(
                    input=formatted_prompt,
                    model_kwargs=model_kwargs,
                    model_type=ModelType.LLM
                )
                
                response = await model_client.acall(api_kwargs=api_kwargs, model_type=ModelType.LLM)
                async for chunk in response:
                    # 检查暂停
                    if job_context and job_context.pause_requested:
                        if job_context.pause_event:
                            job_context.pause_event.wait()
                    xml_response += chunk
                    
            elif provider == "ollama":
                # 类似的处理逻辑，添加暂停检查
                if job_context and job_context.pause_requested:
                    if job_context.pause_event:
                        job_context.pause_event.wait()
                
                model_kwargs = {
                    "model": model_name,
                    "stream": False,
                    "options": {
                        "temperature": 0.5,
                        "top_p": 0.95,
                        "num_ctx": 4096
                    }
                }
                
                api_kwargs = model_client.convert_inputs_to_api_kwargs(
                    input=formatted_prompt,
                    model_kwargs=model_kwargs,
                    model_type=ModelType.LLM
                )
                
                response = await model_client.acall(api_kwargs=api_kwargs, model_type=ModelType.LLM)
                async for chunk in response:
                    # 检查暂停
                    if job_context and job_context.pause_requested:
                        if job_context.pause_event:
                            job_context.pause_event.wait()
                    text = getattr(chunk, 'response', None) or getattr(chunk, 'text', None) or str(chunk)
                    xml_response += text
            else:
                # Google Generative AI
                if job_context and job_context.pause_requested:
                    if job_context.pause_event:
                        job_context.pause_event.wait()
                response = model_client.generate_content(formatted_prompt)
                xml_response = response.text
            
            # 解析XML结构
            wiki_structure = self._parse_xml_structure(xml_response)
            if generation_span:
                generation_span.update(
                    output={
                        "response_preview": _truncate_for_langfuse(xml_response, 4000),
                        "response_length": len(xml_response),
                        "page_count": len(wiki_structure.get("pages", [])),
                    }
                )
                if langfuse_enabled:
                    flush_langfuse()
            _close_generation_span(None, None, None)
            return wiki_structure

        except Exception as e:
            logger.error(f"生成Wiki结构时出错: {e}", exc_info=True)
            if generation_span:
                generation_span.update(level="ERROR", status_message=str(e))
                if langfuse_enabled:
                    flush_langfuse()
            _close_generation_span(type(e), e, e.__traceback__)
            raise

    def _parse_xml_structure(self, xml_string: str) -> Dict[str, Any]:
        try:
            xml_string = xml_string.strip().removeprefix("```xml").removesuffix("```").strip()
            root = ET.fromstring(xml_string)
            
            wiki_structure = {
                "title": root.findtext("title", "Wiki"),
                "description": root.findtext("description", ""),
                "sections": [],
                "pages": [],
                "rootSections": []
            }

            pages_map = {}
            for page_el in root.findall(".//page"):
                # 获取并过滤文件路径，确保非空且有效
                raw_file_paths = [fp.text for fp in page_el.findall(".//file_path") if fp.text and fp.text.strip()]
                filtered_file_paths = [fp.strip() for fp in raw_file_paths if fp.strip()]
                
                page_data = {
                    "id": page_el.get("id"),
                    "title": page_el.findtext("title"),
                    "description": page_el.findtext("description"),
                    "importance": page_el.findtext("importance"),
                    "filePaths": filtered_file_paths,
                    "relatedPages": [rp.text for rp in page_el.findall(".//related")],
                    "parentId": page_el.findtext("parent_section")
                }
                wiki_structure["pages"].append(page_data)
                pages_map[page_data["id"]] = page_data
            
            sections_map = {}
            for section_el in root.findall(".//section"):
                section_data = {
                    "id": section_el.get("id"),
                    "title": section_el.findtext("title"),
                    "pages": [p.text for p in section_el.findall(".//page_ref")],
                    "subsections": [s.text for s in section_el.findall(".//section_ref")]
                }
                wiki_structure["sections"].append(section_data)
                sections_map[section_data["id"]] = section_data
            
            all_subsection_ids = {s_id for sec in sections_map.values() for s_id in sec.get("subsections", [])}
            for sec_id in sections_map:
                if sec_id not in all_subsection_ids:
                    wiki_structure["rootSections"].append(sec_id)
                    
            return wiki_structure
        except ET.ParseError as e:
            logger.error(f"Failed to parse XML structure: {e}\nRaw response:\n{xml_string}")
            raise ValueError("Failed to parse valid XML from LLM response for wiki structure.")

    @retry(max_retries=3, delay=3, backoff=2, exceptions=(ConnectionError, TimeoutError, ValueError))
    async def _generate_page_content(
        self, 
        page_details: Dict[str, Any], 
        repo_url: str, 
        language: str, 
        model_settings: Dict[str, Any], 
        model_client,
        context: str = "",
        job_id: Optional[str] = None,
        job_context=None,
        fast_mode: bool = None,
        repo_paths: List[str] = None,
        user_instructions: Optional[str] = None,
        langfuse_settings: Optional[Dict[str, Any]] = None,
    ) -> str:
        """使用LLM和上下文生成页面内容"""
        page_id = page_details.get("id", "unknown")
        page_title = page_details.get("title", page_id)
        langfuse_cfg = langfuse_settings or {}
        langfuse_enabled = bool(langfuse_cfg.get("enabled"))
        base_metadata = langfuse_cfg.get("base_metadata", {})
        conversation_type = langfuse_cfg.get("conversation_type", "wiki_generation")

        generation_metadata = build_langfuse_metadata(
            base_metadata,
            conversation_type,
            stage="page_llm",
            extra={
                "page_id": page_id,
                "page_title": page_title,
            },
        )
        trace_ctx = langfuse_cfg.get("trace_context")
        # 绑定页面LLM生成跨度，保证上下文一致
        generation_span_handle = bind_langfuse_span(
            create_langfuse_span(
                langfuse_enabled,
                f"{conversation_type}.page_llm",
                generation_metadata,
                as_generation=True,
                trace_context=trace_ctx,
            )
        )
        generation_span = generation_span_handle.enter()
        generation_span_closed = not bool(generation_span)

        def _close_generation_span(exc_type=None, exc_val=None, exc_tb=None):
            nonlocal generation_span_closed
            if not generation_span_closed:
                generation_span_handle.exit(exc_type, exc_val, exc_tb)
                generation_span_closed = True

        try:
            # 检查暂停
            if job_context and job_context.pause_requested:
                if job_context.pause_event:
                    job_context.pause_event.wait()
            
            # 读取Prompt模板
            with open("api/prompts/wiki_page.md", "r", encoding="utf-8") as f:
                prompt_template = f.read()
            
            # 获取文件路径列表
            file_paths = page_details.get("filePaths", [])
            if not file_paths and "relevant_files" in page_details:
                file_paths = page_details["relevant_files"]
            
            # 过滤和清理文件路径
            if file_paths:
                file_paths = [path.strip() for path in file_paths if path and path.strip()]
                logger.info(f"页面 '{page_title}' 关联的文件路径: {file_paths}")
            else:
                logger.warning(f"页面 '{page_title}' 没有关联的文件路径")
                
            file_paths_md = "\n".join([f"- `{path}`" for path in file_paths])

            # 从repo_url提取owner和repo
            owner, repo, host = git_utils.extract_repo_info(repo_url)

            # 直接读取关联文件内容作为上下文
            if repo_paths and file_paths:
                try:
                    logger.info(f"为页面 '{page_title}' 读取关联文件内容")
                    direct_context = await self._read_files_directly(file_paths, repo_paths)
                    if direct_context:
                        context = f"{context}\n\n{direct_context}" if context else direct_context
                        logger.info(f"成功读取到页面 '{page_title}' 的参考内容")
                    else:
                        logger.warning(f"未能为页面 '{page_title}' 读取到文件内容")
                except Exception as e:
                    logger.warning(f"读取文件内容时出错: {e}")

            from string import Template
            # 格式化提示词
            try:
                template = Template(prompt_template)
                formatted_prompt = template.safe_substitute(
                    WIKI_PAGE_TOPIC=page_title,
                    RELEVANT_SOURCE_FILES=file_paths_md,
                    page_title=page_title,
                    file_paths=file_paths_md,
                    repo_url=repo_url,
                    language=language,
                    owner=owner,
                    repo=repo
                )
            except Exception as e:
                # 捕获模板替换错误并提供更有用的错误信息
                logger.error(f"模板格式化错误: {e}，页面标题: {page_title}")
                logger.error(f"可用的格式化键: WIKI_PAGE_TOPIC, RELEVANT_SOURCE_FILES, page_title, file_paths, repo_url, language, owner, repo")
                # 尝试使用一个简化的模板作为备选
                formatted_prompt = f"""
                You are an expert technical writer and software architect.
                Your task is to generate a comprehensive and accurate technical wiki page in Markdown format about "{page_title}" for the repository {owner}/{repo} (URL: {repo_url}).
                
                CRITICAL REQUIREMENTS:
                - Start directly with the main heading (# Title). Do not add any preface.
                - The documentation MUST be written in {language} language.
                - You MUST include precise code references using line numbers when discussing specific implementations, using this exact block format:
                  ```file:line-range
                  [actual code snippet]
                  ```
                - You MUST generate multiple Mermaid diagrams (at least 2-3) to visualize complex logic, architecture, or flows. These are mandatory.
                  Use only: graph TD, sequenceDiagram, classDiagram.
                  Golden Rule: ALL node labels must be wrapped in double quotes.
                
                Relevant source files for this page (use them as the ONLY basis of facts; cite them frequently):
                {file_paths_md}
                
                Structure your output as follows:
                1) <details> block listing ALL relevant source files used (as links), followed immediately by H1 title "# {page_title}".
                2) Introduction: 1-2 paragraphs stating the purpose, scope, and position of "{page_title}" within the project.
                3) Detailed Sections with H2/H3 headings covering:
                   - Architecture and key components
                   - Data flow, state, or control logic
                   - API endpoints or public interfaces (if applicable)
                   - Configuration and environment aspects (if applicable)
                4) Diagrams (MANDATORY): Add at least 2-3 Mermaid diagrams (flowchart, sequence, or class diagrams) derived from the source files. Ensure syntax correctness and quote all node labels.
                5) Tables: Summaries for features/APIs/configs if appropriate.
                6) Code Snippets: Short, relevant snippets from the provided files to illustrate key points.
                7) Source Citations: After each significant claim/diagram/table/snippet, add a citation using the exact formats below:
                   - `Sources: [filename.ext:start-end](/api/file-action?filePath=filename.ext&lines=start-end&owner={owner}&repo={repo}&action=view)`
                   - `Sources: [filename.ext:line](/api/file-action?filePath=filename.ext&lines=line&owner={owner}&repo={repo}&action=view)`
                   - Multiple files separated by a comma.
                8) Conclusion: Briefly recap the key aspects of "{page_title}".
                
                IMPORTANT:
                - Stay strictly grounded in the provided files.
                - If a detail is not present in the files, do not invent it.
                - Ensure clarity, correctness, and developer-oriented language.
                """
                logger.info("已切换到增强的备用格式模板，包含Mermaid与引用要求")

            # 如果有检索到的上下文，添加到提示中
            if context:
                formatted_prompt += f"\n\n## Retrieved Context\n\n{context}"

            if user_instructions:
                formatted_prompt += (
                    "\n\n## Additional User Instructions\n\n" + user_instructions.strip()
                )

            # 根据不同提供商调用模型API
            provider = model_settings.get("provider", "openai")
            model_name = model_settings.get("model", "gpt-3.5-turbo")
            api_key = model_settings.get("api_key")
            
            logger.info(f"开始生成页面 '{page_title}' 内容，使用提供商: {provider}，模型: {model_name}")

            if generation_span:
                generation_span.update(
                    input={
                        "prompt": _truncate_for_langfuse(formatted_prompt, 4000),
                        "repo_url": repo_url,
                        "page_id": page_id,
                    },
                    metadata={"provider": provider, "model": model_name},
                    model=model_name,
                )
                if langfuse_enabled:
                    flush_langfuse()
            
            # 模型调用，捕获特定异常以便重试
            try:
                content = ""
                if provider == "openai" or provider == "whalecloud":
                    # 检查暂停
                    if job_context and job_context.pause_requested:
                        if job_context.pause_event:
                            job_context.pause_event.wait()
                    
                    # 构建模型参数，参考 websocket_wiki.py 中的用法
                    model_kwargs = {
                        "model": model_name,
                        "stream": False,  # 非流式，等待完整结果
                        "temperature": 0.5,
                        "top_p": 0.95
                    }
                    
                    # 转换为API参数
                    api_kwargs = model_client.convert_inputs_to_api_kwargs(
                        input=formatted_prompt,
                        model_kwargs=model_kwargs,
                        model_type=ModelType.LLM
                    )
                    
                    # 确保API密钥被正确传递
                    # 过滤掉api_key中的●符号
                    cleaned_api_key = api_key.replace('●', '') if api_key else ''
                    api_kwargs["api_key"] = cleaned_api_key
                    
                    # 调用API
                    response = await model_client.acall(api_kwargs=api_kwargs, model_type=ModelType.LLM)
                    
                    # 检查返回值是否支持异步迭代
                    if hasattr(response, "__aiter__"):
                        # 是异步迭代器，使用async for
                        async for chunk in response:
                            # 检查暂停
                            if job_context and job_context.pause_requested:
                                if job_context.pause_event:
                                    job_context.pause_event.wait()
                            
                            choices = getattr(chunk, "choices", [])
                            if choices and len(choices) > 0:
                                delta = getattr(choices[0], "delta", None)
                                if delta is not None:
                                    text = getattr(delta, "content", None)
                                    if text is not None:
                                        content += text
                    else:
                        # 非异步迭代器，直接从response获取内容
                        logger.info(f"获取到非异步迭代器响应类型: {type(response)}")
                        if hasattr(response, "choices") and response.choices:
                            # 标准OpenAI响应格式
                            if hasattr(response.choices[0], "message") and hasattr(response.choices[0].message, "content"):
                                content = response.choices[0].message.content
                            elif hasattr(response.choices[0], "text"):
                                content = response.choices[0].text
                        elif hasattr(response, "content"):
                            # 某些API可能直接在响应对象上提供content
                            content = response.content
                        else:
                            # 尝试将整个响应转换为字符串
                            logger.warning(f"未找到标准内容字段，尝试将整个响应转为字符串")
                            content = str(response)
                    
                elif provider == "openrouter":
                    # 类似处理，添加暂停检查
                    if job_context and job_context.pause_requested:
                        if job_context.pause_event:
                            job_context.pause_event.wait()
                    
                    model_kwargs = {
                        "model": model_name,
                        "stream": False,
                        "temperature": 0.7,
                        "top_p": 0.95
                    }
                    
                    api_kwargs = model_client.convert_inputs_to_api_kwargs(
                        input=formatted_prompt,
                        model_kwargs=model_kwargs,
                        model_type=ModelType.LLM
                    )
                    
                    response = await model_client.acall(api_kwargs=api_kwargs, model_type=ModelType.LLM)
                    
                    # 检查返回值是否支持异步迭代
                    if hasattr(response, "__aiter__"):
                        # 是异步迭代器，使用async for
                        async for chunk in response:
                            # 检查暂停
                            if job_context and job_context.pause_requested:
                                if job_context.pause_event:
                                    job_context.pause_event.wait()
                            content += chunk
                    else:
                        # 非异步迭代器，直接获取内容
                        logger.info(f"获取到非异步迭代器响应类型: {type(response)}")
                        # 尝试直接获取文本内容
                        if hasattr(response, "text"):
                            content = response.text
                        else:
                            content = str(response)
                        
                elif provider == "ollama":
                    # 类似处理，添加暂停检查
                    if job_context and job_context.pause_requested:
                        if job_context.pause_event:
                            job_context.pause_event.wait()
                    
                    model_kwargs = {
                        "model": model_name,
                        "stream": False,
                        "options": {
                            "temperature": 0.7,
                            "top_p": 0.95,
                            "num_ctx": 4096
                        }
                    }
                    
                    api_kwargs = model_client.convert_inputs_to_api_kwargs(
                        input=formatted_prompt,
                        model_kwargs=model_kwargs,
                        model_type=ModelType.LLM
                    )
                    
                    response = await model_client.acall(api_kwargs=api_kwargs, model_type=ModelType.LLM)
                    
                    # 检查返回值是否支持异步迭代
                    if hasattr(response, "__aiter__"):
                        # 是异步迭代器，使用async for
                        async for chunk in response:
                            # 检查暂停
                            if job_context and job_context.pause_requested:
                                if job_context.pause_event:
                                    job_context.pause_event.wait()
                            text = getattr(chunk, 'response', None) or getattr(chunk, 'text', None) or str(chunk)
                            content += text
                    else:
                        # 非异步迭代器，直接获取内容
                        logger.info(f"获取到非异步迭代器响应类型: {type(response)}")
                        # 尝试从不同字段获取文本内容
                        if hasattr(response, 'response'):
                            content = response.response
                        elif hasattr(response, 'text'):
                            content = response.text
                        else:
                            content = str(response)
                else:
                    # Google Generative AI
                    if job_context and job_context.pause_requested:
                        if job_context.pause_event:
                            job_context.pause_event.wait()
                    response = model_client.generate_content(formatted_prompt)
                    content = response.text
                
                # 验证生成的内容不为空
                if not content.strip():
                    raise ValueError(f"模型返回了空内容，页面: {page_title}")
                    
            except (ConnectionError, TimeoutError) as e:
                logger.error(f"模型调用网络错误: {str(e)}")
                raise  # 重新抛出以便重试
            except ValueError as e:
                logger.error(f"模型调用参数错误: {str(e)}")
                raise  # 重新抛出以便重试
            except Exception as e:
                logger.error(f"模型调用未预期错误: {str(e)}", exc_info=True)
                # 降级为简单内容
                content = f"""
                # {page_title}
                
                *由于模型调用错误，生成了此降级内容。*
                
                此页面应该包含关于以下文件的信息:
                
                {file_paths_md}
                
                请稍后重新生成此内容。
                """
                    
            # 清理响应中可能的markdown标记和前置文本
            content = content.strip().removeprefix("```markdown").removesuffix("```").strip()
            
            # 移除模型可能添加的前置说明文本（如"好的，我将根据您提供的..."）
            # 查找第一个标题标记"#"的位置
            first_heading_pos = content.find('\n#')
            if first_heading_pos > 0:
                # 如果第一个标题前有内容，则移除这些内容
                content = content[first_heading_pos+1:]
            elif content.startswith('#'):
                # 保留以#开头的内容
                pass
            else:
                # 如果没有找到标题标记但有前导文本，尝试查找段落分隔符
                paragraphs = content.split('\n\n')
                if len(paragraphs) > 1 and not paragraphs[0].startswith('#'):
                    content = '\n\n'.join(paragraphs[1:])
            
            clean_content = content.strip()

        except Exception as e:
            logger.error(f"生成页面 '{page_title}' 内容时出错: {e}", exc_info=True)
            if generation_span:
                generation_span.update(level="ERROR", status_message=str(e))
                if langfuse_enabled:
                    flush_langfuse()
            _close_generation_span(type(e), e, e.__traceback__)
            raise
        else:
            if generation_span:
                generation_span.update(
                    output={
                        "response_preview": _truncate_for_langfuse(clean_content, 4000),
                        "response_length": len(clean_content),
                    }
                )
                if langfuse_enabled:
                    flush_langfuse()
            _close_generation_span(None, None, None)
            return clean_content

    async def _execute_refresh_stage(
        self,
        job_id: str,
        wiki_id: str,
        repo_url: str,
        repo_type: str,
        branch: str,
        token: Optional[str],
        update_status_func,
        job_context=None,
        sub_repos: Optional[List[SubRepoInfo]] = None
    ) -> Dict:
        """执行refresh阶段 - git pull更新代码并检测变更（仅在本地仓库存在时调用）"""
        from api.data_pipeline import git_pull_with_diff, get_git_file_changes
        import subprocess
        
        # 从repo_url提取owner和repo
        owner_name, repo_name, host = git_utils.extract_repo_info(repo_url)
        
        # 生成本地仓库路径
        local_repo_path = self._get_local_repo_path(owner_name, repo_name, branch)
        
        logger.info(f"执行refresh阶段: {repo_url} -> {local_repo_path}")
        
        update_status_func(self.STATUS_PROCESSING, 10, self.STAGE_REFRESH, 0, "准备刷新代码仓库")
        
        try:
            # 检查暂停
            if job_context and job_context.pause_requested:
                if job_context.pause_event:
                    job_context.pause_event.wait()
            
            # 在 pull 前清理本地未提交更改，避免合并失败
            try:
                if os.path.exists(local_repo_path) and os.path.exists(os.path.join(local_repo_path, ".git")):
                    logger.info(f"刷新前清理本地未提交更改: {local_repo_path}")
                    subprocess.run(["git", "reset", "--hard"], cwd=local_repo_path, check=True)
                    subprocess.run(["git", "clean", "-fd"], cwd=local_repo_path, check=True)
            except Exception as e:
                logger.warning(f"清理本地未提交更改失败(主仓库)，继续尝试pull: {e}")

            # 执行git pull（此时假设本地仓库已存在，因为由上层检查过）
            update_status_func(self.STATUS_PROCESSING, 10, self.STAGE_REFRESH, 25, "正在拉取最新代码...")
            refresh_result = git_pull_with_diff(repo_url, local_repo_path, repo_type, token, branch)
            
            logger.info(f"主仓库刷新结果: updated={refresh_result['updated']}, changes={len(refresh_result['changes'])}")
            
            # 处理子仓库
            all_changes = refresh_result["changes"]
            all_additions = refresh_result["additions"]
            all_modifications = refresh_result["modifications"]
            all_deletions = refresh_result["deletions"]
            
            if sub_repos:
                update_status_func(self.STATUS_PROCESSING, 10, self.STAGE_REFRESH, 50, "正在刷新子仓库...")
                for i, sub_repo in enumerate(sub_repos):
                    local_sub_repo_path = self._get_local_sub_repo_path(owner_name, repo_name, branch, sub_repo)
                    
                    if os.path.exists(local_sub_repo_path) and os.path.exists(os.path.join(local_sub_repo_path, ".git")):
                        try:
                            # 子仓库 pull 前同样进行强制清理
                            try:
                                logger.info(f"刷新前清理本地未提交更改(子仓库): {local_sub_repo_path}")
                                subprocess.run(["git", "reset", "--hard"], cwd=local_sub_repo_path, check=True)
                                subprocess.run(["git", "clean", "-fd"], cwd=local_sub_repo_path, check=True)
                            except Exception as e:
                                logger.warning(f"清理本地未提交更改失败(子仓库)，继续尝试pull: {e}")

                            sub_refresh_result = git_pull_with_diff(sub_repo.url, local_sub_repo_path, repo_type, token, sub_repo.branch)
                            
                            if sub_refresh_result["updated"]:
                                # 为子仓库文件路径添加前缀
                                sub_prefix = local_sub_repo_path
                                for change in sub_refresh_result["changes"]:
                                    change["file"] = f"{sub_prefix}/{change['file']}"
                                
                                all_changes.extend(sub_refresh_result["changes"])
                                all_additions.extend([f"{sub_prefix}/{f}" for f in sub_refresh_result["additions"]])
                                all_modifications.extend([f"{sub_prefix}/{f}" for f in sub_refresh_result["modifications"]])
                                all_deletions.extend([f"{sub_prefix}/{f}" for f in sub_refresh_result["deletions"]])
                                
                                refresh_result["updated"] = True
                                
                            logger.info(f"子仓库 {sub_repo.url} 刷新结果: updated={sub_refresh_result['updated']}")
                            
                        except Exception as e:
                            logger.warning(f"子仓库 {sub_repo.url} 刷新失败: {str(e)}")
                    else:
                        logger.warning(f"子仓库路径不存在: {local_sub_repo_path}")
            
            # 更新合并后的结果
            refresh_result["changes"] = all_changes
            refresh_result["additions"] = all_additions
            refresh_result["modifications"] = all_modifications
            refresh_result["deletions"] = all_deletions
            
            if refresh_result["updated"]:
                update_status_func(self.STATUS_PROCESSING, 15, self.STAGE_REFRESH, 100, 
                                 f"代码更新完成，共{len(all_changes)}个文件变更")
            else:
                update_status_func(self.STATUS_PROCESSING, 15, self.STAGE_REFRESH, 100, "代码无更新")
            
            return refresh_result
            
        except Exception as e:
            logger.error(f"refresh阶段失败: {str(e)}", exc_info=True)
            update_status_func(self.STATUS_FAILED, 0, self.STAGE_REFRESH, 0, f"代码刷新失败: {str(e)}")
            raise

    def _upsert_ai_dw_content(self, session, wiki_id: str, structure: Dict[str, Any] = None, pages: Dict[str, Any] = None, pages_increment: Dict[str, Any] = None):
        """
        将内容写入 ai_dw_wiki_content：若存在则更新，不存在则插入。
        structure/pages 为全量覆盖字段；pages_increment 用于增量合并生成的页面。
        """
        try:
            # 优先使用 SQLModel 的 select 接口，避免不同ORM风格的差异
            from sqlmodel import select
            content = session.exec(select(AiDwWikiContent).where(AiDwWikiContent.wiki_id == wiki_id)).first()
        except Exception:
            content = None

        if content is None:
            content = AiDwWikiContent(
                wiki_id=wiki_id,
                wiki_structure=structure if structure is not None else None,
                wiki_pages=pages if pages is not None else (pages_increment or {}),
                total_pages=(len(pages) if pages else (len(pages_increment) if pages_increment else 0)),
                created_time=datetime.utcnow(),
                updated_time=datetime.utcnow(),
            )
            session.add(content)
            return

        changed = False
        if structure is not None:
            content.wiki_structure = structure
            changed = True
        if pages is not None:
            content.wiki_pages = pages
            content.total_pages = len(pages or {})
            changed = True
        if pages_increment:
            existing_pages = content.wiki_pages or {}
            existing_pages.update(pages_increment)
            content.wiki_pages = existing_pages
            content.total_pages = len(existing_pages)
            changed = True
        if changed:
            content.updated_time = datetime.utcnow()

    def _create_or_update_git_repositories(self, session, wiki_id: str, repo_url: str, branch: str, 
                                         existing_topic_id_code: Optional[str] = None, 
                                         sub_repos: Optional[List[SubRepoInfo]] = None,
                                         topic_ids: Optional[List[str]] = None) -> None:
        """
        创建或更新Git仓库记录和Wiki-仓库关系
        
        Args:
            session: 数据库会话
            wiki_id: Wiki ID
            repo_url: 主仓库URL
            branch: 主仓库分支
            existing_topic_id_code: 现有的代码Topic ID
            sub_repos: 子仓库列表
            topic_ids: 所有Topic ID列表（按顺序：主仓库、子仓库1、子仓库2...）
        """
        from api.service.git_repository_service import upsert_repository
        from api.service.wiki_relation_service import add_relation

        # 解析主仓库信息
        repo_owner, repo_name, _ = git_utils.extract_repo_info(repo_url)
        
        # 1. 处理主仓库
        main_topic_id = existing_topic_id_code if existing_topic_id_code else (topic_ids[0] if topic_ids else None)
        
        main_repo = upsert_repository(
            repo_url=repo_url,
            repo_owner=repo_owner,
            repo_name=repo_name,
            branch=branch,
            repo_type="github",  # 可以从repo_url推断
            code_topic_id=main_topic_id,
            session=session,
        )

        # 创建主仓库关系
        add_relation(
            wiki_id,
            main_repo.id,
            is_main_repo=True,
            session=session,
        )
        logger.info(f"已创建主仓库关系: wiki_id={wiki_id}, repo_id={main_repo.id}")

        # 2. 处理子仓库
        if sub_repos:
            for i, sub_repo in enumerate(sub_repos):
                sub_repo_owner, sub_repo_name, _ = git_utils.extract_repo_info(sub_repo.url)
                sub_topic_id = topic_ids[i + 1] if topic_ids and len(topic_ids) > i + 1 else None
                
                sub_repo_record = upsert_repository(
                    repo_url=sub_repo.url,
                    repo_owner=sub_repo_owner,
                    repo_name=sub_repo_name,
                    branch=sub_repo.branch,
                    repo_type="github",  # 可以从repo_url推断
                    code_topic_id=sub_topic_id,
                    session=session,
                )
                
                # 创建子仓库关系
                add_relation(
                    wiki_id,
                    sub_repo_record.id,
                    is_main_repo=False,
                    session=session,
                )
                logger.info(f"已创建子仓库关系: wiki_id={wiki_id}, repo_id={sub_repo_record.id}")

    def _update_wiki_repository_topic_ids(self, session, wiki_id: str, topic_ids: List[str]) -> None:
        """
        更新Wiki相关仓库的Topic ID
        
        Args:
            session: 数据库会话
            wiki_id: Wiki ID
            topic_ids: Topic ID列表（按id顺序）
        """
        from api.service.wiki_relation_service import list_relations
        from api.service.git_repository_service import update_repository
        
        # 获取Wiki的所有仓库关系，按id排序
        relations = list_relations(wiki_id, session=session)
        relations.sort(key=lambda x: x.id)

        # 更新每个仓库的Topic ID
        for i, relation in enumerate(relations):
            if i < len(topic_ids):
                update_repository(relation.repository_id, code_topic_id=topic_ids[i], session=session)
                logger.info(f"已更新仓库Topic ID: repo_id={relation.repository_id}, topic_id={topic_ids[i]}")
            else:
                logger.warning(f"Topic ID数量不足，跳过仓库: repo_id={relation.repository_id}")


    async def refresh(
        self,
        job_id: str,
        wiki_id: str,
        repo_url: str,
        repo_type: str,
        branch: str,
        token: Optional[str],
        language: str,
        model_settings: Dict[str, Any],
        comprehensive: bool = True,
        job_context=None,
        sub_repos_str: Optional[str] = None,
        fast_mode: bool = None,  # 修改：快速模式，None表示使用配置默认值
        **kwargs
    ):
        """
        刷新Wiki内容：
        1. 检查本地仓库是否存在，必要时重新下载
        2. 对已有仓库执行 git pull 并收集变更信息
        3. 根据代码变更或用户配置决定是否重建结构与页面
        4. 重新生成 Wiki 结构和内容
        
        参数:
            refresh_pages (List[str], optional): 指定要重新生成的页面ID列表。
                                               如果为None，则重新生成所有页面。
                                               如果提供列表，则只重新生成指定的页面，
                                               其他页面保持现有内容。
        
        示例:
            # 刷新所有页面
            await generator.refresh(job_id, wiki_id, repo_url, ..., refresh_pages=None)
            
            # 只刷新特定页面
           await generator.refresh(job_id, wiki_id, repo_url, ..., 
                                  refresh_pages=["overview", "api-reference", "installation"])
        """
        job_kwargs = getattr(job_context, "kwargs", {}) if job_context else {}
        user_code_ctx = job_kwargs.get("user_code")
        user_name_ctx = job_kwargs.get("user_name")
        user_id_ctx = job_kwargs.get("user_id")
        session_id_ctx = job_kwargs.get("session_id") or job_id

        langfuse_ctx = get_langfuse_context()
        langfuse_enabled = bool(langfuse_ctx.get("enabled"))
        langfuse_env = langfuse_ctx.get("environment")

        user_id_for_trace, effective_code, user_label = _format_user_identifier(
            user_name_ctx,
            user_code_ctx,
            user_id_ctx,
        )

        refresh_labels: List[str] = []

        def _append_refresh_label(value: Optional[str]) -> None:
            if value and value not in refresh_labels:
                refresh_labels.append(value)

        _append_refresh_label(user_label)
        if wiki_id:
            _append_refresh_label(str(wiki_id))
        repo_combined = f"{repo_url}#{branch}".rstrip("#")
        _append_refresh_label(repo_combined if repo_url else None)
        if repo_url:
            _append_refresh_label(f"repo:{repo_url}")
        if branch:
            _append_refresh_label(f"branch:{branch}")

        refresh_base_metadata: Dict[str, Any] = {
            "session_id": session_id_ctx,
            "job_id": job_id,
            "wiki_id": wiki_id,
            "repo_url": repo_url,
            "branch": branch,
            "environment": langfuse_env,
            "labels": refresh_labels,
        }

        if user_id_for_trace:
            refresh_base_metadata["user_id"] = user_id_for_trace
        if effective_code:
            refresh_base_metadata["user_code"] = effective_code
        if user_name_ctx:
            refresh_base_metadata["user_name"] = user_name_ctx
        if user_label:
            refresh_base_metadata["user_label"] = user_label
        if isinstance(model_settings, dict):
            provider_val = model_settings.get("provider")
            model_val = model_settings.get("model")
            if provider_val:
                refresh_base_metadata["provider"] = provider_val
            if model_val:
                refresh_base_metadata["model"] = model_val

        refresh_metadata = build_langfuse_metadata(
            refresh_base_metadata,
            "wiki_refresh",
            stage="refresh_init",
        )

        # 绑定刷新根跨度，保证上下文一致
        refresh_span_handle = bind_langfuse_span(
            create_langfuse_span(
                langfuse_enabled,
                "wiki_refresh",
                refresh_metadata,
            )
        )
        refresh_span = refresh_span_handle.enter()
        refresh_span_closed = not bool(refresh_span)

        refresh_trace_context = None
        if refresh_span and hasattr(refresh_span, "trace_id"):
            try:
                refresh_trace_context = {"trace_id": str(getattr(refresh_span, "trace_id", ""))}
                if hasattr(refresh_span, "id"):
                    refresh_trace_context["parent_span_id"] = getattr(refresh_span, "id", None)
            except Exception:
                refresh_trace_context = None

        refresh_langfuse_settings = {
            "enabled": langfuse_enabled,
            "base_metadata": refresh_base_metadata,
            "conversation_type": "wiki_refresh",
            "trace_context": refresh_trace_context,
        }

        def _close_refresh_span(exc_type=None, exc_val=None, exc_tb=None):
            nonlocal refresh_span_closed
            if not refresh_span_closed:
                refresh_span_handle.exit(exc_type, exc_val, exc_tb)
                refresh_span_closed = True

        if refresh_span:
            refresh_span.update(
                input={
                    "repo_url": repo_url,
                    "branch": branch,
                    "language": language,
                }
            )
            if langfuse_enabled:
                flush_langfuse()

        # 状态更新函数（刷新流程：仅更新 job，不更新 wiki_info 状态）
        def update_status_func(status, progress, stage, stage_progress, stage_message, **kw):
            try:
                # 若任务已被请求暂停/取消，避免覆盖状态
                try:
                    if job_context and getattr(job_context, 'pause_requested', False):
                        return
                except Exception:
                    pass
                with session_scope() as session:
                    update_params = {
                        "status": status,
                        "progress": progress,
                        "stage": stage,
                        "stage_progress": stage_progress,
                        "stage_message": stage_message
                    }
                    if status == self.STATUS_FAILED:
                        update_params["error_message"] = stage_message
                    if "processed_files" in kw and kw.get("processed_files") is not None:
                        update_params["processed_files"] = kw.get("processed_files")
                    if "total_files" in kw and kw.get("total_files") is not None:
                        update_params["total_files"] = kw.get("total_files")
                    # 明确引用全局的 update_job 函数
                    from api.service.wiki_service import update_job
                    update_job(session, job_id, **update_params)
            except Exception as e:
                logger.error(f"刷新状态更新失败: {e}")

        # 刷新流程确保任务类型正确
        self._ensure_job_type(job_id, expected_type=1, force=True)

        # 解析子仓库
        sub_repos = self._parse_sub_repos(sub_repos_str)
        
        update_status_func("processing", 0, self.STAGE_REFRESH, 0, "开始刷新Wiki")
        
        span_exc_info: Tuple[Optional[type], Optional[BaseException], Optional[Any]] = (None, None, None)

        try:
            # 从repo_url提取owner和repo
            owner_name, repo_name, host = git_utils.extract_repo_info(repo_url)
            
            # 获取本地仓库路径
            repo_path = self._get_local_repo_path(owner_name, repo_name, branch)

            sub_repo_paths = [self._get_local_sub_repo_path(owner_name, repo_name, branch, sub_repo) for sub_repo in sub_repos]
            
            # 检查本地仓库是否存在
            repo_exists = os.path.exists(repo_path) and os.path.exists(os.path.join(repo_path, ".git"))
            
            refresh_result = None
            force_refresh = kwargs.get('force_refresh', False)
            refresh_pages = kwargs.get('refresh_pages') or None
            rebuild_structure = kwargs.get('rebuild_structure', False)
            custom_instructions = kwargs.get('custom_instructions')
            fast_mode = kwargs.get('fast_mode', True)
            
            if repo_exists:
                # 仓库存在，跳过git pull，直接使用当前代码内容
                logger.info(f"本地仓库存在，跳过 git pull，使用本地代码: {repo_path}")
                update_status_func(
                    self.STATUS_PROCESSING,
                    10,
                    self.STAGE_REFRESH,
                    100,
                    "跳过 git pull，使用本地代码仓库",
                )

                refresh_result = {
                    "updated": True,
                    "changes": [],
                    "additions": [],
                    "modifications": [],
                    "deletions": [],
                    "commit_info": {"message": "Skipped git pull; using existing repository"},
                    "output": "git pull skipped",
                }

                update_status_func(
                    self.STATUS_PROCESSING,
                    25,
                    self.STAGE_UPLOAD,
                    100,
                    "使用本地代码继续生成Wiki",
                )
            else:
                # 仓库不存在，执行完整重新下载
                logger.info(f"本地仓库不存在，执行完整重新下载: {repo_path}")

                repo_path = await self._execute_download_stage(
                    job_id,
                    wiki_id,
                    repo_url,
                    repo_type,
                    branch,
                    token,
                    update_status_func,
                    job_context,
                    sub_repos,
                )

                repo_paths = [repo_path]
                if sub_repos:
                    for sub_repo in sub_repos:
                        local_sub_repo_path = self._get_local_sub_repo_path(
                            owner_name,
                            repo_name,
                            branch,
                            sub_repo,
                        )
                        repo_paths.append(local_sub_repo_path)

                await self._execute_upload_stage(
                    job_id,
                    wiki_id,
                    repo_url,
                    repo_name,
                    owner_name,
                    branch,
                    repo_paths,
                    update_status_func=update_status_func,
                    job_context=job_context,
                    fast_mode=kwargs.get('fast_mode', True),
                )

                refresh_result = {
                    "updated": True,
                    "changes": [],
                    "additions": [],
                    "modifications": [],
                    "deletions": [],
                    "commit_info": {"message": "完整重新下载"},
                    "output": "Repository downloaded completely",
                }

                with session_scope() as session:
                    try:
                        self._create_or_update_git_repositories(
                            session,
                            wiki_id,
                            repo_url,
                            branch,
                            None,
                            sub_repos,
                            None,
                        )
                        session.commit()
                    except Exception as error:
                        logger.warning(f"刷新流程更新仓库记录失败: {error}")

            repo_paths = [repo_path]
            if sub_repo_paths:
                repo_paths.extend(sub_repo_paths)

            namespace_map: Dict[str, str] = {}
            namespace_map[repo_path] = git_utils.build_repo_namespace(owner_name, repo_name, branch)
            if sub_repo_paths:
                for sub_repo_path, sub_repo in zip(sub_repo_paths, sub_repos):
                    sub_owner, sub_repo_name, _ = git_utils.extract_repo_info(sub_repo.url)
                    namespace_map[sub_repo_path] = git_utils.build_repo_namespace(sub_owner, sub_repo_name, sub_repo.branch)
            
            # 检查是否需要重新生成内容
            has_code_changes = refresh_result and refresh_result.get("updated", False)

            # 决定是否需要重新生成结构和页面
            should_regenerate_structure = has_code_changes or force_refresh or rebuild_structure
            should_regenerate_pages = has_code_changes or force_refresh or (refresh_pages is not None)

            # 当仅选择部分页面且未指定强制或重建时，复用现有结构
            if refresh_pages and not force_refresh and not rebuild_structure:
                should_regenerate_structure = False

            # 阶段3: 根据需要重新生成Wiki结构
            if should_regenerate_structure:
                logger.info(
                    "重新生成Wiki结构 - 代码变更: %s, 强制刷新: %s, 用户要求重建: %s",
                    has_code_changes,
                    force_refresh,
                    rebuild_structure,
                )
                structure = await self._execute_structure_stage(
                    job_id,
                    wiki_id,
                    repo_paths,
                    language,
                    model_settings,
                    comprehensive,
                    update_status_func=update_status_func,
                    job_context=job_context,
                    user_instructions=custom_instructions,
                    langfuse_settings=refresh_langfuse_settings,
                    **kwargs,
                )
            else:
                # 使用现有结构
                logger.info("使用现有Wiki结构")
                structure = self._get_existing_structure(wiki_id)
                update_status_func("processing", 50, "structure", 100, "使用现有Wiki结构")
            
            # 阶段4: 根据需要重新生成Wiki页面
            if refresh_pages and not force_refresh and not rebuild_structure:
                # 仅刷新指定页面
                should_regenerate_pages = True

            if should_regenerate_pages:
                if refresh_pages is not None:
                    logger.info(f"部分刷新模式：只刷新指定页面 {refresh_pages}")
                else:
                    logger.info(f"完整刷新模式 - 代码变更: {has_code_changes}, 强制刷新: {force_refresh}")
                
                force_regenerate_pages = (
                    force_refresh
                    or rebuild_structure
                    or (not refresh_pages and has_code_changes)
                )

                generated_pages = await self._execute_pages_stage(
                    job_id,
                    wiki_id,
                    structure,
                    model_settings,
                    repo_paths,
                    language,
                    update_status_func=update_status_func,
                    job_context=job_context,
                    fast_mode=fast_mode,
                    force_regenerate=force_regenerate_pages,
                    specific_pages=refresh_pages,
                    user_instructions=custom_instructions,
                    langfuse_settings=refresh_langfuse_settings,
                )
            else:
                # 使用现有页面
                logger.info("使用现有Wiki页面")
                generated_pages = self._get_existing_pages(wiki_id)
                update_status_func("processing", 100, "pages", 100, "使用现有Wiki页面")
            
            # 5. 完成状态（仅更新 job，不更新 wiki_info 状态）
            update_status_func("completed", 100, "completed", 100, "Wiki刷新成功")
            
            # 先保存到wiki_job的result字段
            with session_scope() as session:
                # 构建完整的wiki_data
                wiki_data = {}
                if structure:
                    wiki_data["wiki_structure"] = structure
                if generated_pages:
                    wiki_data["generated_pages"] = generated_pages
                
                # 保存刷新信息到job的result字段，包含完整的wiki_data
                refresh_info = {
                    "refresh_time": datetime.now().isoformat(),
                    "refresh_type": "incremental" if repo_exists else "full_download",
                    "changes": refresh_result["changes"] if refresh_result else [],
                    "commit_info": refresh_result["commit_info"] if refresh_result else {},
                    "wiki_data": wiki_data,
                    "custom_instructions": custom_instructions or "",
                    "rebuild_structure": rebuild_structure,
                }
                
                merged_result: Dict[str, Any] = {}
                current_job = get_job(session, job_id)
                if current_job and getattr(current_job, "result", None):
                    if isinstance(current_job.result, dict):
                        merged_result = dict(current_job.result)
                merged_result.update(refresh_info)
                update_job(session, job_id, result=merged_result)
                logger.info(f"已保存刷新结果到job的result字段，job_id: {job_id}")

            # 最后成功时，将数据写入wiki_info表
            with session_scope() as session:
                job_wiki_data = refresh_info.get("wiki_data", {})

                # 不再写入旧表大字段，仅同步内容到 ai_dw_wiki_content
                # 同步写入 ai_dw_wiki_content（刷新流程全量）
                try:
                    structure_data = job_wiki_data.get("wiki_structure") if job_wiki_data else None
                    pages_data = job_wiki_data.get("generated_pages") if job_wiki_data else None
                    self._upsert_ai_dw_content(session, wiki_id, structure=structure_data, pages=pages_data)

                    # 将wiki内容保存到文件里
                    save_wiki_content(session, wiki_id, structure_data, pages_data)
                except Exception as e:
                    logger.warning(f"写入ai_dw_wiki_content失败(刷新阶段)：{e}")

            if refresh_span:
                refresh_span.update(
                    output={
                        "status": "completed",
                        "regenerated_pages": len(generated_pages) if generated_pages else 0,
                    }
                )
                if langfuse_enabled:
                    flush_langfuse()

        except Exception as e:
            error_message = f"Wiki刷新失败: {str(e)}"
            logger.error(error_message, exc_info=True)
            span_exc_info = (type(e), e, e.__traceback__)
            
            if job_context and job_context.pause_requested:
                logger.info(f"检测到暂停请求，不更新为失败状态")
                return
            
            # 仅更新 job 失败状态与错误信息，不更新 wiki_info
            update_status_func("failed", 0, "failed", 0, error_message)

            if refresh_span:
                try:
                    refresh_span.update(level="ERROR", status_message=str(e))
                except Exception as span_error:
                    logger.debug(f"刷新流程更新Langfuse span失败: {span_error}")

            # 重新抛出异常，以便任务管理器能够执行统一的失败处理/重试逻辑
            raise
        finally:
            _close_refresh_span(*span_exc_info)

    def _count_all_repo_files(self, repo_paths: List[str]) -> int:
        """计算所有仓库的文件总数"""
        total_count = 0
        for repo_path in repo_paths:
            if os.path.exists(repo_path):
                for root, dirs, files in os.walk(repo_path):
                    # 跳过.git目录
                    if '.git' in root:
                        continue
                    for file in files:
                        if self._is_code_file(file):
                            total_count += 1
        return total_count

    def _is_code_file(self, filename: str) -> bool:
        """判断是否为代码文件"""
        code_extensions = {
            '.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.cpp', '.c', '.h', '.hpp',
            '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala', '.sh',
            '.sql', '.html', '.css', '.scss', '.less', '.vue', '.json', '.xml',
            '.yaml', '.yml', '.md', '.txt', '.cfg', '.conf', '.ini', '.toml'
        }
        
        _, ext = os.path.splitext(filename.lower())
        return ext in code_extensions

def save_wiki_content(session, wiki_id, wiki_structure, generated_pages):
    """
    将wiki_data的内容写到 ~/.adalflow/project_workspace/<repo_owner>/<repo_name-branch>/i-doc/wiki 目录下
    """
    wiki_path = None
    try:
        wiki_info: WikiInfo = get_wiki_info(session, wiki_id)
        if wiki_info:
            branch = re.sub(r'[^\w\-_]', '_', wiki_info.branch)
            wiki_path = Path(os.path.expanduser(os.path.join("~", ".adalflow", "project_workspace", wiki_info.repo_owner, f"{wiki_info.repo_name}-{branch}", "i-doc", "wiki")))
            logger.info(f"将wiki内容写入目录：{wiki_path}")
            if not wiki_path.exists():
                wiki_path.mkdir(parents=True, exist_ok=True)
            # 写入wiki_structure.json
            wiki_structure_path = wiki_path.joinpath("wiki_structure.json")
            wiki_structure_path.write_text(data=json.dumps(wiki_structure, ensure_ascii=False), encoding='utf-8')
            logger.info(f"完成wiki_structure.json写入")
            # 写入wiki的markdown文件
            wiki_pages_path = wiki_path.joinpath("pages")
            if wiki_pages_path.exists():
                shutil.rmtree(wiki_pages_path)
            wiki_pages_path.mkdir(parents=True, exist_ok=True)
            for structure_page in wiki_structure.get("pages", []):
                page_content = generated_pages.get(structure_page.get("id"), "")
                if page_content:
                    title = structure_page.get("title")
                    wiki_page_path = wiki_pages_path.joinpath(f"{title}.md")
                    wiki_page_path.write_text(data=page_content, encoding='utf-8')
                    logger.info(f"写入markdown文档：{wiki_page_path}")
        else:
            logger.info(f"根据wiki_id：{wiki_id}从数据库里找不到wiki信息")
    except Exception as ex:
        logger.error(f"将wiki写入文件时发生异常：{ex}")
        if wiki_path and wiki_path.exists():
            shutil.rmtree(wiki_path)
