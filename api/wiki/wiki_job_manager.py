import os
import json
import asyncio
import logging
import threading
import time
import uuid
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Set
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass, field
from enum import Enum
import socket
from sqlmodel import select, and_, or_, text

from api.database.service import DatabaseService
from api.docchain.manager import Doc<PERSON>hainManager
from api.wiki.generator import WikiGenerator
from api.wiki.distributed_lock import get_distributed_lock_service
from api.database.base import session_scope
from api.model.wiki_job import WikiJob
from api.service.wiki_service import get_job, create_job, update_job
from api.service.wiki_info_service import update_wiki_info
from api.model.job_manager_lock import JobManagerInstance, GlobalJobState, JobLock
from api.config import get_wiki_jobs_config
from api.cache.redis.manager import redis_manager

logger = logging.getLogger(__name__)

class JobStatus(Enum):
    """Job状态枚举"""
    PENDING = "pending"
    PENDING_RESUME = "pending_resume"
    PROCESSING = "processing"
    RESUMING = "resuming"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"
    SYNCING = "syncing"  # 新增：同步索引状态

@dataclass
class JobContext:
    """Job执行上下文"""
    job_id: str
    wiki_id: str
    repo_url: str
    repo_type: str
    branch: str
    token: Optional[str]
    language: str
    model_settings: Dict[str, Any]
    comprehensive: bool = True
    # 新增子仓库地址
    sub_repos: Optional[str] = None
    kwargs: Dict[str, Any] = field(default_factory=dict)
    
    # 执行相关
    thread: Optional[threading.Thread] = None
    future: Optional[Any] = None  # ThreadPoolExecutor Future对象
    async_task: Optional[Any] = None  # asyncio Task对象
    start_time: Optional[datetime] = None
    last_heartbeat: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3
    
    # 暂停/继续相关
    pause_requested: bool = False
    pause_event: Optional[threading.Event] = field(default_factory=threading.Event)
    # 取消相关标志：用于区分“暂停等待”和“取消退出”
    cancel_requested: bool = False
    current_stage: Optional[str] = None
    stage_progress: int = 0
    
    # 新增：文件进度相关字段，用于接管任务时的阶段推断
    total_files: Optional[int] = None
    processed_files: Optional[int] = None
    
    # 新增：状态锁和执行标识
    status_lock: threading.RLock = field(default_factory=threading.RLock)
    execution_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    is_active_execution: bool = True  # 标识是否是活跃的执行

class WikiJobManager:
    """
    优化版Wiki任务管理器
    
    主要优化：
    1. 统一的任务分配入口，严格的并发控制
    2. 智能队列管理，自动扫描和分配
    3. 健壮的故障恢复机制
    4. 严格的心跳检测和实例管理
    
    注意：启用严格一致性检查可能会误杀正在排队的任务，建议在生产环境中谨慎使用
    """
    
    @staticmethod
    def _truncate_message(message: str, max_length: int = 200) -> str:
        """截断过长的消息"""
        if len(message) <= max_length:
            return message
        return message[:max_length-3] + "..."

    def __init__(self, db_service: DatabaseService, docchain_manager: DocChainManager, 
                 max_concurrent_jobs: int = None, job_timeout_minutes: int = None, 
                 global_max_concurrent: int = None):
        logger.info("开始初始化WikiJobManager...")
        
        try:
            # 从配置文件读取参数，支持传入参数覆盖
            config = get_wiki_jobs_config()
            self.max_concurrent_jobs = max_concurrent_jobs or config.get('instance_max_concurrent', 2)
            self.global_max_concurrent = global_max_concurrent or config.get('global_max_concurrent', 6)
            self.job_timeout_minutes = job_timeout_minutes or config.get('timeout_minutes', 180)
            self.job_context_cache_ttl = config.get('context_cache_ttl', 24 * 3600)
            
            # 新增：一致性检查配置
            self.consistency_check_strict = config.get('consistency_check_strict', False)  # 是否启用严格的一致性检查
            self.consistency_check_grace_period = config.get('consistency_check_grace_period', 300)  # 宽限期（秒）
            self.consistency_check_threshold = config.get('consistency_check_threshold', 2)  # 触发检查的差异阈值
            
            # 验证输入参数
            if self.max_concurrent_jobs <= 0:
                raise ValueError(f"max_concurrent_jobs必须大于0，当前值: {self.max_concurrent_jobs}")
            if self.job_timeout_minutes <= 0:
                raise ValueError(f"job_timeout_minutes必须大于0，当前值: {self.job_timeout_minutes}")
            if self.global_max_concurrent <= 0:
                raise ValueError(f"global_max_concurrent必须大于0，当前值: {self.global_max_concurrent}")
            
            logger.info(f"参数验证通过: max_concurrent_jobs={self.max_concurrent_jobs}, job_timeout_minutes={self.job_timeout_minutes}, global_max_concurrent={self.global_max_concurrent}")
            logger.info(f"一致性检查配置: strict={self.consistency_check_strict}, grace_period={self.consistency_check_grace_period}s, threshold={self.consistency_check_threshold}")
            
            # 设置基本属性
            self.db_service = db_service
            self.docchain_manager = docchain_manager
            try:
                self._redis = redis_manager.get_client()
                if self._redis:
                    logger.info("Redis客户端初始化完成，将缓存任务上下文")
                else:
                    logger.info("Redis未启用，任务上下文缓存功能跳过")
            except Exception as redis_error:
                self._redis = None
                logger.warning(f"获取Redis客户端失败，继续以无缓存模式运行: {redis_error}")
            
            # 初始化Job管理相关组件
            self.active_jobs: Dict[str, JobContext] = {}
            self.paused_jobs: Dict[str, JobContext] = {}
            self.job_queue: asyncio.Queue = asyncio.Queue()
            
            # 新增：任务分配锁，确保统一入口的原子性（延迟绑定事件循环）
            self._job_assignment_lock = None
            self._job_assignment_lock_loop = None
            
            # 初始化线程池
            self.executor = ThreadPoolExecutor(max_workers=self.max_concurrent_jobs)
            
            # 初始化监控相关
            self._monitor_task: Optional[asyncio.Task] = None
            self._worker_task: Optional[asyncio.Task] = None
            self._heartbeat_task: Optional[asyncio.Task] = None
            self._queue_scanner_task: Optional[asyncio.Task] = None
            self._instance_manager_task: Optional[asyncio.Task] = None
            self._shutdown = False
            
            # 初始化状态管理
            self._global_status_lock = threading.RLock()
            self._execution_registry: Dict[str, str] = {}
            self._last_queue_scan = 0
            self._last_instance_check = 0
            
            # 新增：任务完成事件，用于触发立即扫描
            self._job_completed_event = asyncio.Event()
            
            # 初始化分布式锁服务
            try:
                self.distributed_lock = get_distributed_lock_service()
                if not self.distributed_lock:
                    raise RuntimeError("无法获取分布式锁服务实例")
                logger.info("分布式锁服务初始化完成")
            except Exception as e:
                logger.error(f"分布式锁服务初始化失败: {e}")
                raise
            
            # 初始化WikiGenerator
            try:
                self.wiki_generator = WikiGenerator(db_service, docchain_manager)
                # 设置job_manager引用，用于安全状态更新
                self.wiki_generator.set_job_manager(self)
                logger.info("WikiGenerator初始化完成")
            except Exception as e:
                logger.error(f"WikiGenerator初始化失败: {e}")
                raise
            
            # 新增：僵尸锁清理冷却期控制，防止循环清理
            self._last_zombie_cleanup = None  # 最后一次僵尸锁清理时间
            self._zombie_cleanup_cooldown = 300  # 5分钟冷却期（秒）
            
            logger.info(f"优化版WikiJobManager初始化完成: max_concurrent_jobs={self.max_concurrent_jobs}, timeout={self.job_timeout_minutes}分钟, global_max_concurrent={self.global_max_concurrent}")
            
        except Exception as e:
            logger.error(f"WikiJobManager初始化失败: {e}")
            # 清理已初始化的资源
            if hasattr(self, 'executor') and self.executor:
                try:
                    self.executor.shutdown(wait=False)
                    logger.info("已清理线程池资源")
                except Exception as cleanup_error:
                    logger.error(f"清理线程池时出错: {cleanup_error}")
            raise

    def _get_job_assignment_lock(self) -> asyncio.Lock:
        """返回绑定当前事件循环的任务分配锁，避免跨事件循环使用导致的异常。"""
        loop = asyncio.get_running_loop()
        if self._job_assignment_lock is None or self._job_assignment_lock_loop is not loop:
            self._job_assignment_lock = asyncio.Lock()
            self._job_assignment_lock_loop = loop
        return self._job_assignment_lock

    async def start(self):
        """启动JobManager - 改进的启动顺序"""
        logger.info("启动优化版WikiJobManager...")
        
        # 第一步：全面清理死实例、孤儿锁和孤儿全局状态
        logger.info("第一步：执行启动前清理...")
        try:
            await self._startup_comprehensive_cleanup()
        except Exception as e:
            logger.error(f"启动前清理失败，但继续启动: {e}")
        
        # 第二步：注册分布式实例
        logger.info("第二步：注册当前实例...")
        await self.distributed_lock.register_instance(self.max_concurrent_jobs)
        
        # 第三步：启动故障恢复进程 - 检查和恢复未完成的任务
        logger.info("第三步：恢复待处理任务...")
        await self._recover_jobs_on_startup()
        
        # 第三步补充：检查和修复当前实例的锁数量异常
        logger.info("第三步补充：检查实例锁数量...")
        await self._check_and_fix_excessive_locks()
        
        # 第四步：启动监控任务
        logger.info("第四步：启动监控服务...")
        self._monitor_task = asyncio.create_task(self._job_monitor())
        self._worker_task = asyncio.create_task(self._job_worker())
        self._heartbeat_task = asyncio.create_task(self._heartbeat_worker())
        
        # 启动优化的队列扫描器 - 定期扫描待处理任务
        self._queue_scanner_task = asyncio.create_task(self._optimized_queue_scanner_worker())
        
        # 启动严格的实例管理器 - 心跳检测和死实例清理
        self._instance_manager_task = asyncio.create_task(self._strict_instance_manager_worker())
        
        logger.info("优化版WikiJobManager已启动")

    async def stop(self):
        """停止JobManager"""
        logger.info("停止优化版WikiJobManager...")
        
        self._shutdown = True
        
        # 停止所有活跃任务
        for job_context in list(self.active_jobs.values()):
            job_context.pause_requested = True
            if job_context.pause_event:
                job_context.pause_event.clear()
        
        # 停止监控任务
        for task in [self._monitor_task, self._worker_task, self._heartbeat_task, 
                    self._queue_scanner_task, self._instance_manager_task]:
            if task and not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        # 注销分布式实例
        await self.distributed_lock.unregister_instance()
        
        logger.info("优化版WikiJobManager已停止")

    # ===== 核心优化：统一的任务分配入口 =====

    async def _unified_job_assignment(self, job_context: JobContext, operation: str = "direct_assign") -> bool:
        """
        统一的任务分配入口 - 修复版，解决原子性问题
        
        改进：
        1. 事务性操作：确保锁获取、内存更新、线程启动的原子性
        2. 完整的状态跟踪：使用状态机模式跟踪操作进度
        3. 强化的回滚机制：任何步骤失败都能完整回滚
        4. 资源泄露预防：确保所有资源都能正确释放
        
        Args:
            job_context: Job执行上下文
            operation: 操作类型 (submit, retry, resume, restart, scan, recover)
            
        Returns:
            bool: 分配是否成功
        """
        job_id = job_context.job_id
        
        # 状态跟踪变量
        assignment_state = {
            'lock_acquired': False,
            'added_to_registry': False,
            'added_to_active': False,
            'event_initialized': False,
            'status_updated': False,
            'global_state_updated': False,
            'heartbeat_updated': False,
            'task_started': False
        }
        
        # 使用异步锁确保原子性
        assignment_lock = self._get_job_assignment_lock()
        async with assignment_lock:
            try:
                # 1. 严格检查并发限制（双重检查）
                if not await self._strict_concurrency_check():
                    logger.warning(f"并发限制检查失败，无法分配Job {job_id}")
                    return False
                
                # 2. 检查job是否已存在于当前实例
                # 说明：若任务已在当前实例执行/暂停，表明已被其它协程（如队列扫描器）成功分配。
                # 这里直接返回True，避免上层误判为提交失败导致接口返回503。
                if job_id in self.active_jobs or job_id in self.paused_jobs:
                    logger.warning(f"Job {job_id} 已在当前实例中执行或暂停（视为已提交）")
                    return True
                
                # 3. 预先验证任务上下文的完整性
                if not self._validate_job_context(job_context):
                    logger.error(f"Job {job_id} 上下文验证失败")
                    return False

                # 缓存最新上下文信息，确保恢复时保留扩展指令
                self._cache_job_context(job_context)
                
                # 4. 尝试获取分布式锁 - 关键步骤
                assignment_state['lock_acquired'] = await self.distributed_lock.acquire_job_lock(job_id, operation)
                if not assignment_state['lock_acquired']:
                    # 可能是并发窗口内被队列扫描器/其他实例先获取到锁，这种情况下任务其实已在处理。
                    # 为了接口幂等性，这里再次确认锁/状态后按成功处理。
                    logger.warning(f"Job {job_id} 无法获取分布式锁，可能被其他实例处理")
                    try:
                        lock_info = await self.distributed_lock.get_job_lock_info(job_id)
                    except Exception:
                        lock_info = None
                    if lock_info:
                        logger.info(f"检测到Job {job_id} 已被其它执行流锁定（instance={lock_info.get('instance_id')}），视为提交成功")
                        return True
                    return False
                
                # 5. 锁获取成功后，立即创建完整的执行上下文
                with self._global_status_lock:
                    # 5a. 注册到执行列表
                    self._execution_registry[job_id] = job_context.execution_id
                    job_context.is_active_execution = True
                    assignment_state['added_to_registry'] = True
                    
                    # 5b. 立即加入活跃列表，避免时序窗口
                    self.active_jobs[job_id] = job_context
                    assignment_state['added_to_active'] = True
                
                # 6. 初始化线程控制事件
                job_context.pause_event = threading.Event()
                job_context.pause_event.set()
                assignment_state['event_initialized'] = True
                
                # 7. 设置任务开始时间和心跳（在线程启动前）
                job_context.start_time = datetime.now()
                job_context.last_heartbeat = datetime.now()
                
                # 8. 更新任务状态为处理中
                # 修复：根据操作类型确定正确的状态，保持原有stage状态
                if operation in ["resume", "resume_after_cleanup"]:
                    # 恢复操作：保持原有阶段和进度
                    target_stage = job_context.current_stage or "download"
                    target_progress = job_context.stage_progress or 0
                    status_message = f"任务已恢复执行，从{target_stage}阶段继续"
                elif operation == "adopt":
                    # 接管操作：保持原有阶段和进度，不要强制重置
                    # WikiGenerator的_determine_start_stage方法会根据stage和stage_progress智能判断
                    target_stage = job_context.current_stage or "download"
                    target_progress = job_context.stage_progress or 0
                    status_message = f"任务已接管，保持阶段: {target_stage}，进度: {target_progress}%，"
                    
                    logger.info(f"接管任务 {job_id}: 保持阶段 {target_stage}，进度 {target_progress}%，"
                               f"让WikiGenerator智能判断执行起点")
                else:
                    # 其他操作：根据原有状态确定，不强制重置
                    target_stage = job_context.current_stage or "download"
                    target_progress = job_context.stage_progress or 0
                    status_message = f"任务已分配到当前实例，操作类型: {operation}"
                
                assignment_state['status_updated'] = self._safe_update_job_status(
                    job_context, 
                    JobStatus.PROCESSING.value, 
                    0, 
                    target_stage, 
                    target_progress,
                    status_message
                )
                
                # 9. 更新全局任务状态
                await self.distributed_lock.update_global_job_state(
                    job_id, 
                    "processing",
                    {
                        "instance_id": self.distributed_lock.instance_id, 
                        "operation": operation,
                        "assign_time": datetime.now().isoformat()
                    }
                )
                assignment_state['global_state_updated'] = True
                
                # 10. 更新实例心跳
                await self.distributed_lock.update_heartbeat(len(self.active_jobs))
                assignment_state['heartbeat_updated'] = True
                
                # 11. 最后启动任务执行（关键改进：使用同步方法避免异步竞争）
                try:
                    # 创建任务但不立即启动，确保所有状态都已设置
                    task = asyncio.create_task(self._execute_job(job_context))
                    job_context.async_task = task  # 保存任务引用
                    assignment_state['task_started'] = True
                    
                    logger.info(f"统一分配成功: Job {job_id} 已完整分配到当前实例，操作: {operation}，当前活跃任务数: {len(self.active_jobs)}")
                    return True
                    
                except Exception as task_error:
                    logger.error(f"启动任务执行失败: {task_error}")
                    raise  # 抛出异常触发回滚
                
            except Exception as e:
                logger.error(f"分配job {job_id} 到当前实例失败: {e}")
                
                # 完整的事务性回滚操作
                await self._enhanced_rollback_failed_assignment(job_id, job_context, assignment_state)
                return False

    async def _enhanced_rollback_failed_assignment(self, job_id: str, job_context: JobContext, 
                                                 assignment_state: dict):
        """增强的回滚操作 - 解决资源泄露问题"""
        try:
            logger.warning(f"开始回滚失败的任务分配: {job_id}")
            
            # 1. 停止已启动的任务（关键改进）
            if assignment_state.get('task_started') and hasattr(job_context, 'async_task'):
                try:
                    if not job_context.async_task.done():
                        job_context.async_task.cancel()
                        logger.info(f"已取消Job {job_id} 的异步任务")
                except Exception as e:
                    logger.warning(f"取消异步任务失败: {e}")
            
            # 2. 设置暂停标志，确保任何正在执行的代码能感知到取消
            if assignment_state.get('event_initialized'):
                job_context.pause_requested = True
                if job_context.pause_event:
                    job_context.pause_event.clear()
            
            # 3. 从活跃列表移除
            if assignment_state.get('added_to_active') and job_id in self.active_jobs:
                del self.active_jobs[job_id]
                logger.debug(f"已从活跃列表移除Job {job_id}")
            
            # 4. 清理执行注册
            if assignment_state.get('added_to_registry'):
                with self._global_status_lock:
                    if job_id in self._execution_registry:
                        del self._execution_registry[job_id]
                    job_context.is_active_execution = False
                    logger.debug(f"已清理Job {job_id} 执行注册")
            
            # 5. 释放分布式锁
            if assignment_state.get('lock_acquired'):
                try:
                    await self.distributed_lock.release_job_lock(job_id)
                    logger.debug(f"已释放Job {job_id} 分布式锁")
                except Exception as e:
                    logger.warning(f"释放分布式锁失败: {e}")
                    # 强制释放
                    try:
                        await self.distributed_lock.force_release_job_lock(job_id, "rollback_failed_assignment")
                        logger.info(f"已强制释放Job {job_id} 分布式锁")
                    except Exception as e2:
                        logger.error(f"强制释放分布式锁失败: {e2}")
            
            # 6. 清理全局状态（如果已更新）
            if assignment_state.get('global_state_updated'):
                try:
                    await self.distributed_lock.update_global_job_state(
                        job_id, 
                        "reassignable",
                        {"rollback_reason": "assignment_failed", "rollback_time": datetime.now().isoformat()}
                    )
                    logger.debug(f"已清理Job {job_id} 全局状态")
                except Exception as e:
                    logger.warning(f"清理全局状态失败: {e}")
            
            # 7. 更新心跳（如果已更新过）
            if assignment_state.get('heartbeat_updated'):
                try:
                    await self.distributed_lock.update_heartbeat(len(self.active_jobs))
                    logger.debug(f"已更新心跳，当前活跃任务数: {len(self.active_jobs)}")
                except Exception as e:
                    logger.warning(f"更新心跳失败: {e}")
            
            logger.info(f"任务分配回滚完成: {job_id}")
            
        except Exception as e:
            logger.error(f"回滚任务分配时出错: {e}")
            # 最后的安全措施：强制清理关键资源
            try:
                if job_id in self.active_jobs:
                    del self.active_jobs[job_id]
                await self.distributed_lock.force_release_job_lock(job_id, "emergency_rollback")
            except:
                pass

    def _validate_job_context(self, job_context: JobContext) -> bool:
        """验证任务上下文的完整性"""
        required_fields = ['job_id', 'wiki_id', 'repo_url', 'language']
        
        for field in required_fields:
            if not getattr(job_context, field, None):
                logger.error(f"Job上下文缺少必需字段: {field}")
                return False
        
        return True



    def _job_context_cache_key(self, job_id: str) -> str:
        return f"wiki:job:context:{job_id}"

    def _cache_job_context(self, job_context: JobContext) -> None:
        if not self._redis:
            return
        try:
            payload = {
                "kwargs": job_context.kwargs or {},
            }
            self._redis.set(
                self._job_context_cache_key(job_context.job_id),
                payload,
                expiration=max(self.job_context_cache_ttl, 60),
            )
        except Exception as exc:
            logger.warning(f"写入任务上下文缓存失败: {exc}")

    def _load_cached_job_context(self, job_id: str) -> Optional[Dict[str, Any]]:
        if not self._redis:
            return None
        try:
            cached = self._redis.get(self._job_context_cache_key(job_id))
            if not cached:
                return None
            if isinstance(cached, dict):
                return cached
            # 兼容旧格式（字符串JSON）
            if isinstance(cached, str):
                return json.loads(cached)
            return cached
        except Exception as exc:
            logger.warning(f"读取任务上下文缓存失败: {exc}")
            return None

    def _evict_job_context(self, job_id: str) -> None:
        if not self._redis:
            return
        try:
            self._redis.delete(self._job_context_cache_key(job_id))
        except Exception as exc:
            logger.warning(f"删除任务上下文缓存失败: {exc}")



    async def _strict_concurrency_check(self) -> bool:
        """
        严格的并发限制检查 - 改进版，使用一致的数据源
        
        改进：
        1. 统一使用数据库状态，确保一致性
        2. 全局并发检查更严格
        3. 增强日志记录便于调试
        
        Returns:
            bool: 是否可以接受新任务
        """
        try:
            # 都使用数据库状态，确保一致性
            actual_instance_locks = await self._get_actual_locks_count()
            global_active_count = await self.distributed_lock.get_global_active_jobs_count()
            
            # 检查当前实例并发
            if actual_instance_locks >= self.max_concurrent_jobs:
                logger.debug(f"当前实例已满: {actual_instance_locks}/{self.max_concurrent_jobs}")
                return False
            
            # 检查全局并发
            if global_active_count >= self.global_max_concurrent:
                logger.debug(f"全局任务已满: {global_active_count}/{self.global_max_concurrent}")
                return False
            
            # 记录详细的并发检查信息
            memory_active_count = len(self.active_jobs)
            if actual_instance_locks != memory_active_count:
                logger.debug(f"并发检查数据差异: 内存({memory_active_count}) vs 数据库({actual_instance_locks})")
            
            logger.debug(f"并发检查通过: 实例({actual_instance_locks}/{self.max_concurrent_jobs}) 全局({global_active_count}/{self.global_max_concurrent})")
            return True
            
        except Exception as e:
            logger.warning(f"检查并发失败，使用保守策略: {e}")
            # 保守策略：使用内存状态，只允许当前实例使用一半容量
            memory_active_count = len(self.active_jobs)
            if memory_active_count >= self.max_concurrent_jobs // 2:
                logger.debug(f"保守策略拒绝: {memory_active_count} >= {self.max_concurrent_jobs // 2}")
                return False
            logger.debug(f"保守策略通过: {memory_active_count} < {self.max_concurrent_jobs // 2}")
            return True

    # ===== 优化的任务操作入口 =====

    async def submit_job(self, job_context: JobContext) -> bool:
        """
        提交新任务的统一入口
        
        逻辑优化：
        1. 严格的并发检查
        2. 如果有空闲，直接分配到当前实例
        3. 如果没有空闲，写入数据库排队（不加锁）
        4. 支持优先级队列
        
        Args:
            job_context: Job执行上下文
            
        Returns:
            bool: 提交是否成功
        """
        job_id = job_context.job_id
        
        try:
            # 验证job在数据库中存在且状态正确
            with session_scope() as session:
                job = get_job(session, job_id)
                if not job:
                    logger.error(f"Job {job_id} 在数据库中不存在")
                    return False
                
                # 检查任务状态是否允许提交
                valid_statuses = ["pending", "pending_resume", "failed", "cancelled"]
                if job.status not in valid_statuses:
                    logger.warning(f"Job {job_id} 状态为 {job.status}，不允许提交")
                    return False

            # 缓存上下文信息，便于后续恢复保留扩展指令
            self._cache_job_context(job_context)
            
            # 检查并发限制
            if await self._strict_concurrency_check():
                # 当前实例和全局都有空闲，直接分配
                logger.info(f"实例和全局都有空闲，直接分配Job {job_id}")
                assigned = await self._unified_job_assignment(job_context, "submit")
                if not assigned:
                    # 兜底：若分配失败，检查是否已被其它协程或实例分配（存在锁或状态已变更）
                    try:
                        lock_info = await self.distributed_lock.get_job_lock_info(job_id)
                    except Exception:
                        lock_info = None
                    if lock_info:
                        logger.info(f"Job {job_id} 分配返回失败，但已检测到锁存在（instance={lock_info.get('instance_id')}），视为提交成功")
                        return True
                    # 再查一次数据库状态，处理中/恢复中也视为已接管
                    with session_scope() as session:
                        job = get_job(session, job_id)
                        if job and str(job.status) in [
                            JobStatus.PROCESSING.value,
                            JobStatus.RESUMING.value,
                            JobStatus.PENDING_RESUME.value
                        ]:
                            logger.info(f"Job {job_id} 状态为{job.status}，视为提交成功")
                            return True
                return assigned
            else:
                # 容量已满，写入数据库排队
                logger.info(f"容量已满，Job {job_id} 写入数据库排队")
                
                # 更新任务状态为等待
                with session_scope() as session:
                    current_instance_count = len(self.active_jobs)
                    try:
                        global_active_count = await self.distributed_lock.get_global_active_jobs_count()
                        stage_msg = f"等待空闲实例分配（当前实例: {current_instance_count}/{self.max_concurrent_jobs}, 全局: {global_active_count}/{self.global_max_concurrent}）"
                    except:
                        stage_msg = f"等待空闲实例分配（当前实例: {current_instance_count}/{self.max_concurrent_jobs}）"
                    
                    update_job(session, job_id, 
                             status=JobStatus.PENDING.value,
                             stage="queue", 
                             stage_progress=0,
                             stage_message=self._truncate_message(stage_msg))
                
                logger.info(f"Job {job_id} 已写入数据库排队，等待空闲时被扫描分配")
                return True
            
        except Exception as e:
            logger.error(f"提交job {job_id} 失败: {e}")
            return False

    async def retry_job(self, job_id: str) -> bool:
        """重试任务 - 使用统一分配入口"""
        try:
            with session_scope() as session:
                job = get_job(session, job_id)
                if not job:
                    logger.error(f"重试失败: Job {job_id} 不存在")
                    return False
                
                if job.status not in ["failed", "cancelled", "timeout"]:
                    logger.warning(f"Job {job_id} 状态为 {job.status}，不允许重试")
                    return False
                
                job_context = self._build_job_context_from_db(job)
                if not job_context:
                    logger.error(f"无法构建Job {job_id} 的重试上下文")
                    return False
                
                # 重置重试计数和错误状态
                job_context.retry_count += 1
                
                # 修复：确保任务状态与锁状态一致
                # 先检查是否有锁，如果有锁但状态不一致，需要清理
                lock_info = await self.distributed_lock.get_job_lock_info(job_id)
                if lock_info:
                    lock_instance_id = lock_info.get("instance_id")
                    if lock_instance_id != self.distributed_lock.instance_id:
                        logger.warning(f"Job {job_id} 被其他实例 {lock_instance_id} 锁定，尝试强制清理")
                        await self._force_cleanup_job_lock(job_id, "retry_other_instance_lock")
                
                # 检查是否可以直接分配
                if await self._strict_concurrency_check():
                    success = await self._unified_job_assignment(job_context, "retry")
                    if not success:
                        # 分配失败，可能是锁被死实例持有，强制清理
                        logger.warning(f"Job {job_id} 重试分配失败，尝试清理死实例锁")
                        await self._force_cleanup_job_lock(job_id, "retry_failed")
                        # 再次尝试分配
                        success = await self._unified_job_assignment(job_context, "retry_after_cleanup")
                    return success
                else:
                    # 写入数据库排队
                    update_job(session, job_id, 
                             status=JobStatus.PENDING.value,
                             stage="queue", 
                             stage_progress=0,
                             stage_message="重试任务排队等待分配")
                    logger.info(f"重试Job {job_id} 已排队等待分配")
                    return True
                    
        except Exception as e:
            logger.error(f"重试job {job_id} 失败: {e}")
            return False

    async def resume_job(self, job_id: str) -> bool:
        """继续任务 - 使用统一分配入口"""
        try:
            # 检查是否在暂停列表中
            if job_id in self.paused_jobs:
                job_context = self.paused_jobs[job_id]
                
                # 从暂停列表移除
                del self.paused_jobs[job_id]
                
                # 重置暂停状态
                job_context.pause_requested = False
                if job_context.pause_event:
                    job_context.pause_event.set()
                
                # 使用统一入口分配
                return await self._unified_job_assignment(job_context, "resume")
            else:
                # 从数据库恢复暂停的任务
                with session_scope() as session:
                    job = get_job(session, job_id)
                    if not job or job.status != "paused":
                        logger.warning(f"Job {job_id} 不在暂停状态，无法继续")
                        return False
                    
                    job_context = self._build_job_context_from_db(job)
                    if not job_context:
                        logger.error(f"无法构建Job {job_id} 的继续上下文")
                        return False
                    
                    # 修复：确保任务状态与锁状态一致
                    # 先检查是否有锁，如果有锁但状态不一致，需要清理
                    lock_info = await self.distributed_lock.get_job_lock_info(job_id)
                    if lock_info:
                        lock_instance_id = lock_info.get("instance_id")
                        if lock_instance_id != self.distributed_lock.instance_id:
                            logger.warning(f"Job {job_id} 被其他实例 {lock_instance_id} 锁定，尝试强制清理")
                            await self._force_cleanup_job_lock(job_id, "resume_other_instance_lock")
                    
                    # 检查是否可以直接分配
                    if await self._strict_concurrency_check():
                        success = await self._unified_job_assignment(job_context, "resume")
                        if not success:
                            # 分配失败，可能是锁被死实例持有，强制清理
                            logger.warning(f"Job {job_id} 继续分配失败，尝试清理死实例锁")
                            await self._force_cleanup_job_lock(job_id, "resume_failed")
                            # 再次尝试分配
                            success = await self._unified_job_assignment(job_context, "resume_after_cleanup")
                        return success
                    else:
                        # 写入数据库排队
                        update_job(session, job_id, 
                                 status=JobStatus.PENDING_RESUME.value,
                                 stage="queue", 
                                 stage_progress=0,
                                 stage_message="继续任务排队等待分配")
                        logger.info(f"继续Job {job_id} 已排队等待分配")
                        return True
                    
        except Exception as e:
            logger.error(f"继续job {job_id} 失败: {e}")
            return False

    async def restart_job(self, job_id: str) -> bool:
        """重启任务 - 使用统一分配入口"""
        try:
            with session_scope() as session:
                job = get_job(session, job_id)
                if not job:
                    logger.error(f"重启失败: Job {job_id} 不存在")
                    return False
                
                if job.status not in ["failed", "cancelled", "completed", "timeout", "paused"]:
                    logger.warning(f"Job {job_id} 状态为 {job.status}，不允许重启")
                    return False
                
                job_context = self._build_job_context_from_db(job)
                if not job_context:
                    logger.error(f"无法构建Job {job_id} 的重启上下文")
                    return False
                
                # 重置任务状态（重新开始）
                job_context.retry_count = 0
                job_context.current_stage = "init"
                job_context.stage_progress = 0
                
                # 修复：确保任务状态与锁状态一致
                # 先检查是否有锁，如果有锁但状态不一致，需要清理
                lock_info = await self.distributed_lock.get_job_lock_info(job_id)
                if lock_info:
                    lock_instance_id = lock_info.get("instance_id")
                    if lock_instance_id != self.distributed_lock.instance_id:
                        logger.warning(f"Job {job_id} 被其他实例 {lock_instance_id} 锁定，尝试强制清理")
                        await self._force_cleanup_job_lock(job_id, "restart_other_instance_lock")
                
                # 检查是否可以直接分配
                if await self._strict_concurrency_check():
                    success = await self._unified_job_assignment(job_context, "restart")
                    if not success:
                        # 分配失败，可能是锁被死实例持有，强制清理
                        logger.warning(f"Job {job_id} 重启分配失败，尝试清理死实例锁")
                        await self._force_cleanup_job_lock(job_id, "restart_failed")
                        # 再次尝试分配
                        success = await self._unified_job_assignment(job_context, "restart_after_cleanup")
                    return success
                else:
                    # 写入数据库排队
                    update_job(session, job_id, 
                             status=JobStatus.PENDING.value,
                             stage="queue", 
                             stage_progress=0,
                             stage_message="重启任务排队等待分配",
                             progress=0)  # 重置进度
                    logger.info(f"重启Job {job_id} 已排队等待分配")
                    return True
                    
        except Exception as e:
            logger.error(f"重启job {job_id} 失败: {e}")
            return False

    # ===== 任务操作的统一入口 =====

    async def _execute_job(self, job_context: JobContext):
        """执行job的核心逻辑"""
        job_id = job_context.job_id
        logger.info(f"开始执行job: {job_id}")
        
        try:
            # 检查暂停状态
            if job_context.pause_requested:
                logger.info(f"Job {job_id} 被暂停，移入暂停列表")
                self.paused_jobs[job_id] = job_context
                if job_id in self.active_jobs:
                    del self.active_jobs[job_id]
                return
            
            # 检查线程池状态
            logger.info(f"线程池状态检查 - Job {job_id}: 活跃线程数={self.executor._threads}, 最大线程数={self.executor._max_workers}")
            
            # 根据操作类型更新任务阶段状态，保持原有状态
            # 只有在真正需要重新开始的任务才重置为download
            current_stage = job_context.current_stage or "download"
            current_progress = job_context.stage_progress or 0
            
            # 如果stage是queue或scan，说明任务正在排队或扫描中，应该保持原有状态
            if current_stage not in ["queue", "scan"]:
                # 正常阶段，保持原有状态
                stage_message = f"任务开始执行，当前阶段: {current_stage}"
            else:
                # 排队或扫描状态，重置为download（这种情况应该很少见）
                current_stage = "download"
                current_progress = 0
                stage_message = "任务开始执行，下载阶段"
                logger.warning(f"Job {job_id} 从异常状态 {job_context.current_stage} 开始执行，重置为download")
            
            self.force_update_job_stage(job_id, current_stage, current_progress, stage_message)
            
            # 在新线程中运行任务
            logger.info(f"提交Job {job_id}到线程池执行")
            future = self.executor.submit(self._run_job_in_thread, job_context)
            logger.info(f"Job {job_id}已成功提交到线程池，Future对象: {future}")
            
            # 保存Future引用到JobContext
            job_context.future = future
            
            # 设置完成回调
            self._schedule_job_completion_handler(job_context, future)
            logger.info(f"Job {job_id}完成回调已设置")
            
            # 添加调试信息：确认任务已正确启动
            logger.info(f"Job {job_id} 执行流程启动完成，当前活跃任务数: {len(self.active_jobs)}")
            
        except Exception as e:
            logger.error(f"执行job {job_id} 时出错: {e}", exc_info=True)
            # 强制更新任务状态为失败
            self.force_update_job_stage(job_id, "failed", 0, f"任务执行启动失败: {str(e)}")
            await self._handle_job_failure(job_context, e)

    def _run_job_in_thread(self, job_context: JobContext):
        """在线程中运行job的实际逻辑"""
        job_id = job_context.job_id
        
        try:
            logger.info(f"在线程中开始执行job: {job_id}")
            
            # 检查是否已被请求取消
            if job_context.pause_requested:
                logger.info(f"Job {job_id} 在开始执行前已被取消")
                raise RuntimeError("任务已被取消")
            
            # 设置执行状态
            job_context.start_time = datetime.now()
            logger.info(f"Job {job_id} 执行开始时间: {job_context.start_time}")
            
            # 检查WikiGenerator是否存在
            if not hasattr(self, 'wiki_generator') or not self.wiki_generator:
                logger.error(f"Job {job_id} WikiGenerator未初始化!")
                raise RuntimeError("WikiGenerator未初始化")
            
            logger.info(f"Job {job_id} WikiGenerator检查通过，开始创建事件循环")
            
            # 再次检查取消状态
            if job_context.pause_requested:
                logger.info(f"Job {job_id} 在创建事件循环前已被取消")
                raise RuntimeError("任务已被取消")
            
            # 创建新的事件循环来运行异步的WikiGenerator.start方法
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            logger.info(f"Job {job_id} 事件循环已创建")
            
            try:
                logger.info(f"Job {job_id} 开始调用WikiGenerator.start方法")
                logger.info(f"Job {job_id} 参数: wiki_id={job_context.wiki_id}, repo_url={job_context.repo_url}, branch={job_context.branch}")
                
                # 最后一次检查取消状态
                if job_context.pause_requested:
                    logger.info(f"Job {job_id} 在开始WikiGenerator前已被取消")
                    raise RuntimeError("任务已被取消")
                
                logger.info(f"Job {job_id} 即将调用WikiGenerator.start，开始实际执行...")
                result = loop.run_until_complete(
                    self.wiki_generator.start(
                        job_id=job_context.job_id,
                        wiki_id=job_context.wiki_id,
                        repo_url=job_context.repo_url,
                        repo_type=job_context.repo_type,
                        branch=job_context.branch,
                        token=job_context.token,
                        language=job_context.language,
                        model_settings=job_context.model_settings,
                        comprehensive=job_context.comprehensive,
                        job_context=job_context,  # 传递job_context用于状态更新
                        sub_repos_str=job_context.sub_repos,
                        **job_context.kwargs
                    )
                )
                logger.info(f"Job {job_id} WikiGenerator.start方法执行完成，结果: {result}")
                
                # 确保任务执行完成后状态被正确更新
                if result and hasattr(result, 'get'):
                    # 如果返回结果包含状态信息，更新任务状态
                    if isinstance(result, dict):
                        final_stage = result.get('final_stage', 'completed')
                        final_progress = result.get('final_progress', 100)
                        self.force_update_job_stage(job_id, final_stage, final_progress, "任务执行完成")
                
            except Exception as e:
                logger.error(f"Job {job_id} WikiGenerator执行失败: {e}")
                # 强制更新任务状态为失败
                self.force_update_job_stage(job_id, "failed", 0, f"WikiGenerator执行失败: {str(e)}")
                raise  # 重新抛出异常，让上层处理
            finally:
                logger.info(f"Job {job_id} 关闭事件循环")
                loop.close()
            
            logger.info(f"Job {job_id} 在线程中执行完成")
            return result
            
        except Exception as e:
            # 检查是否是取消导致的异常
            if job_context.pause_requested:
                logger.info(f"Job {job_id} 因取消请求而终止")
                raise RuntimeError("任务已被取消")
            else:
                logger.error(f"Job {job_id} 在线程中执行失败: {e}", exc_info=True)
                raise e

    def _schedule_job_completion_handler(self, job_context: JobContext, future):
        """安排job完成处理器"""
        def completion_callback(f):
            logger.info(f"Job {job_context.job_id} 完成回调被触发，Future状态: cancelled={f.cancelled()}, done={f.done()}")
            
            # 创建异步任务处理完成事件，但要确保有事件循环
            try:
                loop = asyncio.get_running_loop()
                logger.debug(f"Job {job_context.job_id} 在现有事件循环中创建完成处理任务")
                asyncio.create_task(self._handle_job_completion(job_context, f))
            except RuntimeError:
                # 如果没有运行的事件循环，创建一个新的来处理
                logger.debug(f"Job {job_context.job_id} 创建新事件循环处理完成事件")
                asyncio.run(self._handle_job_completion(job_context, f))
        
        future.add_done_callback(completion_callback)
        logger.info(f"Job {job_context.job_id} 完成回调已设置到Future对象")

    async def _handle_job_completion(self, job_context: JobContext, future):
        """处理job完成"""
        job_id = job_context.job_id
        
        try:
            if future.cancelled():
                logger.info(f"Job {job_id} 的Future被取消")
                # 更新数据库状态为已取消
                self._safe_update_job_status(
                    job_context, 
                    JobStatus.CANCELLED.value, 
                    job_context.stage_progress or 0, 
                    "cancelled", 
                    0, 
                    "任务已取消"
                )
                # 更新全局状态
                await self.distributed_lock.update_global_job_state(
                    job_id, 
                    "cancelled",
                    {"cancel_time": datetime.now().isoformat()}
                )
                await self._cleanup_job_resources(job_context)
                    
                # 立即更新心跳，确保实例任务计数正确
                try:
                    await self.distributed_lock.update_heartbeat(len(self.active_jobs))
                    logger.debug(f"Job {job_id} 取消异常后心跳已更新，当前活跃任务数: {len(self.active_jobs)}")
                except Exception as e:
                    logger.warning(f"Job {job_id} 取消异常后更新心跳失败: {e}")
                return
            
            if future.exception():
                exception = future.exception()
                # 检查是否是取消导致的异常
                if job_context.pause_requested and "任务已被取消" in str(exception):
                    logger.info(f"Job {job_id} 因取消请求而结束")
                    # 更新数据库状态为已取消
                    self._safe_update_job_status(
                        job_context, 
                        JobStatus.CANCELLED.value, 
                        job_context.stage_progress or 0, 
                        "cancelled", 
                        0, 
                        "任务已取消"
                    )
                    # 更新全局状态
                    await self.distributed_lock.update_global_job_state(
                        job_id, 
                        "cancelled",
                        {"cancel_time": datetime.now().isoformat()}
                    )
                    await self._cleanup_job_resources(job_context)
                    
                    # 立即更新心跳，确保实例任务计数正确
                    try:
                        await self.distributed_lock.update_heartbeat(len(self.active_jobs))
                        logger.debug(f"Job {job_id} Future取消后心跳已更新，当前活跃任务数: {len(self.active_jobs)}")
                    except Exception as e:
                        logger.warning(f"Job {job_id} Future取消后更新心跳失败: {e}")
                    return
                else:
                    logger.error(f"Job {job_id} 执行失败: {exception}")
                    await self._handle_job_failure(job_context, exception)
                return
            
            # 任务成功完成
            result = future.result()
            logger.info(f"Job {job_id} 成功完成")
            
            # 更新状态为完成
            self._safe_update_job_status(
                job_context, 
                JobStatus.COMPLETED.value, 
                100, 
                "completed", 
                100, 
                "任务成功完成"
            )
            
            # 更新全局状态
            await self.distributed_lock.update_global_job_state(
                job_id, 
                "completed",
                {"completion_time": datetime.now().isoformat()}
            )
            
            # 清理资源
            await self._cleanup_job_resources(job_context)
            
            # 立即更新心跳，确保实例任务计数正确
            try:
                await self.distributed_lock.update_heartbeat(len(self.active_jobs))
                logger.debug(f"Job {job_id} 完成后心跳已更新，当前活跃任务数: {len(self.active_jobs)}")
            except Exception as e:
                logger.warning(f"Job {job_id} 完成后更新心跳失败: {e}")
            
            # 触发立即扫描
            await self._trigger_immediate_scan_after_completion()
            
            logger.info(f"Job {job_id} 完成处理流程结束，结果: {result}")
            
        except Exception as e:
            logger.error(f"处理job {job_id} 完成时出错: {e}")
            await self._handle_job_failure(job_context, e)

    async def _handle_job_failure(self, job_context: JobContext, error: Exception):
        """处理job失败"""
        job_id = job_context.job_id
        
        try:
            # 检查是否需要重试
            if job_context.retry_count < job_context.max_retries:
                job_context.retry_count += 1
                logger.info(f"Job {job_id} 失败，准备重试 ({job_context.retry_count}/{job_context.max_retries})")
                
                # 更新状态为重试中
                self._safe_update_job_status(
                    job_context, 
                    JobStatus.PENDING.value, 
                    job_context.stage_progress or 0, 
                    "retry", 
                    0, 
                    f"失败重试 ({job_context.retry_count}/{job_context.max_retries}): {str(error)[:100]}"
                )
                
                # 清理当前资源
                await self._cleanup_job_on_failure(job_context)
                
                # 重新提交到队列
                await asyncio.sleep(5)  # 等待5秒后重试
                await self.submit_job(job_context)
                
            else:
                # 超过重试次数，标记为失败
                logger.error(f"Job {job_id} 重试次数已用尽，标记为失败")
                
                self._safe_update_job_status(
                    job_context, 
                    JobStatus.FAILED.value, 
                    job_context.stage_progress or 0, 
                    "failed", 
                    0, 
                    f"执行失败: {str(error)[:200]}",
                    error_message=str(error)
                )
                
                # 更新全局状态
                await self.distributed_lock.update_global_job_state(
                    job_id, 
                    "failed",
                    {"error": str(error), "failure_time": datetime.now().isoformat()}
                )
                
                # 清理资源
                await self._cleanup_job_on_failure(job_context)
                
                # 立即更新心跳，确保实例任务计数正确
                try:
                    await self.distributed_lock.update_heartbeat(len(self.active_jobs))
                    logger.debug(f"Job {job_id} 失败后心跳已更新，当前活跃任务数: {len(self.active_jobs)}")
                except Exception as e:
                    logger.warning(f"Job {job_id} 失败后更新心跳失败: {e}")
                
                # 触发立即扫描
                await self._trigger_immediate_scan_after_completion()
                
        except Exception as e:
            logger.error(f"处理job {job_id} 失败时出错: {e}")
            await self._cleanup_job_on_failure(job_context)

    async def _cleanup_job_resources(self, job_context: JobContext):
        """清理job资源 - 增强版，确保锁数据与线程状态一致"""
        job_id = job_context.job_id
        
        try:
            # 1. 清理async_task引用
            if hasattr(job_context, 'async_task') and job_context.async_task:
                try:
                    # 如果异步任务还没完成，尝试取消
                    if not job_context.async_task.done():
                        cancelled = job_context.async_task.cancel()
                        logger.debug(f"Job {job_id} async_task取消结果: {cancelled}")
                    job_context.async_task = None
                except Exception as e:
                    logger.warning(f"清理Job {job_id} async_task时出错: {e}")
            
            # 2. 清理Future引用
            if job_context.future:
                try:
                    # 如果Future还没完成，尝试取消
                    if not job_context.future.done():
                        cancelled = job_context.future.cancel()
                        logger.debug(f"Job {job_id} Future取消结果: {cancelled}")
                    job_context.future = None
                except Exception as e:
                    logger.warning(f"清理Job {job_id} Future时出错: {e}")
            
            # 3. 清理线程引用
            if job_context.thread:
                job_context.thread = None
            
            # 4. 从活跃列表移除
            was_active = job_id in self.active_jobs
            if was_active:
                del self.active_jobs[job_id]
            
            # 5. 从暂停列表移除
            was_paused = job_id in self.paused_jobs
            if was_paused:
                del self.paused_jobs[job_id]
            
            # 6. 清理执行注册
            was_in_registry = False
            with self._global_status_lock:
                if job_id in self._execution_registry:
                    del self._execution_registry[job_id]
                    was_in_registry = True
                job_context.is_active_execution = False
            
            # 7. 更新实例任务计数（如果任务确实在本实例执行）
            if was_active or was_paused or was_in_registry:
                try:
                    await self._decrement_instance_job_count()
                    logger.debug(f"Job {job_id} 实例任务计数已递减")
                except Exception as e:
                    logger.warning(f"递减Job {job_id} 实例任务计数时出错: {e}")
            
            # 8. 释放分布式锁并清理锁持有数据
            try:
                await self.distributed_lock.release_job_lock(job_id)
                logger.debug(f"Job {job_id} 分布式锁已释放")
            except Exception as e:
                logger.warning(f"释放Job {job_id} 分布式锁时出错: {e}")
                # 如果正常释放失败，尝试强制释放
                try:
                    await self.distributed_lock.force_release_job_lock(job_id, "cleanup_job_resources")
                    logger.info(f"Job {job_id} 分布式锁已强制释放")
                except Exception as e2:
                    logger.error(f"强制释放Job {job_id} 分布式锁时出错: {e2}")
            
            # 9. 清理全局任务状态数据
            try:
                # 不直接设置为cancelled，因为这可能在其他地方已经设置了正确的状态
                # 只清理processing_instance_id，确保不再指向当前实例
                await self._cleanup_global_job_state_instance_reference(job_id)
                logger.debug(f"Job {job_id} 全局状态实例引用已清理")
            except Exception as e:
                logger.warning(f"清理Job {job_id} 全局状态实例引用时出错: {e}")

            # 10. 清理缓存的上下文数据
            self._evict_job_context(job_id)
            
            logger.debug(f"Job {job_id} 所有资源清理完成")
            
        except Exception as e:
            logger.error(f"清理job {job_id} 资源时出错: {e}")

    async def _decrement_instance_job_count(self):
        """递减实例任务计数"""
        try:
            if not self.distributed_lock.enable_distributed_lock:
                logger.debug("本地开发模式: 跳过实例任务计数递减")
                return
                
            with session_scope() as session:
                query = select(JobManagerInstance).where(
                    JobManagerInstance.instance_id == self.distributed_lock.instance_id
                )
                instance = session.exec(query).first()
                if instance and instance.current_jobs > 0:
                    instance.current_jobs -= 1
                    instance.updated_time = datetime.now()
                    session.add(instance)
                    session.commit()
                    logger.debug(f"实例 {self.distributed_lock.instance_id} 任务计数递减至: {instance.current_jobs}")
        except Exception as e:
            logger.error(f"递减实例任务计数失败: {e}")

    async def _cleanup_global_job_state_instance_reference(self, job_id: str):
        """清理全局任务状态中的实例引用"""
        try:
            if not self.distributed_lock.enable_distributed_lock:
                logger.debug(f"本地开发模式: 跳过清理全局状态实例引用 {job_id}")
                return
                
            with session_scope() as session:
                query = select(GlobalJobState).where(GlobalJobState.job_id == job_id)
                state = session.exec(query).first()
                if state and state.processing_instance_id == self.distributed_lock.instance_id:
                    # 只有当processing_instance_id指向当前实例时才清理
                    state.processing_instance_id = None
                    state.last_processing_instance = self.distributed_lock.instance_id
                    state.updated_time = datetime.now()
                    session.add(state)
                    session.commit()
                    logger.debug(f"任务 {job_id} 全局状态实例引用已清理")
        except Exception as e:
            logger.error(f"清理任务 {job_id} 全局状态实例引用失败: {e}")

    async def _cleanup_job_on_failure(self, job_context: JobContext):
        """失败时清理job资源"""
        try:
            await self._cleanup_job_resources(job_context)
        except Exception as e:
            logger.error(f"失败清理时出错: {e}")

    # ===== 任务监控和状态管理 =====

    async def _job_monitor(self):
        """job监控协程"""
        logger.info("Job监控器启动")
        
        while not self._shutdown:
            try:
                await self._check_job_timeouts()
                await self._sync_job_status()
                await asyncio.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                logger.error(f"Job监控器出错: {e}")
                await asyncio.sleep(60)
        
        logger.info("Job监控器停止")

    async def _check_job_timeouts(self):
        """检查job超时"""
        try:
            current_time = datetime.now()
            timeout_threshold = timedelta(minutes=self.job_timeout_minutes)
            
            timeout_jobs = []
            for job_id, job_context in list(self.active_jobs.items()):
                if job_context.start_time:
                    if current_time - job_context.start_time > timeout_threshold:
                        timeout_jobs.append(job_context)
            
            for job_context in timeout_jobs:
                logger.warning(f"Job {job_context.job_id} 超时，处理中...")
                await self._handle_job_timeout(job_context)
                
        except Exception as e:
            logger.error(f"检查job超时时出错: {e}")

    async def _handle_job_timeout(self, job_context: JobContext):
        """处理job超时"""
        job_id = job_context.job_id
        
        try:
            # 更新状态为超时
            self._safe_update_job_status(
                job_context, 
                JobStatus.TIMEOUT.value, 
                job_context.stage_progress or 0, 
                "timeout", 
                0, 
                f"任务执行超时 ({self.job_timeout_minutes}分钟)"
            )
            
            # 更新全局状态
            await self.distributed_lock.update_global_job_state(
                job_id, 
                "timeout",
                {"timeout_time": datetime.now().isoformat()}
            )
            
            # 取消线程执行
            if job_context.thread and job_context.thread.is_alive():
                # 注意：Python线程无法强制终止，只能设置标志
                job_context.pause_requested = True
            
            # 清理资源
            await self._cleanup_job_resources(job_context)
            
            # 触发立即扫描
            await self._trigger_immediate_scan_after_completion()
            
        except Exception as e:
            logger.error(f"处理job {job_id} 超时时出错: {e}")

    async def _sync_job_status(self):
        """同步job状态"""
        try:
            # 更新心跳时间
            current_time = datetime.now()
            for job_context in self.active_jobs.values():
                job_context.last_heartbeat = current_time
            
        except Exception as e:
            logger.error(f"同步job状态时出错: {e}")

    def _safe_update_job_status(self, job_context: JobContext, status: str, progress: int, 
                               stage: str, stage_progress: int, stage_message: str,
                               processed_files: int = None, total_files: int = None,
                               error_message: str = None) -> bool:
        """安全更新job状态"""
        job_id = job_context.job_id
        
        try:
            with session_scope() as session:
                update_data = {
                    'status': status,
                    'progress': progress,
                    'stage': stage,
                    'stage_progress': stage_progress,
                    'stage_message': self._truncate_message(stage_message)
                }
                
                if processed_files is not None:
                    update_data['processed_files'] = processed_files
                if total_files is not None:
                    update_data['total_files'] = total_files
                if error_message is not None:
                    update_data['error_message'] = self._truncate_message(error_message, 500)
                
                update_job(session, job_id, **update_data)
                
                # 更新job_context中的状态
                job_context.current_stage = stage
                job_context.stage_progress = stage_progress
                
                return True
                
        except Exception as e:
            logger.error(f"更新job {job_id} 状态失败: {e}")
            return False

    def _build_job_context_from_db(self, job: WikiJob) -> Optional[JobContext]:
        """从数据库job构建JobContext"""
        try:
            # 解析model_settings
            model_settings = {}
            if job.model_settings:
                try:
                    model_settings = json.loads(job.model_settings) if isinstance(job.model_settings, str) else job.model_settings
                except:
                    logger.warning(f"解析job {job.id} 的model_settings失败，使用默认值")
            
            # 确定恢复阶段和进度
            stage, stage_progress = self._determine_recovery_stage(job)
            
            job_context = JobContext(
                job_id=job.id,
                wiki_id=job.wiki_info_id,
                repo_url=job.repo_url,
                repo_type=getattr(job, 'repo_type', 'whaleDevCloud'),  # 添加repo_type，默认为whaleDevCloud
                branch=job.branch or "main",
                token=job.token,
                language=job.language,
                model_settings=model_settings,
                comprehensive=job.comprehensive,
                sub_repos=job.sub_repos,
                current_stage=stage,
                stage_progress=stage_progress,
                retry_count=getattr(job, 'retry_count', 0)
            )

            extra_kwargs: Dict[str, Any] = {}
            if job.job_type == 1:
                extra_kwargs["is_refresh"] = True

            if job.result:
                try:
                    raw_result = job.result if isinstance(job.result, dict) else json.loads(job.result)
                except Exception as parse_err:
                    logger.debug(f"解析job {job.id} 的result失败: {parse_err}")
                    raw_result = None
                if isinstance(raw_result, dict):
                    request_payload = raw_result.get("request") or raw_result.get("params")
                    if isinstance(request_payload, dict):
                        for key in (
                            "is_refresh",
                            "force_refresh",
                            "refresh_pages",
                            "sync_strategy",
                            "rebuild_structure",
                            "custom_instructions",
                        ):
                            if key in request_payload:
                                extra_kwargs[key] = request_payload[key]

            if extra_kwargs:
                job_context.kwargs.update(extra_kwargs)

            cached_payload = self._load_cached_job_context(job.id)
            if cached_payload and isinstance(cached_payload.get("kwargs"), dict):
                job_context.kwargs.update(cached_payload["kwargs"])

            # 延长缓存有效期，确保恢复时可以读取到最新指令
            self._cache_job_context(job_context)

            # 新增：设置文件进度相关字段，用于接管任务时的阶段推断
            if hasattr(job, 'total_files') and job.total_files is not None:
                job_context.total_files = job.total_files
            if hasattr(job, 'processed_files') and job.processed_files is not None:
                job_context.processed_files = job.processed_files
            
            logger.info(f"构建JobContext {job.id}: stage={stage}, stage_progress={stage_progress}, "
                       f"total_files={getattr(job_context, 'total_files', 'N/A')}, "
                       f"processed_files={getattr(job_context, 'processed_files', 'N/A')}")
            
            return job_context
            
        except Exception as e:
            logger.error(f"构建job {job.id} 上下文失败: {e}")
            return None

    def _determine_recovery_stage(self, job: WikiJob) -> tuple[str, int]:
        """确定恢复阶段和进度"""
        try:
            # 根据当前progress确定恢复阶段
            progress = job.progress or 0
            stage = job.stage or "init"
            stage_progress = job.stage_progress or 0
            
            # 修复：保持原有stage状态，不强制重置
            # 只有在真正需要重新开始的任务才重置为init
            # 接管任务时应该保持原有状态，让WikiGenerator自己判断执行起点
            
            # 简化：主要依赖stage字段，让WikiGenerator自己判断
            # 如果任务之前在处理中，尝试从进度推断阶段（仅在stage为init时）
            if job.status in ["processing", "resuming"] and progress > 0 and stage == "init":
                # 只有在阶段确实是init且进度足够时才推断
                if progress < 40:
                    stage = "download"
                    stage_progress = max(0, (progress - 20) * 100 // 20)
                elif progress < 60:
                    stage = "upload"
                    stage_progress = max(0, (progress - 40) * 100 // 20)
                elif progress < 80:
                    stage = "structure"
                    stage_progress = max(0, (progress - 60) * 100 // 20)
                else:
                    stage = "pages"
                    stage_progress = max(0, (progress - 80) * 100 // 20)
                
                logger.info(f"任务 {job.id} 根据整体进度推断阶段: {stage}, 阶段进度: {stage_progress}%")
            
            logger.info(f"任务 {job.id} 恢复阶段确定: {stage}, 阶段进度: {stage_progress}%")
            return stage, stage_progress
            
        except Exception as e:
            logger.error(f"确定job {job.id} 恢复阶段失败: {e}")
            return "init", 0

    # ===== 暂停和继续操作 =====

    async def pause_job(self, job_id: str) -> bool:
        """暂停任务"""
        try:
            if job_id in self.active_jobs:
                job_context = self.active_jobs[job_id]
                job_context.pause_requested = True
                if job_context.pause_event:
                    job_context.pause_event.clear()
                
                # 更新数据库状态
                self._safe_update_job_status(
                    job_context, 
                    JobStatus.PAUSED.value, 
                    job_context.stage_progress or 0, 
                    job_context.current_stage or "paused", 
                    job_context.stage_progress or 0, 
                    "任务已暂停"
                )
                
                logger.info(f"Job {job_id} 已暂停")
                return True
            else:
                logger.warning(f"Job {job_id} 不在活跃列表中，无法暂停")
                return False
                
        except Exception as e:
            logger.error(f"暂停job {job_id} 失败: {e}")
            return False

    async def cancel_job(self, job_id: str) -> bool:
        """取消任务"""
        try:
            # 检查是否在活跃或暂停列表中
            job_context = None
            if job_id in self.active_jobs:
                job_context = self.active_jobs[job_id]
            elif job_id in self.paused_jobs:
                job_context = self.paused_jobs[job_id]
            
            if job_context:
                logger.info(f"开始取消活跃任务 {job_id}")
                
                # 设置取消标志
                job_context.pause_requested = True
                # 标记为取消请求，供执行代码区分暂停与取消
                try:
                    setattr(job_context, 'cancel_requested', True)
                except Exception:
                    pass
                if job_context.pause_event:
                    job_context.pause_event.clear()
                
                # 尝试取消线程池中的Future任务
                future_cancelled = False
                if job_context.future and not job_context.future.done():
                    try:
                        future_cancelled = job_context.future.cancel()
                        if future_cancelled:
                            logger.info(f"Job {job_id} 的Future任务已成功取消")
                        else:
                            logger.warning(f"Job {job_id} 的Future任务无法取消，可能已开始执行")
                    except Exception as e:
                        logger.error(f"取消Job {job_id} 的Future任务时出错: {e}")
                
                # 如果Future无法取消（已在执行），等待一段时间让任务自然结束
                if not future_cancelled and job_context.future:
                    logger.info(f"Job {job_id} 正在执行中，等待任务响应取消信号...")
                    try:
                        # 等待最多5秒让任务响应取消信号
                        await asyncio.wait_for(
                            asyncio.get_event_loop().run_in_executor(None, job_context.future.result), 
                            timeout=5.0
                        )
                        logger.info(f"Job {job_id} 已响应取消信号并完成")
                    except asyncio.TimeoutError:
                        logger.warning(f"Job {job_id} 在5秒内未响应取消信号，强制标记为已取消")
                    except Exception as e:
                        logger.info(f"Job {job_id} 执行过程中出现异常（可能是正常的取消流程）: {e}")
                
                # 更新数据库状态
                self._safe_update_job_status(
                    job_context, 
                    JobStatus.CANCELLED.value, 
                    job_context.stage_progress or 0, 
                    "cancelled", 
                    0, 
                    "任务已取消"
                )
                
                # 更新全局状态
                await self.distributed_lock.update_global_job_state(
                    job_id, 
                    "cancelled",
                    {"cancel_time": datetime.now().isoformat()}
                )
                
                # 清理资源（包括释放锁）
                await self._cleanup_job_resources(job_context)
                
                # 立即更新心跳，确保实例任务计数正确
                try:
                    await self.distributed_lock.update_heartbeat(len(self.active_jobs))
                    logger.debug(f"Job {job_id} 取消后心跳已更新，当前活跃任务数: {len(self.active_jobs)}")
                except Exception as e:
                    logger.warning(f"Job {job_id} 取消后更新心跳失败: {e}")
                
                # 触发立即扫描
                await self._trigger_immediate_scan_after_completion()
                
                logger.info(f"Job {job_id} 已取消并清理完成")
                return True
            else:
                # 直接在数据库中更新状态
                with session_scope() as session:
                    update_job(session, job_id, 
                             status=JobStatus.CANCELLED.value,
                             stage="cancelled",
                             stage_message="任务已取消")
                
                # 确保清理分布式锁
                try:
                    await self.distributed_lock.release_job_lock(job_id)
                    await self.distributed_lock.update_global_job_state(
                        job_id, 
                        "cancelled",
                        {"cancel_time": datetime.now().isoformat()}
                    )
                except Exception as e:
                    logger.warning(f"清理Job {job_id} 的分布式锁时出错: {e}")
                
                logger.info(f"Job {job_id} 已在数据库中取消")
                return True
                
        except Exception as e:
            logger.error(f"取消job {job_id} 失败: {e}")
            return False

    async def cancel_jobs_by_wiki(self, wiki_id: str) -> Dict[str, List[str]]:
        """
        根据Wiki标识批量取消相关任务，确保在删除Wiki前释放执行资源。
        
        参数:
            wiki_id: Wiki唯一标识
        
        返回:
            Dict[str, List[str]]: 包含已成功取消与取消失败的任务ID列表
        """
        # 使用集合去重，避免同一任务重复处理
        target_job_ids: Set[str] = set()
        cancelled_jobs: List[str] = []
        failed_jobs: List[str] = []

        # 1. 收集内存中活跃与暂停的任务
        for job_context in list(self.active_jobs.values()):
            if job_context.wiki_id == wiki_id:
                target_job_ids.add(str(job_context.job_id))
        for job_context in list(self.paused_jobs.values()):
            if job_context.wiki_id == wiki_id:
                target_job_ids.add(str(job_context.job_id))

        # 2. 查询数据库中仍处于待执行或执行中的任务，避免遗漏排队任务
        try:
            with session_scope() as session:
                statement = select(WikiJob.id).where(
                    WikiJob.wiki_info_id == wiki_id,
                    WikiJob.status.in_([
                        JobStatus.PENDING.value,
                        JobStatus.PENDING_RESUME.value,
                        JobStatus.PROCESSING.value,
                        JobStatus.RESUMING.value,
                        JobStatus.PAUSED.value,
                    ])
                )
                db_job_ids = session.exec(statement).scalars().all()
                for job_id in db_job_ids:
                    target_job_ids.add(str(job_id))
        except Exception as e:
            logger.error(f"查询Wiki {wiki_id} 关联任务失败: {e}")

        # 3. 按顺序取消所有关联任务
        for job_id in target_job_ids:
            try:
                success = await self.cancel_job(job_id)
                if success:
                    cancelled_jobs.append(job_id)
                else:
                    failed_jobs.append(job_id)
            except Exception as e:
                logger.error(f"取消Wiki {wiki_id} 任务 {job_id} 时出错: {e}")
                failed_jobs.append(job_id)

        return {"cancelled": cancelled_jobs, "failed": failed_jobs}

    # ===== 严格的实例管理器 =====

    async def _strict_instance_manager_worker(self):
        """
        严格的实例管理器工作协程
        
        改进：
        1. 更频繁的心跳检测
        2. 严格的超时清理
        3. 主机下线逻辑更紧
        """
        logger.info("严格实例管理器启动")
        
        while not self._shutdown:
            try:
                current_time = time.time()
                
                # 每30秒执行一次严格检查（提高频率）
                if current_time - self._last_instance_check > 30:
                    await self._strict_check_and_cleanup_dead_instances()
                    self._last_instance_check = current_time
                
                await asyncio.sleep(10)  # 10秒检查一次（提高频率）
                
            except Exception as e:
                logger.error(f"严格实例管理器出错: {e}")
                await asyncio.sleep(15)
        
        logger.info("严格实例管理器已停止")

    async def _strict_check_and_cleanup_dead_instances(self):
        """
        严格的死实例检查和清理
        
        改进：
        1. 心跳超时时间更短
        2. 只有leader实例执行清理
        3. 区分本机和远程实例
        """
        try:
            # 获取配置的心跳超时阈值
            config = get_wiki_jobs_config()
            heartbeat_timeout_minutes = config.get('heartbeat_timeout_minutes', 3)  # 读取配置文件中的心跳超时时间
            
            logger.debug(f"开始严格检查死实例，心跳超时阈值: {heartbeat_timeout_minutes}分钟")
            
            # 检查是否是leader实例
            current_manager = await self.distributed_lock.get_current_manager_instance()
            is_leader = (current_manager == self.distributed_lock.instance_id)
            
            if not is_leader:
                logger.debug(f"非leader实例({self.distributed_lock.instance_id})，跳过死实例检查，当前leader: {current_manager}")
                return
            
            logger.info(f"leader实例({self.distributed_lock.instance_id})执行严格死实例清理")
            
            # 调用分布式锁服务的清理方法
            await self.distributed_lock.cleanup_crashed_instances()
            
            # 额外的孤儿任务接管检查（带并发控制）
            await self.check_and_adopt_reassignable_jobs_with_concurrency()
            
            logger.debug("严格死实例检查完成")
            
        except Exception as e:
            logger.error(f"严格检查死实例时出错: {e}")

    # ===== 任务完成后的自动扫描触发 =====

    async def _trigger_immediate_scan_after_completion(self):
        """
        任务完成后触发立即队列扫描
        
        在以下情况调用：
        1. 任务正常完成
        2. 任务取消
        3. 任务失败
        """
        try:
            if len(self.active_jobs) < self.max_concurrent_jobs:
                logger.debug("任务完成，设置立即扫描事件")
                self._job_completed_event.set()  # 设置事件，触发扫描器立即扫描
        except Exception as e:
            logger.error(f"触发立即扫描失败: {e}")

    # ===== 优化的心跳工作器 =====

    async def _heartbeat_worker(self):
        """改进的心跳工作协程 - 增加一致性检查"""
        logger.info("改进心跳worker启动")
        
        heartbeat_counter = 0
        
        while not self._shutdown:
            try:
                # 1. 获取实际的锁数量（最准确的数据）
                actual_locks_count = await self._get_actual_locks_count()
                
                # 2. 检查内存中的活跃任务数
                memory_active_count = len(self.active_jobs)
                
                # 3. 数据一致性检查 - 使用配置选项控制检查频率和严格程度
                consistency_diff = abs(actual_locks_count - memory_active_count)
                if consistency_diff > 0:
                    if consistency_diff >= self.consistency_check_threshold:  # 使用配置的阈值
                        logger.warning(f"检测到显著数据不一致: 内存活跃任务({memory_active_count}) ≠ 实际锁数({actual_locks_count})")
                        
                        # 根据配置决定是否执行一致性修复
                        if self.consistency_check_strict:
                            logger.info("启用严格一致性检查，开始修复数据不一致")
                            await self._fix_consistency_mismatch(actual_locks_count, memory_active_count)
                            # 重新获取修复后的锁数量
                            actual_locks_count = await self._get_actual_locks_count()
                        else:
                            logger.info("一致性检查为非严格模式，仅记录警告，不执行修复")
                    else:
                        logger.debug(f"检测到轻微数据不一致: 内存({memory_active_count}) vs 锁数({actual_locks_count})，差异较小暂不修复")
                
                # 4. 使用实际锁数量更新心跳（最准确）
                await self.distributed_lock.update_heartbeat(actual_locks_count)
                heartbeat_counter += 1
                
                # 5. 降低检查频率，每2次心跳检查一次（1分钟）
                if heartbeat_counter % 2 == 0:
                    await self.check_and_adopt_reassignable_jobs_with_concurrency()
                
                # 6. 定期执行更深度的一致性检查
                if heartbeat_counter % 5 == 0:  # 每5次心跳（2.5分钟）
                    await self._deep_consistency_check()
                
                # 7. 每10次心跳记录一次状态日志（5分钟）
                if heartbeat_counter % 10 == 0:
                    current_manager = await self.distributed_lock.get_current_manager_instance()
                    is_current_manager = (current_manager == self.distributed_lock.instance_id)
                    manager_status = "leader实例" if is_current_manager else f"工作实例(leader: {current_manager})"
                    logger.info(f"实例 {self.distributed_lock.instance_id} 心跳正常，内存任务数: {memory_active_count}, 实际锁数: {actual_locks_count}, 状态: {manager_status}")
                    
                    # 执行状态一致性检查
                    try:
                        debug_info = self.debug_job_status_consistency()
                        if debug_info["issues"]:
                            logger.warning(f"发现任务状态不一致问题: {debug_info['issues']}")
                    except Exception as debug_error:
                        logger.error(f"状态一致性检查出错: {debug_error}")
                
                # 心跳间隔30秒
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.error(f"改进心跳worker出错: {e}")
                await asyncio.sleep(30)
        
        logger.info("改进心跳worker停止")

    async def _get_actual_locks_count(self) -> int:
        """获取当前实例的实际锁数量"""
        try:
            with session_scope() as session:
                lock_query = select(JobLock).where(
                    JobLock.instance_id == self.distributed_lock.instance_id
                )
                locks = session.exec(lock_query).all()
                return len(locks)
        except Exception as e:
            logger.error(f"获取实际锁数量失败: {e}")
            return len(self.active_jobs)  # 回退到内存计数

    async def _fix_consistency_mismatch(self, actual_locks: int, memory_count: int):
        """修复一致性不匹配问题 - 带冷却期和严格验证的版本"""
        try:
            # 冷却期检查 - 防止频繁清理导致循环
            current_time = datetime.now()
            if self._last_zombie_cleanup:
                time_since_last = (current_time - self._last_zombie_cleanup).total_seconds()
                if time_since_last < self._zombie_cleanup_cooldown:
                    logger.debug(f"僵尸锁清理冷却期内，还需等待 {self._zombie_cleanup_cooldown - time_since_last:.0f}秒")
                    return
            
            logger.info(f"开始一致性检查: 实际锁数={actual_locks}, 内存任务数={memory_count}")
            
            if actual_locks > memory_count:
                # 情况1: 有锁但内存中没有对应的活跃任务（僵尸锁）
                excess_count = actual_locks - memory_count
                logger.warning(f"检测到{excess_count}个可能的僵尸锁，开始严格验证")
                
                # 获取所有该实例的锁并进行严格验证
                with session_scope() as session:
                    lock_query = select(JobLock).where(
                        JobLock.instance_id == self.distributed_lock.instance_id
                    )
                    locks = session.exec(lock_query).all()
                    
                    confirmed_zombie_locks = []
                    for lock in locks:
                        if lock.job_id not in self.active_jobs and lock.job_id not in self.paused_jobs:
                            # 进一步验证：检查任务是否在数据库中存在且状态合理
                            job_query = select(WikiJob).where(WikiJob.id == lock.job_id)
                            job = session.exec(job_query).first()
                            
                            if job and job.status == "processing":
                                # 这可能不是僵尸锁，而是任务正在被其他组件处理
                                logger.debug(f"锁 {lock.job_id} 对应任务状态为processing，可能正在被处理，跳过清理")
                                continue
                            elif not job:
                                # 任务在数据库中不存在，这是真正的僵尸锁
                                logger.warning(f"锁 {lock.job_id} 对应任务在数据库中不存在，确认为僵尸锁")
                                confirmed_zombie_locks.append(lock)
                            elif job.status in ["completed", "failed", "cancelled"]:
                                # 任务已完成但锁未释放，这是僵尸锁
                                logger.warning(f"锁 {lock.job_id} 对应任务已完成({job.status})，确认为僵尸锁")
                                confirmed_zombie_locks.append(lock)
                            else:
                                logger.debug(f"锁 {lock.job_id} 对应任务状态为{job.status}，暂不清理")
                    
                    # 只清理确认的僵尸锁
                    if confirmed_zombie_locks:
                        logger.warning(f"确认清理 {len(confirmed_zombie_locks)} 个真正的僵尸锁")
                        
                        for lock in confirmed_zombie_locks:
                            logger.warning(f"清理确认的僵尸锁: {lock.job_id}")
                            await self.distributed_lock._graceful_release_lock(session, lock)
                        
                        session.commit()
                        self._last_zombie_cleanup = current_time  # 记录清理时间
                        logger.info(f"清理了{len(confirmed_zombie_locks)}个确认的僵尸锁，下次清理时间: {(current_time + timedelta(seconds=self._zombie_cleanup_cooldown)).strftime('%H:%M:%S')}")
                    else:
                        logger.debug("经过严格验证，未发现需要清理的僵尸锁")
            
            elif memory_count > actual_locks:
                # 情况2: 内存中有任务但没有对应的锁（需要谨慎处理）
                logger.warning(f"检测到{memory_count - actual_locks}个内存任务没有对应的锁，开始谨慎验证")
                
                # 检查哪些内存任务没有对应的锁，但需要谨慎处理
                orphan_jobs = []
                with session_scope() as session:
                    for job_id in self.active_jobs:
                        # 首先检查任务在数据库中的状态
                        job_query = select(WikiJob).where(WikiJob.id == job_id)
                        job = session.exec(job_query).first()
                        
                        if not job:
                            # 任务在数据库中不存在，这是真正的孤儿任务
                            logger.warning(f"发现真正的孤儿任务（数据库不存在）: {job_id}")
                            orphan_jobs.append(job_id)
                            continue
                        
                        # 检查任务状态，只有特定状态的任务才可能是真正的孤儿任务
                        if job.status in ["pending", "pending_resume"]:
                            # 排队中的任务本来就不应该有锁，这是正常的！
                            logger.debug(f"任务 {job_id} 状态为 {job.status}，正在排队中，不应该有锁，跳过清理")
                            continue
                        elif job.status in ["completed", "failed", "cancelled", "timeout"]:
                            # 已完成的任务不应该在内存中，这是真正的孤儿任务
                            logger.warning(f"发现真正的孤儿任务（状态为{job.status}）: {job_id}")
                            orphan_jobs.append(job_id)
                            continue
                        elif job.status == "processing":
                            # 处理中的任务没有锁，需要进一步检查
                            lock_query = select(JobLock).where(JobLock.job_id == job_id)
                            lock = session.exec(lock_query).first()
                            if not lock:
                                # 检查是否是最近分配的任务（给一定的宽限期）
                                if job.updated_time:
                                    time_since_update = (current_time - job.updated_time).total_seconds()
                                    if time_since_update < self.consistency_check_grace_period:  # 使用配置的宽限期
                                        logger.debug(f"任务 {job_id} 最近更新过({time_since_update:.0f}秒前)，可能是正在分配中，跳过清理")
                                        continue
                                    else:
                                        logger.warning(f"任务 {job_id} 长时间无锁且状态为processing，可能是真正的孤儿任务")
                                        orphan_jobs.append(job_id)
                                else:
                                    logger.warning(f"任务 {job_id} 无更新时间且无锁，标记为孤儿任务")
                                    orphan_jobs.append(job_id)
                            else:
                                logger.debug(f"任务 {job_id} 有锁，状态正常")
                        else:
                            # 其他状态的任务，暂时跳过清理
                            logger.debug(f"任务 {job_id} 状态为 {job.status}，暂时跳过清理")
                            continue
                
                # 只清理确认的孤儿任务
                if orphan_jobs:
                    logger.warning(f"确认清理 {len(orphan_jobs)} 个真正的孤儿任务")
                    for job_id in orphan_jobs:
                        logger.warning(f"清理确认的孤儿任务: {job_id}")
                        await self._safe_cleanup_orphan_job(job_id, "confirmed_orphan")
                    logger.info(f"清理了{len(orphan_jobs)}个确认的孤儿任务")
                else:
                    logger.debug("经过谨慎验证，未发现需要清理的孤儿任务")
            
            else:
                logger.debug("数据一致性检查通过，无需修复")
        
        except Exception as e:
            logger.error(f"修复一致性不匹配失败: {e}")

    async def _safe_cleanup_orphan_job(self, job_id: str, reason: str):
        """
        安全清理孤儿任务 - 解决线程未正确释放的问题
        
        该方法确保：
        1. 正确停止正在运行的线程
        2. 取消Future任务
        3. 设置暂停标志
        4. 清理内存记录
        5. 更新数据库状态
        6. 清理全局状态
        
        Args:
            job_id: 任务ID
            reason: 清理原因
        """
        logger.warning(f"开始安全清理孤儿任务 {job_id}，原因: {reason}")
        
        try:
            job_context = None
            was_active = False
            was_paused = False
            
            # 1. 获取任务上下文并设置停止标志
            if job_id in self.active_jobs:
                job_context = self.active_jobs[job_id]
                was_active = True
                
                # 设置暂停请求标志，让正在执行的代码能感知到停止信号
                job_context.pause_requested = True
                if hasattr(job_context, 'pause_event') and job_context.pause_event:
                    job_context.pause_event.clear()
                
                logger.info(f"孤儿任务 {job_id} 暂停标志已设置")
            
            elif job_id in self.paused_jobs:
                job_context = self.paused_jobs[job_id]
                was_paused = True
            
            # 2. 停止异步任务（关键修复）
            if job_context:
                # 取消异步任务（如果存在）
                if hasattr(job_context, 'async_task') and job_context.async_task:
                    try:
                        if not job_context.async_task.done():
                            job_context.async_task.cancel()
                            logger.info(f"孤儿任务 {job_id} 异步任务已取消")
                            
                            # 等待任务真正结束（最多等待5秒）
                            try:
                                await asyncio.wait_for(job_context.async_task, timeout=5.0)
                            except (asyncio.TimeoutError, asyncio.CancelledError):
                                logger.info(f"孤儿任务 {job_id} 异步任务取消完成")
                        else:
                            logger.debug(f"孤儿任务 {job_id} 异步任务已完成")
                    except Exception as e:
                        logger.warning(f"取消孤儿任务 {job_id} 异步任务时出错: {e}")
                
                # 取消线程池中的Future（如果存在）
                if hasattr(job_context, 'future') and job_context.future:
                    try:
                        if not job_context.future.done():
                            cancelled = job_context.future.cancel()
                            if cancelled:
                                logger.info(f"孤儿任务 {job_id} Future任务已取消")
                            else:
                                logger.warning(f"孤儿任务 {job_id} Future任务无法取消，可能已在执行")
                                # 等待任务自然结束
                                try:
                                    await asyncio.wait_for(
                                        asyncio.get_event_loop().run_in_executor(None, job_context.future.result),
                                        timeout=3.0
                                    )
                                    logger.info(f"孤儿任务 {job_id} Future任务已完成")
                                except asyncio.TimeoutError:
                                    logger.warning(f"孤儿任务 {job_id} Future任务未在3秒内完成，强制继续清理")
                                except Exception as future_error:
                                    logger.info(f"孤儿任务 {job_id} Future执行出现异常（正常清理流程）: {future_error}")
                        job_context.future = None
                    except Exception as e:
                        logger.warning(f"处理孤儿任务 {job_id} Future时出错: {e}")
                
                # 清理线程引用
                if hasattr(job_context, 'thread'):
                    job_context.thread = None
                
                # 标记执行状态为非活跃
                job_context.is_active_execution = False
            
            # 3. 从内存字典中移除
            if was_active and job_id in self.active_jobs:
                del self.active_jobs[job_id]
                logger.debug(f"孤儿任务 {job_id} 已从活跃列表移除")
            
            if was_paused and job_id in self.paused_jobs:
                del self.paused_jobs[job_id]
                logger.debug(f"孤儿任务 {job_id} 已从暂停列表移除")
            
            # 4. 清理执行注册
            with self._global_status_lock:
                if job_id in self._execution_registry:
                    del self._execution_registry[job_id]
                    logger.debug(f"孤儿任务 {job_id} 执行注册已清理")
            
            # 5. 更新数据库状态为失败（因为异常清理）
            try:
                with session_scope() as session:
                    update_job(session, job_id, 
                             status=JobStatus.FAILED.value,
                             stage="orphan_cleanup",
                             stage_message=f"孤儿任务清理: {reason}",
                             error_message=f"任务因{reason}被清理，可能是系统异常导致")
                    
                    logger.info(f"孤儿任务 {job_id} 数据库状态已更新为失败")
            except Exception as e:
                logger.warning(f"更新孤儿任务 {job_id} 数据库状态失败: {e}")
            
            # 6. 清理全局状态
            try:
                await self.distributed_lock.update_global_job_state(
                    job_id,
                    "failed",
                    {
                        "orphan_cleanup_reason": reason,
                        "cleanup_time": datetime.now().isoformat(),
                        "cleanup_instance": self.distributed_lock.instance_id
                    }
                )
                logger.debug(f"孤儿任务 {job_id} 全局状态已清理")
            except Exception as e:
                logger.warning(f"清理孤儿任务 {job_id} 全局状态失败: {e}")
            
            # 7. 更新心跳
            try:
                await self.distributed_lock.update_heartbeat(len(self.active_jobs))
                logger.debug(f"孤儿任务 {job_id} 清理后心跳已更新，当前活跃任务数: {len(self.active_jobs)}")
            except Exception as e:
                logger.warning(f"更新心跳失败: {e}")
            
            logger.info(f"孤儿任务 {job_id} 安全清理完成（原因: {reason}）")
            
        except Exception as e:
            logger.error(f"安全清理孤儿任务 {job_id} 失败: {e}")
            
            # 最后的应急清理：至少确保内存状态清理
            try:
                if job_id in self.active_jobs:
                    del self.active_jobs[job_id]
                if job_id in self.paused_jobs:
                    del self.paused_jobs[job_id]
                with self._global_status_lock:
                    if job_id in self._execution_registry:
                        del self._execution_registry[job_id]
                logger.info(f"孤儿任务 {job_id} 应急清理完成")
            except Exception as emergency_error:
                logger.error(f"孤儿任务 {job_id} 应急清理失败: {emergency_error}")

    async def _deep_consistency_check(self):
        """深度一致性检查"""
        try:
            logger.debug("执行深度一致性检查")
            
            # 检查任务状态与锁状态的一致性
            with session_scope() as session:
                # 获取该实例的所有锁
                lock_query = select(JobLock).where(
                    JobLock.instance_id == self.distributed_lock.instance_id
                )
                locks = session.exec(lock_query).all()
                
                inconsistent_count = 0
                
                for lock in locks:
                    # 检查对应的任务状态
                    job_query = select(WikiJob).where(WikiJob.id == lock.job_id)
                    job = session.exec(job_query).first()
                    
                    if job and job.status == "pending":
                        # 发现僵尸锁
                        logger.warning(f"深度检查发现僵尸锁: {lock.job_id}")
                        await self.distributed_lock._graceful_release_lock(session, lock)
                        inconsistent_count += 1
                
                if inconsistent_count > 0:
                    session.commit()
                    logger.info(f"深度检查修复了{inconsistent_count}个不一致问题")
        
        except Exception as e:
            logger.error(f"深度一致性检查失败: {e}")

    # ===== 优化的故障恢复逻辑 =====

    async def _startup_comprehensive_cleanup(self):
        """
        启动前全面清理：清理死实例、孤儿锁和孤儿全局状态
        
        这个方法在实例注册之前调用，确保数据库状态的清洁
        """
        logger.info("开始启动前全面清理...")
        
        try:
            with session_scope() as session:
                # 1. 清理明确已停止的实例
                stopped_instances_query = select(JobManagerInstance).where(
                    JobManagerInstance.status == "stopped"
                )
                stopped_instances = session.exec(stopped_instances_query).all()
                
                for instance in stopped_instances:
                    logger.info(f"清理已停止的实例: {instance.instance_id}")
                    await self.distributed_lock._cleanup_dead_instance(session, instance, "启动前清理-已停止")
                
                # 2. 严格清理心跳超时的实例
                current_time = datetime.now()
                config = get_wiki_jobs_config()
                heartbeat_timeout_minutes = config.get('heartbeat_timeout_minutes', 3)  # 读取配置文件中的心跳超时时间
                heartbeat_timeout = current_time - timedelta(minutes=heartbeat_timeout_minutes)
                
                timeout_instances_query = select(JobManagerInstance).where(
                    and_(
                        JobManagerInstance.status == "active",
                        or_(
                            JobManagerInstance.last_heartbeat.is_(None),
                            JobManagerInstance.last_heartbeat < heartbeat_timeout
                        )
                    )
                )
                timeout_instances = session.exec(timeout_instances_query).all()
                
                for instance in timeout_instances:
                    # 检查是否是本机进程
                    is_local_process = (instance.hostname == socket.gethostname())
                    
                    if is_local_process:
                        # 本机进程，检查是否真的已死
                        try:
                            if self.distributed_lock._is_process_really_alive(instance.pid):
                                logger.warning(f"本机实例 {instance.instance_id} 心跳超时但进程存活，跳过清理")
                                continue
                        except Exception as e:
                            logger.warning(f"检查进程存活状态失败: {e}")
                    
                    logger.info(f"清理心跳超时的实例: {instance.instance_id} (超时: {heartbeat_timeout})")
                    await self.distributed_lock._cleanup_dead_instance(session, instance, "启动前清理-心跳超时")

                # 3. 新增：基于锁活跃度的死锁检测和清理
                dead_locks_cleaned = await self._cleanup_stale_locks(session, current_time)
                
                # 4. 新增：清理孤儿processing任务（processing状态但无锁）
                orphan_processing_cleaned = await self._cleanup_orphan_processing_jobs(session)
                
                # 5. 清理孤儿锁（持有者不存在的锁）
                orphan_locks_cleaned = await self.distributed_lock._cleanup_orphaned_locks_in_session(session)
                
                # 6. 清理孤儿全局状态（关联实例不存在的processing状态）
                orphan_states_cleaned = await self._cleanup_orphan_global_states(session)
                
                # 提交所有清理操作
                session.commit()
                
                total_stopped = len(stopped_instances)
                total_timeout = len(timeout_instances)
                
                logger.info(f"启动前清理完成: {total_stopped}个已停止实例, {total_timeout}个超时实例, "
                          f"{dead_locks_cleaned}个死锁, {orphan_processing_cleaned}个孤儿processing任务, "
                          f"{orphan_locks_cleaned}个孤儿锁, {orphan_states_cleaned}个孤儿全局状态")
                
                # 7. 额外等待，确保清理操作完全生效
                await asyncio.sleep(1)
                
        except Exception as e:
            logger.error(f"启动前全面清理失败: {e}")
            # 不抛出异常，允许继续启动
    
    async def _cleanup_orphan_processing_jobs(self, session) -> int:
        """
        清理孤儿processing任务：处理状态为processing但没有锁的任务
        
        这些任务通常是由于实例异常退出或锁丢失导致的
        """
        try:
            current_time = datetime.now()
            
            # 查找processing状态但没有对应锁的任务
            orphan_processing_sql = """
            SELECT wj.*
            FROM wiki_job wj
            LEFT JOIN job_lock jl ON wj.id = jl.job_id
            WHERE wj.status = 'processing' 
            AND jl.job_id IS NULL
            """
            
            result = session.exec(text(orphan_processing_sql))
            orphan_jobs = result.all()
            
            if not orphan_jobs:
                return 0
            
            orphan_count = 0
            
            for job in orphan_jobs:
                try:
                    job_id = job.id
                    logger.warning(f"发现孤儿processing任务: {job_id}, 最后更新: {job.updated_time}")
                    
                    # 检查任务是否长时间未更新（超过1小时）
                    if job.updated_time and (current_time - job.updated_time).total_seconds() > 3600:
                        # 长时间未更新，重置为pending状态等待重新分配
                        session.execute(
                            text("UPDATE wiki_job SET status = 'pending', stage = 'queue', stage_message = '孤儿processing任务已重置，等待重新分配', updated_time = NOW() WHERE id = :job_id"),
                            {"job_id": job_id}
                        )
                        
                        # 更新全局状态为可重新分配
                        session.execute(
                            text("UPDATE global_job_state SET global_status = 'reassignable', processing_instance_id = NULL, updated_time = NOW() WHERE job_id = :job_id"),
                            {"job_id": job_id}
                        )
                        
                        orphan_count += 1
                        logger.info(f"孤儿processing任务 {job_id} 已重置为pending状态")
                    else:
                        # 最近更新过，可能正在被其他实例处理，暂时保留
                        logger.debug(f"孤儿processing任务 {job_id} 最近更新过，暂时保留")
                    
                except Exception as job_error:
                    logger.error(f"清理孤儿processing任务 {job.id} 失败: {job_error}")
                    continue
            
            logger.info(f"清理了 {orphan_count} 个孤儿processing任务")
            return orphan_count
            
        except Exception as e:
            logger.error(f"清理孤儿processing任务失败: {e}")
            return 0

    async def _cleanup_stale_locks(self, session, current_time: datetime) -> int:
        """
        清理死锁：检查长时间无活动或已过期的锁
        
        检查条件：
        1. 锁已过期（expires_at < 当前时间）
        2. 锁获取时间过长（acquired_time 超过6小时未完成）
        3. 锁更新时间过长（updated_time 超过1小时未更新）
        """
        try:
            # 设置各种超时阈值
            expired_threshold = current_time  # 已过期
            acquired_timeout = current_time - timedelta(hours=6)  # 获取超过6小时
            updated_timeout = current_time - timedelta(hours=1)  # 1小时未更新
            
            # 查询所有可能的死锁
            stale_locks_query = select(JobLock).where(
                or_(
                    # 条件1: 锁已过期
                    JobLock.expires_at < expired_threshold,
                    # 条件2: 获取时间过长
                    JobLock.acquired_time < acquired_timeout,
                    # 条件3: 更新时间过长
                    JobLock.updated_time < updated_timeout
                )
            )
            
            stale_locks = session.exec(stale_locks_query).all()
            
            if not stale_locks:
                return 0
            
            dead_locks_cleaned = 0
            
            for lock in stale_locks:
                try:
                    # 判断死锁原因
                    reasons = []
                    if lock.expires_at < expired_threshold:
                        reasons.append("已过期")
                    if lock.acquired_time < acquired_timeout:
                        reasons.append("获取时间过长")
                    if lock.updated_time < updated_timeout:
                        reasons.append("长时间未更新")
                    
                    reason_str = ", ".join(reasons)
                    
                    logger.warning(f"发现死锁: job_id={lock.job_id}, instance_id={lock.instance_id}, "
                                 f"原因: {reason_str}, 获取时间: {lock.acquired_time}, "
                                 f"过期时间: {lock.expires_at}, 更新时间: {lock.updated_time}")
                    
                    # 使用优雅释放方法（会自动更新全局状态）
                    await self.distributed_lock._graceful_release_lock(session, lock)
                    dead_locks_cleaned += 1
                    
                    # 额外更新元数据以记录清理原因
                    try:
                        global_job_query = select(GlobalJobState).where(
                            GlobalJobState.job_id == lock.job_id
                        )
                        global_job = session.exec(global_job_query).first()
                        
                        if global_job and global_job.job_metadata:
                            try:
                                metadata = json.loads(global_job.job_metadata)
                                metadata.update({
                                    "stale_lock_cleanup_reason": reason_str,
                                    "stale_lock_cleanup_time": current_time.isoformat(),
                                    "original_stale_instance": lock.instance_id
                                })
                                global_job.job_metadata = json.dumps(metadata)
                                session.add(global_job)
                            except Exception as metadata_error:
                                logger.warning(f"更新死锁清理元数据失败: {metadata_error}")
                    except Exception as update_error:
                        logger.warning(f"更新死锁清理信息失败: {update_error}")
                    
                except Exception as lock_error:
                    logger.error(f"清理死锁 {lock.job_id} 失败: {lock_error}")
                    continue
            
            logger.info(f"清理了 {dead_locks_cleaned} 个死锁")
            return dead_locks_cleaned
            
        except Exception as e:
            logger.error(f"清理死锁时出错: {e}")
            return 0

    async def _cleanup_orphan_global_states(self, session) -> int:
        """清理孤儿全局状态：处理状态为processing但实例不存在的记录"""
        try:
            # 获取所有processing状态的全局任务
            processing_states_query = select(GlobalJobState).where(
                GlobalJobState.global_status == "processing"
            )
            processing_states = session.exec(processing_states_query).all()
            
            if not processing_states:
                return 0
            
            # 获取所有存在的实例ID
            existing_instances_query = select(JobManagerInstance.instance_id)
            existing_instance_ids = {row for row in session.exec(existing_instances_query).all()}
            
            orphan_count = 0
            for state in processing_states:
                if state.processing_instance_id and state.processing_instance_id not in existing_instance_ids:
                    # 这是孤儿状态，重置为可重新分配
                    logger.warning(f"发现孤儿全局状态: job_id={state.job_id}, instance_id={state.processing_instance_id}")
                    state.global_status = "reassignable"
                    state.processing_instance_id = None
                    state.updated_time = datetime.now()
                    
                    # 更新元数据
                    metadata = {}
                    if state.job_metadata:
                        try:
                            metadata = json.loads(state.job_metadata)
                        except:
                            pass
                    
                    metadata.update({
                        "cleanup_reason": "orphan_global_state",
                        "cleanup_time": datetime.now().isoformat(),
                        "original_instance": state.processing_instance_id
                    })
                    state.job_metadata = json.dumps(metadata)
                    
                    session.add(state)
                    orphan_count += 1
            
            return orphan_count
            
        except Exception as e:
            logger.error(f"清理孤儿全局状态失败: {e}")
            return 0

    async def _recover_jobs_on_startup(self):
        """
        改进的服务器重启后任务恢复
        
        改进：
        1. 启动前已做全面清理，状态更干净
        2. 更严格的并发限制检查
        3. 智能的任务分类和优先级
        4. 更好的故障处理
        """
        logger.info("开始任务恢复流程（启动前已完成清理）...")
        
        try:
            # 验证实例注册状态和心跳
            try:
                await self.distributed_lock.update_heartbeat(0)  # 初始时没有活跃任务
                logger.info("已注册实例心跳，开始任务恢复")
            except Exception as e:
                logger.warning(f"注册实例心跳失败，但继续恢复: {e}")
            
            # 验证实例注册状态
            if not (hasattr(self.distributed_lock, 'instance_id') and self.distributed_lock.instance_id):
                logger.error("实例ID未正确设置，任务恢复可能失败")
            else:
                logger.info(f"实例注册状态验证成功: {self.distributed_lock.instance_id}")
            
            # 验证清理效果：检查全局活跃任务数是否合理
            try:
                initial_global_count = await self.distributed_lock.get_global_active_jobs_count()
                logger.info(f"当前全局活跃任务数: {initial_global_count}")
                
                # 如果全局活跃任务数过多，记录警告但继续
                if initial_global_count >= self.global_max_concurrent:
                    logger.warning(f"全局活跃任务数({initial_global_count})已达上限({self.global_max_concurrent})，"
                                 "可能影响任务恢复，但已做过清理，继续执行")
            except Exception as e:
                logger.warning(f"检查全局任务状态失败: {e}")
            
            with session_scope() as session:
                # 查找所有处于中间状态的job
                intermediate_statuses = [
                    JobStatus.PENDING.value,
                    JobStatus.PENDING_RESUME.value, 
                    JobStatus.PROCESSING.value,
                    JobStatus.RESUMING.value,
                    JobStatus.PAUSED.value
                ]
                
                from sqlmodel import select
                statement = select(WikiJob).where(WikiJob.status.in_(intermediate_statuses))
                orphaned_jobs = session.exec(statement).all()
                
                logger.info(f"发现{len(orphaned_jobs)}个需要恢复的任务")
                
                # 按优先级分类任务
                same_instance_jobs = []  # 同一实例的任务（实例重启）
                other_instance_jobs = []  # 其他实例的任务（需要检查是否下线）
                timeout_jobs = []
                
                for job in orphaned_jobs:
                    try:
                        if job.updated_time and datetime.now() - job.updated_time > timedelta(hours=6):
                            update_job(session, job.id, status=JobStatus.FAILED.value,
                                     error_message="任务超时失败")
                            
                            # 只有生成任务才更新wiki_info状态，刷新任务不更新
                            if job.wiki_info_id and getattr(job, 'job_type', 0) != 1:  # 0表示生成任务，1表示刷新任务
                                update_wiki_info(session, job.wiki_info_id, status="failed",
                                               error_message="任务超时失败")
                            
                            timeout_jobs.append(job.id)
                            logger.warning(f"Job {job.id} 超时，已标记为失败")
                            continue
                        
                        # 检查全局状态，避免重复恢复
                        try:
                            global_job_state = await self.distributed_lock.get_global_job_state(job.id)
                            if global_job_state:
                                global_status = global_job_state["global_status"]
                                processing_instance_id = global_job_state["processing_instance_id"]
                                
                                if global_status == "processing" and processing_instance_id != self.distributed_lock.instance_id:
                                    logger.info(f"Job {job.id} 正被其他实例 {processing_instance_id} 处理，跳过恢复")
                                    continue
                        except Exception as e:
                            logger.warning(f"获取Job {job.id} 全局状态失败: {e}，将继续尝试恢复")
                        
                        # 检查任务锁状态
                        lock_info = await self.distributed_lock.get_job_lock_info(job.id)
                        if lock_info:
                            lock_instance_id = lock_info.get("instance_id")
                            
                            # 情况1：锁被当前实例持有（同一实例重启）
                            if lock_instance_id == self.distributed_lock.instance_id:
                                logger.info(f"Job {job.id} 锁被当前实例持有（实例重启），直接恢复")
                                same_instance_jobs.append(job)
                            else:
                                # 情况2：锁被其他实例持有，需要检查该实例是否下线
                                logger.info(f"Job {job.id} 锁被其他实例 {lock_instance_id} 持有，检查该实例状态")
                                other_instance_jobs.append((job, lock_instance_id))
                        else:
                            # 没有锁，直接尝试通过统一入口恢复
                            # 统一入口内部会进行并发限制检查和锁获取的原子操作
                            job_context = self._build_job_context_from_db(job)
                            if job_context:
                                success = await self._unified_job_assignment(job_context, "recover")
                                if success:
                                    logger.info(f"Job {job.id} 无锁，通过统一入口成功恢复")
                                else:
                                    # 统一入口失败，可能是并发限制或其他原因，转为排队状态
                                    if job.status in [JobStatus.PROCESSING.value, JobStatus.RESUMING.value]:
                                        update_job(session, job.id, status=JobStatus.PENDING_RESUME.value,
                                                 stage="queue", stage_progress=0,
                                                 stage_message="恢复失败，转为排队等待")
                                    else:
                                        update_job(session, job.id, status=JobStatus.PENDING.value,
                                                 stage="queue", stage_progress=0,
                                                 stage_message="恢复失败，转为排队等待")
                                    logger.info(f"Job {job.id} 通过统一入口恢复失败，已转为排队状态")
                            continue  # 直接继续，不加入same_instance_jobs（因为已经在统一入口中处理了）
                        
                    except Exception as e:
                        logger.error(f"分类job {job.id} 时出错: {e}")
                        continue
                
                logger.info(f"恢复分类: 同实例={len(same_instance_jobs)}, 其他实例={len(other_instance_jobs)}, 超时={len(timeout_jobs)}")
                
                # 恢复同一实例的任务（优先级最高）
                recovered_count = await self._recover_same_instance_jobs(session, same_instance_jobs)
                
                # 检查并接管其他实例的任务
                adopted_count = await self._check_and_adopt_other_instance_jobs(session, other_instance_jobs)
                
                total_recovered = recovered_count + adopted_count
                logger.info(f"故障恢复完成 - 恢复任务: {recovered_count}, 接管任务: {adopted_count}, 总计: {total_recovered}")
                
                # 如果有任务需要扫描，触发延迟扫描
                if total_recovered > 0:
                    logger.info("5秒后触发延迟扫描...")
                    asyncio.create_task(self._delayed_scan_after_recovery())
                
        except Exception as e:
            logger.error(f"故障恢复时出错: {e}")

    async def _recover_same_instance_jobs(self, session, jobs: List[WikiJob]) -> int:
        """恢复同一实例的任务 - 完全使用统一入口"""
        recovered_count = 0
        
        for job in jobs:
            try:
                # 构建任务上下文并恢复
                job_context = self._build_job_context_from_db(job)
                if job_context:
                    if job.status == JobStatus.PAUSED.value:
                        # 修复：暂停的任务应该继续执行，而不是保持暂停状态
                        logger.info(f"Job {job.id} 从暂停状态恢复，准备继续执行")
                        
                        # 重置暂停状态，准备继续执行
                        job_context.pause_requested = False
                        job_context.pause_event = threading.Event()
                        job_context.pause_event.set()  # 设置为继续状态
                        
                        # 通过统一入口恢复执行（内部会进行并发检查、锁获取等原子操作）
                        success = await self._unified_job_assignment(job_context, "resume")
                        if success:
                            recovered_count += 1
                            logger.info(f"Job {job.id} 已从暂停状态成功恢复执行")
                        else:
                            # 统一入口失败，可能是并发限制，转为排队状态
                            update_job(session, job.id, status=JobStatus.PENDING_RESUME.value,
                                     stage="queue", stage_progress=0,
                                     stage_message="暂停任务恢复失败，受并发限制排队等待")
                            logger.info(f"Job {job.id} 从暂停状态恢复失败，已转为排队状态")
                    else:
                        # 其他状态的任务通过统一入口恢复（内部会进行并发检查）
                        success = await self._unified_job_assignment(job_context, "recover")
                        if success:
                            recovered_count += 1
                            logger.info(f"Job {job.id} 已通过统一入口成功恢复执行")
                        else:
                            # 统一入口失败，可能是并发限制，转为排队状态
                            if job.status in [JobStatus.PROCESSING.value, JobStatus.RESUMING.value]:
                                update_job(session, job.id, status=JobStatus.PENDING_RESUME.value,
                                         stage="queue", stage_progress=0,
                                         stage_message="恢复失败，受并发限制排队等待")
                            else:
                                update_job(session, job.id, status=JobStatus.PENDING.value,
                                         stage="queue", stage_progress=0,
                                         stage_message="恢复失败，受并发限制排队等待")
                            logger.info(f"Job {job.id} 通过统一入口恢复失败，已转为排队状态")
                            
            except Exception as e:
                logger.error(f"恢复同实例任务 {job.id} 时出错: {e}")
                
        return recovered_count

    async def _check_and_adopt_other_instance_jobs(self, session, job_instance_pairs: List[tuple]) -> int:
        """检查并接管其他实例的任务"""
        adopted_count = 0
        
        # 检查这些实例是否下线
        dead_instances = set()
        for job, lock_instance_id in job_instance_pairs:
            try:
                # 检查实例是否存活
                with session_scope() as check_session:
                    from sqlmodel import select
                    instance_query = select(JobManagerInstance).where(
                        JobManagerInstance.instance_id == lock_instance_id
                    )
                    instance = check_session.exec(instance_query).first()
                    
                    if not instance:
                        dead_instances.add(lock_instance_id)
                        logger.warning(f"实例 {lock_instance_id} 不存在，标记为死亡")
                    elif instance.status != "active":
                        dead_instances.add(lock_instance_id)
                        logger.warning(f"实例 {lock_instance_id} 状态为 {instance.status}，标记为死亡")
                    else:
                        # 检查心跳超时
                        if instance.last_heartbeat:
                            config = get_wiki_jobs_config()
                            heartbeat_timeout_minutes = config.get('heartbeat_timeout_minutes', 3)  # 读取配置文件中的心跳超时时间
                            timeout_threshold = datetime.now() - timedelta(minutes=heartbeat_timeout_minutes)
                            if instance.last_heartbeat < timeout_threshold:
                                dead_instances.add(lock_instance_id)
                                logger.warning(f"实例 {lock_instance_id} 心跳超时{heartbeat_timeout_minutes}分钟，标记为死亡")
                        else:
                            dead_instances.add(lock_instance_id)
                            logger.warning(f"实例 {lock_instance_id} 无心跳记录，标记为死亡")
                            
            except Exception as e:
                logger.error(f"检查实例 {lock_instance_id} 状态时出错: {e}")
                # 出错时保守处理，不接管任务
        
        # 接管死亡实例的任务
        for job, lock_instance_id in job_instance_pairs:
            if lock_instance_id in dead_instances:
                try:
                    # 强制释放死亡实例的锁
                    await self.distributed_lock.force_release_job_lock(job.id, f"dead_instance_{lock_instance_id}")
                    
                    # 构建任务上下文
                    job_context = self._build_job_context_from_db(job)
                    if job_context:
                        # 通过统一入口接管任务（内部会进行并发检查、锁获取等原子操作）
                        success = await self._unified_job_assignment(job_context, "adopt")
                        if success:
                            adopted_count += 1
                            logger.info(f"Job {job.id} 已从死亡实例 {lock_instance_id} 通过统一入口接管并开始执行")
                        else:
                            # 统一入口失败，可能是并发限制，转为排队状态
                            if job.status in [JobStatus.PROCESSING.value, JobStatus.RESUMING.value]:
                                update_job(session, job.id, status=JobStatus.PENDING_RESUME.value,
                                         stage="queue", stage_progress=0,
                                         stage_message=f"接管死亡实例 {lock_instance_id} 的任务，但受并发限制排队等待")
                            else:
                                update_job(session, job.id, status=JobStatus.PENDING.value,
                                         stage="queue", stage_progress=0,
                                         stage_message=f"接管死亡实例 {lock_instance_id} 的任务，但受并发限制排队等待")
                            logger.info(f"Job {job.id} 从死亡实例 {lock_instance_id} 接管失败，转为排队等待")
                    else:
                        logger.warning(f"Job {job.id} 无法构建JobContext进行接管")
                        
                except Exception as e:
                    logger.error(f"接管任务 {job.id} 时出错: {e}")
        
        return adopted_count

    async def check_and_adopt_reassignable_jobs_with_concurrency(self):
        """
        检查并接手可重新分配的任务
        
        改进：
        1. 增加并发限制检查
        2. 按时间排序，优先接管较早的任务
        3. 统一使用WikiJobManager的并发检查逻辑
        """
        # 本地开发模式下跳过任务接管
        if not self.distributed_lock.enable_distributed_lock:
            logger.debug(f"本地开发模式: 跳过可重新分配任务检查 ({self.distributed_lock.instance_id})")
            return
            
        if not self.distributed_lock.instance_id:
            return
            
        try:
            with session_scope() as session:
                # 查找明确标记为可重新分配的任务
                from sqlmodel import select
                from api.model.job_manager_lock import GlobalJobState, JobLock
                query = select(GlobalJobState).where(
                    GlobalJobState.global_status.in_(["reassignable", "available"])
                ).order_by(GlobalJobState.created_time)  # 按创建时间排序，优先处理较早的任务
                
                reassignable_jobs = session.exec(query).all()
                
                adopted_count = 0
                for job_state in reassignable_jobs:
                    # 检查是否已有锁
                    lock_query = select(JobLock).where(JobLock.job_id == job_state.job_id)
                    existing_lock = session.exec(lock_query).first()
                    
                    if existing_lock:
                        continue  # 已有锁，跳过
                    
                    # 获取对应的WikiJob并构建JobContext
                    job_query = select(WikiJob).where(WikiJob.id == job_state.job_id)
                    job = session.exec(job_query).first()
                    
                    if not job:
                        logger.warning(f"找不到对应的WikiJob: {job_state.job_id}")
                        continue
                    
                    # 构建任务上下文
                    job_context = self._build_job_context_from_db(job)
                    if not job_context:
                        logger.warning(f"无法构建JobContext: {job_state.job_id}")
                        continue
                    
                    # 通过统一入口接管任务（内部会进行并发检查、锁获取等原子操作）
                    success = await self._unified_job_assignment(job_context, "adopt")
                    if success:
                        adopted_count += 1
                        logger.info(f"通过统一入口接管重新分配的任务: {job_state.job_id}")
                    else:
                        # 如果统一入口返回失败（可能是并发限制），停止接管更多任务
                        logger.debug(f"统一入口拒绝接管任务 {job_state.job_id}，可能是并发限制，停止接管更多任务")
                        break
                
                if adopted_count > 0:
                    logger.info(f"本次检查接管了 {adopted_count} 个重新分配的任务")      
        except Exception as e:
             logger.error(f"检查可重新分配任务失败: {e}")

    async def _check_and_fix_excessive_locks(self):
        """
        检查和修复当前实例的锁数量异常问题
        
        如果当前实例持有的锁数量超过了max_concurrent_jobs限制，
        会释放多余的锁并将对应任务转为排队状态。
        """
        if not self.distributed_lock.enable_distributed_lock:
            logger.debug(f"本地开发模式: 跳过锁数量检查")
            return
            
        if not self.distributed_lock.instance_id:
            return
            
        try:
            with session_scope() as session:
                # 获取当前实例持有的所有锁，按获取时间排序（后获取的优先释放）
                from sqlmodel import select
                from api.model.job_manager_lock import JobLock
                
                lock_query = select(JobLock).where(
                    JobLock.instance_id == self.distributed_lock.instance_id
                ).order_by(JobLock.acquired_time.desc())  # 后获取的锁优先释放
                
                current_locks = session.exec(lock_query).all()
                current_lock_count = len(current_locks)
                
                logger.info(f"当前实例持有锁数量: {current_lock_count}/{self.max_concurrent_jobs}")
                
                if current_lock_count <= self.max_concurrent_jobs:
                    logger.info(f"锁数量正常，无需修复")
                    return
                
                # 需要释放的锁数量
                locks_to_release = current_lock_count - self.max_concurrent_jobs
                logger.warning(f"检测到锁数量异常！需要释放 {locks_to_release} 个多余的锁")
                
                # 释放多余的锁（从最晚获取的开始）
                released_count = 0
                for lock in current_locks[:locks_to_release]:  # 取前N个（最晚获取的）
                    try:
                        # 获取对应的任务信息
                        job_query = select(WikiJob).where(WikiJob.id == lock.job_id)
                        job = session.exec(job_query).first()
                        
                        if job:
                            # 将任务转为排队状态
                            if job.status in [JobStatus.PROCESSING.value, JobStatus.RESUMING.value]:
                                update_job(session, job.id, status=JobStatus.PENDING_RESUME.value,
                                         stage="queue", stage_progress=0,
                                         stage_message="启动时修复锁数量异常，重新排队")
                            else:
                                update_job(session, job.id, status=JobStatus.PENDING.value,
                                         stage="queue", stage_progress=0,
                                         stage_message="启动时修复锁数量异常，重新排队")
                        
                        # 释放锁
                        await self.distributed_lock.force_release_job_lock(
                            lock.job_id, "excessive_locks_fix"
                        )
                        
                        released_count += 1
                        logger.warning(f"已释放多余锁: job_id={lock.job_id}, 获取时间={lock.acquired_time}")
                        
                    except Exception as e:
                        logger.error(f"释放锁 {lock.job_id} 时出错: {e}")
                        continue
                
                if released_count > 0:
                    session.commit()
                    logger.info(f"锁数量修复完成：释放了 {released_count} 个多余的锁")
                    
                    # 更新心跳中的当前任务数
                    await self.distributed_lock.update_heartbeat(self.max_concurrent_jobs)
                    
        except Exception as e:
            logger.error(f"检查和修复锁数量时出错: {e}")

    async def _delayed_scan_after_recovery(self):
        """恢复后延迟扫描"""
        await asyncio.sleep(5)  # 等待5秒，确保所有组件启动完成
        try:
            logger.info("开始扫描恢复过程中排队的任务...")
            await self._intelligent_scan_and_assign()
        except Exception as e:
            logger.error(f"恢复后扫描排队任务失败: {e}")

    # ===== 简化的任务执行工作器 =====

    async def _job_worker(self):
        """
        简化的job工作协程，负责从队列中取出job并执行
        
        注意：任务进入队列前已经通过统一入口进行了并发控制，
        这里只需要简单地执行任务即可
        """
        logger.info("优化Job worker启动")
        
        while not self._shutdown:
            try:
                # 从队列获取job（超时1秒避免阻塞）
                try:
                    job_context = await asyncio.wait_for(self.job_queue.get(), timeout=1.0)
                except asyncio.TimeoutError:
                    continue
                
                # 简单检查当前实例容量（双重保险）
                if len(self.active_jobs) >= self.max_concurrent_jobs:
                    logger.warning(f"Job {job_context.job_id} 从队列获取但实例已满，重新入队")
                    await self.job_queue.put(job_context)  # 重新入队
                    await asyncio.sleep(1)
                    continue
                
                # 开始执行job
                await self._execute_job(job_context)
                
            except Exception as e:
                logger.error(f"优化Job worker出错: {e}")
                await asyncio.sleep(1)
        
        logger.info("优化Job worker停止")

    # ===== 任务状态查询和调试 =====

    def get_active_jobs(self) -> List[Dict[str, Any]]:
        """获取当前活跃的job列表"""
        try:
            result = []
            for job_id, job_context in self.active_jobs.items():
                # 确保所有字段都是可序列化的基本类型
                job_info = {
                    "job_id": str(job_id),
                    "wiki_id": str(job_context.wiki_id),
                    "repo_url": str(job_context.repo_url),
                    "repo_type": str(job_context.repo_type),
                    "branch": str(job_context.branch),
                    "language": str(job_context.language),
                    "current_stage": str(job_context.current_stage) if job_context.current_stage else None,
                    "stage_progress": int(job_context.stage_progress),
                    "start_time": job_context.start_time.isoformat() if job_context.start_time else None,
                    "last_heartbeat": job_context.last_heartbeat.isoformat() if job_context.last_heartbeat else None,
                    "pause_requested": bool(job_context.pause_requested),
                    "retry_count": int(job_context.retry_count),
                    "execution_id": str(job_context.execution_id),
                    "is_active_execution": bool(job_context.is_active_execution),
                    "total_files": int(job_context.total_files) if job_context.total_files is not None else None,
                    "processed_files": int(job_context.processed_files) if job_context.processed_files is not None else None
                }
                result.append(job_info)
            return result
        except Exception as e:
            logger.error(f"获取活跃job列表失败: {e}")
            return []

    def get_paused_jobs(self) -> List[Dict[str, Any]]:
        """获取当前暂停的job列表"""
        try:
            result = []
            for job_id, job_context in self.paused_jobs.items():
                # 确保所有字段都是可序列化的基本类型
                job_info = {
                    "job_id": str(job_id),
                    "wiki_id": str(job_context.wiki_id),
                    "repo_url": str(job_context.repo_url),
                    "repo_type": str(job_context.repo_type),
                    "branch": str(job_context.branch),
                    "language": str(job_context.language),
                    "current_stage": str(job_context.current_stage) if job_context.current_stage else None,
                    "stage_progress": int(job_context.stage_progress),
                    "start_time": job_context.start_time.isoformat() if job_context.start_time else None,
                    "pause_requested": bool(job_context.pause_requested),
                    "retry_count": int(job_context.retry_count),
                    "execution_id": str(job_context.execution_id),
                    "total_files": int(job_context.total_files) if job_context.total_files is not None else None,
                    "processed_files": int(job_context.processed_files) if job_context.processed_files is not None else None
                }
                result.append(job_info)
            return result
        except Exception as e:
            logger.error(f"获取暂停job列表失败: {e}")
            return []

    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """获取指定job的状态"""
        try:
            # 检查活跃列表
            if job_id in self.active_jobs:
                job_context = self.active_jobs[job_id]
                return {
                    "job_id": str(job_id),
                    "status": "active",
                    "wiki_id": str(job_context.wiki_id),
                    "repo_url": str(job_context.repo_url),
                    "current_stage": str(job_context.current_stage) if job_context.current_stage else None,
                    "stage_progress": int(job_context.stage_progress),
                    "start_time": job_context.start_time.isoformat() if job_context.start_time else None,
                    "last_heartbeat": job_context.last_heartbeat.isoformat() if job_context.last_heartbeat else None,
                    "pause_requested": bool(job_context.pause_requested),
                    "retry_count": int(job_context.retry_count),
                    "execution_id": str(job_context.execution_id),
                    "is_active_execution": bool(job_context.is_active_execution)
                }
            
            # 检查暂停列表
            if job_id in self.paused_jobs:
                job_context = self.paused_jobs[job_id]
                return {
                    "job_id": str(job_id),
                    "status": "paused",
                    "wiki_id": str(job_context.wiki_id),
                    "repo_url": str(job_context.repo_url),
                    "current_stage": str(job_context.current_stage) if job_context.current_stage else None,
                    "stage_progress": int(job_context.stage_progress),
                    "start_time": job_context.start_time.isoformat() if job_context.start_time else None,
                    "pause_requested": bool(job_context.pause_requested),
                    "retry_count": int(job_context.retry_count),
                    "execution_id": str(job_context.execution_id)
                }
            
            # 从数据库查询
            with session_scope() as session:
                job = get_job(session, job_id)
                if job:
                    return {
                        "job_id": job_id,
                        "status": job.status,
                        "progress": job.progress,
                        "stage": job.stage,
                        "stage_progress": job.stage_progress,
                        "stage_message": job.stage_message,
                        "created_time": job.created_time.isoformat() if job.created_time else None,
                        "updated_time": job.updated_time.isoformat() if job.updated_time else None,
                        "error_message": job.error_message
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"获取job {job_id} 状态失败: {e}")
            return None

    def debug_job_status_consistency(self) -> Dict[str, Any]:
        """调试job状态一致性"""
        try:
            debug_info = {
                "active_jobs_count": len(self.active_jobs),
                "paused_jobs_count": len(self.paused_jobs),
                "queue_size": self.job_queue.qsize(),
                "execution_registry_count": len(self._execution_registry),
                "instance_id": self.distributed_lock.instance_id if hasattr(self.distributed_lock, 'instance_id') else None,
                "max_concurrent_jobs": self.max_concurrent_jobs,
                "global_max_concurrent": self.global_max_concurrent,
                "issues": []
            }
            
            # 检查执行注册一致性
            with self._global_status_lock:
                for job_id in self.active_jobs:
                    if job_id not in self._execution_registry:
                        debug_info["issues"].append(f"活跃job {job_id} 缺少执行注册")
                
                for job_id in self._execution_registry:
                    if job_id not in self.active_jobs and job_id not in self.paused_jobs:
                        debug_info["issues"].append(f"执行注册 {job_id} 没有对应的活跃或暂停job")
            
            # 检查并发限制
            if len(self.active_jobs) > self.max_concurrent_jobs:
                debug_info["issues"].append(f"活跃任务数({len(self.active_jobs)})超过限制({self.max_concurrent_jobs})")
            
            return debug_info
            
        except Exception as e:
            logger.error(f"调试状态一致性失败: {e}")
            return {"error": str(e)}

    def validate_job_assignment_atomicity(self, job_id: str) -> Dict[str, Any]:
        """
        验证任务分配的原子性 - 检查修复效果
        
        验证以下原子性保证：
        1. 有锁必有内存记录
        2. 有内存记录必有执行注册
        3. 有async_task必有内存记录
        4. 任务状态一致性
        5. wiki_job状态与job_lock状态一致性
        """
        validation_result = {
            "job_id": job_id,
            "atomic_consistency": True,
            "issues": [],
            "memory_state": {},
            "lock_state": None,
            "global_state": None,
            "recommendations": []
        }
        
        try:
            # 1. 检查内存状态
            in_active = job_id in self.active_jobs
            in_paused = job_id in self.paused_jobs
            in_registry = job_id in self._execution_registry
            
            validation_result["memory_state"] = {
                "in_active_jobs": in_active,
                "in_paused_jobs": in_paused,
                "in_execution_registry": in_registry
            }
            
            # 2. 获取任务上下文信息
            job_context = None
            if in_active:
                job_context = self.active_jobs[job_id]
            elif in_paused:
                job_context = self.paused_jobs[job_id]
            
            if job_context:
                validation_result["memory_state"].update({
                    "has_async_task": hasattr(job_context, 'async_task') and job_context.async_task is not None,
                    "has_future": job_context.future is not None,
                    "has_pause_event": job_context.pause_event is not None,
                    "is_active_execution": job_context.is_active_execution,
                    "pause_requested": job_context.pause_requested
                })
            
            # 3. 检查原子性一致性
            # 规则1: 如果在活跃或暂停列表，必须在执行注册中
            if (in_active or in_paused) and not in_registry:
                validation_result["atomic_consistency"] = False
                validation_result["issues"].append("内存中有任务记录但缺少执行注册")
                validation_result["recommendations"].append("需要同步执行注册")
            
            # 规则2: 如果在执行注册中，必须在活跃或暂停列表中
            if in_registry and not (in_active or in_paused):
                validation_result["atomic_consistency"] = False
                validation_result["issues"].append("有执行注册但缺少内存任务记录")
                validation_result["recommendations"].append("需要清理孤儿执行注册")
            
            # 规则3: 活跃任务应该有async_task（如果支持）
            if in_active and job_context:
                if not hasattr(job_context, 'async_task') or job_context.async_task is None:
                    validation_result["issues"].append("活跃任务缺少async_task引用")
                    validation_result["recommendations"].append("任务可能已完成或异常，需要检查")
            
            # 规则4: 不应该既在活跃又在暂停列表中
            if in_active and in_paused:
                validation_result["atomic_consistency"] = False
                validation_result["issues"].append("任务同时存在于活跃和暂停列表中")
                validation_result["recommendations"].append("严重错误，需要立即修复")
            
            # 规则5: 检查状态一致性（wiki_job vs job_lock）
            try:
                # 异步检查锁状态（避免阻塞）
                import asyncio
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        # 如果事件循环正在运行，创建任务
                        asyncio.create_task(self._async_validate_job_lock_consistency(job_id, validation_result))
                    else:
                        # 如果没有运行的事件循环，直接运行
                        asyncio.run(self._async_validate_job_lock_consistency(job_id, validation_result))
                except RuntimeError:
                    # 无法获取事件循环，跳过异步检查
                    validation_result["issues"].append("无法检查锁状态一致性（事件循环问题）")
            except Exception as e:
                validation_result["issues"].append(f"状态一致性检查失败: {str(e)}")
            
            # 4. 异步获取锁状态和全局状态（如果需要详细检查）
            # 注：这里只做基本验证，避免阻塞操作
            
            return validation_result
            
        except Exception as e:
            logger.error(f"验证任务 {job_id} 原子性失败: {e}")
            validation_result["atomic_consistency"] = False
            validation_result["issues"].append(f"验证过程出错: {str(e)}")
            return validation_result

    async def _async_validate_job_lock_consistency(self, job_id: str, validation_result: Dict[str, Any]):
        """异步验证任务锁状态一致性"""
        try:
            # 获取锁信息
            lock_info = await self.distributed_lock.get_job_lock_info(job_id)
            
            # 获取全局状态
            global_state = await self.distributed_lock.get_global_job_state(job_id)
            
            # 检查状态一致性
            if lock_info:
                lock_instance_id = lock_info.get("instance_id")
                if lock_instance_id == self.distributed_lock.instance_id:
                    # 锁被当前实例持有
                    if job_id not in self.active_jobs and job_id not in self.paused_jobs:
                        validation_result["issues"].append("锁被当前实例持有但任务不在内存中")
                        validation_result["recommendations"].append("可能存在僵尸锁，需要清理")
                else:
                    # 锁被其他实例持有
                    validation_result["issues"].append(f"锁被其他实例 {lock_instance_id} 持有")
                    validation_result["recommendations"].append("需要检查其他实例状态或强制清理锁")
            else:
                # 没有锁
                if job_id in self.active_jobs or job_id in self.paused_jobs:
                    validation_result["issues"].append("任务在内存中但没有对应的锁")
                    validation_result["recommendations"].append("可能存在孤儿任务，需要清理")
            
            # 检查全局状态一致性
            if global_state:
                global_status = global_state.get("global_status")
                if global_status == "processing" and job_id not in self.active_jobs:
                    validation_result["issues"].append("全局状态为processing但任务不在活跃列表中")
                    validation_result["recommendations"].append("全局状态与实际状态不一致，需要同步")
            
            logger.debug(f"任务 {job_id} 锁状态一致性检查完成")
            
        except Exception as e:
            logger.error(f"异步验证任务 {job_id} 锁状态一致性失败: {e}")
            validation_result["issues"].append(f"锁状态一致性检查失败: {str(e)}")

    async def validate_instance_atomicity_async(self) -> Dict[str, Any]:
        """
        异步验证整个实例的原子性一致性
        包括锁状态和全局状态的检查
        """
        validation_result = {
            "instance_id": self.distributed_lock.instance_id,
            "overall_consistency": True,
            "summary": {},
            "detailed_issues": [],
            "lock_consistency": {},
            "recommendations": []
        }
        
        try:
            # 1. 获取内存统计
            active_count = len(self.active_jobs)
            paused_count = len(self.paused_jobs)
            registry_count = len(self._execution_registry)
            
            # 2. 获取锁统计
            actual_locks_count = await self._get_actual_locks_count()
            
            validation_result["summary"] = {
                "active_jobs": active_count,
                "paused_jobs": paused_count,
                "execution_registry": registry_count,
                "actual_locks": actual_locks_count,
                "total_memory_jobs": active_count + paused_count
            }
            
            # 3. 检查整体一致性
            total_memory_jobs = active_count + paused_count
            
            # 一致性检查规则
            if actual_locks_count != total_memory_jobs:
                validation_result["overall_consistency"] = False
                validation_result["detailed_issues"].append({
                    "type": "lock_memory_mismatch",
                    "description": f"锁数量({actual_locks_count}) != 内存任务数({total_memory_jobs})",
                    "severity": "high" if abs(actual_locks_count - total_memory_jobs) > 1 else "medium"
                })
            
            if registry_count != total_memory_jobs:
                validation_result["overall_consistency"] = False
                validation_result["detailed_issues"].append({
                    "type": "registry_memory_mismatch",
                    "description": f"执行注册数({registry_count}) != 内存任务数({total_memory_jobs})",
                    "severity": "high"
                })
            
            # 4. 检查并发限制
            if active_count > self.max_concurrent_jobs:
                validation_result["overall_consistency"] = False
                validation_result["detailed_issues"].append({
                    "type": "concurrency_violation",
                    "description": f"活跃任务数({active_count})超过实例限制({self.max_concurrent_jobs})",
                    "severity": "critical"
                })
            
            # 5. 生成修复建议
            if not validation_result["overall_consistency"]:
                if actual_locks_count > total_memory_jobs:
                    validation_result["recommendations"].append("存在僵尸锁，建议运行僵尸锁清理")
                elif actual_locks_count < total_memory_jobs:
                    validation_result["recommendations"].append("存在孤儿任务，建议运行孤儿任务清理")
                
                if registry_count != total_memory_jobs:
                    validation_result["recommendations"].append("执行注册不一致，建议重启实例或手动修复")
                
                if active_count > self.max_concurrent_jobs:
                    validation_result["recommendations"].append("并发违规，建议立即停止多余任务")
            
            return validation_result
            
        except Exception as e:
            logger.error(f"异步验证实例原子性失败: {e}")
            validation_result["overall_consistency"] = False
            validation_result["detailed_issues"].append({
                "type": "validation_error",
                "description": f"验证过程出错: {str(e)}",
                "severity": "unknown"
            })
            return validation_result

    # ===== 缺失的优化队列扫描器和智能分配方法 =====
    
    async def _optimized_queue_scanner_worker(self):
        """
        优化的队列扫描器工作协程
        
        改进：
        1. 事件驱动扫描：任务完成后立即触发队列扫描
        2. 优先级队列：resume > retry > new 的优先级排序
        3. 智能扫描频率：根据实例状态调整扫描间隔
        4. 批量分配：一次扫描可分配多个任务（受并发限制）
        """
        logger.info("优化队列扫描器启动")
        
        while not self._shutdown:
            try:
                current_time = time.time()
                
                # 检查扫描条件
                should_scan = False
                scan_reason = ""
                
                # 条件1: 实例有空闲容量
                if len(self.active_jobs) < self.max_concurrent_jobs:
                    should_scan = True
                    scan_reason = "实例有空闲容量"
                
                # 条件2: 定时强制扫描（30秒）
                if current_time - self._last_queue_scan > 30:
                    should_scan = True
                    scan_reason = "定时强制扫描"
                
                # 条件3: 任务完成事件触发
                if self._job_completed_event.is_set():
                    should_scan = True
                    scan_reason = "任务完成事件触发"
                    self._job_completed_event.clear()  # 清除事件标志
                
                if should_scan:
                    logger.debug(f"开始队列扫描: {scan_reason}")
                    await self._intelligent_scan_and_assign()
                    self._last_queue_scan = current_time
                    await asyncio.sleep(5)  # 扫描后短暂等待
                else:
                    # 等待任务完成事件或超时
                    try:
                        await asyncio.wait_for(self._job_completed_event.wait(), timeout=15.0)
                    except asyncio.TimeoutError:
                        pass  # 超时继续循环
                    
            except Exception as e:
                logger.error(f"优化队列扫描器出错: {e}")
                await asyncio.sleep(15)
        
        logger.info("优化队列扫描器已停止")
    
    async def _intelligent_scan_and_assign(self):
        """
        智能扫描和分配排队的任务
        
        改进：
        1. 优先级排序：pending_resume > retry > pending（新任务）
        2. 批量分配：一次扫描可分配多个任务
        3. 严格的并发控制：实时检查实例和全局限制
        4. 智能错误处理：跳过有问题的任务继续处理其他任务
        """
        try:
            # 检查当前实例空闲容量
            current_active_count = len(self.active_jobs)
            available_slots = self.max_concurrent_jobs - current_active_count
            
            if available_slots <= 0:
                logger.debug(f"当前实例已满({current_active_count}/{self.max_concurrent_jobs})，跳过扫描")
                return
            
            # 检查全局并发限制
            try:
                global_active_count = await self.distributed_lock.get_global_active_jobs_count()
                global_available_slots = self.global_max_concurrent - global_active_count
                
                if global_available_slots <= 0:
                    logger.debug(f"全局任务已满({global_active_count}/{self.global_max_concurrent})，跳过扫描")
                    return
                
                # 实际可分配数量
                max_assignable = min(available_slots, global_available_slots)
            except Exception as e:
                logger.warning(f"检查全局并发失败，使用保守策略: {e}")
                max_assignable = min(available_slots, 1)  # 保守策略：最多分配1个
            
            logger.debug(f"开始智能扫描分配，最多可分配 {max_assignable} 个任务")
            
            with session_scope() as session:
                # 按优先级查询待处理任务
                priority_statuses = [
                    JobStatus.PENDING_RESUME.value,  # 优先级最高：恢复任务
                    JobStatus.PENDING.value          # 优先级较低：新任务
                ]
                
                assigned_count = 0
                
                for status in priority_statuses:
                    if assigned_count >= max_assignable:
                        break
                    
                    # 查询该状态的待处理任务
                    from sqlmodel import select
                    statement = (
                        select(WikiJob)
                        .where(WikiJob.status == status)
                        .order_by(WikiJob.created_time.asc())  # 按创建时间排序，先进先出
                        .limit(max_assignable - assigned_count)
                    )
                    
                    pending_jobs = session.exec(statement).all()
                    logger.debug(f"找到 {len(pending_jobs)} 个状态为 {status} 的待处理任务")
                    
                    for job in pending_jobs:
                        try:
                            # 再次检查并发限制（双重检查）
                            if not await self._strict_concurrency_check():
                                logger.debug("并发限制检查失败，停止分配")
                                break
                            
                            # 构建任务上下文
                            job_context = self._build_job_context_from_db(job)
                            if not job_context:
                                logger.warning(f"无法构建Job {job.id} 的上下文，跳过")
                                continue
                            
                            # 确定操作类型
                            operation = "resume" if status == JobStatus.PENDING_RESUME.value else "submit"
                            
                            # 尝试分配任务
                            success = await self._unified_job_assignment(job_context, operation)
                            if success:
                                assigned_count += 1
                                logger.info(f"成功分配任务 {job.id}，操作类型: {operation}")
                            else:
                                logger.warning(f"分配任务 {job.id} 失败")
                                
                        except Exception as e:
                            logger.error(f"处理任务 {job.id} 时出错: {e}")
                            continue  # 跳过有问题的任务，继续处理其他任务
                
                if assigned_count > 0:
                    logger.info(f"智能扫描完成，成功分配了 {assigned_count} 个任务")
                else:
                    logger.debug("智能扫描完成，没有找到可分配的任务")
                    
        except Exception as e:
            logger.error(f"智能扫描和分配时出错: {e}")

    def get_instance_status(self) -> Dict[str, Any]:
        """获取实例状态"""
        try:
            return {
                "instance_id": self.distributed_lock.instance_id if hasattr(self.distributed_lock, 'instance_id') else None,
                "active_jobs_count": len(self.active_jobs),
                "paused_jobs_count": len(self.paused_jobs),
                "max_concurrent_jobs": self.max_concurrent_jobs,
                "global_max_concurrent": self.global_max_concurrent,
                "is_shutdown": self._shutdown,
                "last_queue_scan": self._last_queue_scan,
                "last_instance_check": self._last_instance_check
            }
        except Exception as e:
            logger.error(f"获取实例状态失败: {e}")
            return {"error": str(e)}

    # ===== 兼容性方法（供WikiGenerator调用） =====
    
    async def cleanup_completed_global_states(self):
        """清理已完成任务的全局状态记录"""
        try:
            # 调用分布式锁服务的清理方法
            if hasattr(self.distributed_lock, 'cleanup_completed_global_states'):
                await self.distributed_lock.cleanup_completed_global_states()
        except Exception as e:
            logger.error(f"清理已完成任务全局状态失败: {e}")

    def update_job_progress(self, job_id: str, progress: int, stage: str = None, 
                           stage_progress: int = None, stage_message: str = None,
                           processed_files: int = None, total_files: int = None):
        """更新job进度（供WikiGenerator调用）"""
        try:
            if job_id in self.active_jobs:
                job_context = self.active_jobs[job_id]
                
                # 检查暂停状态
                if job_context.pause_requested:
                    logger.debug(f"Job {job_id} 已暂停，跳过进度更新")
                    return False
                
                # 使用安全更新方法
                return self._safe_update_job_status(
                    job_context,
                    JobStatus.PROCESSING.value,
                    progress,
                    stage or job_context.current_stage or "processing",
                    stage_progress if stage_progress is not None else job_context.stage_progress or 0,
                    stage_message or "处理中...",
                    processed_files=processed_files,
                    total_files=total_files
                )
            else:
                logger.warning(f"Job {job_id} 不在活跃列表中，无法更新进度")
                return False
                
        except Exception as e:
            logger.error(f"更新job {job_id} 进度失败: {e}")
            return False

    def check_job_pause_requested(self, job_id: str) -> bool:
        """检查job是否请求暂停（供WikiGenerator调用）"""
        try:
            if job_id in self.active_jobs:
                job_context = self.active_jobs[job_id]
                return job_context.pause_requested
            return False
        except Exception as e:
            logger.error(f"检查job {job_id} 暂停状态失败: {e}")
            return False

    def wait_if_paused(self, job_id: str, timeout: float = None) -> bool:
        """如果job暂停则等待继续（供WikiGenerator调用）"""
        try:
            if job_id in self.active_jobs:
                job_context = self.active_jobs[job_id]
                if job_context.pause_event:
                    return job_context.pause_event.wait(timeout)
            return True
        except Exception as e:
            logger.error(f"等待job {job_id} 继续失败: {e}")
            return False

    async def _force_cleanup_job_lock(self, job_id: str, reason: str):
        """
        强制清理任务锁，特别是死实例持有的锁
        
        在以下情况调用：
        1. 重试失败时
        2. 重启失败时
        3. 继续失败时
        """
        try:
            logger.info(f"开始强制清理Job {job_id} 的锁，原因: {reason}")
            
            # 获取锁信息
            lock_info = await self.distributed_lock.get_job_lock_info(job_id)
            if not lock_info:
                logger.debug(f"Job {job_id} 没有锁，无需清理")
                return
            
            lock_instance_id = lock_info.get("instance_id")
            logger.info(f"Job {job_id} 被实例 {lock_instance_id} 锁定")
            
            # 检查锁持有者是否是死实例
            from api.model.job_manager_lock import JobManagerInstance
            with session_scope() as session:
                from sqlmodel import select
                instance_query = select(JobManagerInstance).where(
                    JobManagerInstance.instance_id == lock_instance_id
                )
                lock_holder = session.exec(instance_query).first()
                
                if not lock_holder:
                    logger.warning(f"锁持有者实例 {lock_instance_id} 不存在，强制释放锁")
                    await self.distributed_lock.release_job_lock(job_id)
                    return
                
                # 检查实例状态
                current_time = datetime.now()
                is_dead_instance = False
                dead_reason = ""
                
                if lock_holder.status in ["stopped", "crashed"]:
                    is_dead_instance = True
                    dead_reason = f"实例状态为{lock_holder.status}"
                elif not lock_holder.last_heartbeat:
                    is_dead_instance = True
                    dead_reason = "无心跳记录"
                else:
                    # 检查心跳超时（使用配置的超时时间）
                    time_since_heartbeat = current_time - lock_holder.last_heartbeat
                    config = get_wiki_jobs_config()
                    timeout_minutes = config.get('heartbeat_timeout_minutes', 3)  # 读取配置文件中的心跳超时时间
                    
                    if time_since_heartbeat > timedelta(minutes=timeout_minutes):
                        is_dead_instance = True
                        dead_reason = f"心跳超时{timeout_minutes}分钟"
                
                if is_dead_instance:
                    logger.warning(f"确认实例 {lock_instance_id} 为死实例 ({dead_reason})，强制释放Job {job_id} 的锁")
                    
                    # 直接强制释放锁
                    await self.distributed_lock.force_release_job_lock(job_id, f"{reason}: {dead_reason}")
                    
                    # 更新全局任务状态为可重新分配
                    await self.distributed_lock.update_global_job_state(
                        job_id, 
                        "reassignable",
                        {
                            "force_cleanup_reason": reason,
                            "dead_instance_id": lock_instance_id,
                            "cleanup_time": datetime.now().isoformat()
                        }
                    )
                    
                    logger.info(f"Job {job_id} 的锁已强制清理，任务标记为可重新分配")
                else:
                    logger.info(f"实例 {lock_instance_id} 状态正常，不强制清理锁")
                    
        except Exception as e:
            logger.error(f"强制清理Job {job_id} 锁失败: {e}")

    def debug_job_execution_status(self, job_id: str) -> Dict[str, Any]:
        """调试任务执行状态 - 帮助诊断任务卡住的问题"""
        try:
            debug_info = {
                "job_id": job_id,
                "in_active_jobs": job_id in self.active_jobs,
                "in_paused_jobs": job_id in self.paused_jobs,
                "in_execution_registry": job_id in self._execution_registry,
                "execution_details": {}
            }
            
            if job_id in self.active_jobs:
                job_context = self.active_jobs[job_id]
                debug_info["execution_details"] = {
                    "has_async_task": hasattr(job_context, 'async_task') and job_context.async_task is not None,
                    "async_task_done": job_context.async_task.done() if hasattr(job_context, 'async_task') and job_context.async_task else None,
                    "has_future": job_context.future is not None,
                    "future_done": job_context.future.done() if job_context.future else None,
                    "future_cancelled": job_context.future.cancelled() if job_context.future else None,
                    "future_exception": str(job_context.future.exception()) if job_context.future and job_context.future.exception() else None,
                    "pause_requested": job_context.pause_requested,
                    "start_time": job_context.start_time.isoformat() if job_context.start_time else None,
                    "last_heartbeat": job_context.last_heartbeat.isoformat() if job_context.last_heartbeat else None,
                    "current_stage": job_context.current_stage,
                    "stage_progress": job_context.stage_progress,
                    "is_active_execution": job_context.is_active_execution
                }
            
            # 从数据库获取任务状态
            try:
                with session_scope() as session:
                    job = get_job(session, job_id)
                    if job:
                        debug_info["database_status"] = {
                            "status": job.status,
                            "stage": job.stage,
                            "stage_progress": job.stage_progress,
                            "progress": job.progress,
                            "stage_message": job.stage_message,
                            "updated_time": job.updated_time.isoformat() if job.updated_time else None
                        }
            except Exception as e:
                debug_info["database_error"] = str(e)
            
            return debug_info
            
        except Exception as e:
            logger.error(f"调试任务 {job_id} 执行状态失败: {e}")
            return {"error": str(e)}

    def force_update_job_stage(self, job_id: str, new_stage: str, stage_progress: int = 0, stage_message: str = None):
        """强制更新任务阶段状态 - 用于修复任务卡住的问题"""
        try:
            if job_id in self.active_jobs:
                job_context = self.active_jobs[job_id]
                
                # 更新job_context中的状态
                job_context.current_stage = new_stage
                job_context.stage_progress = stage_progress
                
                # 更新数据库状态
                message = stage_message or f"强制更新阶段为: {new_stage}"
                success = self._safe_update_job_status(
                    job_context,
                    JobStatus.PROCESSING.value,
                    job_context.stage_progress or 0,
                    new_stage,
                    stage_progress,
                    message
                )
                
                if success:
                    logger.info(f"Job {job_id} 阶段状态已强制更新为: {new_stage}")
                else:
                    logger.warning(f"Job {job_id} 阶段状态更新失败")
                
                return success
            else:
                logger.warning(f"Job {job_id} 阶段状态已强制更新为: {new_stage}")
                return False
                
        except Exception as e:
            logger.error(f"强制更新Job {job_id} 阶段状态失败: {e}")
            return False

    async def force_sync_job_status_consistency(self, job_id: str) -> bool:
        """
        强制同步任务状态一致性
        
        修复以下问题：
        1. wiki_job状态与job_lock状态不一致
        2. 全局状态与实际状态不一致
        3. 内存状态与数据库状态不一致
        """
        try:
            logger.info(f"开始强制同步任务 {job_id} 状态一致性")
            
            # 1. 检查锁状态
            lock_info = await self.distributed_lock.get_job_lock_info(job_id)
            if lock_info:
                lock_instance_id = lock_info.get("instance_id")
                if lock_instance_id != self.distributed_lock.instance_id:
                    logger.warning(f"任务 {job_id} 被其他实例 {lock_instance_id} 锁定，强制清理")
                    await self._force_cleanup_job_lock(job_id, "force_sync_consistency")
                    lock_info = None
            
            # 2. 检查内存状态
            in_active = job_id in self.active_jobs
            in_paused = job_id in self.paused_jobs
            
            # 3. 检查数据库状态
            with session_scope() as session:
                job = get_job(session, job_id)
                if not job:
                    logger.error(f"任务 {job_id} 在数据库中不存在")
                    return False
                
                # 4. 根据实际情况修复状态
                if in_active or in_paused:
                    # 任务在内存中，应该有锁
                    if not lock_info:
                        logger.warning(f"任务 {job_id} 在内存中但没有锁，尝试获取锁")
                        # 尝试获取锁
                        if await self.distributed_lock.acquire_job_lock(job_id, "force_sync"):
                            logger.info(f"任务 {job_id} 已获取锁")
                        else:
                            logger.warning(f"任务 {job_id} 无法获取锁")
                    
                    # 更新数据库状态为processing
                    if job.status != "processing":
                        update_job(session, job_id, 
                                 status="processing",
                                 stage=job.stage or "download",
                                 stage_message="状态一致性修复：任务正在执行")
                        logger.info(f"任务 {job_id} 数据库状态已更新为processing")
                    
                    # 更新全局状态
                    await self.distributed_lock.update_global_job_state(
                        job_id, 
                        "processing",
                        {
                            "instance_id": self.distributed_lock.instance_id,
                            "operation": "force_sync_consistency",
                            "sync_time": datetime.now().isoformat()
                        }
                    )
                    
                else:
                    # 任务不在内存中，不应该有锁
                    if lock_info:
                        logger.warning(f"任务 {job_id} 不在内存中但有锁，清理锁")
                        await self.distributed_lock.force_release_job_lock(job_id, "force_sync_consistency")
                    
                    # 如果数据库状态为processing，重置为pending
                    if job.status == "processing":
                        update_job(session, job_id, 
                                 status="pending",
                                 stage="queue",
                                 stage_message="状态一致性修复：任务重置为等待状态")
                        logger.info(f"任务 {job_id} 数据库状态已重置为pending")
                    
                    # 更新全局状态为可重新分配
                    await self.distributed_lock.update_global_job_state(
                        job_id, 
                        "reassignable",
                        {
                            "sync_reason": "force_sync_consistency",
                            "sync_time": datetime.now().isoformat()
                        }
                    )
                
                session.commit()
            
            logger.info(f"任务 {job_id} 状态一致性同步完成")
            return True
            
        except Exception as e:
            logger.error(f"强制同步任务 {job_id} 状态一致性失败: {e}")
            return False

    async def batch_sync_job_status_consistency(self, job_ids: List[str] = None) -> Dict[str, Any]:
        """
        批量同步任务状态一致性
        
        Args:
            job_ids: 要同步的任务ID列表，如果为None则同步所有相关任务
            
        Returns:
            Dict: 同步结果统计
        """
        try:
            if job_ids is None:
                # 获取所有需要同步的任务
                with session_scope() as session:
                    # 查找所有处于中间状态的任务
                    intermediate_statuses = [
                        JobStatus.PENDING.value,
                        JobStatus.PENDING_RESUME.value, 
                        JobStatus.PROCESSING.value,
                        JobStatus.RESUMING.value,
                        JobStatus.PAUSED.value
                    ]
                    
                    from sqlmodel import select
                    statement = select(WikiJob).where(WikiJob.status.in_(intermediate_statuses))
                    jobs = session.exec(statement).all()
                    job_ids = [job.id for job in jobs]
                
                logger.info(f"开始批量同步 {len(job_ids)} 个任务的状态一致性")
            
            sync_results = {
                "total": len(job_ids),
                "success": 0,
                "failed": 0,
                "details": []
            }
            
            for job_id in job_ids:
                try:
                    success = await self.force_sync_job_status_consistency(job_id)
                    if success:
                        sync_results["success"] += 1
                        sync_results["details"].append({"job_id": job_id, "status": "success"})
                    else:
                        sync_results["failed"] += 1
                        sync_results["details"].append({"job_id": job_id, "status": "failed"})
                except Exception as e:
                    sync_results["failed"] += 1
                    sync_results["details"].append({"job_id": job_id, "status": "error", "error": str(e)})
                    logger.error(f"同步任务 {job_id} 状态一致性时出错: {e}")
                
                # 避免过于频繁的数据库操作
                await asyncio.sleep(0.1)
            
            logger.info(f"批量状态一致性同步完成: 成功 {sync_results['success']}, 失败 {sync_results['failed']}")
            return sync_results
            
        except Exception as e:
            logger.error(f"批量同步任务状态一致性失败: {e}")
            return {"error": str(e)}

# ===== 全局管理函数 =====

# 全局job manager实例
_job_manager: Optional[WikiJobManager] = None

def get_job_manager() -> WikiJobManager:
    """获取全局job manager实例"""
    global _job_manager
    if _job_manager is None:
        raise RuntimeError("Job manager未初始化，请先调用init_job_manager")
    return _job_manager

def init_job_manager(db_service: DatabaseService, docchain_manager: DocChainManager, 
                     lock_timeout_minutes: int = None, **kwargs):
    """初始化全局job manager"""
    global _job_manager
    
    try:
        # 如果已经有job manager，先停止它
        if _job_manager is not None:
            logger.warning("检测到已存在的job manager，准备停止并重新创建")
            try:
                # 注意：这里是同步方法调用异步停止，在实际使用中需要在异步环境中调用
                logger.warning("需要在异步环境中调用stop()方法停止现有manager")
            except Exception as e:
                logger.error(f"停止现有job manager失败: {e}")
        
        # 如果提供了lock_timeout_minutes，初始化分布式锁服务
        if lock_timeout_minutes is not None:
            from api.wiki.distributed_lock import init_distributed_lock_service
            init_distributed_lock_service(lock_timeout_minutes)
        
        # 创建新的job manager
        _job_manager = WikiJobManager(
            db_service=db_service,
            docchain_manager=docchain_manager,
            **kwargs
        )
        
        logger.info("Job manager初始化成功")
        return _job_manager
        
    except Exception as e:
        logger.error(f"初始化job manager失败: {e}")
        raise

async def start_job_manager():
    """启动job manager"""
    job_manager = get_job_manager()
    await job_manager.start()

async def stop_job_manager():
    """停止job manager"""
    global _job_manager
    if _job_manager:
        await _job_manager.stop()
        _job_manager = None

 
