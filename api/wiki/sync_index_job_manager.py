import asyncio
import threading
import logging
from collections import deque
from dataclasses import dataclass, field, asdict
from datetime import datetime
from typing import Optional, Dict, Any, List, Tuple, Set
from concurrent.futures import ThreadPoolExecutor
import time
import shutil

from api.cache.redis.manager import redis_manager
from api.database.base import session_scope
from api.docchain.manager import Doc<PERSON>hainManager
from api.database.service import DatabaseService
from api.data_pipeline import download_repo, git_pull_with_diff
from api.model.git_repository import AiDwGitRepository
from api.config import configs
from api.utils import git_utils
from sqlmodel import select
import os

logger = logging.getLogger(__name__)


@dataclass
class SyncTask:
    job_id: str
    wiki_id: str
    repo_id: int
    repo_url: str
    repo_type: str
    branch: str
    topic_id: Optional[str]
    strategy: str
    namespace: Optional[str] = None
    access_token: Optional[str] = None


@dataclass
class RepoSyncMetrics:
    expected_files: int
    indexed_files: int
    pending_files: int
    missing_files: int = 0
    manifest_count: Optional[int] = None
    status_total: Optional[int] = None
    status_pending: Optional[int] = None
    duplicate_entries: List[Dict[str, Any]] = field(default_factory=list)
    unexpected_entries: List[Dict[str, Any]] = field(default_factory=list)
    missing_entries: List[Dict[str, Any]] = field(default_factory=list)


class SyncIndexJobManager:
    class SlotCancelled(Exception):
        """内部异常：表示等待全局槽位期间任务被取消."""

    def __init__(self, db_service: DatabaseService, docchain_manager: DocChainManager,
                 max_workers: int = 2, job_ttl_seconds: int = 24 * 3600):
        self.db_service = db_service
        self.docchain_manager = docchain_manager
        self.max_workers = max_workers
        self.job_ttl_seconds = job_ttl_seconds

        self._queue: asyncio.Queue[SyncTask] = asyncio.Queue()
        self._executor = ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="wiki-sync")
        self._workers: List[asyncio.Task] = []
        self._shutdown = False

        self._redis = None
        self._redis_lock = threading.RLock()
        self._next_redis_retry = 0.0
        self._redis_retry_interval = 5.0
        self._record_lock = threading.RLock()
        try:
            self._redis = self._get_redis_client()
            if self._redis:
                logger.info("SyncIndexJobManager: Redis client ready for job tracking")
        except Exception as exc:
            logger.warning(f"SyncIndexJobManager: unable to obtain Redis client, fallback to memory ({exc})")
            self._redis = None

        self._memory_store: Dict[str, Dict[str, Any]] = {}
        self._incremental_max_files = 200
        self._cancel_flags: Dict[str, threading.Event] = {}

        sync_config = configs.get("sync_worker", {}) if configs else {}

        def _coerce_limit(value: Any) -> Optional[int]:
            try:
                if value is None:
                    return None
                parsed = int(value)
                return parsed if parsed > 0 else None
            except (TypeError, ValueError):
                return None

        # 取消环境变量 SYNC_INDEX_MAX_JOBS 的覆盖效果，统一以 settings.yaml 为准
        env_limit = None
        instance_limit_cfg = _coerce_limit(sync_config.get("max_concurrent_jobs") if isinstance(sync_config, dict) else None)
        global_limit_cfg = _coerce_limit(sync_config.get("global_concurrent_jobs") if isinstance(sync_config, dict) else None)

        self._configured_instance_limit = env_limit or instance_limit_cfg or None
        if not self._configured_instance_limit:
            self._configured_instance_limit = max(self.max_workers, 1)

        self._configured_global_limit = env_limit or global_limit_cfg or None
        if not self._configured_global_limit:
            self._configured_global_limit = max(self._configured_instance_limit, self.max_workers, 1)

        self._global_limit = max(int(self._configured_global_limit), 1)
        self._local_limit = max(min(int(self._configured_instance_limit), self._global_limit), 1)

        raw_ttl_env = os.getenv("SYNC_INDEX_SLOT_TTL")
        raw_ttl_config = sync_config.get("slot_ttl_seconds") if isinstance(sync_config, dict) else None
        raw_ttl = raw_ttl_env if raw_ttl_env is not None else raw_ttl_config
        try:
            self._global_slot_ttl = int(raw_ttl) if raw_ttl is not None else 900
        except (TypeError, ValueError):
            self._global_slot_ttl = 900
        if self._global_slot_ttl < 60:
            self._global_slot_ttl = 60

        self._global_slot_key = "wiki:sync:global:active"
        self._local_semaphore = threading.BoundedSemaphore(self._local_limit)
        self._local_wait_queue: deque[str] = deque()
        self._redis_waiting_key = "wiki:sync:queue"

        self._pending_list_key = "wiki:sync:queue:pending"
        self._pending_meta_key = f"{self._pending_list_key}:meta"
        self._running_meta_key = "wiki:sync:queue:running"
        self._job_registry_key = "wiki:sync:jobs"
        self._wiki_active_key = "wiki:sync:active"
        self._task_store_prefix = "wiki:sync:tasks"

        self._memory_task_store: Dict[str, Dict[int, Dict[str, Any]]] = {}
        self._memory_registry: Dict[str, Dict[str, Any]] = {}
        self._memory_wiki_jobs: Dict[str, str] = {}
        self._memory_pending_list: List[str] = []
        self._memory_pending_meta: Dict[str, Dict[str, Any]] = {}
        self._memory_running_meta: Dict[str, Dict[str, Any]] = {}
        self._restored_once = False
        self._final_job_statuses = {"completed", "failed", "cancelled"}
        self._final_task_statuses = {"completed", "failed", "cancelled"}

        # 槽位TTL心跳：定期刷新全局计数键的过期时间，避免长任务期间键过期
        # 取 slot_ttl_seconds 的 1/3，限制在 [10, 120] 秒
        self._slot_heartbeat_interval = max(min(int(self._global_slot_ttl // 3), 120), 10)
        self._last_slot_heartbeat = 0.0

    async def start(self):
        if self._workers:
            return
        self._shutdown = False
        for _ in range(self.max_workers):
            self._workers.append(asyncio.create_task(self._worker()))
        await self._restore_pending_tasks()
        logger.info("SyncIndexJobManager started with %s workers", self.max_workers)

    async def stop(self):
        self._shutdown = True
        for worker in self._workers:
            worker.cancel()
        self._workers.clear()
        self._executor.shutdown(wait=False)
        logger.info("SyncIndexJobManager stopped")

    async def submit_tasks(self, job_id: str, tasks: List[SyncTask]):
        for task in tasks:
            self._persist_task_payload(task)
            await self._queue.put(task)
            self._record_queue_enqueue(task)
        await self._update_job_record(job_id, {
            "status": "queued",
            "updated_at": datetime.utcnow().isoformat(),
        })

    async def _worker(self):
        while not self._shutdown:
            try:
                task = await self._queue.get()
            except asyncio.CancelledError:
                break

            # 调整：仅在成功获取全局槽位后再标记为 running
            
            def runner():
                asyncio.run(self._process_task(task))

            future = self._executor.submit(runner)
            try:
                await asyncio.wrap_future(future)
            except Exception as exc:
                logger.error(f"Sync task failed: {exc}")
            finally:
                self._record_task_completion(task)
                self._queue.task_done()

    def _is_job_cancelled(self, job_id: str) -> bool:
        flag = self._cancel_flags.get(job_id)
        return flag.is_set() if flag else False

    def _normalize_manifest_key(self, value: Optional[str]) -> str:
        """统一归一化本地/DocChain路径，确保命名空间和分隔符一致."""
        if not value:
            return ""
        cleaned = str(value).replace('\\', '/').strip()
        converter = getattr(self.docchain_manager, "converter", None)
        if converter and hasattr(converter, "_normalize_relative_key"):
            try:
                normalized = converter._normalize_relative_key(cleaned)
            except Exception:
                normalized = cleaned
        else:
            normalized = cleaned.lstrip('./')
        return normalized

    async def _process_task(self, task: SyncTask):
        slot_acquired = False
        progress_state: Dict[str, Any] = {}
        try:
            await self._acquire_global_slot(task)
            slot_acquired = True
            # 刚获取槽位：续期一次TTL并切换到运行中列表
            self._refresh_global_slot_ttl(force=True)
            self._record_queue_dequeue(task)

            repo_record = self._load_repository(task.repo_id)
            if not repo_record:
                await self._mark_task_failed(task, "repository_not_found")
                return

            await self._mark_task_running(task)

            if self._is_job_cancelled(task.job_id):
                await self._mark_task_cancelled(task, "任务已取消")
                return

            owner, repo_name = self._resolve_repository_identity(repo_record)
            namespace = task.namespace or git_utils.build_repo_namespace(owner, repo_name, repo_record.branch)
            local_path = self._get_local_repo_path(owner, repo_name, repo_record.branch)

            repo_ready, clone_error = self._ensure_repository(local_path, repo_record, task.access_token)
            if not repo_ready:
                # 失败时直接透出更友好的错误信息
                friendly_message = clone_error or "代码仓库下载失败，请稍后重试"
                await self._mark_task_failed(task, friendly_message)
                return

            try:
                pull_info = git_pull_with_diff(
                    repo_record.repo_url,
                    local_path,
                    repo_record.repo_type,
                    task.access_token,
                    repo_record.branch,
                )
            except ValueError as pull_error:
                logger.warning(
                    "SyncIndex: git pull failed for repo %s (branch %s): %s, falling back to fresh clone",
                    repo_record.repo_url,
                    repo_record.branch,
                    pull_error,
                )
                try:
                    if os.path.isdir(local_path):
                        shutil.rmtree(local_path, ignore_errors=True)
                except Exception as cleanup_exc:
                    logger.debug("SyncIndex: removing %s failed: %s", local_path, cleanup_exc)
                repo_ready, clone_error = self._ensure_repository(local_path, repo_record, task.access_token)
                if not repo_ready:
                    friendly_message = clone_error or "代码仓库下载失败，请稍后重试"
                    await self._mark_task_failed(task, friendly_message)
                    return
                pull_info = {
                    "updated": True,
                    "changes": [],
                    "additions": [],
                    "modifications": [],
                    "deletions": [],
                    "commit_info": {},
                    "output": "recloned",
                    "old_head": None,
                    "new_head": None,
                }
            logger.info(
                "SyncIndex: pulled repo %s (%s), updated=%s",
                repo_record.repo_url,
                repo_record.branch,
                pull_info.get("updated"),
            )

            metrics = await self._collect_repo_metrics(repo_record, namespace, local_path)
            self._persist_repo_metrics(repo_record, metrics)

            current_head = git_utils.get_head_commit(local_path)
            last_synced_commit = getattr(repo_record, "last_sync_commit", None)

            if (
                current_head
                and last_synced_commit
                and current_head == last_synced_commit
                and not pull_info.get("updated")
                and int(metrics.pending_files or 0) == 0
                and int(metrics.missing_files or 0) == 0
                and not metrics.duplicate_entries
                and not metrics.unexpected_entries
            ):
                logger.info(
                    "SyncIndex: repository %s already synced at commit %s, skipping upload",
                    repo_record.repo_url,
                    current_head,
                )
                self._record_repo_expected_counts(
                    repo_record.id,
                    metrics.expected_files,
                    0,
                )
                self._update_repository_sync_info(repo_record.id, current_head)
                await self._mark_task_completed(task, namespace, total_uploaded=0)
                return

            if self._is_job_cancelled(task.job_id):
                await self._mark_task_cancelled(task, "任务已取消")
                return

            topic_id = repo_record.code_topic_id
            if not topic_id:
                topic_id = self.docchain_manager.get_or_create_topic(
                    repo_record.repo_url,
                    repo_record.repo_name,
                    namespace,
                )
                if topic_id:
                    self._update_repository_topic(repo_record.id, topic_id)
                else:
                    await self._mark_task_failed(task, "topic_creation_failed")
                    return

            _, _, manifest_map, manifest_source_map, manifest_relative_map = self._build_manifest_indexes(local_path, namespace)

            change_sets = {
                "additions": pull_info.get("additions", []) if pull_info else [],
                "modifications": pull_info.get("modifications", []) if pull_info else [],
                "deletions": pull_info.get("deletions", []) if pull_info else [],
            }

            def _normalize_repo_path(path: str) -> str:
                return path.replace('\\', '/').lstrip('./')

            changed_files = sorted({_normalize_repo_path(p) for p in change_sets["additions"] + change_sets["modifications"]})
            deletions = change_sets["deletions"]

            target_paths: Set[str] = set(changed_files)
            for entry in metrics.missing_entries or []:
                rel = entry.get("source_relative") or entry.get("relative_without_namespace") or entry.get("relative_key")
                if rel:
                    target_paths.add(_normalize_repo_path(rel))

            for entry in (metrics.duplicate_entries or []) + (metrics.unexpected_entries or []):
                docchain_key = entry.get("docchain_key")
                manifest_entry = manifest_map.get(docchain_key) if docchain_key else None
                if not manifest_entry:
                    # 尝试使用当前路径反查
                    normalized_path = self._normalize_manifest_key(entry.get("path"))
                    manifest_entry = manifest_relative_map.get(normalized_path)
                if manifest_entry:
                    source_rel = manifest_entry.get("source_relative") or manifest_entry.get("relative_without_namespace")
                    if source_rel:
                        target_paths.add(_normalize_repo_path(source_rel))

            relative_to_source: Dict[str, str] = {}
            for source_path in sorted(target_paths):
                display_path = _normalize_repo_path(source_path)
                normalized_source = self._normalize_manifest_key(display_path)
                manifest_entry = manifest_source_map.get(normalized_source)
                if not manifest_entry and normalized_source.endswith('.md'):
                    manifest_entry = manifest_source_map.get(normalized_source[:-3])
                if not manifest_entry and manifest_relative_map:
                    manifest_entry = manifest_relative_map.get(normalized_source)
                relative_key = None
                if manifest_entry:
                    relative_key = self._normalize_manifest_key(
                        manifest_entry.get("relative_key")
                        or manifest_entry.get("docchain_name")
                        or manifest_entry.get("source_relative")
                        or display_path
                    )
                else:
                    candidate = display_path if display_path.endswith('.md') else f"{display_path}.md"
                    relative_key = self._normalize_manifest_key(candidate)
                if relative_key:
                    relative_to_source[relative_key] = display_path

            if not relative_to_source:
                for source_path in sorted(target_paths):
                    display_path = _normalize_repo_path(source_path)
                    candidate = display_path if display_path.endswith('.md') else f"{display_path}.md"
                    relative_to_source[self._normalize_manifest_key(candidate)] = display_path

            target_paths = set(relative_to_source.values())

            normalized_target_paths = {self._normalize_manifest_key(path) for path in target_paths}
            target_paths_with_md = {
                path if path.endswith('.md') else f"{path}.md"
                for path in normalized_target_paths
            }
            target_basenames = {os.path.basename(path) for path in normalized_target_paths}
            target_basenames_with_ext = {os.path.basename(path) for path in target_paths_with_md}
            target_basename_roots = {
                os.path.splitext(name)[0]
                for name in target_basenames.union(target_basenames_with_ext)
            }

            strip_suffix_fn = getattr(self.docchain_manager, "_strip_numeric_suffix", None)

            def _strip_numeric(value: str) -> str:
                if not value:
                    return value
                if strip_suffix_fn:
                    try:
                        return strip_suffix_fn(value)
                    except Exception:
                        pass
                base, ext = os.path.splitext(value)
                return f"{base}{ext}"

            def _matches_target(path_value: str) -> bool:
                if not path_value:
                    return False
                normalized = self._normalize_manifest_key(path_value)
                if normalized in normalized_target_paths or normalized in target_paths_with_md:
                    return True
                if normalized.endswith('.md') and normalized[:-3] in normalized_target_paths:
                    return True
                basename = os.path.basename(normalized)
                if basename in target_basenames or basename in target_basenames_with_ext:
                    return True
                stripped_basename = _strip_numeric(basename)
                if stripped_basename in target_basenames_with_ext:
                    return True
                # 兼容历史：DocChain 旧数据按 basename 存储（无路径），而新数据包含目录前缀（以 __ 连接）
                # 若新目标名以 "__" + 旧basename 结尾，也视为匹配（例如 api__init__.py.md vs __init__.py(3).md）
                for tgt in target_basenames_with_ext:
                    if tgt.endswith(f"__{stripped_basename}") or tgt == stripped_basename:
                        return True
                basename_root = os.path.splitext(stripped_basename)[0]
                if basename_root in target_basename_roots:
                    return True
                stripped_path = _strip_numeric(normalized)
                if stripped_path and stripped_path != normalized:
                    if stripped_path in target_paths_with_md or stripped_path in normalized_target_paths:
                        return True
                    if stripped_path.endswith('.md') and stripped_path[:-3] in normalized_target_paths:
                        return True
                    stripped_basename_alt = os.path.basename(stripped_path)
                    if stripped_basename_alt in target_basenames_with_ext:
                        return True
                    if os.path.splitext(stripped_basename_alt)[0] in target_basename_roots:
                        return True
                return False

            duplicate_ids: List[int] = []
            unexpected_ids: List[int] = []
            for entry in metrics.duplicate_entries:
                doc_id = entry.get("doc_id")
                path = entry.get("path") or entry.get("current") or ""
                if doc_id and _matches_target(path):
                    duplicate_ids.append(int(doc_id))
            for entry in metrics.unexpected_entries:
                doc_id = entry.get("doc_id")
                path = entry.get("path") or ""
                if doc_id and _matches_target(path):
                    unexpected_ids.append(int(doc_id))

            if duplicate_ids:
                try:
                    await self.docchain_manager.delete_documents_batch(duplicate_ids)
                except Exception as exc:
                    logger.warning("SyncIndex: batch delete duplicates failed: %s", exc)
            if unexpected_ids:
                try:
                    await self.docchain_manager.delete_documents_batch(unexpected_ids)
                except Exception as exc:
                    logger.warning("SyncIndex: batch delete unexpected docs failed: %s", exc)

            if not target_paths and not deletions and not duplicate_ids and not unexpected_ids:
                await self._mark_task_completed(task, namespace, total_uploaded=0)
                return

            files_to_sync = len(relative_to_source)
            self._record_repo_expected_counts(
                repo_record.id,
                metrics.expected_files,
                files_to_sync,
            )
            if metrics.expected_files:
                repo_record.doc_total_files = int(metrics.expected_files)
            repo_record.doc_pending_files = int(files_to_sync or 0)

            pending_path_list = sorted(relative_to_source.values())
            initial_missing = max(int(metrics.missing_files or 0), files_to_sync)

            await self._update_job_record(task.job_id, {
                "updated_at": datetime.utcnow().isoformat(),
            }, task_updates={
                task.repo_id: {
                    "status": "running",
                    "mode": "incremental",
                    "pending_files": int(files_to_sync or 0),
                    "total_files": int(files_to_sync or 0),
                    "missing_files": initial_missing,
                    "deleted_file_paths": deletions,
                    "pending_file_paths": pending_path_list,
                    "uploaded_files": 0,
                    "resume_snapshot": {"uploaded_count": 0, "total_files": int(files_to_sync or 0)},
                }
            })

            progress_state = {
                "total": max(int(files_to_sync or 0), 0),
                "last_status_ts": 0.0,
                "uploaded_count": 0,
                "uploaded_file_paths": [],
                "relative_to_source": relative_to_source,
                "remaining_relative_keys": set(relative_to_source.keys()),
            }

            async def handle_progress(
                uploaded: int,
                total: int,
                recent_files: Optional[List[str]] = None,
                force: bool = False,
            ):
                try:
                    progress_state["total"] = total or progress_state.get("total", 0)
                    progress_state["uploaded_count"] = max(uploaded, 0)
                    if progress_state["uploaded_count"] >= progress_state.get("total", 0):
                        progress_state["remaining_relative_keys"] = set()
                    if recent_files:
                        uploaded_list = progress_state.setdefault("uploaded_file_paths", [])
                        uploaded_list.extend(recent_files)
                        remaining_keys = progress_state.setdefault("remaining_relative_keys", set())
                        if remaining_keys:
                            for raw_key in recent_files:
                                normalized_key = self._normalize_manifest_key(raw_key)
                                if normalized_key in remaining_keys:
                                    remaining_keys.discard(normalized_key)
                                elif normalized_key.endswith('.md') and normalized_key[:-3] in remaining_keys:
                                    remaining_keys.discard(normalized_key[:-3])
                    relative_to_source = progress_state.get("relative_to_source", {})
                    remaining_keys = progress_state.setdefault("remaining_relative_keys", set())
                    remaining_paths = sorted(
                        {
                            relative_to_source.get(key) or relative_to_source.get(f"{key}.md") or key
                            for key in remaining_keys
                            if key
                        }
                    )
                    effective_total = progress_state.get("total", 0)
                    pending_remaining = max(effective_total - progress_state["uploaded_count"], len(remaining_paths), 0)
                    percentage = int((uploaded / total) * 100) if total else 100
                    message = f"上传进度 {uploaded}/{total}"
                    await self._update_job_record(task.job_id, {
                        "updated_at": datetime.utcnow().isoformat(),
                    }, task_updates={
                        task.repo_id: {
                            "status": "running",
                            "progress": percentage,
                            "message": message,
                            "uploaded_files": progress_state["uploaded_count"],
                            "pending_files": pending_remaining,
                            "pending_file_paths": remaining_paths,
                            "missing_files": pending_remaining,
                            "total_files": progress_state["total"],
                            "uploaded_file_paths": progress_state.get("uploaded_file_paths"),
                            "resume_snapshot": {
                                "uploaded_count": progress_state["uploaded_count"],
                                "total_files": progress_state["total"],
                            },
                        }
                    })
                    now_ts = time.monotonic()
                    if force or (now_ts - progress_state.get("last_status_ts", 0.0) >= 1.5):
                        await self._update_repo_doc_stats(repo_record)
                        progress_state["last_status_ts"] = now_ts
                        # 心跳：定期续期全局槽位计数键TTL，防止长任务导致键过期
                        self._refresh_global_slot_ttl()
                except Exception as exc:
                    logger.debug(f"progress callback update failed: {exc}")

            def progress_callback(uploaded: int, total: int, **kwargs):
                recent_files = kwargs.get("recent_files")
                asyncio.create_task(handle_progress(uploaded, total, recent_files=recent_files))

            if pull_info and not pull_info.get("updated") and not target_paths and not metrics.missing_entries:
                logger.info("SyncIndex: repository %s has no updates", repo_record.repo_url)
                commit_hash = pull_info.get("new_head") or pull_info.get("old_head") or current_head
                if commit_hash:
                    self._update_repository_sync_info(repo_record.id, commit_hash)
                await self._mark_task_completed(task, namespace)
                return

            cleanup_success = True
            if deletions:
                try:
                    await self._cleanup_deleted_paths(topic_id, deletions)
                except Exception as exc:
                    cleanup_success = False
                    logger.warning(f"SyncIndex: cleanup deleted files failed for repo {repo_record.repo_url}: {exc}")

            uploaded_files_count = 0
            if target_paths:
                if self._is_job_cancelled(task.job_id):
                    await self._mark_task_cancelled(task, "任务已取消")
                    return
                logger.info(
                    "SyncIndex: uploading %d files for repo %s",
                    len(target_paths),
                    repo_record.repo_url,
                )
                upload_result = await self.docchain_manager.upload_repo(
                    repo_paths=[local_path],
                    topic_id=topic_id,
                    included_files=sorted(target_paths),
                    path_namespaces={local_path: namespace},
                    normalize_existing=False,
                    progress_callback=progress_callback,
                )
                if not upload_result.success:
                    await self._mark_task_failed(task, upload_result.error or "upload_failed")
                    return
                uploaded_files_count = upload_result.uploaded_count or len(target_paths)

            commit_hash = None
            if isinstance(pull_info, dict):
                commit_hash = pull_info.get("new_head") or pull_info.get("old_head")
            if not commit_hash:
                commit_hash = current_head or git_utils.get_head_commit(local_path)
            if self._is_job_cancelled(task.job_id):
                await self._mark_task_cancelled(task, "任务已取消")
                return
            await self._update_repo_doc_stats(repo_record)

            if progress_state["total"] > 0:
                await handle_progress(progress_state["total"], progress_state["total"], force=True)
            else:
                await self._update_job_record(task.job_id, {
                    "updated_at": datetime.utcnow().isoformat(),
                }, task_updates={
                    task.repo_id: {
                        "status": "running",
                        "progress": 100,
                        "pending_files": 0,
                        "uploaded_files": progress_state.get("uploaded_count", 0),
                    }
                })

            if not cleanup_success:
                await self._mark_task_failed(task, "cleanup_deleted_files_failed")
                return
            if commit_hash:
                self._update_repository_sync_info(repo_record.id, commit_hash)
            await self._mark_task_completed(
                task,
                namespace,
                total_uploaded=progress_state.get("uploaded_count") or uploaded_files_count,
            )
        except SyncIndexJobManager.SlotCancelled:
            await self._mark_task_cancelled(task, "任务已取消")
        except Exception as exc:
            logger.error("SyncIndex task failed: %s", exc, exc_info=True)
            try:
                await handle_progress(
                    progress_state.get("uploaded_count", 0),
                    progress_state.get("total", 0),
                    force=True,
                )
            except Exception:
                logger.debug("SyncIndex: unable to record final progress snapshot after failure")

            resume_snapshot = {
                "uploaded_count": progress_state.get("uploaded_count", 0),
                "total_files": progress_state.get("total", 0),
            }
            remaining_after_failure = max(
                resume_snapshot["total_files"] - resume_snapshot["uploaded_count"],
                0,
            )
            await self._mark_task_failed(task, str(exc), {
                "uploaded_files": resume_snapshot["uploaded_count"],
                "pending_files": remaining_after_failure,
                "resume_snapshot": resume_snapshot,
            })
        finally:
            if slot_acquired:
                self._release_global_slot()

    def _refresh_global_slot_ttl(self, force: bool = False) -> None:
        """为全局槽位计数键续期TTL，避免长时间任务导致键过期。

        多实例并发调用是安全的（expire不改变计数值）。内部做了简单节流控制。
        """
        client = self._get_redis_client()
        if not client:
            return
        now = time.monotonic()
        if not force and (now - getattr(self, "_last_slot_heartbeat", 0.0)) < self._slot_heartbeat_interval:
            return
        try:
            client.expire(self._global_slot_key, self._global_slot_ttl)
            self._last_slot_heartbeat = now
        except Exception as exc:
            self._handle_redis_error(exc)

    def _ensure_repository(self, local_path: str, repo: AiDwGitRepository, access_token: Optional[str]) -> Tuple[bool, Optional[str]]:
        """确保本地代码仓库可用，失败时返回友好的错误信息。"""
        try:
            if not os.path.exists(local_path) or not os.path.exists(os.path.join(local_path, ".git")):
                download_repo(
                    repo.repo_url,
                    local_path,
                    repo.repo_type,
                    access_token,
                    repo.branch,
                )
            return True, None
        except Exception as exc:
            logger.error("Clone repository failed: %s", exc)
            friendly_message = " ".join(str(exc).split()) or "代码仓库下载失败，请稍后重试"
            return False, friendly_message

    def _load_repository(self, repo_id: int) -> Optional[AiDwGitRepository]:
        with session_scope() as session:
            repo = session.exec(
                select(AiDwGitRepository).where(AiDwGitRepository.id == repo_id)
            ).first()
            if repo:
                session.expunge(repo)  # detach so attributes stay accessible after session closes
            return repo

    def _update_repository_topic(self, repo_id: int, topic_id: str):
        try:
            with session_scope() as session:
                repo = session.get(AiDwGitRepository, repo_id)
                if repo:
                    repo.code_topic_id = topic_id
                    repo.updated_time = datetime.utcnow()
                    session.add(repo)
                    session.commit()
        except Exception as exc:
            logger.warning("Update repository topic failed: %s", exc)

    def _update_repository_sync_info(self, repo_id: int, commit_hash: Optional[str] = None):
        try:
            with session_scope() as session:
                repo = session.get(AiDwGitRepository, repo_id)
                if repo:
                    repo.last_sync_time = datetime.utcnow()
                    if commit_hash:
                        repo.last_sync_commit = commit_hash
                    session.add(repo)
                    session.commit()
        except Exception as exc:
            logger.warning("Update repository sync info failed: %s", exc)

    async def _cleanup_deleted_paths(self, topic_id: Optional[str], deleted_files: List[str]):
        if not topic_id or not deleted_files:
            return
        unique_basenames = {os.path.basename(path) for path in deleted_files if path}
        if not unique_basenames:
            return
        try:
            await self.docchain_manager.cleanup_duplicate_files(topic_id, list(unique_basenames))
        except AttributeError:
            logger.debug("DocChain manager missing cleanup_duplicate_files method")
        except Exception as exc:
            logger.warning(f"Failed to cleanup deleted files for topic {topic_id}: {exc}")

    async def _remove_topic_documents(self, topic_id: Optional[str], entries: List[Dict[str, Any]]):
        if not topic_id or not entries:
            return

        doc_ids: List[int] = []
        for entry in entries:
            doc_id = entry.get("doc_id")
            if doc_id is None:
                continue
            try:
                doc_ids.append(int(doc_id))
            except (TypeError, ValueError):
                continue

        unique_ids = sorted({doc_id for doc_id in doc_ids if doc_id})
        if not unique_ids:
            return

        for doc_id in unique_ids:
            try:
                await self.docchain_manager.delete_document(topic_id, doc_id)
                logger.info("SyncIndex: removed leftover DocChain doc %s from topic %s", doc_id, topic_id)
            except Exception as exc:
                logger.warning(
                    "SyncIndex: failed to remove DocChain doc %s from topic %s: %s",
                    doc_id,
                    topic_id,
                    exc,
                )

    async def _collect_repo_metrics(
        self,
        repo: AiDwGitRepository,
        namespace: str,
        local_path: str,
    ) -> RepoSyncMetrics:
        indexed_files = max(int(repo.doc_total_files or 0), 0)
        cached_pending = max(int(repo.doc_pending_files or 0), 0)

        status_total: Optional[int] = None
        status_pending: Optional[int] = None
        status_data = await self._fetch_topic_status_with_retry(repo.code_topic_id)
        if status_data:
            status_total, status_pending, _ = self._derive_topic_counts(status_data)
            if status_total is not None and status_pending is not None:
                status_total = max(int(status_total), 0)
                status_pending = max(int(status_pending), 0)
                completed = max(status_total - status_pending, 0)
                if completed > indexed_files:
                    indexed_files = completed

        manifest, manifest_count, manifest_map, manifest_source_map, manifest_relative_map = self._build_manifest_indexes(local_path, namespace)

        duplicate_entries: List[Dict[str, Any]] = []
        unexpected_entries: List[Dict[str, Any]] = []
        summary_missing = 0
        missing_entries: List[Dict[str, Any]] = []
        if repo.code_topic_id and hasattr(self.docchain_manager, "summarize_repo_documents"):
            try:
                summary = self.docchain_manager.summarize_repo_documents(
                    repo.code_topic_id,
                    namespace,
                    expected_manifest=manifest if manifest else None,
                )
            except Exception as exc:
                logger.debug(
                    "Summarize repo documents failed for repo %s topic %s: %s",
                    repo.id,
                    repo.code_topic_id,
                    exc,
                )
                summary = None
            if summary:
                indexed_from_summary = summary.get("canonical_total")
                if indexed_from_summary is not None:
                    try:
                        indexed_files = max(indexed_files, int(indexed_from_summary))
                    except (TypeError, ValueError):
                        pass
                duplicate_entries = summary.get("duplicate_docs") or []
                unexpected_entries = summary.get("unexpected_docs") or []
                try:
                    summary_missing = max(int(summary.get("missing_total") or 0), 0)
                except (TypeError, ValueError):
                    summary_missing = 0
                for doc_name in summary.get("missing_doc_names") or []:
                    entry = manifest_map.get(doc_name)
                    if entry:
                        missing_entries.append(entry)

                if manifest_count is None:
                    expected_total = summary.get("expected_total")
                    if expected_total is not None:
                        try:
                            manifest_count = int(expected_total)
                        except (TypeError, ValueError):
                            manifest_count = None

                docchain_total = summary.get("docchain_total")
                if docchain_total is not None and status_total is None:
                    try:
                        status_total = int(docchain_total)
                    except (TypeError, ValueError):
                        status_total = None

        expected_files = int(repo.file_count or 0)
        if manifest_count is not None:
            expected_files = int(manifest_count)
        elif expected_files <= 0:
            if status_total is not None:
                expected_files = int(status_total)
            else:
                expected_files = indexed_files

        base_missing = max(expected_files - min(indexed_files, expected_files), 0)
        missing_files = max(base_missing, summary_missing)
        pending_files = missing_files
        if status_pending is not None:
            pending_files = max(pending_files, min(int(status_pending), expected_files))
        elif cached_pending and pending_files < cached_pending:
            pending_files = cached_pending
        if expected_files and indexed_files == 0 and pending_files == 0:
            pending_files = expected_files

        return RepoSyncMetrics(
            expected_files=max(expected_files, 0),
            indexed_files=max(indexed_files, 0),
            pending_files=max(pending_files, 0),
            missing_files=max(missing_files, 0),
            manifest_count=manifest_count,
            status_total=status_total,
            status_pending=status_pending,
            duplicate_entries=duplicate_entries,
            unexpected_entries=unexpected_entries,
            missing_entries=missing_entries,
        )

    def _build_manifest_indexes(
        self,
        local_path: str,
        namespace: str,
    ) -> Tuple[List[Dict[str, Any]], Optional[int], Dict[str, Dict[str, Any]], Dict[str, Dict[str, Any]], Dict[str, Dict[str, Any]]]:
        manifest: List[Dict[str, Any]] = []
        manifest_count: Optional[int] = None
        manifest_map: Dict[str, Dict[str, Any]] = {}
        manifest_source_map: Dict[str, Dict[str, Any]] = {}
        manifest_relative_map: Dict[str, Dict[str, Any]] = {}

        if not local_path or not os.path.isdir(local_path):
            return manifest, manifest_count, manifest_map, manifest_source_map, manifest_relative_map

        namespace_map = {local_path: namespace}
        try:
            resolved_path = os.path.realpath(local_path)
            if resolved_path and resolved_path != local_path:
                namespace_map[resolved_path] = namespace
        except Exception:
            pass

        try:
            manifest = self.docchain_manager.build_local_manifest([local_path], namespace_map)
        except Exception as exc:
            logger.debug(
                "SyncIndex: failed to build manifest for repo %s: %s",
                local_path,
                exc,
            )
            manifest = []

        manifest_names = {
            entry.get("docchain_name")
            for entry in manifest
            if entry.get("docchain_name")
        }
        manifest_map = {
            entry.get("docchain_name"): entry
            for entry in manifest
            if entry.get("docchain_name")
        }

        for entry in manifest:
            source_key = self._normalize_manifest_key(entry.get("source_relative"))
            if source_key:
                manifest_source_map[source_key] = entry
            relative_key = self._normalize_manifest_key(entry.get("relative_key"))
            if relative_key:
                manifest_relative_map[relative_key] = entry
            docchain_key = self._normalize_manifest_key(entry.get("docchain_name"))
            if docchain_key and docchain_key not in manifest_relative_map:
                manifest_relative_map[docchain_key] = entry

        if manifest_names:
            manifest_count = len(manifest_names)

        return manifest, manifest_count, manifest_map, manifest_source_map, manifest_relative_map

    def _persist_repo_metrics(self, repo: AiDwGitRepository, metrics: RepoSyncMetrics):
        try:
            with session_scope() as session:
                record = session.get(AiDwGitRepository, repo.id)
                if not record:
                    return
                changed = False
                indexed_value = max(int(metrics.indexed_files or 0), 0)
                if record.doc_total_files != indexed_value:
                    record.doc_total_files = indexed_value
                    changed = True

                pending_value = max(int(metrics.pending_files or 0), 0)
                if record.doc_pending_files != pending_value:
                    record.doc_pending_files = pending_value
                    changed = True

                expected_value = max(int(metrics.expected_files or 0), 0)
                if expected_value and record.file_count != expected_value:
                    record.file_count = expected_value
                    changed = True

                if changed:
                    record.updated_time = datetime.utcnow()
                    session.add(record)
                    session.commit()

                repo.doc_total_files = record.doc_total_files
                repo.doc_pending_files = record.doc_pending_files
                if expected_value:
                    repo.file_count = record.file_count
        except Exception as exc:
            logger.debug(f"Persisting repo metrics failed: {exc}")

    async def _fetch_topic_status_with_retry(self, topic_id: Optional[str]) -> Optional[Dict[str, Any]]:
        """Retrieve DocChain topic status with light retry from a worker thread."""
        if not topic_id or not self.docchain_manager:
            return None

        last_error: Optional[Exception] = None
        for attempt in range(3):
            try:
                status = await asyncio.to_thread(self.docchain_manager.get_topic_status, topic_id)
                if status:
                    return status
            except Exception as exc:
                last_error = exc
                logger.debug(
                    "SyncIndex: topic %s status attempt %s failed: %s",
                    topic_id,
                    attempt + 1,
                    exc,
                )
            await asyncio.sleep(0.4)

        if last_error:
            logger.warning(
                "SyncIndex: unable to fetch topic %s status after retries: %s",
                topic_id,
                last_error,
            )
        return None

    def _record_repo_expected_counts(
        self,
        repo_id: int,
        expected_total: Optional[int],
        pending_total: Optional[int],
    ):
        try:
            with session_scope() as session:
                record = session.get(AiDwGitRepository, repo_id)
                if not record:
                    return

                changed = False
                pending_value = max(int(pending_total or 0), 0)
                if record.doc_pending_files != pending_value:
                    record.doc_pending_files = pending_value
                    changed = True

                if expected_total is not None:
                    expected_value = max(int(expected_total or 0), 0)
                    if expected_value and record.doc_total_files != expected_value:
                        record.doc_total_files = expected_value
                        changed = True
                    if expected_value and record.file_count != expected_value:
                        record.file_count = expected_value
                        changed = True

                if changed:
                    record.updated_time = datetime.utcnow()
                    session.add(record)
                    session.commit()
        except Exception as exc:
            logger.debug(f"Recording expected counts failed: {exc}")

    async def _update_repo_doc_stats(self, repo: AiDwGitRepository):
        if not repo:
            return

        topic_id = repo.code_topic_id
        owner = repo.repo_owner
        repo_name = repo.repo_name
        if not owner or not repo_name:
            extracted_owner, extracted_repo, _ = git_utils.extract_repo_info(repo.repo_url)
            owner = owner or extracted_owner
            repo_name = repo_name or extracted_repo
        namespace = git_utils.build_repo_namespace(owner, repo_name, repo.branch)
        local_path = self._get_local_repo_path(owner, repo_name, repo.branch)

        metrics = await self._collect_repo_metrics(repo, namespace, local_path)

        try:
            if topic_id and (metrics.duplicate_entries or metrics.unexpected_entries):
                await self._remove_topic_documents(topic_id, metrics.duplicate_entries + metrics.unexpected_entries)
            self._persist_repo_metrics(repo, metrics)
        except Exception as exc:
            logger.debug(f"Failed to persist repo doc stats: {exc}")

    def _derive_topic_counts(self, status_data: Dict[str, Any]) -> Tuple[int, int, Dict[str, int]]:
        numeric_status: Dict[str, int] = {}
        for key, value in status_data.items():
            if isinstance(value, (int, float)):
                numeric_status[key] = int(value)
                continue
            try:
                numeric_status[key] = int(str(value))
            except (TypeError, ValueError):
                continue

        total = numeric_status.get("total")
        success = None
        for name in ("success", "finished", "completed"):
            if name in numeric_status:
                success = numeric_status[name]
                break

        pending_components = sum(
            value for key, value in numeric_status.items() if key not in {"total", "success", "finished", "completed"}
        )

        if total is None:
            base = success or 0
            total = base + pending_components

        if success is None:
            success = max(total - pending_components, 0)

        pending = max(total - success, pending_components)
        breakdown = {key: numeric_status[key] for key in numeric_status if key != "total"}
        return max(total, 0), max(pending, 0), breakdown

    def _get_redis_client(self, force_refresh: bool = False):
        """获取可用的Redis客户端，必要时重新建立连接."""
        if force_refresh:
            with self._redis_lock:
                self._redis = None

        now = time.monotonic()
        with self._redis_lock:
            if not force_refresh and self._redis:
                try:
                    if self._redis.is_connected():
                        return self._redis
                except Exception as exc:
                    logger.debug("SyncIndex: Redis ping failed, reset client: %s", exc)
                    self._redis = None

            if not force_refresh and now < self._next_redis_retry:
                return None

            try:
                client = redis_manager.get_client()
            except Exception as exc:
                logger.warning("SyncIndex: 获取Redis客户端失败，进入本地模式: %s", exc)
                self._redis = None
                self._next_redis_retry = now + self._redis_retry_interval
                return None

            if not client:
                self._redis = None
                self._next_redis_retry = now + self._redis_retry_interval
                return None

            try:
                if not client.is_connected():
                    client.connect()
            except Exception as exc:
                logger.debug("SyncIndex: Redis重连失败，稍后重试: %s", exc)
                self._redis = None
                self._next_redis_retry = now + self._redis_retry_interval
                return None

            self._redis = client
            self._next_redis_retry = 0.0
            return self._redis

    def _invalidate_redis_client(self, reason: Optional[Exception] = None):
        """标记Redis客户端为失效并设置下一次重试时间."""
        with self._redis_lock:
            self._redis = None
            self._next_redis_retry = time.monotonic() + self._redis_retry_interval
        if reason:
            logger.debug("SyncIndex: Redis客户端失效原因: %s", reason)

    def _handle_redis_error(self, exc: Exception):
        """统一处理Redis异常，保证降级后能够重新拉起Redis."""
        logger.warning("SyncIndex: Redis操作失败，切换至本地信号量: %s", exc)
        self._invalidate_redis_client(exc)

    def _get_active_slot_usage(self) -> Tuple[int, int, int]:
        """返回当前已占用的槽位数量、全局可用槽位与本实例可用槽位."""
        global_limit = max(int(self._global_limit or 0), 1)
        local_limit = max(int(self._local_limit or 0), 1)
        active = 0
        redis_client = self._get_redis_client()
        if redis_client:
            try:
                value = redis_client.get(self._global_slot_key)
                if value is not None:
                    active = max(int(value), 0)
            except Exception as exc:
                self._handle_redis_error(exc)
                redis_client = None

        if not redis_client:
            try:
                current = getattr(self._local_semaphore, "_value", None)
                if current is not None:
                    active_local = max(local_limit - int(current), 0)
                    active = max(active, active_local)
            except Exception:
                active = max(active, 0)

        available_global = max(global_limit - active, 0)
        available_local = max(local_limit - min(active, local_limit), 0)
        return active, available_global, available_local

    async def _acquire_global_slot(self, task: SyncTask):
        job_id = task.job_id
        repo_id = task.repo_id
        queue_key = f"{job_id}:{repo_id}"
        cancel_event = self._cancel_flags.get(job_id)

        redis_client = self._get_redis_client()
        added_to_queue = False
        if redis_client:
            while True:
                if cancel_event and cancel_event.is_set():
                    if added_to_queue:
                        try:
                            redis_client.lrem(self._redis_waiting_key, 0, queue_key)
                        except Exception as cleanup_exc:
                            self._handle_redis_error(cleanup_exc)
                        await self._update_queue_message(job_id, repo_id, None)
                    raise self.SlotCancelled()

                try:
                    value = redis_client.incr(self._global_slot_key)
                    if value <= self._global_limit:
                        try:
                            redis_client.expire(self._global_slot_key, self._global_slot_ttl)
                        except Exception as expire_exc:
                            try:
                                redis_client.decr(self._global_slot_key)
                            except Exception as rollback_exc:
                                self._handle_redis_error(rollback_exc)
                            self._handle_redis_error(expire_exc)
                            redis_client = None
                            break
                        if added_to_queue:
                            try:
                                redis_client.lrem(self._redis_waiting_key, 0, queue_key)
                            except Exception as cleanup_exc:
                                self._handle_redis_error(cleanup_exc)
                        await self._update_queue_message(job_id, repo_id, None)
                        return

                    try:
                        redis_client.decr(self._global_slot_key)
                    except Exception as rollback_exc:
                        self._handle_redis_error(rollback_exc)
                        redis_client = None
                        break

                    if not added_to_queue:
                        try:
                            redis_client.rpush(self._redis_waiting_key, queue_key, expiration=self.job_ttl_seconds)
                        except Exception as enqueue_exc:
                            self._handle_redis_error(enqueue_exc)
                            redis_client = None
                            break
                        added_to_queue = True

                    position = self._get_redis_queue_position(queue_key)
                    if position is not None:
                        await self._update_queue_message(job_id, repo_id, position)
                except Exception as exc:
                    self._handle_redis_error(exc)
                    redis_client = None
                    break

                await asyncio.sleep(0.5)

            if added_to_queue:
                cleanup_client = self._get_redis_client()
                if cleanup_client:
                    try:
                        cleanup_client.lrem(self._redis_waiting_key, 0, queue_key)
                    except Exception as cleanup_exc:
                        self._handle_redis_error(cleanup_exc)
                await self._update_queue_message(job_id, repo_id, None)

        if self._local_semaphore.acquire(blocking=False):
            return

        added_to_queue = False
        while True:
            if cancel_event and cancel_event.is_set():
                if added_to_queue:
                    try:
                        self._local_wait_queue.remove(queue_key)
                    except ValueError:
                        pass
                    await self._update_queue_message(job_id, repo_id, None)
                raise self.SlotCancelled()

            if not added_to_queue:
                self._local_wait_queue.append(queue_key)
                added_to_queue = True

            try:
                position = list(self._local_wait_queue).index(queue_key)
            except ValueError:
                position = None
            if position is not None:
                await self._update_queue_message(job_id, repo_id, position)

            if self._local_semaphore.acquire(blocking=False):
                try:
                    self._local_wait_queue.remove(queue_key)
                except ValueError:
                    pass
                await self._update_queue_message(job_id, repo_id, None)
                return

            await asyncio.sleep(0.5)

    def _release_global_slot(self):
        """释放全局槽位，优先尝试Redis，异常时自动降级."""
        redis_client = self._get_redis_client()
        if redis_client:
            try:
                value = redis_client.decr(self._global_slot_key)
                if value <= 0:
                    redis_client.delete(self._global_slot_key)
                return
            except Exception as exc:
                self._handle_redis_error(exc)
                redis_client = self._get_redis_client(force_refresh=True)
                if redis_client:
                    try:
                        value = redis_client.decr(self._global_slot_key)
                        if value <= 0:
                            redis_client.delete(self._global_slot_key)
                        return
                    except Exception as retry_exc:
                        self._handle_redis_error(retry_exc)
        try:
            # Redis 不可用时回退到本地信号量，保证不会死锁
            self._local_semaphore.release()
        except ValueError:
            pass

    def _get_redis_queue_position(self, queue_key: str) -> Optional[int]:
        if not self._redis:
            return None
        try:
            entries = self._redis.lrange(self._redis_waiting_key, 0, -1)
        except Exception:
            return None
        if not entries:
            return None
        for index, item in enumerate(entries):
            value = item.decode() if isinstance(item, bytes) else item
            if value == queue_key:
                return index
        return None

    async def _update_queue_message(self, job_id: str, repo_id: int, position: Optional[int]):
        message = "等待同步"
        if position is not None:
            message = f"排队中，第{position + 1}位"
        await self._update_job_record(job_id, {
            "updated_at": datetime.utcnow().isoformat(),
        }, task_updates={
            repo_id: {
                "status": "queued",
                "message": message,
            }
        })

    async def _mark_task_running(self, task: SyncTask):
        await self._update_job_record(task.job_id, {
            "status": "running",
            "updated_at": datetime.utcnow().isoformat(),
        }, task_updates={
            task.repo_id: {
                "status": "running",
                "message": f"同步 {task.namespace or task.repo_url}",
            }
        })

    async def _mark_task_completed(
        self,
        task: SyncTask,
        namespace: str,
        total_uploaded: Optional[int] = None,
    ):
        await self._update_job_record(task.job_id, {
            "updated_at": datetime.utcnow().isoformat(),
        }, task_updates={
            task.repo_id: {
                "status": "completed",
                "progress": 100,
                "message": f"同步完成 {namespace}",
                "uploaded_files": total_uploaded if total_uploaded is not None else None,
                "pending_files": 0,
                "pending_file_paths": [],
                "missing_files": 0,
            }
        })
        self._remove_task_payload(task.job_id, task.repo_id)
        await self._maybe_mark_job_finished(task.job_id)

    async def _mark_task_failed(
        self,
        task: SyncTask,
        error: str,
        details: Optional[Dict[str, Any]] = None,
    ):
        task_payload: Dict[str, Any] = {
            "status": "failed",
            "message": error,
        }
        if details:
            task_payload.update(details)
        await self._update_job_record(task.job_id, {
            "status": "failed",
            "updated_at": datetime.utcnow().isoformat(),
        }, task_updates={
            task.repo_id: task_payload
        })
        self._remove_task_payload(task.job_id, task.repo_id)

    async def _mark_task_cancelled(self, task: SyncTask, message: str):
        await self._update_job_record(task.job_id, {
            "updated_at": datetime.utcnow().isoformat(),
        }, task_updates={
            task.repo_id: {
                "status": "cancelled",
                "message": message,
                "progress": 0,
            }
        })
        self._remove_task_payload(task.job_id, task.repo_id)
        await self._maybe_mark_job_cancelled(task.job_id)

    async def _maybe_mark_job_finished(self, job_id: str):
        record = self.get_job_status(job_id)
        if not record:
            return
        tasks = record.get("tasks", [])
        statuses = {task.get("status") for task in tasks}
        if statuses and statuses.issubset({"completed"}):
            await self._update_job_record(job_id, {
                "status": "completed",
                "updated_at": datetime.utcnow().isoformat(),
            })

    async def _maybe_mark_job_cancelled(self, job_id: str):
        record = self.get_job_status(job_id)
        if not record:
            return
        tasks = record.get("tasks", [])
        if not tasks:
            return
        statuses = {task.get("status") for task in tasks}
        if statuses.issubset({"cancelled", "completed"}):
            status = "cancelled" if "cancelled" in statuses else "completed"
            await self._update_job_record(job_id, {
                "status": status,
                "updated_at": datetime.utcnow().isoformat(),
            })

    async def _update_job_record(self, job_id: str, updates: Dict[str, Any],
                                 task_updates: Optional[Dict[int, Dict[str, Any]]] = None):
        with self._record_lock:
            record = self._read_job_record(job_id) or {}
            record.update(updates)
            if task_updates:
                tasks = record.get("tasks", [])
                for task in tasks:
                    repo_id = task.get("repo_id")
                    if repo_id in task_updates:
                        task.update(task_updates[repo_id])
                record["tasks"] = tasks

            # 更新整体进度
            tasks = record.get("tasks", [])
            if tasks:
                total = len(tasks)
                processed = sum(1 for t in tasks if t.get("status") in {"completed", "cancelled"})
                record["processed_units"] = processed
                record["total_units"] = total
                record["progress"] = int((processed / total) * 100)
            self._write_job_record(job_id, record)
            self._update_job_registry(job_id, record)

            status = record.get("status")
            if status in {"completed", "failed", "cancelled"}:
                self._cancel_flags.pop(job_id, None)
                self._clear_wiki_active_job(record.get("wiki_id"), job_id)
                self._remove_task_store(job_id)
                self._cleanup_queue_entries(job_id)

    def _job_redis_key(self, job_id: str) -> str:
        return f"wiki:sync:job:{job_id}"

    def _write_job_record(self, job_id: str, record: Dict[str, Any]):
        if self._redis:
            self._redis.set(
                self._job_redis_key(job_id),
                record,
                expiration=self.job_ttl_seconds,
            )
        else:
            self._memory_store[job_id] = record

    def _read_job_record(self, job_id: str) -> Optional[Dict[str, Any]]:
        if self._redis:
            return self._redis.get(self._job_redis_key(job_id))
        return self._memory_store.get(job_id)

    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        with self._record_lock:
            record = self._read_job_record(job_id)
            if not record:
                return None
            return record.copy()

    def register_job(self, record: Dict[str, Any]):
        with self._record_lock:
            event = self._cancel_flags.get(record["job_id"])
            if event is None:
                self._cancel_flags[record["job_id"]] = threading.Event()
            self._write_job_record(record["job_id"], record)
            self._persist_job_registration(record)

    async def cancel_job(self, job_id: str, reason: str = "请求取消") -> bool:
        with self._record_lock:
            flag = self._cancel_flags.get(job_id)
            if not flag:
                return False
            already_set = flag.is_set()
            flag.set()
        if already_set:
            return True

        prefix = f"{job_id}:"
        if self._redis:
            try:
                waiting_items = self._redis.lrange(self._redis_waiting_key, 0, -1)
                if waiting_items:
                    for raw in waiting_items:
                        value = raw.decode() if isinstance(raw, bytes) else raw
                        if value.startswith(prefix):
                            self._redis.lrem(self._redis_waiting_key, 0, value)
            except Exception as exc:
                logger.debug("SyncIndex: failed to cleanup redis wait queue for %s: %s", job_id, exc)
        else:
            try:
                self._local_wait_queue = deque(
                    item for item in self._local_wait_queue if not item.startswith(prefix)
                )
            except Exception:
                pass

        await self._update_job_record(job_id, {
            "status": "cancelling",
            "updated_at": datetime.utcnow().isoformat(),
            "message": reason,
        })
        return True

    def _resolve_repository_identity(self, repo: AiDwGitRepository) -> tuple[str, str]:
        owner = repo.repo_owner
        name = repo.repo_name
        if owner and name:
            return owner, name
        extracted_owner, extracted_name, _ = git_utils.extract_repo_info(repo.repo_url)
        return owner or extracted_owner, name or extracted_name

    def _get_local_repo_path(self, owner: str, repo_name: str, branch: str) -> str:
        return git_utils.get_repo_cache_path(owner, repo_name, branch)

    def _queue_identifier(self, task: SyncTask) -> str:
        """构造队列任务的唯一标识，便于在Redis与内存中同步状态。"""
        return f"{task.job_id}:{task.repo_id}"

    def _task_store_key(self, job_id: str) -> str:
        """获取任务持久化使用的Redis键，确保命名统一。"""
        return f"{self._task_store_prefix}:{job_id}"

    def _build_task_meta(self, task: SyncTask) -> Dict[str, Any]:
        """生成任务基础元数据，供排队与观测使用。"""
        return {
            "job_id": task.job_id,
            "wiki_id": task.wiki_id,
            "repo_id": task.repo_id,
            "repo_url": task.repo_url,
            "branch": task.branch,
            "namespace": task.namespace,
            "strategy": task.strategy,
        }

    def _ensure_cancel_flag(self, job_id: str):
        """确保作业取消标记已初始化，恢复任务时需要该步骤。"""
        if job_id not in self._cancel_flags:
            self._cancel_flags[job_id] = threading.Event()

    def _persist_task_payload(self, task: SyncTask):
        """持久化任务参数，支持进程重启后的任务恢复。"""
        payload = asdict(task)
        with self._record_lock:
            job_tasks = self._memory_task_store.setdefault(task.job_id, {})
            job_tasks[task.repo_id] = payload
            if self._redis:
                try:
                    self._redis.hsetkey(
                        self._task_store_key(task.job_id),
                        str(task.repo_id),
                        payload,
                        expiration=self.job_ttl_seconds,
                    )
                except Exception as exc:
                    logger.debug("SyncIndex: 持久化任务失败 %s:%s -> %s", task.job_id, task.repo_id, exc)

    def _remove_task_payload(self, job_id: str, repo_id: int):
        """删除指定任务的持久化记录，避免重复恢复。"""
        with self._record_lock:
            job_tasks = self._memory_task_store.get(job_id)
            if job_tasks and repo_id in job_tasks:
                job_tasks.pop(repo_id, None)
                if not job_tasks:
                    self._memory_task_store.pop(job_id, None)
            if self._redis:
                try:
                    self._redis.hdel(self._task_store_key(job_id), str(repo_id))
                except Exception as exc:
                    logger.debug("SyncIndex: 删除任务持久化记录失败 %s:%s -> %s", job_id, repo_id, exc)

    def _remove_task_store(self, job_id: str):
        """清理作业级别的任务持久化数据。"""
        with self._record_lock:
            self._memory_task_store.pop(job_id, None)
            if self._redis:
                try:
                    self._redis.delete(self._task_store_key(job_id))
                except Exception as exc:
                    logger.debug("SyncIndex: 清理任务持久化失败 %s -> %s", job_id, exc)

    def _record_queue_enqueue(self, task: SyncTask):
        """记录任务入队事件，用于排队观测与恢复。"""
        identifier = self._queue_identifier(task)
        meta = self._build_task_meta(task)
        meta["enqueued_at"] = datetime.utcnow().isoformat()
        with self._record_lock:
            if identifier in self._memory_pending_list:
                self._memory_pending_list.remove(identifier)
            self._memory_pending_list.append(identifier)
            self._memory_pending_meta[identifier] = meta
            self._memory_running_meta.pop(identifier, None)
            if self._redis:
                try:
                    self._redis.lrem(self._pending_list_key, 0, identifier)
                    self._redis.rpush(self._pending_list_key, identifier, expiration=self.job_ttl_seconds)
                    self._redis.hsetkey(self._pending_meta_key, identifier, meta, expiration=self.job_ttl_seconds)
                    self._redis.hdel(self._running_meta_key, identifier)
                except Exception as exc:
                    logger.debug("SyncIndex: 记录入队失败 %s -> %s", identifier, exc)

    def _record_queue_dequeue(self, task: SyncTask):
        """记录任务出队并标记为运行中。"""
        identifier = self._queue_identifier(task)
        started_at = datetime.utcnow().isoformat()
        with self._record_lock:
            if identifier in self._memory_pending_list:
                self._memory_pending_list.remove(identifier)
            meta = self._memory_pending_meta.pop(identifier, None) or self._build_task_meta(task)
            meta["started_at"] = started_at
            self._memory_running_meta[identifier] = meta
            if self._redis:
                try:
                    self._redis.lrem(self._pending_list_key, 0, identifier)
                    pending_meta = self._redis.hget(self._pending_meta_key, identifier)
                    if pending_meta is not None:
                        meta = pending_meta
                    meta["started_at"] = started_at
                    self._redis.hdel(self._pending_meta_key, identifier)
                    self._redis.hsetkey(self._running_meta_key, identifier, meta, expiration=self.job_ttl_seconds)
                except Exception as exc:
                    logger.debug("SyncIndex: 记录出队失败 %s -> %s", identifier, exc)

    def _record_task_completion(self, task: SyncTask):
        """记录任务完成，清理运行队列信息。"""
        identifier = self._queue_identifier(task)
        with self._record_lock:
            self._memory_running_meta.pop(identifier, None)
            if self._redis:
                try:
                    self._redis.hdel(self._running_meta_key, identifier)
                except Exception as exc:
                    logger.debug("SyncIndex: 清理运行状态失败 %s -> %s", identifier, exc)

    def _persist_job_registration(self, record: Dict[str, Any]):
        """同步作业注册信息到Redis与内存索引。"""
        job_id = record.get("job_id")
        wiki_id = record.get("wiki_id")
        if not job_id:
            return
        snapshot = {
            "job_id": job_id,
            "wiki_id": wiki_id,
            "status": record.get("status"),
            "created_at": record.get("created_at"),
            "updated_at": record.get("updated_at"),
        }
        with self._record_lock:
            self._memory_registry[job_id] = snapshot
            if wiki_id:
                self._memory_wiki_jobs[wiki_id] = job_id
            if self._redis:
                try:
                    self._redis.hsetkey(self._job_registry_key, job_id, snapshot, expiration=self.job_ttl_seconds)
                    if wiki_id:
                        self._redis.hsetkey(self._wiki_active_key, wiki_id, job_id, expiration=self.job_ttl_seconds)
                except Exception as exc:
                    logger.debug("SyncIndex: 注册作业写入Redis失败 %s -> %s", job_id, exc)

    def _update_job_registry(self, job_id: str, record: Dict[str, Any]):
        """更新作业注册表中的状态信息。"""
        snapshot = {
            "job_id": job_id,
            "wiki_id": record.get("wiki_id"),
            "status": record.get("status"),
            "updated_at": record.get("updated_at"),
        }
        with self._record_lock:
            self._memory_registry[job_id] = snapshot
            wiki_id = record.get("wiki_id")
            if wiki_id:
                if record.get("status") in self._final_job_statuses:
                    self._memory_wiki_jobs.pop(wiki_id, None)
                else:
                    self._memory_wiki_jobs[wiki_id] = job_id
            if self._redis:
                try:
                    self._redis.hsetkey(self._job_registry_key, job_id, snapshot, expiration=self.job_ttl_seconds)
                    if wiki_id:
                        if record.get("status") in self._final_job_statuses:
                            self._redis.hdel(self._wiki_active_key, wiki_id)
                        else:
                            self._redis.hsetkey(self._wiki_active_key, wiki_id, job_id, expiration=self.job_ttl_seconds)
                except Exception as exc:
                    logger.debug("SyncIndex: 更新作业注册表失败 %s -> %s", job_id, exc)

    def _clear_wiki_active_job(self, wiki_id: Optional[str], job_id: str):
        """在作业完成后移除Wiki与作业的映射关系。"""
        if not wiki_id:
            return
        with self._record_lock:
            mapped = self._memory_wiki_jobs.get(wiki_id)
            if mapped == job_id:
                self._memory_wiki_jobs.pop(wiki_id, None)
            if self._redis:
                try:
                    stored = self._redis.hget(self._wiki_active_key, wiki_id)
                    if stored == job_id:
                        self._redis.hdel(self._wiki_active_key, wiki_id)
                except Exception as exc:
                    logger.debug("SyncIndex: 清理Wiki映射失败 %s -> %s", wiki_id, exc)

    def _cleanup_queue_entries(self, job_id: str):
        """移除指定作业相关的排队与运行记录。"""
        prefix = f"{job_id}:"
        with self._record_lock:
            self._memory_pending_list = [item for item in self._memory_pending_list if not item.startswith(prefix)]
            for mapping in [self._memory_pending_meta, self._memory_running_meta]:
                stale_keys = [key for key in mapping if key.startswith(prefix)]
                for key in stale_keys:
                    mapping.pop(key, None)
            if self._redis:
                try:
                    identifiers = self._redis.lrange(self._pending_list_key, 0, -1)
                    for item in identifiers:
                        if isinstance(item, str) and item.startswith(prefix):
                            self._redis.lrem(self._pending_list_key, 0, item)
                            self._redis.hdel(self._pending_meta_key, item)
                    running_meta = self._redis.hgetall(self._running_meta_key)
                    for key in list(running_meta.keys()):
                        if key.startswith(prefix):
                            self._redis.hdel(self._running_meta_key, key)
                except Exception as exc:
                    logger.debug("SyncIndex: 清理队列记录失败 %s -> %s", job_id, exc)

    def _load_task_payloads(self, job_id: str) -> Dict[int, Dict[str, Any]]:
        """加载作业持久化的任务数据，支持恢复流程。"""
        with self._record_lock:
            if job_id in self._memory_task_store:
                return {k: v.copy() for k, v in self._memory_task_store[job_id].items()}
        if self._redis:
            try:
                raw = self._redis.hgetall(self._task_store_key(job_id))
                if raw:
                    return {int(repo_id): data for repo_id, data in raw.items()}
            except Exception as exc:
                logger.debug("SyncIndex: 加载任务持久化失败 %s -> %s", job_id, exc)
        return {}

    def _deserialize_task(self, payload: Dict[str, Any]) -> Optional[SyncTask]:
        """将持久化字典还原为SyncTask实例。"""
        try:
            return SyncTask(**payload)
        except Exception as exc:
            logger.warning("SyncIndex: 反序列化任务失败 %s -> %s", payload, exc)
            return None

    async def _restore_pending_tasks(self):
        """在服务启动时恢复Redis中尚未完成的任务。"""
        if self._restored_once:
            return
        self._restored_once = True

        active_jobs: Dict[str, Dict[str, Any]] = {}
        if self._redis:
            try:
                active_jobs = self._redis.hgetall(self._job_registry_key)
            except Exception as exc:
                logger.debug("SyncIndex: 读取作业注册表失败 -> %s", exc)
                active_jobs = {}
        if not active_jobs:
            with self._record_lock:
                active_jobs = {job_id: data.copy() for job_id, data in self._memory_registry.items()}

        resume_updates: Dict[str, Dict[int, Dict[str, Any]]] = {}

        for job_id, snapshot in active_jobs.items():
            status = (snapshot or {}).get("status")
            if status in self._final_job_statuses:
                continue
            job_record = self.get_job_status(job_id)
            if not job_record:
                continue
            tasks = {int(item.get("repo_id")): item for item in job_record.get("tasks", []) if item.get("repo_id") is not None}
            payloads = self._load_task_payloads(job_id)
            if not payloads:
                continue
            for repo_id, payload in payloads.items():
                task_status = (tasks.get(repo_id) or {}).get("status")
                if task_status in self._final_task_statuses:
                    continue
                task_obj = self._deserialize_task(payload)
                if not task_obj:
                    continue
                self._persist_task_payload(task_obj)
                self._ensure_cancel_flag(task_obj.job_id)
                await self._queue.put(task_obj)
                self._record_queue_enqueue(task_obj)
                meta_updates = resume_updates.setdefault(job_id, {})
                meta_updates[repo_id] = {
                    "status": "queued",
                    "message": "重启恢复，等待同步",
                }

        for job_id, updates in resume_updates.items():
            await self._update_job_record(job_id, {
                "status": "queued",
                "updated_at": datetime.utcnow().isoformat(),
            }, task_updates=updates)

    def get_active_job(self, wiki_id: str) -> Optional[Dict[str, Any]]:
        """获取指定Wiki正在执行的作业信息，避免重复提交。"""
        job_id: Optional[str] = None
        if self._redis:
            try:
                job_id = self._redis.hget(self._wiki_active_key, wiki_id)
            except Exception:
                job_id = None
        if not job_id:
            with self._record_lock:
                job_id = self._memory_wiki_jobs.get(wiki_id)
        if not job_id:
            return None

        record = self.get_job_status(job_id)
        if not record:
            self._clear_wiki_active_job(wiki_id, job_id)
            return None

        if record.get("status") in self._final_job_statuses:
            self._clear_wiki_active_job(wiki_id, job_id)
            return None

        tasks = record.get("tasks", [])
        has_active = any(task.get("status") not in self._final_task_statuses for task in tasks)
        if not has_active:
            self._clear_wiki_active_job(wiki_id, job_id)
            return None
        return record

    def get_queue_snapshot(self) -> Dict[str, Any]:
        """返回排队与运行任务的快照，供仪表盘使用。"""
        redis_client = self._get_redis_client()
        pending_order: List[str] = []
        pending_meta: Dict[str, Any] = {}
        running_meta: Dict[str, Any] = {}

        if redis_client:
            try:
                pending_order = redis_client.lrange(self._pending_list_key, 0, -1)
                pending_meta = redis_client.hgetall(self._pending_meta_key)
                running_meta = redis_client.hgetall(self._running_meta_key)
            except Exception as exc:
                logger.debug("SyncIndex: 读取队列快照失败 -> %s", exc)
                pending_order = []
                pending_meta = {}
                running_meta = {}

        if not pending_order:
            with self._record_lock:
                pending_order = list(self._memory_pending_list)
                pending_meta = {k: v.copy() for k, v in self._memory_pending_meta.items()}
                running_meta = {k: v.copy() for k, v in self._memory_running_meta.items()}

        pending_tasks = [
            {**pending_meta.get(identifier, {}), "queue_key": identifier}
            for identifier in pending_order
        ]
        running_tasks = [
            {**running_meta.get(identifier, {}), "queue_key": identifier}
            for identifier in running_meta
        ]

        with self._record_lock:
            active_jobs = [data.copy() for data in self._memory_registry.values() if data.get("status") not in self._final_job_statuses]

        if redis_client:
            try:
                remote_jobs = redis_client.hgetall(self._job_registry_key)
                if remote_jobs:
                    active_jobs = [data for data in remote_jobs.values() if data.get("status") not in self._final_job_statuses]
            except Exception:
                pass

        active_slots, available_slots_global, available_slots_local = self._get_active_slot_usage()
        metrics = {
            "global_limit": self._global_limit,
            "configured_global_limit": self._configured_global_limit,
            "instance_limit": self._configured_instance_limit,
            "local_limit": self._local_limit,
            "thread_pool_limit": self.max_workers,
            "active_slots": active_slots,
            "available_slots": available_slots_local,
            "available_slots_global": available_slots_global,
            "slot_ttl_seconds": self._global_slot_ttl,
            "job_ttl_seconds": self.job_ttl_seconds,
            "pending_count": len(pending_tasks),
            "running_count": len(running_tasks),
            "active_job_count": len(active_jobs),
            "redis_status": "connected" if redis_client else "fallback",
        }

        redis_keys = {
            "global_slot_key": self._global_slot_key,
            "waiting_queue_key": self._redis_waiting_key,
            "pending_list_key": self._pending_list_key,
            "pending_meta_key": self._pending_meta_key,
            "running_meta_key": self._running_meta_key,
            "job_registry_key": self._job_registry_key,
            "wiki_active_key": self._wiki_active_key,
            "task_store_prefix": self._task_store_prefix,
            "latest_job_key": "wiki:sync:latest:{wiki_id}",
            "summary_cache_key": "wiki:sync:summary:{wiki_id}",
            "summary_refresh_key": "wiki:sync:summary:refreshing:{wiki_id}",
        }

        return {
            "pending": pending_tasks,
            "running": running_tasks,
            "active_jobs": active_jobs,
            "metrics": metrics,
            "redis_keys": redis_keys,
        }


_sync_job_manager: Optional[SyncIndexJobManager] = None


def init_sync_job_manager(db_service: DatabaseService, docchain_manager: DocChainManager,
                          **kwargs) -> SyncIndexJobManager:
    global _sync_job_manager
    if _sync_job_manager is None:
        _sync_job_manager = SyncIndexJobManager(db_service, docchain_manager, **kwargs)
    return _sync_job_manager


def get_sync_job_manager() -> SyncIndexJobManager:
    if _sync_job_manager is None:
        raise RuntimeError("SyncIndexJobManager not initialized")
    return _sync_job_manager
