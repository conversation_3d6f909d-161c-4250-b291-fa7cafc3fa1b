#!/usr/bin/env python3
"""
Docker环境下的优雅停止机制

这个模块确保在Docker容器停止时：
1. 正确释放所有任务锁
2. 删除实例信息
3. 重置processing任务状态
4. 处理信号和清理资源
"""

import signal
import asyncio
import logging
import sys
from typing import Optional
from datetime import datetime

from api.wiki.wiki_job_manager import get_job_manager, WikiJobManager
from api.database.base import session_scope
from api.model.wiki_job import WikiJob
from api.service.wiki_service import update_job
from sqlmodel import select

logger = logging.getLogger(__name__)

class DockerGracefulShutdownHandler:
    """Docker环境下的优雅停止处理器"""
    
    def __init__(self):
        self.shutdown_event = asyncio.Event()
        self.job_manager: Optional[WikiJobManager] = None
        self._signal_handlers_installed = False
        
    def setup_signal_handlers(self):
        """设置信号处理器"""
        if self._signal_handlers_installed:
            return
            
        def signal_handler(signum, frame):
            """信号处理函数"""
            signal_name = signal.Signals(signum).name
            logger.info(f"收到信号 {signal_name} ({signum})，开始优雅停止...")
            
            # 设置停止事件
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self._graceful_shutdown())
                else:
                    asyncio.run(self._graceful_shutdown())
            except Exception as e:
                logger.error(f"处理停止信号时出错: {e}")
                self._force_exit(1)
        
        # 注册信号处理器
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)
        
        # 忽略SIGPIPE（避免因客户端断开连接导致的问题）
        signal.signal(signal.SIGPIPE, signal.SIG_IGN)
        
        self._signal_handlers_installed = True
        logger.info("Docker信号处理器已设置")
    
    def set_job_manager(self, job_manager: WikiJobManager):
        """设置JobManager引用"""
        self.job_manager = job_manager
    
    async def _graceful_shutdown(self):
        """执行优雅停止"""
        if self.shutdown_event.is_set():
            logger.warning("优雅停止已在进行中，跳过重复调用")
            return
            
        self.shutdown_event.set()
        
        try:
            logger.info("开始Docker优雅停止流程...")
            
            # 步骤1：停止JobManager（会释放锁和清理实例）
            if self.job_manager:
                logger.info("停止WikiJobManager...")
                await self.job_manager.stop()
                logger.info("WikiJobManager已停止")
            else:
                # 如果没有JobManager引用，尝试获取全局实例
                try:
                    job_manager = get_job_manager()
                    await job_manager.stop()
                    logger.info("全局WikiJobManager已停止")
                except Exception as e:
                    logger.warning(f"无法获取JobManager进行停止: {e}")
            
            # 步骤2：额外的清理操作 - 确保processing任务状态正确
            await self._cleanup_processing_jobs_on_shutdown()
            
            # 步骤3：最终检查和清理
            await self._final_cleanup_check()
            
            logger.info("Docker优雅停止流程完成")
            
        except Exception as e:
            logger.error(f"优雅停止过程中出错: {e}")
        finally:
            # 确保程序退出
            self._force_exit(0)
    
    async def _cleanup_processing_jobs_on_shutdown(self):
        """清理processing状态的任务"""
        try:
            logger.info("检查并清理processing状态任务...")
            
            with session_scope() as session:
                # 查找所有processing状态的任务
                processing_jobs_query = select(WikiJob).where(
                    WikiJob.status == "processing"
                )
                processing_jobs = session.exec(processing_jobs_query).all()
                
                if not processing_jobs:
                    logger.info("没有发现processing状态的任务")
                    return
                
                logger.info(f"发现 {len(processing_jobs)} 个processing状态的任务，准备重置...")
                
                reset_count = 0
                for job in processing_jobs:
                    try:
                        # 根据阶段决定重置策略
                        if job.stage == "upload":
                            # upload阶段重置为pending_resume
                            update_job(session, job.id, 
                                     status="pending_resume",
                                     stage="queue",
                                     stage_message="实例停止时重置，等待重新分配")
                            reset_count += 1
                            logger.debug(f"任务 {job.id[:8]}... 从upload阶段重置为pending_resume")
                        else:
                            # 其他阶段重置为pending
                            update_job(session, job.id,
                                     status="pending", 
                                     stage="queue",
                                     stage_message="实例停止时重置，等待重新分配")
                            reset_count += 1
                            logger.debug(f"任务 {job.id[:8]}... 从{job.stage}阶段重置为pending")
                            
                    except Exception as job_error:
                        logger.error(f"重置任务 {job.id} 状态失败: {job_error}")
                
                session.commit()
                logger.info(f"成功重置了 {reset_count} 个processing任务的状态")
                
        except Exception as e:
            logger.error(f"清理processing任务时出错: {e}")
    
    async def _final_cleanup_check(self):
        """最终清理检查"""
        try:
            logger.info("执行最终清理检查...")
            
            # 这里可以添加额外的清理逻辑，比如：
            # - 检查是否还有残留的锁
            # - 清理临时文件
            # - 关闭数据库连接池
            # - 等等
            
            logger.info("最终清理检查完成")
            
        except Exception as e:
            logger.error(f"最终清理检查时出错: {e}")
    
    def _force_exit(self, exit_code: int):
        """强制退出"""
        logger.info(f"程序即将退出，退出码: {exit_code}")
        try:
            # 给一点时间让日志输出完成
            import time
            time.sleep(0.1)
        finally:
            sys.exit(exit_code)
    
    async def wait_for_shutdown(self):
        """等待停止信号"""
        await self.shutdown_event.wait()

# 全局实例
_shutdown_handler: Optional[DockerGracefulShutdownHandler] = None

def get_shutdown_handler() -> DockerGracefulShutdownHandler:
    """获取全局停止处理器"""
    global _shutdown_handler
    if _shutdown_handler is None:
        _shutdown_handler = DockerGracefulShutdownHandler()
    return _shutdown_handler

def setup_docker_graceful_shutdown(job_manager: Optional[WikiJobManager] = None):
    """设置Docker优雅停止机制"""
    handler = get_shutdown_handler()
    handler.setup_signal_handlers()
    
    if job_manager:
        handler.set_job_manager(job_manager)
    
    logger.info("Docker优雅停止机制已设置")
    return handler

async def wait_for_shutdown_signal():
    """等待停止信号（用于主程序）"""
    handler = get_shutdown_handler()
    await handler.wait_for_shutdown()

# 使用示例：
"""
from api.wiki.docker_graceful_shutdown import setup_docker_graceful_shutdown, wait_for_shutdown_signal

# 在应用启动时设置
job_manager = get_job_manager()
setup_docker_graceful_shutdown(job_manager)

# 在主程序中等待停止信号
await wait_for_shutdown_signal()
""" 