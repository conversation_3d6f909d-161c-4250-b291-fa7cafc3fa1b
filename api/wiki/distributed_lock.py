import os
import socket
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from contextlib import contextmanager
import json
import time
import uuid

# 添加进程检测依赖
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    logging.warning("psutil not available, process liveness check disabled")

from api.database.base import session_scope
from api.model.job_manager_lock import JobManagerInstance, JobLock, GlobalJobState
from sqlmodel import select
from sqlalchemy.exc import IntegrityError
from sqlalchemy import and_, or_, text

logger = logging.getLogger(__name__)

class DistributedJobLockService:
    """分布式任务锁服务 - 重新设计版本，更安全的锁机制"""
    
    def __init__(self, lock_timeout_minutes: int = 90):  # 从配置读取默认值
        self.instance_id = None
        self.hostname = socket.gethostname()
        self.pid = os.getpid()
        self.lock_timeout_minutes = lock_timeout_minutes
        
        # 从配置读取参数
        from api.config import get_wiki_jobs_config
        config = get_wiki_jobs_config()
        
        # 读取是否启用分布式锁配置
        self.enable_distributed_lock = config.get('enable_distributed_lock', True)
        
        self.heartbeat_interval = 30  # 心跳间隔30秒
        self.crash_detection_threshold = config.get('heartbeat_timeout_minutes', 3)  # 配置的心跳超时阈值缩短到3分钟
        self.cleanup_interval = config.get('cleanup_interval_seconds', 60)  # 配置的清理间隔缩短到1分钟
        self.last_cleanup_time = 0
        
        # 本地开发模式下的提示
        if not self.enable_distributed_lock:
            logger.info(f"本地开发模式启动，不注册到数据库 (hostname: {self.hostname}, PID: {self.pid})")
            self.instance_id = f"local-dev-{self.hostname}-{self.pid}"  # 生成一个本地ID用于日志
        
    async def register_instance(self, max_concurrent_jobs: int = 3) -> str:
        """注册实例 - 简化的注册流程（启动前已做全面清理）"""
        # 首先生成或确保instance_id存在
        if not self.instance_id:
            self.instance_id = f"{socket.gethostname()}-{os.getpid()}-{str(uuid.uuid4())[:8]}"
        
        if not self.enable_distributed_lock:
            # 本地开发模式，使用简单的instance_id
            logger.info(f"本地开发模式: 使用instance_id {self.instance_id}")
            return self.instance_id
            
        try:
            current_time = datetime.now()
            
            with session_scope() as session:
                # 简化清理：只清理同主机同进程的旧实例记录
                cleanup_query = select(JobManagerInstance).where(
                    JobManagerInstance.hostname == self.hostname,
                    JobManagerInstance.pid == self.pid
                )
                old_instances = session.exec(cleanup_query).all()
                
                for old_instance in old_instances:
                    if old_instance.instance_id != self.instance_id:
                        logger.warning(f"清理同主机同进程的旧实例: {old_instance.instance_id}")
                        await self._cleanup_dead_instance(session, old_instance, "同主机同进程的旧实例")
                
                # 检查是否已存在当前实例
                existing_query = select(JobManagerInstance).where(
                    JobManagerInstance.instance_id == self.instance_id
                )
                existing_instance = session.exec(existing_query).first()
                
                if existing_instance:
                    # 更新现有实例
                    existing_instance.status = "active"
                    existing_instance.last_heartbeat = current_time
                    existing_instance.max_concurrent_jobs = max_concurrent_jobs
                    existing_instance.current_jobs = 0
                    existing_instance.updated_time = current_time
                    session.add(existing_instance)
                    logger.info(f"更新现有实例注册: {self.instance_id}")
                else:
                    # 创建新实例
                    new_instance = JobManagerInstance(
                        instance_id=self.instance_id,
                        hostname=self.hostname,
                        pid=self.pid,
                        status="active",
                        max_concurrent_jobs=max_concurrent_jobs,
                        current_jobs=0,
                        last_heartbeat=current_time,
                        created_time=current_time,
                        updated_time=current_time
                    )
                    session.add(new_instance)
                    logger.info(f"注册新实例: {self.instance_id}")
                
                session.commit()
                
                # 移除严格心跳检测，避免重复清理
                # await self._strict_heartbeat_check()  # 已在启动前做了全面清理
                
                return self.instance_id
                
        except Exception as e:
            logger.error(f"注册实例失败: {e}")
            # 即使注册失败，也要有一个instance_id
            if not self.instance_id:
                self.instance_id = f"fallback-{socket.gethostname()}-{os.getpid()}-{int(time.time())}"
            return self.instance_id

    async def _cleanup_confirmed_dead_instances(self, session):
        """只清理确认死亡的实例（启动时调用）"""
        if not PSUTIL_AVAILABLE:
            return
        
        try:
            # 获取本机的所有实例
            host_instances_query = select(JobManagerInstance).where(
                JobManagerInstance.hostname == self.hostname
            )
            host_instances = session.exec(host_instances_query).all()
            
            confirmed_dead = []
            for instance in host_instances:
                # 跳过当前进程
                if instance.pid == self.pid:
                    continue
                
                # 严格检查进程是否存在
                if not self._is_process_really_alive(instance.pid):
                    confirmed_dead.append(instance)
                    logger.info(f"发现确认死亡的实例: {instance.instance_id} (PID: {instance.pid})")
            
            # 清理确认死亡的实例
            for instance in confirmed_dead:
                await self._cleanup_dead_instance(session, instance, "confirmed_dead")
            
            if confirmed_dead:
                session.commit()
                logger.info(f"清理了 {len(confirmed_dead)} 个确认死亡的实例")
                
        except Exception as e:
            logger.error(f"清理确认死亡实例失败: {e}")

    def _is_process_really_alive(self, pid: int) -> bool:
        """严格检查本机进程是否存活"""
        if not PSUTIL_AVAILABLE:
            return True
        
        try:
            process = psutil.Process(pid)
            # 检查进程是否存在且状态正常
            if process.is_running() and process.status() != psutil.STATUS_ZOMBIE:
                return True
            return False
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            return False

    async def unregister_instance(self):
        """注销JobManager实例"""
        if not self.instance_id:
            return
            
        # 本地开发模式下跳过数据库操作
        if not self.enable_distributed_lock:
            logger.info(f"本地开发模式: 跳过实例注销 ({self.instance_id})")
            return
            
        try:
            with session_scope() as session:
                # 更新实例状态为stopped
                query = select(JobManagerInstance).where(
                    JobManagerInstance.instance_id == self.instance_id
                )
                instance = session.exec(query).first()
                if instance:
                    instance.status = "stopped"
                    instance.last_heartbeat = datetime.now()  # 最后一次心跳
                    session.add(instance)
                    
                    # 释放该实例持有的所有锁
                    lock_query = select(JobLock).where(
                        JobLock.instance_id == self.instance_id
                    )
                    locks = session.exec(lock_query).all()
                    for lock in locks:
                        # 标记为优雅停止，而不是直接删除
                        await self._graceful_release_lock(session, lock)
                    
                    session.commit()
                    logger.info(f"JobManager实例 {self.instance_id} 已优雅关闭，释放了 {len(locks)} 个锁")
                    
        except Exception as e:
            logger.error(f"注销JobManager实例失败: {e}")

    async def update_heartbeat(self, current_jobs: int = 0):
        """更新实例心跳 - 增加频率和严格性"""
        if not self.instance_id:
            return
            
        # 本地开发模式下跳过心跳更新
        if not self.enable_distributed_lock:
            logger.debug(f"本地开发模式: 跳过心跳更新 ({self.instance_id})")
            return
            
        try:
            # 增加心跳频率，更及时地更新状态
            current_time = datetime.now()
            
            with session_scope() as session:
                query = select(JobManagerInstance).where(
                    JobManagerInstance.instance_id == self.instance_id
                )
                instance = session.exec(query).first()
                if instance:
                    # 更新心跳时间和任务数
                    instance.last_heartbeat = current_time
                    instance.current_jobs = current_jobs
                    instance.status = "active"  # 确保状态是活跃的
                    instance.updated_time = current_time
                    session.add(instance)
                    session.commit()
                    
                    logger.debug(f"心跳更新成功: 实例 {self.instance_id}, 当前任务数: {current_jobs}")
                else:
                    # 实例在数据库中不存在，可能被误删，尝试重新注册
                    logger.warning(f"实例 {self.instance_id} 在数据库中不存在，可能被误删，尝试重新注册")
                    try:
                        # 重新注册实例，但保持原有的instance_id
                        old_instance_id = self.instance_id
                        new_instance_id = await self.register_instance()
                        if new_instance_id != old_instance_id:
                            logger.warning(f"实例ID发生变化: {old_instance_id} -> {new_instance_id}")
                        logger.info(f"实例 {self.instance_id} 重新注册成功")
                    except Exception as re_reg_error:
                        logger.error(f"重新注册实例失败: {re_reg_error}")
                    
        except Exception as e:
            logger.error(f"更新心跳失败: {e}")

    async def acquire_job_lock(self, job_id: str, operation: str = "process") -> bool:
        """获取任务锁 - 更保守的策略，避免误抢"""
        if not self.instance_id:
            logger.error("实例未注册，无法获取锁")
            return False
            
        # 本地开发模式下总是成功获取锁
        if not self.enable_distributed_lock:
            logger.debug(f"本地开发模式: 假设成功获取锁 {job_id} ({self.instance_id})")
            return True
            
        try:
            with session_scope() as session:
                # 首先清理明确过期的锁
                await self._cleanup_expired_locks(session)
                
                # 检查是否已有锁
                existing_query = select(JobLock).where(JobLock.job_id == job_id)
                existing_lock = session.exec(existing_query).first()
                
                if existing_lock:
                    # 如果是当前实例持有的锁，延长时间
                    if existing_lock.instance_id == self.instance_id:
                        existing_lock.expires_at = datetime.now() + timedelta(minutes=self.lock_timeout_minutes)
                        existing_lock.updated_time = datetime.now()
                        session.add(existing_lock)
                        session.commit()
                        logger.debug(f"延长任务 {job_id} 的锁时间")
                        return True
                    
                    # 检查锁是否真的应该被抢夺
                    can_acquire = await self._can_safely_acquire_lock(session, existing_lock)
                    if not can_acquire:
                        logger.debug(f"任务 {job_id} 锁被安全保护，无法获取")
                        return False
                    
                    # 可以安全获取，先删除旧锁
                    logger.warning(f"强制获取任务 {job_id} 的锁，原持有者: {existing_lock.instance_id}")
                    session.delete(existing_lock)
                    session.flush()  # 确保删除操作立即执行
                
                # 尝试获取新锁前，再次检查当前实例的并发限制（关键修复）
                current_instance_locks_query = select(JobLock).where(
                    JobLock.instance_id == self.instance_id
                )
                current_locks = session.exec(current_instance_locks_query).all()
                current_lock_count = len(current_locks)
                
                # 从数据库获取实例的最大并发数
                instance_query = select(JobManagerInstance).where(
                    JobManagerInstance.instance_id == self.instance_id
                )
                instance = session.exec(instance_query).first()
                
                if instance and current_lock_count >= instance.max_concurrent_jobs:
                    logger.warning(f"获取锁失败：当前实例已达并发限制 {current_lock_count}/{instance.max_concurrent_jobs}")
                    return False
                
                try:
                    new_lock = JobLock(
                        job_id=job_id,
                        instance_id=self.instance_id,
                        hostname=self.hostname,
                        operation=operation,
                        expires_at=datetime.now() + timedelta(minutes=self.lock_timeout_minutes)
                    )
                    session.add(new_lock)
                    session.commit()
                    logger.info(f"成功获取任务 {job_id} 的锁，当前实例锁数量: {current_lock_count + 1}/{instance.max_concurrent_jobs if instance else 'unknown'}")
                    return True
                    
                except IntegrityError:
                    # 并发情况下锁被其他实例获取
                    session.rollback()
                    logger.warning(f"任务 {job_id} 锁获取失败，被其他实例抢占")
                    return False
                    
        except Exception as e:
            logger.error(f"获取任务锁失败: {e}")
            return False

    async def _can_safely_acquire_lock(self, session, existing_lock: JobLock) -> bool:
        """判断是否可以安全地获取已被占用的锁"""
        # 检查锁是否已过期（时间判断）
        current_time = datetime.now()
        if existing_lock.expires_at and current_time > existing_lock.expires_at:
            logger.info(f"锁已过期，可以安全获取: {existing_lock.job_id}")
            return True
        
        # 检查持有锁的实例状态
        lock_holder_query = select(JobManagerInstance).where(
            JobManagerInstance.instance_id == existing_lock.instance_id
        )
        lock_holder = session.exec(lock_holder_query).first()
        
        if not lock_holder:
            logger.info(f"锁持有者实例不存在，可以安全获取: {existing_lock.job_id}")
            return True
        
        # 检查实例状态
        if lock_holder.status in ["stopped", "crashed"]:
            logger.info(f"锁持有者实例已停止/崩溃，可以安全获取: {existing_lock.job_id}")
            return True
        
        # 检查心跳（更aggressive的策略）
        if lock_holder.last_heartbeat:
            time_since_heartbeat = current_time - lock_holder.last_heartbeat
            
            # 使用配置的心跳超时时间
            from api.config import get_wiki_jobs_config
            config = get_wiki_jobs_config()
            timeout_minutes = config.get('heartbeat_timeout_minutes', 1)  # 读取配置文件中的心跳超时时间
            timeout_threshold = timedelta(minutes=timeout_minutes)
            is_local_instance = (lock_holder.hostname == self.hostname)

            if time_since_heartbeat > timeout_threshold:
                # 如果是本机实例，再次确认进程状态
                if is_local_instance and PSUTIL_AVAILABLE:
                    if self._is_process_really_alive(lock_holder.pid):
                        logger.warning(f"本机实例心跳超时但进程存活，考虑网络问题，允许获取锁: {existing_lock.job_id}")
                        # 即使进程存活，心跳超时也允许获取锁（可能是网络问题）
                        return True
                
                logger.warning(f"锁持有者心跳超时{timeout_minutes}分钟，可以获取锁: {existing_lock.job_id}")
                return True
        else:
            # 没有心跳记录，立即获取锁
            logger.warning(f"锁持有者无心跳记录，可以安全获取: {existing_lock.job_id}")
            return True
        
        # 默认不获取锁（保守策略）
        logger.debug(f"锁持有者状态正常，不获取锁: {existing_lock.job_id}")
        return False

    async def release_job_lock(self, job_id: str):
        """释放任务锁"""
        if not self.instance_id:
            return
            
        # 本地开发模式下跳过锁释放
        if not self.enable_distributed_lock:
            logger.debug(f"本地开发模式: 跳过释放锁 {job_id} ({self.instance_id})")
            return
            
        try:
            with session_scope() as session:
                query = select(JobLock).where(
                    and_(
                        JobLock.job_id == job_id,
                        JobLock.instance_id == self.instance_id
                    )
                )
                lock = session.exec(query).first()
                if lock:
                    session.delete(lock)
                    session.commit()
                    logger.info(f"释放任务 {job_id} 的锁")
                    
        except Exception as e:
            logger.error(f"释放任务锁失败: {e}")

    async def force_release_job_lock(self, job_id: str, reason: str = "force_cleanup"):
        """强制释放任务锁，无论是否是当前实例持有"""
        if not self.enable_distributed_lock:
            logger.debug(f"本地开发模式: 跳过强制释放锁 {job_id}")
            return
            
        try:
            with session_scope() as session:
                query = select(JobLock).where(JobLock.job_id == job_id)
                lock = session.exec(query).first()
                if lock:
                    logger.warning(f"强制释放任务 {job_id} 的锁，原持有者: {lock.instance_id}，原因: {reason}")
                    await self._graceful_release_lock(session, lock)
                    session.commit()
                else:
                    logger.debug(f"任务 {job_id} 没有锁，无需强制释放")
                    
        except Exception as e:
            logger.error(f"强制释放任务锁失败: {e}")

    async def get_job_lock_info(self, job_id: str) -> Optional[Dict[str, Any]]:
        """获取任务锁信息"""
        if not self.enable_distributed_lock:
            return {"instance_id": self.instance_id, "operation": "local_dev", "acquired_time": datetime.now()}
            
        try:
            with session_scope() as session:
                lock_query = select(JobLock).where(JobLock.job_id == job_id)
                lock = session.exec(lock_query).first()
                
                if lock:
                    return {
                        "job_id": lock.job_id,
                        "instance_id": lock.instance_id,
                        "operation": lock.operation,
                        "acquired_time": lock.acquired_time,
                        "expires_at": lock.expires_at,
                        "updated_time": lock.updated_time
                    }
                return None
                
        except Exception as e:
            logger.error(f"获取锁信息失败 {job_id}: {e}")
            return None

    async def check_job_lock(self, job_id: str) -> bool:
        """检查当前实例是否持有指定任务的锁"""
        if not self.enable_distributed_lock:
            return True  # 本地开发模式假设总是持有锁
            
        try:
            with session_scope() as session:
                lock_query = select(JobLock).where(
                    and_(JobLock.job_id == job_id, JobLock.instance_id == self.instance_id)
                )
                lock = session.exec(lock_query).first()
                
                if lock and lock.expires_at > datetime.now():
                    return True
                return False
                
        except Exception as e:
            logger.error(f"检查锁状态失败 {job_id}: {e}")
            return False

    async def _cleanup_expired_locks(self, session):
        """清理明确过期的锁"""
        try:
            current_time = datetime.now()
            # 只清理超过过期时间1小时的锁，避免时钟偏差问题
            expired_threshold = current_time - timedelta(hours=1)
            
            expired_query = select(JobLock).where(JobLock.expires_at < expired_threshold)
            expired_locks = session.exec(expired_query).all()
            
            for lock in expired_locks:
                session.delete(lock)
                logger.info(f"清理明确过期锁: job_id={lock.job_id}, instance_id={lock.instance_id}")
            
            if expired_locks:
                session.commit()
                
        except Exception as e:
            logger.error(f"清理过期锁失败: {e}")

    async def _graceful_release_lock(self, session, lock: JobLock):
        """优雅释放锁"""
        try:
            # 更新全局任务状态为可重新分配
            global_job_query = select(GlobalJobState).where(
                GlobalJobState.job_id == lock.job_id
            )
            global_job = session.exec(global_job_query).first()
            
            if global_job and global_job.global_status == "processing":
                global_job.global_status = "reassignable"
                global_job.processing_instance_id = None
                global_job.updated_time = datetime.now()
                
                # 更新元数据
                metadata = {}
                if global_job.job_metadata:
                    try:
                        metadata = json.loads(global_job.job_metadata)
                    except:
                        pass
                
                metadata.update({
                    "graceful_release": True,
                    "release_time": datetime.now().isoformat(),
                    "release_reason": "instance_shutdown"
                })
                global_job.job_metadata = json.dumps(metadata)
                session.add(global_job)
            
            # 删除锁
            session.delete(lock)
            logger.info(f"优雅释放锁: {lock.job_id}")
            
        except Exception as e:
            logger.error(f"优雅释放锁失败: {e}")

    async def get_available_instances(self) -> List[JobManagerInstance]:
        """获取可用的JobManager实例"""
        # 本地开发模式下返回空列表
        if not self.enable_distributed_lock:
            logger.debug(f"本地开发模式: 返回空的可用实例列表")
            return []
            
        try:
            with session_scope() as session:
                # 获取活跃实例（心跳在5分钟内）
                cutoff_time = datetime.now() - timedelta(minutes=self.crash_detection_threshold)
                
                active_query = select(JobManagerInstance).where(
                    and_(
                        JobManagerInstance.status == "active",
                        JobManagerInstance.last_heartbeat > cutoff_time
                    )
                )
                active_instances = session.exec(active_query).all()
                
                return active_instances
                
        except Exception as e:
            logger.error(f"获取可用实例失败: {e}")
            return []

    async def update_global_job_state(self, job_id: str, global_status: str, metadata: Dict[str, Any] = None):
        """更新全局任务状态"""
        # 本地开发模式下跳过全局状态更新
        if not self.enable_distributed_lock:
            logger.debug(f"本地开发模式: 跳过全局状态更新 {job_id} -> {global_status}")
            return
            
        try:
            with session_scope() as session:
                query = select(GlobalJobState).where(GlobalJobState.job_id == job_id)
                state = session.exec(query).first()
                
                if state:
                    state.global_status = global_status
                    state.processing_instance_id = self.instance_id if global_status == "processing" else None
                    if global_status in ["completed", "failed"]:
                        state.last_processing_instance = self.instance_id
                    if metadata:
                        # 合并现有元数据
                        existing_metadata = {}
                        if state.job_metadata:
                            try:
                                existing_metadata = json.loads(state.job_metadata)
                            except:
                                pass
                        existing_metadata.update(metadata)
                        state.job_metadata = json.dumps(existing_metadata)
                    state.updated_time = datetime.now()
                    session.add(state)
                else:
                    state = GlobalJobState(
                        job_id=job_id,
                        global_status=global_status,
                        processing_instance_id=self.instance_id if global_status == "processing" else None,
                        job_metadata=json.dumps(metadata) if metadata else None
                    )
                    session.add(state)
                
                session.commit()
                logger.debug(f"更新任务 {job_id} 全局状态为: {global_status}")
                
        except Exception as e:
            logger.error(f"更新全局任务状态失败: {e}")

    async def get_global_job_state(self, job_id: str) -> Optional[dict]:
        """
        获取全局任务状态
        
        Returns:
            dict: 包含任务状态信息的字典，避免Session绑定问题
        """
        # 本地开发模式下返回None
        if not self.enable_distributed_lock:
            logger.debug(f"本地开发模式: 返回空的全局任务状态 {job_id}")
            return None
            
        try:
            with session_scope() as session:
                query = select(GlobalJobState).where(GlobalJobState.job_id == job_id)
                global_job_state = session.exec(query).first()
                
                if global_job_state:
                    # 在Session内立即提取所有属性，返回字典避免Session绑定问题
                    return {
                        "job_id": global_job_state.job_id,
                        "global_status": global_job_state.global_status,
                        "processing_instance_id": global_job_state.processing_instance_id,
                        "created_time": global_job_state.created_time,
                        "updated_time": global_job_state.updated_time,
                        "job_metadata": global_job_state.job_metadata
                    }
                return None
                
        except Exception as e:
            logger.error(f"获取全局任务状态失败: {e}")
            return None

    async def get_global_active_jobs_count(self) -> int:
        """获取全局活跃任务数量"""
        # 本地开发模式下返回0
        if not self.enable_distributed_lock:
            logger.debug(f"本地开发模式: 返回0个全局活跃任务")
            return 0
            
        try:
            with session_scope() as session:
                query = select(GlobalJobState).where(
                    GlobalJobState.global_status == "processing"
                )
                active_jobs = session.exec(query).all()
                return len(active_jobs)
                
        except Exception as e:
            logger.error(f"获取全局活跃任务数失败: {e}")
            return 0

    @contextmanager
    def job_lock_context(self, job_id: str, operation: str = "process"):
        """任务锁上下文管理器"""
        lock_acquired = False
        try:
            # 获取锁
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            lock_acquired = loop.run_until_complete(self.acquire_job_lock(job_id, operation))
            
            if not lock_acquired:
                raise RuntimeError(f"无法获取任务 {job_id} 的锁")
            
            yield lock_acquired
            
        finally:
            # 释放锁
            if lock_acquired:
                try:
                    loop = asyncio.get_event_loop()
                    loop.run_until_complete(self.release_job_lock(job_id))
                except:
                    # 如果事件循环已关闭，创建新的
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(self.release_job_lock(job_id))
                finally:
                    loop.close()

    async def cleanup_crashed_instances(self):
        """清理崩溃实例 - 更严格的清理策略"""
        # 本地开发模式下跳过清理
        if not self.enable_distributed_lock:
            logger.debug(f"本地开发模式: 跳过实例清理 ({self.instance_id})")
            return
            
        current_time = time.time()
        
        # 限制清理频率，避免过于频繁的清理
        if current_time - self.last_cleanup_time < self.cleanup_interval:
            return
        
        self.last_cleanup_time = current_time
        
        try:
            with session_scope() as session:
                # 获取所有实例
                all_instances_query = select(JobManagerInstance)
                all_instances = session.exec(all_instances_query).all()
                
                # 选举管理实例
                elected_manager = self._elect_manager_instance([inst for inst in all_instances if inst.status == "active"])
                is_manager = (elected_manager and elected_manager.instance_id == self.instance_id)
                
                if not is_manager:
                    logger.debug(f"非管理实例，跳过清理")
                    return
                
                logger.info(f"执行管理实例清理 ({self.instance_id})")
                
                # 1. 直接清理已标记为crashed的实例
                crashed_instances_query = select(JobManagerInstance).where(
                    JobManagerInstance.status == "crashed"
                )
                crashed_instances = session.exec(crashed_instances_query).all()
                
                for instance in crashed_instances:
                    logger.warning(f"清理已标记崩溃的实例: {instance.instance_id}")
                    await self._cleanup_dead_instance(session, instance, "已标记崩溃")
                
                # 2. 严格的心跳检测逻辑（使用配置的超时时间）
                # 本机实例和远程实例都使用配置的心跳超时时间
                from api.config import get_wiki_jobs_config
                config = get_wiki_jobs_config()
                timeout_minutes = config.get('heartbeat_timeout_minutes', 1)  # 读取配置文件中的心跳超时时间
                local_timeout_minutes = timeout_minutes
                remote_timeout_minutes = timeout_minutes
                
                local_crash_threshold = datetime.now() - timedelta(minutes=local_timeout_minutes)
                remote_crash_threshold = datetime.now() - timedelta(minutes=remote_timeout_minutes)
                
                timeout_instances = []
                for instance in all_instances:
                    if instance.instance_id == self.instance_id:
                        continue
                    
                    if instance.status == "active":
                        # 区分本机和远程实例，使用不同的超时阈值
                        is_local_instance = (instance.hostname == self.hostname)
                        crash_threshold = local_crash_threshold if is_local_instance else remote_crash_threshold
                        timeout_minutes = local_timeout_minutes if is_local_instance else remote_timeout_minutes
                        
                        # 严格检查心跳超时
                        if not instance.last_heartbeat or instance.last_heartbeat < crash_threshold:
                            # 本机实例需要额外确认进程状态
                            if is_local_instance and PSUTIL_AVAILABLE:
                                if self._is_process_really_alive(instance.pid):
                                    logger.warning(f"本机实例 {instance.instance_id} 心跳超时但进程存活，强制标记为超时")
                                    # 即使进程存活，心跳超时也要清理，确保严格性
                            
                            logger.warning(f"{'本机' if is_local_instance else '远程'}实例 {instance.instance_id} 在 {instance.hostname} 心跳超时{timeout_minutes}分钟，标记清理")
                            timeout_instances.append((instance, f"{'本机' if is_local_instance else '远程'}实例心跳超时{timeout_minutes}分钟"))
                
                # 3. 清理超时实例
                for instance, reason in timeout_instances:
                    logger.warning(f"清理实例: {instance.instance_id} ({reason})")
                    await self._cleanup_dead_instance(session, instance, reason)
                
                # 4. 清理孤儿锁
                orphaned_count = await self._cleanup_orphaned_locks_in_session(session)
                
                total_cleaned = len(crashed_instances) + len(timeout_instances)
                if total_cleaned > 0 or orphaned_count > 0:
                    session.commit()
                    logger.warning(f"严格清理完成: {len(crashed_instances)} 个崩溃实例, {len(timeout_instances)} 个超时实例, {orphaned_count} 个孤儿锁")
                    
        except Exception as e:
            logger.error(f"清理崩溃实例失败: {e}")

    async def _strict_heartbeat_check(self):
        """
        严格的心跳检测，专门针对多主机情况
        
        改进：
        1. 心跳超时时间更短
        2. 检测频率更高
        3. 只要心跳超时就立即下线
        """
        try:
            with session_scope() as session:
                # 获取所有活跃实例
                active_instances_query = select(JobManagerInstance).where(
                    JobManagerInstance.status == "active"
                )
                active_instances = session.exec(active_instances_query).all()
                
                current_time = datetime.now()
                dead_instances = []
                
                for instance in active_instances:
                    if instance.instance_id == self.instance_id:
                        continue  # 跳过自己
                    
                    # 严格的心跳检测：使用配置的超时时间
                    from api.config import get_wiki_jobs_config
                    config = get_wiki_jobs_config()
                    timeout_minutes = config.get('heartbeat_timeout_minutes', 1)  # 读取配置文件中的心跳超时时间
                    strict_timeout = current_time - timedelta(minutes=timeout_minutes)
                    
                    if not instance.last_heartbeat or instance.last_heartbeat < strict_timeout:
                        dead_instances.append(instance)
                        logger.warning(f"严格心跳检测: 实例 {instance.instance_id} 心跳超时{timeout_minutes}分钟，标记为死亡")
                
                # 清理死亡实例
                for instance in dead_instances:
                    await self._cleanup_dead_instance(session, instance, "严格心跳检测超时1分钟")
                
                if dead_instances:
                    session.commit()
                    logger.info(f"严格心跳检测清理了 {len(dead_instances)} 个超时实例")
                    
        except Exception as e:
            logger.error(f"严格心跳检测失败: {e}")

    def _elect_manager_instance(self, instances: List[JobManagerInstance]) -> Optional[JobManagerInstance]:
        """选举管理实例"""
        if not instances:
            return None
        
        # 筛选活跃实例
        active_instances = [inst for inst in instances if inst.status == "active"]
        if not active_instances:
            return None
        
        # 按instance_id排序，选择最小的作为管理实例
        active_instances.sort(key=lambda x: x.instance_id)
        return active_instances[0]

    async def _cleanup_dead_instance(self, session, instance: JobManagerInstance, reason: str):
        """清理死亡实例"""
        try:
            # 获取该实例的所有锁
            lock_query = select(JobLock).where(
                JobLock.instance_id == instance.instance_id
            )
            instance_locks = session.exec(lock_query).all()
            
            # 释放所有锁并标记任务为可重新分配
            for lock in instance_locks:
                await self._graceful_release_lock(session, lock)
                logger.warning(f"释放死亡实例 {instance.instance_id} 的锁: {lock.job_id}")
            
            # 删除实例记录
            session.delete(instance)
            logger.warning(f"删除死亡实例: {instance.instance_id} ({reason})")
                
        except Exception as e:
            logger.error(f"清理死亡实例 {instance.instance_id} 失败: {e}")

    async def get_current_manager_instance(self) -> Optional[str]:
        """获取当前管理实例ID"""
        try:
            with session_scope() as session:
                active_query = select(JobManagerInstance).where(
                    JobManagerInstance.status == "active"
                )
                active_instances = session.exec(active_query).all()
                
                elected_manager = self._elect_manager_instance(active_instances)
                return elected_manager.instance_id if elected_manager else None
                
        except Exception as e:
            logger.error(f"获取当前管理实例失败: {e}")
            return None

    async def check_and_adopt_reassignable_jobs(self):
        """检查并接手可重新分配的任务 - 更安全的接管策略"""
        # 本地开发模式下跳过任务接管
        if not self.enable_distributed_lock:
            logger.debug(f"本地开发模式: 跳过可重新分配任务检查 ({self.instance_id})")
            return
            
        if not self.instance_id:
            return
            
        try:
            with session_scope() as session:
                # 查找明确标记为可重新分配的任务
                query = select(GlobalJobState).where(
                    GlobalJobState.global_status.in_(["reassignable", "available"])
                )
                reassignable_jobs = session.exec(query).all()
                
                adopted_count = 0
                for job_state in reassignable_jobs:
                    # 检查是否已有锁
                    lock_query = select(JobLock).where(JobLock.job_id == job_state.job_id)
                    existing_lock = session.exec(lock_query).first()
                    
                    if existing_lock:
                        continue  # 已有锁，跳过
                    
                    # 尝试获取锁
                    success = await self.acquire_job_lock(job_state.job_id, "adopt")
                    if success:
                        # 更新全局状态
                        job_state.global_status = "processing"
                        job_state.processing_instance_id = self.instance_id
                        job_state.updated_time = datetime.now()
                        session.add(job_state)
                        session.commit()
                        
                        adopted_count += 1
                        logger.info(f"接管重新分配的任务: {job_state.job_id}")
                
                if adopted_count > 0:
                    logger.info(f"本次检查接管了 {adopted_count} 个重新分配的任务")
                    
        except Exception as e:
            logger.error(f"检查可重新分配任务失败: {e}")

    async def _cleanup_orphaned_locks(self, session):
        """清理孤儿锁（持有者实例不存在的锁）"""
        try:
            # 获取所有锁
            all_locks_query = select(JobLock)
            all_locks = session.exec(all_locks_query).all()
            
            if not all_locks:
                return
            
            # 获取所有存在的实例ID
            existing_instances_query = select(JobManagerInstance.instance_id)
            existing_instance_ids = {row for row in session.exec(existing_instances_query).all()}
            
            orphaned_locks = []
            for lock in all_locks:
                if lock.instance_id not in existing_instance_ids:
                    orphaned_locks.append(lock)
            
            # 清理孤儿锁
            for lock in orphaned_locks:
                logger.warning(f"发现孤儿锁: job_id={lock.job_id}, instance_id={lock.instance_id}")
                await self._graceful_release_lock(session, lock)
            
            if orphaned_locks:
                session.commit()
                logger.info(f"清理了 {len(orphaned_locks)} 个孤儿锁")
                
        except Exception as e:
            logger.error(f"清理孤儿锁失败: {e}")

    async def _cleanup_orphaned_locks_in_session(self, session):
        """在现有session中清理孤儿锁"""
        try:
            # 获取所有锁
            all_locks_query = select(JobLock)
            all_locks = session.exec(all_locks_query).all()
            
            if not all_locks:
                return 0
            
            # 获取所有存在的实例ID
            existing_instances_query = select(JobManagerInstance.instance_id)
            existing_instance_ids = {row for row in session.exec(existing_instances_query).all()}
            
            orphaned_locks = []
            for lock in all_locks:
                if lock.instance_id not in existing_instance_ids:
                    orphaned_locks.append(lock)
            
            # 清理孤儿锁
            for lock in orphaned_locks:
                logger.warning(f"发现孤儿锁: job_id={lock.job_id}, instance_id={lock.instance_id}")
                await self._graceful_release_lock(session, lock)
            
            return len(orphaned_locks)
                
        except Exception as e:
            logger.error(f"清理孤儿锁失败: {e}")
            return 0


# 全局服务实例
_distributed_lock_service: Optional[DistributedJobLockService] = None

def get_distributed_lock_service() -> DistributedJobLockService:
    """获取分布式锁服务实例"""
    global _distributed_lock_service
    if _distributed_lock_service is None:
        raise RuntimeError("DistributedJobLockService未初始化，请先调用init_distributed_lock_service")
    return _distributed_lock_service

def init_distributed_lock_service(lock_timeout_minutes: int = None) -> DistributedJobLockService:
    """初始化分布式锁服务"""
    global _distributed_lock_service
    
    if lock_timeout_minutes is None:
        # 从配置读取默认锁超时时间
        try:
            from api.config import get_wiki_jobs_config
            config = get_wiki_jobs_config()
            lock_timeout_minutes = config.get("lock_timeout_minutes", 90)
        except:
            lock_timeout_minutes = 90  # 兜底默认值
    
    _distributed_lock_service = DistributedJobLockService(lock_timeout_minutes)
    return _distributed_lock_service