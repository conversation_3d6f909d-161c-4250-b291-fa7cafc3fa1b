from typing import Any, Dict, List, Literal, Optional
from pydantic import BaseModel, Field

from api.model.base import SubRepoInfo
from api.model.user_info import UserInfo
from api.type.repo import RepoMetadata


class FileActionRequest(BaseModel):  
    filePath: str  
    owner: str  
    repo: str  
    action: str
    branch: str

# --- Pydantic Models ---
class WikiPage(BaseModel):
    """
    Model for a wiki page.
    """
    id: str
    title: str
    content: str
    filePaths: List[str]
    importance: str # Should ideally be Literal['high', 'medium', 'low']
    relatedPages: List[str]

class ProcessedProjectEntry(BaseModel):
    id: str  # Filename
    owner: str
    repo: str
    name: str  # owner/repo
    repo_type: str # Renamed from type to repo_type for clarity with existing models
    submittedAt: int # Timestamp
    language: str # Extracted from filename
    branch: str

class RepoInfo(BaseModel):
    owner: str
    repo: str
    type: str
    branch: str
    token: Optional[str] = None
    localPath: Optional[str] = None
    repoUrl: Optional[str] = None
    subRepos: Optional[List[SubRepoInfo]] = None
    
class WikiSection(BaseModel):  
    """  
    Model for wiki sections.  
    """  
    id: str  
    title: str  
    pages: List[str]  
    subsections: Optional[List[str]] = None

class WikiStructureModel(BaseModel):
    """
    Model for the overall wiki structure.
    """
    id: str
    title: str
    description: str
    pages: List[WikiPage]
    sections: Optional[List[WikiSection]] = []
    rootSections: Optional[List[str]] = []
    

class WikiCacheData(BaseModel):
    """
    Model for the data to be stored in the wiki cache.
    """
    wiki_id: Optional[str] = Field(None, description="Wiki ID from database")
    wiki_structure: Optional["WikiStructureModel"] = Field(None)  # 假设是嵌套模型
    generated_pages: Optional[Dict[str, "WikiPage"]] = Field(None)
    repo: Optional["RepoInfo"] = Field(None)
    provider: Optional[str] = Field(None)
    model: Optional[str] = Field(None)
    priv_status: Optional[str] = Field(None)
    ownerId: Optional[int] = Field(None)

class WikiCacheRequest(BaseModel):
    """
    Model for the request body when saving wiki cache.
    """
    repo: RepoInfo
    language: str
    wiki_structure: WikiStructureModel
    generated_pages: Dict[str, WikiPage]
    provider: str
    model: str

class WikiGenerationRequest(BaseModel):
    repo_url: str
    branch: str = "master"
    repo_type: str = "whaleDevCloud"
    token: Optional[str] = None
    language: str = "en"
    model_settings: Dict[str, Any]
    # 文件过滤选项
    excluded_dirs: Optional[str] = None
    excluded_files: Optional[str] = None
    included_dirs: Optional[str] = None
    included_files: Optional[str] = None
    # Wiki 类型
    comprehensive: bool = True
    # 子仓库参数
    sub_repos: Optional[List[SubRepoInfo]] = None
    tag_ids: Optional[List[int]] = None
    # 仓库元数据
    repo_metadata: Optional[RepoMetadata] = None
    # 仓库说明
    comments: Optional[str] = None
    # 用户额外指令
    custom_instructions: Optional[str] = None
    # Deprecated DocChain fields (kept for backward compatibility)
    existing_topic_id: Optional[str] = None
    existing_topic_id_code: Optional[str] = None
    existing_topic_id_doc: Optional[str] = None

class ModifyWikiInfoRequest(BaseModel):
    """
    请求更新wiki信息模型
    """
    # 仓库元数据
    repo_metadata: Optional[RepoMetadata] = None
    # 仓库说明
    comments: Optional[str] = None
    # DocChain Topic ID
    topic_id: Optional[str] = None

class WikiRefreshRequest(BaseModel):
    """
    Model for requesting a wiki refresh.
    """
    token: Optional[str] = None
    wiki_id: str = Field(..., description="Wiki ID (int) to refresh")
    model_settings: Dict[str, Any] = Field(..., description="Model settings for regeneration")
    comprehensive: bool = Field(True, description="Whether to generate comprehensive wiki")
    force_refresh: bool = Field(False, description="Force refresh even if no code changes detected")
    refresh_pages: Optional[List[str]] = Field(None, description="Specific page IDs to refresh. If None, refresh all pages")
    rebuild_structure: bool = Field(False, description="Regenerate the wiki structure regardless of detected code changes")
    custom_instructions: Optional[str] = Field(None, description="Additional user instructions to guide regeneration")

class WikiExportRequest(BaseModel):
    """
    Model for requesting a wiki export.
    """
    wiki_id: Optional[str] = Field(..., description="Wiki id")
    branch: Optional[str] = Field(..., description="Repo branch")
    repo_url: str = Field(..., description="URL of the repository")
    pages: List[WikiPage] = Field(..., description="List of wiki pages to export")
    sections: Optional[List[dict]] = Field(default=None, description="Wiki sections structure")  
    rootSections: Optional[List[str]] = Field(default=None, description="List of root section IDs")
    format: Literal["markdown", "json"] = Field(..., description="Export format (markdown or json)")
    rootSections: Optional[List[str]] = Field(default=None, description="List of root section IDs to export")

# --- Model Configuration Models ---
class Model(BaseModel):
    """
    Model for LLM model configuration
    """
    id: str = Field(..., description="Model identifier")
    name: str = Field(..., description="Display name for the model")

class Provider(BaseModel):
    """
    Model for LLM provider configuration
    """
    id: str = Field(..., description="Provider identifier")
    name: str = Field(..., description="Display name for the provider")
    models: List[Model] = Field(..., description="List of available models for this provider")
    supportsCustomModel: Optional[bool] = Field(False, description="Whether this provider supports custom models")

class ModelConfig(BaseModel):
    """
    Model for the entire model configuration
    """
    providers: List[Provider] = Field(..., description="List of available model providers")
    defaultProvider: str = Field(..., description="ID of the default provider")

class AuthorizationConfig(BaseModel):
    code: str = Field(..., description="Authorization code")

class RepoBranchesRequest(BaseModel):
    repo_url: str
    repo_type: str
    token: str
class TestModelRequest(BaseModel):
    model: str
    authorization: str

class TestModelResponse(BaseModel):
    success: bool
    message: str

class WhaleDevCloudTokenRequest(BaseModel):
    token: str

class AnnouncementResponse(BaseModel):
    """
    公告响应模型
    """
    id: int
    title: str
    type: str
    content: str
    seq: int

class AnnouncementsResponse(BaseModel):
    """
    公告列表响应模型
    """
    announcements: List[AnnouncementResponse]

class UsersResponse(BaseModel):
    total: int
    users: List[UserInfo]

class ModifyUserStateRequest(BaseModel):
    """
    请求修改用户状态模型
    """
    id: int
    state: bool

class ModifyUserRolesRequest(BaseModel):
    """
    请求修改用户角色模型
    """
    id: int
    role_ids: List[int]

class ModifyWikiOwnerRequest(BaseModel):
    """
    请求修改wiki拥有者模型
    """
    wiki_id: str
    owner_id: int
