from enum import Enum


class SandboxStatus(Enum):
    """沙盒状态枚举"""
    
    # 未创建 - Job不存在
    NOT_CREATED = "NOT_CREATED"
    
    # 已分配创建中 - Job存在但Pod还在启动中
    CREATING = "CREATING"
    
    # 初始化智能体 - Pod运行中但容器内进程未完全启动
    INITIALIZING = "INITIALIZING"
    
    # 创建完成 - 容器内服务完全启动
    READY = "READY"
    
    # 创建失败 - Pod启动失败
    FAILED = "FAILED"
    
    # 状态查询失败 - 查询过程中发生异常
    QUERY_FAILED = "QUERY_FAILED"

    # 个人配额已满
    QUOTA_EXCEEDED = "QUOTA_EXCEEDED"

    # 系统总量已满
    TOTAL_CAPACITY_FULL = "TOTAL_CAPACITY_FULL"
