from typing import Annotated, List, Optional
from pydantic import BaseModel, Field

from api.sse_chat import ChatMessage

class ChatRequest(BaseModel):
    model: Annotated[str, Field(..., description="Model name for the specified provider")] = "gemini-2.5-flash"
    messages: Annotated[List[ChatMessage], Field(..., description="List of chat messages")] = []
    repo_url: Annotated[str, Field(..., description="Repository URL")] = "https://git-nj.iwhalecloud.com/ptdev01/whale-deepwiki.git"
    api_key: Annotated[str, Field(..., description="API key")] = ""
    provider: Annotated[str, Field("google", description="Model provider (openai, gemini-cli, whalecloud)")] = "gemini-cli"
    branch: Annotated[str, Field("master", description="Branch of repository")] = "master"
    wiki_id: Annotated[str | None, Field(default=None, description="Wiki ID")]

class WikiQARequest(BaseModel):
    """
    wiki知识库问答接口作为MCP工具时所需参数
    """
    provider: Annotated[Optional[str], Field("whalecloud", description="Model provider (openai, gemini-cli, whalecloud)")] = "gemini-cli"
    model: Annotated[Optional[str], Field(..., description="Model name for the specified provider")] = "gemini-2.5-flash"
    content: Annotated[str, Field(..., description="The question about wiki")]
    repo_name: Annotated[str, Field(..., description="Git repository name")]
    branch: Annotated[Optional[str], Field(..., description="Branch of repository")] = ""
    repo_owner: Annotated[Optional[str], Field(..., description="The owner of repository")] = ""
    # App-level chat (optional): when provided, will use App Sandbox chat flow
    app_code: Annotated[Optional[str], Field(default=None, description="App code for App Sandbox chat")]
    app_id: Annotated[Optional[str], Field(default=None, description="App ID for App Sandbox chat")]
    wiki_id: Annotated[Optional[str], Field(default=None, description="The unique identifier of the wiki")]
    session_id: Annotated[Optional[str], Field(default=None, description="The unique identifier of the chat session")]
    system_prompt: Annotated[Optional[str], Field(default=None, description="System prompt")]