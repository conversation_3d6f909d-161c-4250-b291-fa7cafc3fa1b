from pydantic import BaseModel, Field


class DatabaseSettings(BaseModel):
    class UrlConfig(BaseModel):
        driver_name: str = ""
        username: str = ""
        password: str = Field("", description="数据库密码")
        host: str = ""
        port: int = None
        database: str = ""

    class PoolConfig(BaseModel):
        size: int = Field(20, description="连接池大小")
        max_overflow: int = Field(20, description="最大溢出连接数")
        recycle: int = Field(3600, description="连接回收时间(秒)")
        pre_ping: bool = Field(True, description="执行前检查连接")
        timeout: int = Field(30, description="获取连接超时时间(秒)")

    url: UrlConfig = UrlConfig()
    connect_timeout: int = Field(10, description="连接超时时间(秒)")
    pool: PoolConfig = PoolConfig()
    echo: bool = Field(False, description="是否输出SQL日志")