from typing import List, Optional
from pydantic import BaseModel


class Solution(BaseModel):
    # 发布包ID
    release_pkg_id: Optional[int] = None
    # 发布包编码
    release_pkg_code: Optional[str] = None
    # 解决方案名称
    solution_name: Optional[str] = None
    # 解决方案ID
    solution_id: Optional[int] = None

class RepoMetadata(BaseModel):
    # 研发云仓库ID
    dc_repo_id: Optional[int] = None
    # 研发云项目ID
    dc_project_id: Optional[int] = None
    # 分支版本ID
    branch_version_id: Optional[int] = None
    # 分支版本名称
    branch_version_name: Optional[str] = None
    # 产品版本ID
    product_version_id: Optional[int] = None
    # 产品版本编码
    product_version_code: Optional[str] = None
    # 产品名称
    product_name: Optional[str] = None
    # 产品ID
    product_id: Optional[int] = None
    # 产品线ID
    product_line_id: Optional[int] = None
    # 产品线名称
    product_line_name: Optional[str] = None
    # 解决方案
    solutions: Optional[List[Solution]]