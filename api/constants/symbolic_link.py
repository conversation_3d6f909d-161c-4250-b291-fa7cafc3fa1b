from enum import Enum


class SymbolicLinkPath(Enum):
    """软链接路径枚举"""
    
    # 基础工作空间路径
    WORKSPACE_BASE = "/data/workspace"
    
    # 代码相关路径
    CODE_SOURCE = "/data/workspace/deepwiki-c"
    CODE_TARGET = "/data/workspace/code"
    
    # 文档相关路径
    I_DOC_SOURCE = "/data/workspace/deepwiki-pw"
    I_DOC_TARGET = "/data/workspace/i-doc"
    O_DOC_TARGET = "/data/workspace/o-doc"
    
    # 项目工作空间路径
    PROJECT_WORKSPACE_SOURCE = "/data/workspace/deepwiki-pw"
    
    # Gemini相关路径
    SYS_GEMINI_TARGET = "/data/workspace/project-gemini"
    USER_GEMINI_TARGET = "/data/workspace/.gemini"
    
    # 用户工作空间路径
    USER_WORKSPACE_SOURCE = "/data/workspace/deepwiki-w"
    USER_WORKSPACE_TARGET = "/data/workspace/userspace"
    
    # 软链接目标路径列表（用于删除软链接）
    SYMLINK_TARGETS = [
        "/data/workspace/code",
        "/data/workspace/i-doc", 
        "/data/workspace/o-doc",
        "/data/workspace/project-gemini",
        "/data/workspace/userspace",
        "/data/workspace/.gemini"
    ]
    
    @classmethod
    def get_symlink_paths(cls, owner: str, repo_name: str, branch_clean: str, user_code: str) -> list:
        """
        获取软链接路径配置
        
        Args:
            owner: 仓库所有者
            repo_name: 仓库名称
            branch_clean: 清理后的分支名
            user_code: 用户代码
            
        Returns:
            软链接路径配置列表
        """
        return [
            {
                "src_path": f"{cls.CODE_SOURCE.value}/{owner}/{repo_name}-{branch_clean}/",
                "dist_path": cls.CODE_TARGET.value
            },
            {
                "src_path": f"{cls.PROJECT_WORKSPACE_SOURCE.value}/{owner}/{repo_name}-{branch_clean}/i-doc/",
                "dist_path": cls.I_DOC_TARGET.value
            },
            {
                "src_path": f"{cls.PROJECT_WORKSPACE_SOURCE.value}/{owner}/{repo_name}-{branch_clean}/o-doc/",
                "dist_path": cls.O_DOC_TARGET.value
            },
            {
                "src_path": f"{cls.PROJECT_WORKSPACE_SOURCE.value}/{owner}/{repo_name}-{branch_clean}/project-gemini/",
                "dist_path": cls.SYS_GEMINI_TARGET.value
            },
            {
                "src_path": f"{cls.USER_WORKSPACE_SOURCE.value}/{user_code}/{owner}/{repo_name}-{branch_clean}/userspace/",
                "dist_path": cls.USER_WORKSPACE_TARGET.value
            },
            {
                "src_path": f"{cls.USER_WORKSPACE_SOURCE.value}/{user_code}/{owner}/{repo_name}-{branch_clean}/.gemini/",
                "dist_path": cls.USER_GEMINI_TARGET.value
            }
        ]
