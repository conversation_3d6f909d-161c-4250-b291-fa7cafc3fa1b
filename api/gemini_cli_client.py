from datetime import datetime
import os
import json
import logging
import httpx
import time
from typing import Dict, Any, AsyncGenerator, Optional, List, Tuple
from adalflow.core.types import ModelType
from api.logging_config import setup_logging
# 延迟导入以避免循环依赖
# from api.service.job_pool_service import get_job_pool_service
from api.service.wiki_query_service import get_wiki_basic_info_by_id
from api.utils.file_template import get_template_input
from api.utils.file_utils import read_prompt_file
from jinja2 import Environment, FileSystemLoader
from api.sandbox.sandbox_service import sandbox_service
from api.utils.git_utils import extract_repo_info
from api.type.sandbox_status import SandboxStatus
import asyncio
from api.langfuse_utils import (
    bind_langfuse_span,
    build_langfuse_metadata,
    create_langfuse_span,
    get_langfuse_context,
)

fallback_prompt = """
    <note>
        你的回答必须遵守以下文件访问规则：
        1. **可访问范围**：你只能读取或写入目录 `<project_path>` 及其子目录下的文件或者目录。你执行的所有操作必须在`<project_path>`范围内
        2. **禁止访问**：任何试图访问 `<project_path>` 上级目录（如 `<project_path>/../`）的操作将被拒绝。
        3. **强制合规**：每次操作文件时，必须检查路径是否以 `<project_path>/` 开头，否则返回错误。

        示例：
        - 允许：`<project_path>/src/main.py`
        - 禁止：`/etc/passwd` 或 `<project_path>/../secret.txt`

        请严格遵守此规则，否则你的操作将被终止。
    </note>
        """
fallback_sys_prompt = """
# 当前时间:
 {current_time}

只有当你判断用户的问题需要使用docchain-server时，才能参考以下信息，否则直接回答用户问题：
# docchain-server的工具使用说明
docchain-server中 searchByDocchain工具的参数{{"topicId": {topic_id}}}。
当后续调用工具需要topicId时，优先使用用户输入的topicId，如果用户没有指定topicId，则使用上文的topicId
"""        
   
# 获取当前工作目录
current_dir = os.getcwd()
log_dir = os.path.join(current_dir, "logs")
log_file = os.path.join(log_dir, "chat.log")  # 新日志文件

# 确保日志目录存在
os.makedirs(log_dir, exist_ok=True)

# 创建独立的新 Logger
logger = logging.getLogger(__name__)
logger.propagate = False  # 阻止日志向上传播到根 Logger

# 创建文件处理器
file_handler = logging.FileHandler(log_file, encoding="utf-8")
stream_handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)

# 将处理器添加到新 Logger
logger.addHandler(file_handler)
logger.addHandler(stream_handler)


def _truncate_for_langfuse(value: Optional[Any], limit: int = 2000) -> Optional[str]:
    """将长文本截断以便写入 Langfuse，避免超出展示限制。"""

    if value is None:
        return None
    text = str(value)
    if len(text) <= limit:
        return text
    truncated = text[:limit]
    return f"{truncated}… (truncated {len(text) - limit} chars)"

class GeminiCliClient:
    """
    与Gemini CLI兼容的API客户端
    用于直接与gemini-cli服务通信，不经过RAG处理
    """
    
    def __init__(self):
        """初始化客户端"""
        # 直接从环境变量获取配置，不依赖api.config中的configs
        self.base_url = os.environ.get("GEMINI_CLI_API_BASE", "http://localhost:8000")
        # 默认超时时间从环境变量获取，默认为600秒（10分钟）
        self.default_timeout = float(os.environ.get("GEMINI_CLI_DEFAULT_TIMEOUT", "600"))
            
        logger.info(f"GeminiCliClient initialized with base_url: {self.base_url}")
        logger.info(f"Default timeout: {self.default_timeout} seconds")
    
    def convert_inputs_to_api_kwargs(self, input: Optional[str] = None, model_kwargs: Optional[Dict[str, Any]] = None, model_type: Optional[ModelType] = None) -> Dict[str, Any]:
        """
        将输入转换为API调用的参数
        这个方法是为了兼容adalflow的接口规范
        
        Args:
            input: 输入文本
            model_kwargs: 模型参数
            model_type: 模型类型
            
        Returns:
            API调用的参数
        """
        # 这里不需要处理input，因为gemini-cli直接使用messages
        return model_kwargs if model_kwargs else {}
    
    def _read_file_references(self, file_references: List[Dict[str, Any]], repo_url: str, branch: str, user_code: str) -> str:
        """
        读取文件引用的内容
        
        Args:
            file_references: 文件引用列表
            repo_url: 仓库URL
            branch: 分支名称
            user_code: 用户代码
            
        Returns:
            格式化的文件内容字符串
        """
        if not file_references:
            return "", "", ""
            
        try:
            # 导入服务（避免顶层导入造成循环依赖）
            from api.filesearch.file_manager_service import get_file_manager_service
            
            # 获取文件管理服务
            file_manager_service = get_file_manager_service()
            
            # 解析仓库信息
            owner, repo_name, _ = extract_repo_info(repo_url)
            
            # 读取文件引用提示词模板，确保得到字符串
            prompt_read_result = read_prompt_file("file_reference_prompt.md")
            if isinstance(prompt_read_result, dict):
                template_str = prompt_read_result.get("content", "")
            else:
                template_str = str(prompt_read_result) if prompt_read_result is not None else ""
            
            file_list = []
            file_contents = []
            selected_directories = []
            
            for file_ref in file_references:
                # 兼容 Pydantic BaseModel 实例与字典
                try:
                    virtual_file_path = getattr(file_ref, 'path', None)
                    if virtual_file_path is None and isinstance(file_ref, dict):
                        virtual_file_path = file_ref.get('path')

                    file_name = getattr(file_ref, 'name', None)
                    if file_name is None and isinstance(file_ref, dict):
                        file_name = file_ref.get('name')

                    is_directory = getattr(file_ref, 'isDirectory', None)
                    if is_directory is None and isinstance(file_ref, dict):
                        is_directory = file_ref.get('isDirectory', False)
                    if is_directory is None:
                        is_directory = False
                except Exception as e:
                    logger.warning(f"无法解析文件引用对象: {e}")
                    continue
                
                if is_directory:
                    # 记录已选择的目录
                    try:
                        dir_name_for_list = os.path.basename((virtual_file_path or '').rstrip('/')) or (virtual_file_path or '').rstrip('/')
                        selected_directories.append(f"- {dir_name_for_list} (`{virtual_file_path}`)")
                    except Exception:
                        if virtual_file_path:
                            selected_directories.append(f"- `{virtual_file_path}`")
                    # 继续处理下一个引用
                    continue
                
                if not virtual_file_path:
                    logger.warning("文件路径为空，跳过")
                    continue
                
                try:
                    # 使用文件管理服务读取文件内容（前台应该传入标准格式如 /code/xxx, /i-doc/xxx）
                    content, file_info = file_manager_service.read_sandbox_file(
                        owner, repo_name, branch, user_code, virtual_file_path
                    )
                    
                    if content is None:
                        error_msg = file_info.get("error", "未知错误") if file_info else "未知错误"
                        logger.warning(f"无法读取文件 {virtual_file_path}: {error_msg}")
                        continue
                    
                    # 检查是否被截断
                    if file_info and file_info.get("is_truncated", False):
                        content += "\n\n... (文件内容过长，已截断)"
                    
                    # 使用虚拟路径作为显示路径
                    display_path = virtual_file_path
                    if not file_name:
                        file_name = os.path.basename(virtual_file_path)
                    
                    file_list.append(f"- {file_name} (`{display_path}`)")
                    file_contents.append(f"### {file_name}\n**路径**: `{display_path}`\n\n```\n{content}\n```\n")
                    
                    content_length = file_info.get("content_length", len(content)) if file_info else len(content)
                    encoding = file_info.get("encoding", "unknown") if file_info else "unknown"
                    logger.info(f"成功读取文件内容: {virtual_file_path} (编码: {encoding}, 长度: {content_length} 字符)")
                        
                except Exception as e:
                    logger.error(f"处理文件失败 {virtual_file_path}: {e}")
                    continue
            
            # 如果没有具体文件内容，但存在目录选择，也允许继续构造提示词
            if not file_contents and not selected_directories:
                logger.warning("没有成功读取到任何文件内容，且未选择目录")
                return "", "", ""
            
            # 使用 string.Template 安全格式化
            # try:
            #     from string import Template
            #     template = Template(template_str)
            #     formatted_content = template.safe_substitute(
            #         file_list="\n".join(file_list),
            #         file_contents="\n".join(file_contents),
            #         directory_list="\n".join(selected_directories)
            #     )
            # except Exception as fmt_err:
            #     logger.warning(f"使用 Template 格式化失败，回退为原文: {fmt_err}")
            #     formatted_content = template_str
            
            logger.info(f"成功处理 {len(file_contents)} 个文件引用")
            return "\n".join(file_list), "\n".join(file_contents), "\n".join(selected_directories)
            
        except Exception as e:
            logger.error(f"处理文件引用失败: {e}")
            return "", "", ""
    
    def _process_command_params(self, command_params: Dict[str, Any], user_question: str, repo_url: str, branch: str, user_code: str) -> str:
        """
        处理命令参数，生成相应的提示词
        
        Args:
            command_params: 命令参数 {operation: str, param: str}
            user_question: 用户问题
            repo_url: 仓库URL
            branch: 分支名称
            user_code: 用户代码
            
        Returns:
            处理后的提示词
        """
        if not command_params or not command_params.get('operation'):
            return user_question
        
        operation = command_params.get('operation')
        param = command_params.get('param')
        
        logger.info(f"处理命令参数: operation={operation}, param={param}")
        
        try:
            if operation == 'wiki' and param:
                # 验证参数
                if not param.strip():
                    logger.warning("wiki 标题参数为空")
                    return user_question
                
                # 清理参数，移除特殊字符
                clean_param = param.strip().replace('/', '_').replace('\\', '_')
                if clean_param != param:
                    logger.info(f"清理参数: {param} -> {clean_param}")
                    param = clean_param
                
                # 构建虚拟文件路径
                virtual_file_path = f"i-doc/wiki/pages/{param}.md"
                logger.info(f"处理 wiki 标题命令: {param}, 虚拟路径: {virtual_file_path}")
                
                # 检查文件是否存在
                file_exists = self._check_wiki_file_exists(virtual_file_path, repo_url, branch, user_code)
                logger.info(f"文件存在性检查结果: {file_exists}")
                
                if file_exists:
                    # 文件存在：重新生成
                    logger.info(f"文件已存在，生成重新生成提示词")
                    return self._generate_regenerate_prompt(virtual_file_path, user_question, repo_url, branch, user_code)
                else:
                    # 文件不存在：创建新文件
                    logger.info(f"文件不存在，生成创建提示词")
                    return self._generate_create_prompt(param, user_question)
            else:
                logger.warning(f"未知的命令操作: {operation}")
                return user_question
                
        except Exception as e:
            logger.error(f"处理命令参数时发生错误: {e}")
            return user_question
    
    def _check_wiki_file_exists(self, virtual_file_path: str, repo_url: str, branch: str, user_code: str) -> bool:
        """
        检查 wiki 文件是否存在
        
        Args:
            virtual_file_path: 虚拟文件路径
            repo_url: 仓库URL
            branch: 分支名称
            user_code: 用户代码
            
        Returns:
            文件是否存在
        """
        try:
            from api.filesearch.file_manager_service import get_file_manager_service
            file_manager_service = get_file_manager_service()
            
            # 解析仓库信息
            owner, repo_name, _ = extract_repo_info(repo_url)
            
            # 验证仓库信息
            if not owner or not repo_name:
                logger.warning(f"无法解析仓库信息: {repo_url}")
                return False
            
            # 尝试读取文件
            content, file_info = file_manager_service.read_sandbox_file(
                owner, repo_name, branch, user_code, virtual_file_path
            )
            
            return content is not None
        except Exception as e:
            logger.warning(f"检查文件存在性失败: {e}")
            return False
    
    def _render_wiki_prompt(self, mode: str, context: dict) -> str:
        """使用Jinja2渲染统一的wiki提示词模板。"""
        try:
            env = Environment(loader=FileSystemLoader('api/prompts/editablePrompts'), autoescape=False)
            tmpl = env.get_template('wiki_prompt.md')
            return tmpl.render(**{"mode": mode, **context})
        except Exception as e:
            logger.error(f"渲染wiki_prompt.md失败: {e}")
            return ''

    def _generate_create_prompt(self, page_title: str, user_question: str) -> str:
        context = {
            "page_title": page_title,
            "user_question": user_question,
        }
        return self._render_wiki_prompt("create", context)
    def _generate_regenerate_prompt(self, virtual_file_path: str, user_question: str, repo_url: str, branch: str, user_code: str) -> str:
        """生成重新生成 wiki 页面的提示词（统一模板）。"""
        existing_content = self._read_wiki_file_content(virtual_file_path, repo_url, branch, user_code)
        context = {
            "virtual_file_path": virtual_file_path,
            "user_question": user_question,
            "existing_content": existing_content,
        }
        return self._render_wiki_prompt("regenerate", context)
    
    def _read_wiki_file_content(self, virtual_file_path: str, repo_url: str, branch: str, user_code: str) -> str:
        """
        读取 wiki 文件内容
        
        Args:
            virtual_file_path: 虚拟文件路径
            repo_url: 仓库URL
            branch: 分支名称
            user_code: 用户代码
            
        Returns:
            文件内容字符串
        """
        try:
            from api.filesearch.file_manager_service import get_file_manager_service
            file_manager_service = get_file_manager_service()
            
            # 解析仓库信息
            owner, repo_name, _ = extract_repo_info(repo_url)
            
            # 验证仓库信息
            if not owner or not repo_name:
                logger.warning(f"无法解析仓库信息: {repo_url}")
                return "无法解析仓库信息"
            
            # 读取文件内容
            content, file_info = file_manager_service.read_sandbox_file(
                owner, repo_name, branch, user_code, virtual_file_path
            )
            
            if content is None:
                error_msg = file_info.get("error", "未知错误") if file_info else "未知错误"
                logger.warning(f"无法读取文件 {virtual_file_path}: {error_msg}")
                return "文件读取失败"
            
            return content
        except Exception as e:
            logger.error(f"读取 wiki 文件内容失败: {e}")
            return "文件读取失败"
    
    def search_error(self, content: str) -> tuple[Optional[str], Optional[dict]]:
        """
        检查内容是否为API错误信息
        
        Args:
            content: 要检查的内容
        """
        # 检查是否为JSON格式的API错误
        try:
            if content.startswith('API错误'):
                split = content.split(':', 1)
                if len(split) == 1:
                    logger.error(f"报错信息不符合规范，不计为报错，内容为：{content}")
                    return None, None
                str_content = split[0].strip()
                error_content = split[1].strip()
                response = json.loads(error_content)
                error = response.get('error')
                status_code = response.get('status_code')
                if error.get('message') and error.get('type') == 'api_error' and error.get('code') and error.get('param') == 'api_forward_request_error' and error.get('request_id'):
                    code = error.get('code')
                    if f"API错误({code})" == str_content:
                        return str(code), error
                    else:
                        return None, None
            else:
                return None, None
        except (json.JSONDecodeError, TypeError):
            return None, None
    
    def _is_api_error_content(self, content: str) -> tuple[bool, Optional[str], Optional[dict]]:
        """
        检查内容是否为API错误信息
        
        Args:
            content: 要检查的内容
            
        Returns:
            tuple: (is_error, out_code, error)
            - is_error: 如果是API错误信息返回True，否则返回False
            - out_code: 错误代码，如果不是错误则为None
            - error: 错误详情，如果不是错误则为None
        """
        # 检查是否为JSON格式的API错误
        try:
            out_code, error = self.search_error(content)
            if out_code and error:
                return True, out_code, error

            parsed = json.loads(content)
            if isinstance(parsed, dict) and "error" in parsed and "status_code" in parsed:
                return True, None, None
            else:
                # 如果不是错误格式，返回False
                return False, None, None
        except (json.JSONDecodeError, TypeError):
            return False, None, None
    
    def _is_api_error(self, error: dict) -> bool:
        """
        检查内容是否为API错误信息
        
        Args:
            content: 要检查的内容
            
        Returns:
            如果是API错误信息返回True，否则返回False
        """
        # 检查是否为JSON格式的API错误
        try:
            if isinstance(error, dict) and "message" in error and "status" in error:
                return True
        except (json.JSONDecodeError, TypeError):
            pass
        return False
    
    async def _handle_job_status_and_get_url(
        self, 
        user_code: str, 
        repo_url: str, 
        branch: str, 
        api_key: str, 
        *, 
        app_code: Optional[str] = None, 
        app_id: Optional[str] = None, 
        wiki_id: Optional[int] = None, 
        user_id: Optional[int] = None,
        user_name: Optional[str] = None
    ) -> tuple[Optional[str], str, Optional[str]]:
        """
        处理job状态查询并获取沙盒URL
        
        Args:
            user_code: 用户代码
            repo_url: Git仓库URL
            branch: 分支名称
            api_key: API密钥
            app_code: 应用代码（可选）
            app_id: 应用ID（可选）
            wiki_id: Wiki ID（可选）
            user_id: 用户ID（可选）
            user_name: 用户姓名（可选）
            
        Returns:
            tuple: (sandbox_url, message, job_name)
            - sandbox_url: 沙盒URL，如果获取失败则为None
            - message: 需要返回的消息，如果不需要返回则为空字符串
            - job_name: Job名称，如果获取失败则为None
        """
        try:
            from api.sandbox.kubernetes_service import get_kubernetes_service
            kubernetes_service = get_kubernetes_service()
            
            # 检查 kubernetes_service 是否有效
            if kubernetes_service is None:
                logger.error("Kubernetes服务未初始化")
                status_data = {
                    "type": "sandbox_status",
                    "status": "SERVICE_UNAVAILABLE",
                    "message": "Kubernetes服务未初始化，请稍后重试",
                    "can_retry": True,
                    "timestamp": time.time()
                }
                return None, json.dumps(status_data), None
            
            # App 模式：仅做状态查询并返回URL，不创建或删除应用级沙盒（应用级沙盒在其他入口创建/管理）
            if app_code or app_id:
                try:
                    sandbox_url = sandbox_service.get_app_sandbox_url(app_code or str(app_id))
                    if sandbox_url:
                        return sandbox_url, "", None
                except Exception as _e:
                    logger.warning(f"获取应用级沙盒URL失败: {_e}")
                status_data = {
                    "type": "sandbox_status",
                    "status": "APP_NOT_READY",
                    "message": "应用级沙盒未就绪，请先在应用管理中启动",
                    "can_retry": True,
                    "timestamp": time.time()
                }
                return None, json.dumps(status_data), None

            # 个人沙盒：查询job的详细状态
            existing_job = kubernetes_service.find_job_by_annotation(user_code, wiki_id)
            
            if existing_job is None:
                # Job不存在，创建新的沙盒
                logger.info(f"Job不存在，开始创建沙盒: {user_code}/{repo_url}/{branch}")
                try:
                    wiki_info = get_wiki_basic_info_by_id(wiki_id)
                    await sandbox_service.get_or_create_sandbox_for_external_sandbox(user_code, wiki_info.get('wiki_id'), user_id, user_name)
                    logger.info(f"沙盒创建成功")
                    # 返回结构化的沙盒状态信息
                    status_data = {
                        "type": "sandbox_status",
                        "status": "CREATING",
                        "message": "个人沙盒正在创建，请稍候",
                        "can_retry": True,
                        "timestamp": time.time()
                    }
                    return None, json.dumps(status_data), None
                except Exception as create_error:
                    logger.error(f"创建沙盒失败: {create_error}")
                    status_data = {
                        "type": "sandbox_status",
                        "status": "CREATE_FAILED",
                        "message": "个人沙盒创建失败，请稍后重试",
                        "can_retry": True,
                        "timestamp": time.time()
                    }
                    return None, json.dumps(status_data), None
            
            job_name = existing_job.metadata.name
            status_info = await sandbox_service.get_sandbox_detailed_status(user_code, repo_url, branch, job_name)
            job_status = status_info.get("status", "UNKNOWN")
            logger.info(f"Job状态查询结果: {job_status}")
            
            if job_status in [SandboxStatus.CREATING.value, SandboxStatus.INITIALIZING.value]:
                # Job已创建但正在初始化中
                logger.info(f"Job正在初始化中，状态: {job_status}")
                status_data = {
                    "type": "sandbox_status",
                    "status": job_status,
                    "message": "个人沙盒正在初始化，请稍候",
                    "can_retry": True,
                    "timestamp": time.time()
                }
                return None, json.dumps(status_data), None
            
            elif job_status == SandboxStatus.READY.value:
                # Job已就绪，获取沙盒URL
                logger.info(f"Job已就绪，获取沙盒URL")
                try:
                    sandbox_url = sandbox_service.get_sandbox_url(user_code, repo_url, branch, job_name)
                    logger.info(f"获取到沙盒URL: {sandbox_url}")
                    
                    # 更新Job的最后访问时间
                    try:
                        await sandbox_service.update_sandbox_access(user_code, repo_url, branch, job_name)
                        logger.info(f"已更新Job访问时间: {user_code}/{repo_url}/{branch}")
                    except Exception as update_error:
                        logger.warning(f"更新Job访问时间失败: {update_error}")
                    
                    return sandbox_url, "", job_name
                except Exception as url_error:
                    logger.warning(f"获取沙盒URL失败: {url_error}")
                    status_data = {
                        "type": "sandbox_status",
                        "status": "URL_FAILED",
                        "message": "个人沙盒URL获取失败，请稍后重试",
                        "can_retry": True,
                        "timestamp": time.time()
                    }
                    return None, json.dumps(status_data), None
            
            elif job_status == SandboxStatus.FAILED.value:
                # Job创建失败，尝试重新创建
                logger.warning(f"Job创建失败，尝试重新创建: {user_code}/{repo_url}/{branch}")
                try:
                    # 先删除失败的job
                    from api.service.job_pool_service import get_job_pool_service
                    get_job_pool_service().release_job(user_id, int(wiki_id))
                    logger.info(f"已删除失败的Job")
                    
                    # 重新创建
                    await sandbox_service.get_or_create_sandbox_for_external_sandbox(user_code, wiki_info.get('wiki_id'), user_id, user_name)
                    logger.info(f"沙盒重新创建成功")
                    status_data = {
                        "type": "sandbox_status",
                        "status": "RECREATING",
                        "message": "个人沙盒正在重新初始化，请稍候",
                        "can_retry": True,
                        "timestamp": time.time()
                    }
                    return None, json.dumps(status_data), None
                except Exception as recreate_error:
                    logger.error(f"重新创建沙盒失败: {recreate_error}")
                    status_data = {
                        "type": "sandbox_status",
                        "status": "RECREATE_FAILED",
                        "message": "个人沙盒重新创建失败，请稍后重试",
                        "can_retry": True,
                        "timestamp": time.time()
                    }
                    return None, json.dumps(status_data), None
            
            else:
                # 其他状态，返回初始化提示
                logger.info(f"Job状态未知: {job_status}，返回初始化提示")
                status_data = {
                    "type": "sandbox_status",
                    "status": job_status or "UNKNOWN",
                    "message": "个人沙盒正在初始化，请稍候",
                    "can_retry": True,
                    "timestamp": time.time()
                }
                return None, json.dumps(status_data), None
                
        except Exception as e:
            logger.error(f"查询job状态失败: {e}")
            status_data = {
                "type": "sandbox_status",
                "status": "QUERY_FAILED",
                "message": "个人沙盒状态查询失败，请稍后重试",
                "can_retry": True,
                "timestamp": time.time()
            }
            return None, json.dumps(status_data), None
    
    async def acall(self, api_kwargs: Dict[str, Any], client_ip: Optional[str] = None) -> AsyncGenerator[str, None]:
        """
        异步调用gemini-cli API
        
        Args:
            api_kwargs: API调用的参数
            client_ip: 客户端IP地址，用于透传
            
        Returns:
            异步生成器，生成处理后的文本消息（工具调用用特殊前缀）
        """
        # 自动获取 user_code
        from api.middleware.auth_middleware import get_current_user
        current_user = get_current_user()
        # 获取用户姓名
        user_name = None
        try:
            if current_user:
                user_name = current_user.get("user_name") or current_user.get("name")
        except Exception as e:
            logger.warning(f"获取用户姓名失败: {e}")
            
        user_code = api_kwargs.get("user_code")
        user_name = api_kwargs.get("user_name") or api_kwargs.get("userName")
        if not user_code:
            try:
                if current_user:
                    user_code = current_user.get("user_code") or current_user.get("user_name") or current_user.get("id")
                    if not user_name:
                        # 优先从当前登录信息中提取用户姓名，保障Langfuse中可读性
                        user_name = (
                            current_user.get("user_name")
                            or current_user.get("username")
                            or current_user.get("name")
                            or current_user.get("nickname")
                        )
                    logger.info(f"GeminiCliClient自动获取到用户代码: {user_code}")
            except Exception as e:
                logger.warning(f"GeminiCliClient自动获取用户代码失败: {e}")
            api_kwargs["user_code"] = user_code
        if not user_name:
            # 若仍未获取用户姓名，则使用用户编码兜底，避免Langfuse中出现空值
            user_name = user_code
        api_kwargs["user_name"] = user_name
        api_kwargs["userName"] = user_name

        # 初始化时间统计变量
        start_time = None
        end_time = None
        done_time = None
        # 提取参数
        messages = api_kwargs.get("messages", [])
        images = api_kwargs.get("images")
        stream = api_kwargs.get("stream", True)
        session_id = api_kwargs.get("session_id", "")
        repo_url = api_kwargs.get("repo_url", "")
        branch = api_kwargs.get("branch", "main")
        app_code = api_kwargs.get("app_code")
        app_id = api_kwargs.get("app_id")
        api_key = api_kwargs.get("api_key", "")
        model = api_kwargs.get("model", "gemini-2.5-flash")
        file_references = api_kwargs.get("file_references", [])
        command_params = api_kwargs.get("command_params", {})
        topic_id = api_kwargs.get("topic_id", None)
        wiki_id = api_kwargs.get("wiki_id", None)

        # 从api_kwargs中获取超时设置，如果没有则使用默认值
        timeout_seconds = float(api_kwargs.get("timeout", self.default_timeout))
        
        # 计算项目路径：App 模式优先使用本地缓存路径 ~/.adalflow/repos/<owner>/<repo-branch>/<repo-branch>
        project_path = "/data/workspace"
        try:
            if (app_code or app_id) and repo_url:
                owner, repo_name, _ = extract_repo_info(repo_url)
                if owner and repo_name:
                    safe_branch = (branch or "main")
                    branch_dir = f"{repo_name}-{safe_branch}"
                    project_path = f"{project_path}/code/{owner}/{branch_dir}"
        except Exception as _e:
            # 回退默认
            project_path = "/data/workspace"
        
        conversation_type = "gemini_cli"
        langfuse_ctx = get_langfuse_context()
        langfuse_enabled = bool(langfuse_ctx.get("enabled"))
        base_metadata = build_langfuse_metadata(
            {
                "session_id": session_id,
                "user_code": user_code,
                "user_name": user_name,
                "repo_url": repo_url,
                "branch": branch,
                "project_path": project_path,
                "provider": "gemini-cli",
                "model": model,
                "app_code": app_code,
                "app_id": app_id,
            },
            conversation_type,
        )

        parent_span_active = bool(api_kwargs.get("langfuse_parent_span_active"))
        # 若上游传入统一trace上下文（通常以当前generation为父），在工具调用等子跨度中使用
        inherited_trace_context: Optional[Dict[str, Any]] = api_kwargs.get("langfuse_trace_context")
        # 当上游（如SSE聊天流）已开启Langfuse根跨度时，此处仅复用上下文，避免重复根/生成跨度

        root_span_handle = None
        root_span = None
        root_span_closed = True

        generation_span_handle = None
        generation_span = None
        generation_span_closed = True

        if langfuse_enabled and not parent_span_active:
            root_metadata = build_langfuse_metadata(
                base_metadata,
                conversation_type,
                stage="request",
                extra={
                    "stream": bool(stream),
                    "message_count": len(messages or []),
                    "app_mode": bool(app_code or app_id),
                },
            )

            generation_metadata = build_langfuse_metadata(
                base_metadata,
                conversation_type,
                stage="generation",
                extra={"app_mode": bool(app_code or app_id)},
            )

            root_span_handle = bind_langfuse_span(
                create_langfuse_span(
                    langfuse_enabled,
                    "gemini-cli",
                    root_metadata,
                )
            )
            root_span = root_span_handle.enter()
            root_span_closed = not bool(root_span)

            generation_span_handle = bind_langfuse_span(
                create_langfuse_span(
                    langfuse_enabled,
                    "gemini-cli.generation",
                    generation_metadata,
                    as_generation=True,
                )
            )
            generation_span = generation_span_handle.enter()
            generation_span_closed = not bool(generation_span)

        def _close_root_span(exc_type=None, exc_val=None, exc_tb=None) -> None:
            """关闭根跨度，确保异常情况下也能正确收尾。"""

            nonlocal root_span_closed
            if root_span_handle and not root_span_closed:
                root_span_handle.exit(exc_type, exc_val, exc_tb)
                root_span_closed = True

        def _close_generation_span(exc_type=None, exc_val=None, exc_tb=None) -> None:
            """关闭内容生成跨度，保持 Langfuse 记录完整。"""

            nonlocal generation_span_closed
            if generation_span_handle and not generation_span_closed:
                generation_span_handle.exit(exc_type, exc_val, exc_tb)
                generation_span_closed = True

        assistant_response_parts: List[str] = []
        usage_details: Optional[Dict[str, int]] = None
        span_exc_info: Tuple[Optional[type], Optional[BaseException], Optional[Any]] = (None, None, None)
        error_code: Optional[str] = None
        tool_call_summaries: List[Dict[str, Any]] = []
        finalized = False

        def _record_assistant_output(text: Optional[str]) -> None:
            """记录模型输出，方便在 Langfuse 中回放完整回答。"""

            if not text:
                return
            assistant_response_parts.append(str(text))

        def _finalize() -> None:
            """统一整理 Langfuse 输出并关闭所有跨度。"""

            nonlocal finalized
            if finalized:
                return
            finalized = True

            assistant_response = "".join(assistant_response_parts)
            duration_ms: Optional[int] = None
            if start_time is not None:
                duration_ms = int((time.time() - start_time) * 1000)

            output_payload: Dict[str, Any] = {
                "assistant_response": _truncate_for_langfuse(assistant_response, 4000),
                "error_code": error_code,
            }
            if duration_ms is not None:
                output_payload["duration_ms"] = duration_ms
            if tool_call_summaries:
                output_payload["tool_calls"] = tool_call_summaries

            if generation_span:
                generation_update_kwargs: Dict[str, Any] = {
                    "output": output_payload,
                    "metadata": {
                        "conversation_type": conversation_type,
                        "provider": "gemini-cli",
                    },
                }
                if usage_details:
                    generation_update_kwargs["usage_details"] = usage_details
                try:
                    generation_span.update(**generation_update_kwargs)
                except Exception as span_error:  # pragma: no cover - 防御性日志
                    logger.debug(f"更新 Langfuse generation 跨度失败: {span_error}")

            if root_span:
                trace_output = {
                    **output_payload,
                    "conversation_type": conversation_type,
                }
                try:
                    root_span.update_trace(output=trace_output)
                except Exception as span_error:  # pragma: no cover
                    logger.debug(f"更新 Langfuse 根跨度失败: {span_error}")

            _close_generation_span(*span_exc_info)
            _close_root_span(*span_exc_info)

        if root_span:
            def _initialize_root_span() -> None:
                """在原始上下文中记录根跨度的输入与元数据。"""

                trace_messages: List[Dict[str, Any]] = []
                for msg in messages or []:
                    if isinstance(msg, dict):
                        trace_messages.append(
                            {
                                "role": msg.get("role"),
                                "content": _truncate_for_langfuse(msg.get("content"), 800),
                            }
                        )
                    else:
                        trace_messages.append({"role": str(msg)})

                root_span.update(
                    input={
                        "messages": trace_messages,
                        "project_path": project_path,
                        "stream": bool(stream),
                    },
                    metadata={
                        "conversation_type": conversation_type,
                        "provider": "gemini-cli",
                    },
                )

                trace_metadata: Dict[str, Any] = {
                    "conversation_type": conversation_type,
                }
                if repo_url:
                    trace_metadata["repo_url"] = repo_url
                if branch:
                    trace_metadata["branch"] = branch
                if app_code:
                    trace_metadata["app_code"] = app_code
                if base_metadata.get("environment"):
                    trace_metadata["environment"] = base_metadata.get("environment")
                if base_metadata.get("labels"):
                    trace_metadata["labels"] = base_metadata.get("labels")

                root_span.update_trace(metadata=trace_metadata)

            if root_span_handle:
                root_span_handle.run(_initialize_root_span)
            else:
                _initialize_root_span()

        if generation_span:
            latest_message = None
            if messages:
                latest_message = messages[-1]
            user_query = None
            if isinstance(latest_message, dict):
                user_query = latest_message.get("content")

            generation_span.update(
                input={
                    "messages": [
                        {
                            "role": msg.get("role") if isinstance(msg, dict) else None,
                            "content": _truncate_for_langfuse(
                                msg.get("content") if isinstance(msg, dict) else str(msg),
                                1200,
                            ),
                        }
                        for msg in messages or []
                    ],
                    "user_query": _truncate_for_langfuse(user_query, 2000),
                },
                model=model,
            )

        tool_call_state: Dict[str, Dict[str, Any]] = {}
        tool_span_contexts: Dict[str, Dict[str, Any]] = {}
        message_tool_map: Dict[str, str] = {}

        def _start_tool_span(
            tool_id: str,
            tool_name: Optional[str],
            arguments_fragment: Optional[str],
            raw_call: Any,
            message_id: Optional[str],
        ) -> None:
            if not langfuse_enabled:
                return
            name = tool_name or "unknown_tool"
            tool_call_summaries.append(
                {
                    "tool_call_id": tool_id,
                    "tool_name": name,
                    "status": "executing",
                }
            )
            metadata = build_langfuse_metadata(
                base_metadata,
                conversation_type,
                stage="tool_call",
                extra={
                    "tool_call_id": tool_id,
                    "tool_name": name,
                },
            )
            ctx = create_langfuse_span(
                langfuse_enabled,
                f"gemini-cli.tool_call.{name}",
                metadata,
                trace_context=inherited_trace_context,
            )
            # 使用 bind_langfuse_span 来管理上下文
            span_handle = bind_langfuse_span(ctx)
            span = span_handle.enter()
            if span:
                span.update(
                    input={
                        "tool_name": name,
                        "arguments_fragment": arguments_fragment,
                        "raw": raw_call,
                    }
                )

            tool_span_contexts[tool_id] = {
                "span": span,
                "context": span_handle,  # 保存 span_handle 而不是原始 ctx
            }
            if message_id:
                message_tool_map[message_id] = tool_id

        def _update_tool_span_metadata(tool_id: str, metadata: Dict[str, Any]) -> None:
            if not metadata:
                return
            entry = tool_span_contexts.get(tool_id)
            if not entry:
                return
            span = entry.get("span")
            if span:
                span.update(metadata=metadata)

        def _end_tool_span(
            tool_id: str,
            *,
            status: Optional[Any] = None,
            output: Optional[Any] = None,
            error: Optional[str] = None,
        ) -> None:
            entry = tool_span_contexts.pop(tool_id, None)
            state = tool_call_state.pop(tool_id, {})
            arguments_text = state.get("arguments")
            tool_name = state.get("name")
            parsed_arguments: Any = None
            if isinstance(arguments_text, str) and arguments_text:
                try:
                    parsed_arguments = json.loads(arguments_text)
                except Exception:
                    parsed_arguments = arguments_text
            elif arguments_text:
                parsed_arguments = arguments_text

            summary_entry: Optional[Dict[str, Any]] = None
            for summary in tool_call_summaries:
                if summary.get("tool_call_id") == tool_id:
                    summary_entry = summary
                    break
            if summary_entry is not None:
                summary_entry["status"] = "failed" if error else "completed"
                if status is not None:
                    summary_entry["status"] = str(status)
                if output is not None:
                    output_text = (
                        json.dumps(output, ensure_ascii=False)
                        if not isinstance(output, str)
                        else output
                    )
                    summary_entry["output_preview"] = _truncate_for_langfuse(output_text, 500)
                if parsed_arguments is not None and "arguments" not in summary_entry:
                    summary_entry["arguments"] = parsed_arguments

            if entry:
                span = entry.get("span")
                ctx = entry.get("context")
                if span:
                    metadata_payload = {
                        "tool_call_id": tool_id,
                        "tool_name": tool_name,
                    }
                    if status is not None:
                        metadata_payload["status"] = status

                    output_payload: Dict[str, Any] = {
                        "tool_name": tool_name,
                    }
                    if parsed_arguments is not None:
                        output_payload["arguments"] = parsed_arguments
                    if output is not None:
                        output_payload["output"] = output
                    if error:
                        output_payload["error"] = error

                    update_kwargs: Dict[str, Any] = {
                        "output": output_payload,
                        "metadata": metadata_payload,
                    }
                    if error:
                        update_kwargs["level"] = "ERROR"
                        update_kwargs["status_message"] = str(error)

                    span.update(**update_kwargs)
                if ctx:
                    # 使用绑定的上下文句柄关闭跨度
                    try:
                        ctx.exit(None, None, None)
                    except Exception as _e:
                        logger.debug(f"关闭工具跨度失败: {_e}")

        def _end_tool_span_with_error(
            message_id: Optional[str],
            error_message: str,
            *,
            status: Optional[Any] = None,
            tool_id: Optional[str] = None,
        ) -> None:
            resolved = tool_id
            if not resolved and message_id:
                resolved = message_tool_map.get(message_id)
            if resolved:
                _end_tool_span(resolved, status=status or "error", error=error_message)

        # 查询job状态并处理（在后台轮询，使用SSE持续推送状态）
        sandbox_url = None
        if (app_code or app_id) or (user_code and repo_url):
            # 轮询直到READY或超时
            poll_interval = 2.0
            deadline = time.time() + float(timeout_seconds)
            capacity_retry_count = 0
            while time.time() < deadline:
                try:
                    sandbox_url, message, job_name = await self._handle_job_status_and_get_url(
                        user_code, repo_url, branch, api_key, 
                        app_code=app_code, app_id=app_id, wiki_id=wiki_id, 
                        user_id=current_user.get("id"), user_name=user_name
                    )
                    if message:
                        # 解析并根据状态执行特殊处理
                        try:
                            parsed_msg = json.loads(message)
                            if isinstance(parsed_msg, dict) and parsed_msg.get("type") == "sandbox_status":
                                status_code = str(parsed_msg.get("status", "")).upper()
                                # 配额满：直接提示并结束SSE
                                if status_code == "QUOTA_EXCEEDED":
                                    # 确保提示明确
                                    if not parsed_msg.get("message"):
                                        parsed_msg["message"] = "个人沙盒配额已满，请前往个人沙盒管理页面释放后再试"
                                    parsed_msg["can_retry"] = False
                                    yield json.dumps(parsed_msg), ""
                                    error_code = status_code
                                    _finalize()
                                    return
                                # 总量已满或系统繁忙：最多重试3次
                                if status_code in ("TOTAL_CAPACITY_FULL", "SYSTEM_BUSY"):
                                    capacity_retry_count += 1
                                    # 附加重试次数信息
                                    base_msg = parsed_msg.get("message") or "系统总资源已满，正在重试"
                                    parsed_msg["message"] = f"{base_msg}（第{capacity_retry_count}/3次）"
                                    parsed_msg["can_retry"] = True
                                    yield json.dumps(parsed_msg), ""
                                    if capacity_retry_count >= 3:
                                        final_msg = {
                                            "type": "sandbox_status",
                                            "status": "TOTAL_CAPACITY_FULL",
                                            "message": "系统总资源已满，已重试3次，请稍后再试或稍晚再发起请求",
                                            "can_retry": True,
                                            "timestamp": time.time()
                                        }
                                        yield json.dumps(final_msg), ""
                                        error_code = "TOTAL_CAPACITY_FULL"
                                        _finalize()
                                        return
                                    # 未达上限则继续轮询
                                    await asyncio.sleep(poll_interval)
                                    continue
                        except Exception:
                            # 非结构化状态或解析失败，按原样推送
                            pass
                        # 推送当前沙盒状态（非特殊情况）
                        yield message, ""
                    if sandbox_url:
                        # 进入READY，通知前端已就绪
                        ready_status = {
                            "type": "sandbox_status",
                            "status": SandboxStatus.READY.value,
                            "message": "个人沙盒已就绪",
                            "can_retry": False,
                            "timestamp": time.time()
                        }
                        yield json.dumps(ready_status), ""
                        break
                except Exception as poll_error:
                    # 推送查询失败状态，但不中断，直到超时
                    error_status = {
                        "type": "sandbox_status",
                        "status": "QUERY_FAILED",
                        "message": f"个人沙盒状态查询失败: {poll_error}",
                        "can_retry": True,
                        "timestamp": time.time()
                    }
                    logger.warning(f"Sandbox status poll error: {poll_error}")
                    yield json.dumps(error_status), ""
                await asyncio.sleep(poll_interval)
            if not sandbox_url:
                # 超时未就绪
                raise httpx.TimeoutException("等待个人沙盒就绪超时，请稍后重试或联系管理员。")

        # 构建请求URL - 优先使用沙盒URL，否则使用默认URL
        if sandbox_url:
            api_url = f"{sandbox_url}"
            logger.info(f"使用沙盒URL调用Gemini CLI API: {api_url}")
        else:
            api_url = f"{self.base_url}/v1/chat/completions"
            logger.info(f"使用默认URL调用Gemini CLI API: {api_url}")
        
        logger.info(f"Calling Gemini CLI API with timeout: {timeout_seconds}s")

        from api.config import get_input_template_config
        # 处理文件引用
        file_list = ""
        file_reference_contents = ""
        file_reference_directories = ""
        if file_references and user_code:
            file_list, file_reference_contents, file_reference_directories = self._read_file_references(file_references, repo_url, branch, user_code)
            if file_list:
                logger.info(f"已读取 {len(file_references)} 个文件引用")
        
        # 处理命令参数
        if command_params and user_code and repo_url:
            original_question = messages[-1]["content"] if messages else ""
            processed_question = self._process_command_params(command_params, original_question, repo_url, branch, user_code)
            if processed_question != original_question:
                messages[-1]["content"] = processed_question
                logger.info(f"已处理命令参数: {command_params}")
        elif command_params and not user_code:
            logger.warning("命令参数存在但缺少用户代码，跳过命令处理")
        elif command_params and not repo_url:
            logger.warning("命令参数存在但缺少仓库URL，跳过命令处理")
        
        show_file_reference = file_list != "" or file_reference_contents != "" or file_reference_directories != ""
        template_config = get_input_template_config()
        show_docchain = template_config.get("show_docchain", False) and topic_id is not None and topic_id != ""
        user_input = get_template_input(show_file_reference=show_file_reference, show_docchain=show_docchain).strip()
        if user_input:
            if show_file_reference:
                if show_docchain:
                    user_input = user_input.format(current_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"), topic_id=topic_id, file_list=file_list, file_contents=file_reference_contents, directory_list=file_reference_directories, user_input=messages[-1]["content"])
                else:
                    user_input = user_input.format(current_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"), file_list=file_list, file_contents=file_reference_contents, directory_list=file_reference_directories, user_input=messages[-1]["content"])
            else:
                if show_docchain:
                    user_input = user_input.format(current_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"), topic_id=topic_id, user_input=messages[-1]["content"])
                else:
                    user_input = user_input.format(current_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"), user_input=messages[-1]["content"])
            # system_message = {"role": "system", "content": sys_prompt}
            # messages.insert(-2, system_message)
            messages[-1]["content"] = user_input.strip()

        if images:
            messages.extend(images)

        # 构建请求体 - 简化格式，与OpenAI兼容
        request_data = {
            "messages": messages,
            "stream": stream,
            "project_path": project_path,
            "session_id": session_id,
            "api_key": api_key,
            "model": model  # 添加模型参数
        }
        
        logger.info(f"Using Gemini model: {model}")
        
        # 设置必要的请求头，关键是Accept: text/event-stream
        headers = {
            "Content-Type": "application/json",
            "Accept": "text/event-stream",
            "Connection": "keep-alive",
            "Cache-Control": "no-cache"
        }
        
        # 添加客户端IP透传头
        if client_ip:
            headers["X-Client-IP"] = client_ip
            headers["X-Forwarded-For"] = client_ip
        
        logger.info(f"Request data: {request_data}")
        logger.info(f"Request headers: {headers}")
        
        try:
            # 使用简单的超时设置，避免复杂配置可能引起的问题
            async with httpx.AsyncClient(timeout=timeout_seconds) as client:
                async with client.stream("POST", api_url, json=request_data, headers=headers) as response:
                    logger.debug(f"Response status: {response.status_code}, headers: {response.headers}")
                    
                    if response.status_code != 200:
                        # 如果响应不成功，获取错误信息
                        error_text = await response.aread()
                        error_message = error_text.decode('utf-8')
                        logger.error(f"Error from Gemini CLI API: {error_message}")
                        error_info = error_message
                        error_data = {
                            "type": "tool_error",
                            "error": {
                                "type": "content_api_error",
                                "message": error_info,
                                "code": str(response.status_code)
                            },
                            "timestamp": time.time()
                        }
                        yield json.dumps(error_data, ensure_ascii=False), ""
                        error_code = str(response.status_code)
                        _finalize()
                        return
                    
                    # 使用aiter_bytes并正确处理SSE流
                    buffer = b''
                    async for chunk in response.aiter_bytes():
                        # 记录原始字节流以便调试
                        logger.info(f"Received chunk: {len(chunk)} bytes")
                        buffer += chunk
                        
                        # 处理缓冲区中的每一行
                        while b'\n' in buffer:
                            line, buffer = buffer.split(b'\n', 1)
                            if not line:
                                continue  # 跳过空行
                                
                            try:
                                line_str = line.decode('utf-8').strip()
                                if not line_str:
                                    continue  # 跳过空字符串
                                
                                # 调试日志
                                logger.info(f"Processing line: {line_str}")
                                
                                if line_str.startswith("data: "):
                                    # 解析SSE数据
                                    data = line_str[6:]  # 移除 "data: " 前缀
                                    if data == "[DONE]":
                                    # 流式响应结束
                                        done_time = time.time()
                                        logger.info("Stream response completed with [DONE] marker")
                                    # 输出时间统计信息
                                        if start_time is not None and end_time is not None:
                                            total_time = end_time - start_time
                                            done_time_diff = done_time - start_time if done_time is not None else None
                                            logger.info(f"Total response time: {total_time:.2f} seconds")
                                            if done_time_diff is not None:
                                                logger.info(f"Time to [DONE]: {done_time_diff:.2f} seconds")
                                        _finalize()
                                        return
                                    
                                    # 解析JSON数据
                                    parsed_data = json.loads(data)
                                        
                                    # 记录第一个有效响应的时间
                                    if start_time is None:
                                        start_time = time.time()
                                        
                                        # 更新Job访问时间（在第一次成功响应时）
                                        if user_code and repo_url:
                                            try:
                                                await sandbox_service.update_sandbox_access(user_code, repo_url, branch, job_name)
                                                logger.debug(f"API调用成功，已更新Job访问时间: {user_code}/{repo_url}/{branch}")
                                            except Exception as update_error:
                                                logger.warning(f"API调用时更新Job访问时间失败: {update_error}")
                                        
                                        # 记录最后一个有效响应的时间
                                    end_time = time.time()
                                        
                                        # 检查是否包含错误信息
                                    if "error" in parsed_data:
                                        error_info = parsed_data["error"]
                                        error_data = {
                                                "type": "tool_error",
                                                "error": {
                                                    "type": "content_api_error",
                                                    "message": error_info.get('message', ''),
                                                    "code": "400"
                                                },
                                                "timestamp": parsed_data.get("created",int(datetime.now().timestamp()))
                                            }
                                        yield json.dumps(error_data, ensure_ascii=False), line_str
                                        _end_tool_span_with_error(
                                            parsed_data.get("id"),
                                            error_info.get('message', ''),
                                            tool_id=message_tool_map.get(parsed_data.get("id")),
                                        )
                                        logger.error(f"Tool call error: {error_info}")
                                        continue
                                        
                                    # 检查是否是标准的聊天完成响应
                                    if "choices" in parsed_data:
                                        choices = parsed_data.get("choices", [])
                                        if choices and len(choices) > 0:
                                            delta = choices[0].get("delta", {})
                                                
                                            # 检查是否有工具调用
                                            if "tool_calls" in delta:
                                                    tool_calls = delta["tool_calls"]
                                                    tool_timestamp = parsed_data.get("created", int(datetime.now().timestamp()))
                                                    for idx, tool_call in enumerate(tool_calls):
                                                        tool_id = (
                                                            tool_call.get("id")
                                                            or parsed_data.get("id")
                                                            or f"tool-{idx}"
                                                        )
                                                        function_info = tool_call.get("function", {}) or {}
                                                        tool_name = function_info.get("name")
                                                        arguments_fragment = function_info.get("arguments")
                                                        state = tool_call_state.setdefault(
                                                            tool_id,
                                                            {
                                                                "name": tool_name or "unknown_tool",
                                                                "arguments": "",
                                                            },
                                                        )
                                                        if tool_name and not state.get("name"):
                                                            state["name"] = tool_name
                                                        if arguments_fragment:
                                                            state["arguments"] = (
                                                                state.get("arguments", "") + arguments_fragment
                                                            )

                                                        current_message_id = parsed_data.get("id")
                                                        if tool_id not in tool_span_contexts:
                                                            _start_tool_span(
                                                                tool_id,
                                                                state.get("name"),
                                                                arguments_fragment,
                                                                tool_call,
                                                                current_message_id,
                                                            )
                                                        else:
                                                            ctx_span = tool_span_contexts.get(tool_id)
                                                            if (
                                                                ctx_span
                                                                and ctx_span[1]
                                                                and arguments_fragment
                                                            ):
                                                                ctx_span[1].update(
                                                                    input={
                                                                        "arguments_fragment": arguments_fragment
                                                                    }
                                                                )
                                                            if current_message_id:
                                                                message_tool_map[current_message_id] = tool_id

                                                        _update_tool_span_metadata(
                                                            tool_id,
                                                            {
                                                                "tool_call_id": tool_id,
                                                                "tool_name": state.get("name"),
                                                                "timestamp": tool_timestamp,
                                                            },
                                                        )

                                                    # 构造工具调用信息
                                                    tool_call_data = {
                                                        "tool_calls": tool_calls,
                                                        "timestamp": parsed_data.get("created", int(datetime.now().timestamp()))
                                                    }
                                                    if "id" in parsed_data:
                                                            response_data = {
                                                                "type": "tool_call",
                                                                "id": parsed_data["id"],
                                                                "content": tool_call_data
                                                            }
                                                            logger.info(f"Yielding content with id: {response_data}")
                                                            yield json.dumps(response_data, ensure_ascii=False), line_str
                                                    logger.info(f"Yielding tool call: {tool_call_data}")
                                                    continue
                                                
                                                # 检查是否有工具执行状态
                                            if "tool_execution_status" in delta:
                                                    status = delta["tool_execution_status"]
                                                    message_id = parsed_data.get("id")
                                                    tool_id = None
                                                    if isinstance(status, dict):
                                                        tool_id = (
                                                            status.get("tool_call_id")
                                                            or status.get("id")
                                                        )
                                                    if not tool_id and message_id:
                                                        tool_id = message_tool_map.get(message_id) or message_id
                                                    if tool_id:
                                                        tool_state = tool_call_state.setdefault(tool_id, {})
                                                        tool_state["status"] = status
                                                        _update_tool_span_metadata(
                                                            tool_id,
                                                            {
                                                                "status": status,
                                                                "timestamp": parsed_data.get("created", int(datetime.now().timestamp())),
                                                            },
                                                        )

                                                        final_state = None
                                                        output_payload = None
                                                        error_message = None
                                                        if isinstance(status, dict):
                                                            final_state = status.get("state") or status.get("status")
                                                            output_payload = status.get("output") or status.get("result")
                                                            error_message = status.get("error") or status.get("message")
                                                        elif isinstance(status, str):
                                                            final_state = status

                                                        if final_state:
                                                            normalized_state = str(final_state).lower()
                                                            if normalized_state in {"completed", "complete", "done"}:
                                                                _end_tool_span(
                                                                    tool_id,
                                                                    status=status,
                                                                    output=output_payload,
                                                                )
                                                            elif normalized_state in {"failed", "error"}:
                                                                _end_tool_span(
                                                                    tool_id,
                                                                    status=status,
                                                                    output=output_payload,
                                                                    error=error_message or str(final_state),
                                                                )

                                                    # 构造工具执行状态信息
                                                    status_data = {
                                                        "status": status,
                                                        "timestamp": parsed_data.get("created", int(datetime.now().timestamp()))
                                                    }
                                                    if "id" in parsed_data:
                                                            response_data = {
                                                                "type": "tool_execution_status", 
                                                                "id": parsed_data["id"],
                                                                "content": status_data
                                                            }
                                                            logger.info(f"Yielding content with id: {response_data}")
                                                            yield json.dumps(response_data, ensure_ascii=False), line_str
                                                    logger.info(f"Yielding tool status: {status_data}") 
                                                    continue
                                                
                                                # 处理普通内容
                                            content = delta.get("content", "")
                                            if content:
                                                # 检查是否为API错误信息，如果是则转换为工具错误
                                                is_error, out_code, error = self._is_api_error_content(content)
                                                if is_error:
                                                    if out_code and error:
                                                        error_data = {
                                                            "type": "tool_error",
                                                            "error": {
                                                                "type": "content_api_error",
                                                                "message": content,
                                                                "code": out_code
                                                            },
                                                            "timestamp": parsed_data.get("created", int(datetime.now().timestamp()))
                                                        }
                                                        yield json.dumps(error_data, ensure_ascii=False), line_str
                                                        _end_tool_span_with_error(
                                                            parsed_data.get("id"),
                                                            content,
                                                            tool_id=message_tool_map.get(parsed_data.get("id")),
                                                        )
                                                    else:
                                                        error = json.loads(content)
                                                        logger.warning(f"Detected API error in content: {content}")
                                                        error_code = error.get('status_code', '400')
                                                        error_data = {
                                                            "type": "tool_error",
                                                            "error": {
                                                                "type": "content_api_error",
                                                                "message": content,
                                                                "code": error_code if isinstance(error_code, str) else str(error_code)
                                                            },
                                                            "timestamp": parsed_data.get("created", int(datetime.now().timestamp()))
                                                        }
                                                        yield json.dumps(error_data, ensure_ascii=False), line_str
                                                        _end_tool_span_with_error(
                                                            parsed_data.get("id"),
                                                            content,
                                                            tool_id=message_tool_map.get(parsed_data.get("id")),
                                                        )
                                                else:
                                                    # 如果有id，将id和内容一起返回
                                                    if "id" in parsed_data:
                                                        response_data = {
                                                            "type": "message",
                                                            "id": parsed_data["id"],
                                                            "content": content
                                                        }
                                                        logger.info(f"Yielding content with id: {response_data}")
                                                        _record_assistant_output(content)
                                                        yield json.dumps(response_data, ensure_ascii=False), line_str
                                                    else:
                                                        logger.info(f"Yielding content: {content}")
                                                        _record_assistant_output(content)
                                                        yield content.strip(), line_str
                                            else:
                                                # 即使没有内容，如果有id也返回
                                                error = delta.get('error', {})
                                                if error:
                                                    if isinstance(error, str):
                                                        message = error
                                                        code = "400"
                                                    else:
                                                        detail = error
                                                        if detail.get('error_detail'):
                                                            detail = detail.get('error_detail')
                                                        if detail.get('error'):
                                                            detail = detail.get('error', {})
                                                        if detail.get('error_message'):
                                                            detail['message'] = detail.get('error_message')
                                                        message = detail.get('message', '')
                                                        code = detail.get('status', '400')
                                                    error_data = {
                                                        "type": "tool_error",
                                                        "error": {
                                                            "type": "content_api_error",
                                                            "message": message,
                                                            "code": code if isinstance(code, str) else str(code),
                                                        },
                                                    }
                                                    yield json.dumps(error_data, ensure_ascii=False), line_str
                                                    _end_tool_span_with_error(
                                                        parsed_data.get("id"),
                                                        message,
                                                        tool_id=message_tool_map.get(parsed_data.get("id")),
                                                    )
                                                else:
                                                    if "id" in parsed_data:
                                                        response_data = {
                                                            "type": "message",
                                                            "id": parsed_data["id"],
                                                            "content": "",
                                                        }
                                                        logger.info(f"Yielding empty content with id: {response_data}")
                                                        yield json.dumps(response_data, ensure_ascii=False), line_str
                                                    else:
                                                        logger.info(f"Empty content delta: {delta}")
                                        else:
                                            logger.info(f"No choices in response: {parsed_data}")
                            except UnicodeDecodeError as e:
                                logger.error(f"Failed to decode line: {e}")
                                raise e
                    
                    # 处理剩余的缓冲区
                    if buffer:
                        try:
                            remaining = buffer.decode('utf-8').strip()
                            if remaining and remaining.startswith("data: "):
                                data = remaining[6:]
                                if data == "[DONE]":
                                    # 流式响应结束
                                    done_time = time.time()
                                    logger.info("Stream response completed with [DONE] marker")
                                    # 输出时间统计信息
                                    if start_time is not None and end_time is not None:
                                        total_time = end_time - start_time
                                        done_time_diff = done_time - start_time if done_time is not None else None
                                        logger.info(f"Total response time: {total_time:.2f} seconds")
                                        if done_time_diff is not None:
                                            logger.info(f"Time to [DONE]: {done_time_diff:.2f} seconds")
                                    _finalize()
                                    return
                                elif data != "[DONE]":
                                        parsed_data = json.loads(data)
                                        if "choices" in parsed_data:
                                            choices = parsed_data.get("choices", [])
                                            if choices and len(choices) > 0:
                                                delta = choices[0].get("delta", {})
                                                # 检查工具调用和状态
                                                if "tool_calls" in delta:
                                                    tool_calls = delta["tool_calls"]
                                                    tool_timestamp = parsed_data.get("created", int(datetime.now().timestamp()))
                                                    for idx, tool_call in enumerate(tool_calls):
                                                        tool_id = (
                                                            tool_call.get("id")
                                                            or parsed_data.get("id")
                                                            or f"tool-{idx}"
                                                        )
                                                        function_info = tool_call.get("function", {}) or {}
                                                        tool_name = function_info.get("name")
                                                        arguments_fragment = function_info.get("arguments")
                                                        state = tool_call_state.setdefault(
                                                            tool_id,
                                                            {
                                                                "name": tool_name or "unknown_tool",
                                                                "arguments": "",
                                                            },
                                                        )
                                                        if tool_name and not state.get("name"):
                                                            state["name"] = tool_name
                                                        if arguments_fragment:
                                                            state["arguments"] = (
                                                                state.get("arguments", "") + arguments_fragment
                                                            )

                                                        current_message_id = parsed_data.get("id")
                                                        if tool_id not in tool_span_contexts:
                                                            _start_tool_span(
                                                                tool_id,
                                                                state.get("name"),
                                                                arguments_fragment,
                                                                tool_call,
                                                                current_message_id,
                                                            )
                                                        else:
                                                            ctx_span = tool_span_contexts.get(tool_id)
                                                            if (
                                                                ctx_span
                                                                and ctx_span[1]
                                                                and arguments_fragment
                                                            ):
                                                                ctx_span[1].update(
                                                                    input={
                                                                        "arguments_fragment": arguments_fragment
                                                                    }
                                                                )
                                                            if current_message_id:
                                                                message_tool_map[current_message_id] = tool_id

                                                        _update_tool_span_metadata(
                                                            tool_id,
                                                            {
                                                                "tool_call_id": tool_id,
                                                                "tool_name": state.get("name"),
                                                                "timestamp": tool_timestamp,
                                                            },
                                                        )

                                                    tool_call_data = {
                                                        "type": "tool_call",
                                                        "tool_calls": tool_calls,
                                                        "timestamp": parsed_data.get("created", int(datetime.now().timestamp()))
                                                    }
                                                    yield json.dumps(tool_call_data, ensure_ascii=False), line_str      
                                                elif "tool_execution_status" in delta:
                                                    status = delta["tool_execution_status"]
                                                    message_id = parsed_data.get("id")
                                                    tool_id = None
                                                    if isinstance(status, dict):
                                                        tool_id = (
                                                            status.get("tool_call_id")
                                                            or status.get("id")
                                                        )
                                                    if not tool_id and message_id:
                                                        tool_id = message_tool_map.get(message_id) or message_id
                                                    if tool_id:
                                                        tool_state = tool_call_state.setdefault(tool_id, {})
                                                        tool_state["status"] = status
                                                        _update_tool_span_metadata(
                                                            tool_id,
                                                            {
                                                                "status": status,
                                                                "timestamp": parsed_data.get("created", int(datetime.now().timestamp())),
                                                            },
                                                        )

                                                        final_state = None
                                                        output_payload = None
                                                        error_message = None
                                                        if isinstance(status, dict):
                                                            final_state = status.get("state") or status.get("status")
                                                            output_payload = status.get("output") or status.get("result")
                                                            error_message = status.get("error") or status.get("message")
                                                        elif isinstance(status, str):
                                                            final_state = status

                                                        if final_state:
                                                            normalized_state = str(final_state).lower()
                                                            if normalized_state in {"completed", "complete", "done"}:
                                                                _end_tool_span(
                                                                    tool_id,
                                                                    status=status,
                                                                    output=output_payload,
                                                                )
                                                            elif normalized_state in {"failed", "error"}:
                                                                _end_tool_span(
                                                                    tool_id,
                                                                    status=status,
                                                                    output=output_payload,
                                                                    error=error_message or str(final_state),
                                                                )

                                                    status_data = {
                                                        "type": "tool_execution_status", 
                                                        "status": status,
                                                        "timestamp": parsed_data.get("created", int(datetime.now().timestamp()))
                                                    }
                                                    yield json.dumps(status_data, ensure_ascii=False), line_str
                                                elif "content" in delta:
                                                    content = delta.get("content", "")
                                                    if content:
                                                        # 检查是否为API错误信息
                                                        is_error, out_code, error = self._is_api_error_content(content)
                                                        if is_error:
                                                            if out_code and error:
                                                                logger.warning(f"Detected API error in remaining buffer: {content}")
                                                                error_code = out_code
                                                            error_data = {
                                                                "type": "tool_error",
                                                                "error": {
                                                                    "type": "buffer_api_error",
                                                                    "message": content,
                                                                    "code": error_code if isinstance(error_code, str) else str(error_code)
                                                                },
                                                                "timestamp": parsed_data.get("created", int(datetime.now().timestamp()))
                                                            }
                                                            yield json.dumps(error_data, ensure_ascii=False), line_str
                                                            _end_tool_span_with_error(
                                                                parsed_data.get("id"),
                                                                content,
                                                                tool_id=message_tool_map.get(parsed_data.get("id")),
                                                            )
                                                        else:
                                                            # 记录第一个有效响应的时间
                                                            if start_time is None:
                                                                start_time = time.time()
                                                
                                                    # 更新Job访问时间（在处理剩余缓冲区时）
                                                    if user_code and repo_url:
                                                        try:
                                                            await sandbox_service.update_sandbox_access(user_code, repo_url, branch)
                                                            logger.debug(f"剩余缓冲区处理时更新Job访问时间: {user_code}/{repo_url}/{branch}")
                                                        except Exception as update_error:
                                                            logger.warning(f"剩余缓冲区处理时更新Job访问时间失败: {update_error}")
                                                
                                                    # 记录最后一个有效响应的时间
                                                    end_time = time.time()
                                                    response_data = {
                                                        "type": "message",
                                                        "id": parsed_data.get("id", ""),
                                                        "content": content
                                                    }
                                                    logger.info(f"剩余的buffer中返回: {response_data}")
                                                    _record_assistant_output(content)
                                                    yield json.dumps(response_data), line_str
                                                elif "error" in delta:
                                                    error = delta.get('error', {})
                                                    if error and isinstance(error, str):
                                                        logger.error(f"Detected API error in content: {error}")
                                                        error_data = {
                                                            "type": "tool_error",
                                                            "error": {
                                                                "type": "content_api_error",
                                                                "message": error,
                                                                "code": "400"
                                                            },
                                                        }
                                                        yield json.dumps(error_data, ensure_ascii=False), line_str
                                                        _end_tool_span_with_error(
                                                            parsed_data.get("id"),
                                                            error,
                                                            tool_id=message_tool_map.get(parsed_data.get("id")),
                                                        )
                                                    elif error and isinstance(error, dict):
                                                        if error.get('error_detail'):
                                                            error = error.get('error_detail')
                                                            if error.get('error'):
                                                                error = error.get('error', {})
                                                    elif error.get('error'):
                                                        error = error.get('error', {})
                                                    elif error.get('error_message'):
                                                        error['message'] = error.get('error_message')
                                                        if self._is_api_error(error):
                                                            logger.warning(f"Detected API error in content: {error}")
                                                            error_data = {
                                                                "type": "tool_error",
                                                                "error": {
                                                                    "type": "content_api_error",
                                                                    "message": error.get('message', ''),
                                                                    "code": error_code if isinstance(error_code, str) else str(error_code)
                                                                },
                                                            }
                                                            yield json.dumps(error_data, ensure_ascii=False), line_str
                                                            _end_tool_span_with_error(
                                                                parsed_data.get("id"),
                                                                error.get('message', ''),
                                                                tool_id=message_tool_map.get(parsed_data.get("id")),
                                                            )
                                                        _end_tool_span_with_error(
                                                            parsed_data.get("id"),
                                                            error.get('message', ''),
                                                            tool_id=message_tool_map.get(parsed_data.get("id")),
                                                        )
                        except UnicodeDecodeError as e:
                            logger.error(f"Failed to decode remaining buffer")
                            raise e
        except httpx.TimeoutException as e:
            error_msg = f"Gemini CLI API请求超时(>{timeout_seconds}秒)，请联系管理员或稍后再试"
            logger.error(f"Timeout error calling Gemini CLI API: {e}")
            error_code = "TIMEOUT"
            span_exc_info = (type(e), e, e.__traceback__)
            # 抛出超时异常，使调用者能够正确捕获
            raise httpx.TimeoutException(error_msg) from e
        except Exception as e:
            logger.error(f"Error calling Gemini CLI API: {e}")
            span_exc_info = (type(e), e, e.__traceback__)
            if error_code is None:
                error_code = "UNKNOWN_ERROR"
            # 重新抛出异常，使调用者能够正确捕获
            raise e
        finally:
            for remaining_id in list(tool_span_contexts.keys()):
                _end_tool_span(remaining_id, status="cancelled")
            _finalize()
