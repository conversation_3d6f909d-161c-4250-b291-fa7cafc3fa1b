"""
DocChain 数据传输对象 (DTO)

该模块定义了与DocChain API交互所需的数据结构，包括请求和响应对象。
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union

@dataclass
class TopicConfig:
    """Topic配置信息"""
    name: str
    env_lang: str = "zh"
    llm_model_name: str = "gpt-4-omni"
    chunk_size: int = 512
    embedding_model_name: str = "auto"
    extra: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """初始化后处理，确保extra字段包含所有必要的配置项"""
        if self.extra is None:
            self.extra = {}
            
        # 确保基本配置存在
        if "comment" not in self.extra:
            self.extra["comment"] = None
        if "llm_model_name" not in self.extra:
            self.extra["llm_model_name"] = self.llm_model_name
        if "chunk_size" not in self.extra:
            self.extra["chunk_size"] = self.chunk_size
        if "env_lang" not in self.extra:
            self.extra["env_lang"] = self.env_lang
        if "chat_with_neo4j" not in self.extra:
            self.extra["chat_with_neo4j"] = "common"
        if "embedding_model_name" not in self.extra:
            self.extra["embedding_model_name"] = self.embedding_model_name
        if "image_search" not in self.extra:
            self.extra["image_search"] = "cnclip"
            
        # 确保common_config存在并且类型正确
        if "common_config" not in self.extra:
            self.extra["common_config"] = {
                "md_title_rec": "false",
                "remain_origin_level": "true",
                "split_level": "4",
                "split_type": "chunk_size",
                "custom_split_str": "",
                "chunk_size": self.chunk_size,
                "image_chunk_separately": "true",
                "ocr_provider": "paddle",
                "ocr_lang": ["chinese"],
                "summary": "true",
                "summary_language": "auto",
                "summary_chapter": "true",
                "summary_image": "true",
                "generate_qa": "false",
                "qa_pair_prompt": "根据要求生成QA问答对：1. 根据文本内容返回QA问答对 2. 生成的{qa_count}个问答对，内容要求是纯文本 3. {language_text} 使用json的格式返回多个问题和答案:{json} ------ 文本内容如下：{context}",
                "describe_image": "false",
                "describe_image_prompt": "描述图片内容，要求简洁准确，层次分明",
                "describe_table_prompt": "以上表格数据帮我生成描述，要求生成的描述要覆盖所有字段，不要返回多余的内容。",
                "describe_table": "false",
                "structure_md": "false",
                "structure_md_prompt": "优化以下数据结构，要求层次分明，内容简洁，不丢失关键信息，内容如下：",
                "store_to_es": "true"
            }
            
        # 确保extra_config存在
        if "extra_config" not in self.extra:
            self.extra["extra_config"] = {
                "topic_label": "DEFAULT",
                "is_default": "false"
            }
            
        # 确保pdf_config存在
        if "pdf_config" not in self.extra:
            self.extra["pdf_config"] = {
                "pdf_split_model": "ocr",
                "pdf_split_pages_per_file": 0,
                "pdf_split_model_correction": "true",
                "layout_analysis": "true",
                "pdf_slice_size": "1",
                "pdf_table_setting": "lines",
                "pdf_page_crop": "true",
                "image_remove_watermark": "false",
                "pdf_ai_table_header_rec": "false",
                "pdf_table_split_lines": -1,
                "table_image_extraction": "false"
            }
            
        # 确保image_config存在
        if "image_config" not in self.extra:
            self.extra["image_config"] = {
                "image_split_model": "ocr",
                "image_remove_watermark": "false",
                "layout_analysis": "true"
            }
            
        # 确保excel_config存在
        if "excel_config" not in self.extra:
            self.extra["excel_config"] = {
                "excel_convert_html": "false",
                "excel_header_lines": 1,
                "excel_split_mode": "chunk",
                "write_table_html": "false",
                "write_table_json": "false",
                "excel_type": "normal"
            }
            
        # 确保ppt_config存在
        if "ppt_config" not in self.extra:
            self.extra["ppt_config"] = {
                "mllm_ppt_rec": "false",
                "convert_pdf": "true"
            }
            
        # 确保word_config存在
        if "word_config" not in self.extra:
            self.extra["word_config"] = {
                "docx_append_numbering": "false",
                "handle_by_pdf": "false",
                "docx_table_line_size": 5,
                "docx_replace_blank_lines": "false",
                "docx_remove_image_links": "true"
            }
            
        # 确保retrieve_config存在
        if "retrieve_config" not in self.extra:
            self.extra["retrieve_config"] = {
                "save_table_data_into_es": "true",
                "simplify_user_query": "false",
                "auto_detect_title": "false",
                "retry_time_limit": 3,
                "recall_node_limit": "10",
                "recall_data_limit": "50",
                "recall_data_empty_response": "N",
                "continuous_chat_flag": "false",
                "retrieve_count_percent": 1,
                "rerank_reserve_size": 30,
                "llm_temperature": 0,
                "llm_prompt_tokens": 14000,
                "llm_max_tokens": 2000,
                "chat_ranking_mode": "rerank",
                "replenish_context_and_summary_tree": "false",
                "img_filter_rerank_score": 0.3,
                "selected_similar_score": 0.5,
                "retrieve_rerank_model": "NONE",
                "retrieve_file_data": "false",
                "response_lang": "auto",
                "chat_system_prompt": "N",
                "images_retrieve_strategy": "2",
                "retrieve_internet_result": "false",
                "retrieve_search_engines": "bing",
                "retrieve_search_engines_include_sites": "N",
                "retrieve_search_bm25_weight_config": "N",
                "retrieve_doc_reference_flag": "auto",
                "translate_user_question_to_topic_language": "false",
                "topic_summary_llm_model_name": "N",
                "keep_all_the_contents_from_one_doc_strategy": "NONE",
                "retrieve_doc_score_limit": -1,
                "topic_translate_llm_model_name": "N",
                "interrupt_llm_score_limit": "0",
                "interrupt_if_no_similarity_chunk": "false",
                "retrieve_doc_mode": "1",
                "expand_docs_with_graph": "false",
                "deep_search_flag": "auto",
                "mllm_table_chat": "NONE",
                "user_query_rewrite": "NONE"
                
            }
            
        # 确保数据类型正确
        # 检查并修正布尔值类型（确保是字符串"true"或"false"）
        self._fix_boolean_values(self.extra["common_config"])
        self._fix_boolean_values(self.extra["extra_config"])
        self._fix_boolean_values(self.extra["pdf_config"])
        self._fix_boolean_values(self.extra["image_config"])
        self._fix_boolean_values(self.extra["excel_config"])
        self._fix_boolean_values(self.extra["ppt_config"])
        self._fix_boolean_values(self.extra["word_config"])
        self._fix_boolean_values(self.extra["retrieve_config"])
        
    def _fix_boolean_values(self, config_dict):
        """修复配置字典中的布尔值，确保它们是字符串类型的"true"或"false" """
        for key, value in config_dict.items():
            if isinstance(value, bool):
                config_dict[key] = str(value).lower()
            # 确保数字类型保持为数字，但recall_data_limit要保持为字符串
            elif key in ["excel_header_lines", "docx_table_line_size", "retry_time_limit", 
                        "retrieve_count_percent", "rerank_reserve_size", "llm_temperature", 
                        "llm_prompt_tokens", "llm_max_tokens", "img_filter_rerank_score", 
                        "selected_similar_score", "retrieve_doc_score_limit", "pdf_table_split_lines",
                        "pdf_split_pages_per_file"]:
                # 这些字段应该是数字类型
                if isinstance(value, str) and value.isdigit():
                    config_dict[key] = int(value)
                elif isinstance(value, str) and value.replace(".", "", 1).isdigit():
                    config_dict[key] = float(value)


@dataclass
class TopicCreateRequest:
    """创建Topic的请求对象"""
    name: str
    operation: str = "modify"
    extra: Dict[str, Any] = field(default_factory=dict)
    id: Optional[int] = None
    prompt: str = ""
    introduce: str = ""


@dataclass
class TopicInfo:
    """Topic信息"""
    id: int
    name: str
    state: str
    create_date: str
    state_date: str
    extra: Dict[str, Any]
    prompt: Optional[str] = None
    introduce: Optional[str] = None


@dataclass
class DocumentUploadRequest:
    """文档上传请求"""
    topic_id: str
    files: List[str]
    event: Optional[Dict[str, Any]] = None


@dataclass
class DocumentUploadResponse:
    """文档上传响应"""
    success: bool
    message: str = ""
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    uploaded_count: Optional[int] = None
    failed_files: Optional[List[str]] = None


@dataclass
class SearchRequest:
    """搜索请求"""
    query: str
    topic_id: str
    size: int = 5
    with_context: bool = False
    score: float = 0.1
    ranking_mode: str = "rrf"
    rrf_k: int = 60


@dataclass
class SearchResult:
    """搜索结果项"""
    score: float
    data: Dict[str, Any]


@dataclass
class SearchResponse:
    """搜索响应"""
    text: List[SearchResult]
    image: List[SearchResult]
    table: List[SearchResult]


@dataclass
class ChatMessage:
    """聊天消息"""
    role: str
    content: str


@dataclass
class ChatCompletionRequest:
    """聊天完成请求"""
    model: str
    messages: List[ChatMessage]
    temperature: float = 0.7
    top_p: float = 0.95
    stream: bool = True 