"""
DocChain客户端模块 - 提供全局DocChainManager实例

该模块负责创建和管理DocChainManager的全局实例，用于整个应用程序。
"""

import logging
from api.config import configs
from api.docchain.manager import DocChainManager

logger = logging.getLogger(__name__)

# 从配置中获取DocChain配置
DOCCHAIN_CONFIG = configs.get("docchain", {})

# 创建全局DocChainManager实例
docchain_manager = DocChainManager(
    base_url=DOCCHAIN_CONFIG.get("base_url", "http://10.10.176.213:7000"),
    api_key=DOCCHAIN_CONFIG.get("api_key", "")
)

def init_docchain():
    """初始化DocChain客户端"""
    pass 