"""
仓库-Topic映射管理器

该模块负责管理代码仓库与DocChain Topic的映射关系，
当前实现基于数据库存储，并在必要时提供内存缓存以兼容旧逻辑。
"""

import logging
import os
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, Optional

from sqlmodel import select

from api.database.base import session_scope
from api.model.git_repository import AiDwGitRepository
from api.model.wiki_info import WikiInfo
from api.model.wiki_repository_relation import AiDwWikiRepositoryRelation
from api.utils import git_utils

# 配置日志
logger = logging.getLogger(__name__)


@dataclass
class RepoContext:
    """仓库上下文信息"""

    key: str
    repo_owner: Optional[str]
    repo_name: Optional[str]
    branch: Optional[str]
    repo_url: Optional[str]
    is_local: bool
    original: str


class RepoTopicMapper:
    """
    仓库-Topic映射管理器

    负责管理代码仓库与DocChain Topic的映射关系，
    优先使用数据库存储，必要时结合内存缓存兼容历史逻辑。
    """

    def __init__(self, *_args, **_kwargs):
        # 仅保留内存映射，数据库作为真实存储
        self.mapping: Dict[str, str] = {}

    def _normalize_repo_url(self, repo_url: str) -> str:
        """
        标准化仓库URL，移除末尾的.git和/

        Args:
            repo_url: 原始仓库URL

        Returns:
            str: 标准化后的URL
        """
        if not repo_url:
            return ""
        url = repo_url.strip()
        if url.endswith(".git"):
            url = url[:-4]
        if url.endswith("/"):
            url = url[:-1]
        return url

    def _build_cache_key(self, value: str, is_local: bool) -> str:
        if not value:
            return ""
        if is_local:
            normalized = os.path.abspath(os.path.expanduser(value))
            return normalized.replace("\\", "/").rstrip("/")
        return self._normalize_repo_url(value)

    def _resolve_context(self, repo_url: str) -> Optional[RepoContext]:
        if not repo_url:
            return None

        value = repo_url.strip()
        is_remote = value.startswith(("http://", "https://", "git@", "ssh://", "git://"))

        if is_remote:
            owner, repo_name, _ = git_utils.extract_repo_info(value)
            normalized_url = self._normalize_repo_url(value)
            cache_key = self._build_cache_key(normalized_url, is_local=False)
            return RepoContext(
                key=cache_key,
                repo_owner=owner or None,
                repo_name=repo_name or None,
                branch=None,
                repo_url=normalized_url,
                is_local=False,
                original=value,
            )

        normalized_path = os.path.abspath(os.path.expanduser(value))
        normalized_path = normalized_path.replace("\\", "/")
        parts = [part for part in normalized_path.split("/") if part]

        repo_owner = repo_name = branch = None
        cache_key = normalized_path.rstrip("/")

        try:
            repos_idx = parts.index("repos")
            if len(parts) > repos_idx + 1:
                repo_owner = parts[repos_idx + 1]
            branch_segment = parts[repos_idx + 2] if len(parts) > repos_idx + 2 else None
            repo_name_candidate = parts[repos_idx + 3] if len(parts) > repos_idx + 3 else None
            if repo_name_candidate:
                repo_name = repo_name_candidate
            elif branch_segment:
                repo_name = branch_segment.split("-")[0]

            if branch_segment and repo_name and branch_segment.startswith(f"{repo_name}-"):
                branch = branch_segment[len(repo_name) + 1 :] or None
            elif branch_segment and repo_name is None:
                # branch_segment 形如 repo-branch
                tokens = branch_segment.split("-")
                if len(tokens) >= 2:
                    repo_name = "-".join(tokens[:-1])
                    branch = tokens[-1]

            # 构造仓库根目录作为缓存键
            if repo_name:
                base_parts = parts[: repos_idx + 4]
                cache_key = "/".join(base_parts)
                if normalized_path.startswith("/"):
                    cache_key = "/" + cache_key
                elif normalized_path[1:2] == ":":
                    # Windows 驱动器号处理 (如 C:/...)
                    cache_key = cache_key
        except ValueError:
            logger.debug("RepoTopicMapper: 无法在路径中定位 repos 目录，使用完整路径作为缓存键: %s", normalized_path)

        return RepoContext(
            key=cache_key,
            repo_owner=repo_owner,
            repo_name=repo_name,
            branch=branch,
            repo_url=None,
            is_local=True,
            original=normalized_path,
        )

    def _parse_mapping_string(self, value: Optional[str]) -> Dict[str, str]:
        if not value:
            return {}
        result: Dict[str, str] = {}
        for segment in value.split(","):
            segment = segment.strip()
            if not segment or ":" not in segment:
                continue
            key, topic_id = segment.split(":", 1)
            key = key.strip()
            topic_id = topic_id.strip()
            if key and topic_id:
                result[key] = topic_id
        return result

    def _build_mapping_string(self, mapping: Dict[str, str]) -> str:
        if not mapping:
            return ""
        ordered_keys = ["deepwiki_code", "deepwiki_doc", "deepwiki_topic"]
        parts = []
        for key in ordered_keys:
            value = mapping.get(key)
            if value:
                parts.append(f"{key}:{value}")
        for key, value in mapping.items():
            if key in ordered_keys or not value:
                continue
            parts.append(f"{key}:{value}")
        return ",".join(parts)

    def _find_repo_record(self, session, context: RepoContext) -> Optional[AiDwGitRepository]:
        if not context:
            return None

        repo: Optional[AiDwGitRepository] = None

        if context.repo_owner and context.repo_name:
            stmt = select(AiDwGitRepository).where(
                AiDwGitRepository.repo_owner == context.repo_owner,
                AiDwGitRepository.repo_name == context.repo_name,
            )
            if context.branch:
                stmt = stmt.where(AiDwGitRepository.branch == context.branch)
            repo = session.exec(stmt).first()
            if repo:
                return repo

        if context.repo_url:
            stmt = select(AiDwGitRepository).where(
                AiDwGitRepository.repo_url == context.repo_url
            )
            repo = session.exec(stmt).first()
            if repo:
                return repo

            normalized_url = self._normalize_repo_url(context.repo_url)
            rows = session.exec(select(AiDwGitRepository)).all()
            for row in rows:
                if row.repo_url and self._normalize_repo_url(row.repo_url) == normalized_url:
                    return row

        return None

    def _find_associated_wiki(self, session, repo: AiDwGitRepository, context: RepoContext) -> Optional[WikiInfo]:
        if not repo and not (context.repo_owner and context.repo_name):
            return None

        if repo:
            rel_stmt = (
                select(AiDwWikiRepositoryRelation)
                .where(AiDwWikiRepositoryRelation.repository_id == repo.id)
                .order_by(AiDwWikiRepositoryRelation.is_main_repo.desc())
            )
            relation = session.exec(rel_stmt).first()
            if relation:
                return session.exec(
                    select(WikiInfo).where(WikiInfo.wiki_id == relation.wiki_id)
                ).first()

        stmt = select(WikiInfo)
        if context.repo_owner:
            stmt = stmt.where(WikiInfo.repo_owner == context.repo_owner)
        if context.repo_name:
            stmt = stmt.where(WikiInfo.repo_name == context.repo_name)
        if context.branch:
            stmt = stmt.where(WikiInfo.branch == context.branch)
        return session.exec(stmt).first()

    def _fetch_mapping_from_db(self, context: RepoContext) -> Dict[str, str]:
        if not context:
            return {}

        mapping: Dict[str, str] = {}

        try:
            with session_scope() as session:
                repo = self._find_repo_record(session, context)
                if repo and repo.code_topic_id:
                    mapping["deepwiki_code"] = repo.code_topic_id

                wiki = self._find_associated_wiki(session, repo, context)
                if wiki and wiki.project_topic_id:
                    mapping["deepwiki_topic"] = wiki.project_topic_id
        except Exception as exc:
            logger.warning("RepoTopicMapper: 从数据库加载Topic映射失败: %s", exc)

        return mapping

    def _persist_mapping_to_db(self, context: RepoContext, mapping: Dict[str, str]) -> None:
        if not context or not mapping:
            return

        try:
            with session_scope() as session:
                repo = self._find_repo_record(session, context)

                if not repo and not context.is_local and context.repo_owner and context.repo_name:
                    repo = AiDwGitRepository(
                        repo_url=context.repo_url or context.original,
                        repo_owner=context.repo_owner,
                        repo_name=context.repo_name,
                        branch=context.branch or "master",
                        repo_type="github",
                        code_topic_id=None,
                    )
                    session.add(repo)
                    session.flush()

                if not repo:
                    # Local 路径但数据库无记录时，仅更新内存缓存
                    logger.debug(
                        "RepoTopicMapper: 未找到仓库记录，跳过数据库写入: %s",
                        context.original,
                    )
                    return

                updated = False
                code_topic = mapping.get("deepwiki_code")
                if code_topic and repo.code_topic_id != code_topic:
                    repo.code_topic_id = code_topic
                    repo.updated_time = datetime.utcnow()
                    session.add(repo)
                    updated = True

                project_topic = mapping.get("deepwiki_topic")
                if project_topic:
                    wiki = self._find_associated_wiki(session, repo, context)
                    if wiki and wiki.project_topic_id != project_topic:
                        wiki.project_topic_id = project_topic
                        wiki.updated_time = datetime.utcnow()
                        session.add(wiki)
                        updated = True

                if updated:
                    logger.debug(
                        "RepoTopicMapper: 已更新数据库Topic映射 repo_id=%s", getattr(repo, "id", None)
                    )
        except Exception as exc:
            logger.error("RepoTopicMapper: 持久化Topic映射失败: %s", exc)

    def get_topic_id(self, repo_url: str) -> Optional[str]:
        """
        获取仓库对应的Topic ID

        Args:
            repo_url: 仓库URL

        Returns:
            Optional[str]: Topic ID，如果不存在则返回None
        """
        context = self._resolve_context(repo_url)
        cache_key = context.key if context else self._normalize_repo_url(repo_url)

        existing_mapping = self._parse_mapping_string(self.mapping.get(cache_key))
        db_mapping = self._fetch_mapping_from_db(context) if context else {}

        # 数据库结果优先确保持久化值，内存映射用于补充自定义键
        merged_mapping = {**db_mapping, **existing_mapping}
        mapping_str = self._build_mapping_string(merged_mapping)

        if mapping_str:
            self.mapping[cache_key] = mapping_str
            return mapping_str

        self.mapping.pop(cache_key, None)
        return None
    
    def get_topic_id_doc(self, repo_url: str) -> Optional[str]:
        """
        获取仓库对应的Topic ID

        Args:
            repo_url: 仓库URL

        Returns:
            Optional[str]: Topic ID，如果不存在则返回None
        """
        return self.get_topic_id(repo_url)
    
    def _normalize_repo_url_doc(self, repo_url: str) -> str:
        """
        标准化仓库URL，使其匹配mapping中的键格式
        """
        # 处理Windows路径
        if '\\' in repo_url:
            # 尝试直接匹配mapping中的键
            for key in self.mapping.keys():
                if repo_url.endswith(key.split('\\')[-1]):
                    return key
            return repo_url  # 没有找到匹配项时返回原始URL
        
        # 处理Linux路径
        else:
            logger.info(f"linux handle, repo_url: {repo_url}")
            # 直接匹配Linux路径
            for key in self.mapping.keys():
                # 跳过Windows风格的路径
                if '\\' in key:
                    if key.endswith(repo_url):
                        return key
                # 检查Linux路径是否以键结尾
                if repo_url.endswith(key) or repo_url.endswith('/' + key):
                    return key
                # 检查键是否包含在repo_url的路径部分中
                key_parts = key.split('/')
                if len(key_parts) > 1:
                    # 提取最后一个非空部分
                    target_part = next((part for part in reversed(key_parts) if part), '')
                    if target_part and repo_url.endswith(target_part):
                        return key
            return repo_url  # 没有找到匹配项时返回原始URL

    def set_topic_id(self, repo_url: str, topic_id: str) -> bool:
        """
        设置仓库对应的Topic ID

        Args:
            repo_url: 仓库URL
            topic_id: Topic ID

        Returns:
            bool: 设置是否成功
        """
        context = self._resolve_context(repo_url)
        cache_key = context.key if context else self._normalize_repo_url(repo_url)
        self.mapping[cache_key] = topic_id
        mapping_dict = self._parse_mapping_string(topic_id)
        self._persist_mapping_to_db(context, mapping_dict)
        return True

    def remove_mapping(self, repo_url: str) -> bool:
        """
        移除仓库的映射关系

        Args:
            repo_url: 仓库URL

        Returns:
            bool: 移除是否成功
        """
        context = self._resolve_context(repo_url)
        cache_key = context.key if context else self._normalize_repo_url(repo_url)
        if cache_key in self.mapping:
            del self.mapping[cache_key]

        if context:
            try:
                with session_scope() as session:
                    repo = self._find_repo_record(session, context)
                    if repo and repo.code_topic_id:
                        repo.code_topic_id = None
                        repo.updated_time = datetime.utcnow()
                        session.add(repo)
            except Exception as exc:
                logger.warning("RepoTopicMapper: 清理数据库Topic映射失败: %s", exc)
        return True

    def get_all_mappings(self) -> Dict[str, str]:
        """
        获取所有映射关系

        Returns:
            Dict[str, str]: 所有仓库URL到Topic ID的映射
        """
        return self.mapping.copy()
