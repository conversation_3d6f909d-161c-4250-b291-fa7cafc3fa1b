"""
DocChain RAG 实现

该模块提供基于DocChain的检索增强生成（RAG）实现，替代现有的FAISS RAG。
专注于纯检索功能，不依赖复杂的Generator组件。
"""

import logging
import os
import sys
import zipfile
import re
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any, Tuple, Union
import adalflow as adal
from pathlib import Path
from api.rag import Memory, system_prompt, RAGAnswer
from api.docchain.manager import DocChainManager
from api.docchain.dto import SearchResult, SearchResponse
from api.config import configs
from api.data_pipeline import DatabaseManager
import api.data_pipeline
import api.docchain.manager
import requests
from api.utils.git_utils import extract_repo_info, get_unified_repo_path

import asyncio
import time

# Configure logging
from api.logging_config import setup_logging

setup_logging()
# 配置日志
logger = logging.getLogger(__name__)


class DocChainRAG(adal.Component):
    """
    基于DocChain的检索增强生成（RAG）实现
    
    使用DocChain服务进行文档检索，专注于纯检索功能，
    不依赖Generator组件，直接处理搜索结果并构造回答。
    """

    def __init__(self, provider="google", model=None):
        """
        初始化DocChain RAG

        Args:
            provider: 模型提供商 (保留兼容性，实际不使用)
            model: 模型名称 (保留兼容性，实际不使用)
        """
        super().__init__()
        
        self.provider = provider
        self.model = model
        self.memory = Memory()
        
        # 初始化DocChain管理器
        docchain_config = configs.get("docchain", {})
        base_url = docchain_config.get("base_url", "http://localhost:7000")
        api_key = docchain_config.get("api_key", os.environ.get("DOCCHAIN_API_KEY", ""))
        
        self.docchain_manager = DocChainManager(base_url, api_key)

        # 初始化生产DocChain管理器
        lab_base_url = docchain_config.get("lab_base_url", "http://localhost:7000")
        lab_api_key = docchain_config.get("lab_api_key", os.environ.get("LAB_DOCCHAIN_API_KEY", ""))
        self.lab_docchain_manager = DocChainManager(lab_base_url, lab_api_key)
        
        # 当前仓库信息
        self.repo_url_or_path = None
        self.repo_path = None
        self.topic_ids = []
        self.repo_type = None

        # 生产docchain对应的topicId
        self.topic_id = None
        
        logger.info("初始化DocChain RAG（简化版，不依赖Generator）")
        
    def initialize_db_manager(self):
        """Initialize the database manager with local storage"""
        self.db_manager = DatabaseManager()
        self.transformed_docs = []

    async def prepare_retriever(self, repo_url_or_path: str, type: str = "github", access_token: str = None, branch: str = 'master',
                        excluded_dirs: List[str] = None, excluded_files: List[str] = None,
                        included_dirs: List[str] = None, included_files: List[str] = None,
                        force_upload: bool = False, existing_topic_id: str = None,existing_topic_id_code: str = None,existing_topic_id_doc: str = None):
        """
        准备检索器

        Args:
            repo_url_or_path: 仓库URL或本地路径
            type: 仓库类型 (github, gitlab, bitbucket)
            access_token: 访问令牌
            excluded_dirs: 要排除的目录列表
            excluded_files: 要排除的文件列表
            included_dirs: 要包含的目录列表
            
            
            
            included_files: 要包含的文件列表
            force_upload: 是否强制重新上传，即使Topic已存在
            existing_topic_id: 产品对应的已存在文档对应的Topic ID
        """
        self.initialize_db_manager()
        # self.repo_url_or_path = repo_url_or_path
        self.repo_url_or_path = self.db_manager.prepare_database(
            repo_url_or_path,
            type,
            access_token,
            branch,
            is_ollama_embedder=False,
            excluded_dirs=excluded_dirs,
            excluded_files=excluded_files,
            included_dirs=included_dirs,
            included_files=included_files
        )
        repo_url_or_path = self.repo_url_or_path
        # api.data_pipeline.separate_repo_files(self.repo_url_or_path)
        # logger.info("separate_repo_files complete")
        
        logger.info(f"准备DocChain检索器，仓库: {self.repo_url_or_path}")
        logger.info(f"Loaded {len(self.transformed_docs)} documents for retrieval")
        
        # self.repo_url_or_path = repo_url_or_path
        self.repo_type = type
        
        # # 如果提供了已有的Topic ID，直接使用
        # if existing_topic_id:
        #     logger.info(f"使用已有的Topic ID: {existing_topic_id}")
        #     self.topic_id = existing_topic_id
            
        #     # 验证Topic是否有效
        #     try:
        #         test_query = "test"
        #         logger.info(f"验证已有Topic {self.topic_id} 是否有效...")
        #         search_response = self.docchain_manager.search(test_query, self.topic_id, size=1)
                
        #         if search_response and (
        #             len(search_response.text) > 0 or
        #             len(search_response.table) > 0 or
        #             len(search_response.image) > 0
        #         ):
        #             logger.info(f"已有Topic {self.topic_id} 验证成功，可以直接使用")
        #             return
        #         else:
        #             logger.warning(f"已有Topic {self.topic_id} 验证失败，可能为空或无效")
        #             # 继续执行下面的逻辑，尝试创建新Topic
        #     except Exception as e:
        #         logger.warning(f"验证已有Topic时出错: {e}，将创建新Topic")
        #         # 继续执行下面的逻辑，尝试创建新Topic
        
        # 处理本地路径和远程URL，统一使用新的路径格式
        if not (repo_url_or_path.startswith("http://") or repo_url_or_path.startswith("https://") or repo_url_or_path.startswith("git@")):
            # 本地路径
            self.repo_path = repo_url_or_path
            # 使用目录名作为仓库名
            repo_name = os.path.basename(os.path.normpath(repo_url_or_path))
            logger.info(f"使用本地仓库路径: {self.repo_path}, 仓库名: {repo_name}")
        else:
            # 远程仓库URL，使用统一的路径格式
            try:
                # 提取仓库信息
                owner, repo_name, _ = extract_repo_info(repo_url_or_path)
                
                # 使用统一的路径格式
                self.repo_path = get_unified_repo_path(owner, repo_name, branch)
                
                logger.info(f"使用远程仓库URL: {repo_url_or_path}")
                logger.info(f"提取的仓库信息 - Owner: {owner}, Repo: {repo_name}, Branch: {branch}")
                logger.info(f"生成的本地路径: {self.repo_path}")
                
            except Exception as e:
                # 如果新格式失败，回退到旧格式
                logger.warning(f"使用新路径格式失败，回退到旧格式: {e}")
                repo_name = repo_url_or_path.split("/")[-1]
                if repo_name.endswith(".git"):
                    repo_name = repo_name[:-4]
                
                # 使用adalflow的默认路径
                from adalflow.utils import get_adalflow_default_root_path
                root_path = get_adalflow_default_root_path()
                self.repo_path = os.path.join(root_path, "repos", repo_name)
                logger.info(f"使用兼容格式的本地路径: {self.repo_path}, 仓库名: {repo_name}")
        # 如果文档库id存在，简单校验一下topic是否有效
        test_query = "test"

        if existing_topic_id:
            lab_search_response = self.lab_docchain_manager.search(test_query, existing_topic_id, size=1)
            if lab_search_response and (
                        (hasattr(lab_search_response, 'text') and lab_search_response.text and len(lab_search_response.text) > 0) or 
                        (hasattr(lab_search_response, 'table') and lab_search_response.table and len(lab_search_response.table) > 0) or 
                        (hasattr(lab_search_response, 'image') and lab_search_response.image and len(lab_search_response.image) > 0)
                    ):
                    logger.info(f"Docchain Topic {existing_topic_id} 存在，且已包含内容")
                    # valid_values.append(existing_topic_id)
                    logger.info(f"添加repo对应的已存在文档对应的Topic ID，Topic ID: {existing_topic_id}")
                    # return
            else:
                logger.warning(f"Docchain Topic {existing_topic_id} 存在但没有内容，将选择有内容的文档库")
            self.topic_id = existing_topic_id
            
        # 检查是否已有映射且不需要强制上传
        existing_topics = None
        if not force_upload:
            existing_topics = self.docchain_manager.mapper.get_topic_id(repo_url_or_path)
            if existing_topics:
                parsed_dict = self.docchain_manager.parse_key_value_string(existing_topics)
                
                keys_to_check = ["deepwiki_code", "deepwiki_topic"]
                valid_values = []

                for key in keys_to_check:
                    value = parsed_dict.get(key)
                    if value:  # 检查值是否非空（非 None、非空字符串等）
                        valid_values.append(value)

                self.topic_ids = ",".join(valid_values) if valid_values else None                
                
                logger.info(f"找到仓库 {repo_url_or_path} 对应的已存在Topic: {existing_topics}")

                
                # 验证Topic是否有效
                try:
                    # 尝试执行一次简单搜索来验证Topic是否有效
                    logger.info(f"验证Topic {self.topic_ids} 是否有效...")
                    search_response = self.docchain_manager.search(test_query, self.topic_ids, size=1)
                    
                    if search_response and (
                        (hasattr(search_response, 'text') and search_response.text and len(search_response.text) > 0) or 
                        (hasattr(search_response, 'table') and search_response.table and len(search_response.table) > 0) or 
                        (hasattr(search_response, 'image') and search_response.image and len(search_response.image) > 0)
                    ):
                        logger.info(f"Topic {self.topic_ids} 已包含内容，无需重新上传")
                        return
                    else:
                        logger.warning(f"Topic {self.topic_ids} 存在但没有内容，将尝试上传代码")
                except Exception as e:
                    logger.warning(f"验证Topic时出错: {e}，将尝试重新上传代码")
        else:
            logger.info("强制重新上传模式，将创建新的Topic")
            
        code_doc_array = ["deepwiki_code"]
        valid_values = []
            
        for file_path in code_doc_array:
            fileexist =self.check_directory(repo_url_or_path, "")
            topic_id = None
            if not fileexist:
                logger.warning(f"未找到{file_path}目录，请检查是否存在")
                continue
            elif existing_topic_id_code:
                if file_path == "deepwiki_code":
                    if existing_topic_id_code:
                        valid_values.append(existing_topic_id_code)
            else:
                current = Path(self.repo_url_or_path)
                # 获取或创建Topic
                logger.info(f"为仓库 {current} 创建或获取Topic...")
                # 将仓库名中的中划线替换为下划线
                repo_name = repo_name.replace("-", "_")
                docchain_repo_name = f"{repo_name}_{file_path}"
                logger.info("self.docchain_manager.get_or_create_topic start...")
                topic_id = self.docchain_manager.get_or_create_topic(repo_url_or_path, docchain_repo_name,file_path)
                if not topic_id:
                    error_msg = f"无法为仓库 {repo_url_or_path} 创建Topic"
                    logger.error(error_msg)
                    raise ValueError(error_msg)
                    
                logger.info(f"成功创建或获取Topic，ID: {topic_id}")
                
                # 检查仓库路径是否存在
                if not os.path.exists(self.repo_path):
                    error_msg = f"仓库路径 {self.repo_path} 不存在，无法上传内容"
                    logger.warning(error_msg)
                    return
                    
                # 检查排除的目录和文件
                if excluded_dirs:
                    logger.info(f"用户指定的排除目录: {excluded_dirs}")
                if excluded_files:
                    logger.info(f"用户指定的排除文件: {excluded_files}")
                if included_dirs:
                    logger.info(f"用户指定的包含目录: {included_dirs}")
                if included_files:
                    logger.info(f"用户指定的包含文件: {included_files}")
                    
                # 上传仓库内容，传递过滤配置
                repo_paths=[]
                repo_paths.append(self.repo_url_or_path)
                logger.info(f"开始上传仓库 {current} 到DocChain Topic {topic_id}")
                result = await self.docchain_manager.upload_repo(
                    repo_paths,
                    topic_id,
                    excluded_dirs=excluded_dirs,
                    excluded_files=excluded_files,
                    included_dirs=included_dirs,
                    included_files=included_files
                )
                
                if not result.success:
                    logger.warning(f"上传仓库内容失败: {result.error}")
                    # 即使上传失败，我们仍然可以尝试使用已有的内容
                else:
                    logger.info(f"仓库内容上传成功: {result.message}")
                    
                    # 等待Topic处理完成
                    logger.info(f"等待Topic {topic_id} 处理完成...")
                    is_ready = await self.docchain_manager.wait_for_topic_ready(topic_id)
                    if is_ready:
                        logger.info(f"Topic {topic_id} 处理完成，可以开始查询")
                    else:
                        logger.warning(f"Topic {topic_id} 处理超时或失败，但仍可尝试查询")
                
                logger.info(f"DocChain检索器准备完成，Topic ID: {topic_id}")
                valid_values.append(topic_id)
            
        if(valid_values and existing_topic_id):
             self.lab_docchain_manager.set_exist_topic_id(self.repo_url_or_path, existing_topic_id,"deepwiki_topic")
        if(valid_values and existing_topic_id_code):
             self.docchain_manager.set_exist_topic_id(self.repo_url_or_path, existing_topic_id_code,"deepwiki_code")
        if(valid_values and existing_topic_id_doc):
             self.docchain_manager.set_exist_topic_id(self.repo_url_or_path, existing_topic_id_doc,"deepwiki_doc")
             
    def create_zip(self, repo_url_or_path: str, repo_name: str) -> None:
        """
        将指定目录打包为ZIP文件
        
        参数:
        repo_url_or_path (str): 源目录路径
        repo_name (str): 目标ZIP文件名（不含扩展名）
        """
        # 规范化路径
        source_dir = Path(repo_url_or_path).resolve()
        
        # 检查源目录是否存在
        if not source_dir.is_dir():
            raise FileNotFoundError(f"源目录不存在: {source_dir}")
        
        # 创建ZIP文件路径（在源目录下）
        zip_path = (source_dir / repo_name).with_suffix('.zip')
        
        try:
            # 创建ZIP文件并写入内容
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 遍历源目录中的所有文件和子目录
                for root, dirs, files in os.walk(source_dir):
                    # 忽略.git文件夹（及其内容）
                    dirs[:] = [d for d in dirs if d != '.git']
                    
                    # 计算相对路径
                    relative_path = os.path.relpath(root, source_dir)
                    
                    # 添加目录（如果不为空）
                    if relative_path != '.':
                        zipf.write(root, relative_path)
                    
                    # 添加文件
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.join(relative_path, file)
                        zipf.write(file_path, arcname)
            
            print(f"成功创建ZIP文件: {zip_path}")
            
        except Exception as e:
            print(f"创建ZIP文件时出错: {e}")
            raise
        
    def check_directory(self, base_path: str, subdir_name: str) -> bool:
        """
        判断基础路径拼接子目录后是否存在，且该子目录中至少包含一个文件（递归检查所有子目录）
        
        Args:
            base_path: 基础路径（如"/zmq/"或"C:\\zmq\\"）
            subdir_name: 要拼接的子目录名（默认值为"deepwiki_code"）
        
        Returns:
            bool: 子目录存在且至少有一个文件则返回True，否则False
        """
        # 拼接路径
        target_dir = Path(base_path) if subdir_name else Path(base_path) / subdir_name
        
        # 1. 判断目标目录是否存在且为目录
        if not target_dir.exists() or not target_dir.is_dir():
            return False
        
        # 2. 递归检查目录中是否至少有一个文件
        for root, _, files in os.walk(target_dir):
            if files:  # 如果找到任何文件，立即返回True
                return True
        
        return False  # 没有找到任何文件

    def _format_search_results_to_standard_format(self, search_responses: SearchResponse) -> List[Dict]:
        """
        将DocChain搜索结果格式化为与原有RAG一致的标准格式

        Args:
            search_responses: DocChain搜索响应.多个docchainrag的搜索结果

        Returns:
            List[Dict]: 标准格式的上下文文档列表，与原有RAG格式保持一致
        """
        contexts = []
        
        # 多个docchainrag的搜索结果
        for search_response in search_responses:
            # 检查search_response是否为None
            if search_response is None:
                logger.warning("搜索响应为None，跳过此响应")
                continue
                
            # 检查search_response是否有text属性且不为None
            if hasattr(search_response, 'text') and search_response.text is not None:
                # 处理文本结果
                for result in search_response.text:
                    data = result.data
                    # 提取文件路径（从heading_chain中获取）
                    heading_chain = data.get("heading_chain", "")
                    file_path = heading_chain.split("#")[0] if "#" in heading_chain else heading_chain
                    
                    # 构建标准格式的上下文
                    context = {
                        "text": data.get("content", ""),
                        "meta_data": {
                            "file_path": file_path,
                            "type": "text",
                            "score": result.score,
                            "heading_chain": heading_chain,
                            "doc_id": data.get("doc_id", ""),
                            "chunk_id": data.get("chunk_id", "")
                        }
                    }
                    contexts.append(context)
            
            # 检查search_response是否有table属性且不为None
            if hasattr(search_response, 'table') and search_response.table is not None:
                # 处理表格结果
                for result in search_response.table:
                    data = result.data
                    heading_chain = data.get("heading_chain", "")
                    file_path = heading_chain.split("#")[0] if "#" in heading_chain else heading_chain
                    
                    context = {
                        "text": data.get("content", ""),
                        "meta_data": {
                            "file_path": file_path,
                            "type": "table",
                            "score": result.score,
                            "heading_chain": heading_chain,
                            "doc_id": data.get("doc_id", ""),
                            "chunk_id": data.get("chunk_id", "")
                        }
                    }
                    contexts.append(context)
            
            # 检查search_response是否有image属性且不为None
            if hasattr(search_response, 'image') and search_response.image is not None:
                # 处理图像结果（如果需要的话）
                for result in search_response.image:
                    data = result.data
                    heading_chain = data.get("heading_chain", "")
                    file_path = heading_chain.split("#")[0] if "#" in heading_chain else heading_chain
                    
                    context = {
                        "text": data.get("content", ""),
                        "meta_data": {
                            "file_path": file_path,
                            "type": "image",
                            "score": result.score,
                            "heading_chain": heading_chain,
                            "doc_id": data.get("doc_id", ""),
                            "chunk_id": data.get("chunk_id", ""),
                            "image_url": data.get("image_url", "")
                        }
                    }
                    contexts.append(context)
            
        # 按相关性分数排序
        contexts.sort(key=lambda x: x["meta_data"].get("score", 0), reverse=True)
        
        logger.info(f"格式化DocChain搜索结果: {len(contexts)} 个文档")
        return contexts

    def _generate_answer_from_contexts(self, query: str, contexts: List[Dict], language: str = "en") -> RAGAnswer:
        """
        基于搜索到的上下文直接生成回答

        Args:
            query: 用户查询
            contexts: 搜索到的上下文文档列表
            language: 语言代码

        Returns:
            RAGAnswer: 构造的回答
        """
        try:
            if not contexts:
                return RAGAnswer(
                    rationale="没有找到相关的上下文信息。",
                    answer="我在代码库中没有找到与您的问题相关的内容。请尝试用不同的方式提问，或者提供更多的上下文信息。"
                )
            
            # 按文件分组整理上下文
            files_content = {}
            # 检查contexts是否为字典
            if isinstance(contexts, dict):
                # 直接处理单个上下文
                ctx = contexts
                file_path = ctx["meta_data"].get("file_path", "unknown")
                if file_path not in files_content:
                    files_content[file_path] = []
                files_content[file_path].append({
                    "text": ctx["text"],
                    "score": ctx["meta_data"].get("score", 0),
                    "type": ctx["meta_data"].get("type", "text"),
                    "heading_chain": ctx["meta_data"].get("heading_chain", "")
                })
            else:
                # 原有的列表处理逻辑
                for ctx in contexts:
                    file_path = ctx["meta_data"].get("file_path", "unknown")
                    if file_path not in files_content:
                        files_content[file_path] = []
                    files_content[file_path].append({
                        "text": ctx["text"],
                        "score": ctx["meta_data"].get("score", 0),
                        "type": ctx["meta_data"].get("type", "text"),
                        "heading_chain": ctx["meta_data"].get("heading_chain", "")
                    })
            # for ctx in contexts:
            #     file_path = ctx["meta_data"].get("file_path", "unknown")
            #     if file_path not in files_content:
            #         files_content[file_path] = []
            #     files_content[file_path].append({
            #         "text": ctx["text"],
            #         "score": ctx["meta_data"].get("score", 0),
            #         "type": ctx["meta_data"].get("type", "text"),
            #         "heading_chain": ctx["meta_data"].get("heading_chain", "")
            #     })
            
            # 构造基于检索结果的回答
            language_map = {
                "en": "English",
                "zh": "中文", 
                "ja": "日本語",
                "ko": "한국어",
                "es": "Español",
                "fr": "Français"
            }
            target_language = language_map.get(language, "English")
            
            # 构造回答内容
            answer_parts = []
            answer_parts.append(f"基于代码库的搜索结果，我为您找到了以下相关信息：\n")
            
            # 按相关性分数排序，取前5个最相关的文件
            sorted_files = sorted(files_content.items(), 
                                key=lambda x: max(item["score"] for item in x[1]), 
                                reverse=True)[:5]
            
            for file_path, file_contents in sorted_files:
                answer_parts.append(f"## 文件：`{file_path}`")
                
                # 按分数排序该文件的内容片段
                sorted_contents = sorted(file_contents, key=lambda x: x["score"], reverse=True)
                
                for i, content in enumerate(sorted_contents[:3]):  # 每个文件最多显示3个片段
                    if content["text"].strip():
                        # 简化内容显示
                        text = content["text"].strip()
                        if len(text) > 300:
                            text = text[:300] + "..."
                        
                        answer_parts.append(f"**相关内容 {i+1}** (相关性: {content['score']:.2f})")
                        if content["heading_chain"]:
                            answer_parts.append(f"*位置: {content['heading_chain']}*")
                        answer_parts.append(f"```\n{text}\n```")
                        answer_parts.append("")
            
            # 添加总结
            answer_parts.append("## 总结")
            answer_parts.append(f"以上是与您的查询「{query}」最相关的代码内容。")
            answer_parts.append(f"共找到 {len(contexts)} 个相关片段，来自 {len(files_content)} 个文件。")
            answer_parts.append("如果您需要更详细的信息或有其他问题，请继续提问。")
            
            full_answer = "\n".join(answer_parts)
            
            return RAGAnswer(
                rationale=f"基于DocChain搜索到 {len(contexts)} 个相关文档片段，来自 {len(files_content)} 个文件。",
                answer=full_answer
            )
            
        except Exception as e:
            logger.error(f"生成回答时出错: {e}")
            return RAGAnswer(
                rationale="处理搜索结果时发生错误。",
                answer=f"很抱歉，在处理搜索结果时遇到了错误: {str(e)}。请尝试重新提问。"
            )

    def _search_with_retry(self, query: str, topic_id: str, size: int = 30, lab_type: bool = False, max_retries: int = 3, retry_delay: float = 1.0):
        """
        带重试机制的搜索方法
        
        Args:
            query: 查询字符串
            topic_id: Topic ID
            size: 返回结果数量
            lab_type: topicId是否为来自生产docchaintopic类型，默认为False
            max_retries: 最大重试次数
            retry_delay: 重试间隔（秒）
            
        Returns:
            SearchResponse: 搜索响应，保证不为None且有内容
        """
        for attempt in range(max_retries + 1):
            try:
                if lab_type:
                    search_response = self.lab_docchain_manager.search(query, topic_id, size=size)
                else: 
                    search_response = self.docchain_manager.search(query, topic_id, size=size)
                
                # 检查返回结果是否为None
                if search_response is None:
                    logger.warning(f"搜索返回None (尝试 {attempt + 1}/{max_retries + 1})")
                    if attempt < max_retries:
                        time.sleep(retry_delay * (attempt + 1))  # 逐渐增加延迟
                        continue
                    else:
                        logger.error(f"搜索重试{max_retries}次后仍返回None")
                        return None
                
                # 检查返回结果是否有有效内容
                has_content = False
                if hasattr(search_response, 'text') and search_response.text and len(search_response.text) > 0:
                    has_content = True
                elif hasattr(search_response, 'table') and search_response.table and len(search_response.table) > 0:
                    has_content = True
                elif hasattr(search_response, 'image') and search_response.image and len(search_response.image) > 0:
                    has_content = True
                
                if not has_content:
                    logger.warning(f"搜索返回空内容 (尝试 {attempt + 1}/{max_retries + 1})")
                    if attempt < max_retries:
                        time.sleep(retry_delay * (attempt + 1))
                        continue
                    else:
                        logger.warning(f"搜索重试{max_retries}次后仍无内容，返回空响应")
                        return search_response  # 返回空响应而不是None
                
                # 搜索成功，有内容
                logger.info(f"搜索成功 (尝试 {attempt + 1}/{max_retries + 1})，返回 {len(search_response.text) if hasattr(search_response, 'text') and search_response.text else 0} 个文本结果")
                return search_response
                
            except Exception as e:
                logger.error(f"搜索异常 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                if attempt < max_retries:
                    time.sleep(retry_delay * (attempt + 1))
                    continue
                else:
                    logger.error(f"搜索重试{max_retries}次后仍异常，抛出异常")
                    raise
        
        return None

    def call(self, query: str, language: str = "en", coop_topic_id: Optional[str] = None, dept_topic_id: Optional[str] = None) -> Tuple[Any, List]:
        """
        使用DocChain进行问答和上下文检索，带重试机制
        """
        contexts = []
        query_encoded = query
        search_responses = []

        # 检索开始时间
        search_start_time = time.time()

        try:

            # 构建协作生产docchaintopic的搜索列表
            topic_id_docchain_to_search = []

            # 如果指定了协作topic ID，则添加到搜索列表
            if coop_topic_id:
                topic_id_docchain_to_search.append(coop_topic_id)
            # 如果DocChainRAG实例有自己的docchain topic IDs，也添加到搜索列表
            if hasattr(self, 'topic_id') and self.topic_id:
                topic_id_docchain_to_search.append(self.topic_id)    
            # 去重docchain topic IDs
            topic_id_docchain_to_search = list(set(topic_id_docchain_to_search))
            logger.info(f"开始生产docchain的搜索，查询: '{query_encoded}', docchain Topic IDs: {topic_id_docchain_to_search}")

            # 调用生产docchain的接口获取搜索结果
            for topic_id in topic_id_docchain_to_search:
                try:
                    lab_search_response = self._search_with_retry(query, topic_id, size=30, lab_type=True)
                    if lab_search_response is not None:
                        search_responses.append(lab_search_response)
                        logger.info(f"docchain Topic {topic_id} 搜索完成")
                    else:
                        logger.warning(f"docchain Topic {topic_id} 搜索失败，跳过")
                except Exception as e:
                    logger.error(f"docchain Topic {topic_id} 搜索异常: {e}")
                    continue

            # 构建搜索查询，包含所有可能的topic ID源
            topic_ids_to_search = []
            
            # 如果指定了部门topic ID，添加到搜索列表  
            if dept_topic_id:
                topic_ids_to_search.append(dept_topic_id)
            
            # 如果DocChainRAG实例有自己的topic IDs，也添加到搜索列表
            if hasattr(self, 'topic_ids') and self.topic_ids:
                current_topic_ids = self.topic_ids.split(',') if isinstance(self.topic_ids, str) else [str(self.topic_ids)]
                topic_ids_to_search.extend(current_topic_ids)
            
            # 去重topic IDs
            topic_ids_to_search = list(set(topic_ids_to_search))
            
            logger.info(f"开始搜索，查询: '{query_encoded}', Topic IDs: {topic_ids_to_search}")

            # 为每个topic ID执行搜索（使用重试机制）
            for topic_id in topic_ids_to_search:
                try:
                    search_response = self._search_with_retry(query_encoded, topic_id, size=30)
                    if search_response is not None:
                        search_responses.append(search_response)
                        logger.info(f"Topic {topic_id} 搜索完成")
                    else:
                        logger.warning(f"Topic {topic_id} 搜索失败，跳过")
                except Exception as e:
                    logger.error(f"Topic {topic_id} 搜索异常: {e}")
                    continue

            # 如果没有搜索响应，返回空结果
            if not search_responses:
                logger.warning("所有Topic搜索均失败，返回空结果")
                return "没有找到相关信息。", []

            # 处理搜索结果
            contexts = self._format_search_results_to_standard_format(search_responses)
            search_end_time = time.time()
            search_duration = search_end_time - search_start_time
            logger.info(f"DocChain搜索完成，用时: {search_duration:.2f}秒，检索到 {len(contexts)} 个上下文")

            # 生成答案
            answer = self._generate_answer_from_contexts(query, contexts, language)
            
            return answer, contexts

        except Exception as e:
            logger.error(f"DocChain调用过程中发生错误: {e}", exc_info=True)
            return f"搜索过程中发生错误: {str(e)}", [] 