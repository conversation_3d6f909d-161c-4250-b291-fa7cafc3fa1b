"""
DocChain 直答服务

该模块提供简化的DocChain直答功能，不依赖AdalFlow框架，
专门用于直接与DocChain服务进行对话，绕过RAG检索流程。
"""

import logging
import os
from typing import Dict, Any, Optional, AsyncGenerator
from dataclasses import dataclass

from api.docchain.client import DocChainModelClient
from api.docchain.manager import DocChainManager

# 配置日志
logger = logging.getLogger(__name__)


@dataclass
class DirectChatResponse:
    """直答响应数据结构"""
    success: bool
    content: str
    error: Optional[str] = None
    topic_id: Optional[str] = None


class DocChainDirectChat:
    """
    DocChain 直答服务
    
    提供简化的DocChain直答功能，不使用RAG检索，
    直接与DocChain服务进行对话。
    """

    def __init__(self, base_url: str = None, api_key: str = None):
        """
        初始化DocChain直答服务

        Args:
            base_url: DocChain服务基础URL
            api_key: DocChain服务API密钥
        """
        self.base_url = base_url or os.environ.get("DOCCHAIN_BASE_URL", "http://localhost:7000")
        self.api_key = api_key or os.environ.get("DOCCHAIN_API_KEY", "")
        
        # 初始化DocChain管理器（用于Topic管理）
        self.docchain_manager = DocChainManager(self.base_url, self.api_key)
        
        # 初始化模型客户端
        self.model_client = DocChainModelClient(self.base_url, self.api_key)
        
        # 当前Topic信息
        self.topic_id = None
        
        logger.info(f"初始化DocChain直答服务: {self.base_url}")

    def set_topic_id(self, topic_id: str):
        """
        设置Topic ID

        Args:
            topic_id: Topic ID
        """
        self.topic_id = topic_id
        self.model_client.set_topic_id(topic_id)
        logger.info(f"设置Topic ID: {topic_id}")

    # def prepare_topic_from_repo(self, repo_url_or_path: str, repo_name: str = None) -> bool:
    #     """
    #     从仓库准备Topic

    #     Args:
    #         repo_url_or_path: 仓库URL或本地路径
    #         repo_name: 仓库名称（可选）

    #     Returns:
    #         bool: 是否成功准备Topic
    #     """
    #     try:
    #         # 如果没有提供仓库名，从路径中提取
    #         if not repo_name:
    #             if repo_url_or_path.startswith(("http://", "https://")):
    #                 repo_name = repo_url_or_path.split("/")[-1]
    #                 if repo_name.endswith(".git"):
    #                     repo_name = repo_name[:-4]
    #             else:
    #                 repo_name = os.path.basename(os.path.normpath(repo_url_or_path))
            
    #         # 获取或创建Topic
    #         topic_id = self.docchain_manager.get_or_create_topic(repo_url_or_path, repo_name)
    #         if topic_id:
    #             self.set_topic_id(topic_id)
    #             logger.info(f"成功准备Topic: {topic_id}")
    #             return True
    #         else:
    #             logger.error(f"无法为仓库 {repo_url_or_path} 创建Topic")
    #             return False
                
    #     except Exception as e:
    #         logger.error(f"准备Topic失败: {e}")
    #         return False

    def chat(self, message: str, temperature: float = 0.7) -> DirectChatResponse:
        """
        同步直答对话

        Args:
            message: 用户消息
            temperature: 温度参数

        Returns:
            DirectChatResponse: 直答响应
        """
        try:
            if not self.topic_id:
                return DirectChatResponse(
                    success=False,
                    content="",
                    error="Topic ID未设置，请先调用set_topic_id或prepare_topic_from_repo"
                )
            
            # 构建API参数
            api_kwargs = {
                "model": self.topic_id,
                "messages": [{"role": "user", "content": message}],
                "temperature": temperature,
                "stream": False
            }
            
            # 调用DocChain
            logger.info(f"DocChain直答: {message[:50]}...")
            result = self.model_client.call(api_kwargs=api_kwargs)
            
            if result.error:
                logger.error(f"DocChain调用失败: {result.error}")
                return DirectChatResponse(
                    success=False,
                    content="",
                    error=result.error,
                    topic_id=self.topic_id
                )
            
            # 返回成功响应
            return DirectChatResponse(
                success=True,
                content=result.data or "",
                topic_id=self.topic_id
            )
            
        except Exception as e:
            logger.error(f"DocChain直答失败: {e}")
            return DirectChatResponse(
                success=False,
                content="",
                error=str(e),
                topic_id=self.topic_id
            )

    async def chat_stream(self, message: str, temperature: float = 0.7) -> AsyncGenerator[str, None]:
        """
        异步流式直答对话

        Args:
            message: 用户消息
            temperature: 温度参数

        Yields:
            str: 流式响应内容
        """
        try:
            if not self.topic_id:
                yield "错误：Topic ID未设置，请先调用set_topic_id或prepare_topic_from_repo"
                return
            
            # 构建API参数
            api_kwargs = {
                "model": self.topic_id,
                "messages": [{"role": "user", "content": message}],
                "temperature": temperature,
                "stream": True
            }
            
            # 调用DocChain流式接口
            logger.info(f"DocChain流式直答: {message[:50]}...")
            async for chunk in self.model_client.acall(api_kwargs=api_kwargs):
                if chunk:
                    yield chunk
                    
        except Exception as e:
            logger.error(f"DocChain流式直答失败: {e}")
            yield f"错误：{str(e)}"

    def get_topic_info(self) -> Dict[str, Any]:
        """
        获取当前Topic信息

        Returns:
            Dict[str, Any]: Topic信息
        """
        return {
            "topic_id": self.topic_id,
            "base_url": self.base_url,
            "has_api_key": bool(self.api_key)
        } 