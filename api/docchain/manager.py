"""
DocChain 管理器

该模块负责与DocChain服务交互，包括Topic管理、文档上传和检索等功能。
"""

import json
import logging
import os
import re
import time
from collections import defaultdict
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union, Set
import requests
import tempfile
import aiohttp
import asyncio
import httpx
import difflib

from api.docchain.dto import (
    TopicConfig, TopicCreateRequest, TopicInfo, DocumentUploadRequest,
    DocumentUploadResponse, SearchRequest, SearchResponse, SearchResult,
    ChatCompletionRequest, ChatMessage
)
from api.docchain.mapper import RepoTopicMapper
from api.docchain.converter import CodeConverter
# Configure logging
from api.logging_config import setup_logging
setup_logging()
# 配置日志
logger = logging.getLogger(__name__)

class DocChainManager:
    """
    DocChain 管理器

    负责与DocChain服务交互，包括Topic管理、文档上传和检索等功能。
    """

    def __init__(self, base_url: str, api_key: str):
        """
        初始化DocChain管理器

        Args:
            base_url: DocChain服务基础URL
            api_key: DocChain服务API密钥
        """
        self.base_url = base_url.rstrip("/")
        self.api_key = api_key
        self.mapper = RepoTopicMapper()
        self.converter = CodeConverter()
        self.topic_ids = None
        self._rag_instance = None  # 延迟初始化
        self.logger = logger
        
    def get_rag_instance(self):
        """
        获取RAG实例，如果不存在则创建
        
        Returns:
            DocChainRAG: RAG实例
        """
        if self._rag_instance is None:
            # 延迟导入，避免循环导入问题
            from api.docchain.rag import DocChainRAG
            self._rag_instance = DocChainRAG()
            # 如果已经有topic_ids，设置给RAG实例
            if self.topic_ids:
                self._rag_instance.topic_ids = self.topic_ids
        
        return self._rag_instance

    # def test_failed_request(self):
    #     # 完全复刻失败请求的参数和配置
    #     url = "http://*************:7000/v1/search"
    #     headers = {
    #         "X-Api-Key": "mySv1-IWTrmph_a9imXaQWsdtksZ9g4Rwx5PaWmh5NY",
    #         "Content-Type": "application/json",
    #         "accept": "application/json"
    #     }
    #     # 失败请求中的所有参数（与日志完全一致）
    #     request_data = {
    #         "query": "payc是什么",  # 失败请求的查询词
    #         "topic_id_list": [67, 68],  # 失败请求的topic_id
    #         "size": 5,  # 失败请求的size
    #         "with_context": False,  # 失败请求的with_context
    #         "score": 0.1,  # 失败请求的score
    #         "ranking_mode": "rerank",  # 失败请求的排序模式
    #         "rrf_k": 10  # 失败请求的rrf_k值
    #     }
    #     # 序列化数据（与失败请求一致）
    #     data = json.dumps(request_data, separators=(',', ':'), ensure_ascii=False)
        
    #     try:
    #         # 发送请求（与失败请求的方法、URL、头一致）
    #         response = requests.post(url, headers=headers, data=data)
    #         response.raise_for_status()  # 触发HTTP错误
    #         print("测试成功，响应内容：", response.json())
    #     except Exception as e:
    #         print("测试失败，错误信息：", e)
    #         print("服务器返回内容：", response.text)
        
    # def search11(self):
    #     url = "http://*************:7000/v1/search"
    #     headers = {
    #         "X-Api-Key": "mySv1-IWTrmph_a9imXaQWsdtksZ9g4Rwx5PaWmh5NY",
    #         "Content-Type": "application/json",
    #         "accept": "application/json"
    #     }
    #     request_data = {
    #         "query": "payc是什么",
    #         "size": 5,
    #         "score": 0.1,
    #         "rrf_k": 10,
    #         "ranking_mode": "rerank",
    #         "topic_id_list": [67, 68]  # 整数列表
    #     }
    #     data = json.dumps(request_data)  # 关键：序列化为 JSON 字符串

    #     try:
    #         response = requests.post(url, headers=headers, data=data)
    #         response.raise_for_status()  # 抛出 HTTP 错误（如 4xx/5xx）
    #         return response.json()
    #     except Exception as e:
    #         print(f"请求失败：{e}")
    #         print(f"响应内容：{response.text}")
    #         return None


    def _make_request(self, method: str, endpoint: str, data: Any = None,
                     params: Dict = None, files: List = None) -> Dict:
        
        """
        发送HTTP请求到DocChain服务

        Args:
            method: HTTP方法 (GET, POST, PUT, DELETE)
            endpoint: API端点
            data: 请求数据
            params: URL参数
            files: 要上传的文件列表

        Returns:
            Dict: 响应数据

        Raises:
            Exception: 请求失败时抛出异常
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        headers = {"X-Api-Key": self.api_key} if self.api_key else {}

        try:
            # 新增：若 data 是字符串且看起来像 JSON，强制添加 Content-Type
            if isinstance(data, str):
                # 简单判断是否为 JSON 字符串（可根据实际场景优化）
                if data.startswith('{') and data.endswith('}') or data.startswith('[') and data.endswith(']'):
                    headers["Content-Type"] = "application/json"
            # 原有逻辑：处理 dict/list 类型的 data
            elif isinstance(data, dict) or isinstance(data, list):
                headers["Content-Type"] = "application/json"
                data = json.dumps(data, default=self._json_serializer)

            response = requests.request(
                method=method,
                url=url,
                headers=headers,
                data=data,
                params=params,
                files=files
            )

            # 记录请求和响应信息，便于调试
            logger.debug(f"请求URL: {url}")
            logger.debug(f"请求方法: {method}")
            logger.debug(f"请求头: {headers}")
            if data:
                logger.debug(f"请求数据: {data}")
            logger.debug(f"响应状态码: {response.status_code}")
            
            # 检查响应状态
            response.raise_for_status()

            # 解析JSON响应
            if response.text:
                try:
                    return response.json()
                except json.JSONDecodeError:
                    return {"text": response.text}
            return {}

        except requests.RequestException as e:
            logger.error(f"请求失败: {e}")
            if hasattr(e, "response") and e.response is not None:
                logger.error(f"响应状态码: {e.response.status_code}")
                logger.error(f"响应内容: {e.response.text}")
            raise Exception(f"DocChain请求失败: {str(e)}")
            
    def _json_serializer(self, obj):
        """自定义JSON序列化器，处理特殊类型"""
        if isinstance(obj, (int, float, str, bool)) or obj is None:
            return obj
        # 处理其他类型
        try:
            return str(obj)
        except:
            return None
    def _getFileContent(self, method: str, endpoint: str, data: Any = None,
                 params: Dict = None, files: List = None, stream: bool = False) -> Dict:
        """
        发送HTTP请求到DocChain服务（增强版，支持流式响应）

        Args:
            method: HTTP方法 (GET, POST, PUT, DELETE)
            endpoint: API端点
            data: 请求数据
            params: URL参数
            files: 要上传的文件列表
            stream: 是否使用流式响应

        Returns:
            Dict: 响应数据 (JSON格式) 或原始文本内容
            如果stream=True，返回原始的requests.Response对象

        Raises:
            Exception: 请求失败时抛出异常
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        headers = {"X-Api-Key": self.api_key} if self.api_key else {}

        try:
            if isinstance(data, dict) or isinstance(data, list):
                headers["Content-Type"] = "application/json"
                data = json.dumps(data, default=self._json_serializer)

            # 记录详细的请求信息
            logger.debug(f"请求URL: {url}")
            logger.debug(f"请求方法: {method}")
            logger.debug(f"请求头: {headers}")
            logger.debug(f"请求参数: {params}")
            logger.debug(f"请求数据: {data}")
            logger.debug(f"流式响应: {stream}")

            # 构建请求参数
            request_kwargs = {
                "method": method,
                "url": url,
                "headers": headers,
                "data": data,
                "params": params,
                "files": files,
                "stream": stream  # 关键参数，启用流式响应
            }

            # 发送请求
            response = requests.request(**request_kwargs)
            
            # 记录响应状态
            logger.debug(f"响应状态码: {response.status_code}")
            logger.debug(f"响应头: {dict(response.headers)}")

            # 检查响应状态
            response.raise_for_status()

            # 处理流式响应 - 直接返回响应对象
            if stream:
                return response

            # 处理普通响应
            if response.text:
                # 检查内容类型
                content_type = response.headers.get('Content-Type', '')
                
                # 处理JSON响应
                if 'application/json' in content_type.lower():
                    try:
                        return response.json()
                    except json.JSONDecodeError:
                        print(f"响应不是有效的JSON: {response.text[:100]}...")
                        return response.text
                
                # 处理文本响应
                elif 'text/' in content_type.lower() or 'application/octet-stream' in content_type.lower():
                    return response.text
                
                # 其他类型 - 返回原始文本
                else:
                    print(f"未知内容类型: {content_type}，返回原始文本")
                    return response.text
            
            return ''

        except requests.RequestException as e:
            logger.error(f"请求失败: {e}", exc_info=True)
            if hasattr(e, "response") and e.response is not None:
                logger.error(f"响应状态码: {e.response.status_code}")
                logger.error(f"响应内容: {e.response.text[:200]}...")
            raise Exception(f"DocChain请求失败: {str(e)}")

    def list_topics(self) -> List[TopicInfo]:
        """
        获取所有Topic列表

        Returns:
            List[TopicInfo]: Topic信息列表
        """
        try:
            response = self._make_request("GET", "/llmdoc/v1/topic/list")
            topics = []
            for topic_data in response:
                topic = TopicInfo(
                    id=topic_data["id"],
                    name=topic_data["name"],
                    state=topic_data["state"],
                    create_date=topic_data["create_date"],
                    state_date=topic_data["state_date"],
                    extra=topic_data["extra"],
                    prompt=topic_data.get("prompt"),
                    introduce=topic_data.get("introduce")
                )
                topics.append(topic)
            return topics
        except Exception as e:
            logger.error(f"获取Topic列表失败: {e}")
            return []

    def create_or_update_topic(self, config: TopicConfig) -> Optional[int]:
        """
        创建或更新Topic

        Args:
            config: Topic配置信息

        Returns:
            Optional[int]: 创建或更新的Topic ID，如果失败则返回None
        """
        try:
            # 查询是否已存在同名Topic
            topics = self.list_topics()
            topic_id = None
            for topic in topics:
                if topic.name == config.name:
                    topic_id = topic.id
                    print(f"找到同名Topic: {topic.name}, ID: {topic.id}")
                    return topic_id

            # 构建请求数据
            if topic_id:
                # 更新Topic
                request_data = {
                    "name": config.name,
                    "operation": "modify",
                    "extra": config.extra,
                    "id": topic_id,  # 更新时id必传
                    "prompt": "",
                    "introduce": ""
                }
            else:
                # 创建Topic
                request_data = {
                    "name": config.name,
                    "operation": "create",  # 创建时使用create
                    "extra": config.extra,
                    "prompt": "",
                    "introduce": ""
                }

            # 发送请求
            print(f"发送创建/更新Topic请求: {json.dumps(request_data, ensure_ascii=False)}")
            response = self._make_request("POST", "/llmdoc/v1/topic/update", data=request_data)
            
            # 返回Topic ID - 改进响应解析逻辑
            topic_result_id = None
            
            # 解析响应格式
            if isinstance(response, dict):
                # 修改, 接口返回没有data字段,无需校验格式
                if 'modify' == request_data.get('operation') and response.get("success"):
                    topic_result_id = topic_id
                else:
                    # 检查响应是否成功
                    if response.get("success", False) and "data" in response:
                        data = response["data"]
                        if isinstance(data, dict) and "id" in data:
                            topic_result_id = data["id"]
                        else:
                            print(f"响应中未找到有效的ID字段: {response}")
                    else:
                        print(f"请求失败或响应格式不符合预期: {response}")

            if topic_result_id is not None:
                print(f"Topic创建/更新成功，ID: {topic_result_id}")
                return topic_result_id
            else:
                print(f"无法从响应中解析Topic ID: {response}")
                raise Exception(f"无法从响应中解析Topic ID: {response}")

        except Exception as e:
            logger.error(f"创建或更新Topic失败: {e}")
            raise Exception(f"创建或更新Topic失败: {e}")

    def get_or_create_topic(self, repo_url: str, repo_name: str = None, file_path: str = None) -> Optional[str]:
        """
        获取或创建仓库对应的Topic

        Args:
            repo_url: 仓库URL
            repo_name: 仓库名称，如果为None则从URL中提取

        Returns:
            Optional[str]: Topic ID，如果失败则返回None
        """

        # 提取仓库名称
        if repo_name is None:
            repo_name = repo_url.split("/")[-1]
            if repo_name.endswith(".git"):
                repo_name = repo_name[:-4]

        # 创建新Topic
        # docchain_repo_name = repo_name+"_"+file_path
        print(f"为仓库 {repo_url} 创建新Topic: {repo_name}")
        # config = TopicConfig(
        #     name=repo_name,
        #     env_lang="zh",
        #     llm_model_name="doubao-lite-32k",
        #     chunk_size=512,
        #     embedding_model_name="Qodo-embedding"
        # )
        # 创建TopicConfig时覆盖默认配置
        config = TopicConfig(
            name=repo_name,
            env_lang="zh",
            llm_model_name="doubao-lite-32k",
            chunk_size=512,
            embedding_model_name="Qodo-embedding",
            extra={
                "common_config": {
                    "summary": "false",             # 关闭整体摘要
                    "summary_chapter": "false",     # 关闭章节摘要
                    "summary_image": "false",       # 关闭图片描述
                }
            }
        )

        topic_id = self.create_or_update_topic(config)
        # # 示例用法
        # input_str = "deepwiki_code:111,deepwiki_doc:222,deepwiki_topic:333"
        # parsed_dict = parse_key_value_string(input_str)

        # print(parsed_dict)  # 输出: {'deepwiki_code': '111', 'deepwiki_doc': '222', 'deepwiki_topic': '333'}
        # print(parsed_dict["deepwiki_code"])  # 输出: 111
        #         self.parse_key_value_string(topic_id)
        if topic_id:
            return str(topic_id)
        
        logger.error(f"创建Topic失败")
        return None
    
    def set_exist_topic_id(self, repo_url: str, exist_topic_id: str = None, file_path: str = None) -> Optional[str]:
        docchainArr = self.mapper.get_topic_id(repo_url)
        self.mapper.set_topic_id(repo_url, str(self.update_key_value(docchainArr, file_path, str(exist_topic_id))))
    
    def update_key_value(self, value_str: str, key: str, new_value: str) -> str:
        """
        更新或添加键值对到字符串中
        
        Args:
            value_str: 原始字符串，格式为"key1:value1,key2:value2,..."
            key: 要更新或添加的键
            new_value: 新的值
        
        Returns:
            str: 更新后的字符串，保持原有顺序（如果存在），否则添加到末尾
        """
        # 处理value_str为None或空字符串的情况
        if not value_str:
            return f"{key}:{new_value}"
        
        # 解析现有键值对
        pairs = []
        found = False
        
        for item in value_str.split(','):
            item = item.strip()
            if not item:
                continue
                
            parts = item.split(':', 1)
            if len(parts) != 2:
                pairs.append(item)  # 无效格式保持原样
                continue
                
            k, v = parts
            if k == key:
                pairs.append(f"{k}:{new_value}")
                found = True
            else:
                pairs.append(item)
        
        # 如果键不存在，添加到末尾
        if not found:
            pairs.append(f"{key}:{new_value}")
        
        return ','.join(pairs)

    
    def parse_key_value_string(self,s: str) -> dict:
        """
        解析格式为 "deepwiki_code:111,deepwiki_doc:222,deepwiki_topic:333" 的字符串为字典
        
        Args:
            s: 待解析的字符串
        
        Returns:
            dict: 解析后的键值对字典
        """
        result = {}
        if not s:
            return result
        
        # 1. 按逗号分隔字符串，得到多个键值对
        pairs = s.split(',')
        
        for pair in pairs:
            # 2. 按冒号分隔每个键值对
            if ':' not in pair:
                continue  # 跳过无效格式
            
            key, value = pair.split(':', 1)  # 只分割第一个冒号，避免值中包含冒号
            result[key.strip()] = value.strip()  # 去除可能的空格
        
        return result

    async def upload_files(self, files: List[tuple], topic_id: str) -> DocumentUploadResponse:
        """
        异步上传文件到DocChain，失败时自动重试两次
        
        Args:
            files: 文件路径列表
            topic_id: Topic ID
            
        Returns:
            DocumentUploadResponse: 包含上传结果的响应对象
        """
        max_attempts = 3  # 初始请求 + 2次重试
        
        for attempt in range(max_attempts):
            file_objects = []
            try:
                # 构建API URL
                url = f"{self.base_url}/v1/doc/batchUpload?topic_id={topic_id}"
                
                # 添加API密钥到请求头
                headers = {"X-Api-Key": self.api_key} if self.api_key else {}
                
                # 准备文件对象（异步打开文件）
                for relative_key, file_path in files:
                    filename = self.converter.build_docchain_filename(relative_key)
                    # 在线程池中打开文件（避免阻塞事件循环）
                    file_obj = await asyncio.to_thread(open, file_path, 'rb')
                    file_objects.append((filename, file_obj))
                
                # 构建httpx的文件数据格式
                files_data = [
                    ('files', (filename, file_obj, 'application/octet-stream'))
                    for filename, file_obj in file_objects
                ]
                
                # 异步发送请求
                async with httpx.AsyncClient(timeout=3600.0) as client:
                    response = await client.post(url, headers=headers, files=files_data)
                
                # 确保所有文件被关闭
                for _, file_obj in file_objects:
                    file_obj.close()
                
                # 检查HTTP状态码
                response.raise_for_status()
                
                # 解析响应数据
                response_data = response.json()
                
                # 检查业务逻辑是否成功
                if response_data.get('success', True):
                    return DocumentUploadResponse(
                        success=True,
                        message=f"文件上传成功 (尝试 {attempt + 1}/{max_attempts})",
                        data=response_data
                    )
                else:
                    # 处理业务逻辑失败的情况
                    error_msg = response_data.get('msg', '未知错误')
                    failed_files = response_data.get('data', [])
                    
                    logger.error(f"批量上传失败: {error_msg}")
                    if failed_files:
                        logger.error(f"失败的文件列表: {failed_files}")
                        print(f"❌ 上传失败的文件 ({len(failed_files)} 个):")
                        for i, file in enumerate(failed_files, 1):
                            print(f"  {i}. {file}")
                    
                    # 如果所有文件都失败了，返回失败响应
                    if len(failed_files) == len(files):
                        return DocumentUploadResponse(
                            success=False,
                            message=f"所有文件上传失败: {error_msg}",
                            error=error_msg,
                            data=response_data
                        )
                    else:
                        # 部分文件失败，仍然返回成功，但在消息中说明
                        success_count = len(files) - len(failed_files)
                        return DocumentUploadResponse(
                            success=True,
                            message=f"部分文件上传成功: {success_count}/{len(files)} 个文件成功, {len(failed_files)} 个失败",
                            data=response_data,
                            failed_files=failed_files
                        )
                
            except Exception as e:
                # 关闭所有文件（包括异常情况）
                for _, file_obj in file_objects:
                    try:
                        file_obj.close()
                    except Exception:
                        pass
                
                # 计算剩余重试次数
                remaining = max_attempts - attempt - 1
                
                if remaining > 0:
                    # 指数退避策略（每次重试间隔递增）
                    backoff_time = (attempt + 1) * 2  # 2秒, 4秒
                    self.logger.warning(
                        f"上传失败 (尝试 {attempt + 1}/{max_attempts}): {str(e)} - "
                        f"将在 {backoff_time} 秒后重试"
                    )
                    await asyncio.sleep(backoff_time)  # 异步睡眠
                else:
                    self.logger.error(f"上传文件失败（已耗尽所有重试次数）: {str(e)}")
                    return DocumentUploadResponse(
                        success=False,
                        message="文件上传失败（已重试2次）",
                        error=str(e)
                    )

    async def upload_repo(self, repo_paths: List[str], topic_id: str,
                excluded_dirs: List[str] = None,
                excluded_files: List[str] = None,
                included_dirs: List[str] = None,
                included_files: List[str] = None,
                progress_callback=None,
                resume_progress: dict = None,
                path_namespaces: Optional[Dict[str, str]] = None,
                normalize_existing: bool = True) -> DocumentUploadResponse:
        """
        上传整个代码仓库到DocChain，支持上传失败时重试2次，支持续传功能

        Args:
            repo_path: 仓库路径
            topic_id: Topic ID
            excluded_dirs: 要排除的目录列表
            excluded_files: 要排除的文件列表
            included_dirs: 要包含的目录列表
            included_files: 要包含的文件列表（可以是相对路径、文件名或DocChain文件名）
            progress_callback: 进度回调函数
            resume_progress: 续传进度信息，格式: {uploaded_count: int, total_files: int}
            path_namespaces: 自定义仓库命名空间映射
            normalize_existing: 是否输出现有文档的重命名建议

        Returns:
            DocumentUploadResponse: 上传响应
        """
        try:
            # 转换代码为Markdown（总是重新生成，确保一致性）
            logger.info("转换代码为Markdown")
            md_files = self.converter.convert_repo_to_md(
                repo_paths, 
                excluded_dirs=excluded_dirs, 
                excluded_files=excluded_files,
                included_dirs=included_dirs,
                included_files=included_files,
                path_namespaces=path_namespaces,
            )

            if not md_files:
                return DocumentUploadResponse(
                    success=False,
                    message="没有找到可转换的文件",
                    error="No files to convert"
                )

            normalization_plan = None
            if normalize_existing:
                normalization_plan = await self._normalize_existing_documents(
                    topic_id,
                    list(md_files.keys()),
                )
                self._log_normalization_plan(topic_id, normalization_plan)

            # 分批上传文件（每批最多10个文件）
            batch_size = 10
            entries = list(md_files.items())  # (relative_key, file_path)

            if included_files:
                normalized_includes = set()
                for item in included_files:
                    if not item:
                        continue
                    normalized = self.converter._normalize_relative_key(str(item).replace('\\', '/'))
                    if not normalized:
                        continue
                    normalized_includes.add(normalized)
                    # include both md / non-md variants for matching
                    if normalized.endswith('.md'):
                        normalized_includes.add(normalized[:-3])
                    else:
                        normalized_includes.add(f"{normalized}.md")

                docchain_includes = {
                    self.converter.build_docchain_filename(include)
                    for include in normalized_includes
                }

                def build_candidate_keys(relative_key: str) -> set:
                    normalized_key = self.converter._normalize_relative_key(relative_key)
                    candidates = {normalized_key}
                    if normalized_key.endswith('.md'):
                        candidates.add(normalized_key[:-3])

                    # capture suffixes without repository namespace so that
                    # inputs like "src/foo.py" or "foo.py" still match
                    suffix_sources = list(candidates)
                    for value in suffix_sources:
                        parts = [segment for segment in value.split('/') if segment]
                        for idx in range(len(parts)):
                            suffix = '/'.join(parts[idx:])
                            if suffix:
                                candidates.add(suffix)

                    for value in list(candidates):
                        candidates.add(os.path.basename(value))

                    return {candidate for candidate in candidates if candidate}

                filtered_entries = []
                for rel_key, file_path in entries:
                    candidates = build_candidate_keys(rel_key)
                    docchain_candidates = {
                        self.converter.build_docchain_filename(candidate)
                        for candidate in candidates
                    }

                    if (
                        normalized_includes & candidates
                        or docchain_includes & docchain_candidates
                    ):
                        filtered_entries.append((rel_key, file_path))

                entries = filtered_entries

            total_files = len(entries)
            
            # 确定开始上传的位置（续传功能）
            uploaded_count = resume_progress.get('uploaded_count', 0) if resume_progress else 0
            logger.info(f"开始上传，总文件数: {total_files}, 已上传: {uploaded_count}")
            
            # 跳过已上传的文件
            remaining_files = entries[uploaded_count:]
            if not remaining_files:
                logger.info("所有文件已上传完成")
                if progress_callback:
                    progress_callback(uploaded_count, total_files, recent_files=[])
                return DocumentUploadResponse(
                    success=True,
                    message=f"All {total_files} files already uploaded",
                    uploaded_count=uploaded_count
                )
            
            # 将剩余文件分批
            batches = [remaining_files[i:i+batch_size] for i in range(0, len(remaining_files), batch_size)]
            # 修复：current_uploaded应该从续传位置开始，而不是从0开始
            current_uploaded = uploaded_count
            
            results = []
            max_retries = 2  # 最大重试次数

            for batch_idx, batch in enumerate(batches):
                batch_success = False
                retry_count = 0
                final_result = None
                
                # 循环重试，直到成功或达到最大重试次数
                while retry_count <= max_retries and not batch_success:
                    try:
                        # 打印当前批次和重试信息
                        if retry_count == 0:
                            print(f"repo_path: {repo_paths}, topic_id: {topic_id}, 上传第 {batch_idx+1}/{len(batches)} 批文件，共 {len(batch)} 个文件 (续传: {uploaded_count > 0})")
                        else:
                            print(f"repo_path: {repo_paths}, topic_id: {topic_id}, 上传第 {batch_idx+1} 批文件重试第 {retry_count}/{max_retries} 次")
                        
                        # 执行上传
                        result = await self.upload_files(batch, topic_id)
                        final_result = result
                        
                        if result.success:
                            batch_success = True
                            # 处理“部分成功”的情况：DocChain 会返回失败的文件名列表
                            failed_doc_names = set()
                            if getattr(result, 'failed_files', None):
                                failed_doc_names = set(result.failed_files or [])
                            elif isinstance(result.data, dict) and result.data.get('success') is False:
                                failed_doc_names = set((result.data.get('data') or []))

                            # 将批次中成功的相对键筛出（依据DocChain接收后的文件名）
                            success_keys: list[str] = []
                            for rel_key, _ in batch:
                                name = self.converter.build_docchain_filename(rel_key)
                                if name not in failed_doc_names:
                                    success_keys.append(rel_key)

                            # 仅累计成功的文件数
                            current_uploaded += len(success_keys)

                            # 上传成功后，回调当前进度（仅携带成功文件）
                            if progress_callback:
                                progress_callback(
                                    current_uploaded - uploaded_count,
                                    total_files,
                                    recent_files=success_keys,
                                )
                            print(f"第 {batch_idx+1} 批文件上传成功，当前进度: {current_uploaded}/{total_files}")
                        else:
                            # 上传失败，准备重试
                            retry_count += 1
                            if retry_count <= max_retries:
                                print(f"第 {batch_idx+1} 批文件上传失败，准备重试...")
                                await asyncio.sleep(2)
                        
                    except Exception as e:
                        # 捕获上传过程中的异常
                        retry_count += 1
                        print(f"第 {batch_idx+1} 批文件上传发生异常: {str(e)}，准备重试第 {retry_count}/{max_retries} 次")
                        await asyncio.sleep(2)
                
                if final_result is None:
                    # 极端情况：所有重试都未返回结果（如一直抛异常）
                    final_result = DocumentUploadResponse(
                        success=False,
                        message=f"第 {batch_idx+1} 批文件所有重试均失败",
                        error="All retries failed"
                    )
                
                results.append(final_result)
                
                # 批次间添加延迟，避免API限制
                if batch_idx < len(batches) - 1:
                    await asyncio.sleep(1)
            
            # 检查所有批次是否成功（包括重试后）
            all_success = all(result.success for result in results)
            
            if all_success:
                success_msg = f"成功上传 {current_uploaded} 个文件到Topic {topic_id} (总共 {total_files} 个文件)"
                if uploaded_count > 0:
                    success_msg += " (续传模式)"
                
                return DocumentUploadResponse(
                    success=True,
                    message=success_msg,
                    uploaded_count=current_uploaded
                )
            else:
                failed_batches = [i+1 for i, result in enumerate(results) if not result.success]
                error_msg = f"部分批次上传失败: {failed_batches}"
                
                return DocumentUploadResponse(
                    success=False,
                    message=error_msg,
                    error=f"Failed batches: {failed_batches}",
                    uploaded_count=current_uploaded
                )
                
        except Exception as e:
            error_msg = f"上传仓库时发生错误: {str(e)}"
            logger.error(error_msg)
            return DocumentUploadResponse(
                success=False,
                message=error_msg,
                error=str(e)
            )


    async def _normalize_existing_documents(self, topic_id: str, expected_keys: List[str]) -> Optional[Dict[str, List[Any]]]:
        plan = self.analyse_filename_conflicts(topic_id, expected_keys)
        if not plan:
            return plan

        handled = False
        for bucket in ("rename", "delete"):
            for item in list(plan.get(bucket, [])):
                doc_id = item.get("doc_id")
                if not doc_id:
                    continue
                try:
                    await self.delete_document(topic_id, doc_id)
                    item["deleted"] = True
                    handled = True
                    logger.info(
                        "Topic %s 删除历史文档 doc_id=%s, 原路径=%s",
                        topic_id,
                        doc_id,
                        item.get("current"),
                    )
                except NotImplementedError:
                    item["error"] = "delete_not_implemented"
                except Exception as exc:
                    item["error"] = str(exc)
                    logger.warning(
                        "Topic %s 清理历史文档 doc_id=%s 失败: %s",
                        topic_id,
                        doc_id,
                        exc,
                    )

        if handled:
            logger.info("Topic %s 已清理历史重名文档，准备重新上传最新内容", topic_id)

        return plan

    def _log_normalization_plan(self, topic_id: str, plan: Optional[Dict[str, List[Any]]]) -> None:
        if not plan:
            return
        rename_items = [item for item in plan.get("rename", []) if not item.get("deleted")]
        delete_items = [item for item in plan.get("delete", []) if not item.get("deleted")]
        rename_count = len(rename_items)
        delete_count = len(delete_items)
        missing_count = len(plan.get("missing", []))
        if rename_count or delete_count or missing_count:
            logger.info(
                "Topic %s 文件命名检查：待重命名 %s 项，待删除 %s 项，缺失 %s 项",
                topic_id,
                rename_count,
                delete_count,
                missing_count,
            )
            for item in rename_items[:5]:
                logger.debug(
                    "重命名建议 - doc_id=%s: %s -> %s",
                    item.get("doc_id"),
                    item.get("current"),
                    item.get("target_key"),
                )

    def list_topic_documents(self, topic_id: str) -> List[Dict[str, Any]]:
        """Fetch DocChain documents for a topic, preferring the bulk list endpoint."""
        cleaned_topic = str(topic_id).strip()
        if not cleaned_topic:
            return []

        docs: List[Dict[str, Any]] = []

        # 优先使用不分页接口（DocChain批量返回全部文档，网络开销更小）
        try:
            response = self._make_request("GET", "/llmdoc/v1/doc/list", params={"topic_id": cleaned_topic})
            if isinstance(response, dict) and response.get("success", False):
                payload = response.get("data")
                if isinstance(payload, list):
                    docs = payload
                elif isinstance(payload, dict) and isinstance(payload.get("list"), list):
                    docs = payload.get("list", [])
        except Exception as exc:
            logger.debug("Topic %s bulk doc list failed: %s", cleaned_topic, exc)

        if docs:
            return [self._sanitize_docchain_doc(entry) for entry in docs]

        # 兜底：批量接口不可用时再走分页，最多拉100/页
        page = 1
        page_size = 100
        while True:
            params = {
                "page": page,
                "size": page_size,
                "topic_id": cleaned_topic,
            }
            response = self._make_request("GET", "/llmdoc/v1/doc/list/pagination", params=params)
            if not isinstance(response, dict) or not response.get("success", False):
                break
            data = response.get("data") or {}
            page_docs = data.get("list") or []
            if not page_docs:
                break
            docs.extend(page_docs)
            if len(page_docs) < page_size:
                break
            page += 1

        return [self._sanitize_docchain_doc(entry) for entry in docs]

    @staticmethod
    def _sanitize_docchain_doc(entry: Dict[str, Any]) -> Dict[str, Any]:
        """提取DocChain文档记录中最少且稳定的字段."""
        if not isinstance(entry, dict):
            return {}
        doc_id = entry.get("id")
        path = entry.get("path") or entry.get("name") or entry.get("filename")
        sanitized = {
            "id": int(doc_id) if doc_id is not None else None,
            "path": str(path) if path is not None else None,
        }
        # Preserve raw path variants for backwards compatibility when present.
        if "name" in entry and sanitized["path"] is None:
            sanitized["path"] = str(entry["name"])
        if "filename" in entry and sanitized.get("path") is None:
            sanitized["path"] = str(entry["filename"])
        return sanitized

    def build_local_manifest(
        self,
        repo_paths: List[str],
        namespace_map: Optional[Dict[str, str]] = None,
    ) -> List[Dict[str, Any]]:
        if not repo_paths:
            return []
        try:
            return self.converter.collect_repo_manifest(
                repo_paths,
                path_namespaces=namespace_map,
            )
        except Exception as exc:
            logger.debug(f"Failed to collect repo manifest for {repo_paths}: {exc}")
            return []

    def summarize_repo_documents(
        self,
        topic_id: str,
        namespace: str,
        expected_manifest: Optional[List[Dict[str, Any]]] = None,
    ) -> Optional[Dict[str, Any]]:
        """Return DocChain document statistics scoped to a repository namespace."""
        if not topic_id:
            return None

        normalized_namespace = self.converter._normalize_relative_key(namespace or "")
        sanitized_namespace = self.converter.build_docchain_filename(normalized_namespace) if normalized_namespace else ""

        try:
            docs = self.list_topic_documents(topic_id)
        except Exception as exc:
            logger.debug("Failed to list documents for topic %s: %s", topic_id, exc)
            return None

        matched_total = 0
        canonical_keys: Set[str] = set()
        extra_total = 0
        unmatched_samples: List[str] = []

        expected_doc_names: Set[str] = set()
        docchain_entry_map: Dict[str, Dict[str, Any]] = {}
        plain_index: Dict[str, Set[str]] = defaultdict(set)
        source_index: Dict[str, Set[str]] = defaultdict(set)
        basename_index: Dict[str, Set[str]] = defaultdict(set)
        expected_total = 0
        duplicate_total = 0
        duplicate_docs: List[Dict[str, Any]] = []
        unexpected_docs: List[Dict[str, Any]] = []

        if expected_manifest:
            for entry in expected_manifest:
                docchain_name = entry.get("docchain_name")
                relative_without_namespace = self.converter._normalize_relative_key(
                    str(entry.get("relative_without_namespace") or "")
                )
                source_relative = self.converter._normalize_relative_key(
                    str(entry.get("source_relative") or "")
                )
                basename = entry.get("basename") or ""
                if not docchain_name:
                    continue

                expected_doc_names.add(docchain_name)
                docchain_entry_map[docchain_name] = entry
                expected_total += 1

                if relative_without_namespace:
                    plain_index[relative_without_namespace].add(docchain_name)
                if source_relative:
                    source_index[source_relative].add(docchain_name)

                if basename:
                    base_key = self._strip_numeric_suffix(basename).lower()
                    basename_index[base_key].add(docchain_name)
                    basename_md = self._strip_numeric_suffix(f"{basename}.md").lower()
                    basename_index[basename_md].add(docchain_name)

            expected_total = len(expected_doc_names)

        for doc in docs:
            raw_path = str(doc.get("path") or "").strip()
            if not raw_path:
                continue

            normalized_path = self.converter._normalize_relative_key(raw_path.replace('\\', '/'))
            docchain_key = self.converter.build_docchain_filename(normalized_path)

            if expected_manifest:
                candidate = None
                for candidate_key in (
                    docchain_key,
                    self._canonicalize_docchain_key(docchain_key, sanitized_namespace),
                ):
                    if candidate_key and candidate_key in expected_doc_names:
                        candidate = candidate_key
                        break

                if not candidate:
                    # namespace 已去除，退化为相对路径、basename 级别的匹配，兼容旧数据
                    derived_plain = normalized_path
                    if normalized_namespace and derived_plain.startswith(normalized_namespace):
                        derived_plain = derived_plain[len(normalized_namespace):]
                    derived_plain = derived_plain.lstrip('/')
                    normalized_plain = self.converter._normalize_relative_key(derived_plain)

                    if normalized_plain and normalized_plain in plain_index:
                        for option in plain_index[normalized_plain]:
                            if option not in canonical_keys:
                                candidate = option
                                break
                        if not candidate:
                            candidate = next(iter(plain_index[normalized_plain]), None)

                    if not candidate and normalized_plain:
                        base_source = normalized_plain[:-3] if normalized_plain.endswith('.md') else normalized_plain
                        if base_source in source_index:
                            for option in source_index[base_source]:
                                if option not in canonical_keys:
                                    candidate = option
                                    break
                            if not candidate:
                                candidate = next(iter(source_index[base_source]), None)

                    if not candidate:
                        basename = os.path.basename(normalized_plain or normalized_path)
                        if basename:
                            base_key = self._strip_numeric_suffix(basename).lower()
                            for option in basename_index.get(base_key, set()):
                                if option not in canonical_keys:
                                    candidate = option
                                    break
                            if not candidate:
                                candidate = next(iter(basename_index.get(base_key, set())), None)

                if candidate:
                    matched_total += 1
                    if candidate in canonical_keys:
                        duplicate_total += 1
                        extra_total += 1
                        duplicate_docs.append({
                            "doc_id": doc.get("id"),
                            "path": normalized_path,
                            "docchain_key": docchain_key,
                        })
                    else:
                        canonical_keys.add(candidate)
                    continue

                extra_total += 1
                if len(unmatched_samples) < 5:
                    unmatched_samples.append(raw_path)
                unexpected_docs.append({
                    "doc_id": doc.get("id"),
                    "path": normalized_path,
                    "docchain_key": docchain_key,
                })
                continue

            if self._matches_namespace(docchain_key, normalized_path, sanitized_namespace, normalized_namespace):
                matched_total += 1
                canonical_key = self._canonicalize_docchain_key(docchain_key, sanitized_namespace)
                if canonical_key in canonical_keys:
                    duplicate_total += 1
                    extra_total += 1
                    duplicate_docs.append({
                        "doc_id": doc.get("id"),
                        "path": normalized_path,
                        "docchain_key": docchain_key,
                    })
                else:
                    canonical_keys.add(canonical_key)
            else:
                extra_total += 1
                if len(unmatched_samples) < 5:
                    unmatched_samples.append(raw_path)
                unexpected_docs.append({
                    "doc_id": doc.get("id"),
                    "path": normalized_path,
                    "docchain_key": docchain_key,
                })

        summary = {
            "docchain_total": matched_total,
            "canonical_total": len(canonical_keys),
            "unexpected_total": extra_total,
            "namespace": normalized_namespace,
        }

        if expected_manifest:
            missing_doc_names = expected_doc_names - canonical_keys
            missing_samples = []
            missing_entries: List[Dict[str, Any]] = []
            for name in list(missing_doc_names)[:5]:
                entry = docchain_entry_map.get(name)
                if not entry:
                    continue
                sample = entry.get("source_relative") or entry.get("relative_key") or name
                if sample:
                    missing_samples.append(sample)
            summary["missing_doc_names"] = list(missing_doc_names)
            summary.update({
                "expected_total": expected_total,
                "missing_total": len(missing_doc_names),
                "missing_samples": missing_samples,
                "duplicate_total": duplicate_total,
                "duplicate_docs": duplicate_docs,
                "unexpected_docs": unexpected_docs,
            })

        if unmatched_samples:
            summary["unexpected_samples"] = unmatched_samples

        logger.debug(
            "Topic %s namespace %s document summary: matched=%s canonical=%s extras=%s",
            topic_id,
            normalized_namespace,
            matched_total,
            summary["canonical_total"],
            extra_total,
        )

        return summary

    def _matches_namespace(
        self,
        docchain_key: str,
        normalized_path: str,
        sanitized_namespace: str,
        normalized_namespace: str,
    ) -> bool:
        if not sanitized_namespace:
            return True

        if docchain_key == sanitized_namespace:
            return True
        if docchain_key.startswith(f"{sanitized_namespace}__"):
            return True
        if not docchain_key.startswith("@"):
            return True
        if normalized_namespace and normalized_path.startswith(normalized_namespace):
            return True
        if normalized_namespace and normalized_path.startswith(f"{normalized_namespace}/"):
            return True
        return False

    def _canonicalize_docchain_key(self, docchain_key: str, namespace_key: str) -> str:
        if not docchain_key:
            return docchain_key

        normalized = docchain_key
        if namespace_key:
            if normalized.startswith(f"{namespace_key}__"):
                normalized = normalized[len(namespace_key) + 2 :]
            elif normalized.startswith(namespace_key):
                normalized = normalized[len(namespace_key):]

        # 去除命名空间前缀与尾部序号，得到可对比的“规范路径”
        parts = [part for part in normalized.split("__") if part]
        if parts:
            parts[-1] = self._strip_numeric_suffix(parts[-1])
        canonical = "__".join(parts)
        return canonical or normalized

    def analyse_filename_conflicts(self, topic_id: str, expected_keys: List[str]) -> Dict[str, List[Any]]:
        plan = {"rename": [], "delete": [], "missing": []}
        try:
            docs = self.list_topic_documents(topic_id)
        except Exception as exc:
            logger.debug(f"获取Topic {topic_id} 文档列表失败: {exc}")
            return plan

        expected_map = {
            self.converter.build_docchain_filename(key): key
            for key in expected_keys
        }
        matched = set()

        for doc in docs:
            path = (doc.get("path") or doc.get("name") or doc.get("filename") or "").strip()
            if not path:
                continue
            normalized_path = self.converter._normalize_relative_key(path.replace('\\', '/'))
            docchain_name = self.converter.build_docchain_filename(normalized_path)
            if docchain_name in expected_map:
                matched.add(docchain_name)
                continue

            stripped_current = self._strip_numeric_suffix(os.path.basename(normalized_path))
            target_key = None
            for candidate in expected_keys:
                if stripped_current == self._strip_numeric_suffix(os.path.basename(candidate)):
                    target_key = candidate
                    break

            if target_key:
                plan["rename"].append({
                    "doc_id": doc.get("id"),
                    "current": normalized_path,
                    "target_key": target_key,
                    "target_filename": self.converter.build_docchain_filename(target_key),
                })
            else:
                plan["delete"].append({
                    "doc_id": doc.get("id"),
                    "current": normalized_path,
                })

        plan["missing"] = [expected_map[name] for name in expected_map.keys() - matched]
        return plan

    @staticmethod
    def _strip_numeric_suffix(name: str) -> str:
        base, ext = os.path.splitext(name)
        base = re.sub(r'(?:\s*\(\d+\)|[_-]\d+)$', '', base)
        return f"{base}{ext}"

    async def rename_document(self, topic_id: str, doc_id: int, new_file_name: str) -> bool:
        """
        重命名DocChain中的文档
        
        Args:
            topic_id: Topic ID
            doc_id: 文档ID
            new_file_name: 新的文件名
            
        Returns:
            bool: 重命名是否成功
        """
        try:
            # 构建重命名请求
            endpoint = f"/llmdoc/v1/doc/rename_file"
            params = {
                "new_file_name": new_file_name,
                "file_id": doc_id
            }
            
            def _rename():
                response = self._make_request("POST", endpoint, params=params)
                if isinstance(response, dict) and response.get("success", False):
                    return True
                else:
                    error_msg = response.get("message") or response.get("error") or "重命名失败"
                    raise Exception(f"DocChain重命名失败: {error_msg}")
            
            await asyncio.to_thread(_rename)
            logger.info(f"成功重命名文档 {doc_id} -> {new_file_name}")
            return True
            
        except Exception as e:
            logger.error(f"重命名文档失败 doc_id={doc_id}, new_name={new_file_name}: {e}")
            return False

    async def delete_document(self, topic_id: Optional[str], doc_id: int) -> None:
        payload = {
            "id": int(doc_id),
            "operation": "delete",
        }
        if topic_id is not None:
            try:
                payload["topic_id"] = int(topic_id)
            except (TypeError, ValueError):
                payload["topic_id"] = topic_id

        def _delete():
            response = self._make_request("POST", "/llmdoc/v1/doc/update", data=payload)
            if not isinstance(response, dict):
                raise Exception("DocChain delete failed: unexpected response")
            if not response.get("success", True):
                raise Exception(response.get("message") or response.get("error") or "DocChain delete failed")

        await asyncio.to_thread(_delete)

    async def delete_documents_batch(self, doc_ids: List[int]) -> None:
        payload = {"docIds": [int(doc_id) for doc_id in doc_ids if doc_id is not None]}
        if not payload["docIds"]:
            return

        def _batch():
            response = self._make_request("POST", "/llmdoc/v1/doc/batchDelete", data=payload)
            if not isinstance(response, dict):
                raise Exception("DocChain batch delete failed: unexpected response")
            if not response.get("success", True):
                raise Exception(response.get("message") or response.get("error") or "DocChain batch delete failed")

        await asyncio.to_thread(_batch)

    async def cleanup_duplicate_files_by_paths(self, topic_id: str, file_paths_to_update: List[str]) -> Dict[str, Any]:
        """
        根据完整文件路径清理重名文件，为新文件上传做准备（精确路径匹配）
        
        Args:
            topic_id: Topic ID
            file_paths_to_update: 要更新的文件路径列表（包含完整相对路径）
            
        Returns:
            Dict: 清理结果统计
        """
        cleanup_stats = {
            "total_checked": len(file_paths_to_update),
            "duplicates_found": 0,
            "deleted_count": 0,
            "renamed_count": 0,
            "failed_count": 0,
            "details": []
        }
        
        try:
            logger.info(f"开始清理Topic {topic_id} 中的重名文件，检查 {len(file_paths_to_update)} 个文件路径")
            
            for file_path in file_paths_to_update:
                try:
                    # 使用精确路径匹配搜索重名文件
                    duplicate_docs = self.search_duplicate_files(topic_id, file_path, exact_path_match=True)
                    
                    if not duplicate_docs:
                        logger.debug(f"未找到重名文件: {file_path}")
                        continue
                    
                    cleanup_stats["duplicates_found"] += len(duplicate_docs)
                    
                    # 处理重名文件
                    for doc in duplicate_docs:
                        doc_id = doc.get("id")
                        current_path = doc.get("path", "")
                        
                        try:
                            # 策略：直接删除旧的重名文件，让新文件使用正确的名称
                            await self.delete_document(topic_id, doc_id)
                            cleanup_stats["deleted_count"] += 1
                            
                            cleanup_stats["details"].append({
                                "action": "deleted",
                                "doc_id": doc_id,
                                "old_path": current_path,
                                "target_file_path": file_path,
                                "success": True
                            })
                            
                            logger.info(f"删除重名文档: doc_id={doc_id}, path={current_path}, target={file_path}")
                            
                        except Exception as e:
                            cleanup_stats["failed_count"] += 1
                            cleanup_stats["details"].append({
                                "action": "delete_failed",
                                "doc_id": doc_id,
                                "old_path": current_path,
                                "target_file_path": file_path,
                                "success": False,
                                "error": str(e)
                            })
                            logger.error(f"删除重名文档失败: doc_id={doc_id}, error={e}")
                    
                    # 在处理每个文件后稍作延迟，避免API限制
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    logger.error(f"处理文件路径 {file_path} 的重名检查时出错: {e}")
                    cleanup_stats["failed_count"] += 1
            
            logger.info(
                f"重名文件清理完成 - Topic: {topic_id}, "
                f"检查: {cleanup_stats['total_checked']}, "
                f"发现重名: {cleanup_stats['duplicates_found']}, "
                f"删除: {cleanup_stats['deleted_count']}, "
                f"重命名: {cleanup_stats['renamed_count']}, "
                f"失败: {cleanup_stats['failed_count']}"
            )
            
            return cleanup_stats
            
        except Exception as e:
            logger.error(f"清理重名文件过程中出现异常: {e}", exc_info=True)
            cleanup_stats["error"] = str(e)
            return cleanup_stats

    async def cleanup_duplicate_files(self, topic_id: str, files_to_update: List[str]) -> Dict[str, Any]:
        """
        清理重名文件，为新文件上传做准备（向后兼容方法，使用文件名匹配）
        
        Args:
            topic_id: Topic ID
            files_to_update: 要更新的文件名列表
            
        Returns:
            Dict: 清理结果统计
        """
        cleanup_stats = {
            "total_checked": len(files_to_update),
            "duplicates_found": 0,
            "deleted_count": 0,
            "renamed_count": 0,
            "failed_count": 0,
            "details": []
        }
        
        try:
            logger.info(f"开始清理Topic {topic_id} 中的重名文件，检查 {len(files_to_update)} 个文件")
            
            for filename in files_to_update:
                try:
                    # 使用模糊匹配搜索重名文件（向后兼容）
                    duplicate_docs = self.search_duplicate_files(topic_id, filename, exact_path_match=False)
                    
                    if not duplicate_docs:
                        continue
                    
                    cleanup_stats["duplicates_found"] += len(duplicate_docs)
                    
                    # 处理重名文件
                    for doc in duplicate_docs:
                        doc_id = doc.get("id")
                        current_path = doc.get("path", "")
                        
                        try:
                            # 策略：直接删除旧的重名文件，让新文件使用正确的名称
                            await self.delete_document(topic_id, doc_id)
                            cleanup_stats["deleted_count"] += 1
                            
                            cleanup_stats["details"].append({
                                "action": "deleted",
                                "doc_id": doc_id,
                                "old_path": current_path,
                                "target_file": filename,
                                "success": True
                            })
                            
                            logger.info(f"删除重名文档: doc_id={doc_id}, path={current_path}")
                            
                        except Exception as e:
                            cleanup_stats["failed_count"] += 1
                            cleanup_stats["details"].append({
                                "action": "delete_failed",
                                "doc_id": doc_id,
                                "old_path": current_path,
                                "target_file": filename,
                                "success": False,
                                "error": str(e)
                            })
                            logger.error(f"删除重名文档失败: doc_id={doc_id}, error={e}")
                    
                    # 在处理每个文件后稍作延迟，避免API限制
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    logger.error(f"处理文件 {filename} 的重名检查时出错: {e}")
                    cleanup_stats["failed_count"] += 1
            
            logger.info(
                f"重名文件清理完成 - Topic: {topic_id}, "
                f"检查: {cleanup_stats['total_checked']}, "
                f"发现重名: {cleanup_stats['duplicates_found']}, "
                f"删除: {cleanup_stats['deleted_count']}, "
                f"重命名: {cleanup_stats['renamed_count']}, "
                f"失败: {cleanup_stats['failed_count']}"
            )
            
            return cleanup_stats
            
        except Exception as e:
            logger.error(f"清理重名文件过程中出现异常: {e}", exc_info=True)
            cleanup_stats["error"] = str(e)
            return cleanup_stats


    def search(self, query: str, topic_ids: Union[str, List[str]], size: int = 5) -> Optional[SearchResponse]:
        """
        在DocChain中搜索内容

        Args:
            query: 搜索查询
            topic_id: Topic ID
            size: 返回结果数量

        Returns:
            Optional[SearchResponse]: 搜索响应，如果失败则返回None
        """
        try:
            # 假设 topic_ids 是类似 "67,68" 的字符串，先分割并转换为整数列表
            if isinstance(topic_ids, str):
                # 按逗号分割，去除空格，转换为整数
                topic_id_list = [int(id.strip()) for id in topic_ids.split(",") if id.strip()]
            elif isinstance(topic_ids, list):
                # 如果已经是列表，确保元素是整数（可选）
                topic_id_list = [int(id) if isinstance(id, str) else id for id in topic_ids]
            else:
                # 处理单个 ID 的情况（如 67 或 "67"）
                topic_id_list = [int(topic_ids)] if topic_ids else []
            
            
            # 构建请求数据
            request_data = {
                "query": query,
                "topic_id_list": topic_id_list,
                "size": size,
                "with_context": False,
                "score": 0.1,
                "ranking_mode": "rerank",
                "rrf_k": int(60)
            }
            
            # 关键修改：手动将 request_data 序列化为 JSON 字符串
            import json
           # 关键：使用 json.dumps 的 separators 参数，生成紧凑 JSON
            serialized_data = json.dumps(
                request_data,
                separators=(',', ':'),  # 去掉逗号和冒号后的空格
                ensure_ascii=False      # 避免中文转义（可选）
            )
            logger.debug(f"最终发送的 JSON 数据：{serialized_data}")  # 关键：检查是否有 null
  
            # 发送请求
            # response = self._make_request("POST", "/llmdoc/v1/search", data=request_data)
            response = self._make_request("POST", "/v1/search", data=serialized_data)
            
            # 解析响应
            text_results = []
            for item in response.get("text", []):
                text_results.append(SearchResult(
                    score=item["score"],
                    data=item["data"]
                ))
                data1 = item["data"]
                # print(f"请求问题: "+query)
                # print(f"文档ID: {data1['doc_id']}")
                # print(f"内容摘要: {data1['summary']}")
                # print(f"content length: {len(data1['content'])}")
                # print(f"score: {item["score"]}")
                # print("\n\n\n\n\n\n")
                
            image_results = []
            for item in response.get("image", []):
                image_results.append(SearchResult(
                    score=item["score"],
                    data=item["data"]
                ))
                
            table_results = []
            for item in response.get("table", []):
                table_results.append(SearchResult(
                    score=item["score"],
                    data=item["data"]
                ))
            
            return SearchResponse(
                text=text_results,
                image=image_results,
                table=table_results
            )
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return None

    def get_topic_status(self, topic_id: str) -> Optional[Dict]:
        """
        获取Topic下文档的处理状态

        Args:
            topic_id: Topic ID

        Returns:
            Optional[Dict]: Topic状态详情，包含success、failed、running等信息，如果失败则返回None
            返回格式：
            {
                "success": 20,
                "failed": 0,
                "running": 58,
                "splitting": 58,
                "summarizing": 0,
                "total": 78
            }
        """
        try:
            print(f"查询Topic {topic_id} 的文档处理状态")
            response = self._make_request("GET", f"/v1/doc/topic_status", params={"topic_id": topic_id})
            
            # 检查响应格式
            if response.get("success", False) and "data" in response:
                status_data = response["data"]
                print(f"Topic {topic_id} 状态: {status_data}")
                return status_data
            else:
                print(f"获取Topic {topic_id} 状态失败: {response}")
                return None
                
        except Exception as e:
            logger.error(f"获取Topic状态失败: {e}")
            return None

    def get_topic_info(self, topic_id: str) -> Optional[Dict]:
        """
        获取Topic基本信息

        Args:
            topic_id: Topic ID

        Returns:
            Optional[Dict]: Topic信息，如果失败则返回None
        """
        try:
            print(f"查询Topic {topic_id} 的基本信息")
            response = self._make_request("GET", "/v1/topic/info", params={"topic_id": topic_id})
            
            # 检查响应格式
            if response.get("success", False) and "data" in response:
                topic_info = response["data"]
                print(f"Topic {topic_id} 信息: {topic_info}")
                return topic_info
            else:
                print(f"获取Topic {topic_id} 信息失败: {response}")
                return None
                
        except Exception as e:
            logger.error(f"获取Topic信息失败: {e}")
            return None

    async def wait_for_topic_ready(self, topic_id: str, max_wait_time: int = 300, check_interval: int = 10) -> bool:
        """
        等待Topic处理完成

        Args:
            topic_id: Topic ID
            max_wait_time: 最大等待时间（秒），默认5分钟
            check_interval: 检查间隔（秒）

        Returns:
            bool: Topic是否准备好
        """
        import time
        
        print(f"等待Topic {topic_id} 处理完成，最大等待时间: {max_wait_time}秒")
        
        start_time = time.time()
        consecutive_stable_checks = 0  # 连续稳定检查次数
        required_stable_checks = 3     # 需要连续3次检查状态稳定才认为完成
        
        while time.time() - start_time < max_wait_time:
            status_data = self.get_topic_status(topic_id)
            
            if status_data is None:
                logger.error(f"无法获取Topic {topic_id} 状态")
                return False
            
            # 解析状态信息
            total = status_data.get("total", 0)
            success = status_data.get("success", 0)
            failed = status_data.get("failed", 0)
            running = status_data.get("running", 0)
            splitting = status_data.get("splitting", 0)
            summarizing = status_data.get("summarizing", 0)
            
            print(f"Topic {topic_id} 文档状态 - 总计: {total}, 成功: {success}, 失败: {failed}, "
                       f"运行中: {running}, 分割中: {splitting}, 总结中: {summarizing}")
            
            # 判断处理是否完成
            processed = success + failed  # 已完成处理的文档数量
            processing = running + splitting + summarizing  # 正在处理的文档数量
            
            if total == 0:
                # 没有文档，直接返回完成
                print(f"Topic {topic_id} 没有文档，直接返回完成")
                return True
            
            if processing == 0 and processed > 0:
                # 没有正在处理的文档，且有已处理的文档
                consecutive_stable_checks += 1
                print(f"Topic {topic_id} 状态稳定 ({consecutive_stable_checks}/{required_stable_checks})")
                
                if consecutive_stable_checks >= required_stable_checks:
                    print(f"Topic {topic_id} 处理完成")
                    return True
            else:
                # 重置连续稳定检查计数
                consecutive_stable_checks = 0
            
            # 等待一段时间后继续检查
            await asyncio.sleep(check_interval)
        
        logger.warning(f"Topic {topic_id} 等待超时")
        return False

    async def incremental_update_topic(self, topic_id: str, repo_path: str, sub_repo_paths: List[str], changed_files: list, progress_callback=None):
        """
        增量更新Topic中的文件（完全清理策略：删除所有重名文件，重新上传所有相关文件）
        
        Args:
            topic_id: Topic ID
            repo_path: 仓库本地路径
            sub_repo_paths: 子仓库路径列表
            changed_files: 变更的文件列表
            progress_callback: 进度回调函数
            
        Returns:
            DocumentUploadResponse: 上传结果
        """
        try:
            logger.info(f"开始增量更新Topic {topic_id}，变更文件数: {len(changed_files)}")
            
            # 第一步：提取所有变更文件的文件名（用于清理重名文件）
            changed_filenames = set()
            valid_changed_files = []
            
            for file_path in changed_files:
                full_path = os.path.join(repo_path, file_path)
                
                # 检查文件是否存在（排除已删除的文件）
                if os.path.exists(full_path) and os.path.isfile(full_path):
                    try:
                        # 检查文件大小（跳过过大的文件）
                        file_size = os.path.getsize(full_path)
                        if file_size > 10 * 1024 * 1024:  # 10MB限制
                            logger.warning(f"跳过过大的文件: {file_path} ({file_size} bytes)")
                            continue
                        
                        # 检查文件类型（只处理文本文件）
                        if self._is_text_file(full_path):
                            valid_changed_files.append(file_path)
                            # 提取文件名用于清理所有重名文件
                            filename = os.path.basename(file_path)
                            changed_filenames.add(filename)
                        else:
                            logger.debug(f"跳过非文本文件: {file_path}")
                            
                    except Exception as e:
                        logger.warning(f"检查文件 {file_path} 时出错: {str(e)}")
            
            if not valid_changed_files:
                logger.info("没有需要同步的文件")
                return DocumentUploadResponse(success=True, error=None, uploaded_count=0)
            
            # 第二步：完全清理所有重名文件（删除所有同名文件，不管路径）
            logger.info(f"开始清理Topic {topic_id} 中的所有重名文件: {list(changed_filenames)}")
            cleanup_result = await self.cleanup_duplicate_files(topic_id, list(changed_filenames))
            
            if cleanup_result.get("duplicates_found", 0) > 0:
                logger.info(
                    f"重名文件清理结果: 发现 {cleanup_result['duplicates_found']} 个重名文件, "
                    f"删除 {cleanup_result['deleted_count']} 个, "
                    f"失败 {cleanup_result['failed_count']} 个"
                )
            
            # 第三步：重新上传所有相关文件（包括同名但路径不同的所有文件）
            logger.info(f"开始重新上传所有相关文件到Topic {topic_id}")
            
            # 搜索仓库中所有具有相同文件名的文件
            all_files_to_upload = []
            for repo_root in [repo_path] + sub_repo_paths:
                for root, dirs, files in os.walk(repo_root):
                    for file in files:
                        if file in changed_filenames:
                            file_full_path = os.path.join(root, file)
                            if self._is_text_file(file_full_path):
                                all_files_to_upload.append(os.path.relpath(file_full_path, repo_root))
            
            # 去重
            all_files_to_upload = list(set(all_files_to_upload))
            logger.info(f"找到 {len(all_files_to_upload)} 个同名文件需要重新上传: {all_files_to_upload}")
            
            # 使用upload_repo方法上传所有文件
            upload_result = await self.upload_repo(
                repo_paths=[repo_path] + sub_repo_paths, 
                topic_id=topic_id, 
                included_files=all_files_to_upload,  # 上传所有相关文件
                progress_callback=progress_callback,
                normalize_existing=False  # 已经手动清理了重名文件，跳过自动清理
            )
            
            if not upload_result.success:
                raise Exception(f"增量更新失败: {upload_result.error}")
            
            uploaded_files = upload_result.uploaded_count
            logger.info(f"增量更新完成，共上传 {uploaded_files} 个文件")
            
            # 合并清理和上传的结果
            return DocumentUploadResponse(
                success=True, 
                error=None, 
                uploaded_count=uploaded_files,
                message=f"增量更新成功: 清理 {cleanup_result.get('deleted_count', 0)} 个重名文件, 重新上传 {uploaded_files} 个相关文件"
            )
            
        except Exception as e:
            error_msg = f"增量更新Topic失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return DocumentUploadResponse(success=False, error=error_msg, uploaded_count=0)

    async def upload_specific_files(self, topic_id: str, file_paths: list, progress_callback=None):
        """
        上传指定的文件到Topic
        
        Args:
            topic_id: Topic ID
            file_paths: 文件路径列表（绝对路径）
            progress_callback: 进度回调函数
            
        Returns:
            DocumentUploadResponse: 上传结果
        """
        try:
            upload_count = 0
            
            for i, file_path in enumerate(file_paths):
                try:
                    # 读取文件内容
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 构建上传请求
                    filename = os.path.basename(file_path)
                    request = DocumentUploadRequest(
                        topic_id=int(topic_id),
                        filename=filename,
                        content=content
                    )
                    
                    # 调用上传API
                    response = self._make_request(
                        "POST", 
                        "/llmdoc/v1/doc/upload", 
                        data=request.to_dict()
                    )
                    
                    if response.get("code") == 200:
                        upload_count += 1
                        logger.debug(f"文件 {filename} 上传成功")
                    else:
                        logger.warning(f"文件 {filename} 上传失败: {response.get('message', 'Unknown error')}")
                    
                    # 更新进度
                    if progress_callback:
                        progress_callback(i + 1, len(file_paths))
                        
                except UnicodeDecodeError:
                    logger.warning(f"跳过二进制文件: {file_path}")
                except Exception as e:
                    logger.error(f"上传文件 {file_path} 时出错: {str(e)}")
            
            return DocumentUploadResponse(
                success=True, 
                error=None, 
                uploaded_count=upload_count
            )
            
        except Exception as e:
            error_msg = f"上传指定文件失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return DocumentUploadResponse(success=False, error=error_msg, uploaded_count=0)

    def _is_text_file(self, file_path: str) -> bool:
        """
        判断文件是否为文本文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否为文本文件
        """
        # 定义文本文件扩展名
        text_extensions = {
            '.txt', '.md', '.py', '.js', '.ts', '.jsx', '.tsx', '.css', '.scss', '.sass',
            '.html', '.htm', '.xml', '.json', '.yaml', '.yml', '.toml', '.ini', '.cfg',
            '.conf', '.properties', '.sql', '.sh', '.bash', '.ps1', '.bat', '.cmd',
            '.java', '.c', '.cpp', '.h', '.hpp', '.cs', '.php', '.rb', '.go', '.rs',
            '.swift', '.kt', '.scala', '.clj', '.r', '.m', '.mm', '.pl', '.pm', '.lua',
            '.vim', '.dockerfile', '.gitignore', '.gitattributes', '.editorconfig',
            '.eslintrc', '.prettierrc', '.babelrc', '.tsconfig', '.package', '.lock',
            '.log', '.csv', '.svg', '.tex', '.latex', '.rst', '.adoc', '.org'
        }
        
        # 获取文件扩展名
        ext = os.path.splitext(file_path)[1].lower()
        
        # 检查扩展名
        if ext in text_extensions:
            return True
        
        # 对于没有扩展名的文件，尝试读取部分内容判断
        if not ext:
            try:
                with open(file_path, 'rb') as f:
                    chunk = f.read(1024)
                    # 检查是否包含空字节（通常表示二进制文件）
                    if b'\x00' in chunk:
                        return False
                    # 尝试解码为UTF-8
                    chunk.decode('utf-8')
                    return True
            except (UnicodeDecodeError, IOError):
                return False
        
        return False

    def chat_completion(self, messages: List[Dict], model: str, topic_id: str,
                       temperature: float = 0.7, stream: bool = True) -> Union[Dict, Any]:
        """
        调用DocChain的聊天完成API

        Args:
            messages: 聊天消息列表
            model: 模型名称（使用topic_id作为模型名）
            topic_id: Topic ID
            temperature: 温度参数
            stream: 是否流式响应

        Returns:
            Union[Dict, Any]: 聊天完成响应
        """
        try:
            # 构建请求数据
            request_data = {
                "model": topic_id,  # 使用topic_id作为模型名
                "messages": messages,
                "temperature": temperature,
                "top_p": 0.95,
                "stream": stream
            }
            
            # 发送请求
            url = f"{self.base_url}/llmdoc/openai/v1/chat/completions"
            # 确保添加API密钥
            headers = {
                "Content-Type": "application/json"
            }
            
            # 添加API密钥头部
            if self.api_key:
                headers["X-Api-Key"] = self.api_key
            
            if stream:
                # 流式响应
                response = requests.post(
                    url,
                    headers=headers,
                    json=request_data,
                    stream=True
                )
                response.raise_for_status()
                return response
            else:
                # 非流式响应
                response = requests.post(
                    url,
                    headers=headers,
                    json=request_data
                )
                response.raise_for_status()
                return response.json()
            
        except Exception as e:
            logger.error(f"聊天完成请求失败: {e}")
            raise 
        
    def search_duplicate_files(self, topic_id: str, target_file_path: str, exact_path_match: bool = True) -> List[Dict[str, Any]]:
        """
        搜索Topic中的重名文件（支持精确路径匹配和模糊文件名匹配）
        
        Args:
            topic_id: Topic ID
            target_file_path: 目标文件路径（如 "src/com/test/area/pom.xml" 或 "pom.xml"）
            exact_path_match: 是否进行精确路径匹配，True时只匹配相同路径的重名文件
            
        Returns:
            List[Dict]: 匹配的文档列表，包含id, path等信息
        """
        try:
            # 提取文件名
            filename = os.path.basename(target_file_path)
            base_name, ext = os.path.splitext(filename)
            
            # 搜索关键词为基础文件名
            params = {
                "page": 1,
                "size": 50,  # 增加搜索数量以确保找到所有重名文件
                "topic_id": str(topic_id),
                "keyword": base_name  # 搜索基础名称
            }
            
            response = self._make_request("GET", "/llmdoc/v1/doc/list/pagination", params=params)
            
            if not response.get("success", False):
                logger.warning(f"搜索重名文件失败 topic_id={topic_id}, target_path={target_file_path}")
                return []
            
            data = response.get("data", {})
            docs = data.get("list", [])
            
            # 过滤出真正匹配的文件
            duplicate_files = []
            for doc in docs:
                doc_path = doc.get("path", "").strip()
                if not doc_path:
                    continue
                
                # 检查是否匹配
                if exact_path_match:
                    # 精确路径匹配：只匹配相同路径的重名文件
                    if self._is_same_path_duplicate(doc_path, target_file_path):
                        duplicate_files.append(doc)
                else:
                    # 模糊匹配：匹配所有同名文件（不考虑路径）
                    if self._is_duplicate_file(doc_path, filename):
                        duplicate_files.append(doc)
            
            logger.info(f"找到 {len(duplicate_files)} 个重名文件 for {target_file_path} in topic {topic_id} (exact_path={exact_path_match})")
            return duplicate_files
            
        except Exception as e:
            logger.error(f"搜索重名文件失败: {e}", exc_info=True)
            return []

    def _is_same_path_duplicate(self, doc_path: str, target_file_path: str) -> bool:
        """
        判断文档路径是否与目标文件路径匹配（精确路径匹配，包括带数字后缀的情况）
        
        Args:
            doc_path: 文档路径（如 "@deepwiki/src/com/test/area/pom.xml(2).md"）
            target_file_path: 目标文件路径（如 "src/com/test/area/pom.xml"）
            
        Returns:
            bool: 是否为相同路径的重名文件
        """
        try:
            # 去掉.md后缀
            doc_clean_path = doc_path
            if doc_clean_path.endswith('.md'):
                doc_clean_path = doc_clean_path[:-3]
            
            # 去掉仓库前缀（如@deepwiki/）
            if '/' in doc_clean_path and doc_clean_path.startswith('@'):
                parts = doc_clean_path.split('/', 1)
                if len(parts) > 1:
                    doc_clean_path = parts[1]
            
            # 标准化路径分隔符
            doc_clean_path = doc_clean_path.replace('\\', '/')
            target_normalized = target_file_path.replace('\\', '/')
            
            # 情况1：完全匹配
            if doc_clean_path == target_normalized:
                return True
            
            # 情况2：去掉数字后缀后匹配
            stripped_doc_path = self._strip_numeric_suffix(doc_clean_path)
            if stripped_doc_path == target_normalized:
                return True
            
            # 情况3：考虑路径可能的变化（如开头的./或结尾的差异）
            doc_clean_path = doc_clean_path.lstrip('./')
            target_normalized = target_normalized.lstrip('./')
            
            if doc_clean_path == target_normalized:
                return True
            
            stripped_doc_path = self._strip_numeric_suffix(doc_clean_path)
            if stripped_doc_path == target_normalized:
                return True
            
            return False
            
        except Exception as e:
            logger.debug(f"判断相同路径重名文件时出错 doc_path={doc_path}, target={target_file_path}: {e}")
            return False

    def _is_duplicate_file(self, doc_path: str, target_filename: str) -> bool:
        """
        判断文档路径是否与目标文件名重名（包括带数字后缀的情况）
        
        Args:
            doc_path: 文档路径（如 "pom.xml(2).md"）
            target_filename: 目标文件名（如 "pom.xml"）
            
        Returns:
            bool: 是否为重名文件
        """
        try:
            # 提取文档的基础名称（去掉.md后缀）
            doc_name = doc_path
            if doc_name.endswith('.md'):
                doc_name = doc_name[:-3]
            
            # 提取目标文件的基础名称
            target_base, target_ext = os.path.splitext(target_filename)
            
            # 情况1：完全匹配
            if doc_name == target_filename:
                return True
            
            # 情况2：去掉数字后缀后匹配
            stripped_doc = self._strip_numeric_suffix(doc_name)
            if stripped_doc == target_filename:
                return True
            
            # 情况3：考虑文件扩展名的匹配
            stripped_doc_base, stripped_doc_ext = os.path.splitext(stripped_doc)
            if stripped_doc_base == target_base and (not target_ext or stripped_doc_ext == target_ext):
                return True
            
            return False
            
        except Exception as e:
            logger.debug(f"判断重名文件时出错 doc_path={doc_path}, target={target_filename}: {e}")
            return False

    def get_doc_id_by_filename(self, filename: str, topic_id: str) -> Optional[List[int]]:
        try:
            def _fetch_docs(query_filename):
                params = {
                    "page": 1,
                    "size": 10,
                    "topic_id": str(topic_id),
                    "keyword": query_filename
                }
                response = self._make_request("GET", "/llmdoc/v1/doc/list/pagination", params=params)
                
                logger.debug(f"topic_id: {topic_id},查询文件名 '{query_filename}' 的响应: {json.dumps(response, ensure_ascii=False)}")
                
                if not response.get("success", False):
                    error_msg = response.get("err", f"topic_id: {topic_id},查询文件名 '{query_filename}',未知错误")
                    logger.error(f"topic_id: {topic_id},查询文档ID失败: {error_msg}")
                    return []
                    
                data = response.get("data", {})
                docs = data.get("list", [])
                return find_best_match(docs, query_filename)
            # 情况1：PNG 文件替换为 .md
            if filename.lower().endswith('.png'):
                query_name = filename[:-4] + '.md'  # 去掉.png，添加.md
                print(f"topic_id: {topic_id},处理PNG文件，尝试查询 '{query_name}'")
                
            # 情况2：MD 文件保持原样
            elif filename.lower().endswith('.md'):
                query_name = filename
                print(f"topic_id: {topic_id},处理MD文件，尝试查询 '{query_name}'")
                
            # 情况3：其他文件添加 .md 后缀
            else:
                query_name = f"{filename}.md"
                print(f"topic_id: {topic_id},处理其他文件，尝试查询 '{query_name}'")
            
            # 执行查询
            matched_docs = _fetch_docs(query_name)
            
            if matched_docs:
                doc_ids = [doc.get("id") for doc in matched_docs]
                print(f"topic_id: {topic_id},找到 {len(doc_ids)} 个匹配文档: {doc_ids}")
                return doc_ids
            else:
                print(f"topic_id: {topic_id},未找到匹配文档: {query_name}")
                return []
                    
        except Exception as e:
            logger.error(f"根据文件名查询文档ID失败: {str(e)}", exc_info=True)
            return None
    
    def get_document_content_by_id(self, doc_id: int, read_format: str = "md") -> Optional[str]:
        """
        根据文档ID查询文档正文内容（增强调试版）
        
        Args:
            doc_id: 文档ID（必须为正整数）
            read_format: 文档格式
            
        Returns:
            文档内容，若失败则返回None
        """
        # 参数验证
        if not isinstance(doc_id, int) or doc_id <= 0:
            logger.error(f"无效的doc_id: {doc_id}，必须为正整数")
            return None
        
        # 打印详细调试信息
        print(f"查询文档内容 - ID: {doc_id}, 格式: {read_format}")
        
        try:
            # 构建请求参数
            endpoint = "/llmdoc/v1/doc/read"
            params = {
                "doc_id": doc_id,
                "read_format": read_format.lower()
            }
            
            
            # 发送请求并记录详细信息
            logger.debug(f"请求参数: {params}")
            response = self._getFileContent("GET", endpoint, params=params)
            return response
                
        except Exception as e:
            logger.error(f"根据文档ID查询内容失败: {str(e)}", exc_info=True)
            return None

def find_best_match(docs, query_filename):
    # 先找完全匹配
    exact_matches = [doc for doc in docs if doc.get("path", "") == query_filename]
    if exact_matches:
        return exact_matches

    # 没有完全匹配，找最相似的
    all_paths = [doc.get("path", "") for doc in docs]
    # get_close_matches 返回最相似的字符串列表
    close = difflib.get_close_matches(query_filename, all_paths, n=1, cutoff=0)
    if close:
        # 返回第一个最相似的文档
        for doc in docs:
            if doc.get("path", "") == close[0]:
                return [doc]
    # 如果都没有，返回空
    return []

if __name__ == "__main__":
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # DocChain服务配置

# 创建一个全局的DocChainManager实例
# 从环境变量或配置文件中读取URL和API密钥
import os
from api.config import configs  # 使用configs而不是get_config

# 获取DocChain配置
docchain_config = configs.get("docchain", {})
docchain_base_url = docchain_config.get("base_url", "http://localhost:7000")
docchain_api_key = docchain_config.get("api_key", "")

# 创建全局实例
docchain_manager = DocChainManager(docchain_base_url, docchain_api_key)
