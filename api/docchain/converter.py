"""
代码转换器

该模块负责将代码转换为Markdown格式，复用utils.convert_code_to_md的功能
"""

import logging
import os
import re
import shutil
import tempfile
from pathlib import Path
from typing import Any, Dict, List, Optional, Set

from api.utils.convert_code_to_md import FileConverter, try_file_operation
from api.config import (
    DEFAULT_CODE_EXTENSIONS,
    DEFAULT_DOCUMENT_EXTENSIONS,
    get_effective_file_filters,
)

# 配置日志
logger = logging.getLogger(__name__)


class CodeConverter:
    """
    代码转换器

    将代码文件转换为Markdown格式，以便上传到DocChain。
    """

    _ALLOWED_DOCCHAIN_CHARS = set("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789._-@#")

    def __init__(self):
        """初始化代码转换器"""
        self.file_converter = FileConverter()

    def _collect_repo_entries(
        self,
        repo_paths: List[str],
        *,
        excluded_dirs: Optional[List[str]] = None,
        excluded_files: Optional[List[str]] = None,
        included_dirs: Optional[List[str]] = None,
        included_files: Optional[List[str]] = None,
        path_namespaces: Optional[Dict[str, str]] = None,
        document_extensions: Optional[Set[str]] = None,
        code_extensions: Optional[Set[str]] = None,
    ) -> List[Dict[str, Any]]:
        entries: List[Dict[str, Any]] = []
        document_exts = set(document_extensions) if document_extensions is not None else set(DEFAULT_DOCUMENT_EXTENSIONS)
        code_exts = set(code_extensions) if code_extensions is not None else set(DEFAULT_CODE_EXTENSIONS)

        for repo_path in repo_paths:
            repo_path_obj = Path(repo_path)
            repo_path_str = str(repo_path_obj)
            namespace = None
            if path_namespaces:
                namespace = path_namespaces.get(repo_path_str)
                if not namespace:
                    try:
                        namespace = path_namespaces.get(str(repo_path_obj.resolve()))
                    except Exception:
                        namespace = None

            repo_prefix_raw = namespace or self._build_repo_prefix(repo_path_obj)
            repo_prefix = self._format_repo_prefix(repo_prefix_raw)

            for root, dirs, files in os.walk(repo_path_str):
                if included_dirs:
                    rel_root = os.path.relpath(root, repo_path_str)
                    if rel_root != '.' and not any(
                        rel_root.startswith(inc_dir) or rel_root == inc_dir
                        for inc_dir in included_dirs
                    ):
                        dirs.clear()
                        continue

                original_dirs = dirs[:]
                dirs[:] = []
                for dir_name in sorted(original_dirs):
                    rel_dir = os.path.relpath(os.path.join(root, dir_name), repo_path_str)
                    if any(rel_dir.startswith(ex_dir) or rel_dir == ex_dir for ex_dir in excluded_dirs or []):
                        logger.debug(f"排除目录: {rel_dir}")
                        continue
                    dirs.append(dir_name)

                for file_name in sorted(files):
                    absolute_path = os.path.join(root, file_name)
                    rel_path = os.path.relpath(absolute_path, repo_path_str)

                    if included_files:
                        should_include = any(
                            rel_path == inc_file or
                            rel_path.startswith(inc_file + os.sep) or
                            file_name == inc_file
                            for inc_file in included_files
                        )
                        if not should_include:
                            continue

                    exclude_file = False
                    for ex_file in excluded_files or []:
                        if ex_file.startswith('*') and file_name.endswith(ex_file[1:]):
                            exclude_file = True
                            break
                        if file_name == ex_file:
                            exclude_file = True
                            break
                        if rel_path == ex_file or rel_path.startswith(ex_file + os.sep):
                            exclude_file = True
                            break
                    if exclude_file:
                        continue

                    file_ext = os.path.splitext(file_name)[1].lower()
                    file_lower = file_name.lower()
                    category: Optional[str] = None
                    # 特殊处理：JSON 文件在 DocChain 端不直接支持，统一转成 Markdown
                    if file_ext == '.json':
                        category = "code"
                    elif file_ext in document_exts:
                        category = "document"
                    elif file_ext in code_exts:
                        category = "code"
                    elif file_lower == "dockerfile" or file_lower.startswith("dockerfile."):
                        category = "code"
                    if not category:
                        continue

                    normalized_rel = rel_path.replace('\\', '/').lstrip('./')
                    base_key = self._normalize_relative_key(normalized_rel)
                    if category == "code":
                        relative_key = f"{base_key}.md" if base_key else ""
                    else:
                        relative_key = base_key
                    relative_without_namespace = relative_key

                    entries.append({
                        "relative_key": relative_key,
                        "docchain_name": self.build_docchain_filename(relative_key),
                        "source_path": absolute_path,
                        "source_relative": normalized_rel,
                        "relative_without_namespace": relative_without_namespace,
                        "category": category,
                        "basename": os.path.basename(normalized_rel),
                        "repo_prefix": repo_prefix,
                    })

        return entries

    def collect_repo_manifest(
        self,
        repo_paths: List[str],
        *,
        excluded_dirs: Optional[List[str]] = None,
        excluded_files: Optional[List[str]] = None,
        included_dirs: Optional[List[str]] = None,
        included_files: Optional[List[str]] = None,
        path_namespaces: Optional[Dict[str, str]] = None,
    ) -> List[Dict[str, Any]]:
        """Return normalized manifest entries for the requested repositories."""
        effective_excluded_dirs, effective_excluded_files = get_effective_file_filters(
            excluded_dirs=excluded_dirs,
            excluded_files=excluded_files,
        )

        return self._collect_repo_entries(
            repo_paths,
            excluded_dirs=effective_excluded_dirs,
            excluded_files=effective_excluded_files,
            included_dirs=included_dirs,
            included_files=included_files,
            path_namespaces=path_namespaces,
        )

    def convert_repo_to_md(self, repo_paths: List[str], excluded_dirs: Optional[List[str]] = None,
                          excluded_files: Optional[List[str]] = None,
                          included_dirs: Optional[List[str]] = None,
                          included_files: Optional[List[str]] = None,
                          path_namespaces: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """
        将仓库中的代码文件转换为Markdown格式，对于已经是文档格式的文件直接复制

        Args:
            repo_path: 仓库路径
            excluded_dirs: 要排除的目录列表
            excluded_files: 要排除的文件列表
            included_dirs: 要包含的目录列表（如果指定，则只处理这些目录）
            included_files: 要包含的文件列表（如果指定，则只处理这些文件）
            path_namespaces: 可选的路径到命名空间映射，覆盖默认的仓库别名前缀

        Returns:
            Dict[str, str]: 文件路径到Markdown文件路径的映射
        """
        effective_excluded_dirs, effective_excluded_files = get_effective_file_filters(
            excluded_dirs=excluded_dirs,
            excluded_files=excluded_files,
        )

        logger.info(f"最终排除的目录: {effective_excluded_dirs}")
        logger.info(f"最终排除的文件: {effective_excluded_files}")
        if included_dirs:
            logger.info(f"包含的目录: {included_dirs}")
        if included_files:
            logger.info(f"包含的文件: {included_files}")

        # 创建临时目录用于存储转换后的Markdown文件
        try:
            temp_dir = tempfile.mkdtemp(prefix="docchain_md_")
            logger.info(f"创建临时目录用于存储Markdown文件: {temp_dir}")
        except Exception as e:
            logger.error(f"创建临时目录失败: {e}")
            return {}

        # 文档格式文件扩展名（不需要转换，直接复制）
        document_extensions = set(DEFAULT_DOCUMENT_EXTENSIONS)
        code_extensions = set(DEFAULT_CODE_EXTENSIONS)

        try:
            result_files: Dict[str, str] = {}
            entries = self._collect_repo_entries(
                repo_paths,
                excluded_dirs=effective_excluded_dirs,
                excluded_files=effective_excluded_files,
                included_dirs=included_dirs,
                included_files=included_files,
                path_namespaces=path_namespaces,
                document_extensions=document_extensions,
                code_extensions=code_extensions,
            )

            code_count = sum(1 for entry in entries if entry["category"] == "code")
            doc_count = sum(1 for entry in entries if entry["category"] == "document")
            logger.info(f"找到 {code_count} 个代码文件需要转换")
            logger.info(f"找到 {doc_count} 个文档文件需要直接复制")

            success_count = 0
            failed_count = 0
            for entry in entries:
                key = entry["relative_key"]
                dest_file = os.path.join(temp_dir, *key.split('/'))
                os.makedirs(os.path.dirname(dest_file), exist_ok=True)

                if entry["category"] == "document":
                    try:
                        shutil.copy2(entry["source_path"], dest_file)
                        if key in result_files:
                            logger.warning(f"检测到重复文件键 {key}，将覆盖已有条目")
                        result_files[key] = dest_file
                        logger.debug(f"直接复制文档文件: {key}")
                    except Exception as exc:
                        failed_count += 1
                        logger.warning(f"复制文档文件失败 {entry['source_path']}: {exc}")
                    continue

                success, content = try_file_operation(entry["source_path"])
                if success:
                    # 将代码文件内容包装在Markdown代码块中
                    markdown_content = self._convert_code_to_markdown(content, entry["source_path"])
                    with open(dest_file, 'w', encoding='utf-8') as handle:
                        handle.write(markdown_content)
                    if key in result_files:
                        logger.warning(f"检测到重复文件键 {key}，将覆盖已有条目")
                    result_files[key] = dest_file
                    success_count += 1
                    logger.debug(f"转换代码文件: {key}")
                else:
                    failed_count += 1
                    logger.warning(f"读取代码文件失败 {entry['source_path']}: {content}")

            if code_count:
                logger.info(
                    f"代码转换统计: 总计 {code_count} 个文件，成功 {success_count} 个，失败 {failed_count} 个"
                )
            logger.info(f"成功处理 {len(result_files)} 个文件")

        except Exception as e:
            logger.error(f"处理文件时出错: {e}")
            return {}

        if not result_files:
            logger.warning("没有成功处理任何文件，请检查仓库内容和文件过滤设置")

        return result_files

    def _build_repo_prefix(self, repo_path: Path) -> str:
        try:
            resolved = repo_path.resolve()
        except Exception:
            resolved = repo_path
        parts = list(resolved.parts)
        if 'repos' in parts:
            idx = parts.index('repos')
            prefix_parts = parts[idx + 1:]
        else:
            prefix_parts = parts[-2:]
        return '/'.join(filter(None, prefix_parts))

    def _format_repo_prefix(self, prefix: Optional[str]) -> str:
        normalized = (prefix or "").replace('\\', '/').strip('/')
        if not normalized:
            normalized = "unknown_repo"
        if not normalized.startswith('@'):
            normalized = f"@{normalized}"
        return normalized

    def _compose_relative_key(self, repo_prefix: str, relative_path: str) -> str:
        normalized = relative_path.replace(os.sep, '/').lstrip('./')
        if repo_prefix:
            return f"{repo_prefix}/{normalized}" if normalized else repo_prefix
        return normalized

    def _normalize_relative_key(self, key: str) -> str:
        return key.replace('//', '/').strip('/')

    def build_docchain_filename(self, key: str) -> str:
        normalized = self._normalize_relative_key(str(key or ""))
        if not normalized:
            return "_"

        builder: List[str] = []
        for ch in normalized.replace('\\', '/'):
            if ch == '/':
                builder.append('__')
                continue
            if ch in self._ALLOWED_DOCCHAIN_CHARS:
                builder.append(ch)
            else:
                builder.append('_')

        sanitized = ''.join(builder)
        sanitized = re.sub(r'__+', '__', sanitized)
        sanitized = sanitized.strip()
        if not sanitized:
            return "_"
        return sanitized

    def convert_file_to_md(self, file_path: str) -> Optional[str]:
        """
        将单个文件转换为Markdown格式

        Args:
            file_path: 文件路径

        Returns:
            Optional[str]: 转换后的Markdown内容，如果转换失败则返回None
        """
        # 检查文件扩展名是否支持
        file_ext = os.path.splitext(file_path)[1].lower()
        if file_ext not in self.supported_extensions:
            logger.warning(f"不支持的文件类型: {file_ext}")
            return None

        # 读取文件内容
        success, content = try_file_operation(file_path)
        if not success:
            logger.error(f"读取文件失败: {content}")
            return None

        # 对于Markdown文件，直接返回内容
        if file_ext == '.md':
            return content

        # 对于其他文件，添加Markdown代码块格式
        language = file_ext[1:]  # 去掉点号
        file_name = os.path.basename(file_path)

        # 构建Markdown内容
        md_content = f"# {file_name}\n\n"
        md_content += f"```{language}\n{content}\n```\n"

        return md_content

    def batch_convert_files(self, file_paths: List[str]) -> Dict[str, str]:
        """
        批量转换文件为Markdown格式

        Args:
            file_paths: 文件路径列表

        Returns:
            Dict[str, str]: 文件路径到Markdown内容的映射
        """
        results = {}
        for file_path in file_paths:
            md_content = self.convert_file_to_md(file_path)
            if md_content:
                results[file_path] = md_content
        return results

    def _convert_code_to_markdown(self, content: str, file_path: str) -> str:
        """
        将代码文件内容转换为Markdown格式

        Args:
            content: 文件内容
            file_path: 文件路径

        Returns:
            str: Markdown格式的内容
        """
        # 获取文件扩展名以确定语言类型
        file_ext = os.path.splitext(file_path)[1].lower()
        filename = os.path.basename(file_path)
        
        # 映射文件扩展名到语言标识符
        language_map = {
            '.py': 'python',
            '.js': 'javascript', 
            '.ts': 'typescript',
            '.tsx': 'tsx',
            '.jsx': 'jsx',
            '.java': 'java',
            '.kt': 'kotlin',
            '.kts': 'kotlin',
            '.scala': 'scala',
            '.go': 'go',
            '.rs': 'rust',
            '.c': 'c',
            '.cpp': 'cpp',
            '.cc': 'cpp',
            '.cxx': 'cpp',
            '.h': 'c',
            '.hpp': 'cpp',
            '.cs': 'csharp',
            '.php': 'php',
            '.rb': 'ruby',
            '.pl': 'perl',
            '.pm': 'perl',
            '.swift': 'swift',
            '.dart': 'dart',
            '.lua': 'lua',
            '.sh': 'bash',
            '.bash': 'bash',
            '.zsh': 'zsh',
            '.fish': 'fish',
            '.ps1': 'powershell',
            '.bat': 'batch',
            '.cmd': 'batch',
            '.sql': 'sql',
            '.xml': 'xml',
            '.json': 'json',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.toml': 'toml',
            '.ini': 'ini',
            '.cfg': 'ini',
            '.conf': 'ini',
            '.properties': 'properties',
            '.gradle': 'gradle',
            '.groovy': 'groovy',
            '.sbt': 'scala',
            '.css': 'css',
            '.scss': 'scss',
            '.sass': 'sass',
            '.less': 'less',
            '.html': 'html',
            '.htm': 'html',
            '.vue': 'vue',
            '.svelte': 'svelte',
            '.dockerfile': 'dockerfile'
        }
        
        # 获取语言标识符
        language = language_map.get(file_ext, '')
        if not language and filename.lower() in ['dockerfile', 'makefile', 'rakefile']:
            language = filename.lower()
        
        # 构建Markdown内容
        md_content = f"# {filename}\n\n"
        md_content += f"```{language}\n{content}\n```\n"
        
        return md_content 
