import asyncio
from datetime import datetime, timedelta
import uvicorn
import os
import sys
import logging
from dotenv import load_dotenv
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.concurrency import asynccontextmanager
# Include tag API router
from api.cache.redis.manager import redis_manager
from api.middleware.app_auth_middleware import AppAuthMiddleware
from api.middleware.user_auth_middleware import UserAuthMiddleware
from api.router.app_router import app_router
from api.router.user_access_token_router import user_access_token_router
from api.router.zcm_router import zcm_router
from api.router.config_router import config_router
from api.router.auth_router import auth_router
from api.router.tag_router import tag_router
from api.router.job_router import job_router
from api.router.filesearch_router import filesearch_router
from api.router.directory_cache_router import directory_cache_router
from api.router.meta_prompts_router import meta_router
from api.router.k8s_job_pool_router import k8s_job_pool_router
# Load environment variables from .env file
load_dotenv()

from api.logging_config import setup_logging

# Configure logging
setup_logging()
logger = logging.getLogger(__name__)

# Add the current directory to the path so we can import the api package
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Check for required environment variables
required_env_vars = ['GOOGLE_API_KEY', 'OPENAI_API_KEY']
missing_vars = [var for var in required_env_vars if not os.environ.get(var)]
if missing_vars:
    logger.warning(f"Missing environment variables: {', '.join(missing_vars)}")
    logger.warning("Some functionality may not work correctly without these variables.")

# Check Langfuse setup
try:
    from api.openai_client import LANGFUSE_SDK_ACTIVE
    logger.info(f"Langfuse SDK Active: {LANGFUSE_SDK_ACTIVE}")
    if not LANGFUSE_SDK_ACTIVE:
        logger.warning("Langfuse SDK not available - tracing disabled. Install with: pip install langfuse")
except Exception as e:
    logger.warning(f"Failed to check Langfuse SDK: {e}")
    LANGFUSE_SDK_ACTIVE = False

# Configure Google Generative AI
import google.generativeai as genai
from api.config import GOOGLE_API_KEY, configs, CLIENT_CLASSES, is_redis_enabled
from api.wiki.sync_worker_service import get_sync_worker_service

if GOOGLE_API_KEY:
    genai.configure(api_key=GOOGLE_API_KEY)
else:
    logger.warning("GOOGLE_API_KEY not configured")

# 导入GeminiCliClient并添加到CLIENT_CLASSES
try:
    from api.gemini_cli_client import GeminiCliClient
    CLIENT_CLASSES["GeminiCliClient"] = GeminiCliClient
    logger.info("GeminiCliClient successfully registered")
except ImportError as e:
    logger.warning(f"Failed to import GeminiCliClient: {e}")

# 添加Gemini CLI配置
configs["gemini_cli"] = {
    "api_base_url": os.environ.get("GEMINI_CLI_API_BASE", "http://localhost:8000"),
    "enabled": True,
    "add_project_context": os.environ.get("GEMINI_CLI_ADD_PROJECT_CONTEXT", "true").lower() == "true"
}

async def clear_wiki_qa_context_periodically(app: FastAPI, interval: int = 60 * 10):
    """
    当没有启用redis时，wiki问答的session暂时存储在应用的全局变量里，定时清理长时间没有访问的session
    """
    while True:
        await asyncio.sleep(interval)
        logger.info("Clear wiki_qa_context")
        wiki_qa_context = app.state.wiki_qa_context
        discard_keys = []
        now = datetime.now()
        for key, value in wiki_qa_context.items():
            if now - value["last_access_time"] > timedelta(minutes=30):
                discard_keys.append(key)
        for key in discard_keys:
            logger.info(f"Clear wiki_qa_context key {key}")
            wiki_qa_context.pop(key, None)

@asynccontextmanager
async def lifespan(app: FastAPI):
    clear_wiki_qa_context_task = None
    if not is_redis_enabled():
        # 初始化wiki问答上下文
        app.state.wiki_qa_context = {}
        clear_wiki_qa_context_task = asyncio.create_task(clear_wiki_qa_context_periodically(app))

    # 初始化HTTP session
    import aiohttp
    session = aiohttp.ClientSession()
    app.state.session = session

    # 统一加载所有本地缓存
    from api.cache.local.cache_loader import load_all_caches
    load_all_caches()

    # 移动Gemini配置文件 已经挂载到主机下 后面直接修改主机中的该文件内容即可 
    # from api.cache.local.copy_gemini_config import copy_gemini_config
    # copy_gemini_config()
    
    # 初始化WikiJobManager
    from api.wiki.wiki_job_manager import init_job_manager, start_job_manager, stop_job_manager, get_job_manager
    from api.database.base import db_service
    from api.docchain.client import docchain_manager
    from api.wiki.docker_graceful_shutdown import setup_docker_graceful_shutdown

    sync_worker_service = get_sync_worker_service()
    if sync_worker_service.is_enabled():
        logger.info("Sync worker forwarding enabled at %s", sync_worker_service.base_url)
    else:
        logger.info("Sync worker forwarding disabled; falling back to in-process sync")
    
    # 初始化JobManager（配置将从settings.yaml中读取）
    job_manager = init_job_manager(db_service, docchain_manager, lock_timeout_minutes=30)
    
    # 启动JobManager
    await start_job_manager()
    logger.info("WikiJobManager已启动")
    
    # 设置Docker优雅停止机制
    try:
        shutdown_handler = setup_docker_graceful_shutdown(job_manager)
        logger.info("Docker优雅停止机制已启用")
    except Exception as e:
        logger.warning(f"设置Docker优雅停止机制失败: {e}")
    
    # 启动时 - 诊断Langfuse状态
    try:
        from api.langfuse_utils import get_langfuse_context
        langfuse_ctx = get_langfuse_context()
        if langfuse_ctx.get("enabled"):
            logger.info("✓ Langfuse tracing is ENABLED and ready")
        else:
            logger.warning(
                "⚠ Langfuse SDK loaded but tracing is DISABLED. "
                "Check environment variables: LANGFUSE_PUBLIC_KEY, LANGFUSE_SECRET_KEY"
            )
    except Exception as e:
        logger.debug(f"Langfuse context check: {e}")

    logger.info(f"******** 初始化Redis *********")
    await redis_manager.startup()
    
    # 清空 JobPoolService 的所有 Redis keys
    # try:
    #     from api.service.job_pool_service import get_job_pool_service
    #     job_pool_service = get_job_pool_service()
    #     success, message = job_pool_service.clear_all_pool_data()
    #     if success:
    #         logger.info(f"JobPoolService 数据清空成功: {message}")
    #     else:
    #         logger.warning(f"JobPoolService 数据清空失败: {message}")
    # except Exception as e:
    #     logger.error(f"清空 JobPoolService 数据时发生异常: {e}")

    yield
    
    # 关闭时停止JobManager
    await stop_job_manager()
    logger.info("WikiJobManager已停止")
    
    # 关闭HTTP session
    await session.close()
    
     # 关闭时
    logger.info(f"******** 关闭Redis *********")
    await redis_manager.shutdown()

    # 关闭清理wiki问答上下文定时任务
    if clear_wiki_qa_context_task:
        clear_wiki_qa_context_task.cancel()
        try:
            await clear_wiki_qa_context_task
        except asyncio.CancelledError as error:
            logger.error(f"Failed to cancel clear_wiki_qa_context_task, cause by: {error}")

# Create FastAPI app
app = FastAPI(
    title="DeepWiki API",
    description="API for the DeepWiki application",
    version="1.0.0",
    lifespan=lifespan
)

# Include K8s API router first (before mounting catch-all)
from api.router.k8s_router import k8s_router
# 注册中间件，顺序为auth->role->audit_log
from api.middleware.auth_middleware import AuthMiddleware
from api.middleware.role_middleware import RoleMiddleware
from api.middleware.audit_log_middleware import AuditLogMiddleware
from api.middleware.language_middleware import LanguageMiddleware
from api.router.cache_router import cache_router
from api.router.open_router import open_router

app.add_middleware(AuditLogMiddleware)
app.add_middleware(RoleMiddleware)
app.add_middleware(AuthMiddleware)
app.add_middleware(UserAuthMiddleware)
app.add_middleware(AppAuthMiddleware)
app.add_middleware(LanguageMiddleware)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)

app.include_router(k8s_router)
app.include_router(tag_router)
app.include_router(auth_router)
app.include_router(config_router)
app.include_router(job_router)
app.include_router(zcm_router)
app.include_router(cache_router)
app.include_router(filesearch_router)
app.include_router(directory_cache_router)
app.include_router(app_router)
app.include_router(user_access_token_router)
app.include_router(open_router)
app.include_router(meta_router)
app.include_router(k8s_job_pool_router)

# 注册ai_dw_priv_cache路由
from api.router.cache_router import cache_router as ai_dw_priv_cache_router
app.include_router(ai_dw_priv_cache_router)

# 注册测试路由
# app.include_router(example.router, prefix="/api/v1", tags=["example"])

# Mount API app (catch-all, should be last)
from api.api import app as api_app

app.mount("/", api_app)

if __name__ == "__main__":
    # Run uvicorn server
    uvicorn.run(app, host="0.0.0.0", port=int(os.environ.get("PORT", 8001)))
