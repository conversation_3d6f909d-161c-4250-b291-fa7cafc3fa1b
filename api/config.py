import os
import json
import logging
import re
from starlette.requests import Request
import yaml
from pathlib import Path
from typing import Iterable, List, Union, Dict, Any, Optional, Tuple

logger = logging.getLogger(__name__)

from api.openai_client import Open<PERSON><PERSON>lient
from api.openrouter_client import <PERSON><PERSON><PERSON>er<PERSON><PERSON>
from api.bedrock_client import <PERSON><PERSON><PERSON><PERSON>
from adalflow import GoogleGenAIClient, OllamaClient
from api.gemini_cli_client import GeminiCliClient
from api.type.database import DatabaseSettings

# Get API keys from environment variables
OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY')
if OPENAI_API_KEY:
    OPENAI_API_KEY = OPENAI_API_KEY.replace('●', '')

GOOGLE_API_KEY = os.environ.get('GOOGLE_API_KEY')
if GOOGLE_API_KEY:
    GOOGLE_API_KEY = GOOGLE_API_KEY.replace('●', '')

OPENROUTER_API_KEY = os.environ.get('OPENROUTER_API_KEY')
if OPENROUTER_API_KEY:
    OPENROUTER_API_KEY = OPENROUTER_API_KEY.replace('●', '')
AWS_ACCESS_KEY_ID = os.environ.get('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.environ.get('AWS_SECRET_ACCESS_KEY')
AWS_REGION = os.environ.get('AWS_REGION')
AWS_ROLE_ARN = os.environ.get('AWS_ROLE_ARN')
GEMINI_CLI_API_BASE = os.environ.get('GEMINI_CLI_API_BASE')

# Set keys in environment (in case they're needed elsewhere in the code)
if OPENAI_API_KEY:
    os.environ["OPENAI_API_KEY"] = OPENAI_API_KEY
if GOOGLE_API_KEY:
    os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY
if OPENROUTER_API_KEY:
    os.environ["OPENROUTER_API_KEY"] = OPENROUTER_API_KEY
if AWS_ACCESS_KEY_ID:
    os.environ["AWS_ACCESS_KEY_ID"] = AWS_ACCESS_KEY_ID
if AWS_SECRET_ACCESS_KEY:
    os.environ["AWS_SECRET_ACCESS_KEY"] = AWS_SECRET_ACCESS_KEY
if AWS_REGION:
    os.environ["AWS_REGION"] = AWS_REGION
if AWS_ROLE_ARN:
    os.environ["AWS_ROLE_ARN"] = AWS_ROLE_ARN
if GEMINI_CLI_API_BASE:
    os.environ["GEMINI_CLI_API_BASE"] = GEMINI_CLI_API_BASE

# Wiki authentication settings
raw_auth_mode = os.environ.get('DEEPWIKI_AUTH_MODE', 'False')
WIKI_AUTH_MODE = raw_auth_mode.lower() in ['true', '1', 't']
WIKI_AUTH_CODE = os.environ.get('DEEPWIKI_AUTH_CODE', '')

# Get configuration directory from environment variable, or use default if not set
CONFIG_DIR = os.environ.get('DEEPWIKI_CONFIG_DIR', None)

# Client class mapping
CLIENT_CLASSES = {
    "GoogleGenAIClient": GoogleGenAIClient,
    "OpenAIClient": OpenAIClient,
    "OpenRouterClient": OpenRouterClient,
    "OllamaClient": OllamaClient,
    "BedrockClient": BedrockClient,
    "GeminiCliClient": GeminiCliClient
}

# 函数特殊处理gemini-cli配置
def get_gemini_cli_config(model=None):
    """
    获取gemini-cli配置
    
    Parameters:
        model (str): 模型名称，默认为"gemini-cli"
        
    Returns:
        dict: 配置包含model_kwargs和其他参数
    """
    # 从环境变量获取API基础URL
    api_base_url = os.environ.get("GEMINI_CLI_API_BASE", "http://localhost:8000")
    
    return {
        "model_client": None,  # 会在运行时由main.py处理
        "model_kwargs": {
            "model": model or "gemini-cli",
            "api_base_url": api_base_url,
            "temperature": 0.7,
            "top_p": 0.95
        }
    }

def replace_env_placeholders(config: Union[Dict[str, Any], List[Any], str, Any]) -> Union[Dict[str, Any], List[Any], str, Any]:
    """
    Recursively replace placeholders like "${ENV_VAR}" in string values
    within a nested configuration structure (dicts, lists, strings)
    with environment variable values. Logs a warning if a placeholder is not found.
    """
    pattern = re.compile(r"\$\{([A-Z0-9_]+)\}")

    def replacer(match: re.Match[str]) -> str:
        env_var_name = match.group(1)
        original_placeholder = match.group(0)
        env_var_value = os.environ.get(env_var_name)
        if env_var_value is None:
            logger.warning(
                f"Environment variable placeholder '{original_placeholder}' was not found in the environment. "
                f"The placeholder string will be used as is."
            )
            return original_placeholder
        return env_var_value

    if isinstance(config, dict):
        return {k: replace_env_placeholders(v) for k, v in config.items()}
    elif isinstance(config, list):
        return [replace_env_placeholders(item) for item in config]
    elif isinstance(config, str):
        return pattern.sub(replacer, config)
    else:
        # Handles numbers, booleans, None, etc.
        return config

# Load JSON configuration file
def load_json_config(filename):
    try:
        # If environment variable is set, use the directory specified by it
        if CONFIG_DIR:
            config_path = Path(CONFIG_DIR) / filename
        else:
            # Otherwise use default directory
            config_path = Path(__file__).parent / "config" / filename

        logger.info(f"Loading configuration from {config_path}")

        if not config_path.exists():
            logger.warning(f"Configuration file {config_path} does not exist")
            return {}

        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
            config = replace_env_placeholders(config)
            return config
    except Exception as e:
        logger.error(f"Error loading configuration file {filename}: {str(e)}")
        return {}

# 加载YAML配置文件
def load_yaml_config(filename):
    try:
        # 默认使用与config.py相同的目录
        config_path = Path(__file__).parent / filename
        logger.info(f"Loading YAML configuration from {config_path}")

        if not config_path.exists():
            logger.warning(f"Configuration file {config_path} does not exist")
            return {}

        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            return config
    except Exception as e:
        logger.error(f"Error loading YAML configuration file {filename}: {str(e)}")
        return {}

def get_wiki_jobs_config():
    """获取Wiki任务管理配置"""
    settings = settings_config; 
    if not settings:
        settings = load_yaml_config('settings.yaml')
    wiki_jobs_config = settings.get('wiki_jobs', {})
    
    # 设置默认值
    return {
        'global_max_concurrent': wiki_jobs_config.get('global_max_concurrent', 6),
        'instance_max_concurrent': wiki_jobs_config.get('instance_max_concurrent', 2),
        'timeout_minutes': wiki_jobs_config.get('timeout_minutes', 180),
        'lock_timeout_minutes': wiki_jobs_config.get('lock_timeout_minutes', 90),
        'heartbeat_timeout_minutes': wiki_jobs_config.get('heartbeat_timeout_minutes', 5),
        'cleanup_interval_seconds': wiki_jobs_config.get('cleanup_interval_seconds', 300),
        'fast_mode': wiki_jobs_config.get('fast_mode', True)
    }

# Load generator model configuration
def load_generator_config():
    generator_config = load_json_config("generator.json")

    # Add client classes to each provider
    if "providers" in generator_config:
        for provider_id, provider_config in generator_config["providers"].items():
            # Try to set client class from client_class
            if provider_config.get("client_class") in CLIENT_CLASSES:
                provider_config["model_client"] = CLIENT_CLASSES[provider_config["client_class"]]
            # Fall back to default mapping based on provider_id
            elif provider_id in ["google", "openai", "openrouter", "ollama", "bedrock"]:
                default_map = {
                    "google": GoogleGenAIClient,
                    "openai": OpenAIClient,
                    "openrouter": OpenRouterClient,
                    "ollama": OllamaClient,
                    "bedrock": BedrockClient,
                    "gemini-cli": GeminiCliClient
                }
                provider_config["model_client"] = default_map[provider_id]
            else:
                logger.warning(f"Unknown provider or client class: {provider_id}")

    return generator_config


def get_sync_worker_base_url(default: Optional[str] = None) -> Optional[str]:
    """获取同步Worker的基础URL，优先读取环境变量。"""
    env_value = os.environ.get("SYNC_WORKER_BASE_URL")
    if env_value:
        return env_value.rstrip('/')
    base = None
    if settings_config:
        base = settings_config.get("sync_worker", {}).get("base_url")
    if base:
        return str(base).rstrip('/')
    if default:
        return str(default).rstrip('/')
    return None

# Load embedder configuration
def load_embedder_config():
    embedder_config = load_json_config("embedder.json")

    # Process client classes
    for key in ["embedder", "embedder_ollama"]:
        if key in embedder_config and "client_class" in embedder_config[key]:
            class_name = embedder_config[key]["client_class"]
            if class_name in CLIENT_CLASSES:
                embedder_config[key]["model_client"] = CLIENT_CLASSES[class_name]

    return embedder_config

def get_embedder_config():
    """
    Get the current embedder configuration.
    Returns:
        dict: The embedder configuration with model_client resolved
    """
    return configs.get("embedder", {})

def is_ollama_embedder():
    """
    Check if the current embedder configuration uses OllamaClient.
    Returns:
        bool: True if using OllamaClient, False otherwise
    """
    embedder_config = get_embedder_config()
    if not embedder_config:
        return False

    # Check if model_client is OllamaClient
    model_client = embedder_config.get("model_client")
    if model_client:
        return model_client.__name__ == "OllamaClient"

    # Fallback: check client_class string
    client_class = embedder_config.get("client_class", "")
    return client_class == "OllamaClient"

# Load repository and file filters configuration
def load_repo_config():
    return load_json_config("repo.json")

# Load language configuration
def load_lang_config():
    default_config = {
        "supported_languages": {
            "en": "English",
            "ja": "Japanese (日本語)",
            "zh": "Mandarin Chinese (中文)",
            "zh-tw": "Traditional Chinese (繁體中文)",
            "es": "Spanish (Español)",
            "kr": "Korean (한국어)",
            "vi": "Vietnamese (Tiếng Việt)"
        },
        "default": "en"
    }

    loaded_config = load_json_config("lang.json") # Let load_json_config handle path and loading

    if not loaded_config:
        return default_config

    if "supported_languages" not in loaded_config or "default" not in loaded_config:
        logger.warning("Language configuration file 'lang.json' is malformed. Using default language configuration.")
        return default_config

    return loaded_config

# Default excluded directories and files
DEFAULT_EXCLUDED_DIRS: List[str] = [
    # Virtual environments and package managers
    "./.venv/", "./venv/", "./env/", "./virtualenv/",
    "./node_modules/", "./bower_components/", "./jspm_packages/",
    # Version control
    "./.git/", "./.svn/", "./.hg/", "./.bzr/",
    # Cache and compiled files
    "./__pycache__/", "./.pytest_cache/", "./.mypy_cache/", "./.ruff_cache/", "./.coverage/",
    # Build and distribution
    "./dist/", "./build/", "./out/", "./target/", "./bin/", "./obj/",
    # Documentation
    "./docs/", "./_docs/", "./site-docs/", "./_site/",
    # IDE specific
    "./.idea/", "./.vscode/", "./.vs/", "./.eclipse/", "./.settings/",
    # Logs and temporary files
    "./logs/", "./log/", "./tmp/", "./temp/",
]

DEFAULT_EXCLUDED_FILES: List[str] = [
    "yarn.lock", "pnpm-lock.yaml", "npm-shrinkwrap.json", "poetry.lock",
    "Pipfile.lock", "requirements.txt.lock", "Cargo.lock", "composer.lock",
    ".lock", ".DS_Store", "Thumbs.db", "desktop.ini", "*.lnk", ".env",
    ".env.*", "*.env", "*.cfg", "*.ini", ".flaskenv", ".gitignore",
    ".gitattributes", ".gitmodules", ".github", ".gitlab-ci.yml",
    ".prettierrc", ".eslintrc", ".eslintignore", ".stylelintrc",
    ".editorconfig", ".jshintrc", ".pylintrc", ".flake8", "mypy.ini",
    "pyproject.toml", "tsconfig.json", "webpack.config.js", "babel.config.js",
    "rollup.config.js", "jest.config.js", "karma.conf.js", "vite.config.js",
    "next.config.js", "*.min.js", "*.min.css", "*.bundle.js", "*.bundle.css",
    "*.map", "*.gz", "*.zip", "*.tar", "*.tgz", "*.rar", "*.7z", "*.iso",
    "*.dmg", "*.img", "*.msix", "*.appx", "*.appxbundle", "*.xap", "*.ipa",
    "*.deb", "*.rpm", "*.msi", "*.exe", "*.dll", "*.so", "*.dylib", "*.o",
    "*.obj", "*.jar", "*.war", "*.ear", "*.jsm", "*.class", "*.pyc", "*.pyd",
    "*.pyo", "__pycache__", "*.a", "*.lib", "*.lo", "*.la", "*.slo", "*.dSYM",
    "*.egg", "*.egg-info", "*.dist-info", "*.eggs", "node_modules",
    "bower_components", "jspm_packages", "lib-cov", "coverage", "htmlcov",
    ".nyc_output", ".tox", "dist", "build", "bld", "out", "bin", "target",
    "packages/*/dist", "packages/*/build", ".output", "*.log"
]

DEFAULT_CODE_EXTENSIONS: List[str] = [
    ".py", ".js", ".ts", ".tsx", ".jsx", ".java", ".kt", ".kts", ".scala", ".go", ".rs",
    ".c", ".cpp", ".cc", ".cxx", ".h", ".hpp", ".hh", ".m", ".mm", ".cs", ".php",
    ".rb", ".pl", ".pm", ".swift", ".dart", ".lua", ".sh", ".bash", ".zsh",
    ".fish", ".ps1", ".bat", ".cmd", ".sql", ".xml", ".json", ".yaml", ".yml",
    ".toml", ".ini", ".cfg", ".conf", ".properties", ".gradle", ".groovy", ".sbt",
    ".css", ".scss", ".sass", ".less", ".styl", ".html", ".htm", ".xhtml", ".vue",
    ".svelte", ".mdx", ".hbs", ".mustache", ".ejs", ".mjml", ".arb", ".rspec",
    ".dockerfile"
]

DEFAULT_DOCUMENT_EXTENSIONS: List[str] = [
    ".md", ".markdown", ".mdown", ".mkd", ".rst", ".adoc", ".txt", ".csv",
    ".doc", ".docx", ".pdf", ".rtf", ".ppt", ".pptx", ".xls", ".xlsx"
]


def _merge_filter_lists(*lists: Optional[Iterable[str]]) -> List[str]:
    merged: List[str] = []
    seen: set[str] = set()
    for items in lists:
        if not items:
            continue
        for value in items:
            if value is None:
                continue
            if value not in seen:
                merged.append(value)
                seen.add(value)
    return merged


def get_effective_file_filters(
    excluded_dirs: Optional[Iterable[str]] = None,
    excluded_files: Optional[Iterable[str]] = None,
    include_defaults: bool = True,
) -> Tuple[List[str], List[str]]:
    """Merge default, configured, and caller-provided file filters.

    Args:
        excluded_dirs: Additional directory patterns to exclude.
        excluded_files: Additional file patterns to exclude.
        include_defaults: Whether to include library defaults when building the lists.

    Returns:
        A tuple ``(excluded_dirs, excluded_files)`` with duplicates removed while preserving
        the overall precedence order: defaults → repo.json → caller overrides.
    """

    config_filters = configs.get("file_filters", {}) if configs else {}
    default_dirs = DEFAULT_EXCLUDED_DIRS if include_defaults else []
    default_files = DEFAULT_EXCLUDED_FILES if include_defaults else []

    merged_dirs = _merge_filter_lists(
        default_dirs,
        config_filters.get("excluded_dirs"),
        excluded_dirs,
    )
    merged_files = _merge_filter_lists(
        default_files,
        config_filters.get("excluded_files"),
        excluded_files,
    )

    return merged_dirs, merged_files

# Initialize empty configuration
configs = {}

# Load all configuration files
generator_config = load_generator_config()
embedder_config = load_embedder_config()
repo_config = load_repo_config()
lang_config = load_lang_config()
settings_config = load_yaml_config("settings.yaml")

# Update configuration
if generator_config:
    configs["default_provider"] = generator_config.get("default_provider", "google")
    configs["providers"] = generator_config.get("providers", {})

# Update embedder configuration
if embedder_config:
    for key in ["embedder", "embedder_ollama", "retriever", "text_splitter"]:
        if key in embedder_config:
            configs[key] = embedder_config[key]

# Update repository configuration
if repo_config:
    for key in ["file_filters", "repository"]:
        if key in repo_config:
            configs[key] = repo_config[key]

# 添加数据库和JWT配置
if settings_config:
    configs["database"] = settings_config.get("database", {})
    configs["sso"] = settings_config.get("sso", {})
    configs["session"] = settings_config.get("session", {})
    configs["app"] = settings_config.get("app", {})
    configs["kubernetes"] = settings_config.get("kubernetes", {})
    configs["global_config"] = settings_config.get("global_config", {})
    configs["redis"] = settings_config.get('redis', {})
    # 新增认证模式配置
    configs["auth_mode"] = settings_config.get("auth_mode", None)
    configs["file_manager"] = settings_config.get("file_manager", {})
    configs["input_template"] = settings_config.get("input_template", {})
    configs["sync_worker"] = settings_config.get("sync_worker", {})
    configs["acl_version"] = settings_config.get("acl_version", "nfs4-acl-tools")
    langfuse_settings = settings_config.get("langfuse", {}) if settings_config else {}
    if langfuse_settings:
        trace_env_override = os.environ.get("LANGFUSE_TRACING_ENVIRONMENT")
        env_override = os.environ.get("LANGFUSE_ENVIRONMENT")
        current_environment = (
            trace_env_override
            or env_override
            or langfuse_settings.get("environment", "local")
        )

        if current_environment and not os.environ.get("LANGFUSE_TRACING_ENVIRONMENT"):
            os.environ["LANGFUSE_TRACING_ENVIRONMENT"] = str(current_environment)

        def _to_bool(value: Any, default: bool = True) -> bool:
            if value is None:
                return default
            if isinstance(value, bool):
                return value
            if isinstance(value, str):
                return value.strip().lower() in {"1", "true", "yes", "on"}
            return bool(value)

        env_enabled_override = os.environ.get("LANGFUSE_ENABLED")
        if env_enabled_override is not None:
            langfuse_enabled = _to_bool(env_enabled_override, default=True)
        else:
            langfuse_enabled = _to_bool(langfuse_settings.get("enabled", True))

        def _is_placeholder(value: Any) -> bool:
            return isinstance(value, str) and value.startswith("${") and value.endswith("}")

        host = langfuse_settings.get("host")
        public_key = langfuse_settings.get("public_key")
        secret_key = langfuse_settings.get("secret_key")

        if langfuse_enabled:
            if host and not os.environ.get("LANGFUSE_HOST") and not _is_placeholder(host):
                os.environ["LANGFUSE_HOST"] = str(host)
            if public_key and not os.environ.get("LANGFUSE_PUBLIC_KEY") and not _is_placeholder(public_key):
                os.environ["LANGFUSE_PUBLIC_KEY"] = str(public_key)
            if secret_key and not os.environ.get("LANGFUSE_SECRET_KEY") and not _is_placeholder(secret_key):
                os.environ["LANGFUSE_SECRET_KEY"] = str(secret_key)

        configs["langfuse"] = {
            "environment": current_environment,
            "enabled": langfuse_enabled,
            "host": host,
            "public_key": public_key,
            "secret_key": secret_key,
        }

# 添加DocChain配置
docchain_settings = settings_config.get("docchain", {}) if settings_config else {}

def _resolve_bool(env_var: str, setting_key: str, default: bool = False) -> bool:
    env_value = os.environ.get(env_var)
    if env_value is not None:
        return str(env_value).strip().lower() in {"1", "true", "yes", "on"}

    setting_value = docchain_settings.get(setting_key)
    if setting_value is None:
        return default
    if isinstance(setting_value, bool):
        return setting_value
    if isinstance(setting_value, str):
        return setting_value.strip().lower() in {"1", "true", "yes", "on"}
    return bool(setting_value)

configs["docchain"] = {
    "base_url": os.environ.get("DOCCHAIN_BASE_URL") or docchain_settings.get("base_url", "http://localhost:7000"),
    "api_key": os.environ.get("DOCCHAIN_API_KEY") or docchain_settings.get("api_key", ""),
    "enabled": _resolve_bool("USE_DOCCHAIN", "enabled", default=False),
    "direct_mode": _resolve_bool("DOCCHAIN_DIRECT_MODE", "direct_mode", default=False),
    "lab_base_url": os.environ.get("LAB_DOCCHAIN_BASE_URL") or docchain_settings.get("lab_base_url", "http://localhost:7000"),
    "lab_api_key": os.environ.get("LAB_DOCCHAIN_API_KEY") or docchain_settings.get("lab_api_key", ""),
    "lab_direct_mode": _resolve_bool("LAB_DOCCHAIN_DIRECT_MODE", "lab_direct_mode", default=False),
    "async_upload_on_wiki_generation": _resolve_bool(
        "DOCCHAIN_ASYNC_UPLOAD_ON_WIKI_GENERATION",
        "async_upload_on_wiki_generation",
        default=True,
    ),
}

# Update language configuration
if lang_config:
    configs["lang_config"] = lang_config

def get_model_config(provider="google", model=None):
    """
    Get configuration for the specified provider and model

    Parameters:
        provider (str): Model provider ('google', 'openai', 'openrouter', 'ollama', 'bedrock', 'gemini-cli')
        model (str): Model name, or None to use default model

    Returns:
        dict: Configuration containing model_client, model and other parameters
    """
    # Handle gemini-cli special case
    if provider == "gemini-cli":
        return get_gemini_cli_config(model)
    
    # Get provider configuration
    if "providers" not in configs:
        raise ValueError("Provider configuration not loaded")

    provider_config = configs["providers"].get(provider)
    if not provider_config:
        raise ValueError(f"Configuration for provider '{provider}' not found")

    model_client = provider_config.get("model_client")
    if not model_client:
        raise ValueError(f"Model client not specified for provider '{provider}'")
    
    # If model not provided, use default model for the provider
    if not model:
        model = provider_config.get("default_model")
        if not model:
            raise ValueError(f"No default model specified for provider '{provider}'")

    # Get model parameters (if present)
    model_params = {}
    if model in provider_config.get("models", {}):
        model_params = provider_config["models"][model]
    else:
        default_model = provider_config.get("default_model")
        model_params = provider_config["models"][default_model]

    # Prepare base configuration
    result = {
        "model_client": model_client,
    }

    # Provider-specific adjustments
    if provider == "ollama":
        # Ollama uses a slightly different parameter structure
        if "options" in model_params:
            result["model_kwargs"] = {"model": model, **model_params["options"]}
        else:
            result["model_kwargs"] = {"model": model}
    else:
        # Standard structure for other providers
        result["model_kwargs"] = {"model": model, **model_params}

    return result

def get_database_config() -> DatabaseSettings:
    """
    获取数据库配置
    
    Returns:
        DatabaseSettings: 数据库配置
    """
    db_config = configs.get("database", {})
    return DatabaseSettings(**db_config)

def get_config() -> Dict[str, Any]:
    """
    获取全量配置
    """
    return configs

def get_redis_config() -> Dict[str, Any]:
    """
    获取全量配置
    """
    return configs.get("redis", {})

def is_redis_enabled() -> bool:
    return get_redis_config().get("enabled", False)

def get_sso_config() -> Dict[str, str]:
    """
    获取SSO配置
    
    Returns:
        Dict[str, str]: SSO配置，包含app_key和app_secret
    """
    sso_config = configs.get("sso", {})
    return {
        "app_key": sso_config.get("app_key", ""),
        "app_secret": sso_config.get("app_secret", ""),
        "base_url": sso_config.get("base_url", "")
    }

def get_session_config() -> Dict[str, int]:
    """
    获取session配置
    
    Returns:
        Dict[str, str]: session配置，包含 max-age
    """
    return {
        "max-age":configs.get("session", {}).get("cookie", {}).get("max-age", -1),
        "timeout":configs.get("session", {}).get("timeout", 3600)
    }

def get_developMode() -> bool:
    """
    获取developMode配置
    
    Returns:
        bool: developMode配置
    """
    return configs.get("app", {}).get("security", {}).get("developMode", False)

def get_excludeUrl() -> dict[str, str]:
    """
    获取excludeUrl配置
    
    Returns:
        List[str]: excludeUrl配置
    """
    return configs.get("app", {}).get("security", {}).get("excludeUrl", {})

def get_kubernetes_config() -> Dict[str, Any]:
    """
    获取Kubernetes配置
    
    Returns:
        Dict[str, Any]: Kubernetes配置
    """
    return configs.get("kubernetes", {})

def get_enable_role_service_privilege() -> bool:
    """
    获取是否开启角色服务权限校验配置
    Returns:
        bool: True表示开启服务权限校验，False表示直接放行
    """
    return configs.get("app", {}).get("security", {}).get("enable_role_service_privilege", True)

def get_zcm_devspace_config():
    """获取研发云API的配置"""
    settings = {}
    base_url = os.environ.get("ZCM_DEVSPACE_BASE_URL")
    token = os.environ.get("ZCM_DEVSPACE_TOKEN")
    if not base_url or not token:
        settings = settings_config
        if not settings:
            settings = load_yaml_config('settings.yaml')
        base_url = settings_config.get("zcm", {}).get("devspace", {}).get("base_url", "")
        token = settings_config.get("zcm", {}).get("devspace", {}).get("token", "")
    return base_url, token

from api.common.constants import AuthMode

def get_acl_version() -> str:
    """
    获取acl版本
    Returns:
        str: acl版本
    """
    return configs.get("acl_version", "nfs4-acl-tools")

# 新增：获取认证模式
def get_auth_mode() -> AuthMode:
    """
    获取当前认证模式，优先读取 configs['auth_mode']，否则根据 redis.enabled 判断
    返回值: AuthMode.JWT 或 AuthMode.SESSION
    """
    mode = configs.get("auth_mode", None)
    if mode:
        if str(mode).lower() == AuthMode.SESSION.value:
            return AuthMode.SESSION
        else:
            return AuthMode.JWT
    # 兼容老配置，仅根据 redis.enabled 判断
    redis_enabled = configs.get("redis", {}).get("enabled", False)
    return AuthMode.SESSION if redis_enabled else AuthMode.JWT

def get_filemanager_config() -> Dict[str, Any]:
    """
    获取file_manager配置
    
    Returns:
        Dict[str, Any]: file_manager配置
    """
    return configs.get("file_manager", {})

def get_input_template_config() -> Dict[str, Any]:
    """
    获取input_template配置
    
    Returns:
        Dict[str, Any]: input_template配置
    """
    return configs.get("input_template", {})
