import logging
from contextlib import contextmanager
from typing import Generator

from sqlmodel import Session

from api.database.service import DatabaseService

logger = logging.getLogger(__name__)

# 延迟初始化数据库服务
db_service: 'DatabaseService' = None

def _get_db_service() -> 'DatabaseService':
    """延迟获取数据库服务实例"""
    global db_service
    if db_service is None:
        from api.config import get_database_config
        DB_CONFIG = get_database_config()
        db_service = DatabaseService(DB_CONFIG)
    return db_service


def get_db_service() -> 'DatabaseService':
    """公开数据库服务实例，供需要直接访问服务的模块使用"""
    return _get_db_service()

def init_db():
    pass

def get_session() -> Generator[Session, None, None]:
    yield from _get_db_service().get_session()


@contextmanager
def session_scope() -> Session:
    """Provide a transactional scope around a series of operations"""
    session = Session(_get_db_service().engine)
    try:
        yield session
        session.commit()
        logger.debug("Session committed")
    except Exception as e:
        session.rollback()
        logger.error(f"Session rolled back due to error: {str(e)}")
        raise
    finally:
        session.close()
        logger.debug("Session closed")
