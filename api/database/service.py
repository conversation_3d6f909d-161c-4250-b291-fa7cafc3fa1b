import logging
from abc import ABC
from typing import Union, Dict

from sqlalchemy import URL
from sqlmodel import Session, create_engine

from api.type.database import DatabaseSettings

logger = logging.getLogger(__name__)


class Service(ABC):
    name: str  # 服务名称标识 (抽象属性)
    ready: bool = False  # 服务就绪状态

    def teardown(self): pass  # 资源清理方法

    def set_ready(self):  # 标记服务就绪
        self.ready = True


class DatabaseService(Service):
    name: str = 'database_service'

    def __init__(self, config: Union[Dict, DatabaseSettings]):
        """
        使用字典配置初始化数据库服务

        :param config: 数据库配置字典，包含以下键：
            - database_url: 数据库连接URL
            - pool_size: 连接池大小（默认20）
            - max_overflow: 最大溢出连接数（默认10）
            - pool_recycle: 连接回收时间（默认3600秒）
            - echo: 是否输出SQL日志（默认False）
        """
        # 如果是字典，则转换为DatabaseSettings对象
        if isinstance(config, dict):
            from api.type.database import DatabaseSettings
            config = DatabaseSettings(**config)
            
        self.database_url = URL.create(
            drivername=config.url.driver_name,
            username=config.url.username,
            password=config.url.password,
            host=config.url.host,
            port=config.url.port,
            database=config.url.database
        )
        self.pool_size = config.pool.size
        self.max_overflow = config.pool.max_overflow
        self.pool_recycle = config.pool.recycle
        self.pool_pre_ping = config.pool.pre_ping
        self.pool_timeout = config.pool.timeout
        self.connect_args = {
            'connect_timeout': config.connect_timeout
        },
        self.echo = config.echo

        self.engine = self._create_engine()

    def _create_engine(self):
        """Create and configure the SQLAlchemy engine"""
        connect_args = {}

        return create_engine(
            self.database_url,
            connect_args=connect_args,
            pool_size=self.pool_size,
            max_overflow=self.max_overflow,
            pool_recycle=self.pool_recycle,
            pool_pre_ping=self.pool_pre_ping,
            pool_timeout=self.pool_timeout,
            echo=self.echo
        )

    def __enter__(self):
        """Context manager entry"""
        self._session = Session(self.engine)
        logger.debug("Database session opened")
        return self._session

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        try:
            if exc_type is not None:
                logger.error(f"Session rollback due to {exc_type.__name__}: {exc_val}")
                self._session.rollback()
            else:
                self._session.commit()
        finally:
            self._session.close()
            logger.debug("Database session closed")

    def get_session(self):
        with Session(self.engine) as session:
            yield session

    def teardown(self):
        """实现父类要求的资源清理方法"""
        if hasattr(self, 'engine'):
            self.dispose()
            logging.info("Database engine connections disposed")
        self.ready = False

    def dispose(self):
        """Close all connections in the pool"""
        self.engine.dispose()
        logger.info("Database engine disposed")
