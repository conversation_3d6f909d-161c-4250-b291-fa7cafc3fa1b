apiVersion: batch/v1
kind: Job
metadata:
  annotations:
    sidecar.istio.io/inject: "false"
    zcm.iwhalecloud.com/managed-by: "zcm"
    user.code: "USER123"  # 添加用户code注解
    user.project: "PROJ456"  # 添加项目注解
  name: "wct-cli-api-patch-job"
  namespace: "ptdev01"
spec:
  backoffLimit: 4
  ttlSecondsAfterFinished: 3600  # 1小时后自动删除
  template:
    metadata:
      annotations:
        zcm.iwhalecloud.com/app-id: "337753"
        zcm.iwhalecloud.com/project-id: "564272"
        user.code: "USER123"  # 添加用户code注解
        user.project: "PROJ456"  # 添加项目注解
      labels:
        app: "wct-cli-api-patch-job"
        version: "1.0"
        zcm-app: "wct-cli-api-patch-job"
        user-code: "USER123"  # 添加用户code标签
        user-project: "PROJ456"  # 添加项目标签
    spec:
      affinity:
        podAffinity: {}
        podAntiAffinity: {}
      containers:
      - env:
        - name: "ZCM_SERVER_PORT"
          value: "52000"
        - name: "DOCKER_IMAGE"
          value: "hub-nj.iwhalecloud.com/ptdev01/wct-cli-api-patch:D_20250730141530"
        - name: "ZCM_PRJ_ID"
          value: "564272"
        - name: "ZCM_SERVER_VIP"
          value: "************"
        - name: "APP_NAME"
          value: "wct-cli-api-patch"
        - name: "PROJECT_NAME"
          value: "平台技术01团队"
        - name: "CLOUD_APP_NAME"
          value: "平台技术01团队_wct-cli-api-patch"
        - name: "APM_INIT"
          value: "https://cos.iwhalecloud.com/cos-eaa4-x/apm.sh"
        - name: "ZCM_SERVER_SSL_PORT"
          value: "25000"
        - name: "NODE_NAME"
          valueFrom:
            fieldRef:
              apiVersion: "v1"
              fieldPath: "spec.nodeName"
        - name: "HOST_IP"
          valueFrom:
            fieldRef:
              apiVersion: "v1"
              fieldPath: "status.hostIP"
        - name: "WCT_API_KEY"
          value: "ailab_f/jlBBax9efddTSt1f5OsZ9cytweNr7KdgNhe0lkB7PKeO6hIABPB7qCd39ABzX9xfn+gkubRLAfRJ4PUpgqQeL5LmH0xwjWrLrFZVk0vHfB/3Z4jTgKBbA="
        - name: "POD_IP"
          valueFrom:
            fieldRef:
              apiVersion: "v1"
              fieldPath: "status.podIP"
        - name: "APP_OPTS"
        - name: "CLOUD_APP_ID"
          value: "337753"
        - name: "CLOUD_APP_TYPE"
          value: "STATELESS"
        - name: "TOMCAT_DELAY_EXIT_TIMES"
          value: "30"
        image: "hub-nj.iwhalecloud.com/ptdev01/wct-cli-api-patch:D_20250730141530"
        imagePullPolicy: "IfNotPresent"
        lifecycle:
          preStop:
            exec:
              command:
              - "/bin/sh"
              - "-c"
              - "sleep 3"
        name: "wct-cli-api-patch-337753"
        ports:
        - containerPort: 80
          name: "port-80"
          protocol: "TCP"
        - containerPort: 3000
          name: "port-3000"
          protocol: "TCP"
        resources:
          limits:
            cpu: "100m"
            memory: "512Mi"
          requests:
            cpu: "10m"
            memory: "153Mi"
        securityContext: {}
        terminationMessagePath: "/dev/termination-log"
        terminationMessagePolicy: "File"
        volumeMounts:
        - mountPath: "/data/workspace/code"  # 代码目录只读
          name: "code-volume"
          readOnly: true
        - mountPath: "/data/workspace/docs"  # 文档目录读写
          name: "docs-volume"
        - mountPath: "/tmp/zcore"
          name: "volume-1"
        - mountPath: "/tmp/zlogs"
          name: "volume-2"
        - mountPath: "/etc/localtime"
          name: "zcmlocaltime"
        - mountPath: "/etc/timezone"
          name: "zcmtimezone"
      dnsPolicy: "ClusterFirst"
      nodeSelector:
        deepwiki-open-dev-01: "deepwiki-open-dev-01"
      restartPolicy: "Never"
      schedulerName: "default-scheduler"
      securityContext: {}
      terminationGracePeriodSeconds: 33
      volumes:
      - name: "code-volume"  # 代码卷(只读)
        hostPath:
          path: "/app/deepwiki-dev/adalflow/repos/"
          type: ""
      - name: "docs-volume"  # 文档卷(读写)
        hostPath:
          path: "/app/deepwiki-dev/docs/"
          type: ""
      - hostPath:
          path: "/tmp/zcore"
          type: ""
        name: "volume-1"
      - hostPath:
          path: "/tmp/zlogs"
          type: ""
        name: "volume-2"
      - hostPath:
          path: "/etc/localtime"
          type: "File"
        name: "zcmlocaltime"
      - hostPath:
          path: "/etc/timezone"
          type: "File"
        name: "zcmtimezone"