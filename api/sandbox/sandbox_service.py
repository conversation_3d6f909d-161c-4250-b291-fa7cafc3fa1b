"""
沙盒服务管理
提供基于用户和项目的沙盒环境管理
"""

import logging
import asyncio
from typing import Dict, List, Optional
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime

from api.service.user_group_bind_service import get_user_group_bind_service

# from .kubernetes_service import get_kubernetes_service  # 延迟导入避免循环依赖
from api.middleware.language_middleware import get_current_language
from api.database.base import session_scope
from api.model.app import App
from api.model.app_wiki_rel import AppWikiRel
from api.model.wiki_info import WikiInfo
from sqlmodel import select

logger = logging.getLogger(__name__)


class SandboxService:
    """沙盒服务管理类"""
    
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=4)
        self._cleanup_task = None
        self._kubernetes_service = None
        self._initialized = False
    
    def _ensure_initialized(self):
        """确保服务已初始化"""
        if not self._initialized:
            self._start_cleanup_task()
            self._initialized = True
    
    def _start_cleanup_task(self):
        """启动清理任务"""
        try:
            if not self._cleanup_task and asyncio.get_event_loop().is_running():
                self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        except RuntimeError:
            # 如果没有运行的事件循环，跳过清理任务初始化
            logger.warning("No running event loop, skipping cleanup task initialization")

    def _sanitize_dns1123_name(self, value: str, max_len: int = 63) -> str:
        """
        按DNS-1123规范清洗名称：小写字母数字与中划线，首尾为字母数字，长度<=63
        """
        try:
            import re
            v = str(value).lower()
            v = re.sub(r"[^a-z0-9-]", "-", v)
            v = re.sub(r"-+", "-", v)
            v = re.sub(r"^[^a-z0-9]+", "", v)
            v = re.sub(r"[^a-z0-9]+$", "", v)
            if not v:
                v = "job"
            if len(v) > max_len:
                v = v[:max_len]
                v = re.sub(r"[^a-z0-9]+$", "", v) or (v[:max_len-1] + "0")
            return v
        except Exception:
            return "job"
    
    async def _cleanup_loop(self):
        """清理循环任务"""
        from api.config import get_kubernetes_config
        k8s_config = get_kubernetes_config()
        interval = k8s_config.get("job", {}).get("cleanup_interval_minutes", 5) * 60  # 转换为秒
        sync_interval_count = 0  # 用于控制计数同步的频率
        
        while True:
            try:
                await asyncio.sleep(interval)
                
                # 执行常规清理
                await self.cleanup_idle_sandboxes()
                
                # 定时清理执行一次计数同步检查
                sync_interval_count += 1
                if sync_interval_count >= 10:
                    try:
                        sync_result = await self.repair_redis_counts()
                        if "error" not in sync_result:
                            if sync_result.get("total_corrected") or sync_result.get("user_corrections", 0) > 0:
                                logger.info(f"定期计数同步已执行，修正了 {sync_result.get('user_corrections', 0)} 个用户计数")
                    except Exception as sync_error:
                        logger.error(f"定期计数同步失败: {sync_error}")
                    finally:
                        sync_interval_count = 0
                        
            except Exception as e:
                logger.error(f"清理任务执行失败: {e}")
    
    # -------------------- Redis 计数相关辅助方法（仅在可用时启用） --------------------
    def _get_redis_client(self):
        try:
            from api.cache.redis.manager import redis_manager
            client = redis_manager.get_client()
            if client and client.is_connected():
                return client
        except Exception:
            pass
        return None

    def _total_count_key(self) -> Optional[str]:
        try:
            from .kubernetes_service import get_kubernetes_service
            k8s = get_kubernetes_service()
            return f"sandbox:{k8s.namespace}:{k8s.environment}:total"
        except Exception:
            return None

    def _user_count_key(self, user_code: str) -> Optional[str]:
        try:
            from .kubernetes_service import get_kubernetes_service
            k8s = get_kubernetes_service()
            return f"sandbox:{k8s.namespace}:{k8s.environment}:user:{user_code}:count"
        except Exception:
            return None

    def _get_counts_from_redis(self, user_code: str) -> Optional[Dict[str, int]]:
        rc = self._get_redis_client()
        if not rc:
            return None
        try:
            total_key = self._total_count_key()
            user_key = self._user_count_key(user_code)
            total = rc.get(total_key) if total_key else None
            per_user = rc.get(user_key) if user_key else None
            return {
                "total": int(total) if isinstance(total, (int, str)) and str(total).isdigit() else 0,
                "user": int(per_user) if isinstance(per_user, (int, str)) and str(per_user).isdigit() else 0,
            }
        except Exception:
            return None

    def _check_and_reserve_quota(self, user_code: str, per_user_max: int, max_total: int) -> bool:
        """
        检查配额并原子性地预留资源
        先检查当前计数是否允许创建，只有在允许的情况下才增加计数
        """
        rc = self._get_redis_client()
        if not rc:
            return True  # 无Redis则不干预
        try:
            total_key = self._total_count_key()
            user_key = self._user_count_key(user_code)
            if not total_key or not user_key:
                return True

            # 先获取当前计数
            current_total = rc.get(total_key) or 0
            current_user = rc.get(user_key) or 0
            
            try:
                current_total = int(current_total)
                current_user = int(current_user)
            except (ValueError, TypeError):
                current_total = 0
                current_user = 0

            # 检查是否超过限制（预增加1后的值）
            new_total = current_total + 1
            new_user = current_user + 1
            
            if (per_user_max and per_user_max > 0 and new_user > per_user_max):
                logger.info(f"用户 {user_code} 已达个人配额上限: {current_user}/{per_user_max}")
                return False
                
            if (max_total and new_total > max_total):
                logger.info(f"系统已达总配额上限: {current_total}/{max_total}")
                return False

            # 检查通过，执行自增
            rc.incr(total_key, 1)
            rc.incr(user_key, 1)
            
            logger.debug(f"成功预留配额 - 用户: {new_user}/{per_user_max}, 总数: {new_total}/{max_total}")
            return True
            
        except Exception as e:
            logger.error(f"检查和预留配额失败: {e}")
            # 异常则不阻断后续流程，但记录错误
            return True

    def _rollback_quota_reservation(self, user_code: str):
        """
        回滚配额预留，当沙箱创建失败时调用
        """
        rc = self._get_redis_client()
        if not rc:
            return
        try:
            total_key = self._total_count_key()
            user_key = self._user_count_key(user_code)
            if not total_key or not user_key:
                return
            
            # 执行回滚操作
            total_after = rc.incr(total_key, -1)
            user_after = rc.incr(user_key, -1)
            
            # 如果计数变为负数，修正为0
            if isinstance(total_after, int) and total_after < 0:
                rc.incr(total_key, -total_after)
                logger.warning(f"修正负数总计数: {total_after} -> 0")
            if isinstance(user_after, int) and user_after < 0:
                rc.incr(user_key, -user_after)
                logger.warning(f"修正负数用户计数: {user_after} -> 0")
            
            logger.debug(f"成功回滚配额预留 - 用户: {user_code}")
            
        except Exception as e:
            logger.error(f"回滚配额预留失败: {e}")
            # 即使回滚失败也不抛出异常，记录错误即可

    def _decrement_after_delete(self, user_code: Optional[str], job_name: Optional[str] = None):
        """
        删除、清理或驱逐完成后，减少总数与对应用户数量（仅在Redis可用时）
        
        Args:
            user_code: 用户代码，可选
            job_name: Job名称，用于日志记录，可选
        """
        rc = self._get_redis_client()
        if not rc:
            return
        
        try:
            total_key = self._total_count_key()
            user_key = self._user_count_key(user_code) if user_code else None
            
            if not total_key:
                logger.warning("无法生成Redis总数key，跳过计数递减")
                return
            
            # 执行递减操作
            operations = []
            
            # 总数递减
            total_after = rc.incr(total_key, -1)
            operations.append(("total", total_key, total_after))
            
            # 用户数递减（如果有用户信息）
            if user_key:
                user_after = rc.incr(user_key, -1)
                operations.append(("user", user_key, user_after))
            
            # 检查并修正负数
            for op_type, key, result in operations:
                if isinstance(result, int) and result < 0:
                    rc.incr(key, -result)
                    logger.warning(f"修正负数计数 {op_type}={result} -> 0, key={key}")
            
            # 记录成功日志
            log_msg = f"成功递减计数"
            if job_name:
                log_msg += f" (Job: {job_name})"
            if user_code:
                log_msg += f" (用户: {user_code})"
            logger.debug(log_msg)
            
        except Exception as e:
            error_msg = f"递减计数失败: {e}"
            if job_name:
                error_msg += f" (Job: {job_name})"
            if user_code:
                error_msg += f" (用户: {user_code})"
            logger.error(error_msg)
            # 不抛出异常，避免影响删除操作的主流程

    def _sync_redis_counts_with_k8s(self) -> Dict[str, int]:
        """
        同步Redis计数与K8s实际状态
        当发现Redis计数与实际Job数量不符时，自动修正
        
        Returns:
            修正结果统计
        """
        rc = self._get_redis_client()
        if not rc:
            return {"error": "Redis不可用"}
        
        try:
            from .kubernetes_service import get_kubernetes_service
            kubernetes_service = get_kubernetes_service()
            
            # 获取实际的Job列表
            actual_jobs = kubernetes_service.list_jobs()
            
            # 统计实际数量
            actual_total = len(actual_jobs)
            actual_user_counts = {}
            for job in actual_jobs:
                user_code = job.get('user_code')
                if user_code:
                    actual_user_counts[user_code] = actual_user_counts.get(user_code, 0) + 1
            
            # 获取Redis中的计数
            total_key = self._total_count_key()
            if not total_key:
                return {"error": "无法生成Redis key"}
                
            redis_total = rc.get(total_key) or 0
            try:
                redis_total = int(redis_total)
            except (ValueError, TypeError):
                redis_total = 0
            
            corrections = {
                "total_corrected": False,
                "user_corrections": 0,
                "total_before": redis_total,
                "total_after": actual_total,
                "users_corrected": []
            }
            
            # 修正总数
            if redis_total != actual_total:
                rc.set(total_key, actual_total)
                corrections["total_corrected"] = True
                logger.info(f"修正总数计数: {redis_total} -> {actual_total}")
            
            # 修正用户计数
            all_users = set()
            
            # 添加有实际Job的用户
            all_users.update(actual_user_counts.keys())
            
            # 添加Redis中有计数的用户
            try:
                pattern = f"sandbox:{kubernetes_service.namespace}:{kubernetes_service.environment}:user:*:count"
                user_keys = rc.keys(pattern)
                for key in user_keys:
                    # 从key中提取user_code
                    parts = key.decode('utf-8').split(':')
                    if len(parts) >= 6:
                        user_code = parts[4]  # user_code在第5个位置（索引4）
                        all_users.add(user_code)
            except Exception as e:
                logger.warning(f"获取用户计数keys失败: {e}")
            
            # 遍历所有用户，修正计数
            for user_code in all_users:
                if not user_code:
                    continue
                    
                user_key = self._user_count_key(user_code)
                if not user_key:
                    continue
                    
                actual_count = actual_user_counts.get(user_code, 0)
                redis_count = rc.get(user_key) or 0
                
                try:
                    redis_count = int(redis_count)
                except (ValueError, TypeError):
                    redis_count = 0
                
                if redis_count != actual_count:
                    if actual_count == 0:
                        # 如果实际没有Job，删除Redis key
                        rc.delete(user_key)
                    else:
                        rc.set(user_key, actual_count)
                    
                    corrections["user_corrections"] += 1
                    corrections["users_corrected"].append({
                        "user_code": user_code,
                        "before": redis_count,
                        "after": actual_count
                    })
                    logger.info(f"修正用户 {user_code} 计数: {redis_count} -> {actual_count}")
            
            if corrections["total_corrected"] or corrections["user_corrections"] > 0:
                logger.info(f"计数同步完成: 总数修正={corrections['total_corrected']}, "
                           f"用户修正={corrections['user_corrections']}个")
            else:
                logger.debug("计数同步检查完成，无需修正")
            
            return corrections
            
        except Exception as e:
            logger.error(f"同步Redis计数失败: {e}")
            return {"error": str(e)}

    async def repair_redis_counts(self) -> Dict[str, int]:
        """
        异步修复Redis计数的公共接口
        
        Returns:
            修复结果
        """
        self._ensure_initialized()
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor,
                self._sync_redis_counts_with_k8s
            )
            return result
        except Exception as e:
            logger.error(f"异步修复Redis计数失败: {e}")
            return {"error": str(e)}
    
    async def get_or_create_sandbox(self, user_code: str, git_url: str, branch: str, wct_api_key: str, user_name: Optional[str] = None) -> Dict:
        """
        获取或创建沙盒环境
        
        Args:
            user_code: 用户代码
            git_url: Git仓库URL
            branch: 分支名称
            wct_api_key: WCT API密钥，可选
            user_name: 用户姓名（用于注解与标签）
            
        Returns:
            沙盒信息
        """
        self._ensure_initialized()
        try:
            job_name = self._generate_job_name(user_code, git_url, branch)
            from .kubernetes_service import get_kubernetes_service
            kubernetes_service = get_kubernetes_service()
            # 检查是否已存在
            loop = asyncio.get_event_loop()
            existing_job = await loop.run_in_executor(
                self.executor, 
                kubernetes_service.get_job, 
                job_name,
            )
            
            if existing_job:
                # 更新访问时间
                await loop.run_in_executor(
                    self.executor,
                    kubernetes_service.update_job_access_time,
                    job_name,
                )
                logger.info(f"使用现有沙盒: {job_name}")
                return existing_job
            else:
                # 个人配额检查：每用户最大容器数（优先读取数据库的个性化配额）
                per_user_max: int = 0
                try:
                    from api.service.user_service import get_user_sandbox_quota_by_user_code
                    quota_val = get_user_sandbox_quota_by_user_code(user_code)
                    if quota_val is not None:
                        per_user_max = int(quota_val)
                except Exception as _:
                    # 忽略个性化查询错误，回退到配置
                    pass

                if not per_user_max or per_user_max <= 0:
                    try:
                        job_cfg = kubernetes_service.k8s_config.get("job", {})
                        per_user_max = int(job_cfg.get("per_user_max_containers", 0))
                    except Exception:
                        per_user_max = 0

                # 使用Redis计数进行用户额度快速判断（可用时）
                counts = self._get_counts_from_redis(user_code)
                if counts is not None and per_user_max and per_user_max > 0:
                    if counts.get("user", 0) >= per_user_max:
                        msg = f"已达到个人沙盒配额上限（{per_user_max}），请先释放或稍后再试"
                        logger.info(msg + f"，user_code={user_code}")
                        raise Exception(msg)

                # 容量控制与触发清理/驱逐机制
                if counts is not None:
                    current_count = counts.get("total", 0)
                else:
                    current_count = await loop.run_in_executor(self.executor, kubernetes_service.get_current_container_count)
                max_containers = await loop.run_in_executor(self.executor, kubernetes_service.get_max_containers)

                # 达到预警线：触发异步清理，但不阻塞
                if current_count >= int(max_containers * (2/3)):
                    async def _bg_cleanup():
                        try:
                            cleaned_jobs = await loop.run_in_executor(self.executor, kubernetes_service.cleanup_idle_jobs)
                            # 对后台清理的Job也要进行Redis计数自减
                            if cleaned_jobs:
                                for j in cleaned_jobs:
                                    self._decrement_after_delete(j.get('user_code'), j.get('name'))
                                logger.info(f"后台清理了 {len(cleaned_jobs)} 个Job，已更新Redis计数")
                        except Exception as _:
                            pass
                    # fire-and-forget
                    asyncio.create_task(_bg_cleanup())

                # 容量已满：先尝试清理，再尝试驱逐
                if current_count >= max_containers:
                    logger.info("容器数量已达上限，尝试清理过期Job")
                    cleaned_jobs = await loop.run_in_executor(self.executor, kubernetes_service.cleanup_idle_jobs)
                    if not cleaned_jobs:
                        # 查找可驱逐的Job（过安全期且最久未访问）
                        evictable = await loop.run_in_executor(self.executor, kubernetes_service.find_evictable_job)
                        if evictable and evictable.get('name'):
                            ok = await loop.run_in_executor(self.executor, kubernetes_service.delete_job, evictable['name'])
                            if ok:
                                # 成功驱逐后做Redis计数自减
                                self._decrement_after_delete(evictable.get('user_code'), evictable.get('name'))
                            logger.info(f"已驱逐Job: {evictable['name']}")
                        else:
                            # 无可驱逐对象，拒绝请求
                            raise Exception("系统繁忙，请稍后再试")
                    else:
                        # 对清理到的Job进行Redis自减
                        for j in cleaned_jobs:
                            self._decrement_after_delete(j.get('user_code'), j.get('name'))

                # 创建新的沙盒
                # 若未传入用户姓名，则尝试根据user_code查询用户名
                if not user_name:
                    try:
                        from api.service.user_service import select_user_info_by_code
                        _u = select_user_info_by_code(user_code)
                        if _u:
                            user_name = getattr(_u, 'user_name', None) or getattr(_u, 'name', None)
                    except Exception:
                        user_name = None

                # 在真正创建前，检查配额并原子性地预留资源
                ok_to_create = self._check_and_reserve_quota(user_code, per_user_max, max_containers)
                if not ok_to_create:
                    msg = "系统繁忙或已达配额上限，请稍后再试"
                    logger.info(msg + f"，user_code={user_code}")
                    raise Exception(msg)
                language = get_current_language()
                try:
                    new_job = await loop.run_in_executor(
                        self.executor,
                        kubernetes_service.create_job,
                        user_code,
                        git_url,
                        wct_api_key,
                        branch,
                        None,  # job_name
                        user_name,
                        language
                    )
                except Exception as e:
                    # 创建失败则回滚Redis配额预留
                    self._rollback_quota_reservation(user_code)
                    raise

                logger.info(f"创建新沙盒: {job_name}")
                return new_job
                
        except Exception as e:
            logger.error(f"获取或创建沙盒失败: {e}")
            raise

    async def get_or_create_sandbox_for_external_sandbox(self, user_code: str, wiki_id: str, user_id: int, user_name: Optional[str] = None) -> Dict:
        """
        获取或创建沙盒环境
        
        Args:
            user_code: 用户代码
            wiki_id: wiki主键标识
            user_id: 用户ID
            user_name: 用户姓名（可选）
        Returns:
            沙盒信息
        """
        self._ensure_initialized()
        try:
            from api.service.wiki_query_service import get_wiki_basic_info
            from api.service.job_pool_service import get_job_pool_service
            from .kubernetes_service import get_kubernetes_service
            kubernetes_service = get_kubernetes_service()
            loop = asyncio.get_event_loop()
            wiki_info = get_wiki_basic_info(wiki_id)
            wiki_id_int = wiki_info.get('id')
            
            # 检查用户是否已分配该wiki的job（需要同时满足Redis中存在和K8s中存在）
            job_pool_service = get_job_pool_service()
            allocated_job_name = await loop.run_in_executor(
                self.executor,
                job_pool_service.get_user_job,
                user_id,
                wiki_id_int
            )
            
            if allocated_job_name:
                # Redis中存在分配记录，检查K8s中是否真实存在
                existing_job = await loop.run_in_executor(
                    self.executor,
                    kubernetes_service.get_job,
                    allocated_job_name
                )
                
                if existing_job:
                    # 同时满足Redis和K8s中都存在
                    job_name = allocated_job_name
                    # 更新访问时间
                    await loop.run_in_executor(
                        self.executor,
                        kubernetes_service.update_job_access_time,
                        job_name,
                    )
                    logger.info(f"使用现有沙盒: {job_name} (Redis+K8s验证通过)")
                    return existing_job
                else:
                    # Redis中存在但K8s中不存在，清理Redis记录
                    logger.warning(f"Redis中存在分配记录但K8s中job不存在: {allocated_job_name}，清理Redis记录")
                    await loop.run_in_executor(
                        self.executor,
                        job_pool_service.release_job,
                        user_id,
                        wiki_id_int
                    )
            
            # 没有分配或已清理，开始分配新的job
            logger.info("当前未绑定沙盒，尝试开始绑定")
            # 个人配额检查：每用户最大容器数（优先读取数据库的个性化配额）
            per_user_max: int = 0
            try:
                from api.service.user_service import get_user_sandbox_quota_by_user_code
                quota_val = get_user_sandbox_quota_by_user_code(user_code)
                if quota_val is not None:
                    per_user_max = int(quota_val)
            except Exception as _:
                # 忽略个性化查询错误，回退到配置
                pass

            if not per_user_max or per_user_max <= 0:
                try:
                    job_cfg = kubernetes_service.k8s_config.get("job", {})
                    per_user_max = int(job_cfg.get("per_user_max_containers", 0))
                except Exception:
                    per_user_max = 0

            # 使用Redis计数进行用户额度快速判断（可用时）
            counts = self._get_counts_from_redis(user_code)
            if counts is not None and per_user_max and per_user_max > 0:
                if counts.get("user", 0) >= per_user_max:
                    msg = f"已达到个人沙盒配额上限（{per_user_max}），请先释放或稍后再试"
                    logger.info(msg + f"，user_code={user_code}")
                    raise Exception(msg)

            # 容量控制与触发清理/驱逐机制
            if counts is not None:
                current_count = counts.get("total", 0)
            else:
                current_count = await loop.run_in_executor(self.executor, kubernetes_service.get_current_container_count)
            max_containers = await loop.run_in_executor(self.executor, kubernetes_service.get_max_containers)

            # 达到预警线：触发异步清理，但不阻塞
            if current_count >= int(max_containers * (2/3)):
                async def _bg_cleanup():
                    try:
                        cleaned_jobs = await loop.run_in_executor(self.executor, kubernetes_service.cleanup_idle_jobs_for_external_sandbox)
                        # 对后台清理的Job也要进行Redis计数自减
                        if cleaned_jobs:
                            for j in cleaned_jobs:
                                self._decrement_after_delete(j.get('user_code'), j.get('name'))
                            logger.info(f"后台清理了 {len(cleaned_jobs)} 个Job，已更新Redis计数")
                    except Exception as _:
                        pass
                # fire-and-forget
                asyncio.create_task(_bg_cleanup())

            # 容量已满：先尝试清理，再尝试驱逐
            if current_count >= max_containers:
                logger.info("容器数量已达上限，尝试清理过期Job")
                cleaned_jobs = await loop.run_in_executor(self.executor, kubernetes_service.cleanup_idle_jobs_for_external_sandbox)
                if not cleaned_jobs:
                    # 查找可驱逐的Job（过安全期且最久未访问）
                    evictable = await loop.run_in_executor(self.executor, kubernetes_service.find_evictable_job)
                    if evictable and evictable.get('name'):
                        # 从job的labels中获取user_id和wiki_id
                        annotations = evictable.get('annotations', {})
                
                        # 尝试从 annotations 中提取 user_id 和 wiki_id
                        user_id_str = annotations.get('user.id')
                        wiki_id_str = annotations.get('wiki.id')
                        
                        if user_id and wiki_id:
                            # 使用job_pool_service释放job（会自动解绑用户组）
                            from api.service.job_pool_service import get_job_pool_service
                            ok, error_msg = await loop.run_in_executor(
                                self.executor,
                                get_job_pool_service().release_job,
                                int(user_id_str),
                                int(wiki_id_str)
                            )
                            if ok:
                                # 成功驱逐后做Redis计数自减
                                self._decrement_after_delete(evictable.get('user_code'), evictable.get('name'))
                                logger.info(f"已驱逐Job并释放: {evictable['name']}")
                            else:
                                logger.error(f"驱逐Job时释放失败: {error_msg}")
                    else:
                        # 无可驱逐对象，拒绝请求
                        raise Exception("系统繁忙，请稍后再试")
                else:
                    # 对清理到的Job进行Redis自减
                    for j in cleaned_jobs:
                        self._decrement_after_delete(j.get('user_code'), j.get('name'))

            # 在真正创建前，检查配额并原子性地预留资源
            ok_to_create = self._check_and_reserve_quota(user_code, per_user_max, max_containers)
            if not ok_to_create:
                msg = "系统繁忙或已达配额上限，请稍后再试"
                logger.info(msg + f"，user_code={user_code}")
                raise Exception(msg)
                
                
            # 使用job池服务分配job
            try:
                from api.service.job_pool_service import get_job_pool_service
                job_pool_service = get_job_pool_service()
                success, job_name, error_msg = await loop.run_in_executor(
                    self.executor,
                    job_pool_service.allocate_job,
                    user_id,
                    wiki_id,
                    user_code,
                    user_name
                )
                
                if not success:
                    # 创建失败则回滚Redis配额预留
                    self._rollback_quota_reservation(user_code)
                    raise Exception(error_msg or "分配Job失败")
                
                # 获取job详细信息
                from .kubernetes_service import get_kubernetes_service
                kubernetes_service = get_kubernetes_service()
                job_info = await loop.run_in_executor(
                    self.executor,
                    kubernetes_service.get_job,
                    job_name
                )
                
                return job_info
                
            except Exception as e:
                # 创建失败则回滚Redis配额预留
                self._rollback_quota_reservation(user_code)
                logger.error(f"分配Job失败: {e}")
                raise
                
        except Exception as e:
            logger.error(f"获取或创建沙盒失败: {e}")
            raise
    
    async def delete_sandbox(self, job_name: str, user_code: str) -> bool:
        """
        删除沙盒环境
        
        Args:
            job_name: Job名称
            user_code: 用户代码
        Returns:
            是否删除成功
        """
        self._ensure_initialized()
        try:
            from .kubernetes_service import get_kubernetes_service
            kubernetes_service = get_kubernetes_service()
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor,
                kubernetes_service.delete_job,
                job_name,
            )
            
            if result:
                logger.info(f"成功删除沙盒: {job_name}")
                # 删除成功后，Redis计数相应自减
                self._decrement_after_delete(user_code, job_name)
            
            return result
            
        except Exception as e:
            logger.error(f"删除沙盒失败: {e}")
            return False
    
    async def get_sandbox_info(self, user_code: str, git_url: str, branch: str) -> Optional[Dict]:
        """
        获取沙盒信息
        
        Args:
            user_code: 用户代码
            git_url: Git仓库URL
            branch: 分支名称
            
        Returns:
            沙盒信息，如果不存在则返回None
        """
        self._ensure_initialized()
        try:
            job_name = self._generate_job_name(user_code, git_url, branch)
            from .kubernetes_service import get_kubernetes_service
            kubernetes_service = get_kubernetes_service()
            loop = asyncio.get_event_loop()
            job_info = await loop.run_in_executor(
                self.executor,
                kubernetes_service.get_job,
                job_name,
            )
            
            return job_info
            
        except Exception as e:
            logger.error(f"获取沙盒信息失败: {e}")
            return None
    
    async def list_user_sandboxes(self, user_code: str = None) -> List[Dict]:
        """
        获取用户的所有沙盒列表
        
        Args:
            user_code: 用户代码，如果为None则获取所有用户的沙盒
        
        Returns:
            沙盒列表
        """
        self._ensure_initialized()
        try:
            from .kubernetes_service import get_kubernetes_service
            kubernetes_service = get_kubernetes_service()
            loop = asyncio.get_event_loop()
            jobs = await loop.run_in_executor(
                self.executor,
                kubernetes_service.list_jobs,
                user_code,
                None,
            )
            
            return jobs
            
        except Exception as e:
            logger.error(f"获取用户沙盒列表失败: {e}")
            return []
    
    async def list_project_sandboxes(self, project: str) -> List[Dict]:
        """
        获取项目的所有沙盒列表
        
        Args:
            project: 项目名称
        
        Returns:
            沙盒列表
        """
        self._ensure_initialized()
        try:
            from .kubernetes_service import get_kubernetes_service
            kubernetes_service = get_kubernetes_service()
            loop = asyncio.get_event_loop()
            jobs = await loop.run_in_executor(
                self.executor,
                kubernetes_service.list_jobs,
                None,
                project,
            )
            
            return jobs
            
        except Exception as e:
            logger.error(f"获取项目沙盒列表失败: {e}")
            return []
    
    async def list_all_sandboxes(self) -> List[Dict]:
        """
        获取所有沙盒列表
        
        Returns:
            沙盒列表
        """
        self._ensure_initialized()
        try:
            from .kubernetes_service import get_kubernetes_service
            kubernetes_service = get_kubernetes_service()
            loop = asyncio.get_event_loop()
            jobs = await loop.run_in_executor(
                self.executor,
                kubernetes_service.list_jobs,
            )
            
            return jobs
            
        except Exception as e:
            logger.error(f"获取所有沙盒列表失败: {e}")
            return []

    # -------------------- 应用级沙盒（不参与自动清理/驱逐） --------------------
    async def create_app_sandbox(self, app_id: str, app_code: str, app_name: Optional[str] = None,
                                 creator_id: Optional[int] = None, creator_code: Optional[str] = None,
                                 creator_name: Optional[str] = None, wct_api_key: Optional[str] = None,
                                 app_pk: Optional[int] = None) -> Dict:
        """
        创建应用级沙盒：仅挂载代码仓库目录到 /data/workspace/code。
        - Job 命名：job-{environment}-app-{appCode}
        - Tag：app-id, app-code, app-name, sandbox-type=app, namespace, environment, created-time 等
        - 仅挂载绑定Wiki的代码目录到容器的 /data/workspace/code 下
        - 不参与自动清理/驱逐（通过标签在KubernetesService中已排除）
        """
        self._ensure_initialized()
        try:
            from .kubernetes_service import get_kubernetes_service
            k8s = get_kubernetes_service()
            # 生成稳定前缀与系列，加入修订号，避免命名冲突并支持滚动切换
            safe_code = k8s._sanitize_label_value(app_code) if hasattr(k8s, '_sanitize_label_value') else str(app_code).lower()
            series = k8s._compute_app_series(app_id, app_code) if hasattr(k8s, '_compute_app_series') else safe_code
            base_name = k8s._make_app_base_name(app_code) if hasattr(k8s, '_make_app_base_name') else f"app-job-{k8s.environment}-{safe_code}"
            from datetime import datetime as _dt
            revision = _dt.utcnow().strftime("%Y%m%d%H%M%S")
            job_name_raw = f"{base_name}-{series}-{revision}".lower()
            job_name = self._sanitize_dns1123_name(job_name_raw)

            base_code_path = k8s.k8s_config.get("job", {}).get("volumes", {}).get("code_path", "/app/deepwiki/adalflow/repos/")
            resolved_app_pk = app_pk
            volumes: List[Dict[str, object]] = []
            seen_paths = set()

            with session_scope() as session:
                if resolved_app_pk is None:
                    try:
                        if app_id and str(app_id).isdigit():
                            app_row = session.exec(select(App).where(App.id == int(app_id))).first()
                        else:
                            app_row = session.exec(select(App).where(App.app_id == str(app_id))).first()
                        if app_row:
                            resolved_app_pk = int(app_row.id)
                    except Exception as err:
                        logger.warning(f"解析应用主键失败: {err}")

                if resolved_app_pk is None:
                    raise ValueError("Unable to resolve app primary key when creating sandbox.")

                wiki_rows = session.exec(
                    select(WikiInfo)
                    .join(AppWikiRel, AppWikiRel.wiki_id == WikiInfo.id)
                    .where(AppWikiRel.app_id == resolved_app_pk, WikiInfo.status != "failed")
                ).all()

                if not wiki_rows:
                    raise ValueError("当前应用未绑定任何可用的Wiki，请先完成绑定后再创建沙盒。")

                for idx, wiki in enumerate(wiki_rows):
                    owner = (wiki.repo_owner or "unknown").strip().strip('/') or "unknown"
                    repo_name = (wiki.repo_name or wiki.name or wiki.wiki_id or f"wiki-{wiki.id or idx}").strip().strip('/') or f"wiki-{wiki.id or idx}"
                    branch_name = (wiki.branch or "master").strip()
                    host_path = f"{base_code_path.rstrip('/')}/{owner}/{repo_name}-{branch_name}".rstrip('/')

                    if not host_path or host_path in seen_paths:
                        continue

                    seen_paths.add(host_path)
                    container_path = f"/data/workspace/code/{owner}/{repo_name}-{branch_name}"
                    volume_name_raw = f"code-{wiki.id or idx}"
                    volume_name = self._sanitize_dns1123_name(volume_name_raw)
                    if not volume_name:
                        volume_name = f"code-{idx}"

                    volumes.append({
                        "name": volume_name,
                        "host_path": host_path,
                        "container_path": container_path,
                        "read_only": True
                    })

            if not volumes:
                raise ValueError("未找到需要挂载的代码目录，无法创建应用沙盒。")

            # 环境变量最小化（保持时区）
            env_vars = [
                {"name": "TZ", "value": "Asia/Shanghai"},
                {"name": "IS_APP", "value": "true"}
            ]
            if wct_api_key:
                env_vars.append({"name": "WCT_API_KEY", "value": str(wct_api_key)})

            # 额外标签与注解
            # labels 的值使用K8s标签安全清洗（允许 a-z0-9.-_，最长63），与DNS-1123不同
            extra_labels = {
                "sandbox-type": "app",
                "app-id": str(app_id),
                "app-code": safe_code,
                "app-name": k8s._sanitize_label_value(app_name) if app_name else safe_code,
                "k8s-namespace": k8s.namespace,
                "deepwiki-env": k8s.environment,
                "app-series": series,
                "app-base": base_name
            }
            # 记录创建者信息
            if creator_id is not None:
                extra_labels["creator-id"] = str(creator_id)
            if creator_code:
                extra_labels["creator-code"] = k8s._sanitize_label_value(creator_code)
            if creator_name:
                extra_labels["creator-name"] = k8s._sanitize_label_value(creator_name)
            extra_annotations = {
                "app.id": str(app_id),
                "app.code": app_code,
                "app.name": app_name or app_code,
                "app.series": series,
                "app.base": base_name,
            }
            if creator_id is not None:
                extra_annotations["creator.id"] = str(creator_id)
            if creator_code:
                extra_annotations["creator.code"] = creator_code
            if creator_name:
                extra_annotations["creator.name"] = creator_name

            # 从配置中读取资源规格
            job_config = k8s.k8s_config.get("app_job", {})
            resources = job_config.get("resources", {})
            limits = resources.get("limits", {})
            requests = resources.get("requests", {})
            
            # 使用配置中的资源限制，如果没有配置则使用默认值
            cpu_request = requests.get("cpu", "10m")
            memory_request = requests.get("memory", "256Mi")
            cpu_limit = limits.get("cpu", "100m")
            memory_limit = limits.get("memory", "256Mi")
            
            job = await asyncio.get_event_loop().run_in_executor(
                self.executor,
                k8s.create_custom_job,
                job_name,
                k8s.k8s_config.get("job", {}).get("default_image", "hub-nj.iwhalecloud.com/ptdev01/wct-cli-api-patch:D_20250730141530"),
                volumes,
                env_vars,
                cpu_request,
                memory_request,
                cpu_limit,
                memory_limit,
                None,  # user_code
                None,  # git_url
                None,  # branch
                None,  # user_name
                None,  # namespace -> default
                extra_labels,
                extra_annotations
            )
            return job
        except Exception as e:
            logger.error(f"创建应用级沙盒失败: {e}")
            raise

    async def delete_app_sandbox(self, app_code: str) -> bool:
        """
        删除应用级沙盒
        """
        self._ensure_initialized()
        try:
            from .kubernetes_service import get_kubernetes_service
            k8s = get_kubernetes_service()
            # 删除该 app_code 的所有历史版本（同系列或同码）
            jobs = []
            try:
                jobs = k8s.list_app_jobs(app_code)
            except Exception:
                pass
            ok_all = True
            for j in jobs or []:
                name = j.get("name")
                if name:
                    ok = await asyncio.get_event_loop().run_in_executor(self.executor, k8s.delete_job, name)
                    ok_all = ok_all and bool(ok)
            return ok_all
        except Exception as e:
            logger.error(f"删除应用级沙盒失败: {e}")
            return False

    async def get_app_sandbox_status(self, app_code: str) -> Dict:
        """
        查询应用级沙盒的状态（基于现有Job与Pod状态）
        """
        self._ensure_initialized()
        try:
            from api.database.base import session_scope
            from sqlmodel import select
            from api.model.app import App
            from api.model.user_info import UserInfo
            from .kubernetes_service import get_kubernetes_service
            k8s = get_kubernetes_service()
            language = get_current_language()
            # 查询应用创建人信息
            creator_id = None
            creator_code = None
            creator_name = None
            app_identifier = None
            try:
                with session_scope() as session:
                    app_rec = session.exec(select(App).where(App.app_code == app_code)).first()
                    if app_rec and getattr(app_rec, "created_by", None) is not None:
                        app_identifier = getattr(app_rec, "app_id", None)
                        creator_val = app_rec.created_by
                        # 优先按用户ID查询
                        user = session.exec(select(UserInfo).where(UserInfo.id == creator_val)).first()
                        if not user:
                            # 兼容某些环境 created_by 存储的是工号
                            user = session.exec(select(UserInfo).where(UserInfo.user_code == str(creator_val))).first()
                        if user:
                            creator_id = getattr(user, "id", None)
                            creator_code = getattr(user, "user_code", None)
                            creator_name = getattr(user, "user_name", None)
                        else:
                            # 至少返回原始created_by作为creator_code以便前端显示
                            creator_code = str(creator_val)
            except Exception:
                pass
            loop = asyncio.get_event_loop()
            status_info = await loop.run_in_executor(
                self.executor,
                k8s.get_app_job_detailed_status,
                app_code,
                app_identifier,
                language,
            )
            if not isinstance(status_info, dict):
                status_info = {}

            status_info.setdefault("app_code", app_code)
            status_info["creator_id"] = creator_id
            status_info["creator_code"] = creator_code
            status_info["creator_name"] = creator_name

            return status_info
        except Exception as e:
            logger.error(f"查询应用级沙盒状态失败: {e}")
            return {"status": "查询失败", "error": str(e)}

    async def rolling_restart_app(self, app_id: str, app_code: str, app_name: Optional[str] = None,
                                  max_surge: int = 1, wait_ready_timeout: int = 300) -> Dict:
        """
        应用级沙盒滚动重启：
        - 创建新修订版本（按系列加时间戳），等待新版本就绪
        - 准备好后删除所有旧版本
        - 解决命名冲突：使用 app-series 标签与 job 名称中的 series 段
        返回新版本信息与被删除版本列表
        """
        self._ensure_initialized()
        try:
            from .kubernetes_service import get_kubernetes_service
            k8s = get_kubernetes_service()
            old_jobs = k8s.list_app_jobs(app_code, app_id)
            new_job = await self.create_app_sandbox(app_id, app_code, app_name)
            new_name = new_job.get("name")
            ok = await asyncio.get_event_loop().run_in_executor(self.executor, k8s.wait_for_job_ready, new_name, int(wait_ready_timeout))
            if not ok:
                return {"success": False, "error": "新版本未在超时时间内就绪", "new_job": new_job}

            # 删除所有旧版本
            to_delete = []
            for j in old_jobs or []:
                name = j.get("name")
                if name and name != new_name:
                    to_delete.append(name)

            deleted = []
            for name in to_delete:
                ok = await asyncio.get_event_loop().run_in_executor(self.executor, k8s.delete_job, name)
                if ok:
                    deleted.append(name)

            return {"success": True, "new_job": new_job, "deleted_old_jobs": deleted}
        except Exception as e:
            logger.error(f"滚动重启应用失败: {e}")
            return {"success": False, "error": str(e)}

    async def rolling_restart_app_async(self, app_id: str, app_code: str, app_name: Optional[str] = None,
                                        wait_ready_timeout: int = 300) -> Dict:
        """
        异步滚动重启：
        - 立即创建新修订并返回（前端可立刻看到两个任务）
        - 后台等待新任务就绪后，删除旧任务
        """
        self._ensure_initialized()
        try:
            from .kubernetes_service import get_kubernetes_service
            k8s = get_kubernetes_service()
            # 记录当前旧任务列表
            old_jobs = k8s.list_app_jobs(app_code, app_id)
            # 先创建新任务并立即返回
            new_job = await self.create_app_sandbox(app_id, app_code, app_name)
            new_name = new_job.get("name")

            async def _bg_wait_and_cleanup():
                try:
                    # 等待新任务容器与HTTP健康都就绪
                    ok = await asyncio.get_event_loop().run_in_executor(self.executor, k8s.wait_for_job_ready, new_name, int(wait_ready_timeout), 3, True)
                    # 给前端一个短暂展示窗口（2-5秒），确保能看到两个任务
                    try:
                        await asyncio.sleep(3)
                    except Exception:
                        pass
                    if ok:
                        for j in old_jobs or []:
                            name = j.get("name")
                            if name and name != new_name:
                                try:
                                    await asyncio.get_event_loop().run_in_executor(self.executor, k8s.delete_job, name)
                                except Exception:
                                    pass
                except Exception as _e:
                    logger.warning(f"异步滚动清理失败: {_e}")

            # fire-and-forget
            try:
                asyncio.create_task(_bg_wait_and_cleanup())
            except RuntimeError:
                # 如果当前没有事件循环，使用线程执行
                loop = asyncio.new_event_loop()
                loop.run_until_complete(_bg_wait_and_cleanup())
                loop.close()

            return {"success": True, "new_job": new_job, "mode": "async"}
        except Exception as e:
            logger.error(f"异步滚动重启应用失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def cleanup_idle_sandboxes(self) -> int:
        """
        清理空闲沙盒
        
        Returns:
            清理的沙盒数量
        """
        self._ensure_initialized()
        try:
            from .kubernetes_service import get_kubernetes_service
            kubernetes_service = get_kubernetes_service()
            loop = asyncio.get_event_loop()
            cleaned_jobs = await loop.run_in_executor(
                self.executor,
                kubernetes_service.cleanup_idle_jobs_for_external_sandbox,
            )
            # Redis计数自减
            if cleaned_jobs:
                for j in cleaned_jobs:
                    self._decrement_after_delete(j.get('user_code'), j.get('name'))
            
            return len(cleaned_jobs or [])
            
        except Exception as e:
            logger.error(f"清理空闲沙盒失败: {e}")
            return 0
    
    async def update_sandbox_access(self, user_code: str, git_url: str, branch: str, job_name: Optional[str] = None) -> bool:
        """
        更新沙盒访问时间
        
        Args:
            user_code: 用户代码
            git_url: Git仓库URL
            branch: 分支名称
            
        Returns:
            是否更新成功
        """
        self._ensure_initialized()
        try:
            if not job_name:
                job_name = self._generate_job_name(user_code, git_url, branch)
            from .kubernetes_service import get_kubernetes_service
            kubernetes_service = get_kubernetes_service()
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor,
                kubernetes_service.update_job_access_time,
                job_name,
            )
            
            return result
            
        except Exception as e:
            logger.error(f"更新沙盒访问时间失败: {e}")
            return False
    
    def _generate_job_name(self, user_code: str, git_url: str, branch: str) -> str:
        """
        生成job名称
        
        Args:
            user_code: 用户代码
            git_url: Git仓库URL
            branch: 分支名称
            
        Returns:
            job名称
        """
        from .kubernetes_service import get_kubernetes_service
        kubernetes_service = get_kubernetes_service()
        return kubernetes_service._generate_job_name_from_git(user_code, git_url, branch)
    
    async def check_job_exists(self, user_code: str, git_url: str, branch: str) -> bool:
        """
        检查job是否存在
        
        Args:
            user_code: 用户代码
            git_url: Git仓库URL
            branch: 分支名称
            
        Returns:
            job是否存在
        """
        self._ensure_initialized()
        try:
            job_name = self._generate_job_name(user_code, git_url, branch)
            from .kubernetes_service import get_kubernetes_service
            kubernetes_service = get_kubernetes_service()
            loop = asyncio.get_event_loop()
            job_info = await loop.run_in_executor(
                self.executor,
                kubernetes_service.get_job,
                job_name
            )
            return job_info is not None
        except Exception as e:
            logger.error(f"检查job是否存在失败: {e}")
            return False

    def get_sandbox_url(self, user_code: str, git_url: str, branch: str, job_name: Optional[str] = None) -> str:
        """
        获取沙盒访问URL
        
        Args:
            user_code: 用户代码
            git_url: Git仓库URL
            branch: 分支名称
            
        Returns:
            沙盒访问URL
        """
        try:
            from .kubernetes_service import get_kubernetes_service
            kubernetes_service = get_kubernetes_service()
            # 查询实际的Pod IP地址
            pod_ip = kubernetes_service.get_job_ip_by_params(user_code, git_url, branch, job_name)
            
            if pod_ip:
                # 返回实际的IP地址和API端点
                return f"http://{pod_ip}:3000/v1/chat/completions"
            else:
                # 如果无法获取IP，返回默认的占位符URL
                if not job_name:
                    job_name = self._generate_job_name(user_code, git_url, branch)
                from api.config import get_kubernetes_config
                k8s_config = get_kubernetes_config()
                namespace = k8s_config.get("namespace", "ptdev01")
                logger.warning(f"无法获取Pod IP，使用默认URL: {job_name}")
                return f"http://sandbox-{job_name}.{namespace}.svc.cluster.local:3000/v1/chat/completions"
                
        except Exception as e:
            logger.error(f"获取沙盒URL失败: {e}")
            # 发生异常时返回默认URL
            job_name = self._generate_job_name(user_code, git_url, branch)
            from api.config import get_kubernetes_config
            k8s_config = get_kubernetes_config()
            namespace = k8s_config.get("namespace", "ptdev01")
            return f"http://sandbox-{job_name}.{namespace}.svc.cluster.local:3000/v1/chat/completions"

    def get_app_sandbox_url(self, app_code: str) -> str:
        """
        获取应用级沙盒的访问URL（优先使用Pod IP，其次使用集群内Service域名）

        Args:
            app_code: 应用编码

        Returns:
            可访问的HTTP URL（/v1/chat/completions）
        """
        try:
            from .kubernetes_service import get_kubernetes_service
            k8s = get_kubernetes_service()
            # 选择该 app_code 系列最新的版本进行访问
            jobs = k8s.list_app_jobs(app_code)
            latest = jobs[0] if jobs else None
            if not latest:
                # 没有现有Job，返回基于基础名的Service域名
                base_name = k8s._make_app_base_name(app_code) if hasattr(k8s, '_make_app_base_name') else f"app-job-{k8s.environment}-{k8s._sanitize_label_value(app_code)}".lower()
                from api.config import get_kubernetes_config
                k8s_config = get_kubernetes_config()
                namespace = k8s_config.get("namespace", k8s.namespace or "default")
                return f"http://sandbox-{base_name}.{namespace}.svc.cluster.local:3000/v1/chat/completions"

            job_name = latest.get("name")
            pods = k8s.get_job_pods(job_name)
            running_pods = [p for p in (pods or []) if p.get("status") == "Running" and p.get("pod_ip")]
            if running_pods:
                pod_ip = running_pods[0].get("pod_ip")
                return f"http://{pod_ip}:3000/v1/chat/completions"

            # 回退到Service域名
            from api.config import get_kubernetes_config
            k8s_config = get_kubernetes_config()
            namespace = k8s_config.get("namespace", k8s.namespace or "default")
            return f"http://sandbox-{job_name}.{namespace}.svc.cluster.local:3000/v1/chat/completions"

        except Exception as e:
            logger.error(f"获取应用级沙盒URL失败: {e}")
            try:
                from .kubernetes_service import get_kubernetes_service
                k8s = get_kubernetes_service()
                base_name = k8s._make_app_base_name(app_code) if hasattr(k8s, '_make_app_base_name') else f"app-job-{k8s.environment}-{k8s._sanitize_label_value(app_code)}".lower()
                from api.config import get_kubernetes_config
                k8s_config = get_kubernetes_config()
                namespace = k8s_config.get("namespace", k8s.namespace or "default")
                return f"http://sandbox-{base_name}.{namespace}.svc.cluster.local:3000/v1/chat/completions"
            except Exception:
                # 最后兜底：返回一个不可用的本地地址，避免None
                return "http://localhost:3000/v1/chat/completions"

    async def get_sandbox_detailed_status(self, user_code: str, git_url: str, branch: str, job_name: Optional[str] = None) -> Dict:
        """
        获取沙盒的详细状态
        
        Args:
            user_code: 用户代码
            git_url: Git仓库URL
            branch: 分支名称
            job_name: job名称
        Returns:
            包含详细状态信息的字典
        """
        self._ensure_initialized()
        language = get_current_language()
        try:
            from .kubernetes_service import get_kubernetes_service
            kubernetes_service = get_kubernetes_service()
            loop = asyncio.get_event_loop()
            status_info = await loop.run_in_executor(
                self.executor,
                kubernetes_service.get_job_detailed_status,
                user_code,
                git_url,
                branch,
                job_name,
                language
            )
            
            return status_info
            
        except Exception as e:
            logger.error(f"获取沙盒详细状态失败: {e}")
            return {
                "status": "状态查询失败",
                "message": f"获取状态异常: {str(e)}",
                "timestamp": datetime.utcnow().isoformat()
            }

    async def get_sandbox_url_by_wiki_id(self, user_code: str, wiki_id: str) -> str:
        """
        获取沙盒访问URL

        Args:
            user_code: 用户代码
            wiki_id: wiki主键标识

        Returns:
            沙盒访问URL
        """
        self._ensure_initialized()
        try:
            from .kubernetes_service import get_kubernetes_service
            kubernetes_service = get_kubernetes_service()
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor,
                kubernetes_service.get_app_job_detailed_status,
                user_code,
                wiki_id
            )
            return result
        except Exception as e:
            logger.error(f"获取沙盒访问URL失败: {e}")
            return None

    async def list_sandboxes_paginated(self, user_code: Optional[str] = None, project: Optional[str] = None, limit: int = 50, continue_token: Optional[str] = None) -> Dict:
        """
        分页获取沙盒列表（基于k8s SDK limit/_continue）

        Args:
            user_code: 用户代码（可选）
            project: 项目名称（可选）
            limit: 每页大小，默认50
            continue_token: 翻页令牌

        Returns:
            {
                "jobs": List[Dict],
                "continue_token": Optional[str],
                "remaining_item_count": Optional[int]
            }
        """
        self._ensure_initialized()
        try:
            from .kubernetes_service import get_kubernetes_service
            kubernetes_service = get_kubernetes_service()
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor,
                kubernetes_service.list_jobs_paginated,
                user_code,
                project,
                limit,
                continue_token
            )
            return result
        except Exception as e:
            logger.error(f"分页获取沙盒列表失败: {e}")
            return {"jobs": [], "continue_token": None, "remaining_item_count": None}

    async def list_user_sandboxes_paginated(self, user_code: str, limit: int = 50, continue_token: Optional[str] = None) -> Dict:
        """
        分页获取指定用户的沙盒列表
        """
        return await self.list_sandboxes_paginated(user_code=user_code, project=None, limit=limit, continue_token=continue_token)

    async def list_project_sandboxes_paginated(self, project: str, limit: int = 50, continue_token: Optional[str] = None) -> Dict:
        """
        分页获取指定项目的沙盒列表
        """
        return await self.list_sandboxes_paginated(user_code=None, project=project, limit=limit, continue_token=continue_token)
    
    async def create_symbolic_link(self, pod_name: str, paths: List[Dict[str, str]]):
        """
        创建软链接

        参数：
            pod_name: pod名称
            paths: 软链接列表，如下格式: [{"src_path": "/app/test1", "dist_path": "/tmp/test1"}]
        """
        self._ensure_initialized()
        try:
            from .kubernetes_service import get_kubernetes_service
            kubernetes_service = get_kubernetes_service()
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor,
                kubernetes_service.create_symbolic_link,
                pod_name,
                paths
            )
            return result
        except Exception as e:
            logger.error(f"Failed to create symbolic link, cause by: {e}")
            return {"success": False, "msg": "Failed to create symbolic link"}

    async def unlink_symbolic(self, pod_name: str, paths: List[str]):
        self._ensure_initialized()
        try:
            from .kubernetes_service import get_kubernetes_service
            kubernetes_service = get_kubernetes_service()
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor,
                kubernetes_service.unlink_symbolic,
                pod_name,
                paths
            )
            return result
        except Exception as e:
            logger.error(f"Failed to unlink the symbolic link, cause by: {e}")
            return {"success": False, "msg": "Failed to unlink the symbolic link"}

    def decrement_user_quota(self, user_code: str):
        """
        减少用户沙盒配额计数
        """
        self._ensure_initialized()
        try:
            self._decrement_after_delete(user_code)
            return True
        except Exception as e:
            logger.error(f"Failed to decrement user quota, cause by: {e}")
            return False


# 创建全局实例
sandbox_service = SandboxService()
