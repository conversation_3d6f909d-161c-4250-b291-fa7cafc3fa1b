"""
Kubernetes管理服务
提供k8s job的创建、删除、查询等功能
"""

import logging
from datetime import datetime, timedelta, timezone
import time
from typing import List, Dict, Optional
from kubernetes import client, config as k8s_config, stream
from kubernetes.client.rest import ApiException
import os
import re

from api.middleware.language_middleware import get_translation
from api.type.sandbox_status import SandboxStatus

logger = logging.getLogger(__name__)


class KubernetesService:
    """Kubernetes服务管理类"""

    def __init__(self):
        """初始化k8s客户端"""
        from api.config import get_kubernetes_config  # 延迟导入，解决循环依赖
        self.k8s_config = get_kubernetes_config()
        self.namespace = self.k8s_config.get("namespace", "ptdev01")
        self.environment = self.k8s_config.get("environment", "dev") # 读取环境标识
        self.api_server = self.k8s_config.get("api_server", "http://10.45.80.2:8080")
        self.verify_ssl = self.k8s_config.get("verify_ssl", False)
        # 研发云门户地址用于前端跳转容器
        self.portal_base_url = self.k8s_config.get("portal_base_url", "")
        ui_features = self.k8s_config.get("ui_features", {}) or {}
        self.enable_container_portal_button = ui_features.get("enable_container_portal_button", False)
        self.enable_jump_server_button = ui_features.get("enable_jump_server_button", False)
        self.jump_server_url = ui_features.get("jump_server_url", "")
        self._batch_v1_api = None
        self._core_v1_api = None
        self._metrics_api = None
        self._init_client()
    
    def _init_client(self):
        """初始化k8s客户端"""
        try:
            # 配置k8s客户端
            configuration = client.Configuration()
            configuration.host = self.api_server
            configuration.verify_ssl = self.verify_ssl
            
            # 检查是否有k8s配置文件
            config_file = "api/sandbox/config"
            config_loaded = False
            
            if os.path.exists(config_file):
                try:
                    k8s_config.load_kube_config(config_file=config_file, client_configuration=configuration)
                    config_loaded = True
                    logger.info(f"已加载Kubernetes配置文件: {config_file}")
                except Exception as e:
                    logger.warning(f"加载Kubernetes配置文件失败: {e}，将使用默认配置")
            
            if not config_loaded:
                # 使用自定义配置（无配置文件模式）
                client.Configuration.set_default(configuration)
                logger.info("使用默认Kubernetes配置（无配置文件模式）")
            
            self._batch_v1_api = client.BatchV1Api()
            self._core_v1_api = client.CoreV1Api()
            
            # 初始化metrics API客户端（用于获取CPU和内存使用情况）
            try:
                self._metrics_api = client.CustomObjectsApi()
                logger.info("Metrics API客户端初始化成功")
            except Exception as e:
                logger.warning(f"Metrics API客户端初始化失败: {e}")
                self._metrics_api = None
            
            logger.info(f"Kubernetes客户端初始化成功，API地址: {self.api_server}")
            
        except Exception as e:
            logger.error(f"初始化Kubernetes客户端失败: {e}")
            raise
    
    def create_job(self, id: int, job_name: str = None, wct_api_key: str = None, language: str = "zh") -> Dict:
        """
        创建k8s job
        
        Args:
            id: 唯一标识
            job_name: job名称，如果不提供则自动生成
            wct_api_key: WCT API密钥，可选
            language: 语言代码
            username: Linux用户名（可选），如果提供则以该用户身份运行容器
            uid: Linux用户ID（可选），与username配合使用
            
        Returns:
            创建的job信息
        """
        try:
            if not job_name:
                job_name = self.generate_job_name(id=id)
            
            # 生成job配置（传入username和uid以配置initContainer和安全上下文）
            job_manifest = self._generate_job_manifest(job_name, wct_api_key)

            # 创建job
            response = self._batch_v1_api.create_namespaced_job(
                namespace=self.namespace,
                body=job_manifest
            )
            
            logger.info(f"成功创建Job: {job_name}" + (f" (运行用户: {job_name}, UID: {id})" if job_name else ""))
            
            return {
                "name": response.metadata.name,
                "namespace": response.metadata.namespace,
                "creation_time": response.metadata.creation_timestamp,
                "status": "created",
                "username": job_name,
                "uid": id
            }
            
        except ApiException as e:
            if e.status == 409:  # Already exists
                logger.warning(f"Job {job_name} 已存在")
                return self.get_job(job_name)
            else:
                logger.error(f"创建Job失败: {e}")
                raise
        except Exception as e:
            logger.error(f"创建Job时发生错误: {e}")
            raise
    
    def delete_job(self, job_name: str) -> bool:
        """
        删除k8s job
        
        Args:
            job_name: job名称
            
        Returns:
            是否删除成功
        """
        try:
            # 删除job
            self._batch_v1_api.delete_namespaced_job(
                name=job_name,
                namespace=self.namespace,
                propagation_policy='Background'
            )
            
            logger.info(f"成功删除Job: {job_name}")
            return True
            
        except ApiException as e:
            if e.status == 404:  # Not found
                logger.warning(f"Job {job_name} 不存在")
                return True
            else:
                logger.error(f"删除Job失败: {e}")
                return False
        except Exception as e:
            logger.error(f"删除Job时发生错误: {e}")
            return False
    
    def get_job(self, job_name: str) -> Optional[Dict]:
        """
        查询单个job信息
        
        Args:
            job_name: job名称
            
        Returns:
            job信息
        """
        try:
            response = self._batch_v1_api.read_namespaced_job(
                name=job_name,
                namespace=self.namespace
            )
            
            job_info = self._format_job_info(response)
            
            # 获取关联的pod信息
            pods_info = self.get_job_pods(job_name)
            job_info['pods'] = pods_info
            
            return job_info
            
        except ApiException as e:
            if e.status == 404:
                return None
            else:
                logger.error(f"查询Job失败: {e}")
                raise
        except Exception as e:
            logger.error(f"查询Job时发生错误: {e}")
            raise
    
    def get_job_pods(self, job_name: str) -> List[Dict]:
        """
        获取job关联的pod信息
        
        Args:
            job_name: job名称
            
        Returns:
            pod信息列表
        """
        try:
            # 使用标签选择器查询与job关联的pod
            # Kubernetes Job会自动为Pod添加job-name标签
            label_selector = f"job-name={job_name}"
            
            pods_response = self._core_v1_api.list_namespaced_pod(
                namespace=self.namespace,
                label_selector=label_selector
            )
            
            pods_info = []
            for pod in pods_response.items:
                pod_info = {
                    "name": pod.metadata.name,
                    "namespace": pod.metadata.namespace,
                    "status": pod.status.phase,
                    "pod_ip": pod.status.pod_ip,
                    "host_ip": pod.status.host_ip,
                    "node_name": pod.spec.node_name,
                    "creation_time": pod.metadata.creation_timestamp,
                    "labels": pod.metadata.labels or {},
                    "annotations": pod.metadata.annotations or {},
                    "resources": self._get_pod_resources(pod),
                    # 记录容器基本信息，便于前端展示和跳转
                    "containers": [
                        {
                            "name": container.name,
                            "image": container.image
                        }
                        for container in (getattr(pod.spec, "containers", None) or [])
                    ]
                }
                
                # 获取容器状态
                if pod.status.container_statuses:
                    container_statuses = []
                    for container_status in pod.status.container_statuses:
                        # 去掉container_id中的运行时前缀，保留真实ID
                        container_id_raw = getattr(container_status, "container_id", "") or ""
                        if "://" in container_id_raw:
                            container_id_raw = container_id_raw.split("://", 1)[1]
                        container_statuses.append({
                            "name": container_status.name,
                            "ready": container_status.ready,
                            "restart_count": container_status.restart_count,
                            "state": self._get_container_state(container_status.state),
                            "container_id": container_id_raw
                        })
                    pod_info["container_statuses"] = container_statuses
                
                pods_info.append(pod_info)
            
            return pods_info
            
        except Exception as e:
            logger.error(f"获取Job Pods信息失败: {e}")
            return []
    
    def _get_container_state(self, state) -> str:
        """
        获取容器状态
        
        Args:
            state: 容器状态对象
            
        Returns:
            状态字符串
        """
        if state.running:
            return "running"
        elif state.waiting:
            return f"waiting: {state.waiting.reason}" if state.waiting.reason else "waiting"
        elif state.terminated:
            return f"terminated: {state.terminated.reason}" if state.terminated.reason else "terminated"
        else:
            return "unknown"
    
    def _get_pod_resources(self, pod) -> Dict:
        """
        获取Pod的资源信息（CPU和内存）
        
        Args:
            pod: Pod对象
            
        Returns:
            资源信息字典
        """
        resources = {
            "containers": [],
            "total_requests": {"cpu": "0", "memory": "0"},
            "total_limits": {"cpu": "0", "memory": "0"}
        }
        
        try:
            total_cpu_requests = 0
            total_memory_requests = 0
            total_cpu_limits = 0
            total_memory_limits = 0
            
            for container in pod.spec.containers:
                container_resources = {
                    "name": container.name,
                    "requests": {"cpu": "0", "memory": "0"},
                    "limits": {"cpu": "0", "memory": "0"}
                }
                
                if container.resources:
                    # 处理请求资源
                    if container.resources.requests:
                        cpu_req = container.resources.requests.get("cpu", "0")
                        memory_req = container.resources.requests.get("memory", "0")
                        container_resources["requests"]["cpu"] = str(cpu_req)
                        container_resources["requests"]["memory"] = str(memory_req)
                        
                        # 累加总请求资源
                        total_cpu_requests += self._parse_cpu(str(cpu_req))
                        total_memory_requests += self._parse_memory(str(memory_req))
                    
                    # 处理限制资源
                    if container.resources.limits:
                        cpu_limit = container.resources.limits.get("cpu", "0")
                        memory_limit = container.resources.limits.get("memory", "0")
                        container_resources["limits"]["cpu"] = str(cpu_limit)
                        container_resources["limits"]["memory"] = str(memory_limit)
                        
                        # 累加总限制资源
                        total_cpu_limits += self._parse_cpu(str(cpu_limit))
                        total_memory_limits += self._parse_memory(str(memory_limit))
                
                resources["containers"].append(container_resources)
            
            # 设置总资源
            resources["total_requests"]["cpu"] = f"{total_cpu_requests}m" if total_cpu_requests > 0 else "0"
            resources["total_requests"]["memory"] = f"{total_memory_requests}Mi" if total_memory_requests > 0 else "0"
            resources["total_limits"]["cpu"] = f"{total_cpu_limits}m" if total_cpu_limits > 0 else "0"
            resources["total_limits"]["memory"] = f"{total_memory_limits}Mi" if total_memory_limits > 0 else "0"
            
        except Exception as e:
            logger.error(f"获取Pod资源信息失败: {e}")
        
        return resources
    
    def _parse_cpu(self, cpu_str: str) -> float:
        """
        解析CPU值为毫核(millicores)
        
        Args:
            cpu_str: CPU字符串，如 "100m", "0.1", "1"
            
        Returns:
            毫核数值
        """
        try:
            if not cpu_str or cpu_str == "0":
                return 0.0
            
            cpu_str = str(cpu_str)
            if cpu_str.endswith('m'):
                return float(cpu_str[:-1])
            else:
                return float(cpu_str) * 1000
        except:
            return 0.0
    
    def _parse_metrics_cpu(self, cpu_str: str) -> float:
        """
        解析Metrics API返回的CPU值（nano cores）
        
        Args:
            cpu_str: CPU字符串，如 "12476824n" 或 "1000000u" 或 "1000m"
            
        Returns:
            毫核数值
        """
        try:
            if not cpu_str or cpu_str == "0":
                return 0.0
            
            cpu_str = str(cpu_str)
            # 移除可能的空格
            cpu_str = cpu_str.strip()
            
            # 处理不同的单位
            if cpu_str.endswith('n'):  # nano cores
                return float(cpu_str[:-1]) / 1000000  # 转换为毫核
            elif cpu_str.endswith('u'):  # micro cores
                return float(cpu_str[:-1]) / 1000  # 转换为毫核
            elif cpu_str.endswith('m'):  # milli cores
                return float(cpu_str[:-1])
            else:
                # 假设是nano cores（无单位）
                return float(cpu_str) / 1000000  # 转换为毫核
        except:
            return 0.0
    
    def _parse_metrics_memory(self, memory_str: str) -> float:
        """
        解析Metrics API返回的内存值
        
        Args:
            memory_str: 内存字符串，如 "67424Ki", "1073741824" (bytes)
            
        Returns:
            KiB数值（保持与Kubernetes资源规格一致）
        """
        try:
            if not memory_str or memory_str == "0":
                return 0.0
            
            memory_str = str(memory_str)
            # 移除可能的空格
            memory_str = memory_str.strip()
            
            # 处理不同的单位，统一转换为KiB
            if memory_str.endswith('Ki'):
                return float(memory_str[:-2])  # 已经是KiB
            elif memory_str.endswith('Mi'):
                return float(memory_str[:-2]) * 1024  # MiB to KiB
            elif memory_str.endswith('Gi'):
                return float(memory_str[:-2]) * 1024 * 1024  # GiB to KiB
            elif memory_str.endswith('Ti'):
                return float(memory_str[:-2]) * 1024 * 1024 * 1024  # TiB to KiB
            else:
                # 假设是纯数字（bytes），转换为KiB
                return float(memory_str) / 1024  # bytes to KiB
        except:
            return 0.0
    
    def _parse_memory(self, memory_str: str) -> float:
        """
        解析内存值为Mi
        
        Args:
            memory_str: 内存字符串，如 "512Mi", "1Gi", "1024"
            
        Returns:
            Mi数值
        """
        try:
            if not memory_str or memory_str == "0":
                return 0.0
            
            memory_str = str(memory_str)
            if memory_str.endswith('Ki'):
                return float(memory_str[:-2]) / 1024
            elif memory_str.endswith('Mi'):
                return float(memory_str[:-2])
            elif memory_str.endswith('Gi'):
                return float(memory_str[:-2]) * 1024
            elif memory_str.endswith('Ti'):
                return float(memory_str[:-2]) * 1024 * 1024
            else:
                # 假设是字节，转换为Mi
                return float(memory_str) / (1024 * 1024)
        except:
            return 0.0
    
    def list_jobs(self, user_code: str = None, project: str = None) -> List[Dict]:
        """
        查询job列表
        
        Args:
            user_code: 用户代码（可选）
            project: 项目名称（可选）
            
        Returns:
            job列表
        """
        try:
            # 构建标签选择器
            label_selector = []
            if user_code:
                label_selector.append(f"user-code={user_code}")
            if project:
                label_selector.append(f"user-project={project}")
            
            # **核心变更**：始终按环境筛选
            label_selector.append(f"deepwiki-env={self.environment}")
            
            selector = ",".join(label_selector) if label_selector else None
            
            response = self._batch_v1_api.list_namespaced_job(
                namespace=self.namespace,
                label_selector=selector
            )
            
            jobs = []
            for job in response.items:
                job_info = self._format_job_info(job)
                # 获取关联的pod信息
                pods_info = self.get_job_pods(job.metadata.name)
                job_info['pods'] = pods_info
                jobs.append(job_info)
            
            return jobs
            
        except Exception as e:
            logger.error(f"查询Job列表时发生错误: {e}")
            raise
 
    def list_jobs_paginated(self, user_code: str = None, project: str = None, limit: int = 50, continue_token: Optional[str] = None) -> Dict:
        """
        分页查询job列表（利用k8s SDK的limit/_continue能力）

        Args:
            user_code: 用户代码（可选）
            project: 项目名称（可选）
            limit: 每页条数，默认50
            continue_token: 翻页令牌（来自上一页响应的continue_token）

        Returns:
            {
                "jobs": List[Dict],                 # 当前页job信息
                "continue_token": Optional[str],    # 下一页令牌（如果没有更多则为None）
                "remaining_item_count": Optional[int]  # 可能由服务器返回的剩余条数（取决于集群支持）
            }
        """
        try:
            # 构建标签选择器（保持与非分页接口一致，并固定按环境筛选）
            label_selector = []
            if user_code:
                label_selector.append(f"user-code={user_code}")
            if project:
                label_selector.append(f"user-project={project}")
            label_selector.append(f"deepwiki-env={self.environment}")

            selector = ",".join(label_selector) if label_selector else None

            response = self._batch_v1_api.list_namespaced_job(
                namespace=self.namespace,
                label_selector=selector,
                limit=int(limit) if limit and limit > 0 else None,
                _continue=continue_token
            )

            jobs: List[Dict] = []
            for job in response.items:
                job_info = self._format_job_info(job)
                pods_info = self.get_job_pods(job.metadata.name)
                job_info["pods"] = pods_info
                jobs.append(job_info)

            # 兼容不同客户端对continue字段命名
            next_token = None
            try:
                next_token = getattr(response.metadata, "_continue", None)
                if not next_token:
                    next_token = getattr(response.metadata, "continue", None)
            except Exception:
                next_token = None

            remaining_item_count = None
            try:
                remaining_item_count = getattr(response.metadata, "remaining_item_count", None)
            except Exception:
                remaining_item_count = None

            return {
                "jobs": jobs,
                "continue_token": next_token,
                "remaining_item_count": remaining_item_count,
            }

        except Exception as e:
            logger.error(f"分页查询Job列表时发生错误: {e}")
            raise

    def get_current_container_count(self) -> int:
        """
        获取当前容器（Job）数量
        
        Returns:
            当前Job数量
        """
        try:
            jobs = self.list_jobs()
            return len(jobs)
        except Exception as e:
            logger.error(f"获取当前容器数量失败: {e}")
            return 0

    def get_max_containers(self) -> int:
        """获取配置中的最大容器数量上限"""
        try:
            return self.k8s_config.get("job", {}).get("max_containers", 30)
        except Exception:
            return 30

    def find_evictable_job(self) -> Optional[Dict]:
        """
        查找一个可被驱逐的Job（基于 last_access_time）：
        - 需满足 now - last_access_time > min_occupancy_minutes
        - 在符合条件的Job中，选择 last_access_time 最早（最久未访问）的一个
        
        Returns:
            可驱逐的job信息字典，若无则返回None
        """
        try:
            job_cfg = self.k8s_config.get("job", {})
            min_occupancy_minutes = job_cfg.get("min_occupancy_minutes", 5)
            min_occupancy = timedelta(minutes=min_occupancy_minutes)
            now = datetime.now(timezone.utc)

            jobs = self.list_jobs()
            candidates: List[Dict] = []

            for job in jobs:
                # 应用级沙盒不参与驱逐
                try:
                    if (job.get('labels') or {}).get('sandbox-type') == 'app':
                        continue
                except Exception:
                    pass
                # 严格基于最后访问时间计算占用时间
                last_access_str = job.get('last_access_time') or job.get('annotations', {}).get('last-access-time')
                if not last_access_str:
                    # 没有最后访问时间则不参与驱逐，避免误删
                    continue
                try:
                    last_access_dt = datetime.fromisoformat(last_access_str.replace('Z', '+00:00'))
                except Exception:
                    # 无法解析则跳过该job，避免误删
                    continue

                idle_duration = now - (last_access_dt if getattr(last_access_dt, 'tzinfo', None) else last_access_dt.replace(tzinfo=timezone.utc))
                if idle_duration <= min_occupancy:
                    # 未过最小占用安全期
                    continue

                # 仅用于排序的 tiebreaker，不参与判定
                creation_time = job.get('creation_time')
                creation_dt = creation_time if getattr(creation_time, 'tzinfo', None) else (creation_time.replace(tzinfo=timezone.utc) if creation_time else None)

                candidates.append({
                    **job,
                    '_parsed_last_access': last_access_dt,
                    '_parsed_creation': creation_dt or datetime.max.replace(tzinfo=timezone.utc),
                })

            if not candidates:
                return None

            # 选择 last_access 最早，若相同则 creation_time 更早者
            candidates.sort(key=lambda j: (j['_parsed_last_access'], j['_parsed_creation']))
            chosen = candidates[0]
            # 清理辅助字段
            chosen.pop('_parsed_last_access', None)
            chosen.pop('_parsed_creation', None)
            logger.info(f"选择可驱逐Job: {chosen.get('name')} (last_access={chosen.get('last_access_time')})")
            return chosen

        except Exception as e:
            logger.error(f"查找可驱逐Job失败: {e}")
            return None

    def cleanup_idle_jobs(self) -> List[Dict]:
        """
        清理空闲/超期的job（严格基于 last_access_time），返回被删除的Job信息列表
        
        Returns:
            List[Dict]: 被删除的job列表（至少包含 name 与 user_code）
        """
        try:
            idle_timeout = timedelta(minutes=self.k8s_config.get("job", {}).get("max_lifetime_minutes", 30))
            current_time = datetime.now(timezone.utc)
            
            jobs = self.list_jobs()
            deleted_jobs: List[Dict] = []
            
            for job in jobs:
                # 应用级沙盒不参与自动清理
                try:
                    if (job.get('labels') or {}).get('sandbox-type') == 'app':
                        continue
                except Exception:
                    pass
                # 严格基于最后访问时间判断是否空闲
                last_access_str = job.get('last_access_time') or job.get('annotations', {}).get('last-access-time')
                if not last_access_str:
                    # 无最后访问时间则跳过，避免因创建时间误删正在使用的Job
                    continue
                try:
                    last_access = datetime.fromisoformat(last_access_str.replace('Z', '+00:00'))
                    last_access = last_access if getattr(last_access, 'tzinfo', None) else last_access.replace(tzinfo=timezone.utc)
                except ValueError:
                    logger.warning(f"无法解析最后访问时间: {last_access_str}")
                    continue

                if current_time - last_access > idle_timeout:
                    if self.delete_job(job['name']):
                        deleted_jobs.append({
                            "name": job['name'],
                            "user_code": job.get('user_code') or job.get('annotations', {}).get('user.code') or "",
                        })
                        logger.info(f"清理空闲Job: {job['name']}")
            
            if deleted_jobs:
                logger.info(f"清理了 {len(deleted_jobs)} 个空闲Job")
            
            return deleted_jobs
            
        except Exception as e:
            logger.error(f"清理空闲Job时发生错误: {e}")
            return []

    def cleanup_idle_jobs_for_external_sandbox(self) -> List[Dict]:
        """
        清理空闲/超期的job（严格基于 last_access_time），返回被删除的Job信息列表
        使用 job_pool_service 的 release_job 方法来释放 Job
        
        Returns:
            List[Dict]: 被删除的job列表（至少包含 name 与 user_code）
        """
        try:
            from api.service.job_pool_service import get_job_pool_service
            
            idle_timeout = timedelta(minutes=self.k8s_config.get("job", {}).get("max_lifetime_minutes", 30))
            current_time = datetime.now(timezone.utc)
            
            jobs = self.list_jobs()
            deleted_jobs: List[Dict] = []
            
            for job in jobs:
                # 应用级沙盒不参与自动清理
                try:
                    if (job.get('labels') or {}).get('sandbox-type') == 'app':
                        continue
                except Exception:
                    pass
                # 严格基于最后访问时间判断是否空闲
                last_access_str = job.get('last_access_time') or job.get('annotations', {}).get('last-access-time')
                if not last_access_str:
                    # 无最后访问时间则跳过，避免因创建时间误删正在使用的Job
                    continue
                try:
                    last_access = datetime.fromisoformat(last_access_str.replace('Z', '+00:00'))
                    last_access = last_access if getattr(last_access, 'tzinfo', None) else last_access.replace(tzinfo=timezone.utc)
                except ValueError:
                    logger.warning(f"无法解析最后访问时间: {last_access_str}")
                    continue

                if current_time - last_access > idle_timeout:
                    job_name = job['name']
                    annotations = job.get('annotations', {})
                    
                    # 尝试从 annotations 中提取 user_id 和 wiki_id
                    user_id_str = annotations.get('user.id')
                    wiki_id_str = annotations.get('wiki.id')
                    
                    released = False
                    
                    # 如果能提取到 user_id 和 wiki_id，使用 release_job
                    if user_id_str and wiki_id_str:
                        try:
                            user_id = int(user_id_str)
                            wiki_id = int(wiki_id_str)
                            
                            job_pool_service = get_job_pool_service()
                            success, error_msg = job_pool_service.release_job(user_id, wiki_id)
                            
                            if success:
                                released = True
                                logger.info(f"通过 release_job 释放空闲Job: {job_name} (user_id={user_id}, wiki_id={wiki_id})")
                                from api.sandbox.sandbox_service import sandbox_service
                                sandbox_service.decrement_user_quota(job.get('user_code') or annotations.get('user.code') or "")
                            else:
                                logger.warning(f"release_job 失败: {job_name}, 错误: {error_msg}")
                        except (ValueError, TypeError) as e:
                            logger.warning(f"无法转换 user_id 或 wiki_id: {e}")
                    
                    if released:
                        deleted_jobs.append({
                            "name": job_name,
                            "user_code": job.get('user_code') or annotations.get('user.code') or "",
                        })
            
            if deleted_jobs:
                logger.info(f"清理了 {len(deleted_jobs)} 个空闲Job")
            
            return deleted_jobs
            
        except Exception as e:
            logger.error(f"清理空闲Job时发生错误: {e}")
            return []
    
    def update_job_access_time(self, job_name: str) -> bool:
        """
        更新job的最后访问时间
        
        Args:
            job_name: job名称
            
        Returns:
            是否更新成功
        """
        try:
            # 获取当前job
            job = self._batch_v1_api.read_namespaced_job(
                name=job_name,
                namespace=self.namespace
            )
            
            # 更新annotations
            if not job.metadata.annotations:
                job.metadata.annotations = {}
            
            job.metadata.annotations['last-access-time'] = datetime.now(timezone.utc).isoformat()
            
            # 更新job
            self._batch_v1_api.patch_namespaced_job(
                name=job_name,
                namespace=self.namespace,
                body=job
            )
            
            return True
            
        except Exception as e:
            logger.error(f"更新Job访问时间失败: {e}")
            return False

    def update_job_metadata(self, job_name: str, annotations: Optional[Dict[str, str]] = None, labels: Optional[Dict[str, str]] = None) -> bool:
        """
        更新或新增Job的metadata信息(annotations和labels)
        
        Args:
            job_name: job名称
            annotations: 要更新/新增的annotations字典,如 {"key1": "value1", "key2": "value2"}
            labels: 要更新/新增的labels字典,如 {"key1": "value1", "key2": "value2"}
            
        Returns:
            是否更新成功
        """
        try:
            # 构建patch body，只包含需要更新的metadata
            patch_body = {
                "metadata": {}
            }
            
            # 添加annotations到patch body
            if annotations:
                formatted_annotations = {}
                for key, value in annotations.items():
                    formatted_annotations[key] = str(value)
                patch_body["metadata"]["annotations"] = formatted_annotations
                logger.info(f"准备更新Job {job_name} 的annotations: {formatted_annotations}")
            
            # 添加labels到patch body
            if labels:
                sanitized_labels = {}
                for key, value in labels.items():
                    # 对 git-url 键使用 Git URL 的格式化方式
                    if key == "git-url" or key == "git.url":
                        sanitized_value = self._generate_git_url_label(value)
                    elif key == "user-name" or key == "user.name":
                        sanitized_value = self._sanitize_user_name_for_label(value)
                    else:
                        # 其他label值使用标准的Kubernetes规范清洗
                        sanitized_value = self._sanitize_label_value(value)
                    sanitized_labels[key] = sanitized_value
                patch_body["metadata"]["labels"] = sanitized_labels
                logger.info(f"准备更新Job {job_name} 的labels: {sanitized_labels}")
            
            # 如果没有要更新的内容，直接返回
            if not patch_body["metadata"]:
                logger.warning(f"没有要更新的metadata: {job_name}")
                return True
            
            # 使用strategic merge patch更新job metadata
            self._batch_v1_api.patch_namespaced_job(
                name=job_name,
                namespace=self.namespace,
                body=patch_body
            )
            
            logger.info(f"成功更新Job {job_name} 的metadata")
            return True
            
        except Exception as e:
            logger.error(f"更新Job metadata失败: {e}")
            return False

    def delete_job_metadata(self, job_name: str, annotation_keys: Optional[List[str]] = None, label_keys: Optional[List[str]] = None) -> bool:
        """
        删除Job的metadata信息(annotations和labels)
        
        Args:
            job_name: job名称
            annotation_keys: 要删除的annotation键列表,如 ["key1", "key2"]
            label_keys: 要删除的label键列表,如 ["key1", "key2"]
            
        Returns:
            是否删除成功
        """
        try:
            patch = {"metadata": {}}
            
            if annotation_keys:
                patch["metadata"]["annotations"] = {
                    key: None for key in annotation_keys
                }
            
            if label_keys:
                patch["metadata"]["labels"] = {
                    key: None for key in label_keys
                }
            
            # 使用 Strategic Merge Patch（默认）
            self._batch_v1_api.patch_namespaced_job(
                name=job_name,
                namespace=self.namespace,
                body=patch
            )
            
            logger.info(f"成功删除 Job {job_name} 的 metadata")
            return True
        except Exception as e:
            logger.error(f"删除 Job metadata 失败: {e}")
            return False
        
    def _check_container_limit(self) -> bool:
        """
        检查是否超过容器数量限制
        
        Returns:
            是否可以创建新容器
        """
        try:
            max_containers = self.k8s_config.get("job", {}).get("max_containers", 30)
            current_jobs = self.list_jobs()
            current_count = len(current_jobs)
            
            logger.info(f"当前容器数量: {current_count}, 最大限制: {max_containers}")
            return current_count < max_containers
            
        except Exception as e:
            logger.error(f"检查容器数量限制失败: {e}")
            return False
    
    def _ensure_workspace_directories(self, workspace_host_path: str, project_workspace_host_path: str):
        """
        确保工作空间目录存在，并创建必要的子目录
        
        Args:
            workspace_host_path: 工作空间主机路径
            wiki_type: wiki类型 (1:产品, 2:项目)
        """
        try:
            import os

            # 检查主工作空间目录是否存在，不存在则新建
            if not os.path.exists(workspace_host_path):
                os.makedirs(workspace_host_path, exist_ok=True)
            if not os.path.exists(project_workspace_host_path):
                os.makedirs(project_workspace_host_path, exist_ok=True)

            # 产品wiki: i-doc(新建文档), o-doc(产品文档), workspace, .gemini
            project_subdirs = ['i-doc', 'o-doc', 'project-gemini']
            personal_subdirs = ['userspace', '.gemini']
            
            # 如发现误生成的 code 目录，强制删除
            try:
                import shutil
                code_dir = os.path.join(workspace_host_path, 'code')
                if os.path.isdir(code_dir):
                    shutil.rmtree(code_dir)
                    logger.info(f"检测到残留code目录，已强制删除: {code_dir}")
            except Exception as del_err:
                logger.warning(f"删除code目录失败: {workspace_host_path}/code, 错误: {del_err}")

            for subdir in project_subdirs:
                subdir_path = os.path.join(project_workspace_host_path, subdir)
                if not os.path.exists(subdir_path):
                    os.makedirs(subdir_path, exist_ok=True)

            for subdir in personal_subdirs:
                subdir_path = os.path.join(workspace_host_path, subdir)
                if not os.path.exists(subdir_path):
                    os.makedirs(subdir_path, exist_ok=True)
        except Exception as e:
            logger.error(f"创建工作空间目录失败: {workspace_host_path}, 错误: {e}")
            # 不抛出异常，让容器创建继续，k8s会自动创建DirectoryOrCreate类型的目录


    def _sanitize_label_value(self, value: str, max_length: int = 63) -> str:
        """
        清理和缩短标签值，确保符合Kubernetes标签规范
        
        Args:
            value: 原始标签值
            max_length: 最大长度，默认63
            
        Returns:
            处理后的标签值
        """
        import hashlib
        import re
        
        if not value:
            return "unknown"
        
        # 1. 替换特殊字符为短横线
        sanitized = re.sub(r'[^a-zA-Z0-9\-\_\.]', '-', str(value))
        
        # 2. 移除连续的短横线
        sanitized = re.sub(r'-+', '-', sanitized)
        
        # 3. 确保以字母数字开头和结尾
        sanitized = re.sub(r'^[^a-zA-Z0-9]+', '', sanitized)
        sanitized = re.sub(r'[^a-zA-Z0-9]+$', '', sanitized)
        
        # 4. 如果长度超限，使用前缀+hash的方式
        if len(sanitized) > max_length:
            # 计算原始值的hash
            hash_value = hashlib.md5(value.encode()).hexdigest()[:8]
            
            # 保留有意义的前缀部分
            prefix_length = max_length - 9  # 预留8位hash + 1位连接符
            if prefix_length > 0:
                prefix = sanitized[:prefix_length].rstrip('-_.')
                sanitized = f"{prefix}-{hash_value}"
            else:
                # 如果前缀长度不够，只使用hash
                sanitized = hash_value
        
        # 5. 最终检查，确保不为空
        if not sanitized:
            sanitized = hashlib.md5(value.encode()).hexdigest()[:8]
        
        return sanitized.lower()

    def _sanitize_user_name_for_label(self, user_name: Optional[str]) -> Optional[str]:
        """
        将用户名清洗为符合Kubernetes标签规范的ASCII值
        注解(annotations)使用原始值，标签(labels)使用此方法清洗后的值
        
        Args:
            user_name: 原始用户名（可能包含中文等非ASCII字符）
            
        Returns:
            清洗后的用户名，如果输入为空则返回None
        """
        if not user_name:
            return None
            
        try:
            # 替换非ASCII字符为短横线
            val = re.sub(r'[^A-Za-z0-9\.\-_]', '-', str(user_name))
            # 去除首尾的特殊字符并转小写
            val = val.strip('.-').lower()
            # 限制长度为63字符
            if len(val) > 63:
                val = val[:63].strip('.-')
            # 如果清洗后为空，返回默认值
            return val or "unknown"
        except Exception:
            return "unknown"

    def _generate_git_url_label(self, git_url: str) -> str:
        """
        为Git URL生成合规的Kubernetes标签值
        
        Args:
            git_url: Git仓库URL
            
        Returns:
            处理后的标签值
        """
        try:
            # 提取有意义的部分：域名+路径
            from urllib.parse import urlparse
            parsed = urlparse(git_url)
            
            if parsed.netloc and parsed.path:
                # 组合域名和路径的关键部分
                domain = parsed.netloc.replace('www.', '')
                path = parsed.path.strip('/').replace('.git', '')
                meaningful_part = f"{domain}-{path}"
            else:
                meaningful_part = git_url
                
            return self._sanitize_label_value(meaningful_part)
            
        except Exception:
            # 如果解析失败，直接处理原URL
            return self._sanitize_label_value(git_url)

    def _generate_job_name_from_git(self, user_code: str, git_url: str, branch: str) -> str:
        """
        根据用户代码、Git URL和分支生成Job名称
        
        Args:
            user_code: 用户代码
            git_url: Git仓库URL
            branch: 分支名称
            
        Returns:
            job名称
        """
        import hashlib
        
        # 使用用户代码、Git URL和分支作为唯一标识
        unique_identifier = f"{user_code}#{git_url}#{branch}"
        hash_value = hashlib.md5(unique_identifier.encode()).hexdigest()[:8]
        
        # 保留完整的用户代码，只对特殊字符进行处理
        user_clean = user_code.replace("_", "-").replace(" ", "-").replace(".", "-")
        
        # 生成名称，加入环境标识确保唯一性
        name = f"job-{self.environment}-{user_clean}-{hash_value}".lower()
        
        # 确保名称不超过63字符（Kubernetes资源名称限制）
        if len(name) > 63:
            # 如果超长，保留用户代码的前面部分和hash
            max_user_length = 63 - len(f"job-{self.environment}--{hash_value}")
            if max_user_length > 0:
                user_clean = user_clean[:max_user_length]
                name = f"job-{self.environment}-{user_clean}-{hash_value}".lower()
            else:
                # 如果用户代码太长，只保留hash
                name = f"job-{self.environment}-{hash_value}".lower()
        
        return name

    def generate_job_name(self, id: int) -> str:
        """
        生成job名称

        Args:
            id: 唯一标识
            
        Returns:
            job名称
        """
        
        # 生成名称，加入环境标识确保唯一性
        name = f"job-{self.environment}-{id}".lower()
        
        # 确保名称不超过63字符（Kubernetes资源名称限制）
        if len(name) > 63:
            # 如果超长，保留环境的前面部分和id
            max_user_length = 63 - len(f"job-{self.environment}-{id}")
            if max_user_length > 0:
                id = id[:max_user_length]
                name = f"job-{self.environment}-{id}".lower()
            else:
                # 如果环境太长，只保留id
                name = f"job-{self.environment}-{id}".lower()
        
        return name

    def generate_linux_group_name(self, id: int) -> str:
        """
        生成Linux用户组名称
        """
        return f"wiki-{self.environment}-{id}".lower()

    def find_job(self, user_code: str, wiki_id: int) -> Optional[Dict]:
        logger.info(f"查找job: user_code={user_code}, wiki_id={wiki_id}")
        
        jobs = self._batch_v1_api.list_namespaced_job(
            namespace=self.namespace,
            label_selector=f"user-code={user_code},wiki-id={wiki_id},deepwiki-env={self.environment}"
        )
        if not jobs.items:
            return None
        for job in jobs.items:
            if job.metadata.labels.get("user-code") == user_code and job.metadata.labels.get("wiki-id") == wiki_id:
                return job
        return None

    def find_job_by_annotation(self, user_code: str, wiki_id: int) -> Optional[Dict]:
        logger.info(f"查找job: user_code={user_code}, wiki_id={wiki_id}")
        
        jobs = self._batch_v1_api.list_namespaced_job(
            namespace=self.namespace,
            label_selector=f"user-code={user_code},wiki-id={str(wiki_id)},deepwiki-env={self.environment}"
        )
        if not jobs.items:
            return None
        for job in jobs.items:
            if job:
                annotations = job.metadata.annotations or {}
                if annotations.get("user.code") == user_code and annotations.get("wiki.id") == str(wiki_id):
                    logger.info(f"找到job: {job.metadata.name}")
                    return job
        return None

    def ensure_project_workspace(self, project_workspace_path: str):
        if not os.path.exists(project_workspace_path):
            os.makedirs(project_workspace_path)
        
        project_subdirs = ['i-doc', 'o-doc', 'project-gemini']

        for subdir in project_subdirs:
            subdir_path = os.path.join(project_workspace_path, subdir)
            if not os.path.exists(subdir_path):
                os.makedirs(subdir_path, exist_ok=True)

    def ensure_workspace(self, workspace_path: str):
        if not os.path.exists(workspace_path):
            os.makedirs(workspace_path)
        
        workspace_subdirs = ['userspace', '.gemini']
        for subdir in workspace_subdirs:
            subdir_path = os.path.join(workspace_path, subdir)
            if not os.path.exists(subdir_path):
                os.makedirs(subdir_path, exist_ok=True)
    
    def _generate_job_manifest(self, job_name: str, wct_api_key: str) -> Dict:
        """
        生成job配置清单
        
        Args:
            job_name: job名称
            wct_api_key: WCT API密钥，可选
            linux_username: Linux用户名，如果提供则以该用户身份运行
            linux_uid: Linux用户ID，与linux_username配合使用
            
        Returns:
            job配置字典
        """
        current_time = datetime.now(timezone.utc).isoformat()

        # 计算挂载路径（主机路径）
        # 统一使用主web应用的基础路径：kubernetes.base_path（默认 /root/.adalflow）
        base_code_path = self.k8s_config.get("job", {}).get("volumes", {}).get("code_path", "/app/deepwiki/adalflow/repos/")
        base_workspace_path = self.k8s_config.get("job", {}).get("volumes", {}).get("workspace_path", "/app/deepwiki/adalflow/workspace")
        base_project_workspace_path = self.k8s_config.get("job", {}).get("volumes", {}).get("project_workspace_path", "/app/deepwiki/adalflow/project_workspace")

        gemini_host_path = self.k8s_config.get("job", {}).get("volumes", {}).get("gemini_path", "/app/deepwiki/adalflow/base/.gemini")
        
        # 确保工作空间目录存在，并创建必要的子目录
        # self._ensure_workspace_directories(base_workspace_path, base_project_workspace_path)
        
        # 根据wiki类型按需组装挂载
        # 不使用subPath，而是挂载整个配置目录，通过符号链接来实现文件同步
        volume_mounts = [
            {
                "name": "code-volume",
                "mountPath": "/data/workspace/deepwiki-c",
                "readOnly": True
            },
            {
                "name": "g-doc-volume",
                "mountPath": "/data/workspace/g-doc",
                "readOnly": True
            },
            {
                "name": "workspace-volume",
                "mountPath": "/data/workspace/deepwiki-w"
            },
            {
                "name": "project-workspace-volume",
                "mountPath": "/data/workspace/deepwiki-pw"
            },
            {
                "name": "gemini-volume",
                "mountPath": "/home/<USER>/.gemini"
            }
        ]

        volumes = [
            {
                "name": "code-volume",
                "hostPath": {
                    "path": base_code_path,
                    "type": "Directory"
                }
            },
            {
                "name": "g-doc-volume",
                "hostPath": {
                    "path": self.k8s_config.get("job", {}).get("volumes", {}).get("g_doc_path", "/app/deepwiki/adalflow/g-doc"),
                    "type": "DirectoryOrCreate"
                }
            },
            {
                "name": "project-workspace-volume",
                "hostPath": {
                    "path": f"{base_project_workspace_path.rstrip('/')}",
                    "type": "Directory"
                }
            },
            {
                "name": "workspace-volume",
                "hostPath": {
                    "path": f"{base_workspace_path.rstrip('/')}",
                    "type": "Directory"
                }
            },         
            {
                "name": "gemini-volume",
                "hostPath": {
                    "path": gemini_host_path,
                    "type": "DirectoryOrCreate"
                }
            }
        ]

        manifest = {
            "apiVersion": "batch/v1",
            "kind": "Job",
            "metadata": {
                "name": job_name,
                "namespace": self.namespace,
                "annotations": {
                    "sidecar.istio.io/inject": "false",
                    "zcm.iwhalecloud.com/managed-by": "zcm",
                    "created-time": current_time,
                    "last-access-time": current_time,
                },
                "labels": {
                    "app": job_name,
                    "version": "1.0",
                    "zcm-app": job_name,
                    "deepwiki-env": self.environment, # 添加环境标签
                }
            },
            "spec": {
                "backoffLimit": 4,
                "ttlSecondsAfterFinished": 3600,
                "template": {
                    "metadata": {
                        "annotations": {
                            "zcm.iwhalecloud.com/app-id": "337753",
                            "zcm.iwhalecloud.com/project-id": "564272",
                        },
                        "labels": {
                            "app": job_name,
                            "version": "1.0",
                            "zcm-app": job_name,
                            "deepwiki-env": self.environment, # 添加环境标签
                        }
                    },
                    "spec": {
                        "restartPolicy": "Never",
                        "containers": [{
                            "name": f"{job_name}-c",
                            "image": self.k8s_config.get("job", {}).get("default_image", "hub-nj.iwhalecloud.com/ptdev01/wct-cli-api-patch:D_20250730141530"),
                            "imagePullPolicy": "IfNotPresent",
                            "securityContext": {
                            "capabilities": {
                                "add": ["SETGID"]
                            }
                        },
                            "env": [
                                {"name": "ZCM_SERVER_PORT", "value": "52000"},
                                {"name": "DOCKER_IMAGE", "value": self.k8s_config.get("job", {}).get("default_image", "hub-nj.iwhalecloud.com/ptdev01/wct-cli-api-patch:D_20250730141530")},
                                {"name": "ZCM_PRJ_ID", "value": "564272"},
                                {"name": "ZCM_SERVER_VIP", "value": "************"},
                                {"name": "APP_NAME", "value": "wct-cli-api-patch"},
                                {"name": "PROJECT_NAME", "value": "平台技术01团队"},
                                {"name": "CLOUD_APP_NAME", "value": f"平台技术01团队_{job_name}"},
                                {"name": "APM_INIT", "value": "https://cos.iwhalecloud.com/cos-eaa4-x/apm.sh"},
                                {"name": "ZCM_SERVER_SSL_PORT", "value": "25000"},
                                {"name": "WCT_API_KEY", "value": wct_api_key},
                                {"name": "CLOUD_APP_ID", "value": "337753"},
                                {"name": "CLOUD_APP_TYPE", "value": "STATELESS"},
                                {"name": "TOMCAT_DELAY_EXIT_TIMES", "value": "30"},
                                {"name": "TZ", "value": "Asia/Shanghai"},
                                {"name": "WCT_API_REQUEST_TIMEOUT", "value": str(self.k8s_config.get("job", {}).get("wct_timeouts", {}).get("request_timeout", 300) * 1000)},
                                {"name": "WCT_API_TOOL_TIMEOUT", "value": str(self.k8s_config.get("job", {}).get("wct_timeouts", {}).get("tool_timeout", 60) * 1000)},
                                {"name": "IS_APP", "value": "false"}
                            ] + [
                                {"name": key, "value": value} 
                                for key, value in self.k8s_config.get("job", {}).get("env", {}).items()
                            ],
                            "ports": [
                                {"containerPort": 80, "name": "port-80", "protocol": "TCP"},
                                {"containerPort": 3000, "name": "port-3000", "protocol": "TCP"}
                            ],
                            "resources": self.k8s_config.get("job", {}).get("resources", {
                                "limits": {"cpu": "100m", "memory": "256Mi"},
                                "requests": {"cpu": "10m", "memory": "256Mi"}
                            }),
                            "volumeMounts": volume_mounts
                        }],
                        "volumes": volumes,
                        "nodeSelector": self.k8s_config.get("job", {}).get("node_selector", {"deepwiki-open-dev-01": "deepwiki-open-dev-01"}),
                        "securityContext": {},
                        "terminationGracePeriodSeconds": 33
                    }
                }
            }
        }
        
        return manifest
    
    def _format_job_info(self, job) -> Dict:
        """
        格式化job信息
        
        Args:
            job: k8s job对象
            
        Returns:
            格式化的job信息
        """
        annotations = job.metadata.annotations or {}
        labels = job.metadata.labels or {}
        mounts = self._extract_job_mounts(job)
    
        git_url = annotations.get("git.url", "")
        branch = annotations.get("git.branch", "")
        
        return {
            "name": job.metadata.name,
            "namespace": job.metadata.namespace,
            "user_code": annotations.get("user.code", ""),
            "git_url": git_url,
            "branch": branch,
            "creation_time": job.metadata.creation_timestamp,
            "last_access_time": annotations.get("last-access-time"),
            "status": self._get_job_status(job),
            "labels": labels,
            "annotations": annotations,
            "mounts": mounts,
            "user_name": annotations.get("user.name", "")
        }

    def _extract_job_mounts(self, job) -> List[Dict]:
        """
        从Job模板中提取卷挂载映射（容器路径 -> 主机路径）
        返回示例: [{"volume_name":"code-volume","container_path":"/data/workspace/code","host_path":"/host/path","read_only":True}]
        """
        results: List[Dict] = []
        try:
            tpl = getattr(job.spec, 'template', None)
            spec = getattr(tpl, 'spec', None) if tpl else None
            if not spec:
                return results

            # volumes: name -> host_path
            volume_host_map: Dict[str, str] = {}
            for v in getattr(spec, 'volumes', []) or []:
                v_name = getattr(v, 'name', None)
                host_path_obj = getattr(v, 'host_path', None)
                host_path_val = getattr(host_path_obj, 'path', None) if host_path_obj else None
                if v_name:
                    volume_host_map[v_name] = host_path_val

            containers = getattr(spec, 'containers', []) or []
            if not containers:
                return results
            c0 = containers[0]
            for vm in getattr(c0, 'volume_mounts', []) or []:
                vol_name = getattr(vm, 'name', '')
                mount_path = getattr(vm, 'mount_path', '')
                read_only = getattr(vm, 'read_only', False)
                results.append({
                    "volume_name": vol_name,
                    "container_path": mount_path,
                    "host_path": volume_host_map.get(vol_name),
                    "read_only": bool(read_only)
                })
        except Exception as e:
            logger.warning(f"提取Job挂载信息失败: {e}")
        return results

    def _make_app_safe_code(self, app_code: str) -> str:
        """
        生成可用于k8s资源/标签的安全应用代码标识。
        """
        try:
            return self._sanitize_label_value(app_code)
        except Exception:
            return str(app_code).lower() if app_code else "unknown"

    def _make_app_base_name(self, app_code: str) -> str:
        """
        构造应用级沙盒稳定前缀名称（不含版本/修订后缀）。
        例如: app-job-{env}-{safe_code}
        """
        safe_code = self._make_app_safe_code(app_code)
        return f"app-job-{self.environment}-{safe_code}".lower()

    def _compute_app_series(self, app_id: Optional[str], app_code: str) -> str:
        """
        计算同名app的系列ID，解决同app_code在不同实体上的命名冲突。
        基于 app_id 与 app_code 的hash，长度8。
        """
        import hashlib
        base = f"{app_id or ''}:{app_code or ''}"
        return hashlib.md5(base.encode()).hexdigest()[:8]

    def list_app_jobs(self, app_code: str, app_id: Optional[str] = None) -> List[Dict]:
        """
        按应用标签列出应用级沙盒的所有Job，按创建时间倒序返回。
        会自动根据 deepwiki-env 与 sandbox-type=app 过滤。
        若提供 app_id，则同时按 app-series 精确过滤，避免重名冲突。
        """
        try:
            label_selector: List[str] = [
                "sandbox-type=app",
                f"deepwiki-env={self.environment}",
                f"app-code={self._make_app_safe_code(app_code)}",
            ]
            if app_id is not None:
                series = self._compute_app_series(app_id, app_code)
                label_selector.append(f"app-series={series}")

            selector = ",".join(label_selector)
            response = self._batch_v1_api.list_namespaced_job(
                namespace=self.namespace,
                label_selector=selector
            )

            jobs: List[Dict] = []
            for job in response.items:
                jobs.append(self._format_job_info(job))

            # 按创建时间倒序
            def _key(j):
                ct = j.get("creation_time")
                return ct if getattr(ct, 'tzinfo', None) else (ct.replace(tzinfo=timezone.utc) if ct else datetime.min.replace(tzinfo=timezone.utc))

            jobs.sort(key=_key, reverse=True)
            return jobs
        except Exception as e:
            logger.error(f"列出应用Job失败: {e}")
            return []

    def wait_for_job_ready(self, job_name: str, timeout_seconds: int = 300, poll_interval: int = 3, require_http_ready: bool = True) -> bool:
        """
        等待指定Job的Pod运行并容器就绪。
        就绪条件：
        - 容器层面：存在Running的Pod，且所有container ready；并且
        - 应用层面（可选）：Pod的 http://{pod_ip}:3000/v1/chat/sessions 返回 200
        """
        import time
        import requests
        end_time = time.time() + max(int(timeout_seconds), 1)
        try:
            while time.time() < end_time:
                pods = self.get_job_pods(job_name) or []
                for pod in pods:
                    if pod.get("status") == "Running":
                        statuses = pod.get("container_statuses") or []
                        if statuses and all(bool(s.get("ready")) for s in statuses):
                            if not require_http_ready:
                                return True
                            # 应用层健康检查
                            pod_ip = pod.get("pod_ip")
                            if not pod_ip:
                                continue
                            try:
                                url = f"http://{pod_ip}:3000/v1/chat/sessions"
                                resp = requests.get(url, timeout=5)
                                if getattr(resp, 'status_code', None) == 200:
                                    return True
                            except Exception:
                                pass
                time.sleep(max(int(poll_interval), 1))
        except Exception as e:
            logger.warning(f"等待Job就绪异常: {job_name}, {e}")
        return False
    
    def get_pod_metrics(self, pod_name: str) -> Optional[Dict]:
        """
        获取Pod的实时CPU和内存使用情况
        
        Args:
            pod_name: Pod名称
            
        Returns:
            Pod的metrics信息，包含CPU和内存使用情况
        """
        try:
            if not self._metrics_api:
                logger.warning("Metrics API客户端未初始化，无法获取实时metrics")
                return None
            
            # 获取Pod metrics
            metrics = self._metrics_api.get_namespaced_custom_object(
                group="metrics.k8s.io",
                version="v1beta1",
                namespace=self.namespace,
                plural="pods",
                name=pod_name
            )
            
            logger.debug(f"Raw metrics for pod {pod_name}: {metrics}")
            
            if not metrics or 'containers' not in metrics:
                logger.warning(f"No metrics data found for pod {pod_name}")
                return None
            
            # 提取CPU和内存使用情况
            container_metrics = []
            total_cpu_usage = 0
            total_memory_usage = 0
            
            for container in metrics['containers']:
                container_name = container['name']
                usage = container.get('usage', {})
                                
                # 解析CPU使用量（Metrics API返回的是nano cores）
                cpu_usage_str = usage.get('cpu', '0')
                cpu_usage = self._parse_metrics_cpu(cpu_usage_str)
                total_cpu_usage += cpu_usage
                
                logger.debug(f"Container {container_name} CPU: {cpu_usage_str} -> {cpu_usage}m")
                
                # 解析内存使用量（Metrics API返回的是bytes）
                memory_usage_str = usage.get('memory', '0')
                memory_usage = self._parse_metrics_memory(memory_usage_str)
                total_memory_usage += memory_usage
                
                logger.debug(f"Container {container_name} Memory: {memory_usage_str} -> {memory_usage} bytes")
                                
                container_metrics.append({
                    "name": container_name,
                    "cpu_usage": cpu_usage,
                    "cpu_usage_str": cpu_usage_str,
                    "memory_usage": memory_usage,
                    "memory_usage_str": memory_usage_str
                })
            
            return {
                "pod_name": pod_name,
                "timestamp": metrics.get('timestamp'),
                "window": metrics.get('window'),
                "containers": container_metrics,
                "total_cpu_usage": total_cpu_usage,
                "total_memory_usage": total_memory_usage,
                "total_cpu_usage_m": total_cpu_usage,  # 明确单位
                "total_memory_usage_kib": total_memory_usage  # 明确单位
            }
            
        except Exception as e:
            logger.error(f"获取Pod {pod_name} metrics失败: {e}")
            return None
    
    def get_job_metrics(self, job_name: str) -> Optional[Dict]:
        """
        获取Job的实时CPU和内存使用情况
        
        Args:
            job_name: Job名称
            
        Returns:
            Job的metrics信息，包含所有Pod的CPU和内存使用情况
        """
        try:
            # 获取Job关联的所有Pod
            pods = self.get_job_pods(job_name)
            if not pods:
                return None
            
            job_metrics = {
                "job_name": job_name,
                "pods": [],
                "total_cpu_usage": 0,
                "total_memory_usage": 0,
                "pod_count": len(pods)
            }
            
            for pod in pods:
                pod_name = pod['name']
                pod_metrics = self.get_pod_metrics(pod_name)
                
                if pod_metrics:
                    job_metrics["pods"].append(pod_metrics)
                    job_metrics["total_cpu_usage"] += pod_metrics["total_cpu_usage"]
                    job_metrics["total_memory_usage"] += pod_metrics["total_memory_usage"]
            
            return job_metrics
            
        except Exception as e:
            logger.error(f"获取Job {job_name} metrics失败: {e}")
            return None
    
    def _get_job_status(self, job) -> str:
        """
        获取job状态
        
        Args:
            job: k8s job对象
            
        Returns:
            job状态字符串
        """
        if job.status.succeeded:
            return "succeeded"
        elif job.status.failed:
            return "failed"
        elif job.status.active:
            return "running"
        else:
            return "pending"

    def check_job_alive_by_params(self, user_code: str, git_url: str, branch: str) -> bool:
        """
        按用户代码、Git URL和分支查询job是否存活
        
        Args:
            user_code: 用户代码
            git_url: Git仓库URL
            branch: 分支名称
            
        Returns:
            job是否存活
        """
        try:
            # 生成job名称
            job_name = self._generate_job_name_from_git(user_code, git_url, branch)
            
            # 查询job信息
            job_info = self.get_job(job_name)
            if not job_info:
                logger.info(f"Job不存在: {job_name}")
                return False
            
            # 检查job状态
            status = job_info.get('status', 'unknown')
            if status in ['running', 'active']:
                logger.info(f"Job存活: {job_name}, 状态: {status}")
                return True
            else:
                logger.info(f"Job不存活: {job_name}, 状态: {status}")
                return False
                
        except Exception as e:
            logger.error(f"查询Job存活状态失败: {e}")
            return False
    
    def get_job_ip_by_params(self, user_code: str, git_url: str, branch: str, job_name: Optional[str] = None) -> Optional[str]:
        """
        按用户代码、Git URL和分支查询job的IP地址
        
        Args:
            user_code: 用户代码
            git_url: Git仓库URL
            branch: 分支名称
            
        Returns:
            job的IP地址，如果不存在或没有运行中的Pod则返回None
        """
        try:
            # 生成job名称
            if not job_name:
                job_name = self._generate_job_name_from_git(user_code, git_url, branch)
            
            # 获取job关联的pods
            pods_info = self.get_job_pods(job_name)
            if not pods_info:
                logger.info(f"Job {job_name} 没有关联的Pod")
                return None
            
            # 查找运行中的Pod并获取其IP
            for pod_info in pods_info:
                pod_status = pod_info.get('status', '')
                pod_ip = pod_info.get('pod_ip', '')
                
                # 检查Pod是否运行中且有IP
                if pod_status == 'Running' and pod_ip:
                    logger.info(f"找到运行中的Pod: {pod_info['name']}, IP: {pod_ip}")
                    return pod_ip
            
            logger.info(f"Job {job_name} 没有运行中的Pod或Pod没有IP")
            return None
            
        except Exception as e:
            logger.error(f"查询Job IP失败: {e}")
            return None

    def get_job_detailed_status(self, user_code: str, git_url: str, branch: str, job_name: Optional[str] = None, language: str = "zh") -> Dict:
        """
        获取job的详细状态
        
        Args:
            user_code: 用户代码
            git_url: Git仓库URL
            branch: 分支名称
            language: 语言代码，如果为None则从上下文获取
            job_name: job名称
        Returns:
            包含详细状态信息的字典
        """
        import requests
        import time
        
        try:
            # 生成job名称
            if not job_name:
                job_name = self._generate_job_name_from_git(user_code, git_url, branch)
            
            # 查询job信息
            job_info = self.get_job(job_name)
            if not job_info:
                return {
                    "status": SandboxStatus.NOT_CREATED.value,
                    "status_description": get_translation("sandbox.status.description.NOT_CREATED", language),
                    "job_name": job_name,
                    "message": get_translation("sandbox.status.message.NOT_CREATED", language),
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            mounts = job_info.get('mounts')
            
            # 获取pod信息
            pods_info = self.get_job_pods(job_name)
            if not pods_info:
                return {
                    "status": SandboxStatus.CREATING.value,
                    "status_description": get_translation("sandbox.status.description.CREATING", language),
                    "job_name": job_name,
                    "message": get_translation("sandbox.status.message.CREATING", language),
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "mounts": mounts
                }
            
            # 检查pod状态
            running_pods = []
            for pod_info in pods_info:
                pod_status = pod_info.get('status', '')
                pod_ip = pod_info.get('pod_ip', '')
                
                if pod_status == 'Running' and pod_ip:
                    running_pods.append(pod_info)
            
            if not running_pods:
                # 检查是否有pod在启动中
                pending_pods = [pod for pod in pods_info if pod.get('status') in ['Pending', 'ContainerCreating']]
                if pending_pods:
                    return {
                        "status": SandboxStatus.CREATING.value,
                        "status_description": get_translation("sandbox.status.description.CREATING", language),
                        "job_name": job_name,
                        "message": get_translation("sandbox.status.podStarting", language),
                        "pod_status": [pod.get('status') for pod in pending_pods],
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "mounts": mounts
                    }
                else:
                    # 检查是否有失败的pod
                    failed_pods = [pod for pod in pods_info if pod.get('status') in ['Failed', 'Error']]
                    if failed_pods:
                        return {
                            "status": SandboxStatus.FAILED.value,
                            "status_description": get_translation("sandbox.status.description.FAILED", language),
                            "job_name": job_name,
                            "message": get_translation("sandbox.status.message.FAILED", language),
                            "pod_status": [pod.get('status') for pod in failed_pods],
                            "timestamp": datetime.now(timezone.utc).isoformat(),
                            "mounts": mounts
                        }
                    else:
                        return {
                            "status": SandboxStatus.CREATING.value,
                            "status_description": get_translation("sandbox.status.description.CREATING", language),
                            "job_name": job_name,
                            "message": get_translation("sandbox.status.podStatusUnknown", language),
                            "pod_status": [pod.get('status') for pod in pods_info],
                            "timestamp": datetime.now(timezone.utc).isoformat(),
                            "mounts": mounts
                        }
            
            # 有运行中的pod，检查容器内服务状态
            for pod_info in running_pods:
                pod_ip = pod_info.get('pod_ip')
                if not pod_ip:
                    continue
                
                # 检查容器状态
                container_statuses = pod_info.get('container_statuses', [])
                if container_statuses:
                    # 检查容器是否就绪
                    ready_containers = [cs for cs in container_statuses if cs.get('ready', False)]
                    if len(ready_containers) != len(container_statuses):
                        return {
                            "status": SandboxStatus.INITIALIZING.value,
                            "status_description": get_translation("sandbox.status.description.INITIALIZING", language),
                            "job_name": job_name,
                            "message": get_translation("sandbox.status.containerNotReady", language),
                            "pod_ip": pod_ip,
                            "container_statuses": container_statuses,
                            "timestamp": datetime.now(timezone.utc).isoformat(),
                            "mounts": mounts
                        }
                
                # 尝试调用容器内的API接口
                try:
                    session_url = f"http://{pod_ip}:3000/v1/chat/sessions"
                    response = requests.get(session_url, timeout=5)
                    
                    if response.status_code == 200:
                        return {
                            "status": SandboxStatus.READY.value,
                            "status_description": get_translation("sandbox.status.description.READY", language),
                            "job_name": job_name,
                            "message": get_translation("sandbox.status.message.READY", language),
                            "pod_ip": pod_ip,
                            "api_response": get_translation("sandbox.status.normal", language),
                            "timestamp": datetime.now(timezone.utc).isoformat(),
                            "mounts": mounts
                        }
                    else:
                        return {
                            "status": SandboxStatus.INITIALIZING.value,
                            "status_description": get_translation("sandbox.status.description.INITIALIZING", language),
                            "job_name": job_name,
                            "message": get_translation("sandbox.status.apiStartingWithCode", language).format(code=response.status_code),
                            "pod_ip": pod_ip,
                            "api_response": get_translation("sandbox.status.httpStatus", language).format(code=response.status_code),
                            "timestamp": datetime.now(timezone.utc).isoformat(),
                            "mounts": mounts
                        }
                        
                except requests.exceptions.ConnectionError:
                    return {
                        "status": SandboxStatus.INITIALIZING.value,
                        "status_description": get_translation("sandbox.status.description.INITIALIZING", language),
                        "job_name": job_name,
                        "message": get_translation("sandbox.status.apiConnectionFailed", language),
                        "pod_ip": pod_ip,
                        "api_response": get_translation("sandbox.status.normal", language),
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "mounts": mounts
                    }
                except requests.exceptions.Timeout:
                    return {
                        "status": SandboxStatus.INITIALIZING.value,
                        "status_description": get_translation("sandbox.status.description.INITIALIZING", language),
                        "job_name": job_name,
                        "message": get_translation("sandbox.status.apiTimeout", language),
                        "pod_ip": pod_ip,
                        "api_response": get_translation("sandbox.status.requestTimeout", language),
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "mounts": mounts
                    }
                except Exception as e:
                    return {
                        "status": SandboxStatus.INITIALIZING.value,
                        "status_description": get_translation("sandbox.status.description.INITIALIZING", language),
                        "job_name": job_name,
                        "message": get_translation("sandbox.status.apiException", language).format(error=str(e)),
                        "pod_ip": pod_ip,
                        "api_response": get_translation("sandbox.status.exception", language).format(error=str(e)),
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "mounts": mounts
                    }
            
            # 如果没有可用的pod IP，返回初始化状态
            return {
                "status": SandboxStatus.INITIALIZING.value,
                "status_description": get_translation("sandbox.status.description.INITIALIZING", language),
                "job_name": job_name,
                "message": get_translation("sandbox.status.podNoIp", language),
                "pod_status": [pod.get('status') for pod in running_pods],
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "mounts": mounts
            }
            
        except Exception as e:
            logger.error(f"获取Job详细状态失败: {e}")
            return {
                "status": SandboxStatus.QUERY_FAILED.value,
                "status_description": get_translation("sandbox.status.description.QUERY_FAILED", language),
                "job_name": job_name if 'job_name' in locals() else get_translation("sandbox.status.unknown", language),
                "message": get_translation("sandbox.status.queryException", language).format(error=str(e)),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

    def get_app_job_detailed_status(self, app_code: str, app_id: Optional[str] = None, language: str = "zh") -> Dict:
        """获取应用级沙盒Job的详细状态。"""
        from api.middleware.language_middleware import get_translation
        from api.type.sandbox_status import SandboxStatus
        import requests

        try:
            jobs = self.list_app_jobs(app_code, app_id)
            latest = jobs[0] if jobs else None
            if not latest:
                base_name = self._make_app_base_name(app_code)
                return {
                    "status": SandboxStatus.NOT_CREATED.value,
                    "status_description": get_translation("sandbox.status.description.NOT_CREATED", language),
                    "job_name": base_name,
                    "message": get_translation("sandbox.status.message.NOT_CREATED", language),
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "app_code": app_code,
                }

            job_name = latest.get("name")
            mounts = latest.get("mounts")
            namespace = latest.get("namespace")
            labels = latest.get("labels") or {}
            creation_time = latest.get("creation_time")

            pods_info = self.get_job_pods(job_name)
            if not pods_info:
                return {
                    "status": SandboxStatus.CREATING.value,
                    "status_description": get_translation("sandbox.status.description.CREATING", language),
                    "job_name": job_name,
                    "namespace": namespace,
                    "environment": labels.get("deepwiki-env"),
                    "message": get_translation("sandbox.status.message.CREATING", language),
                    "creation_time": creation_time,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "mounts": mounts,
                    "app_code": app_code,
                }

            running_pods = [pod for pod in pods_info if pod.get("status") == "Running" and pod.get("pod_ip")]
            if not running_pods:
                pending_pods = [pod for pod in pods_info if pod.get("status") in ["Pending", "ContainerCreating"]]
                if pending_pods:
                    return {
                        "status": SandboxStatus.CREATING.value,
                        "status_description": get_translation("sandbox.status.description.CREATING", language),
                        "job_name": job_name,
                        "namespace": namespace,
                        "environment": labels.get("deepwiki-env"),
                        "message": get_translation("sandbox.status.podStarting", language),
                        "pod_status": [pod.get("status") for pod in pending_pods],
                        "creation_time": creation_time,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "mounts": mounts,
                        "app_code": app_code,
                    }
                failed_pods = [pod for pod in pods_info if pod.get("status") in ["Failed", "Error"]]
                if failed_pods:
                    return {
                        "status": SandboxStatus.FAILED.value,
                        "status_description": get_translation("sandbox.status.description.FAILED", language),
                        "job_name": job_name,
                        "namespace": namespace,
                        "environment": labels.get("deepwiki-env"),
                        "message": get_translation("sandbox.status.message.FAILED", language),
                        "pod_status": [pod.get("status") for pod in failed_pods],
                        "creation_time": creation_time,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "mounts": mounts,
                        "app_code": app_code,
                    }
                return {
                    "status": SandboxStatus.CREATING.value,
                    "status_description": get_translation("sandbox.status.description.CREATING", language),
                    "job_name": job_name,
                    "namespace": namespace,
                    "environment": labels.get("deepwiki-env"),
                    "message": get_translation("sandbox.status.podStatusUnknown", language),
                    "pod_status": [pod.get("status") for pod in pods_info],
                    "creation_time": creation_time,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "mounts": mounts,
                    "app_code": app_code,
                }

            for pod in running_pods:
                pod_ip = pod.get("pod_ip")
                container_statuses = pod.get("container_statuses", [])
                if container_statuses and any(not cs.get("ready") for cs in container_statuses):
                    return {
                        "status": SandboxStatus.INITIALIZING.value,
                        "status_description": get_translation("sandbox.status.description.INITIALIZING", language),
                        "job_name": job_name,
                        "namespace": namespace,
                        "environment": labels.get("deepwiki-env"),
                        "message": get_translation("sandbox.status.containerNotReady", language),
                        "pod_ip": pod_ip,
                        "container_statuses": container_statuses,
                        "creation_time": creation_time,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "mounts": mounts,
                        "app_code": app_code,
                    }

                try:
                    session_url = f"http://{pod_ip}:3000/v1/chat/sessions"
                    response = requests.get(session_url, timeout=5)
                    if response.status_code == 200:
                        return {
                            "status": SandboxStatus.READY.value,
                            "status_description": get_translation("sandbox.status.description.READY", language),
                            "job_name": job_name,
                            "namespace": namespace,
                            "environment": labels.get("deepwiki-env"),
                            "message": get_translation("sandbox.status.message.READY", language),
                            "pod_ip": pod_ip,
                            "api_response": get_translation("sandbox.status.normal", language),
                            "creation_time": creation_time,
                            "timestamp": datetime.now(timezone.utc).isoformat(),
                            "mounts": mounts,
                            "app_code": app_code,
                        }
                    return {
                        "status": SandboxStatus.INITIALIZING.value,
                        "status_description": get_translation("sandbox.status.description.INITIALIZING", language),
                        "job_name": job_name,
                        "namespace": namespace,
                        "environment": labels.get("deepwiki-env"),
                        "message": get_translation("sandbox.status.apiStartingWithCode", language).format(code=response.status_code),
                        "pod_ip": pod_ip,
                        "api_response": get_translation("sandbox.status.httpStatus", language).format(code=response.status_code),
                        "creation_time": creation_time,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "mounts": mounts,
                        "app_code": app_code,
                    }
                except requests.exceptions.ConnectionError:
                    return {
                        "status": SandboxStatus.INITIALIZING.value,
                        "status_description": get_translation("sandbox.status.description.INITIALIZING", language),
                        "job_name": job_name,
                        "namespace": namespace,
                        "environment": labels.get("deepwiki-env"),
                        "message": get_translation("sandbox.status.apiConnectionFailed", language),
                        "pod_ip": pod_ip,
                        "api_response": get_translation("sandbox.status.normal", language),
                        "creation_time": creation_time,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "mounts": mounts,
                        "app_code": app_code,
                    }
                except requests.exceptions.Timeout:
                    return {
                        "status": SandboxStatus.INITIALIZING.value,
                        "status_description": get_translation("sandbox.status.description.INITIALIZING", language),
                        "job_name": job_name,
                        "namespace": namespace,
                        "environment": labels.get("deepwiki-env"),
                        "message": get_translation("sandbox.status.apiTimeout", language),
                        "pod_ip": pod_ip,
                        "api_response": get_translation("sandbox.status.requestTimeout", language),
                        "creation_time": creation_time,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "mounts": mounts,
                        "app_code": app_code,
                    }
                except Exception as e:
                    return {
                        "status": SandboxStatus.INITIALIZING.value,
                        "status_description": get_translation("sandbox.status.description.INITIALIZING", language),
                        "job_name": job_name,
                        "namespace": namespace,
                        "environment": labels.get("deepwiki-env"),
                        "message": get_translation("sandbox.status.apiException", language).format(error=str(e)),
                        "pod_ip": pod_ip,
                        "api_response": get_translation("sandbox.status.exception", language).format(error=str(e)),
                        "creation_time": creation_time,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "mounts": mounts,
                        "app_code": app_code,
                    }

            return {
                "status": SandboxStatus.INITIALIZING.value,
                "status_description": get_translation("sandbox.status.description.INITIALIZING", language),
                "job_name": job_name,
                "namespace": namespace,
                "environment": labels.get("deepwiki-env"),
                "message": get_translation("sandbox.status.podNoIp", language),
                "pod_status": [pod.get("status") for pod in running_pods],
                "creation_time": creation_time,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "mounts": mounts,
                "app_code": app_code,
            }

        except Exception as e:
            logger.error(f"获取应用Job详细状态失败: {e}")
            return {
                "status": SandboxStatus.QUERY_FAILED.value,
                "status_description": get_translation("sandbox.status.description.QUERY_FAILED", language),
                "job_name": get_translation("sandbox.status.unknown", language),
                "message": get_translation("sandbox.status.queryException", language).format(error=str(e)),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "app_code": app_code,
            }

    def create_custom_job(self, job_name: str, image: str, volumes: List[Dict], env_vars: List[Dict], 
                          cpu_request: str = "10m", memory_request: str = "256Mi", 
                          cpu_limit: str = "100m", memory_limit: str = "256Mi",
                          user_code: str = None, git_url: str = None, branch: str = None,
                          user_name: str = None, namespace: str = None,
                          extra_labels: Optional[Dict] = None, extra_annotations: Optional[Dict] = None) -> Dict:
        """
        创建自定义配置的k8s job
        
        Args:
            job_name: job名称
            image: 容器镜像
            volumes: 卷挂载配置列表，格式:[{"name": "volume-name", "host_path": "/host/path", "container_path": "/container/path", "read_only": false}]
            env_vars: 环境变量列表，格式:[{"name": "VAR_NAME", "value": "var_value"}]
            cpu_request: CPU请求资源，默认"10m"
            memory_request: 内存请求资源，默认"256Mi"
            cpu_limit: CPU限制资源，默认"100m"
            memory_limit: 内存限制资源，默认"256Mi"
            user_code: 用户代码（可选）
            git_url: Git仓库URL（可选）
            branch: 分支名称（可选）
            user_name: 用户姓名（可选）
            namespace: 命名空间（可选，默认使用实例命名空间）
            
        Returns:
            创建的job信息
        """
        try:
            # 检查容器数量限制
            if not self._check_container_limit():
                raise Exception("已达到最大容器数量限制，无法创建新的Job")
            
            # 使用指定的命名空间或默认命名空间
            target_namespace = namespace or self.namespace
            # 对于自定义Job，直接使用传入的job_name，避免重复添加environment前缀
            name = f"{job_name}".lower()
            
            # 生成job配置
            job_manifest = self._generate_custom_job_manifest(
                name, image, volumes, env_vars, 
                cpu_request, memory_request, cpu_limit, memory_limit,
                user_code, git_url, branch, user_name, target_namespace,
                extra_labels or {}, extra_annotations or {}
            )
            
            # 创建job
            response = self._batch_v1_api.create_namespaced_job(
                namespace=target_namespace,
                body=job_manifest
            )
            
            logger.info(f"成功创建自定义Job: {name}")
            
            return {
                "name": response.metadata.name,
                "namespace": response.metadata.namespace,
                "user_code": user_code or "",
                "git_url": git_url or "",
                "branch": branch or "",
                "creation_time": response.metadata.creation_timestamp,
                "status": "created",
                "user_name": user_name or "",
                "image": image,
                "volumes": volumes,
                "env_vars": env_vars,
                "resources": {
                    "requests": {"cpu": cpu_request, "memory": memory_request},
                    "limits": {"cpu": cpu_limit, "memory": memory_limit}
                }
            }
            
        except ApiException as e:
            if e.status == 409:  # Already exists
                logger.warning(f"Job {name} 已存在")
                return self.get_job(name)
            else:
                logger.error(f"创建自定义Job失败: {e}")
                raise
        except Exception as e:
            logger.error(f"创建自定义Job时发生错误: {e}")
            raise
    
    def create_symbolic_link(self, pod_name: str, paths: List[Dict[str, str]], container_name: Optional[str] = None):
        """
        在一个job里创建软链接，允许同时创建多个
        
        Args:
            pod_name: Pod名称
            paths: 软链接路径列表，格式: [{"src_path": "源路径", "dist_path": "目标路径"}]
            container_name: 容器名称（可选），如果Pod有多个容器，需要指定
        
        Returns:
            执行结果字典，包含success和msg字段
        """
        import shlex
        
        commands = []
        for path_dict in paths:
            if "src_path" in path_dict and "dist_path" in path_dict:
                # 使用shlex.quote进行安全转义，防止命令注入
                src_path = shlex.quote(str(path_dict.get("src_path")))
                logger.info(f"src_path: {src_path}")
                result = self.exec_command_in_pod(pod_name, f"[ -e {src_path} ] && echo 'exists' || echo 'not exists'")
                logger.info(f"result: {result}")
                
                # 确保src_path存在后再继续执行
                if result.get("msg", "").strip() != "exists":
                    logger.info(f"src_path不存在，创建目录: {src_path}")
                    mkdir_result = self.exec_command_in_pod(pod_name, f"mkdir -p {src_path}")
                    if not mkdir_result.get("success"):
                        logger.error(f"创建目录失败: {src_path}, 错误信息: {mkdir_result.get('msg')}")
                        raise Exception(f"创建目录失败: {src_path}")
                    logger.info(f"目录创建成功: {src_path}")
                
                dist_path = shlex.quote(str(path_dict.get("dist_path")))
                # 使用 -sfn 选项：s=symbolic, f=force, n=no-dereference
                command = f"ln -sfn {src_path} {dist_path}"
                commands.append(command)
        
        if not commands:
            logger.warning(f"create_symbolic_link: 没有有效的路径配置 (pod={pod_name})")
            return {"success": False, "msg": "没有有效的路径配置"}
        
        final_command = ' && '.join(commands)
        return self.exec_command_in_pod(pod_name, final_command, container_name)
    
    def unlink_symbolic(self, pod_name: str, paths: List[str], container_name: Optional[str] = None):
        """
        在一个job里删除软链接
        
        Args:
            pod_name: Pod名称
            paths: 要删除的软链接路径列表
            container_name: 容器名称（可选），如果Pod有多个容器，需要指定
        
        Returns:
            执行结果字典，包含success和msg字段
        """
        import shlex
        
        commands = []
        for path in paths:
            if path:
                # 使用shlex.quote进行安全转义
                safe_path = shlex.quote(str(path))
                command = f"unlink {safe_path}"
                commands.append(command)
        
        if not commands:
            logger.warning(f"unlink_symbolic: 没有有效的路径配置 (pod={pod_name})")
            return {"success": False, "msg": "没有有效的路径配置"}
        
        final_command = ' && '.join(commands)
        return self.exec_command_in_pod(pod_name, final_command, container_name)
    
    def exec_command_in_pod(self, pod_name: str, command: str, container_name: Optional[str] = None):
        """
        在Pod中执行命令
        
        Args:
            pod_name: Pod名称
            command: 要执行的命令
            container_name: 容器名称（可选），如果Pod有多个容器，需要指定
        
        Returns:
            执行结果字典，包含success和msg字段
        """
        exec_command = [
            '/bin/bash',
            '-c',
            f'{command}; echo "K8S_EXIT_CODE:$?"'
        ]
        
        container_info = f", container={container_name}" if container_name else ""
        logger.info(f"执行命令：pod={pod_name}{container_info}, command={command}")

        start_time = time.time()
        
        try:
            # 构建exec参数
            exec_kwargs = {
                'name': pod_name,
                'namespace': self.namespace,
                'command': exec_command,
                'stderr': True,
                'stdin': False,
                'stdout': True,
                'tty': False
            }
            
            # 如果指定了容器名称，添加到参数中
            if container_name:
                exec_kwargs['container'] = container_name
            
            resp = stream.stream(
                self._core_v1_api.connect_get_namespaced_pod_exec,
                **exec_kwargs
            )
            
            elapsed_time = time.time() - start_time
            logger.info(f"命令执行耗时：{elapsed_time:.6f} 秒")
            logger.debug(f"执行结果：{resp}")
            
            exit_code = -1
            command_output = []
            for line in resp.splitlines():
                if 'K8S_EXIT_CODE:' in line:
                    exit_code_str = line.replace('K8S_EXIT_CODE:', '').strip()
                    try:
                        exit_code = int(exit_code_str)
                    except ValueError:
                        exit_code = -1
                elif line.strip():
                    command_output.append(line)
            
            result = {"success": exit_code == 0, "msg": '\n'.join(command_output)}
            
            if not result["success"]:
                logger.warning(f"命令执行失败：pod={pod_name}{container_info}, exit_code={exit_code}, output={result['msg']}")
            
            return result
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            error_msg = f"命令执行异常：{str(e)}"
            logger.error(f"{error_msg}, pod={pod_name}{container_info}, 耗时={elapsed_time:.6f}秒", exc_info=True)
            return {"success": False, "msg": error_msg}

    def get_pid(self, pod_name: str) -> int:
        """
        在Pod中执行ps aux命令，获取用户为wct的进程PID
        
        Args:
            pod_name: Pod名称
        
        Returns:
            int: 进程PID，如果没有找到或找到多个进程则返回-1
        
        Raises:
            ValueError: 如果找到多个wct用户的进程
        """
        try:
            # 执行ps aux命令并过滤wct用户的进程
            # 使用awk来提取用户为wct的进程信息，第1列是USER，第2列是PID
            command = "ps aux | awk '$1 == \"wct\" {print $2}'"
            
            result = self.exec_command_in_pod(pod_name, command)
            
            if not result["success"]:
                logger.error(f"执行ps aux命令失败: {result['msg']}")
                return -1
            
            # 解析输出，获取PID列表
            output = result["msg"].strip()
            if not output:
                logger.warning(f"未找到用户wct的进程, pod={pod_name}")
                return -1
            
            # 按行分割，过滤空行
            pid_lines = [line.strip() for line in output.split('\n') if line.strip()]
            
            if len(pid_lines) == 0:
                logger.warning(f"未找到用户wct的进程, pod={pod_name}")
                return -1
            elif len(pid_lines) > 1:
                error_msg = f"找到多个用户wct的进程, pod={pod_name}, PIDs={pid_lines}"
                logger.info(error_msg)
                pid_str = pid_lines[-1]
                try:
                    pid = int(pid_str)
                    return pid
                except ValueError:
                    logger.error(f"无法将PID转换为整数: {pid_str}, pod={pod_name}")
                    return -1
            
            # 只有一个进程，返回其PID
            pid_str = pid_lines[0]
            try:
                pid = int(pid_str)
                logger.info(f"成功获取用户wct的进程PID: {pid}, pod={pod_name}")
                return pid
            except ValueError:
                logger.error(f"无法将PID转换为整数: {pid_str}, pod={pod_name}")
                return -1

        except ValueError:
            # 重新抛出找到多个进程的异常
            raise
        except Exception as e:
            logger.error(f"获取PID时发生异常: {str(e)}, pod={pod_name}", exc_info=True)
            return -1

    def send_signal(self, pod_name: str, pid: int) -> bool:
        """
        发送信号到Pod中的进程
        
        Args:
            pod_name: Pod名称
            pid: 进程ID
        """
        try:
            result = self.exec_command_in_pod(pod_name, f"kill -SIGUSR1 {pid}")
            if not result["success"]:
                logger.error(f"发送信号失败: {result['msg']}")
                return False
            return True
        except Exception as e:
            logger.error(f"发送信号时发生异常: {str(e)}, pod={pod_name}", exc_info=True)
            return False

    def _generate_custom_job_manifest(self, job_name: str, image: str, volumes: List[Dict], env_vars: List[Dict],
                                     cpu_request: str, memory_request: str, cpu_limit: str, memory_limit: str,
                                     user_code: str, git_url: str, branch: str, user_name: str, namespace: str,
                                     extra_labels: Dict, extra_annotations: Dict) -> Dict:
        """
        生成自定义job配置清单
        
        Args:
            job_name: job名称
            image: 容器镜像
            volumes: 卷挂载配置列表
            env_vars: 环境变量列表
            cpu_request: CPU请求资源
            memory_request: 内存请求资源
            cpu_limit: CPU限制资源
            memory_limit: 内存限制资源
            user_code: 用户代码
            git_url: Git仓库URL
            branch: 分支名称
            user_name: 用户姓名
            namespace: 命名空间
            
        Returns:
            job配置字典
        """
        # 名称需满足 DNS-1123：小写字母数字与中划线，开头结尾为字母数字，长度<=63
        def _sanitize_dns1123_name(value: str, max_len: int = 63) -> str:
            try:
                v = str(value).lower()
                # 替换非法字符为 '-'
                import re as _re
                v = _re.sub(r"[^a-z0-9-]", "-", v)
                # 合并重复 '-'
                v = _re.sub(r"-+", "-", v)
                # 去除开头/结尾的非字母数字
                v = _re.sub(r"^[^a-z0-9]+", "", v)
                v = _re.sub(r"[^a-z0-9]+$", "", v)
                if not v:
                    v = "job"
                if len(v) > max_len:
                    v = v[:max_len]
                    v = _re.sub(r"[^a-z0-9]+$", "", v) or v[:max_len-1] + "0"
                return v
            except Exception:
                return "job"

        sanitized_job_name = _sanitize_dns1123_name(job_name)
        container_base_name = _sanitize_dns1123_name(f"{sanitized_job_name}-c")
        current_time = datetime.now(timezone.utc).isoformat()
        
        # 处理 user_name：注解使用原始值；标签使用清洗后的ASCII值
        sanitized_user_name = self._sanitize_user_name_for_label(user_name=user_name)
        
        # 构建卷配置
        k8s_volumes = []
        volume_mounts = []
        
        for i, volume in enumerate(volumes):
            vol_name = volume.get("name") or f"volume-{i}"
            host_path = volume.get("host_path", "")
            container_path = volume.get("container_path", "")
            read_only = volume.get("read_only", False)
            
            if host_path and container_path:
                k8s_volumes.append({
                    "name": vol_name,
                    "hostPath": {
                        "path": host_path,
                        "type": "DirectoryOrCreate"
                    }
                })
                
                volume_mounts.append({
                    "name": vol_name,
                    "mountPath": container_path,
                    "readOnly": bool(read_only)
                })

        # 添加默认的 gemini 和 g-doc 挂载
        gemini_host_path = self.k8s_config.get("job", {}).get("volumes", {}).get("gemini_path", "/app/deepwiki/adalflow/base/.gemini")
        g_doc_host_path = self.k8s_config.get("job", {}).get("volumes", {}).get("g_doc_path", "/app/deepwiki/adalflow/g-doc")

        k8s_volumes.extend([
            {
                "name": "gemini-volume",
                "hostPath": {
                    "path": gemini_host_path,
                    "type": "DirectoryOrCreate"
                }
            },
            {
                "name": "g-doc-volume",
                "hostPath": {
                    "path": g_doc_host_path,
                    "type": "DirectoryOrCreate"
                }
            }
        ])

        volume_mounts.extend([
            {
                "name": "gemini-volume",
                "mountPath": "/root/.gemini"
            },
            {
                "name": "g-doc-volume",
                "mountPath": "/data/workspace/g-doc",
                "readOnly": True
            }
        ])

        # 构建环境变量
        k8s_env_vars = []
        for env_var in env_vars:
            if env_var.get("name") and env_var.get("value") is not None:
                k8s_env_vars.append({
                    "name": str(env_var["name"]),
                    "value": str(env_var["value"])
                })
        
        # 添加默认环境变量
        default_env_vars = [
            {"name": "TZ", "value": "Asia/Shanghai"},
            {"name": "WCT_API_REQUEST_TIMEOUT", "value": str(self.k8s_config.get("job", {}).get("wct_timeouts", {}).get("request_timeout", 300) * 1000)},
            {"name": "WCT_API_TOOL_TIMEOUT", "value": str(self.k8s_config.get("job", {}).get("wct_timeouts", {}).get("tool_timeout", 60) * 1000)}
        ]
        
        # 合并环境变量，用户定义的优先
        final_env_vars = k8s_env_vars + default_env_vars
        
        manifest = {
            "apiVersion": "batch/v1",
            "kind": "Job",
            "metadata": {
                "name": sanitized_job_name,
                "namespace": namespace,
                "annotations": {
                    "sidecar.istio.io/inject": "false",
                    "zcm.iwhalecloud.com/managed-by": "zcm",
                    "created-time": current_time,
                    "last-access-time": current_time,
                    **({"user.code": user_code} if user_code else {}),
                    **({"git.url": git_url} if git_url else {}),
                    **({"git.branch": branch} if branch else {}),
                    **({"user.name": user_name} if user_name else {}),
                    **(extra_annotations or {})
                },
                "labels": {
                    "app": sanitized_job_name,
                    "version": "1.0",
                    "zcm-app": sanitized_job_name,
                    "deepwiki-env": self.environment,
                    **({"user-code": user_code} if user_code else {}),
                    **({"git-url": self._generate_git_url_label(git_url)} if git_url else {}),
                    **({"git-branch": self._sanitize_label_value(branch)} if branch else {}),
                    **({"user-name": sanitized_user_name} if sanitized_user_name else {}),
                    **(extra_labels or {})
                }
            },
            "spec": {
                "backoffLimit": 4,
                "ttlSecondsAfterFinished": 3600,
                "template": {
                    "metadata": {
                        "annotations": {
                            "zcm.iwhalecloud.com/app-id": "337753",
                            "zcm.iwhalecloud.com/project-id": "564272",
                            **({"user.code": user_code} if user_code else {}),
                            **({"git.url": git_url} if git_url else {}),
                            **({"git.branch": branch} if branch else {}),
                            **({"user.name": user_name} if user_name else {}),
                            **(extra_annotations or {})
                        },
                        "labels": {
                            "app": sanitized_job_name,
                            "version": "1.0",
                            "zcm-app": sanitized_job_name,
                            "deepwiki-env": self.environment,
                            **({"user-code": user_code} if user_code else {}),
                            **({"git-url": self._generate_git_url_label(git_url)} if git_url else {}),
                            **({"git-branch": self._sanitize_label_value(branch)} if branch else {}),
                            **({"user-name": sanitized_user_name} if sanitized_user_name else {}),
                            **(extra_labels or {})
                        }
                    },
                    "spec": {
                        "restartPolicy": "Never",
                        "containers": [{
                            "name": container_base_name,
                            "image": image,
                            "imagePullPolicy": "IfNotPresent",
                            "env": final_env_vars,
                            "ports": [
                                {"containerPort": 80, "name": "port-80", "protocol": "TCP"},
                                {"containerPort": 3000, "name": "port-3000", "protocol": "TCP"}
                            ],
                            "resources": {
                                "limits": {"cpu": cpu_limit, "memory": memory_limit},
                                "requests": {"cpu": cpu_request, "memory": memory_request}
                            },
                            "volumeMounts": volume_mounts
                        }],
                        "volumes": k8s_volumes,
                        "nodeSelector": self.k8s_config.get("job", {}).get("node_selector", {"deepwiki-open-dev-01": "deepwiki-open-dev-01"}),
                        "securityContext": {},
                        "terminationGracePeriodSeconds": 33
                    }
                }
            }
        }
        
        return manifest

_kubernetes_service_instance = None

def get_kubernetes_service():
    global _kubernetes_service_instance
    if _kubernetes_service_instance is None:
        try:
            _kubernetes_service_instance = KubernetesService()
            logger.info("Kubernetes服务实例创建成功")
        except Exception as e:
            logger.error(f"创建Kubernetes服务实例失败: {e}")
            # 不抛出异常，返回None让调用方处理
            return None
    return _kubernetes_service_instance
