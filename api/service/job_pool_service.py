"""
Job池管理服务
负责管理k8s job池的创建、分配和回收（仅使用Redis）
"""

import logging
from typing import Optional, Tuple, Dict, List
from datetime import datetime, timezone

# 延迟导入以避免循环依赖
# from api.cache.redis.manager import redis_manager
from api.sandbox.kubernetes_service import get_kubernetes_service

logger = logging.getLogger(__name__)


class JobPoolService:
    """Job池管理服务（仅使用Redis）"""
    
    # Redis key前缀
    JOB_POOL_AVAILABLE = "job_pool:available"  # 可用job池 (Set)
    JOB_POOL_ALL = "job_pool:all"  # 所有job (Set)
    JOB_USER_ALLOCATION = "job_pool:user_allocation"  # 用户分配 (Hash: user_id:wiki_id -> job_name)
    JOB_INFO = "job_pool:job_info"  # job信息 (Hash: job_name -> job_info_json)
    JOB_LOCK_PREFIX = "job_pool:lock"  # 分配锁前缀
    
    def __init__(self):
        # 延迟导入以避免循环依赖
        from api.cache.redis.manager import redis_manager
        # 使用系统初始化好的Redis客户端
        self.redis_client = redis_manager.get_client()
    
    def batch_create_jobs(
        self,
        job_number: int,
        wct_api_key: str,
        environment: str,
        start_id: int = 1
    ) -> Tuple[bool, str, Dict]:
        """
        批量创建job池
        
        Args:
            job_number: 要创建的job数量
            wct_api_key: WCT API密钥
            environment: 环境标识
            start_id: 起始ID，默认从1开始
            
        Returns:
            (是否成功, 消息, 额外数据)
        """
        if not self.redis_client:
            return False, "Redis未连接", {}
        
        try:
            kubernetes_service = get_kubernetes_service()
            
            if not kubernetes_service:
                return False, "Kubernetes服务未初始化", {}
            
            created_count = 0
            skipped_count = 0
            failed_count = 0
            created_jobs = []
            
            for i in range(job_number):
                job_id = start_id + i
                job_name = f"job-{environment}-{job_id}"
                
                # 检查job是否已存在（Redis）
                redis_exists = self._job_exists(job_name)
                
                # 检查job是否已存在（K8s）
                k8s_job = kubernetes_service.get_job(job_name)
                
                # 只有当Redis中存在且K8s中也存在时，才跳过
                if redis_exists and k8s_job:
                    logger.info(f"Job在Redis和K8s中都存在，跳过创建: {job_name}")
                    skipped_count += 1
                    continue
                
                # 如果Redis中不存在但K8s中存在，加入池中
                if not redis_exists and k8s_job:
                    logger.info(f"K8s Job已存在但Redis中不存在，加入池中: {job_name}")
                    self._add_job_to_pool(job_name, job_id)
                    skipped_count += 1
                    continue
                
                # 如果Redis中存在但K8s中不存在，从Redis中删除并重新创建
                if redis_exists and not k8s_job:
                    logger.warning(f"Redis中存在但K8s中不存在，清理Redis记录并重新创建: {job_name}")
                    # 从Redis中删除该job的所有记录
                    self._remove_job_from_pool(job_name)
                
                # 创建K8s Job
                try:
                    kubernetes_service.create_job(
                        id=job_id,
                        job_name=job_name,
                        wct_api_key=wct_api_key
                    )
                    
                    # 将job加入Redis池
                    self._add_job_to_pool(job_name, job_id)
                    
                    created_count += 1
                    created_jobs.append({
                        "job_name": job_name,
                        "job_id": job_id
                    })
                    logger.info(f"成功创建Job: {job_name}")
                        
                except Exception as e:
                    logger.error(f"创建K8s Job失败: {job_name}, 错误: {e}")
                    failed_count += 1
                    continue
            
            message = f"批量创建完成: 成功{created_count}个, 跳过{skipped_count}个, 失败{failed_count}个"
            logger.info(message)
            
            return True, message, {
                "created_count": created_count,
                "skipped_count": skipped_count,
                "failed_count": failed_count,
                "created_jobs": created_jobs
            }
            
        except Exception as e:
            error_msg = f"批量创建Job失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg, {}
    
    def allocate_job(
        self,
        user_id: int,
        wiki_id: str,
        user_code: str,
        user_name: str
    ) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        为用户分配一个可用的job
        
        Args:
            user_id: 用户ID
            wiki_id: Wiki ID (字符串类型)
            user_code: 用户代码
            
        Returns:
            (是否成功, job名称, 错误消息)
        """
        # ========== 开始分配Job ==========
        logger.info(f"========== 开始分配Job: user_id={user_id}, wiki_id={wiki_id}, user_code={user_code}, user_name={user_name} ==========")
        
        if not self.redis_client:
            return False, None, "Redis未连接"
        
        try:
            # 1. 先获取wiki基本信息进行验证（提前发现问题，避免浪费job资源）
            from api.service.wiki_query_service import get_wiki_basic_info
            wiki_info = get_wiki_basic_info(wiki_id=wiki_id)
            if not wiki_info:
                return False, None, f"未找到wiki信息，wiki_id: {wiki_id}"
            
            # 检查必要字段是否存在
            wikiId = wiki_info.get("id")
            owner = wiki_info.get("repo_owner")
            repo_name = wiki_info.get("repo_name")
            branch = wiki_info.get("branch")
            
            if not all([wikiId, owner, repo_name, branch]):
                missing_fields = []
                if not wikiId: missing_fields.append("id")
                if not owner: missing_fields.append("repo_owner")
                if not repo_name: missing_fields.append("repo_name")
                if not branch: missing_fields.append("branch")
                return False, None, f"wiki信息不完整，缺少字段: {', '.join(missing_fields)}"
            
            # 2. 检查用户是否已经分配了job
            allocation_key = f"{user_id}:{wikiId}"
            existing_job = self._get_user_allocated_job(allocation_key)
            
            if existing_job:
                logger.info(f"用户已分配Job: user_id={user_id}, wiki_id={wikiId}, job={existing_job}")
                return True, existing_job, None
            
            # 3. 使用分布式锁防止并发分配
            lock_key = f"{self.JOB_LOCK_PREFIX}:{allocation_key}"
            
            # 尝试获取锁，超时时间60秒
            if not self.redis_client.setnx(lock_key, "1", expiration=60):
                return False, None, "正在分配中，请稍后重试"
            
            try:
                # 再次检查是否已分配（双重检查）
                existing_job = self._get_user_allocated_job(allocation_key)
                if existing_job:
                    return True, existing_job, None
                
                # 4. 从池中获取一个可用的job（使用SPOP原子操作）
                job_name = self._pop_available_job()
                
                # 5. 先进行绑定，在Redis中记录分配关系
                self._set_user_allocation(allocation_key, job_name)
                
                if not job_name:
                    return False, None, "Job额度已用完，请稍后再试"
                
                # 6. 调用bind_user_to_wiki执行真正的业务逻辑
                from api.service.user_group_bind_service import get_user_group_bind_service
                bind_success, bind_message, _ = get_user_group_bind_service().bind_user_to_wiki(
                    wiki_info=wiki_info,
                    user_id=user_id,
                    user_code=user_code,
                    user_name=user_name,
                    job_name=job_name
                )
                
                if not bind_success:
                    # 绑定失败，将job放回可用池，并删除绑定关系
                    logger.error(f"绑定Job到用户失败: {bind_message}")
                    self._add_to_available_pool(job_name)
                    self._delete_user_allocation(allocation_key)
                    return False, None, "绑定Job失败，请稍后重试"
                
                # 7. 更新job信息中的分配状态
                self._update_job_allocation(job_name, user_id, wikiId)
                
                logger.info(f"成功分配Job: user_id={user_id}, wiki_id={wikiId}, job={job_name}")
                return True, job_name, None
                
            finally:
                # 释放锁
                self.redis_client.delete(lock_key)
                
        except Exception as e:
            error_msg = f"分配Job失败: {str(e)}"
            logger.error(error_msg)
            return False, None, error_msg
    
    def get_user_job(
        self,
        user_id: int,
        wiki_id: int
    ) -> Optional[str]:
        """
        获取用户已分配的job
        
        Args:
            user_id: 用户ID
            wiki_id: Wiki ID
            
        Returns:
            job名称，如果未分配则返回None
        """
        if not self.redis_client:
            return None
        
        try:
            allocation_key = f"{user_id}:{wiki_id}"
            return self._get_user_allocated_job(allocation_key)
        except Exception as e:
            logger.error(f"获取用户Job失败: {e}")
            return None
    
    def release_job(
        self,
        user_id: int,
        wiki_id: int
    ) -> Tuple[bool, Optional[str]]:
        """
        释放用户的job，将其放回可用池
        
        Args:
            user_id: 用户ID
            wiki_id: Wiki ID
            
        Returns:
            (是否成功, 错误消息)
        """
        if not self.redis_client:
            return False, "Redis未连接"
        
        try:
            allocation_key = f"{user_id}:{wiki_id}"
            job_name = self._get_user_allocated_job(allocation_key)
            
            if not job_name:
                return True, None  # 没有分配的job，视为成功
            
            # 调用user_group_bind_service解绑用户组
            from api.service.user_group_bind_service import get_user_group_bind_service
            unbind_success, unbind_error = get_user_group_bind_service().unbind_user_from_job(job_name)
            
            if not unbind_success:
                logger.error(f"解绑用户组失败: {unbind_error}")
                return False, f"解绑用户组失败: {unbind_error}"
            
            # 从Redis中删除分配记录
            self._delete_user_allocation(allocation_key)
            
            # 清除job的分配信息
            self._clear_job_allocation(job_name)
            
            # 将job放回可用池
            self._add_to_available_pool(job_name)
            
            logger.info(f"成功释放Job: user_id={user_id}, wiki_id={wiki_id}, job={job_name}")
            return True, None
            
        except Exception as e:
            error_msg = "释放Job失败"
            logger.error(f"{error_msg}: {e}")
            return False, error_msg
    
    def get_pool_status(self) -> Dict:
        """
        获取job池状态
        
        Returns:
            包含池状态信息的字典
        """
        if not self.redis_client:
            return {
                "available_count": 0,
                "allocated_count": 0,
                "total_count": 0
            }
        
        try:
            # 获取可用job数量
            available_count = self._get_available_count()
            
            # 获取已分配job数量
            allocated_count = self._get_allocated_count()
            
            # 获取总job数量
            total_count = self._get_total_count()
            
            return {
                "available_count": available_count,
                "allocated_count": allocated_count,
                "total_count": total_count
            }
            
        except Exception as e:
            logger.error(f"获取Job池状态失败: {e}")
            return {
                "available_count": 0,
                "allocated_count": 0,
                "total_count": 0,
                "error": str(e)
            }

    def remove_job_from_pool(self, job_name: str) -> bool:
        result = self._remove_job_from_pool(job_name)
        if result:
            logger.info(f"已从Redis中移除Job: {job_name}")
            return True
        else:
            logger.error(f"从Redis中移除Job失败: {job_name}")
            return False

    def delete_user_allocation(self, user_id: int, wiki_id: int) -> bool:
        try:
            allocation_key = f"{user_id}:{wiki_id}"
            self._delete_user_allocation(allocation_key)
            return True
        except Exception as e:
            logger.error(f"删除用户分配失败: {e}")
            return False
    
    
    # ========== 私有方法：Redis操作 ==========
    
    def _job_exists(self, job_name: str) -> bool:
        """检查job是否存在于池中"""
        try:
            return self.redis_client.sismember(self.JOB_POOL_ALL, job_name)
        except Exception as e:
            logger.error(f"检查Job是否存在失败: {e}")
            return False
    
    def _add_job_to_pool(self, job_name: str, job_id: int) -> bool:
        """将job加入池中"""
        try:
            import json
            
            # 添加到所有job集合（不设置过期时间）
            self.redis_client.sadd(self.JOB_POOL_ALL, job_name, expiration=-1)
            
            # 添加到可用job集合（不设置过期时间）
            self.redis_client.sadd(self.JOB_POOL_AVAILABLE, job_name, expiration=-1)
            
            # 保存job信息（不设置过期时间）
            job_info = {
                "job_name": job_name,
                "job_id": job_id,
                "created_at": datetime.now(timezone.utc).isoformat(),
                "is_allocated": False,
                "user_id": None,
                "wiki_id": None,
                "allocated_at": None
            }
            
            self.redis_client.hsetkey(self.JOB_INFO, job_name, json.dumps(job_info), expiration=-1)
            
            return True
        except Exception as e:
            logger.error(f"添加Job到池失败: {e}")
            return False
    
    def _pop_available_job(self) -> Optional[str]:
        """从可用池中弹出一个job（原子操作）"""
        try:
            return self.redis_client.spop(self.JOB_POOL_AVAILABLE)
        except Exception as e:
            logger.error(f"从可用池弹出Job失败: {e}")
            return None
    
    def _add_to_available_pool(self, job_name: str) -> bool:
        """将job加入可用池"""
        try:
            # 不设置过期时间
            self.redis_client.sadd(self.JOB_POOL_AVAILABLE, job_name, expiration=-1)
            return True
        except Exception as e:
            logger.error(f"添加Job到可用池失败: {e}")
            return False
    
    def _get_user_allocated_job(self, allocation_key: str) -> Optional[str]:
        """获取用户已分配的job"""
        try:
            job_name = self.redis_client.hget(self.JOB_USER_ALLOCATION, allocation_key)
            if job_name:
                return job_name.decode('utf-8') if isinstance(job_name, bytes) else job_name
            return None
        except Exception as e:
            logger.error(f"获取用户分配的Job失败: {e}")
            return None
    
    def _set_user_allocation(self, allocation_key: str, job_name: str) -> bool:
        """设置用户分配记录"""
        try:
            # 不设置过期时间
            self.redis_client.hsetkey(self.JOB_USER_ALLOCATION, allocation_key, job_name, expiration=-1)
            return True
        except Exception as e:
            logger.error(f"设置用户分配记录失败: {e}")
            return False
    
    def _delete_user_allocation(self, allocation_key: str) -> bool:
        """删除用户分配记录"""
        try:
            self.redis_client.hdel(self.JOB_USER_ALLOCATION, allocation_key)
            return True
        except Exception as e:
            logger.error(f"删除用户分配记录失败: {e}")
            return False
    
    def _update_job_allocation(self, job_name: str, user_id: int, wiki_id: int) -> bool:
        """更新job的分配信息"""
        try:
            import json
            
            job_info_str = self.redis_client.hget(self.JOB_INFO, job_name)
            
            if job_info_str:
                job_info = json.loads(job_info_str)
                job_info["is_allocated"] = True
                job_info["user_id"] = user_id
                job_info["wiki_id"] = wiki_id
                job_info["allocated_at"] = datetime.now(timezone.utc).isoformat()
                
                # 不设置过期时间
                self.redis_client.hsetkey(self.JOB_INFO, job_name, json.dumps(job_info), expiration=-1)
                return True
            return False
        except Exception as e:
            logger.error(f"更新Job分配信息失败: {e}")
            return False
    
    def _clear_job_allocation(self, job_name: str) -> bool:
        """清除job的分配信息"""
        try:
            import json
            
            job_info_str = self.redis_client.hget(self.JOB_INFO, job_name)
            
            if job_info_str:
                job_info = json.loads(job_info_str)
                job_info["is_allocated"] = False
                job_info["user_id"] = None
                job_info["wiki_id"] = None
                job_info["allocated_at"] = None
                
                # 不设置过期时间
                self.redis_client.hsetkey(self.JOB_INFO, job_name, json.dumps(job_info), expiration=-1)
                return True
            return False
        except Exception as e:
            logger.error(f"清除Job分配信息失败: {e}")
            return False
    
    def _get_available_count(self) -> int:
        """获取可用job数量"""
        try:
            return self.redis_client.scard(self.JOB_POOL_AVAILABLE)
        except Exception as e:
            logger.error(f"获取可用Job数量失败: {e}")
            return 0
    
    def _get_allocated_count(self) -> int:
        """获取已分配job数量"""
        try:
            return self.redis_client.hlen(self.JOB_USER_ALLOCATION)
        except Exception as e:
            logger.error(f"获取已分配Job数量失败: {e}")
            return 0
    
    def _get_total_count(self) -> int:
        """获取总job数量"""
        try:
            return self.redis_client.scard(self.JOB_POOL_ALL)
        except Exception as e:
            logger.error(f"获取总Job数量失败: {e}")
            return 0
    
    def _remove_job_from_pool(self, job_name: str) -> bool:
        """
        从池中完全移除一个job的所有记录
        
        Args:
            job_name: job名称
            
        Returns:
            是否移除成功
        """
        try:
            # 从所有job集合中移除
            result = self.redis_client.srem(self.JOB_POOL_ALL, job_name)
            if result == 0:
                logger.error(f"从所有job集合中移除Job失败: {job_name}")
            
            # 从可用job集合中移除
            result = self.redis_client.srem(self.JOB_POOL_AVAILABLE, job_name)
            if result == 0:
                logger.error(f"从可用job集合中移除Job失败: {job_name}")
            
            # 删除job信息
            result = self.redis_client.hdel(self.JOB_INFO, job_name)
            if result == 0:
                logger.error(f"删除job信息失败: {job_name}")
            
            logger.info(f"已从Redis中移除Job: {job_name}")
            return True
        except Exception as e:
            logger.error(f"从Redis中移除Job失败: {job_name}, 错误: {e}")
            return False
    
    def clear_all_pool_data(self) -> Tuple[bool, str]:
        """
        清空所有Job池相关的Redis数据
        用于应用启动时重置Job池状态
        
        Returns:
            (是否成功, 消息)
        """
        if not self.redis_client:
            return False, "Redis未连接"
        
        try:
            # 清空所有 job pool 相关的 keys
            keys_to_clear = [
                self.JOB_POOL_AVAILABLE,  # job_pool:available
                self.JOB_POOL_ALL,         # job_pool:all
                self.JOB_USER_ALLOCATION,  # job_pool:user_allocation
                self.JOB_INFO,             # job_pool:job_info
            ]
            
            cleared_keys = []
            for key in keys_to_clear:
                try:
                    self.redis_client.delete(key)
                    cleared_keys.append(key)
                    logger.info(f"已清空 Redis key: {key}")
                except Exception as e:
                    logger.warning(f"清空 Redis key 失败: {key}, 错误: {e}")
            
            # 清空所有 job_pool:lock:* 的锁
            lock_keys_deleted = 0
            try:
                lock_pattern = f"{self.JOB_LOCK_PREFIX}:*"
                # 使用 scan 来查找所有匹配的锁 key
                cursor = 0
                while True:
                    cursor, keys = self.redis_client.scan(cursor=cursor, match=lock_pattern, count=100)
                    if keys:
                        for key in keys:
                            self.redis_client.delete(key)
                            lock_keys_deleted += 1
                    if cursor == 0:
                        break
                if lock_keys_deleted > 0:
                    logger.info(f"已清空 {lock_keys_deleted} 个 job pool 锁")
            except Exception as e:
                logger.warning(f"清空 job pool 锁失败: {e}")
            
            message = f"JobPoolService Redis 数据清空完成: 清空了 {len(cleared_keys)} 个主要 key 和 {lock_keys_deleted} 个锁"
            logger.info(message)
            return True, message
            
        except Exception as e:
            error_msg = f"清空 JobPoolService Redis 数据失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg


# 全局服务实例
_job_pool_service = None


def get_job_pool_service() -> JobPoolService:
    """获取Job池服务实例"""
    global _job_pool_service
    if _job_pool_service is None:
        _job_pool_service = JobPoolService()
    return _job_pool_service
