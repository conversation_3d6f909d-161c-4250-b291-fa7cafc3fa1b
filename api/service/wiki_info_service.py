import logging
import uuid
from datetime import datetime
from typing import Optional, List
from types import SimpleNamespace

from sqlalchemy import delete, or_
from sqlalchemy.orm import aliased
from sqlmodel import Session, select, text, desc

from api.database.base import session_scope
from api.middleware.auth_middleware import get_current_user
from api.model.user_info import UserInfo
from api.model.user_role import Role
from api.model.wiki_dc_ext_1 import WikiDcExt1
from api.model.wiki_dc_ext_2 import WikiDcExt2
from api.model.wiki_info import WikiInfo
from api.model.git_repository import AiDwGitRepository
from api.model.wiki_repository_relation import AiDwWikiRepositoryRelation
from api.model.wiki_user_role import WikiUserRole
from api.model.wiki_tag import WikiTag
from api.model.tag import Tag
from api.model.wiki_content import AiDwWikiContent
from api.service.user_service import select_user_roles
from api.type.repo import RepoMetadata

# from api.middleware.auth_middleware import get_current_user  # 导入获取当前用户的函数

logger = logging.getLogger(__name__)

def create_wiki_info(
    session: Session,
    repo_url: str,
    branch: str,
    repo_owner: str,
    repo_name: str,
    repo_type: str = "whaleDevCloud",
    sub_repos: Optional[str] = None,
    topic_id: Optional[str] = None,
    topic_id_code: Optional[str] = None,
    topic_id_doc: Optional[str] = None,
    provider: str = "whalecloud",
    model: str = "gemini-2.5-flash",
    language: str = "zh",
    excluded_dirs: Optional[str] = None,
    excluded_files: Optional[str] = None,
    included_dirs: Optional[str] = None,
    included_files: Optional[str] = None,
    comprehensive: bool = True,
    created_by: int = 0,
    owner_id: int = 0,
    comments: Optional[str] = None,
    name: Optional[str] = None,
    description: Optional[str] = None,
) -> WikiInfo:
    """Create a new wiki info row using the split schema."""

    wiki_id = str(uuid.uuid4())
    now = datetime.utcnow()

    wiki_info = WikiInfo(
        wiki_id=wiki_id,
        name=name or repo_name,
        description=description or comments,
        repo_url=repo_url,
        repo_owner=repo_owner,
        repo_name=repo_name,
        branch=branch or "master",
        repo_type=repo_type,
        provider=provider,
        model=model,
        language=language,
        comprehensive=comprehensive,
        excluded_dirs=excluded_dirs,
        excluded_files=excluded_files,
        included_dirs=included_dirs,
        included_files=included_files,
        project_topic_id=topic_id,
        status="pending",
        visibility=2,
        created_by=created_by,
        updated_by=created_by,
        owner_id=owner_id,
        created_time=now,
        updated_time=now,
    )

    session.add(wiki_info)
    session.flush()

    # 预创建内容占位，方便后续更新
    session.add(AiDwWikiContent(wiki_id=wiki_id))
    session.flush()

    created = session.exec(select(WikiInfo).where(WikiInfo.wiki_id == wiki_id)).first()
    if not created:
        raise RuntimeError("Failed to persist wiki info")
    return WikiInfo(**created.model_dump())

def extend_wiki_info(session: Session, wiki_id: int, repo_metadata: Optional[RepoMetadata]):
    """
    扩展Wiki信息（添加DevCloud元数据）
    注意：wiki_id 参数是数字主键ID，不是字符串wiki_id
    """
    if repo_metadata:
        user = get_current_user()
        user_id = user.get("id", 0)
        now = datetime.now()
        wiki_dc_ext1 = WikiDcExt1(
            wiki_id=wiki_id,  # 这里使用的是数字主键ID
            dc_repo_id=repo_metadata.dc_repo_id,
            dc_project_id=repo_metadata.dc_project_id,
            branch_version_id=repo_metadata.branch_version_id,
            branch_version_name=repo_metadata.branch_version_name,
            product_version_id=repo_metadata.product_version_id,
            product_version_code=repo_metadata.product_version_code,
            product_name=repo_metadata.product_name,
            product_id=repo_metadata.product_id,
            product_line_id=repo_metadata.product_line_id,
            product_line_name=repo_metadata.product_line_name,
            created_by=user_id,
            created_date=now
        )
        session.add(wiki_dc_ext1)
        if repo_metadata.solutions:
            wiki_dc_ext2_list = [WikiDcExt2(
                wiki_id=wiki_id,
                release_pkg_id=solution.release_pkg_id,
                release_pkg_code=solution.release_pkg_code,
                solution_name=solution.solution_name,
                solution_id=solution.solution_id,
                created_by=user_id,
                created_date=now
            ) for solution in repo_metadata.solutions]
            session.add_all(wiki_dc_ext2_list)
        session.flush()

def update_wiki_ext(session: Session, wiki_id: str, repo_metadata: Optional[RepoMetadata]):
    """
    更新wiki的仓库元数据
    """
    wiki_info = session.exec(select(WikiInfo).where(WikiInfo.wiki_id == wiki_id)).first()
    wiki_primary_key = wiki_info.id
    if not wiki_info:
        raise Exception(f"无法通过wiki_id[{wiki_id}]找到wiki信息")
    if repo_metadata:
        user = get_current_user()
        user_id = user.get("id", 0)
        now = datetime.now()
        wiki_ext1 = session.exec(select(WikiDcExt1).where(WikiDcExt1.wiki_id == wiki_primary_key)).first()
        if wiki_ext1:
            wiki_ext1.dc_repo_id = repo_metadata.dc_repo_id
            wiki_ext1.dc_project_id = repo_metadata.dc_project_id
            wiki_ext1.branch_version_id = repo_metadata.branch_version_id
            wiki_ext1.branch_version_name = repo_metadata.branch_version_name
            wiki_ext1.product_version_id = repo_metadata.product_version_id
            wiki_ext1.product_version_code = repo_metadata.product_version_code
            wiki_ext1.product_name = repo_metadata.product_name
            wiki_ext1.product_id = repo_metadata.product_id
            wiki_ext1.product_line_id = repo_metadata.product_line_id
            wiki_ext1.product_line_name = repo_metadata.product_line_name
            wiki_ext1.update_by = user_id
            wiki_ext1.update_date = now
        else:
            wiki_dc_ext1 = WikiDcExt1(
                wiki_id=wiki_primary_key,
                dc_repo_id=repo_metadata.dc_repo_id,
                dc_project_id=repo_metadata.dc_project_id,
                branch_version_id=repo_metadata.branch_version_id,
                branch_version_name=repo_metadata.branch_version_name,
                product_version_id=repo_metadata.product_version_id,
                product_version_code=repo_metadata.product_version_code,
                product_name=repo_metadata.product_name,
                product_id=repo_metadata.product_id,
                product_line_id=repo_metadata.product_line_id,
                product_line_name=repo_metadata.product_line_name,
                created_by=user_id,
                created_date=now
            )
            session.add(wiki_dc_ext1)
        # 处理解决方案
        if repo_metadata.solutions:
            solutions = repo_metadata.solutions
            wiki_ext2_list = session.exec(select(WikiDcExt2).where(WikiDcExt2.wiki_id == wiki_primary_key)).all()
            ext2_len = len(wiki_ext2_list)
            wiki_ext2_list_index = None
            for index, solution in enumerate(solutions):
                if index < ext2_len:
                    wiki_ext2_list_index = index
                    item = wiki_ext2_list[wiki_ext2_list_index]
                    item.release_pkg_id = solution.release_pkg_id
                    item.release_pkg_code = solution.release_pkg_code
                    item.solution_name = solution.solution_name
                    item.solution_id = solution.solution_id
                    item.update_by = user_id
                    item.update_date = now
                else:
                    new_item = WikiDcExt2(
                        wiki_id=wiki_primary_key,
                        release_pkg_id=solution.release_pkg_id,
                        release_pkg_code=solution.release_pkg_code,
                        solution_name=solution.solution_name,
                        solution_id=solution.solution_id,
                        created_by=user_id,
                        created_date=now
                    )
                    session.add(new_item)
            wiki_ext2_list_index = 0 if wiki_ext2_list_index is None else (wiki_ext2_list_index + 1)
            while wiki_ext2_list_index < ext2_len:
                item = wiki_ext2_list[wiki_ext2_list_index]
                session.exec(delete(WikiDcExt2).where(WikiDcExt2.id == item.id))
                wiki_ext2_list_index += 1
        else:
            session.exec(delete(WikiDcExt2).where(WikiDcExt2.wiki_id == wiki_primary_key))


def get_wiki_info_by_repo_and_branch(session: Session, repo_url: str, branch: str) -> Optional[any]:
    """
    根据仓库URL和分支获取Wiki信息记录

    参数:
        session: 数据库会话
        repo_url: 仓库URL
        branch: 分支名称

    返回:
        Optional[WikiInfo]: 如果找到则返回Wiki信息记录，否则返回None
    """
    # 使用冗余字段直接查询 WikiInfo
    info = session.exec(select(WikiInfo).where(
        WikiInfo.repo_url == repo_url,
        WikiInfo.branch == branch
    )).first()
    return WikiInfo(**info.model_dump()) if info else None

def get_wiki_type_by_repo_and_branch(repo_url: str, branch: str) -> int:
    """
    根据仓库URL和分支获取Wiki类型

    参数:
        repo_url: 仓库URL
        branch: 分支名称

    返回:
        int: wiki类型 (1:产品, 2:项目)，如果未找到则返回默认值1(产品)
    """
    with session_scope() as session:
        info = session.exec(select(WikiInfo).where(
            WikiInfo.repo_url == repo_url,
            WikiInfo.branch == branch
        )).first()
        return int(info.wiki_type) if info and info.wiki_type is not None else 1

def get_wiki_info_for_chat(wiki_id: str, repo_url: str, branch: str) -> Optional[dict]:
    """
    根据wiki_id获取Wiki信息记录
    """
    with session_scope() as session:
        info: Optional[WikiInfo] = None
        main_repo: Optional[AiDwGitRepository] = None
        if wiki_id:
            info = session.exec(select(WikiInfo).where(WikiInfo.wiki_id == wiki_id)).first()
            if info:
                rel = session.exec(select(AiDwWikiRepositoryRelation).where(
                    AiDwWikiRepositoryRelation.wiki_id == info.wiki_id,
                    AiDwWikiRepositoryRelation.is_main_repo == True  # noqa: E712
                )).first()
                if rel:
                    main_repo = session.exec(select(AiDwGitRepository).where(AiDwGitRepository.id == rel.repository_id)).first()
        else:
            # 通过冗余字段直接查询
            info = session.exec(select(WikiInfo).where(
                WikiInfo.repo_url == repo_url,
                WikiInfo.branch == branch
            )).first()
            if info:
                # 查找主仓库信息
                rel = session.exec(select(AiDwWikiRepositoryRelation).where(
                    AiDwWikiRepositoryRelation.wiki_id == info.wiki_id,
                    AiDwWikiRepositoryRelation.is_main_repo == True  # noqa: E712
                )).first()
                if rel:
                    main_repo = session.exec(select(AiDwGitRepository).where(AiDwGitRepository.id == rel.repository_id)).first()

        if not info:
            return None

        # 收集该 wiki 的所有代码 topic（主仓 + 子仓）
        all_rels = session.exec(select(AiDwWikiRepositoryRelation).where(
            AiDwWikiRepositoryRelation.wiki_id == info.wiki_id
        )).all()
        code_topics = []
        if all_rels:
            repo_ids = [r.repository_id for r in all_rels]
            if repo_ids:
                repos = session.exec(select(AiDwGitRepository).where(AiDwGitRepository.id.in_(repo_ids))).all()
                for r in repos:
                    if getattr(r, "code_topic_id", None):
                        code_topics.append(r.code_topic_id)

        # 兼容旧返回：保留主仓 topic_id_code，同时新增 topic_id_code_all
        return {
            "id": getattr(info, "id", None),
            "topic_id": getattr(info, "project_topic_id", None),
            "topic_id_code": getattr(main_repo, "code_topic_id", None),
            "topic_id_code_all": code_topics,
        }

def get_wiki_info_by_repo(session: Session, repo_name: str = None, branch: str = None, owner: str = None, language: Optional[str] = None, include_data: bool = True) -> Optional[any]:
    """
    根据仓库branch和owner筛选获取Wiki信息记录

    参数:
        session: 数据库会话
        repo_name: 仓库名称
        branch: 可选的分支筛选
        owner: 可选的仓库所有者筛选
        language: 可选的语言筛选
        include_data: 是否包含wiki_data字段，默认为True以修复wiki_data为null的问题

    返回:
        Optional[WikiInfo]: 如果找到则返回Wiki信息记录，否则返回None
    """
    logger.info(f"正在查询仓库信息: repo_name={repo_name}, repo branch={branch}, owner={owner}, language={language}, include_data={include_data}")

    # 使用冗余字段直接查询 AiDwWikiInfo
    stmt = select(WikiInfo)
    if owner:
        stmt = stmt.where(WikiInfo.repo_owner == owner)
    if repo_name:
        stmt = stmt.where(WikiInfo.repo_name == repo_name)
    if branch:
        stmt = stmt.where(WikiInfo.branch == branch)
    if language:
        stmt = stmt.where(WikiInfo.language == language)

    info = session.exec(stmt).first()
    if not info:
        logger.warning(f"未找到Wiki信息: owner={owner}, repo={repo_name}, branch={branch}, language={language}")
        return None

    return WikiInfo(**info.model_dump())

def get_wiki_info(session: Session, wiki_id: str) -> Optional[any]:
    """
    根据wiki_id获取Wiki信息记录

    参数:
        session: 数据库会话
        wiki_id: Wiki唯一标识

    返回:
        Optional[WikiInfo]: 如果找到则返回Wiki信息记录，否则返回None
    """
    info = session.exec(select(WikiInfo).where(WikiInfo.wiki_id == wiki_id)).first()
    return WikiInfo(**info.model_dump()) if info else None

def get_wiki_info_by_primary(session: Session, id: int) -> Optional[any]:
    """
    根据主键获取Wiki信息记录

    参数:
        session: 数据库会话
        id: WikiInfo的主键

    返回:
        Optional[WikiInfo]: 如果找到则返回Wiki信息记录，否则返回None
    """
    info = session.exec(select(WikiInfo).where(WikiInfo.id == id)).first()
    return WikiInfo(**info.model_dump()) if info else None

def get_wiki_info_without_data(session: Session, wiki_id: str) -> Optional[any]:
    """
    根据wiki_id获取Wiki信息记录，不包含wiki_data字段

    参数:
        session: 数据库会话
        wiki_id: Wiki唯一标识

    返回:
        Optional[WikiInfo]: 如果找到则返回Wiki信息记录，否则返回None
    """
    import logging
    logger = logging.getLogger(__name__)

    logger.info(f"查询wiki信息，wiki_id: {wiki_id}")

    try:
        # 使用defer来延迟加载wiki_data字段，避免查询大数据
        from sqlalchemy.orm import defer

        info = session.exec(select(WikiInfo).where(WikiInfo.wiki_id == wiki_id)).first()
        if info:
            return WikiInfo(**info.model_dump())
        else:
            logger.warning(f"未找到wiki_id为 {wiki_id} 的记录")
            return None

    except Exception as e:
        # 如果defer方法有问题，回退到简单查询
        logger.warning(f"使用defer查询失败，回退到简单查询: {e}")

        try:
            # 使用partial查询只选择需要的字段，避免加载wiki_data
            from sqlalchemy import select as sa_select

                         # 创建只包含需要字段的查询
            result = session.exec(select(WikiInfo).where(WikiInfo.wiki_id == wiki_id)).first()
            if result:
                return WikiInfo(**result.model_dump())
            else:
                logger.warning(f"简单查询也未找到wiki_id为 {wiki_id} 的记录")
                return None

        except Exception as e2:
            logger.error(f"简单查询也失败: {e2}")
            return None


def get_wiki_info_without_data_by_id(session: Session, id: int) -> Optional[any]:
    """
    根据id获取Wiki信息记录，不包含wiki_data字段

    参数:
        session: 数据库会话
        id: Wiki表的主键ID

    返回:
        Optional[WikiInfo]: 如果找到则返回Wiki信息记录，否则返回None
    """
    import logging
    logger = logging.getLogger(__name__)

    try:
        # 使用defer来延迟加载wiki_data字段，避免查询大数据
        from sqlalchemy.orm import defer

        info = session.exec(select(WikiInfo).where(WikiInfo.id == id)).first()
        if info:
            return WikiInfo(**info.model_dump())
        else:
            logger.warning(f"未找到id为 {id} 的记录")
            return None

    except Exception as e:
        # 如果defer方法有问题，回退到简单查询
        logger.warning(f"使用defer查询失败，回退到简单查询: {e}")

        try:
            # 使用partial查询只选择需要的字段，避免加载wiki_data
            from sqlalchemy import select as sa_select

            # 创建只包含需要字段的查询
            result = session.exec(select(WikiInfo).where(WikiInfo.id == id)).first()
            if result:
                return WikiInfo(**result.model_dump())
            else:
                logger.warning(f"简单查询也未找到id为 {id} 的记录")
                return None

        except Exception as e2:
            logger.error(f"简单查询也失败: {e2}")
            return None


def update_wiki_info(session: Session, wiki_id: str, **kwargs) -> bool:
    """
    更新Wiki信息记录

    参数:
        session: 数据库会话
        wiki_id: Wiki唯一标识
        **kwargs: 要更新的字段和值

    返回:
        bool: 更新是否成功
    """
    wiki_info = session.exec(select(WikiInfo).where(WikiInfo.wiki_id == wiki_id)).first()
    if not wiki_info:
        return False

    # 更新指定字段
    for key, value in kwargs.items():
        if hasattr(wiki_info, key):
            setattr(wiki_info, key, value)

    # 更新更新时间
    wiki_info.updated_time = datetime.now()

    session.add(wiki_info)
    return True

def list_wikis_by_owner(session: Session, owner: str, limit: int = 10, offset: int = 0) -> List[any]:
    """
    列出指定所有者的Wiki信息

    Args:
        session: 数据库会话
        owner: 仓库所有者
        limit: 限制返回数量
        offset: 偏移量

    Returns:
        List[WikiInfo]: Wiki信息列表
    """
    # 通过主仓库 owner 反查 wiki
    subq = select(AiDwWikiRepositoryRelation.wiki_id).where(AiDwWikiRepositoryRelation.is_main_repo == True).subquery()  # noqa: E712
    stmt = (
        select(WikiInfo)
        .where(WikiInfo.wiki_id.in_(select(subq.c.wiki_id)))
        .order_by(desc(WikiInfo.updated_time))
        .limit(limit)
        .offset(offset)
    )
    rows = session.exec(stmt).all()
    return [WikiInfo(**r.model_dump()) for r in rows]

def list_all_wikis(session: Session, limit: int = 100, offset: int = 0) -> List[any]:
    """
    列出所有Wiki信息

    Args:
        session: 数据库会话
        limit: 限制返回数量
        offset: 偏移量

    Returns:
        List[WikiInfo]: Wiki信息列表
    """
    stmt = (
        select(WikiInfo)
        .order_by(desc(WikiInfo.updated_time))
        .limit(limit)
        .offset(offset)
    )
    rows = session.exec(stmt).all()
    return [WikiInfo(**r.model_dump()) for r in rows]

def search_wikis(
    session: Session,
    repo_name: Optional[str] = None,
    repo_owner: Optional[str] = None,
    repo_type: Optional[str] = None,
    language: Optional[str] = None,
    status: Optional[str] = None,
    statuses: Optional[List[str]] = None,
    limit: int = 50,
    include_data: bool = False,
    offset: int = 0
) -> List[any]:
    """
    搜索Wiki信息记录

    参数:
        session: 数据库会话
        repo_name: 仓库名称
        repo_owner: 仓库所有者
        repo_type: 仓库类型
        language: 语言
        status: 状态（单个状态）
        statuses: 状态列表（多个状态）
        limit: 最大返回数量
        include_data: 是否包含wiki_data字段

    返回:
        List[WikiInfo]: Wiki信息记录列表
    """
    stmt = select(WikiInfo)
    if language:
        stmt = stmt.where(WikiInfo.language == language)
    if statuses:
        stmt = stmt.where(WikiInfo.status.in_(statuses))
    elif status:
        stmt = stmt.where(WikiInfo.status == status)
    stmt = stmt.order_by(WikiInfo.updated_time.desc()).limit(limit).offset(offset)
    rows = session.exec(stmt).all()
    return [WikiInfo(**r.model_dump()) for r in rows]

def search_wikis_like(
    session: Session,
    repo_name: Optional[str] = None,
    repo_owner: Optional[str] = None,
    repo_type: Optional[str] = None,
    language: Optional[str] = None,
    status: Optional[str] = None,
    statuses: Optional[List[str]] = None,
    limit: int = 50,
    include_data: bool = False,
    offset: int = 0
) -> List[any]:
    """
    搜索Wiki信息记录

    参数:
        session: 数据库会话
        repo_name: 仓库名称
        repo_owner: 仓库所有者
        repo_type: 仓库类型
        language: 语言
        status: 状态（单个状态）
        statuses: 状态列表（多个状态）
        limit: 最大返回数量
        include_data: 是否包含wiki_data字段

    返回:
        List[WikiInfo]: Wiki信息记录列表
    """
    stmt = select(WikiInfo)
    if language:
        stmt = stmt.where(WikiInfo.language == language)
    if statuses:
        stmt = stmt.where(WikiInfo.status.in_(statuses))
    elif status:
        stmt = stmt.where(WikiInfo.status == status)
    stmt = stmt.order_by(WikiInfo.updated_time.desc()).limit(limit).offset(offset)
    rows = session.exec(stmt).all()
    return [WikiInfo(**r.model_dump()) for r in rows]

def delete_wiki_info(session: Session, wiki_id: str) -> bool:
    """
    删除Wiki信息及其所有相关数据

    删除顺序：
    1. ai_dw_wiki_user_role - Wiki用户角色关系
    2. ai_dw_wiki_repository_relation - Wiki与仓库关系
    3. ai_dw_wiki_content - Wiki内容
    4. ai_dw_chat_history - 聊天历史记录
    5. ai_dw_chat_session - 聊天会话记录
    6. ai_dw_project - 项目表（项目视图）
    7. ai_dw_wiki_dc_ext_1 - wiki仓库关联的研发云扩展信息
    8. ai_dw_wiki_dc_ext_2 - wiki仓库关联的研发云扩展信息
    9. ai_dw_wiki_tag - wiki与tag关联表
    10. ai_dw_app_wiki_rela - 应用与Wiki绑定关系表
    11. ai_dw_wiki_info - Wiki基本信息

    Args:
        session: 数据库会话
        wiki_id: Wiki ID

    Returns:
        bool: 是否删除成功
    """
    try:
        # 1. 查找WikiInfo记录
        wiki_info = get_wiki_info(session, wiki_id)
        if not wiki_info:
            logger.warning(f"Cannot find WikiInfo with wiki_id={wiki_id}")
            return False

        # 2. 删除Wiki用户角色关系
        from api.model.wiki_user_role import WikiUserRole
        try:
            user_roles = session.exec(
                select(WikiUserRole).where(WikiUserRole.wiki_id == wiki_info.id)
            ).all()
            for user_role in user_roles:
                session.delete(user_role)
            logger.info(f"Deleted {len(user_roles)} wiki user role records for wiki_id={wiki_id}")
        except Exception as e:
            logger.warning(f"Failed to delete wiki user roles for wiki_id={wiki_id}: {e}")
            # 使用原生SQL作为备选
            try:
                result = session.execute(
                    text("DELETE FROM ai_dw_wiki_user_role WHERE wiki_id = :wiki_id"),
                    {"wiki_id": wiki_info.id}
                )
                logger.info(f"Deleted {result.rowcount} wiki user role records using raw SQL for wiki_id={wiki_id}")
            except Exception as sql_e:
                logger.error(f"Failed to delete wiki user roles with raw SQL for wiki_id={wiki_id}: {sql_e}")

        # 3. 删除Wiki与仓库关系
        from api.model.wiki_repository_relation import AiDwWikiRepositoryRelation
        try:
            repo_relations = session.exec(
                select(AiDwWikiRepositoryRelation).where(AiDwWikiRepositoryRelation.wiki_id == wiki_id)
            ).all()
            for relation in repo_relations:
                session.delete(relation)
            logger.info(f"Deleted {len(repo_relations)} wiki repository relations for wiki_id={wiki_id}")
        except Exception as e:
            logger.warning(f"Failed to delete wiki repository relations for wiki_id={wiki_id}: {e}")
            # 尝试使用原生SQL删除
            try:
                result = session.execute(
                    text("DELETE FROM ai_dw_wiki_repository_relation WHERE wiki_id = :wiki_id"),
                    {"wiki_id": wiki_id}
                )
                logger.info(f"Deleted {result.rowcount} wiki repository relations using raw SQL for wiki_id={wiki_id}")
            except Exception as sql_e:
                logger.error(f"Failed to delete wiki repository relations with raw SQL for wiki_id={wiki_id}: {sql_e}")

        # 4. 删除Wiki内容
        from api.model.wiki_content import AiDwWikiContent
        try:
            wiki_content = session.exec(
                select(AiDwWikiContent).where(AiDwWikiContent.wiki_id == wiki_id)
            ).first()
            if wiki_content:
                session.delete(wiki_content)
                logger.info(f"Deleted wiki content for wiki_id={wiki_id}")
        except Exception as e:
            logger.warning(f"Failed to delete wiki content for wiki_id={wiki_id}: {e}")
            # 使用原生SQL作为备选
            try:
                result = session.execute(
                    text("DELETE FROM ai_dw_wiki_content WHERE wiki_id = :wiki_id"),
                    {"wiki_id": wiki_id}
                )
                logger.info(f"Deleted {result.rowcount} wiki content records using raw SQL for wiki_id={wiki_id}")
            except Exception as sql_e:
                logger.error(f"Failed to delete wiki content with raw SQL for wiki_id={wiki_id}: {sql_e}")

        # 5. 删除相关的聊天会话和聊天历史
        from api.model.chat_session import ChatSession
        from api.model.chat_history import ChatHistory

        # 查找相关的聊天会话
        chat_sessions = session.exec(
            select(ChatSession).where(ChatSession.wiki_id == wiki_info.id)
        ).all()

        for chat_session in chat_sessions:
            # 删除聊天历史
            chat_histories = session.exec(
                select(ChatHistory).where(ChatHistory.chat_id == chat_session.id)
            ).all()
            for chat_history in chat_histories:
                session.delete(chat_history)
            logger.info(f"Deleted {len(chat_histories)} chat history records for chat_session_id={chat_session.id}")

            # 删除聊天会话
            session.delete(chat_session)

        logger.info(f"Deleted {len(chat_sessions)} chat sessions for wiki_id={wiki_info.id}")

        # 6. 删除项目表（项目视图）
        from api.model.project import Project
        try:
            project = session.exec(
                select(Project).where(Project.wiki_id == wiki_info.id)
            ).first()
            if project:
                session.delete(project)
                logger.info(f"Deleted project for wiki_id={wiki_info.id}")
        except Exception as e:
            logger.warning(f"Failed to delete project for wiki_id={wiki_info.id}: {e}")
            # 使用原生SQL作为备选
            try:
                result = session.execute(
                    text("DELETE FROM ai_dw_project WHERE wiki_id = :wiki_id"),
                    {"wiki_id": wiki_info.id}
                )
                logger.info(f"Deleted {result.rowcount} project using raw SQL for wiki_id={wiki_info.id}")
            except Exception as sql_e:
                logger.error(f"Failed to delete project with raw SQL for wiki_id={wiki_info.id}: {sql_e}")

        # 7. 删除wiki仓库关联的研发云扩展信息1
        from api.model.wiki_dc_ext_1 import WikiDcExt1
        try:
            wiki_dc_ext_1 = session.exec(
                select(WikiDcExt1).where(WikiDcExt1.wiki_id == wiki_info.id)
            ).first()
            if wiki_dc_ext_1:
                session.delete(wiki_dc_ext_1)
                logger.info(f"Deleted wiki_dc_ext_1 for wiki_id={wiki_info.id}")
        except Exception as e:
            logger.warning(f"Failed to wiki_dc_ext_1 for wiki_id={wiki_info.id}: {e}")
            # 使用原生SQL作为备选
            try:
                result = session.execute(
                    text("DELETE FROM ai_dw_wiki_dc_ext_1 WHERE wiki_id = :wiki_id"),
                    {"wiki_id": wiki_info.id}
                )
                logger.info(f"Deleted {result.rowcount} wiki_dc_ext_1 using raw SQL for wiki_id={wiki_info.id}")
            except Exception as sql_e:
                logger.error(f"Failed to delete project with raw SQL for wiki_id={wiki_info.id}: {sql_e}")

        # 8. 删除wiki仓库关联的研发云扩展信息2
        from api.model.wiki_dc_ext_2 import WikiDcExt2
        try:
            wiki_dc_ext_2_list = session.exec(
                select(WikiDcExt2).where(WikiDcExt2.wiki_id == wiki_info.id)
            ).all()
            for wiki_dc_ext_2 in wiki_dc_ext_2_list:
                session.delete(wiki_dc_ext_2)
            logger.info(f"Deleted {len(wiki_dc_ext_2_list)} wiki_dc_ext_2 for wiki_id={wiki_info.id}")
        except Exception as e:
            logger.warning(f"Failed to delete wiki_dc_ext_2 for wiki_id={wiki_info.id}: {e}")
            # 尝试使用原生SQL删除
            try:
                result = session.execute(
                    text("DELETE FROM ai_dw_wiki_dc_ext_2 WHERE wiki_id = :wiki_id"),
                    {"wiki_id": wiki_info.id}
                )
                logger.info(f"Deleted {result.rowcount} wiki_dc_ext_2 using raw SQL for wiki_id={wiki_info.id}")
            except Exception as sql_e:
                logger.error(f"Failed to delete wiki_dc_ext_2 with raw SQL for wiki_id={wiki_info.id}: {sql_e}")

        # 9. 删除wiki与tag关联表
        from api.model.wiki_tag import WikiTag
        try:
            wiki_tag_list = session.exec(
                select(WikiTag).where(WikiTag.wiki_id == wiki_info.id)
            ).all()
            for wiki_tag in wiki_tag_list:
                session.delete(wiki_tag)
            logger.info(f"Deleted {len(wiki_tag_list)} wiki_tag for wiki_id={wiki_info.id}")
        except Exception as e:
            logger.warning(f"Failed to delete wiki_tag for wiki_id={wiki_info.id}: {e}")
            # 尝试使用原生SQL删除
            try:
                result = session.execute(
                    text("DELETE FROM ai_dw_wiki_tag WHERE wiki_id = :wiki_id"),
                    {"wiki_id": wiki_info.id}
                )
                logger.info(f"Deleted {result.rowcount} wiki_tag using raw SQL for wiki_id={wiki_info.id}")
            except Exception as sql_e:
                logger.error(f"Failed to delete wiki_tag with raw SQL for wiki_id={wiki_info.id}: {sql_e}")

        # 10. 删除应用与Wiki绑定关系表
        from api.model.app_wiki_rel import AppWikiRel
        try:
            app_wiki_rel_list = session.exec(
                select(AppWikiRel).where(AppWikiRel.wiki_id == wiki_info.id)
            ).all()
            for app_wiki_rel in app_wiki_rel_list:
                session.delete(app_wiki_rel)
            logger.info(f"Deleted {len(app_wiki_rel_list)} app_wiki_rel for wiki_id={wiki_info.id}")
        except Exception as e:
            logger.warning(f"Failed to delete app_wiki_rel for wiki_id={wiki_info.id}: {e}")
            # 尝试使用原生SQL删除
            try:
                result = session.execute(
                    text("DELETE FROM ai_dw_app_wiki_rela WHERE wiki_id = :wiki_id"),
                    {"wiki_id": wiki_info.id}
                )
                logger.info(f"Deleted {result.rowcount} app_wiki_rel using raw SQL for wiki_id={wiki_info.id}")
            except Exception as sql_e:
                logger.error(f"Failed to delete app_wiki_rel with raw SQL for wiki_id={wiki_info.id}: {sql_e}")

        # 11. 最后删除Wiki基本信息
        # 使用原生SQL删除，避免对象状态问题
        try:
            result = session.execute(
                text("DELETE FROM ai_dw_wiki_info WHERE wiki_id = :wiki_id"),
                {"wiki_id": wiki_id}
            )
            logger.info(f"Deleted WikiInfo for wiki_id={wiki_id}, affected rows: {result.rowcount}")
        except Exception as e:
            logger.error(f"Failed to delete WikiInfo with raw SQL for wiki_id={wiki_id}: {e}")
            # 如果原生SQL也失败，尝试重新查询并删除
            try:
                fresh_wiki_info = session.exec(
                    select(WikiInfo).where(WikiInfo.wiki_id == wiki_id)
                ).first()
                if fresh_wiki_info:
                    session.delete(fresh_wiki_info)
                    logger.info(f"Deleted WikiInfo using fresh query for wiki_id={wiki_id}")
                else:
                    logger.warning(f"WikiInfo not found for deletion: wiki_id={wiki_id}")
            except Exception as e2:
                logger.error(f"Failed to delete WikiInfo with fresh query for wiki_id={wiki_id}: {e2}")
                raise e2

        # 提交所有删除操作
        session.commit()
        logger.info(f"Successfully deleted all wiki data for wiki_id={wiki_id}")
        return True

    except Exception as e:
        logger.error(f"Error deleting wiki data for wiki_id={wiki_id}: {e}")
        session.rollback()
        return False


def search_wikis_with_role(
        session: Session,
        repo_name: Optional[str] = None,
        repo_owner: Optional[str] = None,
        repo_type: Optional[str] = None,
        language: Optional[str] = None,
        status: Optional[str] = None,
        statuses: Optional[List[str]] = None,
        user_id: Optional[str] = None,
        limit: int = 50,
        include_data: bool = False,
        offset: int = 0,
        has_super_admin_role: bool = False,
) -> List[any]:
    """
    搜索Wiki信息记录

    参数:
        session: 数据库会话
        repo_name: 仓库名称
        repo_owner: 仓库所有者
        repo_type: 仓库类型
        language: 语言
        status: 状态（单个状态）
        statuses: 状态列表（多个状态）
        limit: 最大返回数量
        user_id：用户标识
        include_data: 是否包含wiki_data字段

    返回:
        List[WikiInfo]: Wiki信息记录列表
    """

    statement = select(WikiInfo, Role.role_code, Role.access_level, UserInfo.user_name, UserInfo.user_code)
    subq = (
        select(WikiUserRole.role_id, WikiUserRole.wiki_id)
        .where(WikiUserRole.user_id == user_id)  # 当前登录用户ID
        .subquery()
    )
    wiki_user_role_alias = aliased(WikiUserRole, subq)

    # 基础连接（所有wiki_info都会返回）
    statement = (
        statement.join(
            UserInfo,
            UserInfo.id == WikiInfo.created_by,  # 连接创建者信息
        )
        .outerjoin(
            wiki_user_role_alias,
            WikiInfo.id == wiki_user_role_alias.wiki_id  # 左连接当前用户角色
        )
        .outerjoin(
            Role,
            Role.id == wiki_user_role_alias.role_id  # 左连接角色信息
        )
    )

    # 应用过滤条件
    if repo_name:
        statement = statement.where(WikiInfo.repo_name == repo_name)
    if repo_owner:
        statement = statement.where(WikiInfo.repo_owner == repo_owner)
    if repo_type:
        statement = statement.where(WikiInfo.repo_type == repo_type)
    if language:
        statement = statement.where(WikiInfo.language == language)
    # 处理状态过滤
    if statuses:
        statement = statement.where(WikiInfo.status.in_(statuses))
    elif status:
        statement = statement.where(WikiInfo.status == status)
        # 应用限制和排序
    statement = statement.order_by(Role.access_level.desc(), WikiInfo.visibility.asc(), WikiInfo.updated_time.desc())

    # 执行查询
    original_wikis = session.exec(statement).all()

    # 如果不需要包含wiki_data字段，则创建副本并将其wiki_data置为None以减小响应大小
    if original_wikis:
        from copy import copy
        wikis = []

        # 收集所有wiki的ID，用于批量查询标签
        wiki_ids = [wiki_info.id for wiki_info, _, _, _, _ in original_wikis]

        # 批量查询所有wiki的标签信息（只需要一次数据库查询）
        tags_by_wiki = {}
        if wiki_ids:
            try:
                # 使用一次查询获取所有wiki的标签
                tag_query = (
                    select(WikiTag.wiki_id, Tag.id, Tag.name, Tag.color, Tag.type, Tag.comments)
                    .join(Tag, WikiTag.tag_id == Tag.id)
                    .where(WikiTag.wiki_id.in_(wiki_ids))
                    .where(Tag.state == 1)
                )
                tag_results = session.exec(tag_query).all()

                # 按wiki_id分组标签
                for tag_result in tag_results:
                    wiki_id, tag_id, tag_name, tag_color, tag_type, tag_comments = tag_result
                    if wiki_id not in tags_by_wiki:
                        tags_by_wiki[wiki_id] = []
                    tags_by_wiki[wiki_id].append({
                        'id': tag_id,
                        'name': tag_name,
                        'color': tag_color,
                        'type': tag_type,
                        'comments': tag_comments
                    })
            except Exception as e:
                logger.warning(f"Failed to fetch tags for wikis: {e}")
                # 如果查询失败，所有wiki的tags都设为空列表
                tags_by_wiki = {wiki_id: [] for wiki_id in wiki_ids}

        for original_wiki in original_wikis:
            wiki_info, role_code, _, user_name, user_code = original_wiki
            # 将字典属性转换为对象
            wiki_obj = SimpleNamespace(**vars(wiki_info).copy())
            if not include_data:
                wiki_obj.wiki_data = None
            # 添加 role_id 和 role_code 属性
            role_code_ = role_code[0] if isinstance(role_code, list) else role_code
            wiki_obj.role_code = "wiki_grant" if has_super_admin_role else role_code_
            wiki_obj.user_name = user_name
            wiki_obj.user_code = user_code

            # 从预查询的标签映射中获取该wiki的标签
            wiki_obj.tags = tags_by_wiki.get(wiki_info.id, [])

            wikis.append(wiki_obj)
        return wikis

    return original_wikis

def add_wiki_user_role(
        session: Session,
        user_id: str,
        wiki_id: int,
        role_id: int,
        created_by: Optional[str] = None,
):
    """
        先删除用户在该wiki上的所有角色记录，再新增指定角色
        :param session: 数据库会话
        :param user_id: 用户ID
        :param wiki_id: wiki ID
        :param role_id: 角色ID
        :return: 新增的WikiUserRole对象
    """

    user_info = get_current_user()
    current_user_id = None
    if user_info:
        current_user_id = user_info.get("id")
    else:
        logger.warning("未获取到当前用户信息，将使用默认用户ID")
        current_user_id = 1  # 默认用户ID

    if created_by:
        current_user_id = created_by

    # 判断是否已经存在
    statement = select(WikiUserRole).where(
        WikiUserRole.user_id == user_id,
        WikiUserRole.wiki_id == wiki_id
    )
    wiki_user_role = session.exec(statement).first()

    if wiki_user_role:
        session.delete(wiki_user_role)
        session.flush()

    wiki_user_role = WikiUserRole(
        user_id=user_id,
        wiki_id=wiki_id,
        role_id=role_id or 5,
        created_by=current_user_id,
        created_date=datetime.now(),
    )
    session.add(wiki_user_role)
    session.flush()
    return wiki_user_role

def delete_wiki_user_role(session: Session,
        user_id: str,
        wiki_id: int,
        role_id: int,) -> bool:
    statement = select(WikiUserRole).where(
        WikiUserRole.user_id == user_id,
        WikiUserRole.wiki_id == wiki_id,
        WikiUserRole.role_id == role_id
    )
    wiki_user_role = session.exec(statement).first()

    if wiki_user_role:
        session.delete(wiki_user_role)
    return True

def batch_add_wiki_user_role(
        session: Session,
        users: list[str],
        wiki_id: int,
        role_id: int
) -> bool:
    for user_id in users:
        add_wiki_user_role(session, user_id, wiki_id, role_id)  # 假设已定义 add_wiki_user_role
    return True

def search_wikis_with_visibility(
        session: Session
) -> List[WikiInfo]:
    statement = select(WikiInfo).where(WikiInfo.visibility == 1)
    return session.exec(statement).all()

def get_wiki_user_role_by_id(session: Session,
        user_id: int,
        wiki_id: int):
    statement = select(WikiUserRole).where(
        WikiUserRole.user_id == user_id,
        WikiUserRole.wiki_id == wiki_id
    )
    wiki_user_role = session.exec(statement).first()

    return wiki_user_role


def check_user_wiki_priv(session: Session,
        wiki_info: WikiInfo, approver_id: Optional[str] = None) -> bool:
    """
    检查用户是否具备wiki的访问权限
    1. 超管直接返回True
    2. wiki是全员可见，返回True
    3. 在wiki_user_role中存在记录，返回True
    """
    user_info = get_current_user()
    user_id = 0
    if user_info:
        user_id = user_info.get("id")

    if approver_id:
        user_id = approver_id

    user_roles = select_user_roles(user_id)
    # 判断是否是超管
    has_admin_role = False
    if user_roles and len(user_roles) != 0:
        has_admin_role = any(role.role_id == 1 for role in user_roles) if user_roles else False
    if has_admin_role:
        return True

    visibility = wiki_info.visibility
    if visibility and visibility == 1:
        return True

    wiki_user_role = get_wiki_user_role_by_id(session,user_id, wiki_info.id)

    if wiki_user_role:
        return True

    return False

def get_wiki_info_by_id(session: Session, wiki_id: int) -> Optional[any]:
    """
    根据wiki主键ID获取Wiki信息记录

    参数:
        session: 数据库会话
        wiki_id: Wiki主键ID

    返回:
        Optional[WikiInfo]: 如果找到则返回Wiki信息记录，否则返回None
    """
    info = session.exec(select(WikiInfo).where(WikiInfo.id == wiki_id)).first()
    return WikiInfo(**info.model_dump()) if info else None
