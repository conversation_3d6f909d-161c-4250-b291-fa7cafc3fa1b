import logging
import asyncio
from typing import Optional
from fastapi import Request
from datetime import datetime

from api.database.base import session_scope
from api.service.wiki_service import get_job, retry_job  # 导入retry_job

async def process_wiki_job(job_id: str, resume: bool = False):
    """
    处理Wiki任务，支持普通模式和恢复模式
    
    参数:
        job_id: 任务ID
        resume: 是否以恢复模式运行
    """
    from api.wiki.generator import WikiGenerator
    from api.docchain.manager import docchain_manager
    from api.database.service import db_service
    
    try:
        # 获取任务信息
        with session_scope() as session:
            job = get_job(session, job_id)
            if not job:
                logging.error(f"找不到任务: {job_id}")
                return
            
            # 更新任务状态
            if resume:
                # 如果是恢复模式，保持原来的阶段，但状态改为processing
                job.status = "processing"
                job.stage_message = f"从{job.stage or '开始'}阶段继续处理"
                job.updated_time = datetime.now()
            else:
                # 如果是新任务，设置为初始状态
                job.status = "processing"
                job.stage = "init"  # 设置初始阶段
                job.stage_message = "任务开始处理"
            job.updated_time = datetime.now()
            
            session.commit()
                
            # 获取任务参数
            repo_url = job.repo_url
            repo_type = "github"  # 假设所有任务都是GitHub仓库
            branch = job.branch or "main"
            sub_repos_str = job.sub_repos
            token = job.token
            language = job.language or "zh"
            model_settings = job.model_settings or {}
            comprehensive = job.comprehensive
            excluded_dirs = job.excluded_dirs
            excluded_files = job.excluded_files
            included_dirs = job.included_dirs
            included_files = job.included_files
            wiki_id = job.wiki_info_id  # 确保这个字段已添加到WikiJob模型中
            
            # 从现有任务中提取topic ID信息
            existing_topic_id = job.topic_id
            existing_topic_id_code = job.topic_id_code
            existing_topic_id_doc = job.topic_id_doc
            
        # 创建Wiki生成器并启动任务
        wiki_generator = WikiGenerator(db_service, docchain_manager)
        logging.info(f"开始处理任务 {job_id}, resume={resume}, stage={job.stage}")
        
        await wiki_generator.start(
            job_id=job_id,
            repo_url=repo_url,
            repo_type=repo_type,
            branch=branch,
            sub_repos_str=sub_repos_str,
            token=token,
            language=language,
            model_settings=model_settings,
            comprehensive=comprehensive,
            existing_topic_id=existing_topic_id,
            existing_topic_id_code=existing_topic_id_code,
            existing_topic_id_doc=existing_topic_id_doc,
            excluded_dirs=excluded_dirs,
            excluded_files=excluded_files,
            included_dirs=included_dirs,
            included_files=included_files,
            wiki_id=wiki_id,
            resume=resume
        )
    except Exception as e:
        logging.error(f"处理任务 {job_id} 时出错: {e}", exc_info=True)
        # 更新任务状态为失败
        try:
            with session_scope() as session:
                job = get_job(session, job_id)
                if job:
                    job.status = "failed"
                    job.error_message = str(e)
                    job.updated_time = datetime.now()
                    session.commit()
        except Exception as inner_e:
            logging.error(f"更新任务状态失败: {inner_e}") 