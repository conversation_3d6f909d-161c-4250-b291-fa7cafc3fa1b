"""
K8s Job管理服务
管理Kubernetes Job的生命周期，包括创建、查询、更新和删除Job
"""

import logging
from typing import Optional, List, Tuple
from datetime import datetime
from sqlmodel import Session, select
from sqlalchemy import and_

from api.model.k8s_job import K8sJob
from api.database.base import session_scope

logger = logging.getLogger(__name__)


class K8sJobService:
    """K8s Job管理服务"""

    def __init__(self):
        pass

    def create_job(
        self,
        job_name: str,
        job_uid: int,
        job_username: str,
        created_by: int,
        running_status: Optional[int] = 4,  # 默认为pending状态
        is_available: int = 1,
        user_id: Optional[int] = None,
        wiki_id: Optional[int] = None
    ) -> Tuple[bool, Optional[K8sJob], Optional[str]]:
        """
        创建K8s Job记录

        Args:
            job_name: job名称
            job_uid: Linux UID
            job_username: Linux用户名
            created_by: 创建者ID
            running_status: 运行状态，默认为4(pending)
            is_available: 是否可分配，默认为1(是)
            user_id: 用户ID，可选
            wiki_id: Wiki ID，可选

        Returns:
            (是否成功, Job对象, 错误信息)
        """
        try:
            with session_scope() as session:
                # 检查job名称是否已存在
                existing_job = session.exec(
                    select(K8sJob).where(K8sJob.job_name == job_name)
                ).first()

                if existing_job:
                    error_msg = f"Job名称 {job_name} 已存在"
                    logger.warning(error_msg)
                    return False, None, error_msg

                # 检查UID是否已被使用
                existing_uid = session.exec(
                    select(K8sJob).where(K8sJob.job_uid == job_uid)
                ).first()

                if existing_uid:
                    error_msg = f"UID {job_uid} 已被使用"
                    logger.warning(error_msg)
                    return False, None, error_msg

                # 创建新Job记录
                new_job = K8sJob(
                    job_name=job_name,
                    job_uid=job_uid,
                    job_username=job_username,
                    running_status=running_status,
                    user_id=user_id,
                    wiki_id=wiki_id,
                    is_available=is_available,
                    created_by=created_by,
                    created_date=datetime.now()
                )

                session.add(new_job)
                session.commit()
                session.refresh(new_job)

                logger.info(f"成功创建K8s Job: {job_name}, UID: {job_uid}")
                return True, new_job, None

        except Exception as e:
            error_msg = f"创建K8s Job失败: {str(e)}"
            logger.error(error_msg)
            return False, None, error_msg

    def get_job_by_id(self, job_id: int) -> Optional[K8sJob]:
        """
        根据ID获取Job

        Args:
            job_id: Job ID

        Returns:
            Job对象或None
        """
        try:
            with session_scope() as session:
                job = session.get(K8sJob, job_id)
                if job:
                    # 在会话内访问属性，避免detached状态问题
                    _ = job.job_name
                    _ = job.job_username
                    _ = job.id
                    _ = job.job_uid
                    _ = job.running_status
                    _ = job.is_available
                    _ = job.user_id
                    _ = job.wiki_id
                    _ = job.created_by
                    _ = job.created_date
                    _ = job.update_by
                    _ = job.update_date
                return job
        except Exception as e:
            logger.error(f"获取Job失败: {str(e)}")
            return None

    def get_job_by_name(self, job_name: str) -> Optional[K8sJob]:
        """
        根据名称获取Job

        Args:
            job_name: Job名称

        Returns:
            Job对象或None
        """
        try:
            with session_scope() as session:
                job = session.exec(
                    select(K8sJob).where(K8sJob.job_name == job_name)
                ).first()
                if job:
                    # 在会话内访问属性，避免detached状态问题
                    _ = job.job_name
                    _ = job.job_username
                    _ = job.id
                    _ = job.job_uid
                    _ = job.running_status
                    _ = job.is_available
                    _ = job.user_id
                    _ = job.wiki_id
                    _ = job.created_by
                    _ = job.created_date
                    _ = job.update_by
                    _ = job.update_date
                return job
        except Exception as e:
            logger.error(f"根据名称获取Job失败: {str(e)}")
            return None

    def get_job_by_uid(self, job_uid: int) -> Optional[K8sJob]:
        """
        根据UID获取Job

        Args:
            job_uid: Job UID

        Returns:
            Job对象或None
        """
        try:
            with session_scope() as session:
                job = session.exec(
                    select(K8sJob).where(K8sJob.job_uid == job_uid)
                ).first()
                if job:
                    # 在会话内访问属性，避免detached状态问题
                    _ = job.job_name
                    _ = job.job_username
                    _ = job.id
                    _ = job.job_uid
                    _ = job.running_status
                    _ = job.is_available
                    _ = job.user_id
                    _ = job.wiki_id
                    _ = job.created_by
                    _ = job.created_date
                    _ = job.update_by
                    _ = job.update_date
                return job
        except Exception as e:
            logger.error(f"根据UID获取Job失败: {str(e)}")
            return None

    def get_jobs_by_user_id(self, user_id: int) -> List[K8sJob]:
        """
        根据用户ID获取Job列表

        Args:
            user_id: 用户ID

        Returns:
            Job对象列表
        """
        try:
            with session_scope() as session:
                jobs = session.exec(
                    select(K8sJob).where(K8sJob.user_id == user_id)
                ).all()
                # 在会话内访问所有属性，避免detached状态问题
                for job in jobs:
                    _ = job.job_name
                    _ = job.job_username
                    _ = job.id
                    _ = job.job_uid
                    _ = job.running_status
                    _ = job.is_available
                    _ = job.user_id
                    _ = job.wiki_id
                    _ = job.created_by
                    _ = job.created_date
                    _ = job.update_by
                    _ = job.update_date
                return list(jobs)
        except Exception as e:
            logger.error(f"根据用户ID获取Job列表失败: {str(e)}")
            return []

    def get_jobs_by_wiki_id(self, wiki_id: int) -> List[K8sJob]:
        """
        根据Wiki ID获取Job列表

        Args:
            wiki_id: Wiki ID

        Returns:
            Job对象列表
        """
        try:
            with session_scope() as session:
                jobs = session.exec(
                    select(K8sJob).where(K8sJob.wiki_id == wiki_id)
                ).all()
                # 在会话内访问所有属性，避免detached状态问题
                for job in jobs:
                    _ = job.job_name
                    _ = job.job_username
                    _ = job.id
                    _ = job.job_uid
                    _ = job.running_status
                    _ = job.is_available
                    _ = job.user_id
                    _ = job.wiki_id
                    _ = job.created_by
                    _ = job.created_date
                    _ = job.update_by
                    _ = job.update_date
                return list(jobs)
        except Exception as e:
            logger.error(f"根据Wiki ID获取Job列表失败: {str(e)}")
            return []

    def get_job_by_user_and_wiki(self, user_id: int, wiki_id: int) -> Optional[K8sJob]:
        """
        根据用户ID和Wiki ID获取Job

        Args:
            user_id: 用户ID
            wiki_id: Wiki ID

        Returns:
            Job对象或None
        """
        try:
            with session_scope() as session:
                job = session.exec(
                    select(K8sJob).where(
                        and_(
                            K8sJob.user_id == user_id,
                            K8sJob.wiki_id == wiki_id
                        )
                    )
                ).first()
                if job:
                    return K8sJob(**job.model_dump())
        except Exception as e:
            logger.error(f"根据用户ID和Wiki ID获取Job失败: {str(e)}")
            return None

    def get_available_job(self) -> Optional[K8sJob]:
        """
        获取一个可用的Job（is_available=1且状态为succeeded）

        Returns:
            可用的Job对象或None
        """
        try:
            with session_scope() as session:
                job = session.exec(
                    select(K8sJob).where(
                        and_(
                            K8sJob.is_available == 1,
                            K8sJob.running_status == 2  # succeeded状态
                        )
                    ).limit(1)
                ).first()
                if job:
                    return K8sJob(**job.model_dump())
                return None
        except Exception as e:
            logger.error(f"获取可用Job失败: {str(e)}")
            return None

    def list_jobs(
        self,
        running_status: Optional[int] = None,
        is_available: Optional[int] = None,
        user_id: Optional[int] = None,
        wiki_id: Optional[int] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[K8sJob]:
        """
        获取Job列表

        Args:
            running_status: 运行状态筛选
            is_available: 可用性筛选
            user_id: 用户ID筛选
            wiki_id: Wiki ID筛选
            limit: 限制数量
            offset: 偏移量

        Returns:
            Job对象列表
        """
        try:
            with session_scope() as session:
                query = select(K8sJob)

                # 添加筛选条件
                conditions = []
                if running_status is not None:
                    conditions.append(K8sJob.running_status == running_status)
                if is_available is not None:
                    conditions.append(K8sJob.is_available == is_available)
                if user_id is not None:
                    conditions.append(K8sJob.user_id == user_id)
                if wiki_id is not None:
                    conditions.append(K8sJob.wiki_id == wiki_id)

                if conditions:
                    query = query.where(and_(*conditions))

                query = query.limit(limit).offset(offset)
                jobs = session.exec(query).all()
                # 在会话内访问所有属性，避免detached状态问题

                return [K8sJob(**job.model_dump()) for job in jobs]

        except Exception as e:
            logger.error(f"获取Job列表失败: {str(e)}")
            return []

    def update_job_status(
        self,
        job_id: int,
        running_status: int,
        update_by: int
    ) -> Tuple[bool, Optional[str]]:
        """
        更新Job运行状态

        Args:
            job_id: Job ID
            running_status: 新的运行状态
            update_by: 更新者ID

        Returns:
            (是否成功, 错误信息)
        """
        try:
            with session_scope() as session:
                job = session.get(K8sJob, job_id)
                if not job:
                    error_msg = f"Job不存在: {job_id}"
                    logger.warning(error_msg)
                    return False, error_msg

                job.running_status = running_status
                job.update_by = update_by
                job.update_date = datetime.now()

                session.add(job)
                session.commit()

                logger.info(f"成功更新Job状态: {job.job_name}, 新状态: {running_status}")
                return True, None

        except Exception as e:
            error_msg = f"更新Job状态失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def update_job_availability(
        self,
        job_id: int,
        is_available: int,
        update_by: int
    ) -> Tuple[bool, Optional[str]]:
        """
        更新Job可用性

        Args:
            job_id: Job ID
            is_available: 是否可用
            update_by: 更新者ID

        Returns:
            (是否成功, 错误信息)
        """
        try:
            with session_scope() as session:
                job = session.get(K8sJob, job_id)
                if not job:
                    error_msg = f"Job不存在: {job_id}"
                    logger.warning(error_msg)
                    return False, error_msg

                job.is_available = is_available
                job.update_by = update_by
                job.update_date = datetime.now()

                session.add(job)
                session.commit()

                logger.info(f"成功更新Job可用性: {job.job_name}, is_available: {is_available}")
                return True, None

        except Exception as e:
            error_msg = f"更新Job可用性失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def assign_job_to_user_and_wiki(
        self,
        job_id: int,
        user_id: int,
        wiki_id: int,
        update_by: int
    ) -> Tuple[bool, Optional[str]]:
        """
        将Job分配给用户和Wiki

        Args:
            job_id: Job ID
            user_id: 用户ID
            wiki_id: Wiki ID
            update_by: 更新者ID

        Returns:
            (是否成功, 错误信息)
        """
        try:
            with session_scope() as session:
                job = session.get(K8sJob, job_id)
                if not job:
                    error_msg = f"Job不存在: {job_id}"
                    logger.warning(error_msg)
                    return False, error_msg

                job.user_id = user_id
                job.wiki_id = wiki_id
                job.is_available = 0
                job.update_by = update_by
                job.update_date = datetime.now()

                session.add(job)
                session.commit()

                logger.info(f"成功分配Job: {job.job_name} 给用户 {user_id} 和Wiki {wiki_id}")
                return True, None

        except Exception as e:
            error_msg = f"分配Job失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def unassign_job(
        self,
        job_id: int,
        update_by: int
    ) -> Tuple[bool, Optional[str]]:
        """
        取消Job的分配（清空user_id和wiki_id）

        Args:
            job_id: Job ID
            update_by: 更新者ID

        Returns:
            (是否成功, 错误信息)
        """
        try:
            with session_scope() as session:
                job = session.get(K8sJob, job_id)
                if not job:
                    error_msg = f"Job不存在: {job_id}"
                    logger.warning(error_msg)
                    return False, error_msg

                job.user_id = None
                job.wiki_id = None
                job.is_available = 1
                job.update_by = update_by
                job.update_date = datetime.now()

                session.add(job)
                session.commit()

                logger.info(f"成功取消Job分配: {job.job_name}")
                return True, None

        except Exception as e:
            error_msg = f"取消Job分配失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def delete_job(self, job_id: int) -> Tuple[bool, Optional[str]]:
        """
        删除Job记录

        Args:
            job_id: Job ID

        Returns:
            (是否成功, 错误信息)
        """
        try:
            with session_scope() as session:
                job = session.get(K8sJob, job_id)
                if not job:
                    error_msg = f"Job不存在: {job_id}"
                    logger.warning(error_msg)
                    return False, error_msg

                job_name = job.job_name
                session.delete(job)
                session.commit()

                logger.info(f"成功删除Job: {job_name}")
                return True, None

        except Exception as e:
            error_msg = f"删除Job失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def count_jobs(
        self,
        running_status: Optional[int] = None,
        is_available: Optional[int] = None,
        user_id: Optional[int] = None,
        wiki_id: Optional[int] = None
    ) -> int:
        """
        统计Job数量

        Args:
            running_status: 运行状态筛选
            is_available: 可用性筛选
            user_id: 用户ID筛选
            wiki_id: Wiki ID筛选

        Returns:
            Job数量
        """
        try:
            with session_scope() as session:
                query = select(K8sJob)

                # 添加筛选条件
                conditions = []
                if running_status is not None:
                    conditions.append(K8sJob.running_status == running_status)
                if is_available is not None:
                    conditions.append(K8sJob.is_available == is_available)
                if user_id is not None:
                    conditions.append(K8sJob.user_id == user_id)
                if wiki_id is not None:
                    conditions.append(K8sJob.wiki_id == wiki_id)

                if conditions:
                    query = query.where(and_(*conditions))

                jobs = session.exec(query).all()
                return len(jobs)

        except Exception as e:
            logger.error(f"统计Job数量失败: {str(e)}")
            return 0

    def get_max_job_uid(self) -> int:
        """
        获取最大的Job UID
        """
        try:
            with session_scope() as session:
                uid = session.exec(select(K8sJob.job_uid).order_by(K8sJob.job_uid.desc())).first()
                if uid:
                    return int(uid)
                else:
                    return 40000
        except Exception as e:
            logger.error(f"获取最大的Job UID失败: {str(e)}")
            return 40000

# 全局服务实例
_k8s_job_service = None


def get_k8s_job_service() -> K8sJobService:
    """获取K8s Job服务实例"""
    global _k8s_job_service
    if _k8s_job_service is None:
        _k8s_job_service = K8sJobService()
    return _k8s_job_service

