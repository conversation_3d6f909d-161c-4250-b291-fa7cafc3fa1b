import logging
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy import text
from sqlmodel import Session, and_, select

from api.common.constants import DeepWikiRole
from api.database.base import session_scope
from api.logging_config import setup_logging

from api.utils.git_utils import extract_repo_info
from api.model.git_repository import AiDwGitRepository
from api.model.wiki_repository_relation import AiDwWikiRepositoryRelation

setup_logging()
logger = logging.getLogger(__name__)

# 除了失败的其他都查询出来
WIKI_QUERY_STATUS_CONDITION = "wi.status != 'failed'"

VIEW_TYPE_NORMAL_CONDITION = "wi.wiki_type = 1"
VIEW_TYPE_PROJECT_CONDITION = "wi.wiki_type = 2"
VIEW_TYPE_NORMAL = 1
VIEW_TYPE_PROJECT = 2
 
def search_wikis_4_unlogin(keyword: Optional[str] = None, page: int = 1, page_size: int = 50, view_type: int = 1) -> Dict[str, Any]:
    """
    搜索Wiki列表（未登录用户）

    Args:
        keyword (Optional[str]): 搜索关键词
        page (int): 页码，从1开始
        page_size (int): 每页数量

    Returns:
        Dict[str, Any]: 包含wikis列表和总数的字典
    """
    where_conditions, params = _build_search_conditions(keyword, view_type)
    with session_scope() as session:   
        # 构建基础查询SQL
        base_sql = _build_base_wiki_query(view_type)
        where_clause = " WHERE " + " AND ".join(where_conditions)

        # 构建完整查询SQL

        order_clause = " ORDER BY wi.visibility ASC, wi.created_time DESC "
        
        # 分页
        offset = (page - 1) * page_size
        limit_clause = f" LIMIT {page_size} OFFSET {offset}"
        
        query_sql = base_sql + where_clause + order_clause + limit_clause
        # 执行查询
        result = session.execute(text(query_sql), params).fetchall()
        
        # 查询总数，使用公共方法
        total_count = _query_total_count(session, keyword, where_clause, params)
        
        # 获取wiki IDs用于批量查询关联数据
        wiki_ids = [row.wiki_table_id for row in result]
        
        # 批量查询标签信息
        tags_data = _get_wiki_tags(session, wiki_ids, False) if wiki_ids else {}
        
        # 批量查询扩展信息2
        # ext2_data = _get_wiki_ext2_data(session, wiki_ids, False) if wiki_ids else {}
        ext2_data = {}
        
        if view_type == VIEW_TYPE_NORMAL:
        # 组装结果
            wikis = _build_wiki_data_list(result, tags_data, ext2_data, False)
        else:
            ext2_data = _get_wiki_ext2_data(session, wiki_ids, True)
            wikis = _build_project_wiki_data_list(result, tags_data, ext2_data, is_login=False, is_super_admin=False)
        
        return {
            "wikis": wikis,
            "total": total_count
        }

def search_wikis_4_login(user_id: int, keyword: Optional[str] = None, page: int = 1, page_size: int = 50, view_type: int = 1) -> Dict[str, Any]:
    """
    登录用户搜索Wiki列表

    Args:
        user_id (int): 当前登录用户ID
        keyword (Optional[str]): 搜索关键词
        page (int): 页码，从1开始
        page_size (int): 每页数量

    Returns:
        Dict[str, Any]: 包含wikis列表和总数的字典，每个wiki包含role_code字段
    """
    
    with session_scope() as session:
        # 1. 检查是否是超级管理员
        from api.service.priv_checker import check_is_super_admin
        is_super_admin = check_is_super_admin(session, user_id)
        if view_type == VIEW_TYPE_NORMAL:
            # if is_super_admin:
            #     # 超级管理员可以看到所有wiki，角色编码为wiki_grant
            #     return _search_wikis_for_super_admin(session, keyword, page, page_size)
            # else:
            #     # 普通用户只能看到有权限的wiki
            #     return _search_wikis_for_normal_user(session, user_id, keyword, is_super_admin, page, page_size)
            return _search_wikis_for_normal_user(session, user_id, keyword, is_super_admin, page, page_size)
        else:
            return _search_wikis_for_project_view(session, user_id, keyword, page, page_size, is_super_admin)

def _search_wikis_for_project_view(session: Session, user_id: int, keyword: Optional[str], page: int, page_size: int, is_super_admin: bool = False) -> Dict[str, Any]:
    """
    项目视图查询wiki
    """
    base_sql = """
        SELECT DISTINCT
        wi.id as wiki_table_id,
        wi.wiki_id,
        wi.repo_owner,
        wi.repo_name,
        wi.branch,
        wi.repo_type,
        wi.updated_time,
        wi.created_time,
        wi.comprehensive,
        wi.language,
        wi.visibility,
        wi.created_by,
        u.user_code,
        u.user_name,
        u.org,
        wi.created_by,
        wi.owner_id,
        p.project_name,
        p.project_code,
        p.pm_name,
        p.pm_id,
        wiki_user_role.role_code,
        wiki_user_role.access_level
    FROM ai_dw_wiki_info wi
    LEFT JOIN ai_dw_user u ON wi.owner_id = u.id AND u.state = 1
    LEFT JOIN ai_dw_project p ON wi.id = p.wiki_id
    LEFT JOIN (
    SELECT wur.wiki_id, wur.user_id, r.access_level, r.role_code
    FROM ai_dw_wiki_user_role wur
    JOIN ai_dw_role r ON wur.role_id = r.id
    JOIN (
        SELECT DISTINCT
            wiki_id, 
            user_id, 
            MAX(access_level) AS max_access_level
        FROM ai_dw_wiki_user_role wur
        JOIN ai_dw_role r ON wur.role_id = r.id
        where wur.user_id = :user_id
        GROUP BY wiki_id, user_id
        ) max_levels ON wur.wiki_id = max_levels.wiki_id 
            AND wur.user_id = max_levels.user_id
            AND r.access_level = max_levels.max_access_level
) wiki_user_role ON wi.id = wiki_user_role.wiki_id
    """
    where_conditions, params = _build_search_conditions(keyword, VIEW_TYPE_PROJECT)
    params['user_id'] = user_id
    where_clause = " WHERE " + " AND ".join(where_conditions)
    order_clause = " ORDER BY wi.created_time DESC "
    offset = (page - 1) * page_size
    limit_clause = f" LIMIT {page_size} OFFSET {offset}"
    query_sql = base_sql + where_clause + order_clause + limit_clause
    result = session.execute(text(query_sql), params).fetchall()
    
    total_count = _query_total_count(session, keyword, where_clause, params)
    
    # 获取wiki IDs用于批量查询关联数据
    wiki_ids = [row.wiki_table_id for row in result]
    
    # 批量查询标签信息
    tags_data = _get_wiki_tags(session, wiki_ids, False) if wiki_ids else {}
    
    ext2_data = _get_wiki_ext2_data(session, wiki_ids, True)
    # 组装结果，使用现有的构建函数
    wikis = _build_project_wiki_data_list(result, tags_data, ext2_data, is_login=True, is_super_admin=is_super_admin)
    
    return {
        "wikis": wikis,
        "total": total_count
    }


def _search_wikis_for_super_admin(session: Session, keyword: Optional[str], page: int, page_size: int) -> Dict[str, Any]:
    """
    超级管理员查询所有wiki

    Args:
        session (Session): 数据库会话
        keyword (Optional[str]): 搜索关键词
        page (int): 页码，从1开始
        page_size (int): 每页数量

    Returns:
        Dict[str, Any]: 包含wikis列表和总数的字典
    """
    base_sql = _build_base_wiki_query()
    where_conditions, params = _build_search_conditions(keyword)
    
    # 构建完整查询SQL
    where_clause = " WHERE " + " AND ".join(where_conditions)
    # 排序：按照角色访问级别降序，是否全局可见升序，创建时间降序
    order_clause = """
    ORDER BY 
        wi.visibility ASC,
        wi.created_time DESC
    """
    
    # 分页
    offset = (page - 1) * page_size
    limit_clause = f" LIMIT {page_size} OFFSET {offset}"
    
    query_sql = base_sql + where_clause + order_clause + limit_clause
    
    # 执行查询
    result = session.execute(text(query_sql), params).fetchall()
    
    # 查询总数，使用公共方法
    total_count = _query_total_count(session, keyword, where_clause, params)
    
    # 获取wiki IDs用于批量查询关联数据
    wiki_ids = [row.wiki_table_id for row in result]
    
    # 批量查询标签信息
    tags_data = _get_wiki_tags(session, wiki_ids, False) if wiki_ids else {}
    
    # 批量查询扩展信息2
    ext2_data = {}
    
    # 组装结果
    wikis = _build_wiki_data_list(result, tags_data, ext2_data, True, True)
    
    return {
        "wikis": wikis,
        "total": total_count
    }

def _search_wikis_for_normal_user(session: Session, user_id: int, keyword: Optional[str], is_super_admin: bool, page: int, page_size: int) -> Dict[str, Any]:
    """
    普通用户查询有权限的wiki

    Args:
        session (Session): 数据库会话
        user_id (int): 用户ID
        keyword (Optional[str]): 搜索关键词
        page (int): 页码，从1开始
        page_size (int): 每页数量

    Returns:
        Dict[str, Any]: 包含wikis列表和总数的字典
    """
    # TODO 先使用这种方式来写 后面mysql升级到8.x之后, 查询角色相关的再改成窗口函数

    # 构建包含用户角色信息的查询SQL
    base_sql = """
    SELECT DISTINCT
        wi.id as wiki_table_id,
        wi.wiki_id,
        wi.repo_owner,
        wi.repo_name,
        wi.branch,
        wi.repo_type,
        wi.updated_time,
        wi.created_time,
        wi.comprehensive,
        wi.language,
        wi.visibility,
        wi.created_by,
        wi.owner_id,
        u.user_code,
        u.user_name,
        u.org,
        ext1.product_version_code,
        ext1.product_name,
        ext1.product_line_name,
        ext1.product_version_id,
        ext1.product_id,
        ext1.product_line_id,
        wiki_user_role.role_code,
        wiki_user_role.access_level
    FROM ai_dw_wiki_info wi
    LEFT JOIN ai_dw_user u ON wi.owner_id = u.id AND u.state = 1
    LEFT JOIN ai_dw_wiki_dc_ext_1 ext1 ON wi.id = ext1.wiki_id
    LEFT JOIN (
        SELECT wur.wiki_id, wur.user_id, r.access_level, r.role_code
        FROM ai_dw_wiki_user_role wur
        JOIN ai_dw_role r ON wur.role_id = r.id
        JOIN (
            SELECT DISTINCT
                wiki_id, 
                user_id, 
                MAX(access_level) AS max_access_level
            FROM ai_dw_wiki_user_role wur
            JOIN ai_dw_role r ON wur.role_id = r.id
            where wur.user_id = :user_id
            GROUP BY wiki_id, user_id
            ) max_levels ON wur.wiki_id = max_levels.wiki_id 
             AND wur.user_id = max_levels.user_id
             AND r.access_level = max_levels.max_access_level
    ) wiki_user_role ON wi.id = wiki_user_role.wiki_id
    """
    
    where_conditions, params = _build_search_conditions(keyword, VIEW_TYPE_NORMAL)
    params['user_id'] = user_id
    
    # 添加基础状态条件
    where_clause = " WHERE " + " AND ".join(where_conditions)

    # 排序：按照是否全局可见升序，角色访问级别降序，创建时间降序
    order_clause = """
    ORDER BY 
        wi.visibility ASC,
        wiki_user_role.access_level DESC,
        wi.created_time DESC
    """
    
    # 分页
    offset = (page - 1) * page_size
    limit_clause = f" LIMIT {page_size} OFFSET {offset}"
    
    query_sql = base_sql + where_clause + order_clause + limit_clause
    
    # 执行查询
    result = session.execute(text(query_sql), params).fetchall()
    
    # 查询总数，使用公共方法（普通用户count查询需要去掉user_id参数）
    total_count = _query_total_count(session, keyword, where_clause, params, remove_user_id=True)
    
    # 获取wiki IDs用于批量查询关联数据
    wiki_ids = [row.wiki_table_id for row in result]
    
    # 批量查询标签信息
    tags_data = _get_wiki_tags(session, wiki_ids, False) if wiki_ids else {}
    
    # 批量查询扩展信息2
    ext2_data = {}
    
    # 组装结果
    wikis = _build_wiki_data_list(result=result, tags_data=tags_data, ext2_data=ext2_data, is_super_admin=is_super_admin, is_login=True)
    
    return {
        "wikis": wikis,
        "total": total_count
    }

def get_wiki_detail(wiki_id: str) -> Optional[Dict[str, Any]]:
    """
    根据wiki_id查询Wiki详情

    Args:
        wiki_id (str): Wiki的ID

    Returns:
        Optional[Dict[str, Any]]: Wiki详情信息，如果不存在返回None
    """
    with session_scope() as session:   
        wiki_basic_info = get_wiki_basic_info(wiki_id)
        if wiki_basic_info.get('type') == 1:
            # 构建详情查询SQL
            detail_sql = """
            SELECT DISTINCT
                wi.id as wiki_table_id,
                wi.wiki_id,
                wi.repo_owner,
                wi.repo_name,
                wi.branch,
                wi.repo_type,
                wi.updated_time,
                wi.comprehensive,
                wi.language,
                wi.visibility,
                wi.status,
                wi.created_time,
                wi.repo_url,
                wi.description as comments,
                wi.wiki_type as type,
                wi.project_topic_id as topic_id,
                u.user_code,
                u.user_name,
                u.org,
                ext1.product_version_code,
                ext1.product_name,
                ext1.product_line_name,
                ext1.product_version_id,
                ext1.product_id,
                ext1.product_line_id
            FROM ai_dw_wiki_info wi
            LEFT JOIN ai_dw_user u ON wi.created_by = u.id AND u.state = 1
            LEFT JOIN ai_dw_wiki_dc_ext_1 ext1 ON wi.id = ext1.wiki_id
            WHERE wi.wiki_id = :wiki_id
            """

        else:
            detail_sql = """
            SELECT DISTINCT
                wi.id as wiki_table_id,
                wi.wiki_id,
                wi.repo_owner,
                wi.repo_name,
                wi.branch,
                wi.repo_type,
                wi.updated_time,
                wi.comprehensive,
                wi.language,
                wi.visibility,
                wi.status,
                wi.created_time,
                wi.repo_url,
                wi.description as comments,
                wi.wiki_type as type,
                wi.project_topic_id as topic_id,
                '' as sub_repos,
                u.user_code,
                u.user_name,
                u.org,
                p.project_name,
                p.project_code,
                p.pm_name,
                p.pm_id
            FROM ai_dw_wiki_info wi
            LEFT JOIN ai_dw_user u ON wi.created_by = u.id AND u.state = 1
            LEFT JOIN ai_dw_project p ON wi.id = p.wiki_id
            WHERE wi.wiki_id = :wiki_id
            """
        params = {'wiki_id': wiki_id}


        result = session.execute(text(detail_sql), params).fetchone()
        
        if not result:
            return None
        
        # 获取标签信息
        tags_data = _get_wiki_tags(session, [result.wiki_table_id], True)
        
        # 获取扩展信息2
        ext2_data = _get_wiki_ext2_data(session, [result.wiki_table_id], True)
        
        # 组装详情结果
        if result.type == 1:
            wiki_detail = {
                "id": result.wiki_table_id,
                "wiki_id": result.wiki_id,
                "repo_owner": result.repo_owner,
                "repo_name": result.repo_name,
                "repo_type": result.repo_type,
                "repo_url": result.repo_url,
                "updated_time": result.updated_time,
                "created_time": result.created_time,
                "language": result.language,
                "comprehensive": bool(result.comprehensive),
                "branch": result.branch,
                "user_name": result.user_name or "",
                "user_code": result.user_code or "",
                "visibility": result.visibility,
                "status": result.status,
                "comments": result.comments,
                "topic_id": result.topic_id,
                "tags": tags_data.get(result.wiki_table_id, []),
                "dev_cloud": _build_dev_cloud_data(result, ext2_data.get(result.wiki_table_id, []), True)
            }
        else:
            wiki_detail = {
                "id": result.wiki_table_id,
                "wiki_id": result.wiki_id,
                "repo_owner": result.repo_owner,
                "repo_name": result.repo_name,
                "repo_type": result.repo_type,
                "repo_url": result.repo_url,
                "updated_time": result.updated_time,
                "created_time": result.created_time,
                "language": result.language,
                "comprehensive": bool(result.comprehensive),
                "branch": result.branch,
                "user_name": result.user_name or "",
                "user_code": result.user_code or "",
                "visibility": result.visibility,
                "status": result.status,
                "comments": result.comments,
                "topic_id": result.topic_id,
                "tags": tags_data.get(result.wiki_table_id, []),
                "project_name": result.project_name,
                "project_code": result.project_code,
                "pm_name": result.pm_name,
                "pm_id": result.pm_id,
                "dev_cloud": {"releases": _build_release_pkg_data(ext2_data.get(result.wiki_table_id, []), True)},
                "products": get_wiki_sub_repos(session, wiki_id)
            }
        
        return wiki_detail

def get_wiki_sub_repos(session: Session, wiki_id: str):
    try:
        sub_repos = session.exec(select(AiDwGitRepository).join(AiDwWikiRepositoryRelation, and_(AiDwWikiRepositoryRelation.repository_id == AiDwGitRepository.id, AiDwWikiRepositoryRelation.wiki_id == wiki_id, AiDwWikiRepositoryRelation.is_main_repo == False))).all()
        result = []
        for sub_repo in sub_repos:
            _, repo_name, _ = extract_repo_info(sub_repo.repo_url)
            item = {"productName": repo_name, "repo_url": sub_repo.repo_url}
            result.append(item)
        return result
    except Exception as ex:
        logger.error(f"Failed to get sub repos for wiki[{wiki_id}], cause by: {ex}")
        return []

def resolve_sub_repo(json_str: str) -> List[Dict[str, any]]:
    import json
    if json_str:
        try:
            sub_repos = json.loads(json_str)
            result = []
            for sub_repo in sub_repos:
                repo_url = sub_repo.get("url", "")
                if repo_url:
                    product = { "repo_url": repo_url }
                    owner, repo_name, host = extract_repo_info(repo_url)
                    productName = sub_repo.get("productName", repo_name)
                    product["productName"] = productName
                    result.append(product)
            return result
        except Exception as ex:
            logger.error(f"Failed to resolve subRepo, cause by: {ex}")
    return []
    
def get_wiki_basic_info(wiki_id: str) -> Optional[Dict[str, Any]]:
    """
    获取Wiki基本信息（不包含关联数据，性能更好）

    Args:
        wiki_id (str): Wiki的ID

    Returns:
        Optional[Dict[str, Any]]: Wiki基本信息，如果不存在返回None
    """
    sql = """
    SELECT 
        wi.id,
        wi.wiki_id,
        wi.repo_owner,
        wi.repo_name,
        wi.branch,
        wi.repo_type,
        wi.repo_url,
        wi.status,
        wi.language,
        wi.comprehensive,
        wi.visibility,
        wi.created_time,
        wi.updated_time,
        wi.wiki_type as type
    FROM ai_dw_wiki_info wi
    WHERE wi.wiki_id = :wiki_id
    """
    with session_scope() as session:

        result = session.execute(text(sql), {'wiki_id': wiki_id}).fetchone()
        
        if not result:
            return None
        
        return {
            "id": result.id,
            "wiki_id": result.wiki_id,
            "repo_owner": result.repo_owner,
            "repo_name": result.repo_name,
            "repo_type": result.repo_type,
            "repo_url": result.repo_url,
            "status": result.status,
            "language": result.language,
            "comprehensive": bool(result.comprehensive),
            "branch": result.branch,
            "visibility": result.visibility,
            "created_time": result.created_time,
            "updated_time": result.updated_time,
            "type": result.type
        }


def get_wiki_basic_info_by_id(id: int) -> Optional[Dict[str, Any]]:
    """
    获取Wiki基本信息（不包含关联数据，性能更好）

    Args:
        wiki_id (str): Wiki的ID

    Returns:
        Optional[Dict[str, Any]]: Wiki基本信息，如果不存在返回None
    """
    sql = """
    SELECT 
        wi.id,
        wi.wiki_id,
        wi.repo_owner,
        wi.repo_name,
        wi.branch,
        wi.repo_type,
        wi.repo_url,
        wi.status,
        wi.language,
        wi.comprehensive,
        wi.visibility,
        wi.created_time,
        wi.updated_time,
        wi.wiki_type as type
    FROM ai_dw_wiki_info wi
    WHERE wi.id = :id
    """
    with session_scope() as session:

        result = session.execute(text(sql), {'id': id}).fetchone()
        
        if not result:
            return None
        
        return {
            "id": result.id,
            "wiki_id": result.wiki_id,
            "repo_owner": result.repo_owner,
            "repo_name": result.repo_name,
            "repo_type": result.repo_type,
            "repo_url": result.repo_url,
            "status": result.status,
            "language": result.language,
            "comprehensive": bool(result.comprehensive),
            "branch": result.branch,
            "visibility": result.visibility,
            "created_time": result.created_time,
            "updated_time": result.updated_time,
            "type": result.type
        }


def count_non_failed_wikis() -> int:
    """
    查询状态不为 failed 的 wiki 数量

    Returns:
        int: 状态不为 failed 的 wiki 总数
    """
    with session_scope() as session:
        # 迁移到新表 ai_dw_wiki_info 统计
        sql = "SELECT COUNT(*) FROM ai_dw_wiki_info wi WHERE " + WIKI_QUERY_STATUS_CONDITION
        result = session.execute(text(sql)).scalar()
        return int(result) if result is not None else 0        
   
def _get_wiki_tags(session: Session, wiki_ids: List[int], with_id: bool = False) -> Dict[int, List[Dict[str, str]]]:
    """
    批量获取Wiki标签信息

    Args:
        session (Session): 数据库会话
        wiki_ids (List[int]): Wiki的ID列表
        with_id (bool): 是否包含标签ID和类型

    Returns:
        Dict[int, List[Dict[str, str]]]: 每个wiki_id对应的标签列表
    """
    if not wiki_ids:
        return {}
        
    wiki_ids_str = ','.join(map(str, wiki_ids))
    sql = f"""
    SELECT wt.wiki_id, t.id, t.name, t.color, t.type, t.module_type
    FROM ai_dw_wiki_tag wt
    JOIN ai_dw_tag t ON wt.tag_id = t.id
    WHERE wt.wiki_id IN ({wiki_ids_str})
    ORDER BY wt.seq
    """
    
    result = session.execute(text(sql)).fetchall()
    
    tags_dict = {}
    for row in result:
        wiki_id = row.wiki_id
        if wiki_id not in tags_dict:
            tags_dict[wiki_id] = []
        tag = {
            "name": row.name,
            "color": row.color,
            "module_type": row.module_type
        }
        if with_id:
            tag["id"] = row.id
            tag["type"] = row.type

        tags_dict[wiki_id].append(tag)
    
    return tags_dict

def _get_wiki_ext2_data(session: Session, wiki_ids: List[int], with_id: bool) -> Dict[int, List[Dict[str, str]]]:
    """
    批量获取Wiki扩展信息2

    Args:
        session (Session): 数据库会话
        wiki_ids (List[int]): Wiki的ID列表
        with_id (bool): 是否包含发布包ID和解决方案ID

    Returns:
        Dict[int, List[Dict[str, str]]]: 每个wiki_id对应的扩展信息2列表
    """
    if not wiki_ids:
        return {}
        
    wiki_ids_str = ','.join(map(str, wiki_ids))
    sql = f"""
    SELECT wiki_id, release_pkg_id, release_pkg_code, solution_id, solution_name
    FROM ai_dw_wiki_dc_ext_2
    WHERE wiki_id IN ({wiki_ids_str})
    ORDER BY wiki_id, created_date DESC
    """
    
    result = session.execute(text(sql)).fetchall()
    
    ext2_dict = {}
    for row in result:
        wiki_id = row.wiki_id
        if wiki_id not in ext2_dict:
            ext2_dict[wiki_id] = []
        release_pkg = {
            "release_pkg_code": row.release_pkg_code,
            "solution_name": row.solution_name
        }
        if with_id:
            release_pkg["release_pkg_id"] = row.release_pkg_id
            release_pkg["solution_id"] = row.solution_id

        ext2_dict[wiki_id].append(release_pkg)
    
    return ext2_dict

def _build_dev_cloud_data(row, ext2_list: List[Dict[str, str]], with_id: bool) -> Dict[str, Any]:
    """
    构建dev_cloud数据

    Args:
        row: Wiki主表/扩展表的查询结果行
        ext2_list (List[Dict[str, str]]): 扩展信息2列表
        with_id (bool): 是否包含ID字段

    Returns:
        Dict[str, Any]: dev_cloud相关数据
    """
    dev_cloud = {
        "product_version_code": row.product_version_code if row.product_version_code else "V1.0",
        "product_name": row.product_name if row.product_name else row.repo_name,
        "product_line_name": row.product_line_name if row.product_line_name else row.org,
        "releases": []
    }
    
    if with_id:
        dev_cloud["product_version_id"] = row.product_version_id
        dev_cloud["product_id"] = row.product_id
        dev_cloud["product_line_id"] = row.product_line_id

    # 添加发布包和解决方案信息
    for ext2 in ext2_list:
        release_pkg = {
            "release_pkg_code": ext2["release_pkg_code"],
            "solution_name": ext2["solution_name"]
        }
        if with_id:
            release_pkg["release_pkg_id"] = ext2["release_pkg_id"]
            release_pkg["solution_id"] = ext2["solution_id"]

        dev_cloud["releases"].append(release_pkg)
    
    return dev_cloud

def _build_release_pkg_data(ext2_list: List[Dict], with_id: bool) -> List[Dict[str, Any]]:
    """
    构建发布包数据
    """
    if not ext2_list:
        return []
    release_pkgs = []
    ext2 = ext2_list[0]
    release_pkg = {
        "release_pkg_code": ext2["release_pkg_code"],
        "solution_name": ext2["solution_name"]
    }
    if with_id:
            release_pkg["release_pkg_id"] = ext2["release_pkg_id"]
            release_pkg["solution_id"] = ext2["solution_id"]
    release_pkgs.append(release_pkg)
    return release_pkgs

def _build_wiki_data_list(result: List, tags_data: Dict, ext2_data: Dict, is_super_admin: bool = False, is_login: bool = False) -> List[Dict[str, Any]]:
    """
    构建Wiki数据列表

    Args:
        result (List): 查询结果列表
        tags_data (Dict): 标签数据
        ext2_data (Dict): 扩展信息2数据
        is_super_admin (bool): 是否超级管理员
        is_login (bool): 是否登录用户

    Returns:
        List[Dict[str, Any]]: Wiki数据列表
    """
    wikis = []
    for row in result:
        wiki_data = {
            "id": row.wiki_table_id,
            "wiki_id": row.wiki_id,
            "owner": row.repo_owner,
            "repo": row.repo_name,
            "repo_type": row.repo_type,
            "updated_time": row.updated_time,
            "created_time": row.created_time,
            "language": row.language,
            "comprehensive": bool(row.comprehensive),
            "branch": row.branch,
            "userName": row.user_name or "",
            "userCode": row.user_code or "",
            "visibility": row.visibility,
            "createdBy": row.created_by,
            "ownerId": row.owner_id,
            "tags": tags_data.get(row.wiki_table_id, []),
            "dev_cloud": _build_dev_cloud_data(row, ext2_data.get(row.wiki_table_id, []), False)
        }
        
        # 如果是登录用户查询，添加角色信息
        if is_login:
            if is_super_admin:
                wiki_data["role_code"] = DeepWikiRole.WIKI_GRANT.value.role_code
            elif hasattr(row, 'role_code'):
                wiki_data["role_code"] = row.role_code
            if not wiki_data["role_code"] and row.visibility == 1:
                wiki_data["role_code"] = DeepWikiRole.WIKI_ACCESS.value.role_code
                
        wikis.append(wiki_data)
    
    return wikis

def _build_project_wiki_data_list(result: List, tags_data: Dict, ext2_data: Dict, is_login: bool = False, is_super_admin: bool = False) -> List[Dict[str, Any]]:
    """
    构建项目视图的Wiki数据列表
    
    Args:
        result (List): 查询结果列表
        tags_data (Dict): 标签数据字典
        ext2_data (Dict): 扩展信息2数据字典
        is_login (bool): 是否登录用户
        is_super_admin (bool): 是否超级管理员
    Returns:
        List[Dict[str, Any]]: 格式化后的wiki数据列表
    """
    wikis = []
    
    for row in result:
        wiki_data = {
            "id": row.wiki_table_id,
            "wiki_id": row.wiki_id,
            "owner": row.repo_owner,
            "repo": row.repo_name,
            "repo_type": row.repo_type,
            "updated_time": row.updated_time.isoformat() if row.updated_time else None,
            "created_time": row.created_time.isoformat() if row.created_time else None,
            "language": row.language,
            "comprehensive": bool(row.comprehensive),
            "branch": row.branch,
            "userName": row.user_name or "",
            "userCode": row.user_code or "",
            "visibility": row.visibility,
            "createdBy": row.created_by,
            "ownerId": row.owner_id,
            "tags": tags_data.get(row.wiki_table_id, []),
            # 项目相关信息
            "project_name": getattr(row, 'project_name', None),
            "project_code": getattr(row, 'project_code', None),
            "pm_name": getattr(row, 'pm_name', None),
            "pm_id": getattr(row, 'pm_id', None),
            # 扩展信息
            "dev_cloud": {"releases": _build_release_pkg_data(ext2_data.get(row.wiki_table_id, []), True)}
        }
        if is_login:
            if is_super_admin:
                wiki_data["role_code"] = DeepWikiRole.WIKI_GRANT.value.role_code
            elif hasattr(row, 'role_code'):
                wiki_data["role_code"] = row.role_code
            if not wiki_data["role_code"] and row.visibility == 1:
                wiki_data["role_code"] = DeepWikiRole.WIKI_ACCESS.value.role_code
        wikis.append(wiki_data)
    
    return wikis
    
def _build_base_wiki_query(view_type: Optional[int]) -> str:
    """
    构建基础Wiki查询SQL

    Returns:
        str: 基础Wiki查询SQL字符串
    """
    if view_type == VIEW_TYPE_NORMAL:
        return """
        SELECT DISTINCT
            wi.id as wiki_table_id,
            wi.wiki_id,
            wi.repo_owner,
            wi.repo_name,
            wi.branch,
            wi.repo_type,
            wi.updated_time,
            wi.created_time,
            wi.comprehensive,
            wi.language,
            wi.visibility,
            wi.created_by,
            u.user_code,
            u.user_name,
            u.org,
            wi.created_by,
            wi.owner_id,
            ext1.product_version_code,
            ext1.product_name,
            ext1.product_line_name,
            ext1.product_version_id,
            ext1.product_id,
            ext1.product_line_id
        FROM ai_dw_wiki_info wi
        LEFT JOIN ai_dw_user u ON wi.owner_id = u.id AND u.state = 1
        LEFT JOIN ai_dw_wiki_dc_ext_1 ext1 ON wi.id = ext1.wiki_id
        """
    else:
            return """
        SELECT DISTINCT
        wi.id as wiki_table_id,
        wi.wiki_id,
        wi.repo_owner,
        wi.repo_name,
        wi.branch,
        wi.repo_type,
        wi.updated_time,
        wi.created_time,
        wi.comprehensive,
        wi.language,
        wi.visibility,
        wi.created_by,
        u.user_code,
        u.user_name,
        u.org,
        wi.created_by,
        wi.owner_id,
        p.project_name,
        p.project_code,
        p.pm_name,
        p.pm_id
    FROM ai_dw_wiki_info wi
    LEFT JOIN ai_dw_user u ON wi.owner_id = u.id AND u.state = 1
    LEFT JOIN ai_dw_project p ON wi.id = p.wiki_id
    """
    
def _build_search_conditions(keyword: Optional[str], view_type: Optional[int]) -> Tuple[List[str], Dict[str, Any]]:
    """
    构建搜索条件

    Args:
        keyword (Optional[str]): 搜索关键词

    Returns:
        Tuple[List[str], Dict[str, Any]]: where条件列表和参数字典
    """
    where_conditions = [WIKI_QUERY_STATUS_CONDITION]
    if view_type:
        if view_type == VIEW_TYPE_NORMAL:
            where_conditions.append(VIEW_TYPE_NORMAL_CONDITION)
        else:
            where_conditions.append(VIEW_TYPE_PROJECT_CONDITION)
    else:
        where_conditions.append(VIEW_TYPE_NORMAL_CONDITION)
    params = {}

    if keyword:
        search_conditions = [
            "LOWER(u.user_code) LIKE LOWER(:keyword)",
            "LOWER(u.user_name) LIKE LOWER(:keyword)",
            "LOWER(wi.repo_name) LIKE LOWER(:keyword)",
            "LOWER(wi.repo_owner) LIKE LOWER(:keyword)"
        ]

        if view_type == VIEW_TYPE_NORMAL:
            search_conditions.append("LOWER(ext1.product_line_name) LIKE LOWER(:keyword)")
            search_conditions.append("LOWER(ext1.product_version_code) LIKE LOWER(:keyword)")
            search_conditions.append("LOWER(ext1.product_name) LIKE LOWER(:keyword)")
        elif view_type == VIEW_TYPE_PROJECT:
            search_conditions.append("LOWER(p.project_name) LIKE LOWER(:keyword)")
            search_conditions.append("LOWER(p.pm_name) LIKE LOWER(:keyword)")

        # 添加标签搜索条件
        search_conditions.append("""
            EXISTS (
                SELECT 1 FROM ai_dw_wiki_tag wt
                JOIN ai_dw_tag t ON wt.tag_id = t.id
                WHERE wt.wiki_id = wi.id AND t.state = 1 AND LOWER(t.name) LIKE LOWER(:keyword)
            )
        """)

        # 添加发布包和解决方案搜索条件
        search_conditions.append("""
            EXISTS (
                SELECT 1 FROM ai_dw_wiki_dc_ext_2 ext2
                WHERE ext2.wiki_id = wi.id
                AND (LOWER(ext2.release_pkg_code) LIKE LOWER(:keyword) OR LOWER(ext2.solution_name) LIKE LOWER(:keyword))
            )
        """)

        where_conditions.append(f"({' OR '.join(search_conditions)})")
        params['keyword'] = f"%{keyword.lower()}%"

    return where_conditions, params

def _query_total_count(
    session: Session,
    keyword: Optional[str],
    where_clause: str,
    params: Dict[str, Any],
    remove_user_id: bool = False
) -> int:
    """
    公共方法：查询wiki总数（未登录、超级管理员、普通用户均可用）

    Args:
        session (Session): 数据库会话
        keyword (Optional[str]): 搜索关键词
        where_clause (str): WHERE条件字符串（如 " WHERE ... "）
        params (Dict[str, Any]): 查询参数字典
        remove_user_id (bool): 是否去掉user_id参数（普通用户count查询需要）

    Returns:
        int: wiki总数
    """
    count_sql = f"""
    SELECT COUNT(DISTINCT wi.id)
    FROM ai_dw_wiki_info wi
    LEFT JOIN ai_dw_user u ON wi.created_by = u.id AND u.state = 1
    LEFT JOIN ai_dw_wiki_dc_ext_1 ext1 ON wi.id = ext1.wiki_id
    LEFT JOIN ai_dw_project p ON wi.id = p.wiki_id
    {where_clause}
    """

    count_params = params.copy()
    if remove_user_id and "user_id" in count_params:
        count_params.pop("user_id")
    return session.execute(text(count_sql), count_params).scalar()
