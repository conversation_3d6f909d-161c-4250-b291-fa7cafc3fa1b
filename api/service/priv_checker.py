from sqlalchemy import text
from sqlmodel import Session
from enum import Enum

class WikiAction(Enum):
    """Wiki操作类型枚举"""
    DELETE = "delete"      # 删除wiki
    REFRESH = "refresh"    # 刷新wiki
    READ = "read"          # 查询wiki详情
    UPDATE = "update"      # 编辑wiki
    EXPORT = "export"      # 导出wiki
    GENERATE = "generate"  # 生成wiki

from api.service.user_role_service import get_or_cache_user_role_ids

def check_is_super_admin(session: Session, user_id: int) -> bool:
    """检查用户是否是超级管理员（优先用 get_or_cache_user_role_ids）"""
    role_ids = get_or_cache_user_role_ids(user_id)
    if role_ids and 1 in role_ids:
        return True
    # 兜底查库（兼容老数据或异常）
    query = """
    SELECT 1
    FROM ai_dw_user_role ur
    WHERE ur.user_id = :user_id AND ur.role_id = 1
    """
    result = session.execute(text(query), {"user_id": user_id}).fetchone()
    return result is not None

def check_is_repo_owner(session: Session, user_id: int, wiki_id: str) -> bool:
    """
    检查用户是否为wiki所有者且是仓库管理员
    
    Args:
        session: 数据库会话
        user_id: 用户ID
        wiki_id: Wiki ID
        
    Returns:
        bool: 是否为wiki所有者且是仓库管理员
    """
    # 检查用户是否为wiki所有者
    owner_query = """
    SELECT 1
    FROM ai_dw_wiki_info wi
    WHERE wi.wiki_id = :wiki_id AND wi.owner_id = :user_id
    """
    owner_result = session.execute(text(owner_query), {"wiki_id": wiki_id, "user_id": user_id}).fetchone()
    
    if not owner_result:
        return False
    
    # 检查用户是否有全局仓库管理员权限
    return check_is_repo_admin_global(session, user_id)

def check_is_repo_admin(session: Session, user_id: int, wiki_id: str) -> bool:
    """
    检查用户是否为仓库管理员
    
    权限要求：必须同时满足以下两个条件
    1. 检查用户是否有查看特定wiki的权限
    2. 用户具有仓库管理员权限（角色2）
    
    Args:
        session: 数据库会话
        user_id: 用户ID
        wiki_id: Wiki ID
        
    Returns:
        bool: 是否为仓库管理员（同时满足wiki级别和全局级别权限）
    """
    # 检查用户是否有查看特定wiki的权限
    role_query = """
    SELECT 1
    FROM ai_dw_wiki_info wi
    JOIN ai_dw_wiki_user_role wur ON wi.id = wur.wiki_id
    WHERE wi.wiki_id = :wiki_id AND wur.user_id = :user_id AND wur.role_id = 5
    """
    result = session.execute(text(role_query), {"wiki_id": wiki_id, "user_id": user_id}).fetchone()
    if not result:
        return False
    
    # 检查用户是否有全局仓库管理员权限
    return check_is_repo_admin_global(session, user_id)

def check_is_repo_admin_global(session: Session, user_id: int) -> bool:
    """
    检查用户是否有全局的仓库管理员权限（优先用 get_or_cache_user_role_ids）
    Args:
    session: 数据库会话
    user_id: 用户ID
        
    Returns:
        bool: 是否有全局的仓库管理员权限
    """
    role_ids = get_or_cache_user_role_ids(user_id)
    if role_ids and 2 in role_ids:
        return True
    # 兜底查库
    query = """
    SELECT 1
    FROM ai_dw_user_role ur
    WHERE ur.user_id = :user_id AND ur.role_id = 2
    """
    result = session.execute(text(query), {"user_id": user_id}).fetchone()
    return result is not None

def check_is_wiki_global_visibility(session: Session, wiki_id: str) -> bool:
    """
    检查wiki是否全局可见
    
    Args:
        session: 数据库会话
        wiki_id: Wiki ID
        
    Returns:
        bool: 是否支持导出
    """
    query = """
    SELECT 1
    FROM ai_dw_wiki_info wi
    WHERE wi.wiki_id = :wiki_id AND wi.visibility = 1
    """
    result = session.execute(text(query), {"wiki_id": wiki_id}).fetchone()
    return result is not None

def check_wiki_permission(session: Session, user_id: int, wiki_id: str, action: WikiAction) -> bool:
    """
    通用的Wiki权限检查方法
    
    Args:
        session: 数据库会话
        user_id: 当前用户ID
        wiki_id: Wiki ID（生成操作时可以为None）
        action: 要执行的操作类型
        
    Returns:
        bool: 是否有权限执行该操作
    """
    # 超级管理员拥有所有权限
    if check_is_super_admin(session, user_id):
        return True

    # 读取权限单独处理
    if action == WikiAction.READ:
        return _check_can_read_wiki(session, user_id, wiki_id)
    
    # 根据操作类型判断权限
    if action in [WikiAction.DELETE, WikiAction.REFRESH, WikiAction.UPDATE]:
        # 删除、刷新、编辑：wiki所有者且是仓库管理员
        return check_is_repo_owner(session, user_id, wiki_id)
    
    elif action == WikiAction.EXPORT:
        # 导出：仓库管理员
        return check_is_repo_admin(session, user_id, wiki_id)
    
    elif action == WikiAction.GENERATE:
        # 生成：全局仓库管理员权限
        return check_is_repo_admin_global(session, user_id)
    
    return False

def _check_can_read_wiki(session: Session, user_id: int, wiki_id: str) -> bool:
    """
    检查用户是否有读取wiki的权限
    1. 超级管理员可读（已在 check_wiki_permission 处理）
    2. wiki 全局可见时可读
    3. 用户有该wiki授权（ai_dw_wiki_user_role表有记录）
    """
    # wiki 全局可见
    if check_is_wiki_global_visibility(session, wiki_id):
        return True
    # 用户有该wiki授权
    query = """
    SELECT 1
    FROM ai_dw_wiki_info wi
    JOIN ai_dw_wiki_user_role wur ON wi.id = wur.wiki_id
    WHERE wi.wiki_id = :wiki_id AND wur.user_id = :user_id
    """
    result = session.execute(text(query), {"wiki_id": wiki_id, "user_id": user_id}).fetchone()
    return result is not None

# 便捷的权限检查函数
def can_delete_wiki(session: Session, user_id: int, wiki_id: str) -> bool:
    """检查用户是否可以删除wiki"""
    return check_wiki_permission(session, user_id, wiki_id, WikiAction.DELETE)

def can_refresh_wiki(session: Session, user_id: int, wiki_id: str) -> bool:
    """检查用户是否可以刷新wiki"""
    return check_wiki_permission(session, user_id, wiki_id, WikiAction.REFRESH)

def can_update_wiki(session: Session, user_id: int, wiki_id: str) -> bool:
    """检查用户是否可以编辑wiki"""
    return check_wiki_permission(session, user_id, wiki_id, WikiAction.UPDATE)

def can_export_wiki(session: Session, user_id: int, wiki_id: str) -> bool:
    """检查用户是否可以导出wiki"""
    return check_wiki_permission(session, user_id, wiki_id, WikiAction.EXPORT)

def can_generate_wiki(session: Session, user_id: int) -> bool:
    """检查用户是否可以生成wiki"""
    # 对于生成操作，wiki_id为None
    return check_wiki_permission(session, user_id, None, WikiAction.GENERATE)

def can_read_wiki(session: Session, user_id: int, wiki_id: str) -> bool:
    """检查用户是否可以读取wiki"""
    return check_wiki_permission(session, user_id, wiki_id, WikiAction.READ)

def can_upload_project_docs(session: Session, user_id: int, wiki_id: str) -> bool:
    """
    校验是否允许在项目级文档目录（i-doc/o-doc）下上传/创建：
    满足以下任一条件即可：
    1) 超级管理员
    2) 当前 wiki 的拥有者（owner）
    3) 当前 wiki 的管理员（具有仓库管理员权限且对该 wiki 有授权）
    """
    # 超管
    if check_is_super_admin(session, user_id):
        return True
    # wiki 拥有者
    owner_query = """
    SELECT 1 FROM ai_dw_wiki_info wi WHERE wi.wiki_id = :wiki_id AND wi.owner_id = :user_id
    """
    owner_result = session.execute(text(owner_query), {"wiki_id": wiki_id, "user_id": user_id}).fetchone()
    if owner_result:
        return True
    # 仓库管理员（需具有管理员角色且对该 wiki 有授权）
    return check_is_repo_admin(session, user_id, wiki_id)
