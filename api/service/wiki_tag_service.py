from datetime import datetime
import logging
from typing import Optional, List

from sqlmodel import Session, select
from sqlalchemy import delete

from api.model.wiki_tag import WikiTag
from api.model.tag import Tag

logger = logging.getLogger(__name__)

def get_wiki_tag_by_wiki_id_and_tag_id(session: Session, wiki_id: int, tag_id: int) -> Optional[WikiTag]:
    """
    通过wiki_id和tag_id查询wiki与标签的关联关系
    """
    return session.exec(select(WikiTag).where(WikiTag.wiki_id == wiki_id, WikiTag.tag_id == tag_id)).first()

def get_wiki_tags_with_tag_info(session: Session, wiki_id: int) -> List[dict]:
    """
    通过wiki_id查询wiki的所有标签信息，包含tag表的详细信息
    """
    # 使用join查询关联WikiTag和Tag表
    query = select(WikiTag, Tag).join(Tag, WikiTag.tag_id == Tag.id).where(WikiTag.wiki_id == wiki_id).order_by(WikiTag.seq)
    results = session.exec(query).all()
    
    # 将结果转换为字典格式，包含tag信息
    tag_list = []
    for wiki_tag, tag in results:
        tag_info = {
            "id": tag.id,
            "wiki_id": wiki_tag.wiki_id,
            "tag_id": wiki_tag.tag_id,
            "name": tag.name,
            "type": tag.type,
            "color": tag.color,
            "comments": tag.comments,
            "seq": wiki_tag.seq
        }
        tag_list.append(tag_info)
    
    return tag_list

def get_wiki_tag_with_tag_info(session: Session, wiki_tag_id: int) -> Optional[dict]:
    """
    通过wiki_tag_id查询单个wiki标签的详细信息，包含tag表的详细信息
    """
    # 使用join查询关联WikiTag和Tag表
    query = select(WikiTag, Tag).join(Tag, WikiTag.tag_id == Tag.id).where(WikiTag.id == wiki_tag_id)
    result = session.exec(query).first()
    
    if result:
        wiki_tag, tag = result
        tag_info = {
            "wiki_tag_id": wiki_tag.id,
            "wiki_id": wiki_tag.wiki_id,
            "tag_id": wiki_tag.tag_id,
            "tag_name": tag.name,
            "tag_type": tag.type,
            "tag_color": tag.color,
            "tag_comments": tag.comments,
            "tag_module_type": tag.module_type,
            "tag_state": tag.state,
            "created_by": wiki_tag.created_by,
            "created_date": wiki_tag.created_date,
            "update_by": wiki_tag.update_by,
            "update_date": wiki_tag.update_date
        }
        return tag_info
    
    return None

def add_wiki_tag(session: Session, wiki_id: int, tag_ids: List[int], created_by: int) -> bool:
    """
    添加wiki与标签的关联关系
    """
    wiki_tags = get_wiki_tags_with_tag_info(session, wiki_id)
    seq = 100
    for tag in wiki_tags:
        if tag.get('seq') >= seq:
            seq = tag.get('seq')
    seq += 1
    for tag_id in tag_ids:
        if tag_id == 1:
            wiki_tag = WikiTag(wiki_id=wiki_id, tag_id=tag_id, seq=1, created_by=created_by, created_date=datetime.now())
        else:
            wiki_tag = WikiTag(wiki_id=wiki_id, tag_id=tag_id, seq=seq, created_by=created_by, created_date=datetime.now())
            seq += 1
        session.add(wiki_tag)
    session.commit()
    return True

def delete_wiki_tag(session: Session, id: int) -> bool:
    """
    删除wiki与标签的关联关系
    """
    wiki_tag = session.get(WikiTag, id)
    session.delete(wiki_tag)
    session.commit()
    return True

def delete_wiki_tag_by_wiki_id_and_tag_id(session: Session, wiki_id: int, tag_id: int) -> bool:
    """
    删除wiki与标签的关联关系
    """
    session.execute(delete(WikiTag).where(WikiTag.wiki_id == wiki_id, WikiTag.tag_id == tag_id))
    session.commit()
    return True

def delete_wiki_tag_by_wiki_id(session: Session, wiki_id: int) -> bool:
    """
    删除wiki与标签的关联关系
    """
    session.execute(delete(WikiTag).where(WikiTag.wiki_id == wiki_id))
    session.commit()
    return True

def batch_delete_wiki_tag_by_tag_id(session: Session, tag_id: int) -> bool:
    """
    批量删除wiki与标签的关联关系
    """
    wiki_tags = session.exec(select(WikiTag).where(WikiTag.tag_id == tag_id)).all()
    for wiki_tag in wiki_tags:
        session.delete(wiki_tag)
    session.commit()
    return True
