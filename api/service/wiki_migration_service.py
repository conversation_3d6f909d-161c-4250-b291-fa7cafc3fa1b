import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List

from sqlmodel import Session, select

from api.database.base import session_scope
from api.model.wiki_info_bak import WikiInfoBak
from api.model.wiki_info import WikiInfo
from api.model.git_repository import AiDwGitRepository
from api.model.wiki_repository_relation import AiDwWikiRepositoryRelation
from api.model.wiki_content import AiDwWikiContent


logger = logging.getLogger(__name__)


def _ensure_ai_dw_tables(session: Session):
    """确保 ai_dw_* 三张表已创建（避免 1146 表不存在）。"""
    try:
        # 触发导入，确保元数据已注册
        _ = (WikiInfo, AiDwGitRepository, AiDwWikiRepositoryRelation, AiDwWikiContent)
        from sqlmodel import SQLModel
        bind = session.get_bind()
        # 仅创建缺失表，不会覆盖已有结构
        SQLModel.metadata.create_all(bind=bind, tables=[
            WikiInfo.__table__,
            AiDwGitRepository.__table__,
            AiDwWikiRepositoryRelation.__table__,
            AiDwWikiContent.__table__,
        ])
        logger.info("已检查/创建 ai_dw_* 新表")
    except Exception as e:
        logger.warning(f"创建 ai_dw_* 表时出现问题（可能已存在）：{e}")


def _parse_sub_repos(sub_repos_str: Optional[str]) -> List[Dict[str, Any]]:
    """解析 WikiInfoBak.sub_repos: JSON 字符串 -> [{url, branch}, ...]"""
    if not sub_repos_str:
        return []
    try:
        data = json.loads(sub_repos_str)
        if isinstance(data, list):
            # 只保留有 url 的项
            return [
                {"url": (item.get("url") or item.get("repo_url") or "").strip(),
                 "branch": (item.get("branch") or item.get("repo_branch") or "master").strip()}
                for item in data if isinstance(item, dict) and (item.get("url") or item.get("repo_url"))
            ]
        return []
    except Exception as e:
        logger.warning(f"解析 sub_repos 失败: {e}")
        return []


def _get_or_create_ai_dw_wiki_info(session: Session, old: WikiInfoBak) -> WikiInfo:
    existing = session.exec(select(WikiInfo).where(WikiInfo.wiki_id == old.wiki_id)).first()
    if existing:
        # 更新冗余字段（如果为空的话）
        updated = False
        if not existing.repo_url and old.repo_url:
            existing.repo_url = old.repo_url; updated = True
        if not existing.repo_owner and old.repo_owner:
            existing.repo_owner = old.repo_owner; updated = True
        if not existing.repo_name and old.repo_name:
            existing.repo_name = old.repo_name; updated = True
        if not existing.branch and old.branch:
            existing.branch = old.branch; updated = True
        if not existing.repo_type and old.repo_type:
            existing.repo_type = old.repo_type; updated = True
        if updated:
            existing.updated_time = datetime.utcnow()
        return existing

    name = old.repo_name or (old.comments or "").splitlines()[0] or old.wiki_id
    new_item = WikiInfo(
        id=old.id,
        wiki_id=old.wiki_id,
        name=name,
        description=old.comments,
        wiki_type=old.type or 1,
        project_topic_id=old.topic_id,
        provider=old.provider,
        model=old.model,
        language=old.language,
        comprehensive=bool(old.comprehensive),
        excluded_dirs=old.excluded_dirs,
        excluded_files=old.excluded_files,
        included_dirs=old.included_dirs,
        included_files=old.included_files,
        visibility=old.visibility or 2,
        status=old.status or "active",
        # 冗余主仓库字段
        repo_url=old.repo_url,
        repo_owner=old.repo_owner,
        repo_name=old.repo_name,
        branch=old.branch or "master",
        repo_type=old.repo_type,
        # 时间和用户信息
        created_time=old.created_time or datetime.utcnow(),
        updated_time=old.updated_time or datetime.utcnow(),
        created_by=old.created_by or 0,
        updated_by=old.updated_by or 0,
        owner_id=old.owner_id or 0,
    )
    session.add(new_item)
    session.flush()
    return new_item


def _get_or_create_git_repo(session: Session, repo_url: str, branch: str, owner: str, name: str,
                             repo_type: str, description: Optional[str], code_topic_id: Optional[str]) -> AiDwGitRepository:
    existing = session.exec(
        select(AiDwGitRepository).where(
            AiDwGitRepository.repo_url == repo_url,
            AiDwGitRepository.branch == branch,
        )
    ).first()
    if existing:
        # 回填缺失字段
        changed = False
        if not existing.repo_owner and owner:
            existing.repo_owner = owner; changed = True
        if not existing.repo_name and name:
            existing.repo_name = name; changed = True
        if not existing.repo_type and repo_type:
            existing.repo_type = repo_type; changed = True
        if not existing.description and description:
            existing.description = description; changed = True
        if not existing.code_topic_id and code_topic_id:
            existing.code_topic_id = code_topic_id; changed = True
        if changed:
            existing.updated_time = datetime.utcnow()
        return existing

    new_repo = AiDwGitRepository(
        repo_url=repo_url,
        branch=branch or "master",
        repo_owner=owner,
        repo_name=name,
        repo_type=repo_type or "whaleDevCloud",
        description=description,
        code_topic_id=code_topic_id,
        status="active",
    )
    session.add(new_repo)
    session.flush()
    return new_repo


def _ensure_relation(session: Session, wiki_id: str, repo_id: int, is_main: bool,
                     overrides: Optional[Dict[str, Optional[str]]] = None) -> AiDwWikiRepositoryRelation:
    existing = session.exec(
        select(AiDwWikiRepositoryRelation).where(
            AiDwWikiRepositoryRelation.wiki_id == wiki_id,
            AiDwWikiRepositoryRelation.repository_id == repo_id,
        )
    ).first()
    if existing:
        changed = False
        if existing.is_main_repo != is_main:
            existing.is_main_repo = is_main; changed = True
        if overrides:
            for k in ["excluded_dirs", "excluded_files", "included_dirs", "included_files"]:
                val = overrides.get(k) if overrides else None
                if val is not None and getattr(existing, k) != val:
                    setattr(existing, k, val)
                    changed = True
        if changed:
            existing.updated_time = datetime.utcnow()
        return existing

    item = AiDwWikiRepositoryRelation(
        wiki_id=wiki_id,
        repository_id=repo_id,
        is_main_repo=is_main,
        excluded_dirs=(overrides or {}).get("excluded_dirs"),
        excluded_files=(overrides or {}).get("excluded_files"),
        included_dirs=(overrides or {}).get("included_dirs"),
        included_files=(overrides or {}).get("included_files"),
    )
    session.add(item)
    session.flush()
    return item


def _upsert_content(session: Session, wiki_id: str, wiki_data: Optional[Dict[str, Any]]):
    if not wiki_data:
        return
    structure = wiki_data.get("wiki_structure")
    pages = wiki_data.get("generated_pages")
    existing = session.exec(select(AiDwWikiContent).where(AiDwWikiContent.wiki_id == wiki_id)).first()
    if existing:
        changed = False
        if structure is not None:
            existing.wiki_structure = structure; changed = True
        if pages is not None:
            existing.wiki_pages = pages; existing.total_pages = len(pages or {}); changed = True
        if changed:
            existing.updated_time = datetime.utcnow()
        return
    item = AiDwWikiContent(
        wiki_id=wiki_id,
        wiki_structure=structure,
        wiki_pages=pages,
        total_pages=len(pages or {}),
        version=1,
        generation_info=None,
    )
    session.add(item)


def migrate_one_wiki(session: Session, old_wiki: WikiInfoBak) -> Dict[str, Any]:
    """迁移单条 WikiInfoBak 到 ai_dw_* 表。"""
    result = {"wiki_id": old_wiki.wiki_id, "created": False, "updated": False}

    # 1) WikiInfo
    ai_wiki = _get_or_create_ai_dw_wiki_info(session, old_wiki)

    # 2) 主仓库 -> AiDwGitRepository + 关联
    main_repo = _get_or_create_git_repo(
        session,
        repo_url=old_wiki.repo_url,
        branch=old_wiki.branch or "master",
        owner=old_wiki.repo_owner,
        name=old_wiki.repo_name,
        repo_type=old_wiki.repo_type,
        description=old_wiki.comments,
        code_topic_id=old_wiki.topic_id_code,
    )
    _ensure_relation(
        session,
        wiki_id=old_wiki.wiki_id,
        repo_id=main_repo.id,
        is_main=True,
        overrides={
            "excluded_dirs": old_wiki.excluded_dirs,
            "excluded_files": old_wiki.excluded_files,
            "included_dirs": old_wiki.included_dirs,
            "included_files": old_wiki.included_files,
        }
    )

    # 3) 子仓库
    sub_repos = _parse_sub_repos(old_wiki.sub_repos)
    for idx, sub in enumerate(sub_repos, start=1):
        url = sub.get("url"); branch = sub.get("branch") or "master"
        if not url:
            continue
        # 尝试从URL中提取 owner/name（保底使用原主仓库 owner/name 前缀）
        try:
            from api.utils import git_utils
            sub_owner, sub_name, _ = git_utils.extract_repo_info(url)
        except Exception:
            sub_owner, sub_name = (old_wiki.repo_owner, f"sub-{idx}")

        sub_repo = _get_or_create_git_repo(
            session,
            repo_url=url,
            branch=branch,
            owner=sub_owner,
            name=sub_name,
            repo_type=old_wiki.repo_type,
            description=None,
            code_topic_id=None,
        )
        _ensure_relation(session, old_wiki.wiki_id, sub_repo.id, is_main=False)

    # 4) 内容表（从旧 wiki_data 迁移）
    _upsert_content(session, old_wiki.wiki_id, old_wiki.wiki_data or {})

    result["updated"] = True
    return result


def migrate_all_wikis(batch_size: int = 200) -> Dict[str, Any]:
    """全量迁移入口。分批读取 wiki_info，迁移到 ai_dw_* 三张表。"""
    stats = {"scanned": 0, "migrated": 0, "errors": 0}
    with session_scope() as session:
        # 确保新表存在
        # _ensure_ai_dw_tables(session)
        offset = 0
        while True:
            rows = session.exec(select(WikiInfoBak).order_by(WikiInfoBak.id).offset(offset).limit(batch_size)).all()
            if not rows:
                break
            for old in rows:
                stats["scanned"] += 1
                try:
                    migrate_one_wiki(session, old)
                    stats["migrated"] += 1
                except Exception as e:
                    logger.error(f"迁移 wiki[{old.wiki_id}] 失败: {e}", exc_info=True)
                    stats["errors"] += 1
            session.commit()
            offset += batch_size
    logger.info(f"迁移完成：{stats}")
    return stats


def migrate_single_wiki(wiki_id: str) -> Dict[str, Any]:
    """按 wiki_id 迁移单条。"""
    with session_scope() as session:
        # 确保新表存在
        # _ensure_ai_dw_tables(session)
        old = session.exec(select(WikiInfoBak).where(WikiInfoBak.wiki_id == wiki_id)).first()
        if not old:
            raise ValueError(f"wiki_id={wiki_id} 不存在")
        result = migrate_one_wiki(session, old)
        session.commit()
        logger.info(f"迁移单条完成：{result}")
        return result


