import logging
from typing import List
from sqlmodel import select

from api.cache.local.priv_cache import get_privs_by_type
from api.common.constants import PrivType
from api.database.base import session_scope
from api.model.user_role import UserRole, RolePriv
from api.model.priv import Priv, PrivState

logger = logging.getLogger(__name__)

from api.service.user_role_service import get_or_cache_user_role_ids

def get_user_privileges_by_type(user_id: int, priv_type: PrivType) -> dict:
    """
    查询用户所有角色下指定类型的权限（如 priv_type=COMPONENT 或 SERVICE，state=1），
    返回 {"role_ids": [...], "privileges": [...]}，权限去重且不分组。
    """
    with session_scope() as session:
        ur = UserRole
        rp = RolePriv
        p = Priv
        # 单SQL查出所有角色id和权限
        stmt = (
            select(ur.role_id, p)
            .select_from(ur)
            .outerjoin(rp, ur.role_id == rp.role_id)
            .outerjoin(p, (rp.priv_id == p.id) & (p.state == PrivState.VALID.value) & (p.priv_type == priv_type.value))
            .where(ur.user_id == user_id)
        )
        rows = session.exec(stmt).all()
        role_id_set = set()
        priv_seen = set()
        priv_list = []
        for role_id, priv in rows:
            role_id_set.add(role_id)
            if priv is not None and priv.id not in priv_seen:
                priv_list.append(priv.model_dump(exclude={"created_by", "update_by", "created_date", "update_date"}))
                priv_seen.add(priv.id)
        role_ids = list(role_id_set)
        logger.debug(f"User {user_id} role_ids: {role_ids}, privileges of type {priv_type}: {priv_list}")
        return {"role_ids": role_ids, "privileges": priv_list}

def get_user_privileges_by_type_with_cache(user_id: int, priv_type: PrivType, role_ids: list = None) -> dict:
    """
    优先从redis和本地缓存获取用户角色及权限，查不到则降级到数据库。
    如果传入role_ids，则直接用该列表查权限。
    """
    try:
        success = True
        if role_ids is not None:
            user_role_ids = role_ids
        else:
            user_role_ids = get_or_cache_user_role_ids(user_id)
        if user_role_ids:
            privileges = []
            priv_seen = set()
            for rid in user_role_ids:
                for priv in get_privs_by_type(rid, priv_type=priv_type):
                    pid = priv.get("id")
                    if pid and pid not in priv_seen:
                        privileges.append(priv)
                        priv_seen.add(pid)
            if privileges:
                return {
                    "role_ids": user_role_ids,
                    "privileges": privileges
                }
    except Exception as e:
        success = False
        logger.warning(f"本地缓存/redis获取用户角色权限失败，降级到原有逻辑: {e}")
    if success:
        return {}
    # 查不到则降级到原有数据库逻辑
    if priv_type.value == PrivType.SERVICE.value:
        return get_user_privileges_by_type(user_id, priv_type)
    else:
        return get_user_component_privileges(user_id=user_id)

def get_user_component_privileges(user_id: int) -> dict:
    """
    查询用户所有角色下的组件权限（priv_type=COMPONENT，state=1），
    表之间采用内连接 join 方式，返回权限列表（去重，不含角色信息）。
    """
    with session_scope() as session:
        ur = UserRole
        rp = RolePriv
        p = Priv
        # 使用内连接 join，priv_type 固定为 COMPONENT
        stmt = (
            select(p)
            .select_from(ur)
            .join(rp, ur.role_id == rp.role_id)
            .join(
                p,
                (rp.priv_id == p.id)
                & (p.state == PrivState.VALID.value)
                & (p.priv_type == PrivType.COMPONENT.value)
            )
            .where(ur.user_id == user_id)
        )
        rows = session.exec(stmt).all()
        priv_seen = set()
        priv_list = []
        for priv in rows:
            if priv is not None and priv.id not in priv_seen:
                priv_list.append({"id": priv.id, "priv_code": priv.priv_code, "priv_name": priv.priv_name})
                priv_seen.add(priv.id)
        logger.debug(f"User {user_id} component privileges: {priv_list}")
        return {
            "role_ids": [],
            "privileges": priv_list
        }

def get_all_valid_component_privileges() -> List[dict]:
    """
    查询全量有效的组件权限（priv_type=COMPONENT，state=1），返回权限列表。
    仅查 Priv 表，不关联用户或角色。
    """
    with session_scope() as session:
        p = Priv
        stmt = (
            select(p)
            .where(
                (p.state == PrivState.VALID.value) &
                (p.priv_type == PrivType.COMPONENT.value)
            )
        )
        rows = session.exec(stmt).all()
        priv_list = []
        for priv in rows:
            priv_list.append({"id": priv.id, "priv_code": priv.priv_code, "priv_name": priv.priv_name})
        logger.debug(f"All valid component privileges: {priv_list}")
        return priv_list