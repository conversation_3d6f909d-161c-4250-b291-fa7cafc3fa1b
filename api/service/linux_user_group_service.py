"""
Linux用户组管理服务
基于wiki信息动态管理Linux用户组，不使用数据库存储
实现wiki级别的文件系统权限隔离
"""

import logging
import subprocess
import os
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import threading
import time
from concurrent.futures import ThreadPoolExecutor

from api.sandbox.kubernetes_service import get_kubernetes_service
from api.service.k8s_job_service import get_k8s_job_service

logger = logging.getLogger(__name__)


# 全局服务实例
_linux_user_group_service = None


class LinuxUserGroupService:
    """Linux用户组管理服务"""
    
    def __init__(self):
        self.lock = threading.Lock()
    
    def _user_exists(self, username: str) -> bool:
        """
        检查用户是否存在
        
        Args:
            username: 用户名
            
        Returns:
            用户是否存在
        """
        try:
            cmd = ["id", username]
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
        except Exception as e:
            logger.error(f"检查用户存在性时发生错误: {str(e)}")
            return False
    
    def create_deepwiki_group(self, gid: int, group_name: str) -> Tuple[bool, str]:
        """
        为wiki创建Linux用户组
        
        Args:
            code_path: 代码目录路径
            gid: 组ID
            group_name: 用户组名称
        Returns:
            (是否成功, 错误信息或组名)
        """
        try:
            # 检查组是否已存在
            cmd_check = ["getent", "group", group_name]
            result_check = subprocess.run(cmd_check, capture_output=True, text=True)
            
            if result_check.returncode == 0:
                logger.info(f"用户组 {group_name} 已存在")
                return True, group_name
            
            # 创建用户组
            cmd = ["groupadd", "-g", str(gid), group_name]
            logger.info(f"执行命令: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            if result.returncode == 0:
                logger.info(f"成功创建用户组: {group_name}")
                return True, group_name
            else:
                error_msg = f"创建用户组失败: {result.stderr}"
                logger.error(error_msg)
                return False, error_msg
                
        except subprocess.CalledProcessError as e:
            error_msg = f"创建用户组时发生错误: {e.stderr}"
            logger.error(error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"创建用户组时发生未知错误: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def set_directory_permissions(self, code_path: str, group_name: str) -> bool:
        """
        设置目录权限,只有指定用户组能访问
        
        Args:
            code_path: 代码目录路径
            group_name: 用户组名称
            
        Returns:
            是否设置成功
        """
        try:
            # 确保目录存在
            Path(code_path).mkdir(parents=True, exist_ok=True)
            
            # 设置目录所属组
            cmd_chgrp = ["chgrp", group_name, code_path]
            logger.info(f"执行命令: {' '.join(cmd_chgrp)}")
            start_time = time.time()
            result = subprocess.run(cmd_chgrp, check=True, capture_output=True, text=True)
            chgrp_duration = time.time() - start_time
            logger.info(f"chgrp命令执行耗时: {chgrp_duration:.3f}秒")
            
            # 设置所有目录的setgid位，确保新文件继承组权限
            cmd_setgid = ["find", code_path, "-type", "d", "-exec", "chmod", "g+s", "{}", "+"]
            logger.info(f"执行命令: {' '.join(cmd_setgid)}")
            start_time = time.time()
            result = subprocess.run(cmd_setgid, check=True, capture_output=True, text=True)
            setgid_duration = time.time() - start_time
            logger.info(f"find+chmod命令执行耗时: {setgid_duration:.3f}秒")
            
            total_duration = chgrp_duration + setgid_duration
            logger.info(f"成功设置目录权限: {code_path} -> {group_name} (总耗时: {total_duration:.3f}秒)")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"设置目录权限失败: {e}")
            return False
        except Exception as e:
            logger.error(f"设置目录权限时发生错误: {str(e)}")
            return False

    def change_directory_permissions(self, code_path: str, mod: str, if_recursive: bool = False) -> bool:
        """
        修改目录权限
        
        Args:
            code_path: 代码目录路径
        """
        try:
            # 修改目录权限
            if if_recursive:
                cmd = ["chmod", "-R", mod, code_path]
            else:
                cmd = ["chmod", mod, code_path]
            logger.info(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
        except Exception as e:
            logger.error(f"修改目录权限时发生错误: {str(e)}")
            return False
    
    def remove_user_from_group(self, group_name: str, username: str) -> Tuple[bool, str]:
        """
        从wiki用户组中移除用户
        
        Args:
            group_name: 用户组名称
            username: 用户名
            
        Returns:
            (是否成功, 错误信息)
        """
        try:
            # 检查用户是否存在
            if not self._user_exists(username):
                logger.warning(f"用户 {username} 不存在，跳过移除操作")
                return True, ""
            
            # 从组中移除用户
            cmd = ["gpasswd", "-d", username, group_name]
            logger.info(f"执行命令: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            # gpasswd -d 在用户不在组中时会返回非0，但这不算错误
            if result.returncode == 0 or "is not a member of" in result.stderr:
                logger.info(f"用户 {username} 已从组 {group_name} 中移除")
                return True, ""
            else:
                error_msg = f"从组中移除用户失败: {result.stderr}"
                logger.error(error_msg)
                return False, error_msg
                
        except Exception as e:
            error_msg = f"从组中移除用户时发生错误: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def get_group_members(self, group_name: str) -> List[str]:
        """
        获取wiki用户组的所有成员
        
        Args:
            group_name: 用户组名称
            
        Returns:
            用户名列表
        """
        try:
            # 使用 getent 命令获取组成员信息
            cmd = ["getent", "group", group_name]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.warning(f"用户组不存在: {group_name}")
                return []
            
            # 解析输出: groupname:x:gid:member1,member2,member3
            line = result.stdout.strip()
            if not line:
                return []
            
            parts = line.split(':')
            if len(parts) < 4:
                return []
            
            members_str = parts[3]
            if not members_str:
                return []
            
            return [member.strip() for member in members_str.split(',') if member.strip()]
            
        except Exception as e:
            logger.error(f"获取用户组成员失败: {str(e)}")
            return []
    
    def fix_existing_directory_permissions(self, group_name: str, code_path: str) -> Tuple[bool, str]:
        """
        修复已存在目录的权限
        用于对已存在的代码目录应用用户组权限
        
        Args:
            group_name: 用户组名称
            code_path: 代码目录路径
            
        Returns:
            (是否成功, 错误信息或成功消息)
        """
        try:
            # 检查用户组是否存在，不存在则创建
            cmd_check = ["getent", "group", group_name]
            result_check = subprocess.run(cmd_check, capture_output=True, text=True)
            
            if result_check.returncode != 0:
                # 创建用户组
                cmd = ["groupadd", group_name]
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                if result.returncode != 0:
                    return False, f"创建用户组失败: {result.stderr}"
                logger.info(f"成功创建用户组: {group_name}")
            else:
                logger.info(f"用户组 {group_name} 已存在")
            
            # 设置目录权限
            success = self.set_directory_permissions(code_path, group_name)
            if success:
                return True, f"成功修复目录权限: {code_path} -> {group_name}"
            else:
                return False, "设置目录权限失败"
                
        except Exception as e:
            error_msg = f"修复目录权限时发生错误: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def set_facl_permissions(self, code_path: str) -> bool:
        """
        设置目录的acl权限
        
        Args:
            code_path: 代码目录路径
        """
        from api.config import get_acl_version
        try:
            # 设置目录的acl权限
            if get_acl_version() == "nfs4-acl-tools":
                # 使用 -s 参数一次性设置完整的 ACL
                cmd = [
                    "nfs4_setfacl",
                    "-s", "A:fd:OWNER@:rwx,A:fd:GROUP@:rwx,A:fd:EVERYONE@:rwx",
                    code_path
                ]
            else:
                cmd = [
                    "setfacl",
                    "-R", "-d", "-m", f"u::rwx,g::rwx,o::rwx", code_path
                ]
            logger.info(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return result
        except Exception as e:
            logger.error(f"设置目录acl权限时发生错误: {str(e)}")
            return False    

    def exec_command_in_pod(self, pod_name: str, command: str) -> Tuple[bool, str]:
        """
        在pod中执行命令
        
        Args:
            pod_name: pod名称
            command: 命令
            
        Returns:
            (是否成功, 错误信息)
        """
        try:
            result = get_kubernetes_service().exec_command_in_pod(pod_name=pod_name, command=command)
            return result.get("success"), result.get("msg")
        except Exception as e:
            return False, f"创建用户时发生错误: {str(e)}"

    def create_group_in_pod(self, pod_name: str, gid: int, group_name: str) -> Tuple[bool, str]:
        """
        在pod中创建用户组
        
        Args:
            pod_name: pod名称
            gid: 组ID
            group_name: 用户组名称
            
        Returns:
            (是否成功, 错误信息)
        """
        try:
            # 组合命令：检查组是否存在，且 GID 是否匹配
            # 1. 从 /etc/group 文件中查询组信息（更可靠，支持纯数字组名）
            # 2. 如果组不存在（变量为空），直接创建
            # 3. 如果组存在但 GID 不匹配，先删除旧组再创建新组
            # 4. 如果组存在且 GID 匹配，不做任何操作
            cmd = f"""
                GROUP_INFO=$(grep -E "^{group_name}:" /etc/group 2>/dev/null)
                if [ -z "$GROUP_INFO" ]; then
                    groupadd -g {gid} '{group_name}'
                else
                    EXISTING_GID=$(echo "$GROUP_INFO" | cut -d: -f3)
                    if [ "$EXISTING_GID" != "{gid}" ]; then
                        groupdel '{group_name}'
                        groupadd -g {gid} '{group_name}'
                    fi
                fi
                """.strip()
            
            result = get_kubernetes_service().exec_command_in_pod(
                pod_name=pod_name, 
                command=cmd
            )
            
            if result.get("success"):
                logger.info(f"用户组 {group_name} (GID={gid}) 在 pod {pod_name} 中已确保存在")
            
            return result.get("success"), result.get("msg")
            
        except Exception as e:
            return False, f"创建用户组时发生错误: {str(e)}"


    def add_user_to_group_in_pod(self, pod_name: str, group_name: str) -> Tuple[bool, str]:
        """
        在pod中将用户添加到用户组
        
        Args:
            pod_name: pod名称
            group_name: 用户组名称
            username: 用户名
        """
        cmd = f"usermod -a -G {group_name} wct"
        try:
            result = get_kubernetes_service().exec_command_in_pod(pod_name=pod_name, command=cmd)
            return result.get("success"), result.get("msg")
        except Exception as e:
            return False, f"将用户添加到用户组时发生错误: {str(e)}"

    def remove_user_from_group_in_pod(self, pod_name: str, group_name: str) -> Tuple[bool, str]:
        """
        在pod中将用户从用户组中移除
        
        Args:
            pod_name: pod名称
            group_name: 用户组名称
        """
        cmd = f"gpasswd -d wct {group_name}"
        try:
            result = get_kubernetes_service().exec_command_in_pod(pod_name=pod_name, command=cmd)
            return result.get("success"), result.get("msg")
        except Exception as e:
            return False, f"将用户从用户组中移除时发生错误: {str(e)}"

def get_linux_user_group_service() -> LinuxUserGroupService:
    """获取Linux用户组管理服务实例"""
    global _linux_user_group_service
    if _linux_user_group_service is None:
        _linux_user_group_service = LinuxUserGroupService()
    return _linux_user_group_service
