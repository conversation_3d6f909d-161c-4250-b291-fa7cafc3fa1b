import logging
from typing import Optional
from datetime import datetime

from sqlalchemy import or_
from sqlmodel import Session, select

from api.model.tag import Tag

logger = logging.getLogger(__name__)

def get_all_tags(session: Session) -> list[Tag]:
    """
    获取所有标签
    """
    statement = select(Tag).order_by(Tag.type.asc(), Tag.id.desc())
    return session.exec(statement).all()

def get_tag_by_name_like(session: Session, name: str = None, user_id: int = None) -> list[Tag]:
    """
    通过名称查询标签
    """
    statement = select(Tag).distinct().order_by(Tag.type.asc(), Tag.id.desc())
    if name:
        statement = statement.where(Tag.name.ilike(f"%{name}%"))
    if user_id:
        statement = statement.where(or_(Tag.created_by == user_id, Tag.type == 1))
    return session.exec(statement).all()

def get_tag_by_name(session: Session, name: str = None, user_id: int = None) -> Optional[Tag]:
    """
    通过名称查询标签
    """
    statement = select(Tag)
    if name:
        statement = statement.where(Tag.name == name)
    if user_id:
        statement = statement.where(or_(Tag.created_by == user_id, Tag.type == 1))
    # statement = statement.where(Tag.state == 1)
    return session.exec(statement).first()

def get_system_tags(session: Session) -> list[Tag]:
    """
    获取系统标签
    """
    statement = select(Tag).where(Tag.type == 1)
    return session.exec(statement).all()

def get_tag_by_id(session: Session, id: int) -> Optional[Tag]:
    """
    通过ID查询标签
    """
    return session.get(Tag, id)

def create_tag(session: Session, name: str, type: int, color: str, created_by: int, 
                   comments: str = None, module_type: int = 1, state: int = 1, 
                   update_by: int = None) -> Tag:
    """
    全量插入标签数据
    Args:
        session: 数据库会话
        name: 标签名称
        type: 标签类型（1:系统 2:用户）
        color: 标签颜色（如#FF0000）
        created_by: 创建人ID
        comments: 标签描述（可选）
        module_type: 标签归属模块，默认1:deepwiki
        state: 标签状态，默认1:有效
        update_by: 修改人ID（可选）
    Returns:
        创建的标签对象
    """
    tag = Tag(
        name=name,
        type=type,
        color=color,
        created_by=created_by,
        comments=comments,
        module_type=module_type,
        state=state,
        created_date=datetime.now(),
    )
    session.add(tag)
    session.commit()
    session.refresh(tag)
    return tag

def update_tag(session: Session, tag_id: int, name: str, type: int, color: str,
                   comments: str = None, module_type: int = 1, state: int = 1, 
                   update_by: int = None) -> Tag:
    """
    更新标签
    """
    tag = get_tag_by_id(session, id=tag_id)
    if not tag:
        raise ValueError(f"标签ID {tag_id} 不存在")
    
    tag.name = name
    tag.type = type
    tag.color = color
    tag.comments = comments
    tag.state = state
    tag.update_by = update_by
    tag.update_date = datetime.now()
    
    session.commit()
    session.refresh(tag)
    return tag

def disable_tag(session: Session, id: int, update_by: int) -> bool:
    """
    禁用标签
    """
    tag = get_tag_by_id(session, id)
    if not tag:
        raise ValueError(f"标签ID {id} 不存在")
    
    tag.state = 0
    tag.update_by = update_by
    tag.update_date = datetime.now()
    session.commit()
    return True

def enable_tag(session: Session, id: int, update_by: int) -> bool:
    """
    启用标签
    """
    tag = get_tag_by_id(session, id)
    if not tag:
        raise ValueError(f"标签ID {id} 不存在")

    tag.state = 1
    tag.update_by = update_by
    tag.update_date = datetime.now()
    session.commit()
    return True

def delete_tag(session: Session, id: int, update_by: int) -> bool:
    """
    删除标签
    """
    tag = get_tag_by_id(session, id)
    if not tag:
        raise ValueError(f"标签ID {id} 不存在")

    session.delete(tag)
    session.commit()
    return True

def query_tags_with_filters(
    session: Session,
    name: str = None,
    tag_type: int = None,
    color: str = None,
    created_by: int = None,
    module_type: int = None,
    state: int = None,
    update_by: int = None,
    created_date_start: datetime = None,
    created_date_end: datetime = None,
    update_date_start: datetime = None,
    update_date_end: datetime = None,
    comments: str = None,
    limit: int = None,
    offset: int = None,
    order_by: str = "id",
    order_desc: bool = False
) -> list[Tag]:
    """
    根据所有可选条件查询标签，支持分页和排序
    
    Args:
        session: 数据库会话
        name: 标签名称（支持模糊查询）
        tag_type: 标签类型（1:系统 2:用户）
        color: 标签颜色
        created_by: 创建人ID
        module_type: 标签归属模块
        state: 标签状态
        update_by: 修改人ID
        created_date_start: 创建时间开始
        created_date_end: 创建时间结束
        update_date_start: 修改时间开始
        update_date_end: 修改时间结束
        comments: 标签描述（支持模糊查询）
        limit: 限制返回数量
        offset: 偏移量
        order_by: 排序字段，默认按id排序
        order_desc: 是否降序排列，默认升序
    
    Returns:
        标签列表
    """
    statement = select(Tag)
    
    # 基本字段条件
    if name:
        statement = statement.where(Tag.name == name)
    if tag_type is not None:
        statement = statement.where(Tag.type == tag_type)
    if color:
        statement = statement.where(Tag.color == color)
    if created_by is not None:
        statement = statement.where(Tag.created_by == created_by)
    if module_type is not None:
        statement = statement.where(Tag.module_type == module_type)
    if state is not None:
        statement = statement.where(Tag.state == state)
    if update_by is not None:
        statement = statement.where(Tag.update_by == update_by)
    if comments:
        statement = statement.where(Tag.comments.ilike(f"%{comments}%"))
    
    # 时间范围条件
    if created_date_start:
        statement = statement.where(Tag.created_date >= created_date_start)
    if created_date_end:
        statement = statement.where(Tag.created_date <= created_date_end)
    if update_date_start:
        statement = statement.where(Tag.update_date >= update_date_start)
    if update_date_end:
        statement = statement.where(Tag.update_date <= update_date_end)
    
    # 排序
    if hasattr(Tag, order_by):
        order_column = getattr(Tag, order_by)
        if order_desc:
            statement = statement.order_by(order_column.desc())
        else:
            statement = statement.order_by(order_column.asc())
    else:
        # 如果排序字段不存在，默认按id排序
        if order_desc:
            statement = statement.order_by(Tag.id.desc())
        else:
            statement = statement.order_by(Tag.id.asc())
    
    # 分页
    if limit:
        statement = statement.limit(limit)
    if offset:
        statement = statement.offset(offset)
    
    return session.exec(statement).all()

def count_tags_with_filters(
    session: Session,
    name: str = None,
    tag_type: int = None,
    color: str = None,
    created_by: int = None,
    module_type: int = None,
    state: int = None,
    update_by: int = None,
    created_date_start: datetime = None,
    created_date_end: datetime = None,
    update_date_start: datetime = None,
    update_date_end: datetime = None,
    comments: str = None
) -> int:
    """
    根据条件统计标签数量
    
    Args:
        session: 数据库会话
        name: 标签名称（支持模糊查询）
        tag_type: 标签类型
        color: 标签颜色
        created_by: 创建人ID
        module_type: 标签归属模块
        state: 标签状态
        update_by: 修改人ID
        created_date_start: 创建时间开始
        created_date_end: 创建时间结束
        update_date_start: 修改时间开始
        update_date_end: 修改时间结束
        comments: 标签描述（支持模糊查询）
    
    Returns:
        符合条件的标签数量
    """
    statement = select(Tag)
    
    # 基本字段条件
    if name:
        statement = statement.where(Tag.name.ilike(f"%{name}%"))
    if tag_type is not None:
        statement = statement.where(Tag.type == tag_type)
    if color:
        statement = statement.where(Tag.color == color)
    if created_by is not None:
        statement = statement.where(Tag.created_by == created_by)
    if module_type is not None:
        statement = statement.where(Tag.module_type == module_type)
    if state is not None:
        statement = statement.where(Tag.state == state)
    if update_by is not None:
        statement = statement.where(Tag.update_by == update_by)
    if comments:
        statement = statement.where(Tag.comments.ilike(f"%{comments}%"))
    
    # 时间范围条件
    if created_date_start:
        statement = statement.where(Tag.created_date >= created_date_start)
    if created_date_end:
        statement = statement.where(Tag.created_date <= created_date_end)
    if update_date_start:
        statement = statement.where(Tag.update_date >= update_date_start)
    if update_date_end:
        statement = statement.where(Tag.update_date <= update_date_end)
    
    return len(session.exec(statement).all())

