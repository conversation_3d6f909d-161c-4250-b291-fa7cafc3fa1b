"""Service helpers for managing app and wiki bindings."""

from __future__ import annotations

import logging
from contextlib import contextmanager
from datetime import datetime
from typing import List, Optional, Sequence

from sqlmodel import Session, select

from api.database.base import session_scope
from api.model.app_wiki_rel import AppWikiRel
from api.model.wiki_info import WikiInfo

logger = logging.getLogger(__name__)


@contextmanager
def _managed_session(session: Optional[Session]):
    if session is not None:
        yield session
        return
    with session_scope() as managed:
        yield managed


def list_app_wiki_relations(app_id: int, *, session: Optional[Session] = None) -> List[AppWikiRel]:
    with _managed_session(session) as db:
        stmt = select(AppWikiRel).where(AppWikiRel.app_id == app_id)
        rows = db.exec(stmt).all()
        return rows


def list_app_wikis(app_id: int, *, session: Optional[Session] = None) -> List[WikiInfo]:
    with _managed_session(session) as db:
        stmt = (
            select(WikiInfo)
            .join(AppWikiRel, AppWikiRel.wiki_id == WikiInfo.id)
            .where(AppWikiRel.app_id == app_id)
            .order_by(WikiInfo.id.asc())
        )
        rows = db.exec(stmt).all()
        return rows


def list_wiki_ids_for_app(app_id: int, *, session: Optional[Session] = None) -> List[int]:
    with _managed_session(session) as db:
        stmt = select(AppWikiRel.wiki_id).where(AppWikiRel.app_id == app_id)
        rows = db.exec(stmt).all()
        wiki_ids: List[int] = []
        for row in rows:
            if isinstance(row, int):
                wiki_ids.append(row)
            elif isinstance(row, (list, tuple)) and row:
                wiki_ids.append(int(row[0]))
            elif hasattr(row, "wiki_id") and getattr(row, "wiki_id") is not None:
                wiki_ids.append(int(getattr(row, "wiki_id")))
        return wiki_ids


def replace_app_wikis(
    app_id: int,
    wiki_ids: Sequence[int],
    *,
    operator_id: int,
    session: Optional[Session] = None,
) -> None:
    wiki_id_set = {int(wid) for wid in wiki_ids}
    now = datetime.utcnow()

    with _managed_session(session) as db:
        if wiki_id_set:
            wiki_rows = db.exec(select(WikiInfo.id).where(WikiInfo.id.in_(wiki_id_set))).all()
            existing_wiki_ids = set()
            for row in wiki_rows:
                if isinstance(row, int):
                    existing_wiki_ids.add(row)
                elif hasattr(row, "id") and getattr(row, "id") is not None:
                    existing_wiki_ids.add(getattr(row, "id"))
                elif isinstance(row, (list, tuple)) and row:
                    existing_wiki_ids.add(row[0])
            missing = wiki_id_set - existing_wiki_ids
            if missing:
                raise ValueError(f"Wiki ids {sorted(missing)} do not exist")

        existing_relations = db.exec(
            select(AppWikiRel).where(AppWikiRel.app_id == app_id)
        ).all()
        existing_map = {rel.wiki_id: rel for rel in existing_relations}

        # 删除不再需要的关联
        for rel in existing_relations:
            if rel.wiki_id not in wiki_id_set:
                db.delete(rel)

        # 新增关联
        for wiki_id in wiki_id_set:
            if wiki_id not in existing_map:
                db.add(
                    AppWikiRel(
                        app_id=app_id,
                        wiki_id=wiki_id,
                        created_by=operator_id,
                        created_date=now,
                        update_by=operator_id,
                        update_date=now,
                    )
                )
            else:
                # 更新更新时间
                existing = existing_map[wiki_id]
                existing.update_by = operator_id
                existing.update_date = now
                db.add(existing)

        db.flush()
