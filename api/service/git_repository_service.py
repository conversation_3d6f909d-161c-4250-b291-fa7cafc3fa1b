import logging
from contextlib import contextmanager
from typing import Iterable, Optional, List

from sqlmodel import Session, select

from api.database.base import session_scope
from api.model.git_repository import AiDwGitRepository
from api.model.wiki_repository_relation import AiDwWikiRepositoryRelation


logger = logging.getLogger(__name__)


@contextmanager
def _managed_session(session: Optional[Session]):
    if session is not None:
        yield session
        return
    with session_scope() as new_session:
        yield new_session


def get_by_url_branch(repo_url: str, branch: str, *, session: Optional[Session] = None) -> Optional[AiDwGitRepository]:
    with _managed_session(session) as db:
        row = db.exec(
            select(AiDwGitRepository).where(
                AiDwGitRepository.repo_url == repo_url,
                AiDwGitRepository.branch == branch,
            )
        ).first()
        return AiDwGitRepository(**row.model_dump()) if row else None


def upsert_repository(
    *,
    repo_url: str,
    repo_owner: str,
    repo_name: str,
    branch: str = "master",
    repo_type: str = "whaleDevCloud",
    description: Optional[str] = None,
    code_topic_id: Optional[str] = None,
    is_private: Optional[bool] = None,
    file_count: Optional[int] = None,
    status: Optional[str] = None,
    linux_gid: Optional[int] = None,
    linux_group_name: Optional[str] = None,
    linux_code_file_perm: Optional[bool] = None,
    linux_pw_file_perm: Optional[bool] = None,
    linux_uw_file_perm: Optional[bool] = None,
    session: Optional[Session] = None,
) -> AiDwGitRepository:
    """Create or update a git repository row and return a detached copy."""
    with _managed_session(session) as db:
        existing = db.exec(
            select(AiDwGitRepository).where(
                AiDwGitRepository.repo_url == repo_url,
                AiDwGitRepository.branch == branch,
            )
        ).first()

        if existing:
            fields_to_update = {
                "repo_owner": repo_owner,
                "repo_name": repo_name,
                "repo_type": repo_type,
                "description": description,
                "code_topic_id": code_topic_id,
                "is_private": is_private,
                "file_count": file_count,
                "status": status,
                "linux_gid": linux_gid,
                "linux_group_name": linux_group_name,
                "linux_code_file_perm": linux_code_file_perm,
                "linux_pw_file_perm": linux_pw_file_perm,
                "linux_uw_file_perm": linux_uw_file_perm,
            }
            changed = False
            for field, value in fields_to_update.items():
                if value is not None and getattr(existing, field) != value:
                    setattr(existing, field, value)
                    changed = True
            if changed:
                db.add(existing)
            db.flush()
            return AiDwGitRepository(**existing.model_dump())

        repo = AiDwGitRepository(
            repo_url=repo_url,
            branch=branch,
            repo_owner=repo_owner,
            repo_name=repo_name,
            repo_type=repo_type,
            description=description,
            code_topic_id=code_topic_id,
            is_private=is_private or False,
            file_count=file_count or 0,
            status=status or "active",
            linux_gid=linux_gid,
            linux_group_name=linux_group_name,
            linux_code_file_perm=linux_code_file_perm or False,
            linux_pw_file_perm=linux_pw_file_perm or False,
            linux_uw_file_perm=linux_uw_file_perm or False,
        )
        db.add(repo)
        db.flush()
        return AiDwGitRepository(**repo.model_dump())


def update_repository(repo_id: int, *, session: Optional[Session] = None, **fields) -> Optional[AiDwGitRepository]:
    """Update fields on a repository by id."""
    if not fields:
        return None

    with _managed_session(session) as db:
        repo = db.exec(select(AiDwGitRepository).where(AiDwGitRepository.id == repo_id)).first()
        if not repo:
            return None

        changed = False
        for key, value in fields.items():
            if value is not None and hasattr(repo, key) and getattr(repo, key) != value:
                setattr(repo, key, value)
                changed = True

        if changed:
            db.add(repo)
            db.flush()
        return AiDwGitRepository(**repo.model_dump())


def list_by_wiki(
    wiki_id: str,
    *,
    only_main: bool = False,
    session: Optional[Session] = None,
) -> List[AiDwGitRepository]:
    with _managed_session(session) as db:
        rel_stmt = select(AiDwWikiRepositoryRelation).where(AiDwWikiRepositoryRelation.wiki_id == wiki_id)
        if only_main:
            rel_stmt = rel_stmt.where(AiDwWikiRepositoryRelation.is_main_repo == True)  # noqa: E712
        rel_stmt = rel_stmt.order_by(AiDwWikiRepositoryRelation.id.asc())
        relations = db.exec(rel_stmt).all()
        repo_ids = [rel.repository_id for rel in relations]
        if not repo_ids:
            return []
        rows = db.exec(select(AiDwGitRepository).where(AiDwGitRepository.id.in_(repo_ids))).all()
        return [AiDwGitRepository(**row.model_dump()) for row in rows]


def bulk_update_topic_ids(
    relations: Iterable[AiDwWikiRepositoryRelation],
    topic_ids: Iterable[str],
    *,
    session: Optional[Session] = None,
) -> None:
    """Utility to update repository topic ids in order of relations."""
    with _managed_session(session) as db:
        for relation, topic_id in zip(relations, topic_ids):
            update_repository(relation.repository_id, session=db, code_topic_id=topic_id)


def get_main_repository_by_wiki_id(wiki_id: str, *, session: Optional[Session] = None) -> Optional[AiDwGitRepository]:
    with _managed_session(session) as db:
        rel_stmt = select(AiDwWikiRepositoryRelation).where(AiDwWikiRepositoryRelation.wiki_id == wiki_id)
        rel_stmt = rel_stmt.where(AiDwWikiRepositoryRelation.is_main_repo == True)  # noqa: E712
        relation = db.exec(rel_stmt).first()
        if not relation:
            return None
        return AiDwGitRepository(**relation.model_dump())

