import logging
from typing import List
from sqlmodel import select
from api.database.base import session_scope
from api.model.user_role import UserRole
from api.cache.redis.manager import redis_manager

logger = logging.getLogger(__name__)

def get_user_role_cache_key(user_id: int) -> str:
    """
    生成用户角色redis缓存key
    """
    return f"user:{{{user_id}}}:roles"

def get_user_roles_from_redis(user_id: int):
    """
    从redis获取用户角色id列表
    """
    client = redis_manager.get_client()
    if not client:
        return None
    key = get_user_role_cache_key(user_id)
    role_ids = client.get(key)
    if role_ids:
        return [int(r) for r in role_ids.split(",") if r]
    return None

def set_user_roles_to_redis(user_id: int, role_ids: list):
    """
    设置用户角色id列表到redis
    """
    client = redis_manager.get_client()
    if not client:
        return
    key = get_user_role_cache_key(user_id)
    value = ",".join(str(r) for r in role_ids)
    client.set(key, value)

def get_user_role_ids(user_id: int) -> List[int]:
    """
    查询用户所有角色id列表
    """
    with session_scope() as session:
        ur = UserRole
        stmt = (
            select(ur.role_id)
            .where(ur.user_id == user_id)
        )
        return session.scalars(stmt).all()

def get_or_cache_user_role_ids(user_id: int) -> list:
    """
    优先从redis获取用户角色id，查不到则查库并写入redis，返回角色id列表
    """
    role_ids = get_user_roles_from_redis(user_id)
    if not role_ids:
        role_ids = get_user_role_ids(user_id)
        if role_ids:
            set_user_roles_to_redis(user_id, role_ids)
    return role_ids

def delete_user_roles_from_redis(user_id: int):
    """
    从redis中删除用户角色id缓存
    """
    client = redis_manager.get_client()
    if not client:
        return
    key = get_user_role_cache_key(user_id)
    client.delete(key)

