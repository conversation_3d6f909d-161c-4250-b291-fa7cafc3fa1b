"""
用户组绑定服务
处理用户与Wiki的Linux用户组绑定业务逻辑
"""

from datetime import datetime, timezone
import logging
from typing import Tuple, Optional, Dict

from api.database.base import session_scope
from api.model.git_repository import AiDwGitRepository
from api.sandbox.kubernetes_service import get_kubernetes_service
from api.service.linux_user_group_service import get_linux_user_group_service
from api.service.wiki_relation_service import list_relations
from api.utils.job_redis_utils import get_user_group_id, get_wiki_group_id
from api.constants.symbolic_link import SymbolicLinkPath

logger = logging.getLogger(__name__)


class UserGroupBindService:
    """用户组绑定服务"""
    
    def __init__(self):
        pass
    
    def bind_user_to_wiki(
        self, 
        wiki_info: Dict, 
        user_id: int, 
        user_code: str,
        user_name: str,
        job_name: str
    ) -> Tuple[bool, str, Optional[Dict]]:
        """
        将用户绑定到wiki的用户组
        
        Args:
            wiki_info: Wiki信息字典
            user_id: 用户ID
            user_code: 用户代码
            job_name: 从job池中分配的job名称
            
        Returns:
            (是否成功, 消息, 额外数据)
        """
        res = False
        updated_metadata = False
        try:
            # 1. 从传入的wiki_info中获取信息
            wiki_id = wiki_info.get("wiki_id")
            wikiId = wiki_info.get("id")
            owner = wiki_info.get("repo_owner")
            repo_name = wiki_info.get("repo_name")
            branch = wiki_info.get("branch")
            branch_clean = branch
            
            # 2. 获取wiki关联的仓库信息
            relations = list_relations(wiki_id=wiki_id, only_main=True)
            if len(relations) <= 0:
                return False, "未找到wiki关联的仓库", None
            wiki_repo_relation = relations[0]
            
            # 3. 检查或创建wiki用户组
            wiki_repo = self._ensure_wiki_group(
                wiki_repo_relation=wiki_repo_relation
            )
            if not wiki_repo:
                return False, "创建或获取wiki用户组失败", None
            wiki_group_name = wiki_repo.linux_group_name
            wiki_gid = wiki_repo.linux_gid
            
            # 4. 检查或创建用户的个人用户组
            user_group_result = self._ensure_user_group(
                user_id=user_id,
                user_code=user_code
            )
            if not user_group_result:
                return False, "创建或获取用户个人组失败", None
            user_group_name, user_group_id = user_group_result

            from api.sandbox.kubernetes_service import get_kubernetes_service
            # 7. 获取job的pod名称
            job_info = get_kubernetes_service().get_job(job_name=job_name)
            if not job_info or not job_info.get("pods"):
                return False, f"未找到job的pod: {job_name}", None
            
            job_pod_name = job_info.get("pods")[0].get("name")
            logger.info(f"job pod名称: {job_pod_name}")


            pid = get_kubernetes_service().get_pid(pod_name=job_pod_name)
            if pid <= 0:
                logger.error(f"未找到进程: {job_pod_name}")
                return False, f"未找到进程: {job_pod_name}", None
            
            # 5. 使用从job池中分配的job_name，更新job的metadata
            current_time = datetime.now(timezone.utc).isoformat()

            result = get_kubernetes_service().update_job_metadata(
                job_name=job_name, 
                annotations={"wiki.id": str(wikiId), "user.code": user_code, "user.id": str(user_id), "git.url": wiki_info.get("repo_url"), "git.branch": wiki_info.get("branch"), "user.name": user_name, "last-access-time": current_time}, 
                labels={"wiki-id": str(wikiId),
                        "user-code": user_code,
                        "user-id": str(user_id),
                        "git-url": wiki_info.get("repo_url"),
                        "git-branch": wiki_info.get("branch"),
                        "user-group-name": user_group_name,
                        "wiki-group-name": wiki_group_name,
                        "pid": str(pid),
                        "user-name": user_name,
                        "start-type": "pre-load"
                    }
            )    
            if not result:
                logger.error(f"更新job metadata失败: {job_name}")
                return False, "更新job metadata失败", None
            else:
                logger.info(f"成功更新job metadata: {job_name}")
                updated_metadata = True
            
            # 6. 设置目录权限（传入GID以便在主机上创建用户组），如果查询repo表，已经都设置过权限，则跳过此步骤
            linux_code_file_perm, linux_pw_file_perm, linux_uw_file_perm = self._setup_directory_permissions(
                owner=owner,
                repo_name=repo_name,
                branch_clean=branch_clean,
                user_code=user_code,
                wiki_group_name=wiki_group_name,
                wiki_group_gid=wiki_gid,
                user_group_name=user_group_name,
                user_group_gid=user_group_id,
                wiki_repo=wiki_repo
            )

            if not linux_code_file_perm:
                return False, "设置仓库目录权限失败", None
            if not linux_pw_file_perm:
                return False, "设置项目工作空间目录权限失败", None
            if not linux_uw_file_perm:
                return False, "设置用户工作空间目录权限失败", None
            
            with session_scope() as session:
                from api.service.git_repository_service import update_repository
                try:
                    update_repository(
                        repo_id=wiki_repo_relation.repository_id,
                        linux_code_file_perm=linux_code_file_perm,
                        linux_pw_file_perm=linux_pw_file_perm,
                        linux_uw_file_perm=linux_uw_file_perm,
                        session=session
                    )
                    session.commit()
                    logger.info(f"已更新git仓库，linux_code_file_perm=True, linux_pw_file_perm=True, linux_uw_file_perm=True")
                except Exception as e:
                    logger.error(f"更新git仓库时发生错误: {e}")
                    session.rollback()
                    raise e
            # 8. 在pod中创建用户组
            result, error_msg = get_linux_user_group_service().create_group_in_pod(pod_name=job_pod_name, gid=wiki_gid, group_name=wiki_group_name)
            if not result:
                return False, f"创建wiki组失败: {error_msg}", None

            result, error_msg = get_linux_user_group_service().create_group_in_pod(pod_name=job_pod_name, gid=user_group_id, group_name=user_group_name)
            if not result:
                return False, f"创建用户组失败: {error_msg}", None
            
            # 9. 将job用户添加到wiki用户组
            result, error_msg = get_linux_user_group_service().add_user_to_group_in_pod(pod_name=job_pod_name, group_name=wiki_group_name)
            if not result:
                return False, f"将用户添加到用户组失败: {error_msg}", None

            result, error_msg = get_linux_user_group_service().add_user_to_group_in_pod(pod_name=job_pod_name, group_name=user_group_name)
            if not result:
                return False, f"将用户添加到用户组失败: {error_msg}", None
            
            # 10. 在pod容器中创建软链接
            # 软链接格式: ln -s <源路径(实际存在的)> <目标路径(要创建的链接)>
            logger.info(f"gemini目录：{f"/deepwiki/.gemini/"}")
            symlink_paths = SymbolicLinkPath.get_symlink_paths(owner, repo_name, branch_clean, user_code)
            
            symlink_result = get_kubernetes_service().create_symbolic_link(
                pod_name=job_pod_name,
                paths=symlink_paths
            )
            
            if not symlink_result.get("success"):
                logger.error(f"创建软链接失败: {symlink_result.get('msg')}")
                raise Exception(f"创建软链接失败: {symlink_result.get('msg')}")
            else:
                logger.info(f"成功创建软链接: {job_pod_name}")

            # 11. 发送信号，设置进程gid

            result = get_kubernetes_service().send_signal(pod_name=job_pod_name, pid=pid)
            if not result:
                logger.error(f"发送信号失败: {job_pod_name}")
                return False, f"发送信号失败: {job_pod_name}", None
            
            logger.info(f"成功将用户 {user_id} 绑定到 wiki {wikiId}，使用job: {job_name}")
            res = True
            return True, "绑定Linux用户组成功", {
                "job_name": job_name,
                "job_username": "wct"
            }
            
        except Exception as e:
            error_msg = "分配沙盒时发生错误"
            logger.error(f"{error_msg}: {e}")
            return False, error_msg, None
        finally:
            if not res and updated_metadata:
                logger.warning("绑定job失败，回退job元数据")
                get_kubernetes_service().delete_job_metadata(
                    job_name=job_name, 
                    label_keys=["wiki-id", "user-code", "user-id", "git-url", "git-branch", "user-group-name", "wiki-group-name", "pid", "user-name"],
                    annotation_keys=["wiki.id", "user.code", "user.id", "git.url", "git.branch", "user.name", "last-access-time"]
                )
    
    def unbind_user_from_job(self, job_name: str) -> Tuple[bool, str]:
        job_info = get_kubernetes_service().get_job(job_name=job_name)
        if not job_info:
            return False, "未找到job"

        # 获取job的pod名称
        if not job_info.get("pods"):
            return False, f"未找到job的pod: {job_name}"
        
        job_pod_name = job_info.get("pods")[0].get("name")
        logger.info(f"解绑job pod名称: {job_pod_name}")

        wiki_group_name = job_info.get("labels").get("wiki-group-name")
        user_group_name = job_info.get("labels").get("user-group-name")

        # 1. 从组中移除用户
        result, error_msg = get_linux_user_group_service().remove_user_from_group_in_pod(pod_name=job_pod_name, group_name=wiki_group_name)
        if not result:
            return False, error_msg

        result, error_msg = get_linux_user_group_service().remove_user_from_group_in_pod(pod_name=job_pod_name, group_name=user_group_name)
        if not result:
            return False, error_msg

        # 2. 获取pid
        pid = get_kubernetes_service().get_pid(pod_name=job_pod_name)
        if pid <= 0:
            return False, "未找到进程"
        # 3. 发送信号，刷新进程gid
        result = get_kubernetes_service().send_signal(pod_name=job_pod_name, pid=pid)
        if not result:
            return False, "发送信号失败"

        # 4. 删除pod中label和注释相关信息
        result = get_kubernetes_service().delete_job_metadata(
            job_name=job_name, 
            label_keys=["wiki-id", "user-code", "user-id", "git-url", "git-branch", "user-group-name", "wiki-group-name", "pid", "user-name"],
            annotation_keys=["wiki.id", "user.code", "user.id", "git.url", "git.branch", "user.name", "last-access-time"]
        )
        if not result:
            return False, "删除label和注释相关信息失败"

        # 5. 删除软链接
        result = get_kubernetes_service().unlink_symbolic(pod_name=job_pod_name, paths=SymbolicLinkPath.SYMLINK_TARGETS.value)
        if not result:
            return False, "删除软链接失败"
        return True, ""
    
    def _ensure_wiki_group(self, wiki_repo_relation) -> Optional[AiDwGitRepository]:
        """
        确保wiki用户组存在
        
        Args:
            wiki_id: Wiki ID
            wiki_repo_relation: Wiki仓库关联对象
            
        Returns:
            用户组名称，失败返回None
        """
        try:
            # 从关联表中获取repository_id，然后查询GitRepository表
            from sqlmodel import select
            from api.database.base import session_scope
            
            repo_id = wiki_repo_relation.repository_id
            
            with session_scope() as session:
                repo = session.exec(select(AiDwGitRepository).where(AiDwGitRepository.id == repo_id)).first()
                
                # 检查是否已有用户组
                if repo and repo.linux_gid and repo.linux_group_name:
                    logger.info(f"该wiki已绑定Linux用户组: {repo.linux_group_name}")
                    return AiDwGitRepository(**repo.model_dump())
                
                # 创建wiki用户组
                wiki_group_id = get_wiki_group_id()
                
                from api.sandbox.kubernetes_service import get_kubernetes_service
                group_name = get_kubernetes_service().generate_linux_group_name(id=wiki_group_id - 40000)
                result, group_name = get_linux_user_group_service().create_deepwiki_group(
                    gid=wiki_group_id, 
                    group_name=group_name
                )
                if not result:
                    logger.error(f"创建wiki用户组失败: {group_name}")
                    return None
                
                # 更新GitRepository表
                from api.service.git_repository_service import update_repository
                repo = update_repository(
                    repo_id=repo_id,
                    linux_gid=wiki_group_id,
                    linux_group_name=group_name,
                    session=session
                )
                session.commit()
                
                return AiDwGitRepository(**repo.model_dump())
            
        except Exception as e:
            logger.error(f"确保wiki用户组存在时发生错误: {str(e)}")
            return None
    
    def _ensure_user_group(self, user_id: int, user_code: str) -> Optional[Tuple[str, int]]:
        """
        确保用户个人用户组存在
        
        Args:
            user_id: 用户ID
            user_code: 用户代码
            
        Returns:
            (用户组名称, 用户组ID)，失败返回None
        """
        try:
            # 延迟导入避免循环依赖
            from api.service.user_service import select_user_ext_by_user_id
            # 获取用户扩展信息
            user_ext = select_user_ext_by_user_id(user_id=user_id)
            if not user_ext:
                logger.error(f"用户扩展信息不存在: user_id={user_id}")
                return None
            
            # 检查是否已有个人用户组
            if user_ext.linux_gid and user_ext.linux_group_name:
                logger.info(f"该用户已有个人用户组: {user_ext.linux_group_name}")
                return user_ext.linux_group_name, user_ext.linux_gid
            
            # 创建用户个人用户组
            user_group_id = get_user_group_id()
            
            # 用户个人组名称添加 u 前缀，以区分 wiki 组
            user_group_name = f"u{user_code}"
            result, user_group_name = get_linux_user_group_service().create_deepwiki_group(
                gid=user_group_id, 
                group_name=user_group_name
            )
            if not result:
                logger.error(f"创建用户个人组失败: {user_group_name}")
                return None
            
            # 更新user_ext表
            user_ext.linux_gid = user_group_id
            user_ext.linux_group_name = user_group_name
            if user_ext.id:
                # 延迟导入避免循环依赖
                from api.service.user_service import update_user_ext
                update_user_ext(user_ext=user_ext, id=user_ext.id)
            else:
                from api.service.user_service import add_user_ext
                add_user_ext(user_ext=user_ext)
            
            return user_group_name, user_group_id
            
        except Exception as e:
            logger.error(f"确保用户个人组存在时发生错误: {str(e)}")
            return None
    
    def _setup_directory_permissions(
        self,
        owner: str,
        repo_name: str,
        branch_clean: str,
        user_code: str,
        wiki_group_name: str,
        wiki_group_gid: int,
        user_group_name: str,
        user_group_gid: int,
        wiki_repo: AiDwGitRepository
    ) -> Tuple[bool, bool, bool]:
        """
        设置目录权限
        
        Args:
            owner: 仓库所有者
            repo_name: 仓库名称
            branch_clean: 清理后的分支名
            user_code: 用户代码
            wiki_group_name: Wiki用户组名称
            wiki_group_gid: Wiki用户组GID
            user_group_name: 用户个人组名称
            user_group_gid: 用户个人组GID
            wiki_repo: Wiki仓库对象
        """
        try:
            # 延迟导入避免循环依赖
            from api.config import get_kubernetes_config
            container_base_path = get_kubernetes_config().get("k8s_config", {}).get("base_path", "/root/.adalflow")
            
            # 确保wiki用户组在主机上存在
            linux_service = get_linux_user_group_service()

            success, msg = linux_service.create_deepwiki_group(gid=wiki_group_gid, group_name=wiki_group_name)
            if not success:
                logger.warning(f"在主机上创建wiki用户组失败: {msg}")
                return False, False, False

            success, msg = linux_service.create_deepwiki_group(gid=user_group_gid, group_name=user_group_name)
            if not success:
                logger.warning(f"在主机上创建用户个人组失败: {msg}")
                return False, False, False
            if not wiki_repo.linux_code_file_perm:
                container_repo_path = f"{container_base_path.rstrip('/')}/repos/{owner}/{repo_name}-{branch_clean}/"
                result = linux_service.set_directory_permissions(
                    code_path=container_repo_path,
                    group_name=wiki_group_name
                )
                if not result:
                    logger.error(f"设置仓库目录权限失败: {container_repo_path}")
                    return False, False, False

                result = linux_service.change_directory_permissions(
                    code_path=container_repo_path,
                    mod="750"
                )
                if not result:
                    logger.error(f"修改仓库目录权限失败: {container_repo_path}, mod=750")
                    return False, False, False
            # 确保用户个人组在主机上存在
            if not wiki_repo.linux_uw_file_perm:
                # 个人工作空间目录
                container_user_workspace_path = f"{container_base_path.rstrip('/')}/workspace/{user_code}/"
                container_workspace_path = f"{container_base_path.rstrip('/')}/workspace/{user_code}/{owner}/{repo_name}-{branch_clean}/"
                # 确保目录存在
                get_kubernetes_service().ensure_workspace(workspace_path=container_workspace_path)
                # result = linux_service.set_facl_permissions(code_path=container_user_workspace_path)
                # if not result:
                #     logger.error(f"设置个人工作空间目录acl权限失败: {container_user_workspace_path}")
                #     return True, False, False
                result = linux_service.set_directory_permissions(
                    code_path=container_user_workspace_path,
                    group_name=user_group_name
                )
                if not result:
                    logger.error(f"设置个人工作空间目录权限失败: {container_workspace_path}")
                    return True, False, False

                result = linux_service.change_directory_permissions(
                    code_path=container_user_workspace_path,
                    mod="750"
                )
                if not result:
                    logger.error(f"修改个人工作空间目录权限失败: {container_user_workspace_path}, mod=750")
                    return True, False, False

                result = linux_service.set_directory_permissions(
                    code_path=container_workspace_path,
                    group_name=wiki_group_name
                )
                if not result:
                    logger.error(f"设置仓库目录权限失败: {container_workspace_path}")
                    return True, False, False

                result = linux_service.change_directory_permissions(
                    code_path=container_workspace_path,
                    mod="777",
                    if_recursive=True
                )
                if not result:
                    logger.error(f"修改仓库目录权限失败: {container_workspace_path}, mod=770")
                    return True, False, False

                result = linux_service.change_directory_permissions(
                    code_path=container_workspace_path,
                    mod="770",
                    if_recursive=False
                )
                if not result:
                    logger.error(f"修改仓库目录权限失败: {container_workspace_path}, mod=770")
                    return True, False, False

            if not wiki_repo.linux_pw_file_perm:
                # 项目工作空间目录
                container_project_workspace_path = f"{container_base_path.rstrip('/')}/project_workspace/{owner}/{repo_name}-{branch_clean}/"
                
                # 确保项目工作空间目录存在
                get_kubernetes_service().ensure_project_workspace(project_workspace_path=container_project_workspace_path)
                # result = linux_service.set_facl_permissions(code_path=container_project_workspace_path)
                # if not result:
                #     logger.error(f"设置项目工作空间目录acl权限失败: {container_project_workspace_path}")
                #     return True, True, False
                result = linux_service.set_directory_permissions(
                    code_path=container_project_workspace_path,
                    group_name=wiki_group_name
                )
                if not result:
                    logger.error(f"设置项目工作空间目录权限失败: {container_project_workspace_path}")
                    return True, True, False

                result = linux_service.change_directory_permissions(
                    code_path=container_project_workspace_path,
                    mod="777",
                    if_recursive=True
                )
                if not result:
                    logger.error(f"修改项目工作空间目录权限失败: {container_project_workspace_path}, mod=777")
                    return True, True, False

                result = linux_service.change_directory_permissions(
                    code_path=container_project_workspace_path,
                    mod="770",
                    if_recursive=False
                )
                if not result:
                    logger.error(f"修改项目工作空间目录权限失败: {container_project_workspace_path}, mod=770")
                    return True, True, False
            logger.info("目录权限设置完成")
            return True, True, True
        except Exception as e:
            logger.error(f"设置目录权限时发生错误: {str(e)}")
            return False, False, False

    def setup_code_and_project_workspace_permissions(
        self,
        owner: str,
        repo_name: str,
        branch_clean: str,
        wiki_group_name: Optional[str],
        wiki_group_gid: Optional[int],
        wiki_repo: AiDwGitRepository) -> Tuple[bool, bool]:
        """
        设置代码和项目工作空间目录权限
        
        Args:
            wiki_repo: Wiki仓库对象
        """
        try:
            # 延迟导入避免循环依赖
            from api.config import get_kubernetes_config
            container_base_path = get_kubernetes_config().get("k8s_config", {}).get("base_path", "/root/.adalflow")
            
            # 确保wiki用户组在主机上存在
            linux_service = get_linux_user_group_service()
            if not wiki_repo.linux_code_file_perm:
                success, msg = linux_service.create_deepwiki_group(gid=wiki_group_gid, group_name=wiki_group_name)
                if not success:
                    logger.warning(f"在主机上创建wiki用户组失败: {msg}")
                    return False, False
                container_repo_path = f"{container_base_path.rstrip('/')}/repos/{owner}/{repo_name}-{branch_clean}/"
                result = linux_service.set_directory_permissions(
                    code_path=container_repo_path,
                    group_name=wiki_group_name
                )
                if not result:
                    logger.error(f"设置仓库目录权限失败: {container_repo_path}")
                    return False, False

                result = linux_service.change_directory_permissions(
                    code_path=container_repo_path,
                    mod="750"
                )
                if not result:
                    logger.error(f"修改仓库目录权限失败: {container_repo_path}, mod=750")
                    return False, False

            if not wiki_repo.linux_pw_file_perm:
                success, msg = linux_service.create_deepwiki_group(gid=wiki_group_gid, group_name=wiki_group_name)
                if not success:
                    logger.warning(f"在主机上创建用户个人组失败: {msg}")
                    return True, False
                # 项目工作空间目录
                container_project_workspace_path = f"{container_base_path.rstrip('/')}/project_workspace/{owner}/{repo_name}-{branch_clean}/"
                result = linux_service.set_directory_permissions(
                    code_path=container_project_workspace_path,
                    group_name=wiki_group_name
                )
                if not result:
                    logger.error(f"设置项目工作空间目录权限失败: {container_project_workspace_path}")
                    return True, False

                result = linux_service.change_directory_permissions(
                    code_path=container_project_workspace_path,
                    mod="777",
                    if_recursive=True
                )
                if not result:
                    logger.error(f"修改项目工作空间目录权限失败: {container_project_workspace_path}, mod=770")
                    return True, False
                
                result = linux_service.change_directory_permissions(
                    code_path=container_project_workspace_path,
                    mod="770",
                    if_recursive=False
                )
                if not result:
                    logger.error(f"修改项目工作空间目录权限失败: {container_project_workspace_path}, mod=770")
                    return True, False
                

            logger.info("目录权限设置完成")
            return True, True
        except Exception as e:
            logger.error(f"设置代码和项目工作空间目录权限时发生错误: {str(e)}")
            return False, False

# 全局服务实例
_user_group_bind_service = None


def get_user_group_bind_service() -> UserGroupBindService:
    """获取用户组绑定服务实例"""
    global _user_group_bind_service
    if _user_group_bind_service is None:
        _user_group_bind_service = UserGroupBindService()
    return _user_group_bind_service
