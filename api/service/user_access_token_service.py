import logging
from typing import Optional
from datetime import datetime

from sqlalchemy import or_
from sqlmodel import Session, select
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from api.model.user_access_token import UserAccessToken

logger = logging.getLogger(__name__)

def add_user_access_token(session: Session, user_id: int, name: str, token: str, use_type: Optional[str], effective_at: Optional[datetime], expires_at: Optional[datetime], created_by: int, created_date: datetime, update_by: Optional[int], update_date: Optional[datetime], last_used_time: Optional[datetime]) -> UserAccessToken:
    user_access_token = UserAccessToken(user_id=user_id, name=name, token=token, state=1, use_type=use_type, effective_at=effective_at, expires_at=expires_at, created_by=created_by, created_date=created_date, update_by=update_by, update_date=update_date, last_used_time=last_used_time)
    session.add(user_access_token)
    session.commit()
    return user_access_token

def get_user_access_token(session: Session, user_id: int, name: Optional[str] = None):
    sql = select(UserAccessToken).where((UserAccessToken.user_id == user_id) & (UserAccessToken.state == 1))
    if name:
        sql = sql.where(UserAccessToken.name.ilike(f"%{name}%"))
    return session.exec(sql).all()

def get_accurate_user_access_token(session: Session, user_id: int, name: Optional[str] = None):
    sql = select(UserAccessToken).where((UserAccessToken.user_id == user_id) & (UserAccessToken.state == 1))
    if name:
        sql = sql.where(UserAccessToken.name == name)
    return session.exec(sql).first()

def disable_user_access_token(session: Session, id: int, user_id: int):
    user_access_token = session.get(UserAccessToken, id)
    if not user_access_token:
        raise HTTPException(status_code=404, detail="User access token not found")
    user_access_token.state = 0
    user_access_token.update_by = user_id
    user_access_token.update_date = datetime.now()
    session.commit()
    return user_access_token

def delete_user_access_token(session: Session, id: int, user_id: int):
    user_access_token = session.get(UserAccessToken, id)
    if not user_access_token:
        raise HTTPException(status_code=404, detail="User access token not found")
    session.delete(user_access_token)
    session.commit()
    return user_access_token

def enable_user_access_token(session: Session, id: int, user_id: int):
    user_access_token = session.get(UserAccessToken, id)
    if not user_access_token:
        raise HTTPException(status_code=404, detail="User access token not found")
    user_access_token.state = 1
    user_access_token.update_by = user_id
    user_access_token.update_date = datetime.now()
    session.commit()
    return user_access_token