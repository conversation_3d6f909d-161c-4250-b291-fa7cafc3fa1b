import logging
from datetime import datetime
from typing import Optional, List

from cachetools import TTL<PERSON><PERSON>
from sqlalchemy import func, or_,  not_
from sqlmodel import select, Session

from api.common.constants import PrivType
from api.database.base import session_scope
from api.middleware.auth_middleware import get_current_user
from api.model.user_ext import UserExt
from api.model.user_info import UserInfo
from api.model.user_role import UserRole, Role, RolePriv
from api.model.wiki_info import WikiInfo
from api.model.wiki_user_role import WikiUserRole
from sqlalchemy.orm import aliased

# 创建日志记录器
logger = logging.getLogger(__name__)

# 缓存序列化数据而不是SQLModel对象
# user_id_cache = TTLCache(maxsize=1000, ttl=60 * 60 * 24)
# user_ext_cache = TTLCache(maxsize=1000, ttl=60 * 60 * 24)

def select_user_info(wcp_user_id: int) -> Optional[UserInfo]:
    """
    通过鲸加用户id查询用户信息
    """
    # if wcp_user_id in user_id_cache:
        # 从缓存的字典数据重新创建UserInfo对象
        # cached_data = user_id_cache[wcp_user_id]
        # return UserInfo(**cached_data)
    
    with session_scope() as session:
        user_info_db = _select_by_user_id(session, wcp_user_id)
        if user_info_db is None:
            logger.warning(f"User info not found for user_id: {wcp_user_id}")
            return None
        
        # 缓存序列化后的数据
        user_data = user_info_db.model_dump()
        # user_id_cache[wcp_user_id] = user_data
        
        # 返回一个新的UserInfo对象
        return UserInfo(**user_data)

def init_user_when_login(user_info: UserInfo) -> Optional[UserInfo]:
    with session_scope() as session:
        try:
            user_info.created_date = datetime.now()
            # 先不设置 created_by
            session.add(user_info)
            # 默认有效
            user_info.state = 1
            
            # 立即执行SQL但不提交，以获取生成的ID
            session.flush()

            # 更新创建人为自己的user_id
            user_info.created_by = user_info.id

            # 绑定默认角色 4
            user_role = (UserRole(user_id=user_info.id, role_id=4))
            session.add(user_role)

            # 提交事务（包括created_by的更新）
            session.commit()
            
            # 刷新数据
            session.refresh(user_info)

            # 缓存序列化数据
            user_data = user_info.model_dump()
            # user_id_cache[user_info.wcp_user_id] = user_info.model_dump()
            return UserInfo(**user_data)
        except Exception as e:
            session.rollback()
            logger.error(f"Failed to add user info for user_id {user_info.wcp_user_id}: {str(e)}")
            raise e
        
def update_user_info_when_login(user_info: UserInfo, id: int) -> bool:
    with session_scope() as session:
        try:
            # 使用 session.get() 获取与当前会话绑定的实例
            db_user_info = session.get(UserInfo, id)
            if not db_user_info:
                logger.warning(f"User info not found for update with id: {id}")
                return False
            
            user_info.update_by = id
            user_info.update_date = datetime.now()
            # 从传入的 user_info 对象中获取要更新的数据
            update_data = user_info.model_dump(exclude_unset=True)
            for key, value in update_data.items():
                # 不更新主键
                if key != 'id':
                    setattr(db_user_info, key, value)
        
            user_roles = select_user_roles(id)
            if user_roles is None or len(user_roles) == 0:
                user_role = (UserRole(user_id=id, role_id=4))
                session.add(user_role)

            session.commit()
            # 刷新实例以获取最新状态
            session.refresh(db_user_info)
            # 缓存序列化数据
            # user_id_cache[db_user_info.wcp_user_id] = db_user_info.model_dump()
            return True
        except Exception as e:
            session.rollback()
            logger.error(f"Failed to update user info for id {id}: {str(e)}")
            return False

def _select_by_user_id(session: Session, wcp_user_id: int) -> Optional[UserInfo]:
    statement = select(UserInfo).where(UserInfo.wcp_user_id == wcp_user_id)
    return session.exec(statement).first()

def select_user_info_by_code(user_code: str) -> Optional[UserInfo]:
    with session_scope() as session:
        statement = select(UserInfo).where(UserInfo.user_code == user_code)
        user_info_db = session.exec(statement).first()
        if not user_info_db:
            return None
        user_data = user_info_db.model_dump()
        return UserInfo(**user_data)

# 新增：一体化查询个人沙盒配额（优先数据库，返回 None 表示未设置）

def get_user_sandbox_quota_by_user_code(user_code: str) -> Optional[int]:
    with session_scope() as session:
        user_stmt = select(UserInfo).where(UserInfo.user_code == user_code)
        user = session.exec(user_stmt).first()
        if not user:
            return None
        ext_stmt = select(UserExt).where(UserExt.user_id == user.id)
        user_ext = session.exec(ext_stmt).first()
        if not user_ext:
            return None
        quota_val = getattr(user_ext, "sandbox_quota", None)
        try:
            return int(quota_val) if quota_val is not None else None
        except Exception:
            return None


def select_user_ext(user_id: int) -> Optional[UserExt]:
    """
    通过用户id查询用户扩展信息
    """
    # if user_id in user_ext_cache:
        # 从缓存的字典数据重新创建UserExt对象
        # cached_data = user_ext_cache[user_id]
        # return UserExt(**cached_data)
    
    with session_scope() as session:
        user_ext_db = _select_by_user_id_ext(session, user_id)
        if user_ext_db is None:
            logger.warning(f"User ext info not found for user_id: {user_id}")
            return None
        
        
        # 提取需要的字段，增加 sandbox_quota
        user_data = {
            "ai_api_key": getattr(user_ext_db, "ai_api_key", None),
            "dev_cloud_token": getattr(user_ext_db, "dev_cloud_token", None),
            "sandbox_quota": getattr(user_ext_db, "sandbox_quota", None),
            "id": getattr(user_ext_db, "id", None)
        }
        # user_ext_cache[user_id] = user_data

        # 只返回包含这几个字段的 UserExt 对象
        return UserExt(**user_data)

def add_user_ext(user_ext: UserExt) -> bool:
    with session_scope() as session:
        try:
            session.add(user_ext)
            session.commit()
            session.refresh(user_ext)  # 获取数据库生成的ID等字段
            # 缓存序列化数据
            # user_ext_cache[user_ext.user_id] = user_ext.model_dump()
            return True
        except Exception as e:
            session.rollback()
            logger.error(f"Failed to add user ext info for user_id {user_ext.user_id}: {str(e)}")
            return False
    
def update_user_ext(user_ext: UserExt, id: int) -> bool:
    with session_scope() as session:
        try:
            # 使用 session.get() 获取与当前会话绑定的实例
            db_user_ext = session.get(UserExt, id)
            if not db_user_ext:
                logger.warning(f"User ext info not found for update with id: {id}")
                return False

            # 从传入的 user_ext 对象中获取要更新的数据
            update_data = user_ext.model_dump(exclude_unset=True)
            for key, value in update_data.items():
                if key != 'id':  # 不更新主键
                    setattr(db_user_ext, key, value)
        
            session.commit()
            session.refresh(db_user_ext)  # 刷新实例以获取最新状态
            # 缓存序列化数据
            # user_ext_cache[db_user_ext.user_id] = user_ext.model_dump()
            return True
        except Exception as e:
            session.rollback()
            logger.error(f"Failed to update user ext info for id {id}: {str(e)}")
            return False

def select_user_ext_by_user_id(user_id: int) -> Optional[UserExt]:
    with session_scope() as session:
        statement = select(UserExt).where(UserExt.user_id == user_id)
        user_ext_db = session.exec(statement).first()
        if not user_ext_db:
            return None
        user_data = user_ext_db.model_dump()
        return UserExt(**user_data)

def get_max_user_linux_gid(session: Optional[Session] = None) -> int:
    """获取用户扩展表中最大的Linux GID"""
    with session_scope() as db:
        result = db.exec(
            select(UserExt.linux_gid)
            .where(UserExt.linux_gid.isnot(None))
            .order_by(UserExt.linux_gid.desc())
        ).first()
        if result:
            return result
        else:
            return 40000
    
def _select_by_user_id_ext(session: Session, user_id: int) -> Optional[UserExt]:
    statement = select(UserExt).where(UserExt.user_id == user_id)
    return session.exec(statement).first()

def select_user_info_by_id(session: Session, user_id: int) -> Optional[UserInfo]:
    statement = select(UserInfo).where(UserInfo.id == user_id)
    return session.exec(statement).first()

# ==================== 用户角色关联操作 ====================

def select_user_roles(user_id: int) -> List[UserRole]:
    """
    查询用户的所有角色关联
    """
 
    with session_scope() as session:
        statement = select(UserRole).where(UserRole.user_id == user_id)
        user_roles_db = session.exec(statement).all()
        
        # 缓存序列化后的数据
        roles_data = [role.model_dump() for role in user_roles_db]
        
        return [UserRole(**data) for data in roles_data]

# ==================== 复合查询操作 ====================

def get_user_roles_with_details(user_id: int) -> List[dict]:
    """
    获取用户的角色详细信息（包含角色名称、编码等）
    """
    with session_scope() as session:
        statement = select(UserRole, Role).join(Role, UserRole.role_id == Role.id).where(UserRole.user_id == user_id)
        results = session.exec(statement).all()
        
        return [
            {
                "user_role_id": user_role.id,
                "user_id": user_role.user_id,
                "role_id": role.id,
                "role_name": role.role_name,
                "role_code": role.role_code,
                "role_comments": role.comments
            }
            for user_role, role in results
        ]
user_priv_cache = TTLCache(maxsize=1000, ttl=60 * 60 * 24)
user_priv_service_cache = TTLCache(maxsize=1000, ttl=60 * 60 * 24)
user_priv_component_cache = TTLCache(maxsize=1000, ttl=60 * 60 * 24)

from api.model.priv import Priv, PrivState

def get_user_all_privileges(user_id: int) -> dict:
    """
    查询用户所有关联角色下的权限数据，三表关联：ai_dw_user_role, ai_dw_role_priv, ai_dw_priv
    返回格式：
    {
        "service": {role_id: [priv_dict, ...], ...},
        "component": {role_id: [priv_dict, ...], ...}
    }
    """
    # if user_id in user_priv_service_cache and user_id in user_priv_component_cache:
        # return {
            # "service": user_priv_service_cache[user_id],
            # "component": user_priv_component_cache[user_id]
        # }
    with session_scope() as session:
        ur = UserRole
        rp = RolePriv
        p = Priv
        stmt = (
            select(ur.role_id, p)
            .select_from(ur)
            .outerjoin(rp, ur.role_id == rp.role_id)
            .outerjoin(p, (rp.priv_id == p.id) & (p.state == PrivState.VALID.value))
            .where(ur.user_id == user_id)
        )
        rows = session.exec(stmt).all()
        service_result = {}
        component_result = {}
        for role_id, priv in rows:
            if priv is not None:
                priv_dict = priv.model_dump()
                priv_type = priv_dict.get("priv_type")
                if priv_type == PrivType.SERVICE.value:
                    service_result.setdefault(role_id, []).append(priv_dict)
                elif priv_type == PrivType.COMPONENT.value:
                    component_result.setdefault(role_id, []).append(priv_dict)
            else:
                # 角色无权限时，确保空分组
                service_result.setdefault(role_id, [])
                component_result.setdefault(role_id, [])
        # user_priv_service_cache[user_id] = service_result
        # user_priv_component_cache[user_id] = component_result
        return {
            "service": service_result,
            "component": component_result
        }

def clear_user_priv_cache(user_id: int) -> bool:
    """
    清除用户的权限缓存
    """
    user_priv_service_cache.pop(user_id, None)
    user_priv_component_cache.pop(user_id, None)
    return True


def select_users_for_grant(session: Session, wiki_id: str, key_word: str,filter: str, page_num: int, page_size: int) -> any:
    """
      查询可授权的用户列表
    """
    # 1. 多字段模糊查询条件
    where_clause = []
    if key_word:
        # 同时匹配 name 和 code 字段（不区分大小写）
        where_clause.append(
            or_(
                UserInfo.user_name.ilike(f"%{key_word}%"),  # ilike 不区分大小写
                UserInfo.user_code.ilike(f"%{key_word}%")
            )
        )

    current_user_info = get_current_user()
    user_id = 1
    if current_user_info:
        user_id = current_user_info.get("id")

    # 2. 关联表查询逻辑
    statement = select(UserInfo).select_from(UserInfo)
    if filter and filter == 'T':  # 如果传入了 wiki_id，则关联 WikiUserRole 表
        subquery = select(WikiUserRole.user_id).where(WikiUserRole.wiki_id == wiki_id).scalar_subquery()
        # 主查询：筛选出不在子查询结果中的用户
        statement = statement.where(not_(UserInfo.id.in_(subquery)))
    else:
        # 使用新的 ai_dw_wiki_info，通过 wiki_id 字段匹配
        subquery = select(WikiInfo.created_by).where(WikiInfo.wiki_id == wiki_id).scalar_subquery()
        statement = statement.where(not_(UserInfo.id.in_(subquery)))

    # 添加模糊查询条件
    if where_clause:
        statement = statement.where(*where_clause)

    # 分页逻辑
    count_sql = select(func.count()).select_from(statement)  # 计算总数
    if page_num is not None and page_size is not None:
        statement = statement.offset(page_size * (page_num - 1)).limit(page_size)
    # 执行查询
    total_count = session.scalar(count_sql)
    users = session.exec(statement).all()
    return { "total": total_count, "users": [UserInfo(**user.model_dump()) for user in users] }


def list_users_with_quota(keyword: str | None = None, page_num: int | None = None, page_size: int | None = None) -> dict:
    """
    列出用户及其配额（来自 ai_dw_user_ext.sandbox_quota）。
    返回: { total, users: [ { id, user_code, user_name, sandbox_quota } ] }
    """
    with session_scope() as session:
        # 左连接 UserExt 以便取出 sandbox_quota
        stmt = (
            select(UserInfo, UserExt)
            .select_from(UserInfo)
            .join(UserExt, UserExt.user_id == UserInfo.id, isouter=True)
        )
        if keyword:
            kw = f"%{keyword}%"
            stmt = stmt.where(or_(UserInfo.user_name.ilike(kw), UserInfo.user_code.ilike(kw)))

        # 统计总数（仅按 UserInfo 统计）
        count_stmt = select(func.count()).select_from(
            select(UserInfo.id)
            .where(or_(UserInfo.user_name.ilike(f"%{keyword}%"), UserInfo.user_code.ilike(f"%{keyword}%"))) if keyword else select(UserInfo.id)
        )
        total = session.scalar(count_stmt)

        if page_num is not None and page_size is not None and page_num > 0 and page_size > 0:
            stmt = stmt.offset(page_size * (page_num - 1)).limit(page_size)

        rows = session.exec(stmt).all()
        users = []
        for ui, ue in rows:
            users.append({
                "id": ui.id,
                "user_code": ui.user_code,
                "user_name": ui.user_name,
                "sandbox_quota": getattr(ue, "sandbox_quota", None) if ue else None,
            })
        return {"total": total or 0, "users": users}


def set_user_sandbox_quota_by_user_code(user_code: str, quota: int | None) -> bool:
    """
    设置（或清空）指定用户的个性化沙盒并发配额。
    quota 为 None 表示清空个性化配置，使用系统默认。
    """
    with session_scope() as session:
        # 找到用户
        ui = session.exec(select(UserInfo).where(UserInfo.user_code == user_code)).first()
        if not ui:
            logger.warning(f"User not found for code: {user_code}")
            return False

        ue = session.exec(select(UserExt).where(UserExt.user_id == ui.id)).first()
        if ue:
            # 更新已有记录
            ue.sandbox_quota = int(quota) if quota is not None else None
            session.add(ue)
        else:
            # 创建新记录
            ue = UserExt(user_id=ui.id, sandbox_quota=int(quota) if quota is not None else None)
            session.add(ue)

        try:
            session.commit()
            return True
        except Exception as e:
            session.rollback()
            logger.error(f"Failed to set sandbox_quota for user_code {user_code}: {e}")
            return False


def get_max_user_linux_gid() -> int:
    with session_scope() as db:
        result = db.exec(
            select(UserExt.linux_gid)
            .where(UserExt.linux_gid.isnot(None))
            .order_by(UserExt.linux_gid.desc())
        ).first()
        if result and result[0]:
            return result[0]
        else:
            return 50000
