import logging
from contextlib import contextmanager
from typing import List, Optional

from sqlmodel import Session, select

from api.database.base import session_scope
from api.model.wiki_repository_relation import AiDwWikiRepositoryRelation


logger = logging.getLogger(__name__)


@contextmanager
def _managed_session(session: Optional[Session]):
    if session is not None:
        yield session
        return
    with session_scope() as new_session:
        yield new_session


def list_relations(
    wiki_id: str,
    *,
    only_main: bool = False,
    session: Optional[Session] = None,
) -> List[AiDwWikiRepositoryRelation]:
    with _managed_session(session) as db:
        stmt = select(AiDwWikiRepositoryRelation).where(AiDwWikiRepositoryRelation.wiki_id == wiki_id)
        if only_main:
            stmt = stmt.where(AiDwWikiRepositoryRelation.is_main_repo == 1)  # noqa: E712
        stmt = stmt.order_by(AiDwWikiRepositoryRelation.id.asc())
        rows = db.exec(stmt).all()
        return [AiDwWikiRepositoryRelation(**row.model_dump()) for row in rows]

def list_relations_by_primary_key(
    id: int,
    *,
    only_main: bool = False,
    session: Optional[Session] = None,
) -> List[AiDwWikiRepositoryRelation]:
    with _managed_session(session) as db:
        stmt = select(AiDwWikiRepositoryRelation).where(AiDwWikiRepositoryRelation.id == id)
        if only_main:
            stmt = stmt.where(AiDwWikiRepositoryRelation.is_main_repo == True)  # noqa: E712
        stmt = stmt.order_by(AiDwWikiRepositoryRelation.id.asc())
        rows = db.exec(stmt).all()
        return [AiDwWikiRepositoryRelation(**row.model_dump()) for row in rows]


def add_relation(
    wiki_id: str,
    repo_id: int,
    *,
    is_main_repo: bool = False,
    excluded_dirs: Optional[str] = None,
    excluded_files: Optional[str] = None,
    included_dirs: Optional[str] = None,
    included_files: Optional[str] = None,
    session: Optional[Session] = None,
) -> AiDwWikiRepositoryRelation:
    with _managed_session(session) as db:
        existing = db.exec(
            select(AiDwWikiRepositoryRelation).where(
                AiDwWikiRepositoryRelation.wiki_id == wiki_id,
                AiDwWikiRepositoryRelation.repository_id == repo_id,
            )
        ).first()
        if existing:
            changed = False
            if existing.is_main_repo != is_main_repo:
                existing.is_main_repo = is_main_repo
                changed = True
            for attr, value in {
                "excluded_dirs": excluded_dirs,
                "excluded_files": excluded_files,
                "included_dirs": included_dirs,
                "included_files": included_files,
            }.items():
                if value is not None and getattr(existing, attr) != value:
                    setattr(existing, attr, value)
                    changed = True
            if changed:
                db.add(existing)
                db.flush()
            return AiDwWikiRepositoryRelation(**existing.model_dump())

        relation = AiDwWikiRepositoryRelation(
            wiki_id=wiki_id,
            repository_id=repo_id,
            is_main_repo=is_main_repo,
            excluded_dirs=excluded_dirs,
            excluded_files=excluded_files,
            included_dirs=included_dirs,
            included_files=included_files,
        )
        db.add(relation)
        db.flush()
        return AiDwWikiRepositoryRelation(**relation.model_dump())


def delete_relation(wiki_id: str, repo_id: int, *, session: Optional[Session] = None) -> bool:
    with _managed_session(session) as db:
        item = db.exec(
            select(AiDwWikiRepositoryRelation).where(
                AiDwWikiRepositoryRelation.wiki_id == wiki_id,
                AiDwWikiRepositoryRelation.repository_id == repo_id,
            )
        ).first()
        if not item:
            return False
        db.delete(item)
        db.flush()
        return True


def update_relation(
    wiki_id: str, 
    repository_id: int, 
    *, 
    session: Optional[Session] = None
) -> bool:
    with _managed_session(session) as db:
        item = db.exec(
            select(AiDwWikiRepositoryRelation).where(
                AiDwWikiRepositoryRelation.wiki_id == wiki_id,
                AiDwWikiRepositoryRelation.repository_id == repository_id,
            )
        ).first()
        if not item:
            return False
        
        db.add(item)
        db.flush()
        return True

def get_max_linux_gid(session: Optional[Session] = None) -> int:
    """获取最大的 Linux GID，现在从 AiDwGitRepository 表中查询"""
    from api.model.git_repository import AiDwGitRepository
    with _managed_session(session) as db:
        result = db.exec(
            select(AiDwGitRepository.linux_gid)
            .where(AiDwGitRepository.linux_gid.isnot(None))
            .order_by(AiDwGitRepository.linux_gid.desc())
        ).first()
        if result and result[0]:
            return result[0]
        else:
            return 40000