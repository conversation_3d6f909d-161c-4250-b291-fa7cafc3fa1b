import logging
import re
from datetime import datetime
from typing import Optional

from cachetools import TTL<PERSON>ache
from sqlalchemy import select
from sqlalchemy.orm import Session
from sqlmodel import select

from api.database.base import session_scope
from api.model.chat_session import ChatSession

# 创建日志记录器
logger = logging.getLogger(__name__)

# 会话信息缓存，key 为 chat_sid，value 为 {"id": ..., "created_by": ..., "wiki_id": ...}
chat_session_info_cache = TTLCache(maxsize=1000, ttl=60 * 60 * 24)

def generate_session_title(first_message: str) -> str:
    """
    从第一条消息生成会话标题
    """
    if not first_message:
        logger.info("No first message provided, using default title")
        return "新会话"
    
    logger.info(f"Generating title from message: {first_message[:50]}...")
    
    # 去除markdown标记
    message = re.sub(r'[#*`\[\](){}>|!]', '', first_message)
    
    # 去除特殊字符，保留中文、英文、数字和空格
    clean_message = re.sub(r'[^\w\s\u4e00-\u9fff]', '', message)
    
    # 去除多余空格
    clean_message = re.sub(r'\s+', ' ', clean_message).strip()
    
    # 取前30个字符
    title = clean_message[:30].strip()
    
    logger.info(f"Cleaned message: {clean_message[:20]}..., Generated title: {title}")
    
    # 如果为空或太短，使用默认值
    if not title:
        logger.info(f"Generated title too short or empty, using default title")
        return "新会话"
    
    return title


def select_chat_session_by_chat_sid(chat_sid: str) -> Optional[dict]:
    """
    通过会话ID查询会话，优先从缓存获取（key为chat_sid，value为{id, created_by, wiki_id}）
    """
    # 优先从缓存获取完整信息
    info_in_cache = chat_session_info_cache.get(chat_sid)
    if info_in_cache is not None:
        logger.info(f"Cache hit for chat_sid: {chat_sid}, info: {info_in_cache}")
        return info_in_cache

    # 缓存未命中，查数据库
    with session_scope() as session:
        chat_session_db = _select_by_chat_sid(session, chat_sid)
        if chat_session_db is None:
            logger.warning(f"Chat session not found for chat_sid: {chat_sid}")
            return None

        # 写入缓存
        info = {
            "id": chat_session_db.id,
            "created_by": chat_session_db.created_by,
            "wiki_id": chat_session_db.wiki_id
        }
        chat_session_info_cache[chat_sid] = info
        logger.info(f"Cache set for chat_sid: {chat_sid}, info: {info}")
        return info


def select_chat_sessions_by_user_and_wiki(user_id: int, wiki_id: str, page_num: int = 1, page_size: int = 10, search_keyword: str = None):
    """
    通过用户ID和wiki ID分页查询会话，支持搜索，返回(当前页会话列表, 总数)
    """
    # 首先通过wiki_id查询wiki的主键ID
    from api.model.wiki_info import WikiInfo
    from api.database.base import session_scope
    from sqlalchemy import or_
    
    wiki_primary_id = None
    with session_scope() as session:
        wiki_info = session.exec(select(WikiInfo.id).where(WikiInfo.wiki_id == wiki_id)).first()
        if wiki_info:
            wiki_primary_id = wiki_info
    
    if not wiki_primary_id:
        logger.warning(f"Wiki not found for wiki_id: {wiki_id}")
        return [], 0

    # 使用wiki主键ID查询会话
    with session_scope() as session:
        # 构建基础查询条件
        base_filter = [
            ChatSession.wiki_id == wiki_primary_id,
            ChatSession.created_by == user_id, # 添加用户ID过滤
            ChatSession.state != 0 # 添加状态过滤，排除已删除的会话
        ]
        
        # 如果有搜索关键字，添加搜索条件
        if search_keyword and search_keyword.strip():
            search_term = f"%{search_keyword.strip()}%"
            base_filter.append(
                ChatSession.title.ilike(search_term)
            )
        
        # 查询总数（只查询未删除的会话）
        total = session.query(ChatSession).filter(*base_filter).count()
        
        # 分页查询（只查询未删除的会话）
        offset = (page_num - 1) * page_size
        chat_sessions_db = session.query(ChatSession).filter(*base_filter).order_by(ChatSession.created_date.desc()).offset(offset).limit(page_size).all()
        
        if not chat_sessions_db:
            logger.warning(f"Chat sessions not found for user_id: {user_id}, wiki_id: {wiki_id}, search_keyword: {search_keyword}")
            return [], total
            
        return [ChatSession(**chat.model_dump()) for chat in chat_sessions_db], total


def add_chat_session(chat_session: ChatSession, first_message: str = None) -> Optional[dict]:
    """
    添加会话，支持自动生成标题
    """
    with session_scope() as session:
        try:
            # 设置创建时间
            chat_session.created_date = datetime.now()
            
            # 如果标题为空且有第一条消息，自动生成标题
            if (not chat_session.title or chat_session.title.strip() == "") and first_message:
                chat_session.title = generate_session_title(first_message)
                logger.info(f"Generated session title: {chat_session.title} from message: {first_message[:20]}...")
            
            # 如果仍然没有标题，使用默认标题
            if not chat_session.title or chat_session.title.strip() == "":
                chat_session.title = "新会话"
            
            session.add(chat_session)
            
            # 提交事务（包括created_by的更新）
            session.commit()
            
            # 重新查询数据库以获取更新后的对象，避免refresh操作
            # 因为在commit后，对象可能已经与session分离
            refreshed_chat_session = session.get(ChatSession, chat_session.id)

            # 新增：将 chat_sid 与 id 的关系存储到本地缓存
            if refreshed_chat_session and refreshed_chat_session.chat_sid and refreshed_chat_session.id:
                # 将 id, created_by, wiki_id 以 chat_sid 为 key 存储到本地缓存
                chat_session_info_cache[refreshed_chat_session.chat_sid] = {
                    "id": refreshed_chat_session.id,
                    "created_by": refreshed_chat_session.created_by,
                    "wiki_id": refreshed_chat_session.wiki_id
                }
                logger.info(f"Cached chat_session info: {chat_session_info_cache[refreshed_chat_session.chat_sid]}")

            return chat_session_info_cache[refreshed_chat_session.chat_sid]
        except Exception as e:
            session.rollback()
            logger.error(f"Failed to add chat session for user_id {chat_session.created_by}: {str(e)}")
            raise e

def update_chat_session(chat_sid: str, title: str, user_id: int) -> bool:
    """
    根据 chat_sid 更新会话标题（以及 update_by, update_date）
    """
    with session_scope() as session:
        try:
            statement = select(ChatSession).where(ChatSession.chat_sid == chat_sid, ChatSession.state != 0)
            chat_session = session.exec(statement).first()
            if not chat_session:
                logger.warning(f"Chat session not found for update with chat_sid: {chat_sid}")
                return False

            chat_session.title = title
            chat_session.update_by = user_id
            chat_session.update_date = datetime.now()
            session.commit()
            return True
        except Exception as e:
            logger.error(f"Failed to update chat session for chat_sid {chat_sid}: {str(e)}")
            raise e

def delete_chat_session_by_sid(chat_sid: str, user_id: int) -> bool:
    """
    根据 chat_sid 逻辑删除会话（state=0）
    """
    with session_scope() as session:
        try:
            statement = select(ChatSession).where(ChatSession.chat_sid == chat_sid, ChatSession.state != 0)
            chat_session = session.exec(statement).first()
            if not chat_session:
                logger.warning(f"Chat session not found for delete with chat_sid: {chat_sid}")
                return False
            chat_session.state = 0
            chat_session.update_by = user_id
            chat_session.update_date = datetime.now()
            session.commit()
            return True
        except Exception as e:
            logger.error(f"Failed to delete chat session for chat_sid {chat_sid}: {str(e)}")
            raise e

# ==================== 私有方法 ====================

def _select_by_chat_sid(session: Session, chat_sid: str) -> Optional[ChatSession]:
    """
    通过会话ID查询会话（私有方法）
    """
    statement = select(ChatSession).where(ChatSession.chat_sid == chat_sid, ChatSession.state != 0)
    return session.exec(statement).first()
