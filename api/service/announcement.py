import logging
from typing import List, Optional
from sqlmodel import select, Session

from api.database.base import session_scope
from api.model.announcement import Announcement

# 创建日志记录器
logger = logging.getLogger(__name__)


def get_active_announcements() -> List[Announcement]:
    """
    获取所有上架的公告，按seq排序
    """
    with session_scope() as session:
        try:
            statement = select(Announcement).where(
                Announcement.state == 1
            ).order_by(Announcement.seq)
            announcements = session.exec(statement).all()
            logger.info(f"Retrieved {len(announcements)} active announcements")
            return announcements
        except Exception as e:
            logger.error(f"Error retrieving active announcements: {str(e)}")
            return []


def get_announcements_by_type(announcement_type: str) -> List[Announcement]:
    """
    根据类型获取上架的公告
    """
    with session_scope() as session:
        try:
            statement = select(Announcement).where(
                Announcement.state == 1,
                Announcement.type == announcement_type
            ).order_by(Announcement.seq)
            announcements = session.exec(statement).all()
            logger.info(f"Retrieved {len(announcements)} active announcements of type '{announcement_type}'")
            return announcements
        except Exception as e:
            logger.error(f"Error retrieving announcements by type '{announcement_type}': {str(e)}")
            return []


def get_announcement_by_id(announcement_id: int) -> Optional[Announcement]:
    """
    根据ID获取公告
    """
    with session_scope() as session:
        try:
            announcement = session.get(Announcement, announcement_id)
            if announcement:
                logger.info(f"Retrieved announcement with ID: {announcement_id}")
            else:
                logger.warning(f"Announcement not found with ID: {announcement_id}")
            return announcement
        except Exception as e:
            logger.error(f"Error retrieving announcement with ID {announcement_id}: {str(e)}")
            return None


def get_all_announcements() -> List[Announcement]:
    """
    获取所有公告（包括下架的），按seq排序
    """
    with session_scope() as session:
        try:
            statement = select(Announcement).order_by(Announcement.seq)
            announcements = session.exec(statement).all()
            logger.info(f"Retrieved {len(announcements)} total announcements")
            return announcements
        except Exception as e:
            logger.error(f"Error retrieving all announcements: {str(e)}")
            return []


def create_announcement(announcement: Announcement) -> Optional[Announcement]:
    """
    创建新公告
    """
    with session_scope() as session:
        try:
            session.add(announcement)
            session.commit()
            session.refresh(announcement)
            logger.info(f"Created announcement with ID: {announcement.id}")
            return announcement
        except Exception as e:
            logger.error(f"Error creating announcement: {str(e)}")
            return None


def update_announcement(announcement: Announcement, announcement_id: int) -> bool:
    """
    更新公告
    """
    with session_scope() as session:
        try:
            db_announcement = session.get(Announcement, announcement_id)
            if not db_announcement:
                logger.warning(f"Announcement not found for update with ID: {announcement_id}")
                return False

            # 从传入的 announcement 对象中获取要更新的数据
            update_data = announcement.model_dump(exclude_unset=True)
            for key, value in update_data.items():
                if key != 'id':  # 不更新主键
                    setattr(db_announcement, key, value)

            session.commit()
            session.refresh(db_announcement)
            logger.info(f"Updated announcement with ID: {announcement_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating announcement with ID {announcement_id}: {str(e)}")
            return False


def delete_announcement(announcement_id: int) -> bool:
    """
    删除公告（软删除，将状态设置为下架）
    """
    with session_scope() as session:
        try:
            db_announcement = session.get(Announcement, announcement_id)
            if not db_announcement:
                logger.warning(f"Announcement not found for deletion with ID: {announcement_id}")
                return False

            db_announcement.state = 2  # 设置为下架状态
            session.commit()
            logger.info(f"Soft deleted announcement with ID: {announcement_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting announcement with ID {announcement_id}: {str(e)}")
            return False 