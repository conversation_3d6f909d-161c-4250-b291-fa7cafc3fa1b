from typing import Optional, Dict, Any
from sqlmodel import Session, select, desc
from api.model.wiki_job import Wiki<PERSON>ob
from datetime import datetime
# from api.middleware.auth_middleware import get_current_user

import logging
import uuid
import logging

logger = logging.getLogger(__name__)

def create_job(db: Session, repo_url: str, branch: str, wiki_info_id: str, model_settings: Dict[str, Any], sub_repos_str: Optional[str] = None, token: Optional[str] = None, created_by: Optional[int] = None, excluded_dirs: Optional[str] = None, excluded_files: Optional[str] = None, included_dirs: Optional[str] = None, included_files: Optional[str] = None, job_type: int = 0, sync_strategy: Optional[str] = None) -> WikiJob:
    """
    在数据库中创建一个新的Wiki生成任务
    """
    # 从 repo_url 提取 repo_owner 和 repo_name
    try:
        # 使用传入的用户ID，如果没有则使用默认用户ID
        user_id = created_by if created_by is not None else 0
                
        # 创建新任务
        new_job = WikiJob(
            repo_url=repo_url,
            branch=branch,
            sub_repos=sub_repos_str,
            wiki_info_id=wiki_info_id,
            token=token,
            status="pending",
            job_type=job_type,  # 0表示生成任务, 1表示刷新任务, 2表示同步索引任务
            sync_strategy=sync_strategy,
            progress=0,
            model_settings=model_settings,
            excluded_dirs=excluded_dirs,
            excluded_files=excluded_files,
            included_dirs=included_dirs,
            included_files=included_files,
            created_by=user_id,
            updated_by=user_id
        )
        
        db.add(new_job)
        db.commit()
        return new_job
    except Exception as e:
        logging.error(f"创建任务失败: {e}")
        raise

def get_job(session, job_id: str):
    """获取任务信息"""
    from api.model.wiki_job import WikiJob
    return session.query(WikiJob).filter(WikiJob.id == job_id).first()

def get_latest_successful_job(session: Session, repo_url: str) -> Optional[WikiJob]:
    """
    Retrieves the latest successfully completed job for a given repository.
    """
    statement = select(WikiJob).where(
        WikiJob.repo_url == repo_url,
        WikiJob.status == "completed"
    ).order_by(desc(WikiJob.updated_time))
    job = session.exec(statement).first()
    return job

def get_active_jobs(session: Session) -> list[WikiJob]:
    """
    Retrieves all active (pending or processing) jobs.
    """
    statement = select(WikiJob).where(WikiJob.status.in_(["pending", "processing"]))
    jobs = session.exec(statement).all()
    return jobs

def get_active_jobs_by_wiki_id(session: Session, wiki_info_id: str) -> Optional[WikiJob]:
    """
    获取指定wiki_info的可接管job（除processing状态外的所有job）
    
    参数:
        session: 数据库会话
        wiki_info_id: Wiki信息ID
        
    返回:
        Optional[WikiJob]: 如果找到可接管job则返回，否则返回None
    """
    statement = select(WikiJob).where(
        WikiJob.wiki_info_id == wiki_info_id,
        WikiJob.status == "processing"
    ).order_by(WikiJob.created_time.desc())
    
    return session.exec(statement).first()

def update_job(session: Session, job_id: str, **kwargs) -> Optional[WikiJob]:
    """更新 wiki 生成任务状态"""
    try:
        job = session.query(WikiJob).filter(WikiJob.id == job_id).first()
        if not job:
            logger.warning(f"Job {job_id} not found")
            return None
        
        # 更新提供的字段
        for key, value in kwargs.items():
            if hasattr(job, key):
                setattr(job, key, value)
        
        # 使用默认用户ID
        user_id = 0
        
        # 更新更新人字段
        job.updated_by = user_id  # 使用当前用户 ID
        
        session.commit()
        logger.debug(f"Successfully updated job {job_id} with fields: {list(kwargs.keys())}")
        return job
    except Exception as e:
        session.rollback()
        logger.error(f"Failed to update job {job_id}: {str(e)}")
        raise e

def retry_job(session, job_id: str):
    """
    重试失败的任务，基于已记录的阶段进行智能重试
    
    参数:
        session: 数据库会话
        job_id: 任务ID
    
    返回:
        dict: 包含操作结果和重试信息
    """
    from api.model.wiki_job import WikiJob
    try:
        job = session.query(WikiJob).filter(WikiJob.id == job_id).first()
        if not job:
            return {"success": False, "message": "任务不存在"}
            
        # 仅允许重试失败的任务
        if job.status != "failed":
            return {"success": False, "message": "只能重试失败的任务"}
        
        # 根据当前阶段决定重试策略
        resume = False
        if job.stage in ["upload", "generate"]:
            # 上传和生成阶段可以继续，不需要重新下载
            resume = True
            new_status = "pending_resume"
            stage_message = f"将从{job.stage}阶段继续"
        else:
            # 下载阶段或其他阶段需要重新开始
            new_status = "pending"
            stage_message = "任务将重新开始"
        
        # 更新任务状态
        job.status = new_status
        job.error_message = None
        job.progress = job.progress if resume else 0
        job.stage = job.stage if resume else "pending"
        job.stage_progress = job.stage_progress if resume else 0
        job.stage_message = stage_message
        job.updated_time = datetime.now()
        
        session.commit()
        return {
            "success": True, 
            "resume": resume, 
            "stage": job.stage,
            "message": stage_message
        }
    except Exception as e:
        session.rollback()
        logging.error(f"重试任务失败: {e}")
        return {"success": False, "message": str(e)} 