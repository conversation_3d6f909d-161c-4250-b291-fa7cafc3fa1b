import logging
from contextlib import contextmanager
from typing import Optional, Dict, Any

from sqlmodel import Session, select

from api.database.base import session_scope
from api.model.wiki_content import AiDwWikiContent


logger = logging.getLogger(__name__)


@contextmanager
def _managed_session(session: Optional[Session]):
    if session is not None:
        yield session
        return
    with session_scope() as new_session:
        yield new_session


def get_by_wiki_id(wiki_id: str, *, session: Optional[Session] = None) -> Optional[AiDwWikiContent]:
    with _managed_session(session) as db:
        item = db.exec(select(AiDwWikiContent).where(AiDwWikiContent.wiki_id == wiki_id)).first()
        return AiDwWikiContent(**item.model_dump()) if item else None


def upsert_structure(
    wiki_id: str,
    structure: Dict[str, Any],
    *,
    session: Optional[Session] = None,
) -> AiDwWikiContent:
    with _managed_session(session) as db:
        item = db.exec(select(AiDwWikiContent).where(AiDwWikiContent.wiki_id == wiki_id)).first()
        if item is None:
            item = AiDwWikiContent(wiki_id=wiki_id, wiki_structure=structure, wiki_pages={}, total_pages=0)
            db.add(item)
            db.flush()
            return AiDwWikiContent(**item.model_dump())
        item.wiki_structure = structure
        db.add(item)
        db.flush()
        return AiDwWikiContent(**item.model_dump())


def upsert_pages(
    wiki_id: str,
    pages: Dict[str, Any],
    *,
    increment: bool = False,
    session: Optional[Session] = None,
) -> AiDwWikiContent:
    with _managed_session(session) as db:
        item = db.exec(select(AiDwWikiContent).where(AiDwWikiContent.wiki_id == wiki_id)).first()
        if item is None:
            item = AiDwWikiContent(
                wiki_id=wiki_id,
                wiki_structure=None,
                wiki_pages=pages or {},
                total_pages=len(pages or {}),
            )
            db.add(item)
            db.flush()
            return AiDwWikiContent(**item.model_dump())

        current = item.wiki_pages or {}
        if increment:
            current.update(pages or {})
            item.wiki_pages = current
        else:
            item.wiki_pages = pages or {}
        item.total_pages = len(item.wiki_pages or {})
        db.add(item)
        db.flush()
        return AiDwWikiContent(**item.model_dump())

