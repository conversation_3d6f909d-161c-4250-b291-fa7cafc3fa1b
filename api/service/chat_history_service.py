import logging
from datetime import datetime
from typing import Optional, List

from cachetools import T<PERSON><PERSON><PERSON>
from sqlalchemy import select
from sqlalchemy.orm import Session
from sqlmodel import select

from api.database.base import session_scope
from api.model.chat_history import ChatHistory

# 创建日志记录器
logger = logging.getLogger(__name__)

def select_chat_history_by_msg_sid(msg_sid: str) -> Optional[ChatHistory]:
    """
    通过消息ID查询聊天历史
    """
    with session_scope() as session:
        chat_history_db = _select_by_msg_sid(session, msg_sid)
        if chat_history_db is None:
            logger.warning(f"Chat history not found for msg_sid: {msg_sid}")
            return None
        
        # 返回一个新的ChatHistory对象
        return ChatHistory(**chat_history_db.model_dump())

def select_chat_histories_by_chat_id(chat_id: int) -> List[ChatHistory]:
    """
    通过会话ID查询所有聊天历史（过滤掉deep_research_iter为'research'的记录）
    """
    with session_scope() as session:
        chat_histories_db = _select_by_chat_id_filtered(session, chat_id)
        if not chat_histories_db:
            logger.warning(f"Chat histories not found for chat_id: {chat_id}")
            return []
        
        # 返回一个新的ChatHistory对象列表
        return [ChatHistory(**chat.model_dump()) for chat in chat_histories_db]

def add_chat_history(chat_history: ChatHistory) -> Optional[dict]:
    """
    添加聊天历史
    """
    with session_scope() as session:
        try:
            # 设置创建时间
            chat_history.created_date = datetime.now()
            session.add(chat_history)
            # 立即执行SQL但不提交，以获取生成的ID
            session.flush()

            # 更新创建人为自己的id（如果created_by为空）
            if chat_history.created_by is None:
                chat_history.created_by = chat_history.id
            
            # 提交事务（包括created_by的更新）
            session.commit()
            
            # 重新查询数据库以获取更新后的对象，避免refresh操作
            # 因为在commit后，对象可能已经与session分离
            refreshed_chat_history = session.get(ChatHistory, chat_history.id)
            return refreshed_chat_history.model_dump()
        except Exception as e:
            session.rollback()
            logger.error(f"Failed to add chat history for chat_id {chat_history.chat_id}: {str(e)}")
            raise e

# ==================== 私有方法 ====================

def _select_by_msg_sid(session: Session, msg_sid: str) -> Optional[ChatHistory]:
    """
    通过消息ID查询聊天历史（私有方法）
    """
    statement = select(ChatHistory).where(ChatHistory.msg_sid == msg_sid, ChatHistory.state == 1)
    return session.exec(statement).first()

def _select_by_chat_id_filtered(session: Session, chat_id: int) -> List[ChatHistory]:
    """
    通过会话ID查询所有聊天历史（私有方法）
    过滤掉deep_research_iter为'research'的记录，保留NULL值的记录
    """
    from sqlalchemy import or_
    statement = select(ChatHistory).where(
        ChatHistory.chat_id == chat_id, 
        ChatHistory.state == 1,
        or_(
            ChatHistory.deep_research_iter != 'research',
            ChatHistory.deep_research_iter.is_(None)
        )
    ).order_by(ChatHistory.created_date.asc())
    return session.exec(statement).all()
