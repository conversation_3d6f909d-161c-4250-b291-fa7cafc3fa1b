import logging
from fastapi.responses import JSONResponse
from api.common.constants import PrivType
from api.service.user_priv_service import get_user_privileges_by_type_with_cache
from api.service.user_role_service import get_user_role_ids, set_user_roles_to_redis
from api.utils.aes_utils import AESUtils
from api.utils.jwt_utils import create_jwt_token
from api.utils.cookie_utils import set_cookie
from api.utils.session_utils import (
    generate_session_id, set_session, SESSION_COOKIE_NAME
)
from api.config import is_redis_enabled, get_session_config

logger = logging.getLogger(__name__)

max_age = get_session_config().get("max-age", -1)
timeout = get_session_config().get("timeout", 3600)


aes = AESUtils()

def login_with_session(token_user_info):
    """
    session模式登录，写入redis并设置cookie
    """
    session_id = generate_session_id()
    set_session(session_id, token_user_info, expiration=timeout)
    
    # 设置用户角色到缓存中
    _set_user_role_2_redis(token_user_info.get("id"))
        
    res = JSONResponse(
        status_code=200,
        content={"session_id": session_id}
    )
    set_cookie(res, SESSION_COOKIE_NAME, session_id, max_age=max_age)
    set_cookie(res, "user_code", token_user_info.get('user_code'), max_age=max_age)
    return res

def login_with_jwt(token_user_info):
    """
    jwt模式登录，生成token并设置cookie
    """
    token_result = create_jwt_token(token_user_info)
    if token_result['error']:
        logger.error(f"[登录]error occur when create jwt token: {token_user_info}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Internal Error: {token_result['errorMsg']}"}
        )
    
    # 设置用户角色到缓存中
    _set_user_role_2_redis(token_user_info.get("id"))
        
    res = JSONResponse(
        status_code=200,
        content={"token": token_result['token']}
    )

    set_cookie(res, "token", token_result['token'], max_age=max_age)
    set_cookie(res, "user_code", token_user_info.get('user_code'), max_age=max_age)
    return res

def is_logged_by_jwt(request):
    token = request.cookies.get("token")
    if token:
        from api.utils.jwt_utils import verify_jwt_token
        decoded = verify_jwt_token(token)
        if decoded['error']:
            return {"is_login": False, "token_expired": True, "error": decoded['errorMsg']}
        user_id = decoded['payload'].get('wcp_user_id') or decoded['payload'].get('user_id')
        return _get_user_login_info(user_id)
    else:
        return {"is_login": False, "token_expired": True, "error": "Invalid Token"}

def is_logged_by_session(request):
    from api.utils.session_utils import get_session, get_request_session_id
    session_id = get_request_session_id(request)
    if not session_id:
        return {"is_login": False, "error": "No session_id"}
    user_info = get_session(session_id)
    if not user_info:
        return {"is_login": False, "error": "Session expired or invalid"}
    wcp_user_id = user_info.get("wcp_user_id")
    return _get_user_login_info(wcp_user_id)


def _get_user_login_info(wcp_user_id):
    try:
        from api.service.user_service import select_user_info, get_user_roles_with_details
        import base64
        user_info = select_user_info(wcp_user_id)
        user_roles = get_user_roles_with_details(user_info.id)
        role_ids = list(set([r["role_id"] for r in user_roles])) if user_roles else []

        component_privs = get_user_privileges_by_type_with_cache(user_info.id, PrivType.COMPONENT, role_ids=role_ids)
        return {"is_login": True, "user_info": user_info, "error": None, "user_roles": user_roles, "component_privileges": component_privs.get("privileges")}
    except Exception as e:
        logger.error(f"Error getting user ext: {e}")
        return {"is_login": False, "error": "Internal Error"}

def _set_user_role_2_redis(user_id: int):
    if is_redis_enabled():
        # 设置用户角色到缓存中
        role_ids = get_user_role_ids(user_id=user_id)
        if role_ids:
            set_user_roles_to_redis(user_id=user_id, role_ids=role_ids)