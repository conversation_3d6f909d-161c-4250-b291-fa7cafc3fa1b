from datetime import datetime
import logging
import time
from fastapi import Request
from typing import List, Optional

from fastapi.responses import JSONResponse
from sqlalchemy import and_
from sqlmodel import select
from api.database.base import session_scope
from api.model.access_token import AppAccessToken
from api.model.api_def import ApiDef
from api.model.app import App
from api.model.app_api_rel import AppApiRel
from api.model.app_token_user_rel import AppTokenUserRel
from api.model.user_info import UserInfo
from starlette.middleware.base import BaseHTTPMiddleware
from api.config import get_developMode
from api.utils.aes_utils import AESUtils

logger = logging.getLogger(__name__)

developMode = get_developMode()

aes = AESUtils()

class AppAuthMiddleware(BaseHTTPMiddleware):
    def __init__(self, app):
        super().__init__(app)
    
    def _auth_failed(self, err_code: str = "", err_msg: str = "", status_code: int = 401):
        return JSONResponse(
            status_code=status_code,
            content={"success": False, "errCode": err_code, "errMsg": err_msg}
        )

    async def dispatch(self, request: Request, call_next):
        # 如果启用了开发者模式，跳过认证
        if developMode:
            return await call_next(request)
        
        now = datetime.now()
        # 用"auth_successful"来表示校验是否通过,后面的认证中间件通过这个字段判断是否跳过校验,默认校验不通过
        request.state.auth_successful = False
        try:
            auth_header = request.headers.get("X-App-Token", "")
            if not auth_header:
                # 没有找到 X-App-Token: <token> 这样的头,往下流转
                return await call_next(request)
            request.state.request_auth_way = 'app_token'
            # 提取token
            app_token = auth_header
            app_token = aes.encrypt_fixed(app_token)
            with session_scope() as session:
                token: AppAccessToken = session.exec(select(AppAccessToken).where(AppAccessToken.token == app_token, AppAccessToken.state == True)).first()
                # 在数据库里找不到有效的token,校验不通过
                if token is None:
                    return self._auth_failed("AUTH_0001", "Invalid token.")
                
                # 设置app_id到request状态里，后面有些接口需要用到
                request.state.app_id = token.app_id

                # token未到生效日期,校验不通过
                effective_at = token.effective_at if token.effective_at is not None else token.created_date
                if effective_at > now:
                    return self._auth_failed("AUTH_0002", "The token has not reached its effective date.")

                # token过期,校验不通过
                if token.expires_at is not None and token.expires_at < now:
                    return self._auth_failed("AUTH_0003", "The token has expired.")

                # 根据token里的app_id、请求接口的路径和方法在数据库找到应用绑定的接口
                api = session.exec(select(ApiDef)
                        .join(AppApiRel, AppApiRel.api_id == ApiDef.id)
                        .join(App, AppApiRel.app_id == App.id)
                        .where(AppApiRel.app_id == token.app_id,
                               AppApiRel.state == True,
                               ApiDef.state == True,
                               App.state == True,
                               ApiDef.api_path == request.url.path,
                               ApiDef.api_method == request.method)).first()
                # 如果没有找到对应的接口,校验不通过
                if api is None:
                    return self._auth_failed("AUTH_0004", "No matching interface found.", 403)

                # 用户级 token (1)：后续中间件需要校验用户权限
                if token.token_type == 1:
                    logger.info("Authorized by app token successfully (system)")
                    request.state.auth_successful = True
                    # 设置跳过服务鉴权的标记，供后续中间件使用
                    request.state.skip_service_priv_check = True
                    request.state.app_token_type = 1
                    return await call_next(request)

                # 系统级 token (2)：无需校验用户信息，直接放行
                if token.token_type == 2:
                    logger.info("Authorized by app token successfully (user)")
                    request.state.auth_successful = True
                    request.state.skip_service_priv_check = True
                    request.state.app_token_type = 2
                    return await call_next(request)
        except Exception as ex:
            logger.error(f"Failed to authorize by app token, cause by: {ex}")
            raise
