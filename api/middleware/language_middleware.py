import logging
from fastapi import Request
from typing import Optional
from contextvars import ContextV<PERSON>
from starlette.middleware.base import BaseHTTPMiddleware
from api.cache.local.i18n_cache import _translations

logger = logging.getLogger(__name__)

# 定义线程上下文变量
language_context: ContextVar[Optional[str]] = ContextVar('language_context', default=None)

def get_current_language() -> Optional[str]:
    return language_context.get()

def get_translation(key: str, language: Optional[str] = None) -> str:
    """
    获取翻译内容
    
    Args:
        key: 翻译键，支持点号分隔的嵌套键，如 'sandbox.status.description.READY'
        language: 语言代码，如果为None则使用当前上下文语言
    
    Returns:
        翻译后的文本，如果找不到则返回键本身
    """
    if language is None:
        language = get_current_language()
    
    if language not in _translations:
        language = 'zh'
    
    # 按点号分割键
    keys = key.split('.')
    value = _translations[language]
    
    try:
        for k in keys:
            value = value[k]
        return str(value)
    except (KeyError, TypeError):
        logger.warning(f"未找到翻译键: {key} (语言: {language})")
        return key

class LanguageMiddleware(BaseHTTPMiddleware):
    def __init__(self, app):
        super().__init__(app)
        # 在中间件初始化时加载翻译文件

    async def dispatch(self, request: Request, call_next):
        # 检查当前路径是否需要认证
        logger.debug(f"[认证]访问接口 {request.url}")
        logger.debug(f"language: {request.cookies.get('DEEPWIKI_LOCALE')}")
        language_context.set(request.cookies.get('DEEPWIKI_LOCALE'))
        return await call_next(request)