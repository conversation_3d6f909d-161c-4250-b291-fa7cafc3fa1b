from bdb import effective
import logging
from datetime import datetime
import time
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Dict, Any, Optional
from api.common.constants import AuthMode
from api.database.base import session_scope
from api.middleware.language_middleware import get_translation
from api.model.user_info import UserInfo
from api.utils.cookie_utils import set_cookie
from api.utils.jwt_utils import verify_jwt_token, create_jwt_token
from contextvars import ContextVar
from starlette.middleware.base import BaseHTTPMiddleware
import re
from api.config import get_auth_mode, get_developMode, get_excludeUrl, configs, get_session_config
from api.utils.aes_utils import AESUtils

logger = logging.getLogger(__name__)

# 定义线程上下文变量
user_context: ContextVar[Optional[Dict[str, Any]]] = ContextVar('user_context', default=None)

developMode = get_developMode()

MIN_REFRESH_INTERVAL= configs.get("app", {}).get("security", {}).get("jwt", {}).get("token_min_refresh_interval", 300)
REFRESH_TOKEN = configs.get("app", {}).get("security", {}).get("jwt", {}).get("refresh_token", True)

max_age = get_session_config().get("max-age", -1)
timeout = get_session_config().get("timeout", 3600)

aes = AESUtils()

def get_current_user() -> Optional[Dict[str, Any]]:
    return user_context.get()


class JWTBearer(HTTPBearer):
    def __init__(self, auto_error: bool = True):
        super(JWTBearer, self).__init__(auto_error=auto_error)

    async def __call__(self, request: Request):
        credentials: HTTPAuthorizationCredentials = await super(JWTBearer, self).__call__(request)
        if credentials:
            if not credentials.scheme == "Bearer":
                raise HTTPException(status_code=403, detail=get_translation('auth.invalidScheme'))
            return credentials.credentials
        else:
            raise HTTPException(status_code=403, detail="get_translation('auth.invalidScheme')")


class AuthMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, exempt_paths: dict = {}):
        super().__init__(app)
        # 不需要认证的路径，支持正则表达式
        self.exempt_paths = exempt_paths | get_excludeUrl()
        self.jwt_bearer = JWTBearer()
    
    def _auth_failed(self, err_code: str = "", err_msg: str = "", status_code: int = 401):
        return JSONResponse(
            status_code=status_code,
            content={"success": False, "errCode": err_code, "errMsg": err_msg}
        )

    async def dispatch(self, request: Request, call_next):
        # 如果启用了开发者模式，跳过token认证
        if developMode:
            return await call_next(request)

        # 若已通过App鉴权且为用户级token（2），则直接放行用户认证
        if (
            getattr(request.state, "auth_successful", False)
            and getattr(request.state, "request_auth_way", "") == "app_token"
            and getattr(request.state, "app_token_type", None) == 2
        ):
            return await call_next(request)

        # 若已注入用户信息（会话/JWT或其他中间件），则放行
        if hasattr(request.state, "user"):
            user_context.set(request.state.user)
            return await call_next(request)

        # 检查当前路径是否需要认证
        logger.debug(f"[认证]访问接口 {request.url}")
        if self._is_exempt_path(request):
            return await call_next(request)
            
        # 特殊路径强制使用 JWT 认证
        if request.url.path in ["/api/users/role", "/api/wiki/grant-for-dxp"]:
            return await self._jwt_auth(request, call_next)

        # 根据认证模式分发
        auth_mode = get_auth_mode()
        try:
            if auth_mode == AuthMode.SESSION:
                return await self._session_auth(request, call_next)
            else:
                return await self._jwt_auth(request, call_next)
        except HTTPException as e:
            logger.error(f"[登录认证] Error occur when login, auth_mode={auth_mode}, error info: {e}")
            return self._auth_failed("AUTH_0000", get_translation('auth.authFailed'))
            
    async def _session_auth(self, request: Request, call_next):
        # session认证逻辑
        from api.utils.session_utils import get_session, set_session, get_request_session_id, SESSION_COOKIE_NAME
        session_id = get_request_session_id(request)
        if not session_id:
            logger.error(f"[Session认证] 请求未携带session_id，url:{request.url}")
            return self._auth_failed("AUTH_0007", get_translation('auth.sessionAuthenticationRequired'))
        user_info = get_session(session_id)
        if not user_info:
            logger.error(f"[Session认证] session_id无效或已过期，url:{request.url}")
            return self._auth_failed("AUTH_0008", get_translation('auth.sessionExpiredOrInvalid'))
        # 注入用户上下文
        user_context.set(user_info)
        response = await call_next(request)

        # 使用 Redis 原生过期时间 API 续期（滑动过期）
        from api.utils.session_utils import refresh_session_expire
        refresh_session_expire(session_id, timeout)

        # 同步更新 session_id cookie 的过期时间（如需）
        # set_cookie(response, SESSION_COOKIE_NAME, session_id, max_age=max_age)
        return response

    async def _jwt_auth(self, request: Request, call_next):
        # JWT认证逻辑
        token = request.cookies.get("token")
        if not token:
            logger.error(f"[JWT认证] 请求未携带token，url:{request.url}")
            return self._auth_failed("AUTH_0009", get_translation('auth.authenticationRequired'))
        payload = verify_jwt_token(token)
        if payload['error']:
            logger.error(f"[JWT认证] 请求解析token报错，url:{request.url}，报错信息：{payload['errorMsg']}")
            return self._auth_failed("AUTH_0010", payload['errorMsg'])
            
        # 滑动过期：每次访问都检查是否需要延长过期时间
        new_token = None
        if REFRESH_TOKEN and self._should_slide_expiry(payload['payload'], time.time()):
            user_payload = {k: v for k, v in payload['payload'].items() if k not in ['exp', 'iat', 'sub']}
            token_result = create_jwt_token(user_payload)
            if token_result['error']:
                logger.error(f"[JWT认证] Failed to slide token expiry: {payload['payload']} - {token_result['errorMsg']}")
            else:
                new_token = token_result['token']
        # 将用户信息设置到上下文变量中
        user_context.set(payload['payload'])
        response = await call_next(request)
        if new_token:
            set_cookie(
                response=response,
                name="token",
                value=new_token,
                max_age=max_age,
            )
            response.headers["X-Token-Refreshed"] = "true"
        return response

    def _is_exempt_path(self, request: Request) -> bool:
        """检查路径是否在排除路径列表中"""
        path = request.url.path
        method = request.method

        # logout为了记录审计日志时获取用户信息进行特殊处理
        if self._is_logout(path, method):
            return False
        
        for pattern, methods in self.exempt_paths.items():
            try:
                # 使用fullmatch确保完整匹配正则表达式
                if re.fullmatch(pattern, path) and method in methods:
                    return True
            except re.error as e:
                logger.error(f"[JWT认证] Invalid regex pattern '{pattern}': {e}")
                continue
        
        return False
    
    def _is_logout(self, path: str, method: str) -> bool:
        """
        判断是不是登出“/api/auth/logout”
        """
        return path == "/api/auth/logout" and method == "GET"
    
    def _should_slide_expiry(self, payload: Dict[str, Any], current_time: float) -> bool:
        """
        判断是否应该滑动过期时间
        策略：如果距离上次颁发时间超过MIN_REFRESH_INTERVAL，就滑动过期时间
        """
        iat_timestamp = payload.get('iat', current_time)
        time_since_issued = current_time - iat_timestamp
        
        # 避免频繁续期，只有超过最小间隔才续期
        should_slide = time_since_issued >= MIN_REFRESH_INTERVAL
        
        if should_slide:
            logger.info(f"[JWT认证] Should slide expiry. Time since issued: {time_since_issued}s")
        
        return should_slide
    
    def _is_user_active(self, payload: Dict[str, any]):
        data = payload.get("payload", {})
        user_id = data.get("id", "")
        if user_id:
            with session_scope() as session:
                user_info = session.query(UserInfo).where(UserInfo.id == user_id).first()
                if user_info:
                    return getattr(user_info, "state", False)
                else:
                    raise Exception(f"Can not get user info by id {user_id} from db")
        else:
            raise Exception("Can not get user id from token")
