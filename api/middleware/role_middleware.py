import logging
from fastapi import Request, HTTPException
from fastapi.responses import J<PERSON><PERSON><PERSON>ponse
from typing import Dict, Any
from api.common.constants import PrivType
from api.middleware.auth_middleware import get_current_user
from api.middleware.language_middleware import get_translation
from api.service.user_priv_service import get_user_privileges_by_type_with_cache
from starlette.middleware.base import BaseHTTPMiddleware
import re
from api.config import get_developMode, get_excludeUrl, get_enable_role_service_privilege

logger = logging.getLogger(__name__)

# 获取开发模式配置
developMode = get_developMode()

class RoleMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, exempt_paths: dict = {}):
        super().__init__(app)
        # 不需要认证的路径，支持正则表达式
        self.exempt_paths = exempt_paths | get_excludeUrl()
    
    def _auth_failed(self, err_code: str = "", err_msg: str = "", status_code: int = 403):
        return JSONResponse(
            status_code=status_code,
            content={"success": False, "errCode": err_code, "errMsg": err_msg}
        )

    async def dispatch(self, request: Request, call_next):
        # 如果启用了开发者模式，跳过token认证
        if developMode:
            return await call_next(request)
        
        # 新逻辑：只要 request.state 存在 skip_service_priv_check 且为 True，则跳过服务权限校验
        if hasattr(request.state, "skip_service_priv_check") and request.state.skip_service_priv_check:
            return await call_next(request)
        
        # 检查当前路径是否需要认证
        if self._is_exempt_path(request):
            return await call_next(request)

        # 需要认证的路径处理
        try:

            # 检查用户是否已经登录
            user_info = get_current_user()
            if not user_info:
                return self._auth_failed("AUTH_0006", get_translation('auth.authenticationRequired'), 401)
                
            # 检查用户权限
            has_permission = await self._check_user_permission(request, user_info)
            if not has_permission:
                return self._auth_failed("AUTH_0011", get_translation('role.insufficientPermissionsDescription'))
            
            # 继续处理请求
            response = await call_next(request)
            return response
        except HTTPException as e:
            logger.error(f"Error occur when verify token: {e}")
            return self._auth_failed("AUTH_0000", get_translation('auth.authFailed'))
        except Exception as e:
            logger.error(f"Unexpected error in role middleware: {e}")
            return self._auth_failed("INTERNAL_0000", get_translation('role.internalServerError'), 500)

    def _is_exempt_path(self, request: Request) -> bool:
        """检查路径是否在排除路径列表中"""
        path = request.url.path
        method = request.method
        
        for pattern, methods in self.exempt_paths.items():
            try:
                # 使用fullmatch确保完整匹配正则表达式
                if re.fullmatch(pattern, path) and method in methods:
                    logger.debug(f"Path {path} with method {method} matches exempt pattern {pattern}")
                    return True
            except re.error as e:
                logger.error(f"Invalid regex pattern '{pattern}': {e}")
                continue
        
        return False

    async def _check_user_permission(self, request: Request, user_info: Dict[str, Any]) -> bool:
        """
        检查用户是否有权限访问当前请求的接口
        
        Args:
            request: FastAPI请求对象
            user_info: 用户信息字典，包含user_id等信息
            
        Returns:
            bool: True表示有权限，False表示没有权限
        """
        try:
            # 新增：如果未开启服务权限校验，则直接放行
            if not get_enable_role_service_privilege():
                logger.debug("服务权限校验未开启，直接放行所有服务权限")
                return True

            # 获取请求的路径和方法
            path = request.url.path
            method = request.method.upper()
            
            # 从用户信息中获取user_id
            user_id = user_info.get('id')
            if not user_id:
                logger.warning("User ID not found in token payload")
                return False
            
            # 查询用户的所有服务权限表达式（优先缓存）
            user_service_privileges = get_user_privileges_by_type_with_cache(user_id, PrivType.SERVICE)
            if not user_service_privileges or not user_service_privileges.get("privileges"):
                logger.warning(f"No privileges found for user_id: {user_id}")
                return False

            # 检查用户是否有role_id为1的角色，如果有则直接放行
            if 1 in user_service_privileges.get("role_ids"):
                logger.debug(f"Permission granted: user has role_id=1, allowing all access")
                return True

            # 检查用户权限是否包含当前请求的权限
            for privilege in user_service_privileges.get("privileges"):
                if self._match_permission_parts(privilege.get("priv_el"), method, path):
                    logger.debug(f"Permission granted: {method} {path} matches {privilege}")
                    return True

            logger.warning(f"Permission denied for user_id {user_id}: {method} {path} not in user privileges")
            return False
            
        except Exception as e:
            logger.error(f"Error checking user permission for user[{user_id}]: {str(e)}", exc_info=True)
            return False

    def _match_permission_parts(self, privilege: str, method: str, path: str) -> bool:
        """
        分别匹配权限表达式的方法和路径部分
        
        Args:
            privilege: 数据库中的权限表达式，格式如 "GET-/api/path" 或 "GET-/api/path/*"
            method: 当前请求的HTTP方法，如 "GET", "POST"
            path: 当前请求的路径，如 "/api/users/123"
            
        Returns:
            bool: True表示匹配，False表示不匹配
        """
        try:
            # 解析权限表达式，分离方法和路径
            if '-' not in privilege:
                logger.warning(f"Invalid privilege format: {privilege}")
                return False
                
            priv_method, priv_path = privilege.split('-', 1)
            
            # 如果权限表达式中的方法为ALL，则不校验接口的方法类型
            if priv_method.upper() != 'ALL':
            # 匹配HTTP方法（不区分大小写）
                if priv_method.upper() != method.upper():
                    return False
            
            # 匹配路径
            return self._match_path(priv_path, path)
            
        except Exception as e:
            logger.error(f"Error matching permission parts '{privilege}' with '{method} {path}': {str(e)}", exc_info=True)
            return False

    def _match_path(self, priv_path: str, request_path: str) -> bool:
        """
        匹配路径部分
        
        Args:
            priv_path: 权限表达式中的路径部分，如 "/api/path" 或 "/api/path/*"
            request_path: 当前请求的路径，如 "/api/users/123"
            
        Returns:
            bool: True表示匹配，False表示不匹配
        """
        try:
            # 精确匹配
            if priv_path == request_path:
                return True
            
            # 支持正则表达式匹配
            try:
                if re.match(f"^{priv_path}$", request_path):
                    return True
            except re.error:
                # 如果不是有效的正则表达式，跳过
                pass
                
            return False
            
        except Exception as e:
            logger.error(f"Error matching path '{priv_path}' with '{request_path}': {str(e)}", exc_info=True)
            return False
