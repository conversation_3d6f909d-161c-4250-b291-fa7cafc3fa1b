from sqlmodel import select
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import JSONResponse
from api.database.base import session_scope
from api.model.api_def import ApiDef
from api.model.user_access_token import UserAccessToken
from api.middleware.language_middleware import get_translation
from api.utils.aes_utils import AESUtils
from datetime import datetime
from api.model.user_info import UserInfo
from contextvars import ContextVar

class UserAuthMiddleware(BaseHTTPMiddleware):
    def __init__(self, app):
        super().__init__(app)
        
    def _auth_failed(self, err_code: str = "", err_msg: str = "", status_code: int = 401):
        return JSONResponse(
            status_code=status_code,
            content={"success": False, "errCode": err_code, "errMsg": err_msg}
        )
    
    async def dispatch(self, request: Request, call_next):
        # 若App鉴权已成功且为用户级token（token_type==2），则无需再进行用户级token校验，直接放行
        if getattr(request.state, "auth_successful", False) and getattr(request.state, "request_auth_way", "") == "app_token" and getattr(request.state, "app_token_type", None) == 2:
            return await call_next(request)
        if request.headers.get("X-User-Token"):
            return await self._user_access_token_auth(request, call_next)
        return await call_next(request)

    async def _user_access_token_auth(self, request: Request, call_next):
        aes = AESUtils()
        user_access_token = request.headers.get("X-User-Token")
        if not user_access_token:
            return self._auth_failed("AUTH_0001", get_translation('auth.authenticationRequired'))

        # 根据token里的app_id、请求接口的路径和方法在数据库找到应用绑定的接口
        include_urls = []
        with session_scope() as session:
            apis = session.exec(select(ApiDef.api_path).where(ApiDef.state == 1)).all()
        # 如果没有找到对应的接口,校验不通过
            if not apis:
                return self._auth_failed("AUTH_0004", "No matching interface found.", 403)
            include_urls = apis
        if request.url.path not in include_urls:
            return self._auth_failed("AUTH_0004", get_translation('auth.userTokenAuthNotInclude'), 403)
        user_access_token = aes.encrypt_fixed(user_access_token)
        with session_scope() as session:
            user_access_token_db = session.query(UserAccessToken).where((UserAccessToken.token == user_access_token) & (UserAccessToken.state == 1)).first()
            if not user_access_token_db:
                return self._auth_failed("AUTH_0001", get_translation('auth.authenticationRequired'))
            if user_access_token_db.effective_at is not None and datetime.now() < user_access_token_db.effective_at:
                return self._auth_failed("AUTH_0002", get_translation('auth.authenticationRequired'))
            if user_access_token_db.expires_at is not None and datetime.now() > user_access_token_db.expires_at:
                return self._auth_failed("AUTH_0003", get_translation('auth.authenticationRequired'))
            user_access_token_db.last_used_time = datetime.now()
            user_info = session.query(UserInfo).where(UserInfo.id == user_access_token_db.user_id).first()
            if not user_info:
                return self._auth_failed("AUTH_0006", get_translation('auth.authenticationRequired'))
            
            request.state.user = {**user_info.model_dump()}
            if hasattr(request.state, "request_auth_way") and request.state.request_auth_way == "app_token":
                request.state.request_auth_way = "app_user_token"
            else:
                request.state.request_auth_way = "user_token"
            request.state.auth_successful = True
            # 设置跳过服务鉴权的标记，供后续中间件使用
            request.state.skip_service_priv_check = True
            session.commit()

        return await call_next(request)
