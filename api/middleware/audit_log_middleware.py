import asyncio
import json
import logging
from contextvars import <PERSON>textVar
from typing import Any, Dict, Optional
from fastapi import Request
from api.database.base import session_scope
from api.model.serv_log import ServLog
from api.utils.ip_utils import get_client_ip, get_server_ip
from starlette.middleware.base import BaseHTTPMiddleware
from api.middleware.auth_middleware import get_current_user
from api.model.event_code import EventCode

logger = logging.getLogger(__name__)

# 审计日志全局上下文，用来保存部分记录日志所需的信息
audit_log_context: ContextVar[Optional[Dict[str, Any]]] = ContextVar('audit_log_context', default=None)

def audit_logger():
    def log(**kwargs):
        audit_data = audit_log_context.get()
        if audit_data is None:
            audit_data = {}
        audit_data.update(kwargs)
        audit_log_context.set(audit_data)
    return log

class AuditLogMiddleware(BaseHTTPMiddleware):
    def __init__(self, app):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next):
        # 初始化审计日志全局上下文
        audit_log_context.set({})
        success = True
        try:
            # 往下运行接口
            response = await call_next(request)
            if response and response.status_code:
                # call_next 会把 endpoint 的异常交给框架的异常处理器处理，并返回一个 Response，中间件里无法捕获这个异常，所以通过response里的状态码来判断请求是否成功
                success = response.status_code // 100 == 2
        except Exception:
            success = False
            raise
        finally:
            audit_data = audit_log_context.get()
            asyncio.create_task(self._add_audit_log(request, success, audit_data))

        return response
    
    async def _add_audit_log(self, request: Request, success: bool, audit_data: Dict[str, any]):
        try:
            from api.cache.local.event_code_cache import get_event_by_code
            with session_scope() as session:
                if audit_data:
                    event_code = audit_data.get("code")
                    if event_code:
                        # 优先从缓存获取
                        event_code_model = get_event_by_code(event_code)
                        # 缓存为空则从数据库查
                        if not event_code_model:
                            event_code_model = session.query(EventCode).where(EventCode.is_audit == True, EventCode.event_code == event_code).first()
                        if event_code_model:
                            # 兼容缓存返回 dict 或 ORM 对象
                            if isinstance(event_code_model, dict):
                                serv_log = ServLog()
                                serv_log.event_src = event_code_model.get("event_src_code")
                                serv_log.event_type = event_code_model.get("event_type")
                                serv_log.event_code = event_code_model.get("event_code")
                                serv_log.comments = event_code_model.get("comments")
                            else:
                                serv_log = ServLog()
                                serv_log.event_src = event_code_model.event_src_code
                                serv_log.event_type = event_code_model.event_type
                                serv_log.event_code = event_code_model.event_code
                                serv_log.comments = event_code_model.comments
                            serv_log.src_ip = get_client_ip(request)
                            serv_log.server_ip = get_server_ip()
                            serv_log.is_success = success
                            # 将从全局上下文获取的审计日志数据设置到 serv_log 对象里
                            self._set_audit_data(serv_log, audit_data)
                            # 获取用户信息
                            user = get_current_user() or {}
                            # 设置登录用户信息
                            serv_log.oper_id = audit_data.get("oper_id", user.get("id", 0))
                            serv_log.dept_id = user.get("dept_id")
                            serv_log.dept_name = user.get("dept")
                            self._sync_login_logout(serv_log, user.get("user_code"), user.get("user_name"), audit_data)
                            # 审计日志入库
                            session.add(serv_log)
                    else:
                        logger.warning("Failed to add audit log, cause by: event code is none")
        except Exception as ex:
            logger.error(f"Failed to add audit log, cause by: {ex}")
    
    def _set_audit_data(self, serv_log: ServLog, audit_data: Dict[str, any]):
        serv_log.party_type = audit_data.get("party_type")
        serv_log.party_code = audit_data.get("party_code")
        serv_log.party_id = audit_data.get("party_id", 0)
        serv_log.party_name = audit_data.get("party_name")
        if 'is_success' in audit_data:
            serv_log.is_success = audit_data['is_success']
        if 'oper_data' in audit_data:
            oper_data = audit_data['oper_data']
            if isinstance(oper_data, dict):
                serv_log.oper_data = json.dumps(oper_data)
            if isinstance(oper_data, str):
                serv_log.oper_data = oper_data
    
    def _sync_login_logout(self, serv_log: ServLog, user_code: str, user_name: str, audit_data: Dict[str, any]):
        """
        针对"登录"、"登出"做特殊处理，因为"登录"、"登出"操作，操作人和被操作对象应该是相同的，
        且"登录"、"登出"时没有经过 auth_middleware 所以无法获取登录信息，需要额外同步信息
        """
        if serv_log.event_code == "USER_LOGIN":
            serv_log.oper_id = serv_log.party_id
            serv_log.dept_id = audit_data.get("dept_id")
            serv_log.dept_name = audit_data.get("dept")
        if serv_log.event_code == "USER_LOGOUT":
            serv_log.party_id = serv_log.oper_id
            serv_log.party_code = user_code
            serv_log.party_name = user_name
