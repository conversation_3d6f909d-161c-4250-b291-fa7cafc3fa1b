from enum import Enum
from typing import NamedTuple

class PrivType(str, Enum):
    SERVICE = "S"
    COMPONENT = "C"
    

class AuthMode(str, Enum):
    JWT = "jwt"
    SESSION = "session"
    USER_TOKEN_AUTH = "user_token_auth"
    
class RoleInfo(NamedTuple):
    id: int
    role_name: str
    role_code: str
    role_type: str
    access_level: int
    comments: str

class DeepWikiRole(Enum):
    SUPER_ADMIN = RoleInfo(1, "超级管理员", "super_admin", "S", 4, "具备网站的所有权限")
    REPO_OWNER = RoleInfo(2, "wiki管理员", "repo_owner", "S", 3, "具备代码仓库的管理权限，可以生成、删除、更新wiki")
    USER = RoleInfo(3, "普通用户", "user", "S", 1, "可以查看wiki")
    AI_QA = RoleInfo(4, "wiki问答用户", "ai_qa", "S", 2, "可以查看wiki，并支持问答")
    WIKI_GRANT = RoleInfo(5, "wiki授权", "wiki_grant", "D", 2, "可以将wiki授权给用户")
    WIKI_ACCESS = RoleInfo(6, "wiki访问用户", "wiki_access", "D", 1, "前端界面用户可以看到wiki")
    
    @classmethod
    def get_by_code(cls, role_code: str):
        """根据角色编码获取角色"""
        for role in cls:
            if role.value.role_code == role_code:
                return role
        return None
    
    @classmethod
    def get_by_id(cls, role_id: int):
        """根据ID获取角色"""
        for role in cls:
            if role.value.id == role_id:
                return role
        return None
    
    def has_higher_access_than(self, other_role):
        """判断是否比其他角色权限更高"""
        return self.value.access_level > other_role.value.access_level