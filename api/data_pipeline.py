import adalflow as adal
from adalflow.core.types import Document, List
from typing import Union
from adalflow.components.data_process import TextSplitter, ToEmbeddings
import os
import subprocess
import json
import tiktoken
import logging
import base64
import re
import glob
from adalflow.utils import get_adalflow_default_root_path
from adalflow.core.db import LocalDB
from api.config import (
    configs,
    DEFAULT_CODE_EXTENSIONS,
    DEFAULT_DOCUMENT_EXTENSIONS,
    get_effective_file_filters,
)
from api.ollama_patch import OllamaDocumentProcessor
from urllib.parse import urlparse, urlunparse, quote
from api.middleware.language_middleware import get_translation
import requests
from requests.exceptions import RequestException
from api.docchain.manager import DocChainManager

import os  
import shutil  
import glob  
import logging  
from pathlib import Path  

from api.tools.embedder import get_embedder
from api.utils.git_utils import extract_repo_info, get_unified_repo_path

# Configure logging
logger = logging.getLogger(__name__)

# Maximum token limit for OpenAI embedding models
MAX_EMBEDDING_TOKENS = 8192

def count_tokens(text: str, is_ollama_embedder: bool = None) -> int:
    """
    Count the number of tokens in a text string using tiktoken.

    Args:
        text (str): The text to count tokens for.
        is_ollama_embedder (bool, optional): Whether using Ollama embeddings.
                                           If None, will be determined from configuration.

    Returns:
        int: The number of tokens in the text.
    """
    try:
        # Determine if using Ollama embedder if not specified
        if is_ollama_embedder is None:
            from api.config import is_ollama_embedder as check_ollama
            is_ollama_embedder = check_ollama()

        if is_ollama_embedder:
            encoding = tiktoken.get_encoding("cl100k_base")
        else:
            encoding = tiktoken.encoding_for_model("text-embedding-3-small")

        return len(encoding.encode(text))
    except Exception as e:
        # Fallback to a simple approximation if tiktoken fails
        logger.warning(f"Error counting tokens with tiktoken: {e}")
        # Rough approximation: 4 characters per token
        return len(text) // 4

def download_repo(repo_url: str, local_path: str, type: str = "whaleDevCloud", access_token: str = None, branch: str = "master") -> str:
    """
    Downloads a Git repository (GitHub, GitLab, or Bitbucket) to a specified local path.

    Args:
        repo_url (str): The URL of the Git repository to clone.
        local_path (str): The local directory where the repository will be cloned.
        access_token (str, optional): Access token for private repositories.

    Returns:
        str: The output message from the `git` command.
    """
    try:
        # Check if Git is installed
        logger.info(f"Preparing to clone repository to {local_path}")
        subprocess.run(
            ["git", "--version"],
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )

        # Check if repository already exists
        if os.path.exists(local_path) and os.listdir(local_path):
            # Directory exists and is not empty
            logger.warning(f"Repository already exists at {local_path}. Using existing repository.")
            return f"Using existing repository at {local_path}"

        # Ensure the local path exists
        os.makedirs(local_path, exist_ok=True)

        # Prepare the clone URL with access token if provided
        clone_url = repo_url
        if access_token:
            parsed = urlparse(repo_url)
            # Determine the repository type and format the URL accordingly
            if type == "github":
                # Format: https://{token}@github.com/owner/repo.git
                # Works for both github.com and enterprise GitHub domains
                clone_url = urlunparse((parsed.scheme, f"{access_token}@{parsed.netloc}", parsed.path, '', '', ''))
            elif type == "gitlab":
                # Format: https://oauth2:{token}@gitlab.com/owner/repo.git
                clone_url = urlunparse((parsed.scheme, f"oauth2:{access_token}@{parsed.netloc}", parsed.path, '', '', ''))
            elif type == "bitbucket":
                # Format: https://{token}@bitbucket.org/owner/repo.git
                clone_url = urlunparse((parsed.scheme, f"{access_token}@{parsed.netloc}", parsed.path, '', '', ''))
            elif type == "whaleDevCloud":
                # Format: https://{token}@github.com/owner/repo.git
                # Works for both github.com and enterprise GitHub domains
                clone_url = urlunparse((parsed.scheme, f"{access_token}@{parsed.netloc}", parsed.path, '', '', ''))
            logger.info("Using access token for authentication")

        # Clone the repository
        logger.info(f"Cloning repository from {repo_url} to {local_path}")
        # We use repo_url in the log to avoid exposing the token in logs
        result = subprocess.run(
            ["git", "clone", "-b", branch, clone_url, local_path],
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )
        logger.info(f"type {type}, clone_url{clone_url}")

        logger.info("Repository cloned successfully")
        
        # logger.info("separate_repo_files begin..")
        # separate_repo_files(local_path)
        # logger.info("separate_repo_files end..")
        
        return result.stdout.decode("utf-8")

    except subprocess.CalledProcessError as e:
        # 读取并标准化错误信息
        error_msg = e.stderr.decode('utf-8')
        if access_token and access_token in error_msg:
            error_msg = error_msg.replace(access_token, "***TOKEN***")

        logger.error("git clone 失败: %s", error_msg)

        # 分支不存在检测（覆盖多种git输出）
        branch_hint = None
        branch_match = re.search(r"Remote branch\s+(\S+)\s+not found", error_msg)
        if branch_match:
            branch_hint = branch_match.group(1)
        else:
            ref_match = re.search(r"(?:couldn't|could not) find remote ref\s+([^\s']+)", error_msg, flags=re.IGNORECASE)
            if ref_match:
                branch_hint = ref_match.group(1)
            else:
                pathspec_match = re.search(r"pathspec '([^']+)' did not match any", error_msg, flags=re.IGNORECASE)
                if pathspec_match:
                    branch_hint = pathspec_match.group(1)

        # 根据常见错误关键字给出更友好的提示
        normalized_error = error_msg.lower()
        # 模式 -> 翻译键
        common_hints = [
            ("authentication failed", "api.errors.gitAuthFailed"),
            ("could not read username", "api.errors.gitAuthFailed"),
            ("repository not found", "api.errors.gitRepoNotFound"),
            ("http basic: access denied", "api.errors.gitPermissionDenied"),
            ("permission denied", "api.errors.gitPermissionDenied"),
            ("could not resolve host", "api.errors.gitResolveHostFailed"),
            ("failed to connect", "api.errors.gitConnectFailed"),
            ("connection timed out", "api.errors.gitTimeout"),
            ("request timed out", "api.errors.gitTimeout"),
            ("couldn't find remote ref", "api.errors.gitBranchNotFound"),
            ("could not find remote ref", "api.errors.gitBranchNotFound"),
        ]

        friendly_key = None
        for pattern, key in common_hints:
            if pattern in normalized_error:
                friendly_key = key
                break

        if branch_hint:
            # 分支不存在：优先用分支名提示
            use_branch = branch_hint or branch
            human_message = get_translation("api.errors.gitBranchNotFound").format(branch=use_branch)
        elif friendly_key:
            # 常见错误的友好提示（国际化）
            if friendly_key.endswith("gitBranchNotFound"):
                # 可能在错误文本中解析不到分支名，使用入参兜底
                use_branch = branch_hint or branch
                human_message = get_translation(friendly_key).format(branch=use_branch)
            else:
                human_message = get_translation(friendly_key)
        else:
            # 通用场景压缩换行，避免写入过长
            compressed = " ".join(error_msg.split())
            reason = compressed or get_translation("api.errors.unknownError")
            human_message = reason

        if len(human_message) > 200:
            human_message = f"{human_message[:197]}..."

        final_message = get_translation("api.errors.gitCloneFailed").format(reason=human_message)
        raise ValueError(final_message)
    except Exception as e:
        logger.error("git clone 过程中发生了未知异常: %s", e)
        fallback_message = " ".join(str(e).split()) or get_translation("api.errors.unknownError")
        if len(fallback_message) > 200:
            fallback_message = f"{fallback_message[:197]}..."
        final_message = get_translation("api.errors.gitUnknownError").format(detail=fallback_message)
        raise ValueError(final_message)

def git_pull_with_diff(repo_url: str, local_path: str, repo_type: str = "whaleDevCloud", access_token: str = None, branch: str = "master") -> dict:
    """
    执行git pull更新本地仓库并返回更新信息
    
    Args:
        local_path (str): 本地仓库路径
        repo_type (str): 仓库类型
        access_token (str, optional): 访问令牌
        branch (str): 分支名称
        
    Returns:
        dict: 包含更新信息的字典
            {
                "updated": bool,  # 是否有更新
                "changes": list,  # 变更的文件列表
                "additions": list,  # 新增的文件列表
                "modifications": list,  # 修改的文件列表
                "deletions": list,  # 删除的文件列表
                "commit_info": dict,  # 最新提交信息
                "output": str  # git pull输出
            }
    """
    try:
        git_dir = os.path.join(local_path, ".git")
        if not os.path.exists(local_path) or not os.path.exists(git_dir):
            raise ValueError(f"Invalid git repository path: {local_path}")

        # 切换到仓库目录
        original_cwd = os.getcwd()
        os.chdir(local_path)

        try:
            # 确保仓库干净
            try:
                subprocess.run(["git", "reset", "--hard"], check=True, capture_output=True)
                subprocess.run(["git", "clean", "-fd"], check=True, capture_output=True)
            except subprocess.CalledProcessError as cleanup_exc:
                logger.debug("git clean/reset failed (ignored): %s", cleanup_exc)

            # 获取当前HEAD的commit hash（可能为空，例如初次克隆失败后残留）
            old_head = None
            try:
                old_head_result = subprocess.run(
                    ["git", "rev-parse", "HEAD"],
                    capture_output=True,
                    text=True,
                    check=True
                )
                old_head = (old_head_result.stdout or "").strip() or None
            except subprocess.CalledProcessError:
                old_head = None

            # 如果提供了access_token，配置临时的remote URL
            if access_token:
                # 获取当前的remote origin URL
                # origin_result = subprocess.run(
                #     ["git", "config", "--get", "remote.origin.url"],
                #     capture_output=True,
                #     text=True,
                #     check=True
                # )
                # original_url = origin_result.stdout.strip()
                
                # 构建带token的URL
                parsed = urlparse(repo_url)
                if repo_type == "github" or repo_type == "whaleDevCloud":
                    auth_url = urlunparse((parsed.scheme, f"{access_token}@{parsed.netloc}", parsed.path, '', '', ''))
                elif repo_type == "gitlab":
                    auth_url = urlunparse((parsed.scheme, f"oauth2:{access_token}@{parsed.netloc}", parsed.path, '', '', ''))
                elif repo_type == "bitbucket":
                    auth_url = urlunparse((parsed.scheme, f"{access_token}@{parsed.netloc}", parsed.path, '', '', ''))
                else:
                    auth_url = repo_url
                
                # 临时设置带认证的URL
                subprocess.run(
                    ["git", "remote", "set-url", "origin", auth_url],
                    check=True,
                    capture_output=True
                )
            
            # 确保远端分支存在并获取最新引用
            fetch_result = subprocess.run(
                ["git", "fetch", "origin", branch],
                capture_output=True,
                text=True,
                check=False
            )
            if fetch_result.returncode != 0:
                error_msg = fetch_result.stderr or fetch_result.stdout or ""
                normalized = (error_msg or "").lower()
                if "couldn't find remote ref" in normalized or "could not find remote ref" in normalized:
                    logger.warning(
                        "Git fetch skipped for missing branch %s (repo_url=%s, path=%s)",
                        branch,
                        repo_url,
                        local_path,
                    )
                    if access_token:
                        try:
                            subprocess.run(
                                ["git", "remote", "set-url", "origin", repo_url],
                                check=True,
                                capture_output=True,
                            )
                        except subprocess.CalledProcessError:
                            logger.debug("恢复 remote URL 失败（忽略）")
                    return {
                        "updated": False,
                        "changes": [],
                        "additions": [],
                        "modifications": [],
                        "deletions": [],
                        "commit_info": {},
                        "output": f"branch {branch} missing; skipped",
                        "old_head": old_head,
                        "new_head": old_head,
                    }
                raise ValueError(f"Git fetch failed (repo_url={repo_url}, local_path={local_path}): {error_msg}")

            # 确保本地分支跟踪远端
            subprocess.run(
                ["git", "checkout", "-B", branch, f"origin/{branch}"],
                capture_output=True,
                text=True,
                check=True
            )

            # 执行git pull（fast-forward）
            pull_result = subprocess.run(
                ["git", "pull", "--ff-only", "origin", branch],
                capture_output=True,
                text=True,
                check=False
            )
            if pull_result.returncode not in (0, 1):  # git 返回1表示Already up to date
                error_msg = pull_result.stderr or pull_result.stdout or ""
                raise ValueError(f"Git pull failed (repo_url={repo_url}, local_path={local_path}): {error_msg}")

            # 恢复原始URL（如果修改了的话）
            if access_token:
                subprocess.run(
                    ["git", "remote", "set-url", "origin", repo_url],
                    check=True,
                    capture_output=True
                )

            # 获取更新后的HEAD
            try:
                new_head_result = subprocess.run(
                    ["git", "rev-parse", "HEAD"],
                    capture_output=True,
                    text=True,
                    check=True
                )
                new_head = (new_head_result.stdout or "").strip() or None
            except subprocess.CalledProcessError:
                new_head = None

            # 检查是否有更新
            updated = False
            if new_head and old_head:
                updated = old_head != new_head
            elif new_head and not old_head:
                updated = True
            changes = []
            additions = []
            modifications = []
            deletions = []
            commit_info = {}

            if updated and old_head and new_head:
                # 获取变更的文件列表
                diff_result = subprocess.run(
                    ["git", "diff", "--name-status", old_head, new_head],
                    capture_output=True,
                    text=True,
                    check=True
                )
                
                # 解析文件变更
                for line in diff_result.stdout.strip().split('\n'):
                    if line:
                        parts = line.split('\t')
                        if len(parts) >= 2:
                            status = parts[0]
                            filepath = parts[1]
                            changes.append({"status": status, "file": filepath})
                            
                            if status == 'A':
                                additions.append(filepath)
                            elif status == 'M':
                                modifications.append(filepath)
                            elif status == 'D':
                                deletions.append(filepath)
                
                # 获取最新提交信息
            if updated and new_head:
                commit_result = subprocess.run(
                    ["git", "log", "-1", "--pretty=format:%H|%an|%ae|%ad|%s", new_head],
                    capture_output=True,
                    text=True,
                    check=True
                )

                if commit_result.stdout:
                    commit_parts = commit_result.stdout.split('|')
                    if len(commit_parts) >= 5:
                        commit_info = {
                            "hash": commit_parts[0],
                            "author_name": commit_parts[1],
                            "author_email": commit_parts[2],
                            "date": commit_parts[3],
                            "message": commit_parts[4]
                        }
            
            return {
                "updated": updated,
                "changes": changes,
                "additions": additions,
                "modifications": modifications,
                "deletions": deletions,
                "commit_info": commit_info,
                "output": pull_result.stdout,
                "old_head": old_head,
                "new_head": new_head
            }
            
        finally:
            # 恢复原始工作目录
            os.chdir(original_cwd)

    except subprocess.CalledProcessError as e:
        error_message = e.stderr if e.stderr else e.stdout
        logger.error(
            "Git pull failed for %s (path=%s): %s",
            repo_url,
            local_path,
            error_message,
        )
        raise ValueError(f"Git pull failed (repo_url={repo_url}, local_path={local_path}): {error_message}")
    except Exception as e:
        logger.error(f"Git pull error: {str(e)}")
        raise ValueError(f"Git pull error: {str(e)}")

def get_git_file_changes(local_path: str, file_paths: list) -> dict:
    """
    获取指定文件的具体变更内容
    
    Args:
        local_path (str): 本地仓库路径
        file_paths (list): 需要获取变更的文件路径列表
        
    Returns:
        dict: 文件路径到变更内容的映射
    """
    try:
        if not os.path.exists(local_path) or not os.path.exists(os.path.join(local_path, ".git")):
            raise ValueError(f"Invalid git repository path: {local_path}")
        
        original_cwd = os.getcwd()
        os.chdir(local_path)
        
        file_changes = {}
        
        try:
            for file_path in file_paths:
                if os.path.exists(file_path):
                    # 读取文件内容
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        file_changes[file_path] = content
                    except UnicodeDecodeError:
                        # 对于二进制文件，不读取内容
                        file_changes[file_path] = f"[Binary file: {file_path}]"
                    except Exception as e:
                        logger.warning(f"Failed to read file {file_path}: {str(e)}")
                        file_changes[file_path] = f"[Error reading file: {str(e)}]"
        finally:
            os.chdir(original_cwd)
            
        return file_changes
        
    except Exception as e:
        logger.error(f"Get file changes error: {str(e)}")
        raise ValueError(f"Get file changes error: {str(e)}")

# Alias for backward compatibility
download_github_repo = download_repo


def separate_repo_files(repo_path: str):  
    """  
    将仓库文件按代码和文档分离到不同目录，并删除原始文件  
      
    Args:  
        repo_path (str): 原始仓库路径，如 'r-zmq-base'  
    """  
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)
    
    # 文件扩展名定义
    code_extensions = [".py", ".js", ".ts", ".java", ".cpp", ".c", ".h", ".hpp", ".go", ".rs",  
                       ".jsx", ".tsx", ".html", ".css", ".php", ".swift", ".cs",".def",".sql",".sh"]  
    doc_extensions = [".md", ".txt", ".rst", ".json", ".yaml", ".yml"]  
      
    # 创建目标目录  
    code_dir = os.path.join(repo_path, "deepwiki_code")  
    doc_dir = os.path.join(repo_path, "deepwiki_doc")  
      
    os.makedirs(code_dir, exist_ok=True)  
    os.makedirs(doc_dir, exist_ok=True)  
      
    logger.info(f"创建目录 code_dir: {code_dir}")  
    logger.info(f"创建目录 doc_dir: {doc_dir}")  
      
    # 需要排除的目录  
    excluded_dirs = {'.git', 'node_modules', '__pycache__', '.venv', 'build', 'dist',   
                     'deepwiki_code', 'deepwiki_doc'}  # 排除我们新创建的目录  
      
    def should_exclude_path(file_path: str) -> bool:  
        """检查路径是否应该被排除"""  
        path_parts = Path(file_path).parts  
        return any(excluded_dir in path_parts for excluded_dir in excluded_dirs)  
      
    def copy_file_with_structure(src_file: str, target_base_dir: str, repo_base: str):  
        """复制文件并保持目录结构"""  
        # 计算相对路径  
        rel_path = os.path.relpath(src_file, repo_base)  
        target_file = os.path.join(target_base_dir, rel_path)  
          
        # 创建目标目录  
        target_dir = os.path.dirname(target_file)  
        os.makedirs(target_dir, exist_ok=True)  
          
        # 复制文件  
        shutil.copy2(src_file, target_file)  
        # logger.info(f"已复制: {rel_path} -> {os.path.relpath(target_file, repo_path)}")  
        return target_file
    
    # 收集所有需要处理的文件
    all_files = []
    for root, dirs, files in os.walk(repo_path, topdown=True):
        # 修改dirs列表以跳过排除的目录
        dirs[:] = [d for d in dirs if d not in excluded_dirs]
        for file in files:
            file_path = os.path.join(root, file)
            if not should_exclude_path(file_path):
                all_files.append(file_path)
    
    # 处理代码和文档文件
    logger.info("正在处理代码和文档文件...")
    code_count = 0
    doc_count = 0
    
    for file_path in all_files:
        file_ext = os.path.splitext(file_path)[1].lower()
        file_name_lower = os.path.basename(file_path).lower()

        if (
            file_ext in code_extensions
            or file_name_lower == "dockerfile"
            or file_name_lower.startswith("dockerfile.")
        ):
            copy_file_with_structure(file_path, code_dir, repo_path)
            code_count += 1
        elif file_ext in doc_extensions:
            copy_file_with_structure(file_path, doc_dir, repo_path)
            doc_count += 1
    
    # 删除原始仓库中的所有内容，保留deepwiki_code和deepwiki_doc
    logger.info("正在删除原始仓库中的所有内容...")
    deleted_count = 0
    
    # 重新遍历仓库，这次不跳过任何目录
    for root, dirs, files in os.walk(repo_path, topdown=False):
        for file in files:
            file_path = os.path.join(root, file)
            # 跳过排除的目录中的文件
            if should_exclude_path(file_path):
                continue
            
            try:
                os.remove(file_path)
                deleted_count += 1
                # logger.info(f"已删除文件: {file_path}")
            except Exception as e:
                logger.warning(f"无法删除文件 {file_path}: {e}")
        
        # 删除空目录（除了deepwiki_code和deepwiki_doc及其子目录）
        for dir_name in dirs:
            dir_path = os.path.join(root, dir_name)
            # 跳过排除的目录
            if should_exclude_path(dir_path):
                continue
            
            try:
                # 检查目录是否为空
                if not os.listdir(dir_path):
                    os.rmdir(dir_path)
                    # logger.info(f"已删除空目录: {dir_path}")
            except Exception as e:
                logger.warning(f"无法删除目录 {dir_path}: {e}")
    
    logger.info(f"操作完成。代码文件: {code_count}, 文档文件: {doc_count}, 已删除文件和目录: {deleted_count}")  
    logger.info(f"代码文件位置: {code_dir}")  
    logger.info(f"文档文件位置: {doc_dir}")  

    
def read_all_documents(path: str, is_ollama_embedder: bool = None, excluded_dirs: List[str] = None, excluded_files: List[str] = None,
                      included_dirs: List[str] = None, included_files: List[str] = None):
    """
    Recursively reads all documents in a directory and its subdirectories.

    Args:
        path (str): The root directory path.
        is_ollama_embedder (bool, optional): Whether using Ollama embeddings for token counting.
                                           If None, will be determined from configuration.
        excluded_dirs (List[str], optional): List of directories to exclude from processing.
            Overrides the default configuration if provided.
        excluded_files (List[str], optional): List of file patterns to exclude from processing.
            Overrides the default configuration if provided.
        included_dirs (List[str], optional): List of directories to include exclusively.
            When provided, only files in these directories will be processed.
        included_files (List[str], optional): List of file patterns to include exclusively.
            When provided, only files matching these patterns will be processed.

    Returns:
        list: A list of Document objects with metadata.
    """
    documents = []
    # File extensions to look for, prioritizing code files
    code_extensions = list(dict.fromkeys(DEFAULT_CODE_EXTENSIONS))
    doc_extensions = list(dict.fromkeys(DEFAULT_DOCUMENT_EXTENSIONS))

    # Determine filtering mode: inclusion or exclusion
    use_inclusion_mode = (included_dirs is not None and len(included_dirs) > 0) or (included_files is not None and len(included_files) > 0)

    if use_inclusion_mode:
        # Inclusion mode: only process specified directories and files
        final_included_dirs = set(included_dirs) if included_dirs else set()
        final_included_files = set(included_files) if included_files else set()

        logger.info(f"Using inclusion mode")
        logger.info(f"Included directories: {list(final_included_dirs)}")
        logger.info(f"Included files: {list(final_included_files)}")

        # Convert to lists for processing
        included_dirs = list(final_included_dirs)
        included_files = list(final_included_files)
        excluded_dirs = []
        excluded_files = []
    else:
        # Exclusion mode: use default exclusions plus any additional ones
        excluded_dirs, excluded_files = get_effective_file_filters(
            excluded_dirs=excluded_dirs,
            excluded_files=excluded_files,
        )
        included_dirs = []
        included_files = []

        logger.info(f"Using exclusion mode")
        logger.info(f"Excluded directories: {excluded_dirs}")
        logger.info(f"Excluded files: {excluded_files}")

    logger.info(f"Reading documents from {path}")

    def should_process_file(file_path: str, use_inclusion: bool, included_dirs: List[str], included_files: List[str],
                           excluded_dirs: List[str], excluded_files: List[str]) -> bool:
        """
        Determine if a file should be processed based on inclusion/exclusion rules.

        Args:
            file_path (str): The file path to check
            use_inclusion (bool): Whether to use inclusion mode
            included_dirs (List[str]): List of directories to include
            included_files (List[str]): List of files to include
            excluded_dirs (List[str]): List of directories to exclude
            excluded_files (List[str]): List of files to exclude

        Returns:
            bool: True if the file should be processed, False otherwise
        """
        file_path_parts = os.path.normpath(file_path).split(os.sep)
        file_name = os.path.basename(file_path)

        if use_inclusion:
            # Inclusion mode: file must be in included directories or match included files
            is_included = False

            # Check if file is in an included directory
            if included_dirs:
                for included in included_dirs:
                    clean_included = included.strip("./").rstrip("/")
                    if clean_included in file_path_parts:
                        is_included = True
                        break

            # Check if file matches included file patterns
            if not is_included and included_files:
                for included_file in included_files:
                    if file_name == included_file or file_name.endswith(included_file):
                        is_included = True
                        break

            # If no inclusion rules are specified for a category, allow all files from that category
            if not included_dirs and not included_files:
                is_included = True
            elif not included_dirs and included_files:
                # Only file patterns specified, allow all directories
                pass  # is_included is already set based on file patterns
            elif included_dirs and not included_files:
                # Only directory patterns specified, allow all files in included directories
                pass  # is_included is already set based on directory patterns

            return is_included
        else:
            # Exclusion mode: file must not be in excluded directories or match excluded files
            is_excluded = False

            # Check if file is in an excluded directory
            for excluded in excluded_dirs:
                clean_excluded = excluded.strip("./").rstrip("/")
                if clean_excluded in file_path_parts:
                    is_excluded = True
                    break

            # Check if file matches excluded file patterns
            if not is_excluded:
                for excluded_file in excluded_files:
                    if file_name == excluded_file:
                        is_excluded = True
                        break

            return not is_excluded

    # Process code files first
    for ext in code_extensions:
        files = glob.glob(f"{path}/**/*{ext}", recursive=True)
        for file_path in files:
            # Check if file should be processed based on inclusion/exclusion rules
            if not should_process_file(file_path, use_inclusion_mode, included_dirs, included_files, excluded_dirs, excluded_files):
                continue

            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()
                    relative_path = os.path.relpath(file_path, path)

                    # Determine if this is an implementation file
                    is_implementation = (
                        not relative_path.startswith("test_")
                        and not relative_path.startswith("app_")
                        and "test" not in relative_path.lower()
                    )

                    # Check token count
                    token_count = count_tokens(content, is_ollama_embedder)
                    if token_count > MAX_EMBEDDING_TOKENS * 10:
                        logger.warning(f"Skipping large file {relative_path}: Token count ({token_count}) exceeds limit")
                        continue

                    doc = Document(
                        text=content,
                        meta_data={
                            "file_path": relative_path,
                            "type": ext[1:],
                            "is_code": True,
                            "is_implementation": is_implementation,
                            "title": relative_path,
                            "token_count": token_count,
                        },
                    )
                    documents.append(doc)
            except Exception as e:
                logger.error(f"Error reading {file_path}: {e}")

    # Then process documentation files
    for ext in doc_extensions:
        files = glob.glob(f"{path}/**/*{ext}", recursive=True)
        for file_path in files:
            # Check if file should be processed based on inclusion/exclusion rules
            if not should_process_file(file_path, use_inclusion_mode, included_dirs, included_files, excluded_dirs, excluded_files):
                continue

            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()
                    relative_path = os.path.relpath(file_path, path)

                    # Check token count
                    token_count = count_tokens(content, is_ollama_embedder)
                    if token_count > MAX_EMBEDDING_TOKENS * 10:
                        logger.warning(f"Skipping large file {relative_path}: Token count ({token_count}) exceeds limit")
                        continue

                    doc = Document(
                        text=content,
                        meta_data={
                            "file_path": relative_path,
                            "type": ext[1:],
                            "is_code": False,
                            "is_implementation": False,
                            "title": relative_path,
                            "token_count": token_count,
                        },
                    )
                    documents.append(doc)
            except Exception as e:
                logger.error(f"Error reading {file_path}: {e}")

    logger.info(f"Found {len(documents)} documents")
    return documents

def prepare_data_pipeline(is_ollama_embedder: bool = None):
    """
    Creates and returns the data transformation pipeline.

    Args:
        is_ollama_embedder (bool, optional): Whether to use Ollama for embedding.
                                           If None, will be determined from configuration.

    Returns:
        adal.Sequential: The data transformation pipeline
    """
    from api.config import get_embedder_config, is_ollama_embedder as check_ollama

    # Determine if using Ollama embedder if not specified
    if is_ollama_embedder is None:
        is_ollama_embedder = check_ollama()

    splitter = TextSplitter(**configs["text_splitter"])
    embedder_config = get_embedder_config()

    embedder = get_embedder()

    if is_ollama_embedder:
        # Use Ollama document processor for single-document processing
        embedder_transformer = OllamaDocumentProcessor(embedder=embedder)
    else:
        # Use batch processing for other embedders
        batch_size = embedder_config.get("batch_size", 500)
        embedder_transformer = ToEmbeddings(
            embedder=embedder, batch_size=batch_size
        )

    data_transformer = adal.Sequential(
        splitter, embedder_transformer
    )  # sequential will chain together splitter and embedder
    return data_transformer

def transform_documents_and_save_to_db(
    documents: List[Document], db_path: str, is_ollama_embedder: bool = None
) -> LocalDB:
    """
    Transforms a list of documents and saves them to a local database.

    Args:
        documents (list): A list of `Document` objects.
        db_path (str): The path to the local database file.
        is_ollama_embedder (bool, optional): Whether to use Ollama for embedding.
                                           If None, will be determined from configuration.
    """
    # Get the data transformer
    data_transformer = prepare_data_pipeline(is_ollama_embedder)

    # Save the documents to a local database
    db = LocalDB()
    db.register_transformer(transformer=data_transformer, key="split_and_embed")
    db.load(documents)
    db.transform(key="split_and_embed")
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    db.save_state(filepath=db_path)
    return db

def get_github_file_content(repo_url: str, file_path: str, access_token: str = None) -> str:
    """
    Retrieves the content of a file from a GitHub repository using the GitHub API.
    Supports both public GitHub (github.com) and GitHub Enterprise (custom domains).

    Args:
        repo_url (str): The URL of the GitHub repository (e.g., "https://github.com/username/repo" or "https://github.company.com/username/repo")
        file_path (str): The path to the file within the repository (e.g., "src/main.py")
        access_token (str, optional): GitHub personal access token for private repositories

    Returns:
        str: The content of the file as a string

    Raises:
        ValueError: If the file cannot be fetched or if the URL is not a valid GitHub URL
    """
    try:
        # Parse the repository URL to support both github.com and enterprise GitHub
        parsed_url = urlparse(repo_url)
        if not parsed_url.scheme or not parsed_url.netloc:
            raise ValueError("Not a valid GitHub repository URL")

        # Check if it's a GitHub-like URL structure
        path_parts = parsed_url.path.strip('/').split('/')
        if len(path_parts) < 2:
            raise ValueError("Invalid GitHub URL format - expected format: https://domain/owner/repo")

        owner = path_parts[-2]
        repo = path_parts[-1].replace(".git", "")

        # Determine the API base URL
        if parsed_url.netloc == "github.com":
            # Public GitHub
            api_base = "https://api.github.com"
        elif parsed_url.netloc == "git-nj.iwhalecloud.com":  
            api_base = f"{parsed_url.scheme}://{parsed_url.netloc}/api/v1"  # whaleDevCloud
        else:
            # GitHub Enterprise - API is typically at https://domain/api/v3/
            api_base = f"{parsed_url.scheme}://{parsed_url.netloc}/api/v3"
        
        # Use GitHub API to get file content
        # The API endpoint for getting file content is: /repos/{owner}/{repo}/contents/{path}
        api_url = f"{api_base}/repos/{owner}/{repo}/contents/{file_path}"

        # Fetch file content from GitHub API
        headers = {}
        if access_token:
            headers["Authorization"] = f"token {access_token}"
        logger.info(f"Fetching file content from GitHub API: {api_url}")
        try:
            response = requests.get(api_url, headers=headers)
            response.raise_for_status()
        except RequestException as e:
            raise ValueError(f"Error fetching file content: {e}")
        try:
            content_data = response.json()
        except json.JSONDecodeError:
            raise ValueError("Invalid response from GitHub API")

        # Check if we got an error response
        if "message" in content_data and "documentation_url" in content_data:
            raise ValueError(f"GitHub API error: {content_data['message']}")

        # GitHub API returns file content as base64 encoded string
        if "content" in content_data and "encoding" in content_data:
            if content_data["encoding"] == "base64":
                # The content might be split into lines, so join them first
                content_base64 = content_data["content"].replace("\n", "")
                content = base64.b64decode(content_base64).decode("utf-8")
                return content
            else:
                raise ValueError(f"Unexpected encoding: {content_data['encoding']}")
        else:
            raise ValueError("File content not found in GitHub API response")

    except Exception as e:
        raise ValueError(f"Failed to get file content: {str(e)}")
    
def get_whaleDevCloud_file_content(repo_url: str, file_path: str, access_token: str = None, branch: str = "main") -> str:  
    """  
    Retrieves the content of a file from a whaleDevCloud repository using the whaleDevCloud API.  
      
    Args:  
        repo_url (str): The URL of the whaleDevCloud repository   
        file_path (str): The path to the file within the repository  
        access_token (str, optional): whaleDevCloud access token for private repositories  
        branch (str): The branch to fetch from (default: "main")  
  
    Returns:  
        str: The content of the file as a string  
    """  
    try:  
        parsed_url = urlparse(repo_url)  
        if not parsed_url.scheme or not parsed_url.netloc:  
            raise ValueError("Not a valid whaleDevCloud repository URL")  
  
        path_parts = parsed_url.path.strip('/').split('/')  
        if len(path_parts) < 2:  
            raise ValueError("Invalid whaleDevCloud URL format - expected format: https://domain/owner/repo")  
  
        owner = path_parts[-2]  
        repo = path_parts[-1].replace(".git", "")  
  
        # whaleDevCloud API 格式 - 使用 /api/v1  
        api_base = f"{parsed_url.scheme}://{parsed_url.netloc}/api/v1"  
        api_url = f"{api_base}/repos/{owner}/{repo}/contents/{file_path}?ref={branch}"  
          
        headers = {}  
        if access_token:  
            headers["Authorization"] = f"token {access_token}"  
              
        logger.info(f"Fetching file content from whaleDevCloud API: {api_url}")  
        response = requests.get(api_url, headers=headers)  
        response.raise_for_status()  
          
        content_data = response.json()  
          
        # whaleDevCloud 返回 base64 编码的内容  
        if "content" in content_data and "encoding" in content_data:  
            if content_data["encoding"] == "base64":  
                content_base64 = content_data["content"].replace("\n", "")  
                content = base64.b64decode(content_base64).decode("utf-8")  
                return content  
            else:  
                raise ValueError(f"Unexpected encoding: {content_data['encoding']}")  
        else:  
            raise ValueError("File content not found in whaleDevCloud API response")  
              
    except Exception as e:  
        raise ValueError(f"Failed to get file content from whaleDevCloud: {str(e)}")

def get_gitlab_file_content(repo_url: str, file_path: str, access_token: str = None) -> str:
    """
    Retrieves the content of a file from a GitLab repository (cloud or self-hosted).

    Args:
        repo_url (str): The GitLab repo URL (e.g., "https://gitlab.com/username/repo" or "http://localhost/group/project")
        file_path (str): File path within the repository (e.g., "src/main.py")
        access_token (str, optional): GitLab personal access token

    Returns:
        str: File content

    Raises:
        ValueError: If anything fails
    """
    try:
        # Parse and validate the URL
        parsed_url = urlparse(repo_url)
        if not parsed_url.scheme or not parsed_url.netloc:
            raise ValueError("Not a valid GitLab repository URL")

        gitlab_domain = f"{parsed_url.scheme}://{parsed_url.netloc}"
        if parsed_url.port not in (None, 80, 443):
            gitlab_domain += f":{parsed_url.port}"
        path_parts = parsed_url.path.strip("/").split("/")
        if len(path_parts) < 2:
            raise ValueError("Invalid GitLab URL format — expected something like https://gitlab.domain.com/group/project")

        # Build project path and encode for API
        project_path = "/".join(path_parts).replace(".git", "")
        encoded_project_path = quote(project_path, safe='')

        # Encode file path
        encoded_file_path = quote(file_path, safe='')

        # Default to 'main' branch if not specified
        default_branch = 'main'

        api_url = f"{gitlab_domain}/api/v4/projects/{encoded_project_path}/repository/files/{encoded_file_path}/raw?ref={default_branch}"
        # Fetch file content from GitLab API
        headers = {}
        if access_token:
            headers["PRIVATE-TOKEN"] = access_token
        logger.info(f"Fetching file content from GitLab API: {api_url}")
        try:
            response = requests.get(api_url, headers=headers)
            response.raise_for_status()
            content = response.text
        except RequestException as e:
            raise ValueError(f"Error fetching file content: {e}")

        # Check for GitLab error response (JSON instead of raw file)
        if content.startswith("{") and '"message":' in content:
            try:
                error_data = json.loads(content)
                if "message" in error_data:
                    raise ValueError(f"GitLab API error: {error_data['message']}")
            except json.JSONDecodeError:
                pass

        return content

    except Exception as e:
        raise ValueError(f"Failed to get file content: {str(e)}")

def get_bitbucket_file_content(repo_url: str, file_path: str, access_token: str = None) -> str:
    """
    Retrieves the content of a file from a Bitbucket repository using the Bitbucket API.

    Args:
        repo_url (str): The URL of the Bitbucket repository (e.g., "https://bitbucket.org/username/repo")
        file_path (str): The path to the file within the repository (e.g., "src/main.py")
        access_token (str, optional): Bitbucket personal access token for private repositories

    Returns:
        str: The content of the file as a string
    """
    try:
        # Extract owner and repo name from Bitbucket URL
        if not (repo_url.startswith("https://bitbucket.org/") or repo_url.startswith("http://bitbucket.org/")):
            raise ValueError("Not a valid Bitbucket repository URL")

        parts = repo_url.rstrip('/').split('/')
        if len(parts) < 5:
            raise ValueError("Invalid Bitbucket URL format")

        owner = parts[-2]
        repo = parts[-1].replace(".git", "")

        # Use Bitbucket API to get file content
        # The API endpoint for getting file content is: /2.0/repositories/{owner}/{repo}/src/{branch}/{path}
        api_url = f"https://api.bitbucket.org/2.0/repositories/{owner}/{repo}/src/main/{file_path}"

        # Fetch file content from Bitbucket API
        headers = {}
        if access_token:
            headers["Authorization"] = f"Bearer {access_token}"
        logger.info(f"Fetching file content from Bitbucket API: {api_url}")
        try:
            response = requests.get(api_url, headers=headers)
            if response.status_code == 200:
                content = response.text
            elif response.status_code == 404:
                raise ValueError("File not found on Bitbucket. Please check the file path and repository.")
            elif response.status_code == 401:
                raise ValueError("Unauthorized access to Bitbucket. Please check your access token.")
            elif response.status_code == 403:
                raise ValueError("Forbidden access to Bitbucket. You might not have permission to access this file.")
            elif response.status_code == 500:
                raise ValueError("Internal server error on Bitbucket. Please try again later.")
            else:
                response.raise_for_status()
                content = response.text
            return content
        except RequestException as e:
            raise ValueError(f"Error fetching file content: {e}")

    except Exception as e:
        raise ValueError(f"Failed to get file content: {str(e)}")


# def get_file_content(repo_url: str, file_path: str, type: str = "github", access_token: str = None) -> str:
def get_file_content(repo_url: str, file_path: str, type: str = "github", access_token: str = None, request_rag=None,branch: str = "main") -> str:
    """
    Retrieves the content of a file from a Git repository (GitHub or GitLab).

    Args:
        repo_url (str): The URL of the repository
        file_path (str): The path to the file within the repository
        access_token (str, optional): Access token for private repositories

    Returns:
        str: The content of the file as a string

    Raises:
        ValueError: If the file cannot be fetched or if the URL is not valid
    """
    if type == "github":
        return get_github_file_content(repo_url, file_path, access_token)
    elif type == "gitlab":
        return get_gitlab_file_content(repo_url, file_path, access_token)
    elif type == "bitbucket":
        return get_bitbucket_file_content(repo_url, file_path, access_token)
    elif type == "whaleDevCloud":  
        return get_whaleDevCloud_file_content(repo_url, file_path, access_token, branch)
    elif type == "local":

        file_paths = process_file_paths(file_path)  
        if not file_paths:
            print("local模式下，没有找到文件路径")
            return ""
            
        # 初始化DocChain管理器
        base_url = os.environ.get("DOCCHAIN_BASE_URL", "http://localhost:7000")
        api_key = os.environ.get("DOCCHAIN_API_KEY", "")
        
        print(f"DocChain服务地址: {base_url}")
        print(f"API密钥: {'已设置' if api_key else '未设置'}")
        
        # 创建管理器
        manager = DocChainManager(base_url, api_key)
        
        # 用于存储拼接后的内容
        all_contents = []
        
        for file in file_paths:
            print(f"处理文件: {file}")
            
            # 从文件名和topic_id中提取参数
            try:
                filename = file.get("filename")
                topic_id = request_rag.topic_id
                
                if not filename or not topic_id:
                    print(f"跳过无效文件配置: {file}")
                    continue
                    
                # 获取文档ID
                doc_ids = manager.get_doc_id_by_filename(filename, topic_id)
                
                # 如果没有找到文档ID，尝试添加.md后缀
                if not doc_ids and not filename.endswith('.md'):
                    md_filename = f"{filename}.md"
                    print(f"未找到文件 '{filename}' 的文档ID，尝试使用 '{md_filename}' 重新查询")
                    doc_ids = manager.get_doc_id_by_filename(md_filename, topic_id)
                    
                    # 如果添加后缀后找到文档ID，更新文件名用于后续处理
                    if doc_ids:
                        print(f"✅ 使用 '{md_filename}' 成功找到文档ID")
                        filename = md_filename
                    else:
                        print(f"❌ 即使使用 '{md_filename}' 也未找到文档ID")
                
                if not doc_ids:
                    print(f"未找到文件 '{filename}' 在topic_id '{topic_id}' 中的文档ID")
                    continue
                    
                # 获取每个文档的内容
                for doc_id in doc_ids:
                    content = manager.get_document_content_by_id(doc_id)
                    
                    if content:
                        # 按照要求格式拼接内容
                        formatted_content = f"{filename} :{content}\n"
                        all_contents.append(formatted_content)
                        print(f"✅ 成功获取文件 '{filename}' 的内容，长度: {len(content)}")
                    else:
                        print(f"❌ 获取文件 '{filename}' 的内容失败")
                        
            except Exception as e:
                print(f"处理文件 '{file}' 时出错: {str(e)}")
                
        print(f"共处理了 {len(file_paths)} 个文件路径")
        
        # 将所有内容拼接成一个字符串
        return "".join(all_contents)
    else:
        raise ValueError("Unsupported repository URL. Only GitHub, GitLab, Bitbucket and local are supported.")

    
def process_file_paths(path_str, default_topic_id=None):
    """
    处理文件路径字符串，将其分割成数组，并只保留每个路径的最后部分(文件名)
    
    参数:
        path_str (str): 包含文件路径的字符串，用逗号分隔
        default_topic_id (int): 默认topic_id，如未提供则使用None
    
    返回:
        list: 处理后的文件配置列表，每个元素是包含filename和topic_id的字典
    """
    if not path_str:
        return []
        
    # 按逗号分割字符串
    paths = path_str.split(',')
    
    # 处理每个路径
    result = []
    for path in paths:
        path = path.strip()  # 去除前后空格
        if not path:
            continue
            
        # 提取文件名（处理/和\分隔符）
        filename = path.split('/')[-1].split('\\')[-1]
        
        # 创建文件配置
        file_config = {
            "filename": filename,
            "topic_id": default_topic_id
        }
        
        result.append(file_config)
    
    return result

class DatabaseManager:
    """
    Manages the creation, loading, transformation, and persistence of LocalDB instances.
    """

    def __init__(self):
        self.db = None
        self.repo_url_or_path = None
        self.repo_paths = None

    def prepare_database(
        self,
        repo_url_or_path: str,
        type: str = "github",
        access_token: str = None,
        branch: str = "master",
        is_ollama_embedder: bool = None,
        excluded_dirs: List[str] = None,
        excluded_files: List[str] = None,
        included_dirs: List[str] = None,
        included_files: List[str] = None,
        return_repo_path_only: bool = False,
    ) -> Union[str, List[Document]]:
        """创建或刷新仓库数据库，并按需返回仓库路径或文档列表。"""

        # 每次准备数据库前重置内部状态，确保不会复用旧数据
        self.reset_database()
        self._create_repo(repo_url_or_path, type, access_token, branch)

        if return_repo_path_only:
            # 仅返回仓库路径，供只需要本地目录的场景使用
            return self.repo_paths["save_repo_dir"]

        # 返回文档列表供检索流程使用
        return self.prepare_db_index(
            is_ollama_embedder=is_ollama_embedder,
            excluded_dirs=excluded_dirs,
            excluded_files=excluded_files,
            included_dirs=included_dirs,
            included_files=included_files,
        )

    def reset_database(self):
        """
        Reset the database to its initial state.
        """
        self.db = None
        self.repo_url_or_path = None
        self.repo_paths = None

    def _extract_repo_name_from_url(self, repo_url_or_path: str, repo_type: str) -> str:
        # Extract owner and repo name to create unique identifier
        url_parts = repo_url_or_path.rstrip('/').split('/')

        if repo_type in ["github", "gitlab", "bitbucket"] and len(url_parts) >= 5:
            # GitHub URL format: https://github.com/owner/repo
            # GitLab URL format: https://gitlab.com/owner/repo or https://gitlab.com/group/subgroup/repo
            # Bitbucket URL format: https://bitbucket.org/owner/repo
            owner = url_parts[-2]
            repo = url_parts[-1].replace(".git", "")
            repo_name = f"{owner}_{repo}"
        else:
            repo_name = url_parts[-1].replace(".git", "")
        return repo_name

    def _create_repo(self, repo_url_or_path: str, repo_type: str = "github", access_token: str = None, branch: str = "master") -> None:
        """
        Download and prepare all paths.
        Paths:
        ~/.adalflow/项目code/仓库名/分支名/（源代码）
        ~/.adalflow/databases/{owner}_{repo_name}_{branch}.pkl
        """
        logger.info(f"Preparing repo storage for {repo_url_or_path}...")

        try:
            root_path = get_adalflow_default_root_path()
            os.makedirs(root_path, exist_ok=True)
            # url
            if repo_url_or_path.startswith("https://") or repo_url_or_path.startswith("http://"):
                # repo_name = self._extract_repo_name_from_url(repo_url_or_path, repo_type)
                # 提取仓库信息
                owner, repo_name, _ = extract_repo_info(repo_url_or_path)
                # 使用统一的路径格式
                save_repo_dir = get_unified_repo_path(owner, repo_name, branch)
                
                logger.info(f"Extracted repo name: {repo_name}")
                # Check if the repository directory already exists and is not empty
                if not (os.path.exists(save_repo_dir) and os.listdir(save_repo_dir)):
                    # Only download if the repository doesn't exist or is empty
                    download_repo(repo_url_or_path, save_repo_dir, repo_type, access_token, branch)
                else:
                    logger.info(f"Repository already exists at {save_repo_dir}. Using existing repository.")
            else:  # local path
                repo_name = os.path.basename(os.path.normpath(repo_url_or_path))
                save_repo_dir = repo_url_or_path
            save_db_file = os.path.join(root_path, "databases", f"{repo_name}.pkl")
            os.makedirs(save_repo_dir, exist_ok=True)
            os.makedirs(os.path.dirname(save_db_file), exist_ok=True)
            self.repo_paths = {
                "save_repo_dir": save_repo_dir,
                "save_db_file": save_db_file,
            }
            self.repo_url_or_path = repo_url_or_path
            logger.info(f"Repo paths: {self.repo_paths}")
        except Exception as e:
            logger.error(f"Failed to create repository structure: {e}")
            raise

    def prepare_db_index(self, is_ollama_embedder: bool = None, excluded_dirs: List[str] = None, excluded_files: List[str] = None,
                        included_dirs: List[str] = None, included_files: List[str] = None) -> List[Document]:
        """
        Prepare the indexed database for the repository.

        Args:
            is_ollama_embedder (bool, optional): Whether to use Ollama for embedding.
                                               If None, will be determined from configuration.
            excluded_dirs (List[str], optional): List of directories to exclude from processing
            excluded_files (List[str], optional): List of file patterns to exclude from processing
            included_dirs (List[str], optional): List of directories to include exclusively
            included_files (List[str], optional): List of file patterns to include exclusively

        Returns:
            List[Document]: List of Document objects
        """
        # check the database
        if self.repo_paths and os.path.exists(self.repo_paths["save_db_file"]):
            logger.info("Loading existing database...")
            try:
                self.db = LocalDB.load_state(self.repo_paths["save_db_file"])
                documents = self.db.get_transformed_data(key="split_and_embed")
                if documents:
                    logger.info(f"Loaded {len(documents)} documents from existing database")
                    return documents
            except Exception as e:
                logger.error(f"Error loading existing database: {e}")
                # Continue to create a new database

        # prepare the database
        logger.info("Creating new database...")
        documents = read_all_documents(
            self.repo_paths["save_repo_dir"],
            is_ollama_embedder=is_ollama_embedder,
            excluded_dirs=excluded_dirs,
            excluded_files=excluded_files,
            included_dirs=included_dirs,
            included_files=included_files
        )
        self.db = transform_documents_and_save_to_db(
            documents, self.repo_paths["save_db_file"], is_ollama_embedder=is_ollama_embedder
        )
        logger.info(f"Total documents: {len(documents)}")
        transformed_docs = self.db.get_transformed_data(key="split_and_embed")
        logger.info(f"Total transformed documents: {len(transformed_docs)}")
        return transformed_docs

    def prepare_retriever(self, repo_url_or_path: str, type: str = "github", access_token: str = None, branch: str = 'master'):
        """
        Prepare the retriever for a repository.
        This is a compatibility method for the isolated API.

        Args:
            repo_url_or_path (str): The URL or local path of the repository
            access_token (str, optional): Access token for private repositories

        Returns:
            List[Document]: List of Document objects
        """
        return self.prepare_database(repo_url_or_path, type, access_token, branch)
