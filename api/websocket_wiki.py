# 移除循环导入问题，清理不必要的导入
import logging
import os
import hashlib
import json  # 添加json模块导入
from typing import List, Optional, Dict, Any
from urllib.parse import unquote

# 导入Google Generative AI依赖
import google.generativeai as genai

from adalflow.components.model_client.ollama_client import OllamaClient
from adalflow.core.types import ModelType, Document
from fastapi import WebSocket, WebSocketDisconnect, HTTPException
from pydantic import BaseModel, Field

from api.config import get_model_config, configs, load_repo_config
from api.data_pipeline import count_tokens, get_file_content
from api.openai_client import OpenAIClient
from api.openrouter_client import OpenRouterClient
from api.rag import RAG
from api.docchain.rag import DocChainRAG
from api.gemini_cli_client import GeminiCliClient
from api.database.base import session_scope
from api.service.wiki_info_service import get_wiki_info_by_repo_and_branch
from api.service.chat_session_service import add_chat_session
from api.service.chat_history_service import add_chat_history
from api.model.chat_session import ChatSession
from api.model.chat_history import ChatHistory

# Configure logging
from api.logging_config import setup_logging

setup_logging()
logger = logging.getLogger(__name__)


# # 获取当前工作目录
# current_dir = os.getcwd()
# log_file = os.path.join(current_dir, "new_logger.log")  # 新日志文件

# # 创建独立的新 Logger（命名为 "new_logger"，与原有 Logger 隔离）
# new_logger = logging.getLogger("new_logger")
# new_logger.setLevel(logging.INFO)  # 设置日志级别
# new_logger.propagate = False  # 阻止日志向上传播到根 Logger

# # 创建文件处理器
# file_handler = logging.FileHandler(log_file, encoding="utf-8")
# formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
# file_handler.setFormatter(formatter)

# # 将处理器添加到新 Logger
# new_logger.addHandler(file_handler)


# Get API keys from environment variables
google_api_key = os.environ.get('GOOGLE_API_KEY')
# 过滤掉api_key中的●符号
if google_api_key:
    google_api_key = google_api_key.replace('●', '')

# Configure Google Generative AI 
# 注意：这里的 configure 方法确实存在，但 linter 可能报错
# 可以在 /home/<USER>/code/deepwiki-open/.venv/lib/python3.12/site-packages/google/generativeai/__init__.py 中看到
if google_api_key:
    try:
        # 忽略linter错误，实际上这个方法存在
        genai.configure(api_key=google_api_key)  # type: ignore
    except Exception as e:
        logger.warning(f"Error configuring Google Generative AI: {str(e)}")
else:
    logger.warning("GOOGLE_API_KEY not found in environment variables")

# Models for the API
class ChatMessage(BaseModel):
    role: str  # 'user' or 'assistant'
    content: str

class ChatCompletionRequest(BaseModel):
    """
    Model for requesting a chat completion.
    """
    session_id: str = Field(..., description="Session ID")
    repo_url: str = Field(..., description="URL of the repository to query")
    messages: List[ChatMessage] = Field(..., description="List of chat messages")
    filePath: Optional[str] = Field(None, description="Optional path to a file in the repository to include in the prompt")
    token: Optional[str] = Field(None, description="Personal access token for private repositories")
    type: Optional[str] = Field("whaleDevCloud", description="Type of repository (e.g., 'github', 'gitlab', 'bitbucket')")
    branch: Optional[str] = Field("master", description="Branch of repository")

    # model parameters
    provider: str = Field("google", description="Model provider (google, openai, openrouter, ollama, gemini-cli)")
    model: Optional[str] = Field(None, description="Model name for the specified provider")
    api_key: Optional[str] = Field(None, description="API key for the model provider")

    # DocChain topic parameters
    existing_topic_id: Optional[str] = Field(None, description="Existing DocChain Topic ID to use instead of creating new one")
    existing_topic_id_code: Optional[str] = Field(None, description="Repo Code Existing DocChain Topic ID to use instead of creating new one")
    existing_topic_id_doc: Optional[str] = Field(None, description="Repo Doc Existing DocChain Topic ID to use instead of creating new one")

    language: Optional[str] = Field("en", description="Language for content generation (e.g., 'en', 'ja', 'zh', 'es', 'kr', 'vi')")
    excluded_dirs: Optional[str] = Field(None, description="Comma-separated list of directories to exclude from processing")
    excluded_files: Optional[str] = Field(None, description="Comma-separated list of file patterns to exclude from processing")
    included_dirs: Optional[str] = Field(None, description="Comma-separated list of directories to include exclusively")
    included_files: Optional[str] = Field(None, description="Comma-separated list of file patterns to include exclusively")
    
    # 客户端信息（可选）
    client_ip: Optional[str] = Field(None, description="Client IP address")

async def get_gemini_cli_provider_id():
    """获取gemini-cli在配置中的provider ID"""
    providers = configs.get("providers", {})
    for provider_id in providers.keys():
        if provider_id == "gemini-cli":
            return provider_id
    return "gemini-cli"  # 默认ID

async def handle_gemini_cli_chat(websocket: WebSocket, request: ChatCompletionRequest, session_id: str, api_key: str, client_ip: str = None):
    """处理gemini-cli模式下的聊天请求"""
    logger.info("使用Gemini CLI直接问答模式")
    
    # 记录客户端IP
    if client_ip:
        logger.info(f"Gemini CLI request from IP: {client_ip}")
            
    if not request.messages or len(request.messages) == 0:
        await websocket.send_text("Error: No messages provided")
        await websocket.close()
        return
        
    last_message = request.messages[-1]
    if last_message.role != "user":
        await websocket.send_text("Error: Last message must be from the user")
        await websocket.close()
        return
    
    # 数据库插入逻辑
    chat_id = None  # 初始化chat_id变量
    try:
        # 从repo_url中提取wiki_id（使用repo名称作为wiki_id）
        wiki_id = request.repo_url.split("/")[-1] if "/" in request.repo_url else request.repo_url
        if wiki_id.endswith(".git"):
            wiki_id = wiki_id[:-4]
        
        # 创建聊天会话记录
        chat_session = ChatSession(
            chat_sid=session_id,
            title=f"Chat about {wiki_id}",
            wiki_id=wiki_id,
            ip=client_ip or "unknown",
            state=1,
            created_by=1,  # 默认用户ID，可以根据实际需求调整
            created_date=None  # 会在service中自动设置
        )
        
        # 插入会话记录
        session_result = add_chat_session(chat_session)
        if not session_result:
            logger.error("Failed to create chat session")
            await websocket.send_text("Error: Failed to create chat session")
            await websocket.close()
            return
        
        # 获取会话ID（chat_id）
        chat_id = session_result.get('id')
        if not chat_id:
            logger.error("Failed to get chat session ID")
            await websocket.send_text("Error: Failed to get chat session ID")
            await websocket.close()
            return
        
        # 创建聊天历史记录
        chat_history = ChatHistory(
            msg_sid=f"msg_{session_id}_{hash(last_message.content) % 1000000}",
            chat_id=chat_id,
            role="user",
            content=last_message.content,
            model=request.model or "gemini-2.5-flash",
            msg_data="",
            state=1,
            deep_research=0,
            provider="gemini-cli",
            created_by=1,  # 默认用户ID，可以根据实际需求调整
            created_date=None  # 会在service中自动设置
        )
        
        # 插入聊天历史记录
        history_result = add_chat_history(chat_history)
        if not history_result:
            logger.error("Failed to create chat history")
            # 注意：这里不中断流程，因为聊天功能仍然可以继续
        
        logger.info(f"Successfully created chat session {chat_id} and history for session {session_id}")
        
    except Exception as e:
        logger.error(f"Error creating database records: {str(e)}")
        # 注意：这里不中断流程，因为聊天功能仍然可以继续
        
    repo_url = request.repo_url
    # 从repo_url中获取分支信息，如果没有提供则使用type作为分支
    branch = getattr(request, 'branch', 'main') if hasattr(request, 'branch') else request.type
    if not branch or branch in ['github', 'gitlab', 'bitbucket']:
        branch = 'main'  # 默认分支
    
    # 为了向后兼容，保留project_name的提取逻辑作为备选
    project_name = repo_url.split("/")[-1] if "/" in repo_url else repo_url
    if project_name.endswith(".git"):
        project_name = project_name[:-4]
        logger.info(f"Removed .git suffix from project name: {project_name}")
    
    try:
        model = GeminiCliClient()
        messages = [{"role": msg.role, "content": msg.content} for msg in request.messages]
        
        # 获取请求中的模型名称，默认为gemini-2.5-flash
        model_name = request.model if request.model else "gemini-2.5-flash"
        logger.info(f"Selected Gemini model: {model_name}")
        
        api_kwargs = {
            "messages": messages,
            "stream": True,
            "session_id": session_id,
            "api_key": api_key,
            "repo_url": repo_url,  # 使用新的参数格式
            "branch": branch,      # 传递分支信息
            "project_name": project_name,  # 保留向后兼容
            "model": model_name,  # 添加模型名称
            "timeout": 300.0
        }
        
        # 传递客户端IP到acall方法
        response = model.acall(api_kwargs=api_kwargs, client_ip=client_ip)
        
        # 简单转发消息，gemini_cli_client已经处理好了工具调用
        sent_chunks = set()
        full_response = ""
        async for chunk in response:
            # 工具调用信息不做去重处理，因为它们包含时间戳等唯一信息
            if chunk.startswith(('TOOL_CALL:', 'TOOL_STATUS:', 'TOOL_ERROR:')):
                await websocket.send_text(chunk)
                logger.debug(f"Forwarded tool message: {chunk[:50]}...")
            else:
                # 只对普通文本内容进行去重
                chunk_hash = hashlib.md5(chunk.encode()).hexdigest()
                if chunk_hash not in sent_chunks:
                    sent_chunks.add(chunk_hash)
                    full_response += chunk
                    await websocket.send_text(chunk)
                    logger.debug(f"Forwarded content: {chunk}")
        
        # 在AI回复完成后，插入AI回复的数据库记录
        try:
            if chat_id and full_response.strip():
                ai_chat_history = ChatHistory(
                    msg_sid=f"msg_{session_id}_{hash(full_response) % 1000000}",
                    chat_id=chat_id,
                    role="assistant",
                    content=full_response,
                    model=request.model or "gemini-2.5-flash",
                    msg_data="",
                    state=1,
                    deep_research=0,
                    provider="gemini-cli",
                    created_by=1,  # 默认用户ID，可以根据实际需求调整
                    created_date=None  # 会在service中自动设置
                )
                
                ai_history_result = add_chat_history(ai_chat_history)
                if ai_history_result:
                    logger.info(f"Successfully created AI response history for session {session_id}")
                else:
                    logger.error("Failed to create AI response history")
        except Exception as e:
            logger.error(f"Error creating AI response database record: {str(e)}")
        
        await websocket.close()
    except Exception as e:
        logger.error(f"Error in Gemini CLI mode: {str(e)}")
        await websocket.send_text(f"Error: {str(e)}")
        await websocket.close()

async def handle_websocket_chat(websocket: WebSocket):
    """
    Handle WebSocket connection for chat completions.
    This replaces the HTTP streaming endpoint with a WebSocket connection.
    """
    await websocket.accept()
    
    # 获取客户端IP地址
    client_ip = None
    try:
        # 尝试从不同的header中获取客户端IP
        client_ip = (
            websocket.headers.get("x-forwarded-for") or
            websocket.headers.get("x-real-ip") or
            websocket.headers.get("x-client-ip") or
            websocket.client.host if websocket.client else None
        )
        
        # 如果x-forwarded-for包含多个IP，取第一个
        if client_ip and "," in client_ip:
            client_ip = client_ip.split(",")[0].strip()
            
        logger.info(f"Client IP: {client_ip}")
    except Exception as e:
        logger.warning(f"Failed to get client IP: {e}")
    
    api_key = websocket.query_params.get("apiKey")
    try:
        # Receive and parse the request data
        request_data = await websocket.receive_json()
        request = ChatCompletionRequest(**request_data)
        
        # 检查是否为需要API密钥的提供商但没有提供API密钥
        if (request.provider == "openai" or request.provider == "whalecloud") and not api_key:
            logger.error("使用OpenAI/WhaleCloud但未提供API密钥")
            await websocket.send_text("Error: 请在设置中配置大模型Token。使用OpenAI/WhaleCloud模型需要提供有效的API密钥。")
            await websocket.close()
            return

        # 检查是否使用gemini-cli模式 (从配置文件获取provider ID)
        gemini_cli_provider_id = await get_gemini_cli_provider_id()
        is_gemini_cli_mode = request.provider == gemini_cli_provider_id
        
        if is_gemini_cli_mode:
            await handle_gemini_cli_chat(websocket, request, request.session_id, api_key, client_ip)
            return

        # Check if request contains very large input
        input_too_large = False
        if request.messages and len(request.messages) > 0:
            last_message = request.messages[-1]
            if hasattr(last_message, 'content') and last_message.content:
                tokens = count_tokens(last_message.content, request.provider == "ollama")
                logger.info(f"Request size: {tokens} tokens")
                if tokens > 8000:
                    logger.warning(f"Request exceeds recommended token limit ({tokens} > 7500)")
                    input_too_large = True

        # Create a new RAG instance for this request
        try:
            request_rag = DocChainRAG(provider=request.provider, model=request.model)

            # Extract custom file filter parameters if provided
            excluded_dirs = None
            excluded_files = None
            included_dirs = None
            included_files = None

            if request.excluded_dirs:
                excluded_dirs = [unquote(dir_path) for dir_path in request.excluded_dirs.split('\n') if dir_path.strip()]
                logger.info(f"Using custom excluded directories: {excluded_dirs}")
            if request.excluded_files:
                excluded_files = [unquote(file_pattern) for file_pattern in request.excluded_files.split('\n') if file_pattern.strip()]
                logger.info(f"Using custom excluded files: {excluded_files}")
            if request.included_dirs:
                included_dirs = [unquote(dir_path) for dir_path in request.included_dirs.split('\n') if dir_path.strip()]
                logger.info(f"Using custom included directories: {included_dirs}")
            if request.included_files:
                included_files = [unquote(file_pattern) for file_pattern in request.included_files.split('\n') if file_pattern.strip()]
                logger.info(f"Using custom included files: {included_files}")

            # 获取repo.json配置（仅用于日志记录，实际合并在CodeConverter中处理）
            repo_config = load_repo_config()
            repo_excluded_dirs = repo_config.get("file_filters", {}).get("excluded_dirs", [])
            repo_excluded_files = repo_config.get("file_filters", {}).get("excluded_files", [])
            
            # 记录repo.json中的配置信息
            if repo_excluded_dirs:
                logger.info(f"Using repo.json excluded directories: {len(repo_excluded_dirs)} entries")
            if repo_excluded_files:
                logger.info(f"Using repo.json excluded files: {len(repo_excluded_files)} entries")
            
            # 在prepare_retriever前调用get_wiki_info_by_repo_and_branch获取topic_id_code
            with session_scope() as session:
                wiki_info = get_wiki_info_by_repo_and_branch(session, request.repo_url, request.branch)
                if wiki_info and wiki_info.topic_id_code:
                    request.existing_topic_id_code = wiki_info.topic_id_code
                    request.existing_topic_id = wiki_info.topic_id
                    logger.info(f"Found existing topic_id_code: {wiki_info.topic_id_code}")
                else:
                    logger.info("No existing topic_id_code found, will create new one")
            
            # 准备检索器，传入过滤参数和已有Topic ID
            await request_rag.prepare_retriever(
                request.repo_url, 
                request.type, 
                request.token, 
                request.branch,
                excluded_dirs, 
                excluded_files, 
                included_dirs, 
                included_files,
                existing_topic_id=request.existing_topic_id,
                existing_topic_id_code=request.existing_topic_id_code,
                existing_topic_id_doc=request.existing_topic_id_doc        
            )
            logger.info(f"Retriever prepared for {request.repo_url}")
        except ValueError as e:
            if "No valid documents with embeddings found" in str(e):
                logger.error(f"No valid embeddings found: {str(e)}")
                await websocket.send_text("Error: No valid document embeddings found. This may be due to embedding size inconsistencies or API errors during document processing. Please try again or check your repository content.")
                await websocket.close()
                return
            else:
                logger.error(f"ValueError preparing retriever: {str(e)}")
                await websocket.send_text(f"Error preparing retriever: {str(e)}")
                await websocket.close()
                return
        except Exception as e:
            logger.error(f"Error preparing retriever: {str(e)}")
            # Check for specific embedding-related errors
            if "All embeddings should be of the same size" in str(e):
                await websocket.send_text("Error: Inconsistent embedding sizes detected. Some documents may have failed to embed properly. Please try again.")
            else:
                await websocket.send_text(f"Error preparing retriever: {str(e)}")
            await websocket.close()
            return

        # Validate request
        if not request.messages or len(request.messages) == 0:
            await websocket.send_text("Error: No messages provided")
            await websocket.close()
            return

        last_message = request.messages[-1]
        if last_message.role != "user":
            await websocket.send_text("Error: Last message must be from the user")
            await websocket.close()
            return

        # Process previous messages to build conversation history
        for i in range(0, len(request.messages) - 1, 2):
            if i + 1 < len(request.messages):
                user_msg = request.messages[i]
                assistant_msg = request.messages[i + 1]

                if user_msg.role == "user" and assistant_msg.role == "assistant":
                    request_rag.memory.add_dialog_turn(
                        user_query=user_msg.content,
                        assistant_response=assistant_msg.content
                    )

        # Check if this is a Deep Research request
        is_deep_research = False
        research_iteration = 1

        # Process messages to detect Deep Research requests
        for msg in request.messages:
            if hasattr(msg, 'content') and msg.content and "[DEEP RESEARCH]" in msg.content:
                is_deep_research = True
                # Only remove the tag from the last message
                if msg == request.messages[-1]:
                    # 移除所有的[DEEP RESEARCH]标记，可能有多个
                    while "[DEEP RESEARCH]" in msg.content:
                        msg.content = msg.content.replace("[DEEP RESEARCH]", "", 1).strip()

        # Count research iterations if this is a Deep Research request
        if is_deep_research:
            # 查找当前深度研究的起始位置
            last_deep_research_start_index = -1
            for i in range(len(request.messages) - 1, -1, -1):
                msg = request.messages[i]
                if msg.role == 'user' and hasattr(msg, 'content') and "[DEEP RESEARCH]" in msg.content:
                    last_deep_research_start_index = i
                    break
            
            # 计算从起始位置开始的助手消息数量
            if last_deep_research_start_index != -1:
                assistant_count = sum(1 for i in range(last_deep_research_start_index + 1, len(request.messages)) 
                                    if request.messages[i].role == 'assistant')
                research_iteration = assistant_count + 1
            else:
                # This case should ideally not be hit if is_deep_research is true
                research_iteration = 1
            
            logger.info(f"Deep Research request detected - iteration {research_iteration}")
            
            # 添加进度信息到日志，便于前端显示
            total_iterations = 5  # 默认总迭代次数
            progress = min(research_iteration / total_iterations, 1.0)
            logger.info(f"Deep Research progress: {research_iteration}/{total_iterations} ({progress:.0%})")

            # Check if this is a continuation request
            if "continue" in last_message.content.lower() and "research" in last_message.content.lower():
                # Find the original topic from the user message that started this research
                original_topic = None
                if last_deep_research_start_index != -1:
                    original_topic_msg = request.messages[last_deep_research_start_index]
                    if hasattr(original_topic_msg, 'content'):
                        original_topic = original_topic_msg.content.replace("[DEEP RESEARCH]", "").strip()
                        logger.info(f"Found original research topic: {original_topic}")

                if original_topic:
                    # Replace the continuation message with the original topic
                    last_message.content = original_topic
                    logger.info(f"Using original topic for research: {original_topic}")
                else:
                    # 如果找不到原始问题，记录警告
                    logger.warning("Could not find the original research question for a continuation request.")

            # 确保记录当前使用的研究问题
            logger.info(f"最终研究问题: {last_message.content}")

        # Get the query from the last message
        query = last_message.content

        # Only retrieve documents if input is not too large
        context_text = ""
        retrieved_documents = None

        if not input_too_large:
            try:
                # If filePath exists, modify the query for RAG to focus on the file
                rag_query = query
                if request.filePath:
                    # Use the file path to get relevant context about the file
                    rag_query = f"Contexts related to {request.filePath}"
                    logger.info(f"Modified RAG query to focus on file: {request.filePath}")

                # Try to perform RAG retrieval
                try:
                    # 正常RAG模式：执行检索流程
                    # DocChain检索模式：使用DocChain检索 + 外部大模型生成
                    logger.info("使用DocChain检索模式")
                            
                    rag_result, retrieved_contexts = request_rag.call(rag_query, language=request.language)
                            
                            # 模拟原有的文档结构
                    if retrieved_contexts:
                                documents = [Document(text=ctx.get("text", ""), meta_data=ctx.get("meta_data", {})) 
                                           for ctx in retrieved_contexts]
                                class MockRAGResult:
                                    def __init__(self, documents):
                                        self.documents = documents
                                retrieved_documents = [MockRAGResult(documents)]
                    else:
                                retrieved_documents = None


                    if retrieved_documents and retrieved_documents[0].documents:
                            # Format context for the prompt in a more structured way
                            documents = retrieved_documents[0].documents
                            logger.info(f"Retrieved {len(documents)} documents")

                            # Group documents by file path
                            docs_by_file = {}
                            for doc in documents:
                                file_path = doc.meta_data.get('file_path', 'unknown')
                                if file_path not in docs_by_file:
                                    docs_by_file[file_path] = []
                                docs_by_file[file_path].append(doc)

                            # Format context text with file path grouping
                            context_parts = []
                            for file_path, docs in docs_by_file.items():
                                # Add file header with metadata
                                file_path_temp = ensure_markdown_extension(file_path)
                                header = f"## File Path: {file_path_temp}\n\n"
                                # Add document content
                                content = "\n\n".join([doc.text for doc in docs])
                                
                                context_parts.append(f"{header}{content}")

                            # Join all parts with clear separation
                            context_text = "\n\n" + "-" * 10 + "\n\n".join(context_parts)
                    else:
                            logger.warning("No documents retrieved from RAG")
                except Exception as e:
                    logger.error(f"Error in RAG retrieval: {str(e)}")
                    # Continue without RAG if there's an error

            except Exception as e:
                logger.error(f"Error retrieving documents: {str(e)}")
                context_text = ""

        # Get repository information
        repo_url = request.repo_url
        repo_name = repo_url.split("/")[-1] if "/" in repo_url else repo_url

        # Determine repository type
        repo_type = request.type

        # Get language information
        language_code = request.language or "en"
        language_name = {
            "en": "English",
            "ja": "Japanese (日本語)",
            "zh": "Mandarin Chinese (中文)",
            "es": "Spanish (Español)",
            "kr": "Korean (한국어)",
            "vi": "Vietnamese (Tiếng Việt)"
        }.get(language_code, "English")

        # Create system prompt
        if is_deep_research:
            # Check if this is the first iteration
            is_first_iteration = research_iteration == 1

            # Check if this is the final iteration
            is_final_iteration = research_iteration >= 5

            # 发送带有进度信息的特殊消息，前端可以解析这些消息来更新进度条
            progress_info = {
                "type": "research_progress",
                "current": research_iteration,
                "total": 5,
                "is_final": is_final_iteration
            }
            try:
                await websocket.send_text(f"<!-- PROGRESS_INFO: {json.dumps(progress_info)} -->")
            except Exception as e:
                logger.error(f"Error sending progress info: {str(e)}")
            
            if is_first_iteration:
                system_prompt = f"""<role>
You are an expert code analyst examining the {repo_type} repository: {repo_url} ({repo_name}).
You are conducting a multi-turn Deep Research process to thoroughly investigate the specific topic in the user's query.
Your goal is to provide detailed, focused information EXCLUSIVELY about this topic.
IMPORTANT:You MUST respond in {language_name} language.
</role>

<guidelines>
- This is the first iteration of a multi-turn research process focused EXCLUSIVELY on the user's query
- Start your response with "## Research Plan"
- Outline your approach to investigating this specific topic
- If the topic is about a specific file or feature (like "Dockerfile"), focus ONLY on that file or feature
- Clearly state the specific topic you're researching to maintain focus throughout all iterations
- Identify the key aspects you'll need to research
- Provide initial findings based on the information available
- End with "## Next Steps" indicating what you'll investigate in the next iteration
- Do NOT provide a final conclusion yet - this is just the beginning of the research
- Do NOT include general repository information unless directly relevant to the query
- Focus EXCLUSIVELY on the specific topic being researched - do not drift to related topics
- Your research MUST directly address the original question
- NEVER respond with just "Continue the research" as an answer - always provide substantive research findings
- Remember that this topic will be maintained across all research iterations
</guidelines>

<style>
- Be concise but thorough
- Use markdown formatting to improve readability
- Cite specific files and code sections when relevant
</style>"""
            elif is_final_iteration:
                system_prompt = f"""<role>
You are an expert code analyst examining the {repo_type} repository: {repo_url} ({repo_name}).
You are in the final iteration of a Deep Research process focused EXCLUSIVELY on the latest user query.
Your goal is to synthesize all previous findings and provide a comprehensive conclusion that directly addresses this specific topic and ONLY this topic.
IMPORTANT:You MUST respond in {language_name} language.
</role>

<guidelines>
- This is the final iteration of the research process
- CAREFULLY review the entire conversation history to understand all previous findings
- Synthesize ALL findings from previous iterations into a comprehensive conclusion
- Start with "## Final Conclusion"
- Your conclusion MUST directly address the original question
- Stay STRICTLY focused on the specific topic - do not drift to related topics
- Include specific code references and implementation details related to the topic
- Highlight the most important discoveries and insights about this specific functionality
- Provide a complete and definitive answer to the original question
- Do NOT include general repository information unless directly relevant to the query
- Focus exclusively on the specific topic being researched
- NEVER respond with "Continue the research" as an answer - always provide a complete conclusion
- If the topic is about a specific file or feature (like "Dockerfile"), focus ONLY on that file or feature
- Ensure your conclusion builds on and references key findings from previous iterations
</guidelines>

<style>
- Be concise but thorough
- Use markdown formatting to improve readability
- Cite specific files and code sections when relevant
- Structure your response with clear headings
- End with actionable insights or recommendations when appropriate
</style>"""
            else:
                system_prompt = f"""<role>
You are an expert code analyst examining the {repo_type} repository: {repo_url} ({repo_name}).
You are currently in iteration {research_iteration} of a Deep Research process focused EXCLUSIVELY on the latest user query.
Your goal is to build upon previous research iterations and go deeper into this specific topic without deviating from it.
IMPORTANT:You MUST respond in {language_name} language.
</role>

<guidelines>
- CAREFULLY review the conversation history to understand what has been researched so far
- Your response MUST build on previous research iterations - do not repeat information already covered
- Identify gaps or areas that need further exploration related to this specific topic
- Focus on one specific aspect that needs deeper investigation in this iteration
- Start your response with "## Research Update {research_iteration}"
- Clearly explain what you're investigating in this iteration
- Provide new insights that weren't covered in previous iterations
- If this is iteration 3, prepare for a final conclusion in the next iteration
- Do NOT include general repository information unless directly relevant to the query
- Focus EXCLUSIVELY on the specific topic being researched - do not drift to related topics
- If the topic is about a specific file or feature (like "Dockerfile"), focus ONLY on that file or feature
- NEVER respond with just "Continue the research" as an answer - always provide substantive research findings
- Your research MUST directly address the original question
- Maintain continuity with previous research iterations - this is a continuous investigation
</guidelines>

<style>
- Be concise but thorough
- Focus on providing new information, not repeating what's already been covered
- Use markdown formatting to improve readability
- Cite specific files and code sections when relevant
</style>"""
        else:
            system_prompt = f"""<role>
You are an expert code analyst examining the {repo_type} repository: {repo_url} ({repo_name}).
You provide direct, concise, and accurate information about code repositories.
You NEVER start responses with markdown headers or code fences.
IMPORTANT:You MUST respond in {language_name} language.
</role>

<guidelines>
- Answer the user's question directly without ANY preamble or filler phrases
- DO NOT include any rationale, explanation, or extra comments.
- DO NOT start with preambles like "Okay, here's a breakdown" or "Here's an explanation"
- DO NOT start with markdown headers like "## Analysis of..." or any file path references
- DO NOT start with ```markdown code fences
- DO NOT end your response with ``` closing fences
- DO NOT start by repeating or acknowledging the question
- JUST START with the direct answer to the question

<example_of_what_not_to_do>
```markdown
## Analysis of `adalflow/adalflow/datasets/gsm8k.py`

This file contains...
```
</example_of_what_not_to_do>

- Format your response with proper markdown including headings, lists, and code blocks WITHIN your answer
- For code analysis, organize your response with clear sections
- Think step by step and structure your answer logically
- Start with the most relevant information that directly addresses the user's query
- Be precise and technical when discussing code
- Your response language should be in the same language as the user's query
</guidelines>

<style>
- Use concise, direct language
- Prioritize accuracy over verbosity
- When showing code, include line numbers and file paths when relevant
- Use markdown formatting to improve readability
</style>"""

        # Fetch file content if provided
        file_content = ""
        if request.filePath:
            try:
                file_content = get_file_content(request.repo_url, request.filePath, request.type, request.token, request_rag)
                logger.info(f"Successfully retrieved content for file: {request.filePath}")
            except Exception as e:
                logger.error(f"Error retrieving file content: {str(e)}")
                # Continue without file content if there's an error

        # Format conversation history
        conversation_history = ""
        for turn_id, turn in request_rag.memory().items():
            if not isinstance(turn_id, int) and hasattr(turn, 'user_query') and hasattr(turn, 'assistant_response'):
                conversation_history += f"<turn>\n<user>{turn.user_query.query_str}</user>\n<assistant>{turn.assistant_response.response_str}</assistant>\n</turn>\n"

        # Create the prompt with context
        prompt = f"/no_think {system_prompt}\n\n"

        if conversation_history:
            prompt += f"<conversation_history>\n{conversation_history}</conversation_history>\n\n"

        # Check if filePath is provided and fetch file content if it exists
        if file_content:
            # Add file content to the prompt after conversation history
            prompt += f"<currentFileContent path=\"{request.filePath}\">\n{file_content}\n</currentFileContent>\n\n"

        # Only include context if it's not empty
        CONTEXT_START = "<START_OF_CONTEXT>"
        CONTEXT_END = "<END_OF_CONTEXT>"
        if context_text.strip():
            prompt += f"{CONTEXT_START}\n{context_text}\n{CONTEXT_END}\n\n"
        else:
            logger.info("No context available from RAG")
            prompt += "<note>Answering without retrieval augmentation.</note>\n\n"

        query = optimize_file_paths(query)
        prompt += f"<query>\n{query}\n</query>\n\nAssistant: "

        model_config = get_model_config(request.provider, request.model)["model_kwargs"]

        if request.provider == "openai" or request.provider == "whalecloud":
            logger.info(f"Using Openai protocol with model: {request.model}")

            # 再次检查API密钥是否存在（虽然前面已经检查过，这里是双重保障）
            if not api_key:
                logger.error("使用OpenAI/WhaleCloud但未提供API密钥")
                await websocket.send_text("Error: 请在设置中配置大模型Token。使用OpenAI/WhaleCloud模型需要提供有效的API密钥。")
                await websocket.close()
                return

            # 使用前端传递的API密钥初始化OpenAI客户端，不使用环境变量
            # 为了确保不使用环境变量中的API密钥，我们设置一个临时的环境变量名
            # 过滤掉api_key中的●符号
            cleaned_api_key = api_key.replace('●', '') if api_key else ''
            model = OpenAIClient(
                api_key=cleaned_api_key,
                env_api_key_name="_UNUSED_ENV_VAR_NAME_"  # 使用一个不可能存在的环境变量名
            )
            config_kwargs = model_config.get("model_kwargs", {})
            model_kwargs = {
                "model": request.model,
                "stream": True,
                "temperature": config_kwargs.get("temperature"),
                "top_p": config_kwargs.get("top_p")
            }

            api_kwargs = model.convert_inputs_to_api_kwargs(
                input=prompt,
                model_kwargs=model_kwargs,
                model_type=ModelType.LLM
            )
            # 确保API密钥被正确传递
            api_kwargs["api_key"] = api_key
            
            # 记录客户端IP
            if client_ip:
                logger.info(f"OpenAI/WhaleCloud请求来自IP: {client_ip}")

        else:
            # Initialize Google Generative AI model
            # 从模型配置中获取参数，如果不存在则使用默认值
            model_kwargs = model_config.get("model_kwargs", {})
            model = genai.GenerativeModel(
                model_name=model_kwargs.get("model", "gemini-pro"),
                generation_config={
                    "temperature": model_kwargs.get("temperature", 0.7),
                    "top_p": model_kwargs.get("top_p", 0.95),
                    "top_k": model_kwargs.get("top_k", 40)  # 添加默认值40
                }
            )

        # Process the response based on the provider
        try:
            if request.provider == "openai" or request.provider == "whalecloud":
                try:
                    # Get the response and handle it properly using the previously created api_kwargs
                    logger.info("Making Openai API call")
                    logger.info(f"final prompt: {prompt}")
                    logger.info(f"prompt length: {len(prompt)}")
                    response = await model.acall(api_kwargs=api_kwargs, model_type=ModelType.LLM)
                    # Handle streaming response from Openai
                    async for chunk in response:
                        
                        choices = getattr(chunk, "choices", [])
                        if len(choices) > 0:
                            delta = getattr(choices[0], "delta", None)
                            if delta is not None:
                                text = getattr(delta, "content", None)
                                if text is not None:
                                    new_logger.info(f"Openai API response: {text}")
                                    await websocket.send_text(text)
                    # Explicitly close the WebSocket connection after the response is complete
                    await websocket.close()
                except Exception as e_openai:
                    logger.error(f"Error with Openai API: {str(e_openai)}")
                    error_msg = f"Error with Openai API: {str(e_openai)}\n\nPlease check that you have set the OPENAI_API_KEY environment variable with a valid API key."
                    await websocket.send_text(error_msg)
                    # Close the WebSocket connection after sending the error message
                    await websocket.close()
            else:
                # Generate streaming response
                response = model.generate_content(prompt, stream=True)
                # Stream the response
                for chunk in response:
                    if hasattr(chunk, 'text'):
                        await websocket.send_text(chunk.text)
                # Explicitly close the WebSocket connection after the response is complete
                await websocket.close()

        except Exception as e_outer:
            logger.error(f"Error in streaming response: {str(e_outer)}")
            error_message = str(e_outer)

            # Check for token limit errors
            if "maximum context length" in error_message or "token limit" in error_message or "too many tokens" in error_message:
                # If we hit a token limit error, try again without context
                logger.warning("Token limit exceeded, retrying without context")
                try:
                    # Create a simplified prompt without context
                    simplified_prompt = f"/no_think {system_prompt}\n\n"
                    if conversation_history:
                        simplified_prompt += f"<conversation_history>\n{conversation_history}</conversation_history>\n\n"

                    # Include file content in the fallback prompt if it was retrieved
                    if request.filePath and file_content:
                        simplified_prompt += f"<currentFileContent path=\"{request.filePath}\">\n{file_content}\n</currentFileContent>\n\n"

                    simplified_prompt += "<note>Answering without retrieval augmentation due to input size constraints.</note>\n\n"
                    simplified_prompt += f"<query>\n{query}\n</query>\n\nAssistant: "

                    if request.provider == "openai" or request.provider == "whalecloud":
                        try:
                            # 再次检查API密钥是否存在
                            if not api_key:
                                logger.error("降级处理时使用OpenAI/WhaleCloud但未提供API密钥")
                                await websocket.send_text("Error: 请在设置中配置大模型Token。使用OpenAI/WhaleCloud模型需要提供有效的API密钥。")
                                await websocket.close()
                                return
                                
                            config_kwargs = model_config.get("model_kwargs", {})
                            fallback_model_kwargs = {
                                "model": request.model,
                                "stream": True,
                                "temperature": config_kwargs.get("temperature"),
                                "top_p": config_kwargs.get("top_p")
                            }
                            # Create new api_kwargs with the simplified prompt
                            fallback_api_kwargs = model.convert_inputs_to_api_kwargs(
                                input=simplified_prompt,
                                model_kwargs=fallback_model_kwargs,
                                model_type=ModelType.LLM
                            )

                            # 确保API密钥被正确传递
                            # 过滤掉api_key中的●符号
                            cleaned_api_key = api_key.replace('●', '') if api_key else ''
                            fallback_api_kwargs["api_key"] = cleaned_api_key
                            
                            # Get the response using the simplified prompt
                            logger.info("Making fallback Openai API call")
                            fallback_response = await model.acall(api_kwargs=fallback_api_kwargs, model_type=ModelType.LLM)

                            # Handle streaming fallback_response from Openai
                            async for chunk in fallback_response:
                                text = chunk if isinstance(chunk, str) else getattr(chunk, 'text', str(chunk))
                                await websocket.send_text(text)
                        except Exception as e_fallback:
                            logger.error(f"Error with Openai API fallback: {str(e_fallback)}")
                            error_msg = f"Error with Openai API fallback: {str(e_fallback)}\n\nPlease check that you have set the OPENAI_API_KEY environment variable with a valid API key."
                            await websocket.send_text(error_msg)
                    else:
                        # Initialize Google Generative AI model
                        model_config = get_model_config(request.provider, request.model)
                        # 从模型配置中获取参数
                        model_kwargs = model_config.get("model_kwargs", {})
                        fallback_model = genai.GenerativeModel(
                            model_name=model_kwargs.get("model", "gemini-pro"),
                            generation_config={
                                "temperature": model_kwargs.get("temperature", 0.7),
                                "top_p": model_kwargs.get("top_p", 0.95),
                                "top_k": model_kwargs.get("top_k", 40)  # 默认值40
                            }
                        )

                        # Get streaming response using simplified prompt
                        fallback_response = fallback_model.generate_content(simplified_prompt, stream=True)
                        # Stream the fallback response
                        for chunk in fallback_response:
                            if hasattr(chunk, 'text'):
                                await websocket.send_text(chunk.text)
                except Exception as e2:
                    logger.error(f"Error in fallback streaming response: {str(e2)}")
                    await websocket.send_text(f"Error: \nI apologize, but your request is too large for me to process. Please try a shorter query or break it into smaller parts.")
                    # Close the WebSocket connection after sending the error message
                    await websocket.close()
            else:
                # For other errors, return the error message
                await websocket.send_text(f"Error: \n{error_message}")
                # Close the WebSocket connection after sending the error message
                await websocket.close()

    except WebSocketDisconnect:
        logger.info("WebSocket disconnected")
    except Exception as e:
        logger.error(f"Error in WebSocket handler: {str(e)}")
        try:
            await websocket.send_text(f"Error: {str(e)}")
            await websocket.close()
        except:
            pass
        
# 定义需要保留的文件扩展名列表
TEXT_EXTENSIONS = [
    # 编程语言
    ".py", ".js", ".ts", ".java", ".cpp", ".c", ".h", ".hpp", ".go", ".rs",
    ".jsx", ".tsx", ".html", ".css", ".php", ".swift", ".cs", ".sh", ".bat",
    ".ps1", ".pl", ".rb", ".lua", ".dart",
    # 标记/文档格式
    ".md", ".markdown", ".txt", ".rst", ".tex", ".adoc", ".org",
    # 配置文件
    ".json", ".yaml", ".yml", ".ini", ".conf", ".cfg", ".toml", ".env",
    ".properties", ".xml",
    # 数据/脚本
    ".sql", ".diff", ".patch", ".csv", ".tsv"
]

def ensure_markdown_extension(file_path: str) -> str:
    """
    检查文件路径是否以指定的文本扩展名结尾，若不是则添加 .md 后缀
    
    Args:
        file_path: 原始文件路径
    
    Returns:
        处理后的文件路径
    """
    # 转换为小写进行比较
    lower_path = file_path.lower()
    
    # 检查是否以任意允许的扩展名结尾
    for ext in TEXT_EXTENSIONS:
        if lower_path.endswith(ext):
            return file_path  # 已匹配有效扩展名，无需处理
    
    # 未匹配到有效扩展名，添加 .md
    return f"{file_path}.md"

def optimize_file_paths(paths_str):
    """
    参数:
    paths_str (str): 包含多个文件路径的字符串，路径之间用换行符分隔
    
    返回:
    str: 优化后的路径字符串
    """
    logger.info(f"websocket_wiki.py: 原始日志信息长度: {len(paths_str)}")
    
    # 找到<file_tree>和</file_tree>标签的位置
    start_tag = "<file_tree>"
    end_tag = "</file_tree>"
    start_pos = paths_str.find(start_tag)
    end_pos = paths_str.find(end_tag)
    
    if start_pos == -1 or end_pos == -1 or end_pos <= start_pos:
        # 如果没有找到标签或标签顺序错误，直接返回原始字符串
        logger.warning("未找到有效的<file_tree>标签，返回原始字符串")
        return paths_str
    
    # 分割字符串为三部分
    prefix = paths_str[:start_pos + len(start_tag)]
    file_tree_content = paths_str[start_pos + len(start_tag):end_pos].strip()
    suffix = paths_str[end_pos:]
    MAX_LENGTH = 500000  # <file_tree>内容的最大长度限制
    if len(file_tree_content) <= MAX_LENGTH:
        logger.info("文件树内容，无需优化")
        return paths_str
    
    # 按行分割<file_tree>内容并过滤空行
    paths = [line.strip() for line in file_tree_content.split('\n') if line.strip()]
    
    # 用于存储每个目录下的文件列表
    dir_files_map = {}
    
    # 处理每个路径
    for path in paths:
        # 分离目录和文件名
        dir_path, file_name = os.path.split(path)
        
        # 如果目录不在映射中，添加它
        if dir_path not in dir_files_map:
            dir_files_map[dir_path] = []
        
        # 添加文件名到对应的目录列表中
        dir_files_map[dir_path].append(file_name)
    
    # 构建优化后的路径字符串
    optimized_paths = []
    current_length = 0
    
    for dir_path, files in dir_files_map.items():
        # 简化目录路径，移除common前缀和项目根目录
        simplified_dir = dir_path
        
        # 尝试移除项目根目录部分（假设是第一个目录）
        parts = simplified_dir.split(os.sep)
        if len(parts) > 1 and (parts[0] == "code" or parts[0] == "local"):
            simplified_dir = os.sep.join(parts[1:])
        
        # 进一步简化：如果目录以"feature"开头，保留feature之后的部分
        if simplified_dir.startswith("feature" + os.sep):
            simplified_dir = simplified_dir[len("feature" + os.sep):]
        
        # 每个目录最多保留两个文件
        files_to_keep = files[:6]
        
        # 如果目录下只有一个文件，直接添加完整路径
        if len(files_to_keep) == 1:
            line = os.path.join(simplified_dir, files_to_keep[0]) + "\n"
        else:
            # 如果有多个文件，将目录路径和文件名列在同一行，用逗号分隔
            files_str = ", ".join(files_to_keep)
            line = f"{simplified_dir}\\{files_str}\n"
        
        # 检查添加此行后的总长度
        if current_length + len(line) > MAX_LENGTH:
            # 如果超过最大长度，停止处理
            break
        
        # 添加行并更新当前长度
        optimized_paths.append(line)
        current_length += len(line)
    
    # 用换行符连接所有优化后的路径，并移除末尾的换行符
    optimized_file_tree = "".join(optimized_paths).rstrip("\n")
    
    # 重新组合三部分内容
    result = prefix + optimized_file_tree + suffix
    logger.info("优化后的日志信息如下："+result)
    logger.info(f"websocket_wiki.py: 优化后的日志信息长度: {len(result)}")
    return result
