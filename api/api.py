import glob
import os
import base64
import logging
import re
import shutil
from copy import deepcopy

from sqlalchemy import and_, func, not_, or_, text
from sqlalchemy.orm import aliased

from api.dto.common_dto import AnnouncementResponse, AnnouncementsResponse, FileActionRequest, Model, ModelConfig, ModifyUserRolesRequest, ModifyUserStateRequest, ModifyWikiInfoRequest, ModifyWikiOwnerRequest, Provider, RepoBranchesRequest, RepoInfo, TestModelRequest, TestModelResponse, WhaleDevCloudTokenRequest, WikiCacheData, WikiCacheRequest, WikiExportRequest, WikiGenerationRequest, WikiPage, WikiRefreshRequest
from api.middleware.app_auth_middleware import AppAuthMiddleware
from api.model.chat_history import ChatHistory
from api.model.chat_session import ChatSession
from api.model.user_role import Role, UserRole
from api.model.wiki_user_role import Wiki<PERSON><PERSON><PERSON><PERSON>
from api.sandbox.kubernetes_service import get_kubernetes_service
from api.sandbox.sandbox_service import sandbox_service
from api.service.chat_history_service import select_chat_histories_by_chat_id, select_chat_history_by_msg_sid
from api.service.priv_checker import can_delete_wiki, can_read_wiki
from api.service.user_role_service import delete_user_roles_from_redis
from api.service.wiki_content_service import get_by_wiki_id
from api.service.wiki_query_service import count_non_failed_wikis, get_wiki_detail, search_wikis_4_login, search_wikis_4_unlogin, get_wiki_basic_info
from api.service.wiki_tag_service import add_wiki_tag, delete_wiki_tag_by_wiki_id_and_tag_id, get_wiki_tag_by_wiki_id_and_tag_id
from api.type.chat_request import ChatRequest
from api.type.chat_session_update_request import ChatSessionUpdateRequest
from api.type.sandbox_status import SandboxStatus
from api.utils.git_utils import extract_repo_info
from typing_extensions import Annotated
import aiohttp
import subprocess
from fastapi import FastAPI, HTTPException, Path, Query, Request, Depends, Body
from fastapi.concurrency import asynccontextmanager
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, Response
from typing import List, Optional, Dict, Any, Set
import json
from datetime import datetime, timedelta
from fastapi_mcp import FastApiMCP
from api.docchain.manager import DocChainManager
from api.utils import git_utils
from api.wiki.generator import WikiGenerator
# Configure logging - 注意：logging已在main.py中配置，这里只获取logger
from urllib.parse import urlparse, urlunparse  
import requests  
import base64
from api.config import configs, WIKI_AUTH_MODE, WIKI_AUTH_CODE
from api.service.user_service import select_users_for_grant, select_user_info_by_code
from api.utils.jwt_utils import SHARE_EXPIRES_MINUTES, create_jwt_token, verify_jwt_token
from api.middleware.auth_middleware import AuthMiddleware,get_current_user
from api.middleware.language_middleware import LanguageMiddleware, get_translation
from api.service.wiki_service import create_job, update_job
from api.database.base import db_service, session_scope
from sqlmodel import Session, delete, select
from api.model.wiki_info import WikiInfo
from api.model.git_repository import AiDwGitRepository
from api.docchain.client import docchain_manager
from api.service.wiki_info_service import create_wiki_info, extend_wiki_info, get_wiki_info_by_primary, get_wiki_info_by_repo, search_wikis_like, update_wiki_ext, \
    update_wiki_info, get_wiki_info, get_wiki_info_by_repo_and_branch,  \
    add_wiki_user_role, batch_add_wiki_user_role, delete_wiki_user_role, check_user_wiki_priv
    
from api.service import wiki_info_service
from api.model.wiki_job import WikiJob
from api.middleware.auth_middleware import AuthMiddleware, get_current_user
from api.middleware.role_middleware import RoleMiddleware
from api.middleware.audit_log_middleware import AuditLogMiddleware
from api.utils.aes_utils import AESUtils
from api.model.user_info import UserInfo
from api.wiki.sync_index_service import get_sync_index_service
from api.wiki.sync_worker_service import get_sync_worker_service
from api.utils.path_mapping import get_path_mapping_service
from api.sse_chat import chat_completions_stream, ChatCompletionRequest, select_chat_session_by_chat_sid
from api.websocket_wiki import handle_websocket_chat
from api.model.base import SubRepoInfo
from api.service.user_priv_service import PrivType, get_user_privileges_by_type
import uuid
from api.middleware.audit_log_middleware import audit_logger
from api.service.chat_session_service import delete_chat_session_by_sid, select_chat_sessions_by_user_and_wiki, update_chat_session
from pathlib import Path as FilePath


# 不再调用setup_logging()，因为main.py已经配置过了
logger = logging.getLogger(__name__)

aes = AESUtils()
# 简单内存存储，生产环境请用数据库
share_snapshots = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 初始化HTTP session
    session = aiohttp.ClientSession()
    app.state.session = session
    
    # 初始化WikiJobManager
    from api.wiki.wiki_job_manager import init_job_manager, start_job_manager, stop_job_manager
    from api.database.base import db_service
    from api.docchain.client import docchain_manager
    from api.wiki.docker_graceful_shutdown import setup_docker_graceful_shutdown
    
    # 初始化JobManager（配置将从settings.yaml中读取）
    job_manager = init_job_manager(db_service, docchain_manager, lock_timeout_minutes=30)
    
    # 启动JobManager
    await start_job_manager()
    logger.info("WikiJobManager已启动")
    
    # 设置Docker优雅停止机制
    try:
        shutdown_handler = setup_docker_graceful_shutdown(job_manager)
        logger.info("Docker优雅停止机制已启用")
    except Exception as e:
        logger.warning(f"设置Docker优雅停止机制失败: {e}")
    
    yield
    
    # 关闭时停止JobManager
    await stop_job_manager()
    logger.info("WikiJobManager已停止")
    
    # 关闭HTTP session
    await session.close()

# Initialize FastAPI app
app = FastAPI(
    title="Streaming API",
    description="API for streaming chat completions",
    lifespan=lifespan
)

# 审计日志中间件
app.add_middleware(AuditLogMiddleware)

# 添加认证中间件 ，顺序是按照添加的反序执行
app.add_middleware(RoleMiddleware)

app.add_middleware(AuthMiddleware)

app.add_middleware(AppAuthMiddleware)

app.add_middleware(LanguageMiddleware)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Helper function to get adalflow root path
def get_adalflow_default_root_path():
    return os.path.expanduser(os.path.join("~", ".adalflow"))

@app.get("/api/whaleDevCloud/structure")
async def get_whaleDevCloud_repo_structure(
    repo_url: str = Query(..., description="whaleDevCloud repository URL"),
    token: str = Query(None, description="whaleDevCloud access token"),
    branch: str = Query("main", description="Branch name")
):
    """Return the file tree and README content for a whaleDevCloud repository."""
    if not repo_url:
        return JSONResponse(
            status_code=400,
            content={"error": get_translation("api.errors.noRepoUrl")}
        )

    try:
        logger.info(f"Processing whaleDevCloud repository: {repo_url}, branch: {branch}")
        
        # Parse repository URL
        parsed_url = urlparse(repo_url)
        path_parts = parsed_url.path.strip('/').split('/')
        if len(path_parts) < 2:
            raise ValueError(get_translation("api.errors.invalidWhaleDevCloudUrl"))
            
        owner = path_parts[-2]
        repo = path_parts[-1].replace(".git", "")
        
        # Get file tree from whaleDevCloud API
        api_base = f"{parsed_url.scheme}://{parsed_url.netloc}/api/v1"
        tree_url = f"{api_base}/repos/{owner}/{repo}/git/trees/{branch}?recursive=1"
        
        headers = {}
        if token:
            headers["Authorization"] = f"token {token}"
        
        # 使用异步请求库
        import aiohttp
        
        # Fetch repository tree
        async with aiohttp.ClientSession() as session:
            # 获取文件树
            async with session.get(tree_url, headers=headers) as tree_response:
                tree_response.raise_for_status()
                tree_data = await tree_response.json()
                
            # 提取文件路径
            file_tree_lines = []
            if 'tree' in tree_data:
                for item in tree_data['tree']:
                    if item.get('type') == 'blob':  # Only include files, not directories
                        file_tree_lines.append(item['path'])
            
            file_tree_str = '\n'.join(sorted(file_tree_lines))
            
            # 在文件树中查找README文件（大小写不敏感，支持多种扩展名）
            readme_path = None
            readme_candidates = []
            # 通过文件内容API读取README
            readme_content = ""

            for item in tree_data['tree']:
                # 只处理文件（blob类型）
                if item.get('type') != 'blob':
                    continue
                
                # 获取文件名并转换为小写进行比较
                file_name = item['path'].split('/')[-1].lower()
                
                # 检查是否为README文件（如README.md、readme.txt等）
                if file_name.startswith('readme'):
                    readme_candidates.append((item['path'], file_name))

            # 按优先级选择最合适的README文件
            if readme_candidates:
                # 定义扩展名优先级
                ext_priority = {'.md': 1, '.rst': 2, '.txt': 3}
                
                # 排序函数：先按扩展名优先级，再按原始顺序
                def sort_key(candidate):
                    path, name = candidate
                    ext = '.' + name.split('.')[-1] if '.' in name else ''
                    return ext_priority.get(ext, 99), name
                
                # 按优先级排序并选择第一个
                readme_candidates.sort(key=sort_key)
                readme_path = readme_candidates[0][0]
                logger.info(f"Found README file: {readme_path}")
                

                try:
                    file_url = f"{api_base}/repos/{owner}/{repo}/contents/{readme_path}?ref={branch}"
                    async with session.get(file_url, headers=headers) as file_response:
                        if file_response.status == 200:
                            file_data = await file_response.json()
                            if file_data.get('encoding') == 'base64':
                                # 解码base64内容
                                content_base64 = file_data['content'].replace('\n', '')
                                readme_content = base64.b64decode(content_base64).decode('utf-8')
                            else:
                                logger.warning(f"Unexpected encoding for {readme_path}: {file_data.get('encoding')}")
                        else:
                            error_text = await file_response.text()
                            logger.warning(f"Failed to read {readme_path}. Status: {file_response.status}, Content: {error_text}")
                except Exception as e:
                    logger.warning(f"Error reading {readme_path}: {str(e)}")
            else:
                logger.info("No README file found in the repository")
                
            return {"file_tree": file_tree_str, "readme": readme_content}
            
    except Exception as e:
        logger.error(f"Error processing whaleDevCloud repository: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"error": get_translation("api.errors.errorProcessingWhaleDevCloud")}
        )
    
def extract_last_part(path_str: str) -> str:
    """
    从路径字符串中提取最后一部分（文件名或目录名），支持多种路径格式
    
    Args:
        path_str: 路径字符串，支持Windows（含\）和Linux（含/）格式
    
    Returns:
        str: 路径中的最后一部分
    """
    # 处理Windows路径分隔符
    if '\\' in path_str:
        parts = path_str.split('\\')
        return parts[-1] if parts else ''
    
    # 处理Linux路径分隔符
    elif '/' in path_str:
        parts = path_str.split('/')
        return parts[-1] if parts else ''
    
    # 如果没有路径分隔符，返回原始字符串（可能是单个文件名）
    return path_str

def parse_existing_topic_id(existing_topic_id: str) -> list:
    if not existing_topic_id or not existing_topic_id.strip():
        return []
        
    if ':' not in existing_topic_id:
        # 处理纯数字 ID 的情况
        return [existing_topic_id.strip()]
    
    # 定义顺序和已添加的值集合
    order = ['deepwiki_code', 'deepwiki_doc', 'deepwiki_topic']
    added_values = set()
    result = []
    
    # 解析输入字符串
    parts = existing_topic_id.split(',')
    key_value_map = {}
    
    for part in parts:
        part = part.strip()
        if not part:
            continue
        key_value = part.split(':')
        if len(key_value) == 2:
            key, value = key_value
            if value and value.strip():  # Only add non-empty values
                key_value_map[key] = value.strip()
    
    # 按指定顺序添加值
    for key in order:
        if key in key_value_map:
            value = key_value_map[key]
            if value not in added_values:
                result.append(value)
                added_values.add(value)
    
    return result


@app.post("/api/models/testConnection", response_model=TestModelResponse)
async def test_model_connection(request: TestModelRequest):
    # 过滤掉authorization中的●符号
    authorization = request.authorization.replace('●', '')
    url = "https://lab.iwhalecloud.com/gpt-proxy/v1/chat/completions"

    payload = json.dumps({
        "model": "doubao-lite-32k",
        "messages": [
            {
                "role": "system",
                "content": "You are a helpful assistant."
            },
            {
                "role": "user",
                "content": "Hello!"
            }
        ],
        "stream": "false"
    })
    headers = {
        'Authorization': f'Bearer {authorization}',
        'content-type': 'application/json'
    }

    response = requests.request("POST", url, headers=headers, data=payload)

    if response.status_code == 200:
        return {"success": True, "message": get_translation("api.messages.connectionSuccessful")}
    else:
        return {"success": False, "message": f"{response.status_code}"}

@app.post("/api/whaleDevCloud/verifyToken")
async def verify_whaledevcloud_token(request: WhaleDevCloudTokenRequest):
    # 过滤掉token中的●符号
    verify_token = request.token.replace('●', '')
    # 构建请求URL
    verify_url = "https://dev.iwhalecloud.com/portal/zcm-cmdb/v1/access-tokens/verify"

    # 调用ZCM-CMDB服务验证token
    headers = {
        'Authorization': 'Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJwdXJwb3NlIjoiZXh0ZXJuYWwiLCJwcm9kdWN0IjoiZGVlcHdpa2kiLCJhcGlzIjpbIi9wb3J0YWwvemNtLWNtZGIvdjEvYWNjZXNzLXRva2Vucy92ZXJpZnkiXSwiZXhwIjo0OTA2Njg3MDc1fQ.MP_lm8T9d9YC36bHFLyXWvPVZqmyeeLynz7yokkt-ou2Y_dj5vpFuRtT0q9q5AIhFpDPbsuDdqMaIYRnXQwcmQ',
        'content-type': 'text/plain'
    }

    response = requests.request("POST", verify_url, headers=headers, data=verify_token)

    # 解析ZCM-CMDB服务响应
    try:
        result = response.json()
    except ValueError:
        raise HTTPException(
            status_code=500,
            detail="Call ZCM-CMDB Service failed"
        )

    # 不区分大小写判断passed值（支持true/TRUE/True等格式）
    passed_value = result.get("passed")
    if isinstance(passed_value, str):
        # 将字符串值转为小写后判断
        is_success = passed_value.lower() == "true"
    else:
        # 非字符串值直接判断（如布尔值true/false）
        is_success = bool(passed_value)

    # 统一响应格式
    if is_success:
        return {
            "success": True,
            "message": get_translation("api.messages.verifySuccessful")
        }
    else:
        return {
            "success": False,
            "message": get_translation("api.messages.verifyFailed")
        }

@app.post("/api/wiki/export")
async def export_wiki(request: WikiExportRequest, current_user: dict = Depends(get_current_user), audit_log=Depends(audit_logger)):
    """
    Export wiki content as Markdown or JSON.

    Args:
        request: The export request containing wiki pages and format

    Returns:
        A downloadable file in the requested format
    """
    try:
        # 权限校验：检查用户是否有导出wiki的权限
        from api.service.priv_checker import can_export_wiki
        with session_scope() as session:
            if not can_export_wiki(session, current_user['id'], request.wiki_id):
                raise HTTPException(status_code=403, detail=get_translation("api.errors.noPermissionToExportWiki"))
        
        logger.info(f"Exporting wiki for {request.repo_url} in {request.format} format")

        # Extract repository name from URL for the filename
        repo_parts = request.repo_url.rstrip('/').split('/')
        repo_name = repo_parts[-1] if len(repo_parts) > 0 else "wiki"
        owner_name = repo_parts[-2] if len(repo_parts) > 2 else "unknown"

        # Get current timestamp for the filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if request.format == "markdown":
            # Generate Markdown content
            content = generate_markdown_export(request.repo_url, request.pages, request.sections, request.rootSections)
            filename = f"{repo_name}_wiki_{timestamp}.md"
            media_type = "text/markdown"
        else:  # JSON format
            # Generate JSON content
            content = generate_json_export(request.repo_url, request.pages, request.sections, request.rootSections)
            filename = f"{repo_name}_wiki_{timestamp}.json"
            media_type = "application/json"

        # Create response with appropriate headers for file download
        response = Response(
            content=content,
            media_type=media_type,
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )

        # 设置审计日志信息
        branch = getattr(request, "branch", "unknow")
        repo_name = repo_name.replace(".git", "")
        audit_log(code="WIKI_EXPORT", party_type="服务", party_id=request.wiki_id, party_name=f"{owner_name}/{repo_name}/{branch}")
        return response
    
    except HTTPException:
        raise

    except Exception as e:
        error_msg = f"Error exporting wiki: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=get_translation("api.errors.errorExportingWiki"))

@app.get("/api/local_repo/structure")
async def get_local_repo_structure(path: str = Query(None, description="Path to local repository")):
    """Return the file tree and README content for a local repository."""
    if not path:
        return JSONResponse(
            status_code=400,
            content={"error": get_translation("api.errors.noPathProvided")}
        )

    if not os.path.isdir(path):
        return JSONResponse(
            status_code=404,
            content={"error": get_translation("api.errors.directoryNotFound").format(path=path)}
        )

    try:
        logger.info(f"Processing local repository at: {path}")
        file_tree_lines = []
        readme_content = ""

        for root, dirs, files in os.walk(path):
            # Exclude hidden dirs/files and virtual envs
            dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__' and d != 'node_modules' and d != '.venv']
            for file in files:
                if file.startswith('.') or file == '__init__.py' or file == '.DS_Store':
                    continue
                rel_dir = os.path.relpath(root, path)
                rel_file = os.path.join(rel_dir, file) if rel_dir != '.' else file
                file_tree_lines.append(rel_file)
                # Find README.md (case-insensitive)
                if file.lower() == 'readme.md' and not readme_content:
                    try:
                        with open(os.path.join(root, file), 'r', encoding='utf-8') as f:
                            readme_content = f.read()
                    except Exception as e:
                        logger.warning(f"Could not read README.md: {str(e)}")
                        readme_content = ""

        file_tree_str = '\n'.join(sorted(file_tree_lines))
        return {"file_tree": file_tree_str, "readme": readme_content}
    except Exception as e:
        logger.error(f"Error processing local repository: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"error": get_translation("api.errors.errorProcessingLocalRepo")}
        )

def generate_markdown_export(repo_url: str, pages: List[WikiPage], sections: Optional[List[dict]] = None, rootSections: Optional[List[str]] = None) -> str:  
    """
    Generate Markdown export of wiki pages with proper heading hierarchy.  

    Args:
        repo_url: The repository URL
        pages: List of wiki pages
        sections: Optional wiki sections structure  
        rootSections: Optional list of root section IDs

    Returns:
        Markdown content as string
    """
    # Start with metadata
    markdown = f"# Wiki Documentation for {repo_url}\n\n"
    markdown += f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

    # Add table of contents with sections  
    markdown += "## Table of Contents\n\n"
    if sections and len(sections) > 0:  
        # Generate hierarchical TOC based on sections
        # If rootSections is provided, only include those sections
        section_ids_to_include = rootSections if rootSections else [s['id'] for s in sections]
        root_sections = [s for s in sections if s['id'] in section_ids_to_include]
        
        for section in root_sections:  
            markdown += f"- **{section['title']}**\n"  
            section_pages = [p for p in pages if p.id in section.get('pages', [])]  
            for page in section_pages:  
                markdown += f"  - [{page.title}](#{page.id})\n"  
        markdown += "\n"  
    else:  
        # Fallback to flat structure  
        for page in pages:
            markdown += f"- [{page.title}](#{page.id})\n"
        markdown += "\n"

    # Add content organized by sections  
    if sections and len(sections) > 0:  
        for section in sections:  
            markdown += f"# {section['title']}\n\n"  
            section_pages = [p for p in pages if p.id in section.get('pages', [])]  
              
            for page in section_pages:  
                markdown += f"<a id='{page.id}'></a>\n\n"  
                markdown += f"## {page.title}\n\n"  
                  
                # Add related pages  
                if hasattr(page, 'relatedPages') and page.relatedPages and len(page.relatedPages) > 0:  
                    markdown += "### Related Pages\n\n"  
                    related_titles = []  
                    for related_id in page.relatedPages:  
                        related_page = next((p for p in pages if p.id == related_id), None)  
                        if related_page:  
                            related_titles.append(f"[{related_page.title}](#{related_id})")  
                      
                    if related_titles:  
                        markdown += "Related topics: " + ", ".join(related_titles) + "\n\n"  
                  
                # Adjust heading levels in page content (base level = 2, so content headings start from ###)  
                adjusted_content = adjust_heading_levels(page.content, base_level=2)  
                markdown += f"{adjusted_content}\n\n"  
                markdown += "---\n\n"  
    else:  
        # Fallback to current flat structure with heading adjustment  
        for page in pages:
            markdown += f"<a id='{page.id}'></a>\n\n"
            markdown += f"## {page.title}\n\n"

            # Add related pages
            if hasattr(page, 'relatedPages') and page.relatedPages and len(page.relatedPages) > 0:  
                markdown += "### Related Pages\n\n"
                related_titles = []
                for related_id in page.relatedPages:
                    related_page = next((p for p in pages if p.id == related_id), None)
                    if related_page:
                        related_titles.append(f"[{related_page.title}](#{related_id})")

                if related_titles:
                    markdown += "Related topics: " + ", ".join(related_titles) + "\n\n"

            # Adjust heading levels in page content  
            adjusted_content = adjust_heading_levels(page.content, base_level=2)  
            markdown += f"{adjusted_content}\n\n"  
            markdown += "---\n\n"

    return markdown

def adjust_heading_levels(content: str, base_level: int = 2) -> str:  
    """  
    调整 Markdown 内容中的标题层级，确保相对于基础层级的正确嵌套  
      
    Args:  
        content: 原始 Markdown 内容  
        base_level: 基础标题层级（页面标题的层级）  
      
    Returns:  
        调整后的 Markdown 内容  
    """  
    import re  
      
    lines = content.split('\n')  
    adjusted_lines = []  
      
    for line in lines:  
        # 匹配标题行（以 # 开头）  
        heading_match = re.match(r'^(#+)\s*(.*)', line)  
        if heading_match:  
            current_hashes = heading_match.group(1)  
            heading_text = heading_match.group(2)  
            current_level = len(current_hashes)  
              
            # 调整层级：基础层级 + 当前层级  
            new_level = base_level + current_level  
            # 限制最大层级为 6（Markdown 标准）  
            new_level = min(new_level, 6)  
              
            adjusted_line = '#' * new_level + ' ' + heading_text  
            adjusted_lines.append(adjusted_line)  
        else:  
            adjusted_lines.append(line)  
      
    return '\n'.join(adjusted_lines)

def generate_json_export(repo_url: str, pages: List[WikiPage], sections: Optional[List[dict]] = None, rootSections: Optional[List[str]] = None) -> str:
    """
    Generate JSON export of wiki pages.

    Args:
        repo_url: The repository URL
        pages: List of wiki pages
        sections: Optional wiki sections structure
        rootSections: Optional list of root section IDs to export

    Returns:
        JSON content as string
    """
    # Create a dictionary with metadata and pages
    export_data = {
        "metadata": {
            "repository": repo_url,
            "generated_at": datetime.now().isoformat(),
            "page_count": len(pages)
        },
        "pages": [page.model_dump() for page in pages]
    }

    # Add sections if provided
    if sections:
        export_data["sections"] = sections

    # Add rootSections if provided
    if rootSections:
        export_data["rootSections"] = rootSections

    # Convert to JSON string with pretty formatting
    return json.dumps(export_data, indent=2)

# Import the simplified chat implementation


# Add the chat_completions_stream endpoint to the main app
app.add_api_route("/api/chat/stream", chat_completions_stream, methods=["POST"])

# Add the WebSocket endpoint
app.add_websocket_route("/ws/chat", handle_websocket_chat)

# --- Wiki Cache API Endpoints ---

@app.get("/api/wiki_cache", response_model=Optional[WikiCacheData])
async def get_cached_wiki(
    owner: str = Query(..., description="Repository owner"),
    repo: str = Query(..., description="Repository name"),
    branch: str = Query(..., description="Repository branch"),
    repo_type: str = Query(..., description="Repository type (e.g., github, gitlab)"),
    language: str = Query(..., description="Language of the wiki content"),
    comprehensive: bool = Query(True, description="是否是全面Wiki"),
    wiki_id: Optional[str] = Query(None, description="Wiki唯一ID"),
    audit_log=Depends(audit_logger)
):
    """
    Retrieves cached wiki data (structure and generated pages) for a repository.
    首先尝试从数据库中获取WikiInfo，如果存在则返回其wiki_data
    如果不存在，则尝试从文件系统中读取缓存
    支持通过wiki_id直接查询
    """
    # Language validation
    supported_langs = configs["lang_config"]["supported_languages"]
    if not supported_langs.__contains__(language):
        language = configs["lang_config"]["default"]

    logger.info(f"Attempting to retrieve wiki cache for {owner}/{repo} ({repo_type}), lang: {language}, wiki_id: {wiki_id}")
    
    # 1. 尝试从数据库中获取
    try:
        with session_scope() as session:
            if wiki_id:
                from api.service.wiki_info_service import get_wiki_info
                wiki_info = get_wiki_info(session, wiki_id)
            else:
                wiki_info = get_wiki_info_by_repo(session, repo_name=repo, owner=owner, branch=branch, language=language, include_data=True)
        
            # 如果找到了完成状态的WikiInfo，且包含wiki_data
            if wiki_info:
                # 设置审计日志数据
                audit_log(code="WIKI_ACCESS", party_type="服务", party_name=f"{owner}/{repo}/{branch}", party_id=wiki_info.wiki_id)

                # 效验是否具备权限
                user_has_access_priv = check_user_wiki_priv(session, wiki_info)
                if not user_has_access_priv:
                    wiki_cache = WikiCacheData(
                        wiki_id=wiki_info.wiki_id,  # 添加wiki_id（字符串）
                        priv_status='forbidden',
                    )
                    return wiki_cache

                logger.info(f"找到WikiInfo记录: id={wiki_info.id}, status={wiki_info.status}")
                wiki_structure = {}
                generated_pages = {}
                wiki_content = None
                
                if wiki_info.status == "completed":
                    # 从新的内容表获取 wiki_data
                    from api.service.wiki_content_service import get_by_wiki_id
                    wiki_content = get_by_wiki_id(wiki_info.wiki_id)
                    if wiki_content and (wiki_content.wiki_structure or wiki_content.wiki_pages):
                        logger.info(f"Found wiki data in ai_dw_wiki_content for {owner}/{repo}, language: {language}")
                        
                        # 记录wiki_content的大小和结构
                        logger.info(f"wiki_content结构: structure={bool(wiki_content.wiki_structure)}, pages={wiki_content.total_pages}")
                    
                        # 处理并确保数据符合模型要求
                        wiki_structure = wiki_content.wiki_structure
                        generated_pages_raw = wiki_content.wiki_pages
                        # 确保wiki_structure有id字段，如果没有则生成一个
                        if "id" not in wiki_structure:
                            wiki_structure["id"] = f"ws-{owner}-{repo}"
                            logger.info(f"Added missing id to wiki_structure: {wiki_structure['id']}")
                        
                        # 确保其他必要字段存在
                        if "title" not in wiki_structure:
                            wiki_structure["title"] = f"{owner}/{repo} Wiki"
                        if "description" not in wiki_structure:
                            wiki_structure["description"] = f"Generated wiki for {owner}/{repo}"
                        
                        # 确保pages中的每个页面都有content字段
                        for page in wiki_structure.get("pages", []):
                            if "content" not in page:
                                page["content"] = ""  # 添加空的content字段
                        
                        # 转换generated_pages格式，从字符串转换为WikiPage对象
                        generated_pages = {}
                        for page_id, page_content in generated_pages_raw.items():
                            # 查找对应的页面信息
                            page_info = next((p for p in wiki_structure.get("pages", []) if p.get("id") == page_id), None)
                            
                            if page_info:
                                # 如果page_content是字符串，创建完整的WikiPage对象
                                if isinstance(page_content, str):
                                    generated_pages[page_id] = WikiPage(
                                        id=page_id,
                                        title=page_info.get("title", f"Page {page_id}"),
                                        content=page_content,
                                        filePaths=page_info.get("filePaths", []),
                                        importance=page_info.get("importance", "medium"),
                                        relatedPages=page_info.get("relatedPages", [])
                                    )
                                else:
                                    # 如果已经是字典或WikiPage对象，确保有所有必要字段
                                    if isinstance(page_content, dict):
                                        # 确保必要字段存在
                                        if "content" not in page_content:
                                            page_content["content"] = ""
                                        if "filePaths" not in page_content:
                                            page_content["filePaths"] = page_info.get("filePaths", [])
                                        if "importance" not in page_content:
                                            page_content["importance"] = page_info.get("importance", "medium")
                                        if "relatedPages" not in page_content:
                                            page_content["relatedPages"] = page_info.get("relatedPages", [])
                                        generated_pages[page_id] = WikiPage(**page_content)
                                    else:
                                        # 如果不是字典也不是字符串，则使用页面信息创建新的WikiPage
                                        logger.warning(f"Unexpected type for generated_pages[{page_id}]: {type(page_content)}")
                                        generated_pages[page_id] = WikiPage(
                                            id=page_id,
                                            title=page_info.get("title", f"Page {page_id}"),
                                            content="",
                                            filePaths=page_info.get("filePaths", []),
                                            importance=page_info.get("importance", "medium"),
                                            relatedPages=page_info.get("relatedPages", [])
                                        )
                    else:
                        logger.warning(f"wiki_content为空或无内容，虽然status为completed")
                else:
                    logger.warning(f"WikiInfo记录状态不为completed或wiki_data为空: status={wiki_info.status}")
                                        
                try:
                    # 将wiki_data转换为WikiCacheData格式返回
                    wiki_cache = WikiCacheData(
                        wiki_id=wiki_info.wiki_id,  # 添加wiki_id（字符串）
                        wiki_structure=wiki_structure or None,
                        generated_pages=generated_pages or None,
                        repo=RepoInfo(
                            owner=owner,
                            repo=repo,
                            type=repo_type,
                            branch=branch,
                            repoUrl=wiki_info.repo_url,
                        ),
                        provider=wiki_info.provider,
                        model=wiki_info.model,
                        ownerId=wiki_info.owner_id,
                    )
                    logger.info(f"成功创建WikiCacheData对象")
                    return wiki_cache
                except Exception as validation_error:
                    # 记录验证错误详情
                    logger.error(f"Failed to create WikiCacheData due to validation error: {validation_error}")
                    # 继续尝试从文件系统读取
                    pass
            else:
                logger.warning(f"未找到仓库的WikiInfo记录: owner={owner}, repo={repo}")
    except Exception as e:
        logger.error(f"Error retrieving wiki data from database: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving wiki data from database")
    
@app.get("/api/wiki_detail")
async def get_wiki_pages(id: int, audit_log=Depends(audit_logger)):
    with session_scope() as session:
        wiki_info: WikiInfo = session.exec(select(WikiInfo).where(WikiInfo.id == id)).first()
        if not wiki_info:
            raise HTTPException(status_code=404, detail=f"Can not find wiki by id {id}")
        # 设置审计日志数据
        audit_log(code="WIKI_ACCESS", party_type="服务", party_name=f"{wiki_info.repo_owner}/{wiki_info.repo_name}/{wiki_info.branch}", party_id=wiki_info.wiki_id)

        # 效验是否具备权限
        user_has_access_priv = check_user_wiki_priv(session, wiki_info)
        if not user_has_access_priv:
            raise HTTPException(status_code=403, detail=f"Sorry, You have not permission to access this wiki.")

        wiki_base = {
            "id": wiki_info.id,
            "wiki_id": wiki_info.wiki_id,
            "repo_type": wiki_info.repo_type,
            "repo_owner": wiki_info.repo_owner,
            "repo_name": wiki_info.repo_name,
            "repo_url": wiki_info.repo_url,
            "branch": wiki_info.branch,
            "owner_id": wiki_info.owner_id,
            "provider": wiki_info.provider,
            "updated_time": wiki_info.updated_time,
            "model": wiki_info.model,
            "comprehensive": wiki_info.comprehensive
        }
        # 先确保结构和页面字典存在，防止后续处理时报错
        wiki_structure, wiki_pages = {}, {}
        generated_pages = {}
        if wiki_info.status == "completed":
            # 优先从磁盘读取Wiki内容，内部会自动回退到数据库
            disk_structure, disk_pages = get_wiki_content(wiki_info)

            if disk_structure:
                # 深拷贝结构，避免直接修改原始对象
                wiki_structure = deepcopy(disk_structure)
                wiki_structure.setdefault("sections", [])
                wiki_structure.setdefault("rootSections", [])
                wiki_structure.setdefault("pages", [])

            # 若磁盘没有结构数据，则兜底从数据库再次获取
            if not wiki_structure:
                db_structure, db_generated_pages = get_wiki_from_db(wiki_info)
                if db_structure:
                    wiki_structure = deepcopy(db_structure)
                    wiki_structure.setdefault("sections", [])
                    wiki_structure.setdefault("rootSections", [])
                    wiki_structure.setdefault("pages", [])
                    # 当磁盘缺失时，使用数据库里的页面内容构建临时字典
                    temp_pages = {}
                    for db_page_id, db_page_data in (db_generated_pages or {}).items():
                        if isinstance(db_page_data, WikiPage):
                            data_dict = db_page_data.dict()
                        elif isinstance(db_page_data, dict):
                            data_dict = dict(db_page_data)
                        else:
                            continue
                        page_title = data_dict.get("title") or db_page_id
                        page_content = data_dict.get("content", "")
                        if page_title:
                            temp_pages[page_title] = page_content
                        if db_page_id:
                            temp_pages.setdefault(db_page_id, page_content)
                    if temp_pages:
                        disk_pages = temp_pages

            # 使用磁盘返回的页面内容字典，用于组装返回值
            disk_pages = disk_pages or {}

            if wiki_structure.get("pages"):
                for page_info in wiki_structure.get("pages", []):
                    if not page_info:
                        continue
                    page_id = page_info.get("id")
                    title = page_info.get("title") or page_id

                    # 先从磁盘读取内容，若缺失则使用结构里的原始内容
                    content = ""
                    if title and title in disk_pages:
                        content = disk_pages.get(title, "")
                    if (not content) and page_id and page_id in disk_pages:
                        content = disk_pages.get(page_id, "")
                    if not content:
                        content = page_info.get("content", "")

                    # 统一构造页面详情字典，确保字段齐全
                    resolved_id = page_id or title
                    page_dict = {
                        "id": resolved_id,
                        "title": title,
                        "content": content,
                        "filePaths": page_info.get("filePaths", []),
                        "importance": page_info.get("importance", "medium"),
                        "relatedPages": page_info.get("relatedPages", []),
                    }

                    if resolved_id:
                        generated_pages[resolved_id] = page_dict
                    if page_id:
                        wiki_pages[page_id] = content
                    if title:
                        # 按标题兜底，兼容旧前端按标题读取
                        wiki_pages.setdefault(title, content)

        return {
            "wiki_info": wiki_base,
            "wiki_structure": wiki_structure,
            "wiki_pages": wiki_pages,
            "generated_pages": generated_pages
        }

def get_wiki_from_db(wiki_info: WikiInfo):
    """
    从数据库获取wiki的内容
    """
    try:
        wiki_structure = {}
        generated_pages = {}
        owner = wiki_info.repo_owner
        repo = wiki_info.repo_name
        language = wiki_info.language
        wiki_content = get_by_wiki_id(wiki_info.wiki_id)
        if wiki_content and (wiki_content.wiki_structure or wiki_content.wiki_pages):
            logger.info(f"Found wiki data in ai_dw_wiki_content for {owner}/{repo}, language: {language}")
            
            # 记录wiki_content的大小和结构
            logger.info(f"wiki_content结构: structure={bool(wiki_content.wiki_structure)}, pages={wiki_content.total_pages}")
        
            # 处理并确保数据符合模型要求
            wiki_structure = wiki_content.wiki_structure
            generated_pages_raw = wiki_content.wiki_pages
            # 确保wiki_structure有id字段，如果没有则生成一个
            if "id" not in wiki_structure:
                wiki_structure["id"] = f"ws-{owner}-{repo}"
                logger.info(f"Added missing id to wiki_structure: {wiki_structure['id']}")
            
            # 确保其他必要字段存在
            if "title" not in wiki_structure:
                wiki_structure["title"] = f"{owner}/{repo} Wiki"
            if "description" not in wiki_structure:
                wiki_structure["description"] = f"Generated wiki for {owner}/{repo}"
            
            # 确保pages中的每个页面都有content字段
            for page in wiki_structure.get("pages", []):
                if "content" not in page:
                    page["content"] = ""  # 添加空的content字段
            
            # 转换generated_pages格式，从字符串转换为WikiPage对象
            generated_pages = {}
            for page_id, page_content in generated_pages_raw.items():
                # 查找对应的页面信息
                page_info = next((p for p in wiki_structure.get("pages", []) if p.get("id") == page_id), None)
                
                if page_info:
                    # 如果page_content是字符串，创建完整的WikiPage对象
                    if isinstance(page_content, str):
                        generated_pages[page_id] = WikiPage(
                            id=page_id,
                            title=page_info.get("title", f"Page {page_id}"),
                            content=page_content,
                            filePaths=page_info.get("filePaths", []),
                            importance=page_info.get("importance", "medium"),
                            relatedPages=page_info.get("relatedPages", [])
                        )
                    else:
                        # 如果已经是字典或WikiPage对象，确保有所有必要字段
                        if isinstance(page_content, dict):
                            # 确保必要字段存在
                            if "content" not in page_content:
                                page_content["content"] = ""
                            if "filePaths" not in page_content:
                                page_content["filePaths"] = page_info.get("filePaths", [])
                            if "importance" not in page_content:
                                page_content["importance"] = page_info.get("importance", "medium")
                            if "relatedPages" not in page_content:
                                page_content["relatedPages"] = page_info.get("relatedPages", [])
                            generated_pages[page_id] = WikiPage(**page_content)
                        else:
                            # 如果不是字典也不是字符串，则使用页面信息创建新的WikiPage
                            logger.warning(f"Unexpected type for generated_pages[{page_id}]: {type(page_content)}")
                            generated_pages[page_id] = WikiPage(
                                id=page_id,
                                title=page_info.get("title", f"Page {page_id}"),
                                content="",
                                filePaths=page_info.get("filePaths", []),
                                importance=page_info.get("importance", "medium"),
                                relatedPages=page_info.get("relatedPages", [])
                            )
        return wiki_structure, generated_pages
    except Exception as ex:
        logger.error(f"Error retrieving wiki data from database: {ex}")
        raise HTTPException(status_code=500, detail="Error retrieving wiki data from database")

def get_wiki_content(wiki_info: WikiInfo):
    """
    磁盘优先读取wiki的结构和markdown页面：
    1. 优先从磁盘（i-doc目录）读取
    2. 如果磁盘不存在或不完整，才从数据库获取并同步到磁盘
    """
    try:
        branch = re.sub(r'[^\w\-_]', '_', wiki_info.branch)
        wiki_path = FilePath(os.path.expanduser(os.path.join("~", ".adalflow", "project_workspace", wiki_info.repo_owner, f"{wiki_info.repo_name}-{branch}", "i-doc", "wiki")))

        wiki_structure, wiki_pages = {}, {}

        # 1. 检查磁盘文件是否存在
        wiki_structure_path = wiki_path.joinpath("wiki_structure.json")
        wiki_pages_path = wiki_path.joinpath("pages")

        # 2. 磁盘优先：如果磁盘文件存在，优先从磁盘读取
        if wiki_structure_path.exists() and wiki_pages_path.exists():
            try:
                # 从磁盘读取结构
                with open(wiki_structure_path, 'r', encoding='utf-8') as structure:
                    wiki_structure = json.load(structure)

                # 从磁盘读取页面
                wiki_pages = {}
                for file in wiki_pages_path.iterdir():
                    if file.is_file() and file.suffix == '.md':
                        with open(file, 'r', encoding='utf-8') as page:
                            wiki_pages[file.stem] = page.read()

                # 验证磁盘数据的完整性
                if wiki_structure and wiki_structure.get("pages") and len(wiki_pages) >= len(wiki_structure.get("pages", [])):
                    logger.info(f"从磁盘成功读取wiki内容: {wiki_path}")
                    return wiki_structure, wiki_pages
                else:
                    logger.warning(f"磁盘数据不完整，将从数据库重新加载: {wiki_path}")
            except Exception as e:
                logger.error(f"读取磁盘文件失败，将从数据库加载: {e}")

        # 3. 磁盘文件不存在或不完整时，从数据库获取
        logger.info(f"从数据库加载wiki内容: {wiki_info.wiki_id}")
        wiki_structure, generated_pages = get_wiki_from_db(wiki_info)

        if not wiki_structure:
            logger.error(f"数据库中未找到wiki内容: {wiki_info.wiki_id}")
            return {}, {}

        # 4. 确保目录存在
        if not wiki_path.exists():
            wiki_path.mkdir(parents=True, exist_ok=True)

        # 5. 同步到磁盘
        try:
            # 保存结构文件
            wiki_structure_path.write_text(data=json.dumps(wiki_structure, ensure_ascii=False), encoding='utf-8')

            # 创建并填充pages目录
            if not wiki_pages_path.exists():
                wiki_pages_path.mkdir(parents=True, exist_ok=True)

            # 从数据库数据构建wiki_pages
            wiki_pages = {}
            for structure_page in wiki_structure.get("pages", []):
                page_id = structure_page.get("id")
                if page_id and page_id in generated_pages:
                    page_data = generated_pages[page_id]
                    # 处理不同格式的页面数据
                    if isinstance(page_data, str):
                        page_content = page_data
                    elif isinstance(page_data, dict):
                        page_content = page_data.get("content", "")
                    elif hasattr(page_data, "content"):
                        page_content = page_data.content
                    else:
                        page_content = ""

                    if page_content:
                        title = structure_page.get("title", page_id)
                        wiki_pages[title] = page_content

                        # 保存到磁盘
                        wiki_page_path = wiki_pages_path.joinpath(f"{title}.md")
                        wiki_page_path.write_text(data=page_content, encoding='utf-8')

            logger.info(f"wiki内容已同步到磁盘: {wiki_path}")

        except Exception as e:
            logger.error(f"同步wiki内容到磁盘失败: {e}")
            # 即使磁盘同步失败，也要返回从数据库获取的数据

        return wiki_structure, wiki_pages

    except Exception as ex:
        logger.error(f"获取wiki内容失败: {ex}")
        # 最后的兜底：尝试直接从数据库获取
        try:
            logger.info(f"兜底方案：直接从数据库获取wiki内容: {wiki_info.wiki_id}")
            return get_wiki_from_db(wiki_info)
        except Exception as db_ex:
            logger.error(f"兜底方案也失败: {db_ex}")
            raise HTTPException(status_code=500, detail=f"Failed to get wiki content: {ex}")

@app.delete("/api/wiki_cache")
async def delete_wiki_cache(
    owner: str = Query(..., description="Repository owner"),
    repo: str = Query(..., description="Repository name"),
    repo_type: str = Query(..., description="Repository type (e.g., github, gitlab)"),
    language: str = Query(..., description="Language of the wiki content"),
    authorization_code: Optional[str] = Query(None, description="Authorization code")
):
    """
    Deletes a specific wiki cache from the file system and database.
    """
    # Language validation
    supported_langs = configs["lang_config"]["supported_languages"]
    if not supported_langs.__contains__(language):
        raise HTTPException(status_code=400, detail=get_translation("api.errors.languageNotSupported"))

    if WIKI_AUTH_MODE:
        logger.info("check the authorization code")
        if WIKI_AUTH_CODE != authorization_code:
            raise HTTPException(status_code=401, detail=get_translation("api.errors.authorizationCodeInvalid"))

    # 1. 从数据库中查找并删除wiki_info
    logger.info(f"Searching for wiki info in DB for {owner}/{repo} ({repo_type}), lang: {language}")
    
    deleted_wiki_ids = []
    branches = set()
    try:
        with session_scope() as session:
            wikis_to_delete = wiki_info_service.search_wikis(
                session,
                repo_owner=owner,
                repo_name=repo,
                repo_type=repo_type,
                language=language
            )

            if wikis_to_delete:
                for wiki in wikis_to_delete:
                    wiki_id = wiki.wiki_id
                    try:
                        branch_value = getattr(wiki, "branch", None)
                        if branch_value:
                            branches.add(branch_value)
                        if wiki_info_service.delete_wiki_info(session, wiki_id):
                            logger.info(f"Successfully deleted wiki_info with id: {wiki_id} from database.")
                            deleted_wiki_ids.append(wiki_id)
                        else:
                            logger.warning(f"Wiki info with id: {wiki_id} not found in DB for deletion.")
                    except Exception as e:
                        logger.error(f"Error deleting wiki_info with id {wiki_id} from database: {e}")
                        # Decide if you want to stop or continue
                        # For now, we continue to delete file cache
            else:
                logger.warning(f"No wiki_info found in database for {owner}/{repo} ({repo_type}), lang: {language}")
    except Exception as e:
        logger.error(f"Error during database operations: {e}")
        # Continue with file deletion even if database operations fail

    from adalflow.utils import get_adalflow_default_root_path
    import shutil
    import stat

    def remove_readonly(func, path, exc):
        if isinstance(exc, PermissionError):
            os.chmod(path, stat.S_IWRITE)
            try:
                func(path)
            except Exception as e:
                print(f"Failed to delete {path} again: {e}")

    # 3. 删除代码仓库
    repo_path = os.path.join(get_adalflow_default_root_path(), "repos", repo)
    if os.path.exists(repo_path):
        try:
            shutil.rmtree(repo_path, onexc=remove_readonly)
            logger.info(f"Successfully deleted repo {repo_path}")
        except Exception as e:
            logger.error(f"Failed to delete repo {repo_path}: {e}")
    else:
        logger.warning(f"Repo not found: {repo_path}")
    
    topic_ids: List[str] = []
    # 4. 清理数据库中的 Topic 映射
    try:
        with session_scope() as session:
            branch_filters = {b for b in branches if b}
            repo_query = select(AiDwGitRepository).where(
                AiDwGitRepository.repo_owner == owner,
                AiDwGitRepository.repo_name == repo,
                AiDwGitRepository.repo_type == repo_type,
            )
            if branch_filters:
                repo_query = repo_query.where(AiDwGitRepository.branch.in_(list(branch_filters)))

            repo_rows = session.exec(repo_query).all()
            for repo_row in repo_rows:
                if repo_row.code_topic_id:
                    topic_ids.append(repo_row.code_topic_id)
                    repo_row.code_topic_id = None
                    repo_row.updated_time = datetime.utcnow()
                    session.add(repo_row)
    except Exception as e:
        logger.error(f"Failed to clear topic mapping for {owner}/{repo}: {e}")

    # 5. 删除docchain里的文档（暂时不删除docchain里的数据）
    # await delete_topics(topic_ids)

    return {"message": get_translation("api.messages.deletedWikiSuccessfully").format(owner=owner, repo=repo, language=language, count=len(deleted_wiki_ids))}


@app.get("/health")
async def health_check():
    """Health check endpoint for Docker and monitoring"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "deepwiki-api"
    }

@app.get("/")
async def root():
    """Root endpoint to check if the API is running and list available endpoints dynamically."""
    # Collect routes dynamically from the FastAPI app
    endpoints = {}
    for route in app.routes:
        if hasattr(route, "methods") and hasattr(route, "path"):
            # Skip docs and static routes
            if route.path in ["/openapi.json", "/docs", "/redoc", "/favicon.ico"]:
                continue
            # Group endpoints by first path segment
            path_parts = route.path.strip("/").split("/")
            group = path_parts[0].capitalize() if path_parts[0] else "Root"
            method_list = list(route.methods - {"HEAD", "OPTIONS"})
            for method in method_list:
                endpoints.setdefault(group, []).append(f"{method} {route.path}")

    # Optionally, sort endpoints for readability
    for group in endpoints:
        endpoints[group].sort()

    return {
        "message": get_translation("api.messages.welcomeToStreamingApi"),
        "version": "1.0.0",
        "endpoints": endpoints
    }

@app.post("/api/repo/branches")
async def getRepoBranches(request_data: RepoBranchesRequest):
    logger.info(f"Get branches for {request_data.repo_url}")

    clone_url = request_data.repo_url
    if request_data.token:
        parsed = urlparse(request_data.repo_url)
        # Determine the repository type and format the URL accordingly
        if request_data.repo_type == "github":
            # Format: https://{token}@github.com/owner/repo.git
            # Works for both github.com and enterprise GitHub domains
            clone_url = urlunparse((parsed.scheme, f"{request_data.token}@{parsed.netloc}", parsed.path, '', '', ''))
        elif request_data.repo_type == "gitlab":
            # Format: https://oauth2:{token}@gitlab.com/owner/repo.git
            clone_url = urlunparse((parsed.scheme, f"oauth2:{request_data.token}@{parsed.netloc}", parsed.path, '', '', ''))
        elif request_data.repo_type == "bitbucket":
            # Format: https://{token}@bitbucket.org/owner/repo.git
            clone_url = urlunparse((parsed.scheme, f"{request_data.token}@{parsed.netloc}", parsed.path, '', '', ''))
        elif request_data.repo_type == "whaleDevCloud":
            # Format: https://{token}@github.com/owner/repo.git
            # Works for both github.com and enterprise GitHub domains
            clone_url = urlunparse((parsed.scheme, f"{request_data.token}:x-oauth-basic@{parsed.netloc}", parsed.path, '', '', ''))
        logger.info("Using access token for authentication")
    
    logger.info(f"{clone_url}")
    branches = []
    try:
        result = subprocess.run(
            ["git", "ls-remote", "--heads", clone_url],
            capture_output=True, text=True
        )
        lines = result.stdout.strip().split("\n")
        logger.info(f"{lines}")
        branches = [line.split("refs/heads/")[1] for line in lines if "refs/heads/" in line]
        logger.info(f"branches: {branches}")
    except Exception as e:
        logger.error(f"Failed to get repo branches: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get branches for repo: {request_data.repo_url}")
    
    return branches

def get_docchain_manager():
    """
    返回全局DocChainManager实例
    
    Returns:
        DocChainManager: DocChain管理器全局实例
    """
    return docchain_manager

def get_wiki_generator(docchain_manager: DocChainManager = Depends(get_docchain_manager)):
    """
    使用全局db_service创建WikiGenerator
    """
    return WikiGenerator(db_service=db_service, docchain_manager=docchain_manager)

def sub_repos_to_str(sub_repos: Optional[List[SubRepoInfo]]) -> str:
    """
    将 sub_repos（Pydantic对象列表或None）转为JSON字符串，便于存库。
    """
    if sub_repos:
        return json.dumps([sub.model_dump() for sub in sub_repos])
    return "[]"

def parse_sub_repos_str(sub_repos_str: str) -> List[dict]:
    """
    将数据库中的 sub_repos 字符串解析为 dict 列表。
    """
    if not sub_repos_str:
        return []
    try:
        data = json.loads(sub_repos_str)
        if not data:
            return []
        # 如果需要对象，可以用 dataclass.asdict，但前端用 dict 最方便
        return data
    except Exception:
        logger.error(f"sub_repos解析失败:{sub_repos_str}")
        return []

@app.post("/api/wiki/generate")
async def generate_wiki(request: WikiGenerationRequest, 
                        generator: WikiGenerator = Depends(get_wiki_generator),
                        audit_log=Depends(audit_logger)):
    """
    启动新的Wiki生成任务，或返回已存在的任务/Wiki信息
    
    逻辑：
    1. 验证Git URL格式
    2. 获取当前用户信息
    3. 检查同一仓库同一分支是否已存在WikiInfo
       - 如果存在且状态为completed，则提示已存在完成的Wiki
       - 如果存在且状态为pending/processing，则提示已有任务正在处理
       - 如果存在且状态为failed，则可以重用该WikiInfo，创建新任务
    4. 如果不存在相应的WikiInfo，则创建新的WikiInfo和关联的Job
    """
    # 初始化变量，确保在所有执行路径中都定义了它们
    job_id = None
    wiki_info = None
    wiki_id = None
    id = None
    org_created_by = None

    try:
        # 1. 验证Git URL格式
        from api.utils.git_utils import validate_git_url
        is_valid, error_message = validate_git_url(request.repo_url)
        if not is_valid:
            raise HTTPException(status_code=400, detail=get_translation("api.errors.gitUrlFormatIncorrect").format(message=error_message))
        
        # 2. 获取当前用户信息
        from api.middleware.auth_middleware import get_current_user
        current_user = get_current_user()
        user_id = None
        user_code = None
        user_name = None
        if current_user:
            user_id = current_user.get("id") or current_user.get("user_id")
            user_code = (
                current_user.get("user_code")
                or current_user.get("username")
                or current_user.get("userCode")
            )
            user_name = (
                current_user.get("user_name")
                or current_user.get("name")
                or current_user.get("username")
            )
            logger.info(f"当前用户ID: {user_id}")
        else:
            logger.warning("未获取到当前用户信息，将使用默认用户ID")
            user_id = 1  # 默认用户ID

        # 3. 权限校验：检查用户是否有生成wiki的权限
        with session_scope() as session:
            from api.service.priv_checker import can_generate_wiki
            if not can_generate_wiki(session, user_id):
                raise HTTPException(status_code=403, detail=get_translation("api.errors.noPermissionToGenerateWiki"))
        # 提取仓库URL和分支信息
        repo_url = request.repo_url
        branch = request.branch
        
        # 从仓库URL中提取所有者和仓库名
        repo_owner, repo_name, host = git_utils.extract_repo_info(request.repo_url)

        # 设置审计日志数据
        audit_log(code="WIKI_CREATE", party_type="服务", party_name=f"{repo_owner}/{repo_name}/{branch}")

        # sub_repos 转为字符串存库
        sub_repos_str = sub_repos_to_str(request.sub_repos)

        
        # 1. 首先检查是否已存在相同仓库同一分支的WikiInfo
        with session_scope() as session:
            # 查找同一仓库同一分支的WikiInfo
            existing_wiki_info = session.exec(select(WikiInfo.id, WikiInfo.wiki_id, WikiInfo.status, WikiInfo.created_by).where(WikiInfo.repo_url == repo_url, WikiInfo.branch == branch)).first()
            
            if existing_wiki_info:
                # 获取已存在WikiInfo的状态和ID
                id = existing_wiki_info.id
                wiki_id = existing_wiki_info.wiki_id
                current_status = existing_wiki_info.status
                org_created_by = existing_wiki_info.created_by
                
                # 根据状态进行不同处理
                if current_status == "completed":
                    # 如果Wiki已经生成完成，则返回提示信息
                    logger.info(f"相同仓库和分支的Wiki已生成完成: {repo_url}, branch: {branch}, wiki_id: {wiki_id}")
                    return {
                        "wiki_id": wiki_id,
                        "status": "completed",
                        "message": get_translation("api.messages.wikiAlreadyGenerated"),
                        "exists": True
                    }
                elif current_status in ["pending", "processing"]:
                    # 如果有正在处理的任务，获取任务ID
                    active_job = (
                        session.query(WikiJob)
                        .filter(WikiJob.wiki_info_id == wiki_id, 
                                WikiJob.status.in_(["pending", "pending_resume", "processing", "resuming"]))
                        .order_by(WikiJob.updated_time.desc())
                        .first()
                    )
                    
                    if active_job:
                        # 如果有活跃任务，返回任务信息
                        logger.info(f"相同仓库和分支有任务正在处理: {repo_url}, branch: {branch}, job_id: {active_job.id}")
                        return {
                            "job_id": active_job.id,
                            "wiki_id": wiki_id,
                            "status": active_job.status,
                            "message": get_translation("api.messages.taskAlreadyProcessing"),
                            "exists": True
                        }
                
                # 如果WikiInfo状态为failed或没有活跃任务，则可以重用该WikiInfo，创建新任务
                logger.info(f"重用现有WikiInfo创建新任务: {repo_url}, branch: {branch}, wiki_id: {wiki_id}")
                
                # 更新WikiInfo状态为pending
                update_wiki_info(
                    session, 
                    wiki_id, 
                    status="pending",
                    error_message=None  # 清除之前的错误信息
                )
                
                # 更新DocChain相关信息（如果提供了新的值）
                if request.existing_topic_id or request.existing_topic_id_code or request.existing_topic_id_doc:
                    update_params = {}
                    if request.existing_topic_id:
                        update_params["topic_id"] = request.existing_topic_id
                    if request.existing_topic_id_code:
                        update_params["topic_id_code"] = request.existing_topic_id_code
                    if request.existing_topic_id_doc:
                        update_params["topic_id_doc"] = request.existing_topic_id_doc
                    
                    update_wiki_info(session, wiki_id, **update_params)
            else:
                # 如果不存在WikiInfo，创建新记录
                logger.info(f"创建新的WikiInfo: {repo_url}, branch: {branch}, 用户ID: {user_id}")
                wiki_info = create_wiki_info(
                    session=session,
                    repo_url=request.repo_url,
                    branch=request.branch,
                    repo_owner=repo_owner,
                    repo_name=repo_name,
                    repo_type=request.repo_type,
                    sub_repos=sub_repos_str,
                    topic_id=request.existing_topic_id,
                    topic_id_code=request.existing_topic_id_code,
                    topic_id_doc=request.existing_topic_id_doc,
                    provider=request.model_settings.get("provider", "whalecloud"),
                    model=request.model_settings.get("model", "gemini-2.5-flash"),
                    language=request.language,
                    excluded_dirs=request.excluded_dirs,
                    excluded_files=request.excluded_files,
                    included_dirs=request.included_dirs,
                    included_files=request.included_files,
                    comprehensive=request.comprehensive,
                    created_by=user_id,  # 使用当前用户ID
                    owner_id=user_id,
                    comments=request.comments
                )
                wiki_id = wiki_info.wiki_id
                id = wiki_info.id

                # 拓展仓库元数据
                extend_wiki_info(session=session, wiki_id=id, repo_metadata=request.repo_metadata)

                if request.tag_ids:
                    add_wiki_tag(session, id, request.tag_ids, user_id)
            
            # 设置审计日志数据
            audit_log(party_id=wiki_id)

            # 无论是新建还是重用，都创建新的Job任务
            job = create_job(
                session, 
                repo_url=request.repo_url,
                branch=request.branch,
                wiki_info_id=wiki_id,  # 设置关联的wiki_info_id
                model_settings=request.model_settings,
                sub_repos_str=sub_repos_str, # 设置子仓库信息
                token=request.token,
                created_by=user_id,  # 设置创建者
                excluded_dirs=request.excluded_dirs,
                excluded_files=request.excluded_files,
                included_dirs=request.included_dirs,
                included_files=request.included_files
            )
            job_id = job.id
            logger.info(f"已创建新任务, job_id: {job_id}, wiki_id: {wiki_id}, 创建者: {user_id}")

        # 使用WikiJobManager启动任务
        try:
            from api.wiki.wiki_job_manager import get_job_manager, JobContext
            
            # 构建kwargs字典
            kwargs = {}
            if request.existing_topic_id:
                kwargs["existing_topic_id"] = request.existing_topic_id
            if request.existing_topic_id_code:
                kwargs["existing_topic_id_code"] = request.existing_topic_id_code
            if request.existing_topic_id_doc:
                kwargs["existing_topic_id_doc"] = request.existing_topic_id_doc
            if request.excluded_dirs:
                kwargs["excluded_dirs"] = request.excluded_dirs
            if request.excluded_files:
                kwargs["excluded_files"] = request.excluded_files
            if request.included_dirs:
                kwargs["included_dirs"] = request.included_dirs
            if request.included_files:
                kwargs["included_files"] = request.included_files
            if request.custom_instructions:
                kwargs["custom_instructions"] = request.custom_instructions

            # 传递用户上下文信息，便于Langfuse追踪
            if user_id is not None:
                kwargs["user_id"] = user_id
            if user_code:
                kwargs["user_code"] = user_code
            if user_name:
                kwargs["user_name"] = user_name

            # 创建JobContext
            # 过滤掉token中的●符号
            cleaned_token = request.token.replace('●', '') if request.token else request.token
            job_context = JobContext(
                job_id=job_id,
                wiki_id=wiki_id,
                repo_url=request.repo_url,
                repo_type=request.repo_type,
                branch=request.branch,
                token=cleaned_token,
                language=request.language,
                sub_repos=sub_repos_str,
                model_settings=request.model_settings,
                comprehensive=request.comprehensive,
                kwargs=kwargs
            )
            
            # 提交到JobManager
            job_manager = get_job_manager()
            success = await job_manager.submit_job(job_context)
            
            if not success:
                # 提交失败，更新状态
                with session_scope() as session:
                    update_job(session, job_id, status="failed", error_message=get_translation("api.errors.taskSubmitToJobManagerFailed"))
                    update_wiki_info(session, wiki_id, status="failed", error_message=get_translation("api.errors.taskSubmitToJobManagerFailed"))
                raise HTTPException(status_code=500, detail=get_translation("api.errors.taskSubmitToJobManagerFailed"))
            
            logger.info(f"任务已成功提交到JobManager，任务ID: {job_id}, Wiki ID: {wiki_id}")

            # 新增ai_dw_wiki_user_role记录
            with (session_scope() as session):
                if org_created_by and org_created_by != user_id:
                    # 删除wiki已经分配给旧用户角色记录
                    session.query(WikiUserRole).filter(
                        WikiUserRole.wiki_id == id,
                        WikiUserRole.user_id == org_created_by
                    ).delete()
                    # 更新wiki的创建者
                    update_wiki_info(session, wiki_id, created_by=user_id)
                add_wiki_user_role(
                    session=session,
                    user_id=user_id,
                    wiki_id=id,
                    role_id=5
            )
            
        except Exception as e:
            logger.error(f"提交任务到JobManager失败: {e}")
            # 提交失败，更新状态
            with session_scope() as session:
                update_job(session, job_id, status="failed", error_message=f"{get_translation('api.errors.taskSubmitToJobManagerFailed')}: {str(e)}")
                update_wiki_info(session, wiki_id, status="failed", error_message=f"{get_translation('api.errors.taskSubmitToJobManagerFailed')}: {str(e)}")
            raise HTTPException(status_code=500, detail=get_translation("api.errors.taskSubmitToJobManagerFailed"))

        return {
            "job_id": job_id, 
            "wiki_id": wiki_id, 
            "exists": False,
            "message": get_translation("api.messages.newTaskCreated")
        }
        
    except HTTPException as e:
        # HTTP异常直接传播，不做处理（如URL验证失败等）
        logger.warning(f"HTTP异常在generate_wiki中: {e.detail}")
        raise e
    except Exception as e:
        # 如果处理失败，更新相关记录的状态
        if job_id or wiki_id:
            try:
                with session_scope() as session:
                    if job_id:
                        update_job(session, job_id, status="failed", error_message=str(e))
                    if wiki_id:
                        update_wiki_info(session, wiki_id, status="failed", error_message=str(e))
            except Exception as cleanup_error:
                logger.error(f"Failed to cleanup job/wiki after error: {cleanup_error}")
        
        logger.error(f"Error starting wiki generation: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.errorStartingWikiGeneration"))

@app.post("/api/wiki/refresh")
async def refresh_wiki(request: WikiRefreshRequest, 
                      generator: WikiGenerator = Depends(get_wiki_generator),
                      audit_log=Depends(audit_logger)):
    """
    刷新已存在的Wiki：
    1. git pull更新代码
    2. 检测变更文件
    3. 增量同步到DocChain
    4. 重新生成wiki结构和内容
    """
    job_id = None
    wiki_id = None

    # 设置审计日志数据
    audit_log(code="WIKI_UPDATE", party_type="服务", party_id=request.wiki_id)
    
    try:
        # 获取当前用户信息
        from api.middleware.auth_middleware import get_current_user
        current_user = get_current_user()
        user_id = None
        user_code = None
        user_name = None
        if current_user:
            user_id = current_user.get("id") or current_user.get("user_id")
            user_code = (
                current_user.get("user_code")
                or current_user.get("username")
                or current_user.get("userCode")
            )
            user_name = (
                current_user.get("user_name")
                or current_user.get("name")
                or current_user.get("username")
            )
            logger.info(f"当前用户ID: {user_id}")
        else:
            raise HTTPException(status_code=401, detail=get_translation("api.errors.authenticationRequired"))

        # 在session内部提取所有需要的数据
        wiki_data = None
        with session_scope() as session:
            # 根据wiki_id字段查询WikiInfo
            wiki_info = get_wiki_info(session, request.wiki_id)
            if not wiki_info:
                raise HTTPException(status_code=404, detail=get_translation("api.errors.wikiNotExist").format(wiki_id=request.wiki_id))
            
            # 权限校验：检查用户是否有刷新wiki的权限
            from api.service.priv_checker import can_refresh_wiki
            if not can_refresh_wiki(session, user_id, request.wiki_id):
                raise HTTPException(status_code=403, detail=get_translation("api.errors.noPermissionToRefreshWiki"))
            
            # 设置审计日志数据
            audit_log(party_name=f"{wiki_info.repo_owner}/{wiki_info.repo_name}/{wiki_info.branch}")
            
            # 验证token是否有权限
            if not git_utils.is_token_valid(wiki_info.repo_url, request.token):
                return HTTPException(status_code=403, detail="Your token does not have pull permission.")
            
            # 提取所有需要的数据到字典中，避免session外部访问对象属性
            wiki_data = {
                "id": wiki_info.id,  # 数据库主键（int）
                "wiki_id": wiki_info.wiki_id,  # wiki标识符（string）
                "repo_url": wiki_info.repo_url,
                "repo_type": wiki_info.repo_type,
                "branch": wiki_info.branch,
                "language": wiki_info.language,
                "sub_repos": None,  # 新表结构中没有sub_repos字段，需要从关系表查询
            }
            
            # 查询子仓库信息
            from api.service.git_repository_service import list_by_wiki
            sub_repos_list = list_by_wiki(wiki_info.wiki_id, only_main=False)
            if sub_repos_list:
                # 转换为旧格式的JSON字符串
                import json
                sub_repos_data = []
                for repo in sub_repos_list:
                    sub_repos_data.append({
                        "url": repo.repo_url,
                        "branch": repo.branch
                    })
                wiki_data["sub_repos"] = json.dumps(sub_repos_data)
            else:
                wiki_data["sub_repos"] = None
            
            wiki_id = wiki_data["wiki_id"]
            
            # 检查是否已有正在进行的刷新任务（使用wiki_id字符串）
            active_refresh_job = (
                session.query(WikiJob)
                .filter(WikiJob.wiki_info_id == str(wiki_data["id"]), 
                        WikiJob.job_type == 1,  # 1表示刷新任务
                        WikiJob.status.in_(["pending", "pending_resume", "processing", "resuming"]))
                .order_by(WikiJob.updated_time.desc())
                .first()
            )
            
            if active_refresh_job:
                return {
                    "job_id": active_refresh_job.id,
                    "wiki_id": wiki_id,
                    "status": active_refresh_job.status,
                    "message": get_translation("api.messages.wikiRefreshTaskProcessing"),
                    "exists": True
                }
            
            # 创建新的刷新任务（wiki_info_id使用字符串格式的id）
            refresh_request_payload = {
                "is_refresh": True,
                "force_refresh": request.force_refresh,
                "refresh_pages": request.refresh_pages or [],
                "rebuild_structure": request.rebuild_structure,
                "custom_instructions": request.custom_instructions or "",
            }

            refresh_job = WikiJob(
                repo_url=wiki_data["repo_url"],
                branch=wiki_data["branch"],
                sub_repos=wiki_data["sub_repos"],
                wiki_info_id=str(wiki_data["wiki_id"]),  # 转换为字符串
                token=request.token,  # 从请求或wiki_info中获取
                status="pending",
                job_type=1,  # 1表示刷新任务
                progress=0,
                language=wiki_data["language"],
                comprehensive=request.comprehensive,
                model_settings=request.model_settings,
                result={"request": refresh_request_payload},
                created_by=user_id,
                updated_by=user_id
            )
            
            session.add(refresh_job)
            session.commit()
            job_id = refresh_job.id
            
            logger.info(f"创建刷新任务成功，job_id: {job_id}, wiki_id: {wiki_id}")
        
        # 获取job manager并提交任务
        from api.wiki.wiki_job_manager import get_job_manager
        job_manager = get_job_manager()
        
        if job_manager is None:
            raise HTTPException(status_code=503, detail=get_translation("api.errors.jobManagerNotStarted"))
        
        # 构建JobContext for refresh（使用提取的数据）
        from api.wiki.wiki_job_manager import JobContext
        job_context = JobContext(
            job_id=job_id,
            wiki_id=wiki_id,
            repo_url=wiki_data["repo_url"],
            repo_type=wiki_data["repo_type"],
            branch=wiki_data["branch"],
            token=request.token,  # 如需要可从wiki_info获取
            language=wiki_data["language"],
            model_settings=request.model_settings,
            comprehensive=request.comprehensive,
            sub_repos=wiki_data["sub_repos"]
        )
        
        # 设置为refresh模式，并添加force_refresh和refresh_pages参数
        job_context.kwargs = {
            "is_refresh": True,
            "force_refresh": request.force_refresh,
            "refresh_pages": request.refresh_pages,
            "rebuild_structure": request.rebuild_structure,
            "custom_instructions": request.custom_instructions,
        }

        if user_id is not None:
            job_context.kwargs["user_id"] = user_id
        if user_code:
            job_context.kwargs["user_code"] = user_code
        if user_name:
            job_context.kwargs["user_name"] = user_name

        # 提交任务
        success = await job_manager.submit_job(job_context)
        
        if not success:
            # 兜底处理：可能在极短并发窗口内被队列扫描器/其他实例先锁定，导致当前提交返回失败。
            # 此时任务其实已在处理，不应直接返回503给前端。
            try:
                from api.wiki.distributed_lock import get_distributed_lock_service
                lock_svc = get_distributed_lock_service()
                lock_info = await lock_svc.get_job_lock_info(job_id)
            except Exception:
                lock_info = None

            with session_scope() as session:
                # 重新拉取任务状态
                from api.service.wiki_service import get_job as _get_job
                db_job = _get_job(session, job_id)

            # 若检测到锁存在或状态已变更为处理中/恢复中/排队，则视为已成功提交，返回200
            if lock_info or (db_job and str(db_job.status) in [
                "processing", "resuming", "pending", "pending_resume"
            ]):
                msg = get_translation("api.messages.wikiRefreshSubmitted")
                if request.refresh_pages:
                    msg = get_translation("api.messages.wikiPartialRefreshSubmitted").format(pages=', '.join(request.refresh_pages))
                elif request.force_refresh:
                    msg = get_translation("api.messages.wikiForceRefreshSubmitted")

                return {
                    "job_id": job_id,
                    "wiki_id": wiki_id,
                    "status": str(db_job.status) if db_job else "processing",
                    "message": msg,
                    "refresh": True,
                    "force_refresh": request.force_refresh,
                    "refresh_pages": request.refresh_pages
                }

            # 确认不是并发占用导致的“假失败”，再做清理并返回503
            with session_scope() as session:
                job_to_delete = session.query(WikiJob).filter(WikiJob.id == job_id).first()
                if job_to_delete:
                    session.delete(job_to_delete)
                session.commit()
            raise HTTPException(status_code=503, detail=get_translation("api.errors.taskSubmissionFailed"))
        
        # 构建返回消息
        if request.refresh_pages:
            message = get_translation("api.messages.wikiPartialRefreshSubmitted").format(pages=', '.join(request.refresh_pages))
        elif request.force_refresh:
            message = get_translation("api.messages.wikiForceRefreshSubmitted")
        else:
            message = get_translation("api.messages.wikiRefreshSubmitted")
        
        return {
            "job_id": job_id,
            "wiki_id": wiki_id,
            "status": "pending",
            "message": message,
            "refresh": True,
            "force_refresh": request.force_refresh,
            "refresh_pages": request.refresh_pages
        }
        
    except HTTPException:
        raise
    except Exception as e:
        # 错误处理
        if job_id and wiki_id:
            try:
                with session_scope() as session:
                    # 删除创建的job
                    job_to_delete = session.query(WikiJob).filter(WikiJob.id == job_id).first()
                    if job_to_delete:
                        session.delete(job_to_delete)
                    
                    # 更新WikiInfo状态（使用wiki_id字符串）
                    update_wiki_info(session, wiki_id, status="failed", error_message=str(e))
                    session.commit()
            except Exception as cleanup_error:
                logger.error(f"Failed to cleanup refresh job after error: {cleanup_error}")
        
        logger.error(f"Error starting wiki refresh: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.errorStartingWikiRefresh"))

@app.get("/api/wiki/info/{wiki_id}/basic")
async def get_wiki_basic_info_api(
    wiki_id: str = Path(..., description="Wiki ID")
):
    """
    获取Wiki基本信息（轻量级查询，不包含标签和扩展信息）

    适用于只需要基本信息的场景，性能更好
    """
    try:
        user_info = get_current_user()
        user_id = user_info.get('id') if user_info else None

        with session_scope() as session:
            if not can_read_wiki(session, user_id, wiki_id):
                raise HTTPException(status_code=403, detail=get_translation("api.errors.noPermissionToAccessWiki"))
            
            basic_info = get_wiki_basic_info(wiki_id)
            if not basic_info:
                raise HTTPException(status_code=404, detail=get_translation("api.errors.wikiNotFound"))

            return {
                "code": 200,
                "message": get_translation("common.success"),
                "data": basic_info
            }
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        logger.error(f"Error getting wiki info: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetWikiInfo"))
    
@app.get("/api/wiki/info/{wiki_id}")
async def get_wiki_info_api(wiki_id: str):
    """
    根据wiki_id查询Wiki详情

    返回完整的Wiki信息，包括：
    - 基本信息（仓库信息、创建时间等）
    - 创建人信息
    - 标签信息
    - 产品线相关信息
    - 发布包和解决方案信息

    Args:
        wiki_id: Wiki唯一标识
    """
    try:
        user_info = get_current_user()
        user_id = user_info.get('id') if user_info else None

        with session_scope() as session:
            # 校验用户是否有权限查询
            if not can_read_wiki(session, user_id, wiki_id):
                raise HTTPException(status_code=403, detail=get_translation("api.errors.noPermissionToAccessWiki"))
            
            wiki_detail = get_wiki_detail(wiki_id)
            if not wiki_detail:
                raise HTTPException(status_code=404, detail="Wiki not found")

            return {
                "code": 200,
                "message": get_translation("common.success"),
                "data": wiki_detail
            }
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        logger.error(f"Error getting wiki info: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetWikiInfo"))


@app.get("/api/wiki/info/{wiki_id}/nocheck")
async def get_wiki_info_api(wiki_id: str):
    """
    根据wiki_id查询Wiki详情,不效验权限

    返回完整的Wiki信息，包括：
    - 基本信息（仓库信息、创建时间等）
    - 创建人信息
    - 标签信息
    - 产品线相关信息
    - 发布包和解决方案信息

    Args:
        wiki_id: Wiki唯一标识
    """
    try:
        wiki_detail = get_wiki_detail(wiki_id)
        if not wiki_detail:
            raise HTTPException(status_code=404, detail="Wiki not found")

        return {
            "code": 200,
            "message": get_translation("common.success"),
            "data": wiki_detail
        }
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        logger.error(f"Error getting wiki info: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetWikiInfo"))

@app.get("/api/wiki/projects")
async def get_wiki_projects(
    keyword: Optional[str] = Query(None, description="搜索关键字"),
    page: int = Query(1, ge=1, description="页码"),
    view_type: int = Query(1, ge=1, le=2, description="视图类型"),
    page_size: int = Query(50, ge=1, le=100, description="每页大小"),
):
    """
    搜索Wiki列表
    
    支持按以下字段模糊搜索：
    - 创建人工号、姓名
    - 产品线、产品版本、产品名称
    - 发布包名称、解决方案名称
    - 仓库名称、仓库所有者
    - 标签名称
    """
    try:
        result = search_wikis_4_unlogin(keyword=keyword, page=page, page_size=page_size, view_type=view_type)
    
        return {
            "code": 200,
            "message": "success",
            "data": result
        }
    except Exception as e:
        logger.error(f"获取Wiki项目列表失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetWikiProjects"))
    
@app.get("/api/wiki/projects/mcp", operation_id="get_projects_wiki")
async def get_projects_wiki(
    key_word: Annotated[str, Query(description="关键字")] = "",
    offset: Annotated[int, Query(description="偏移量")] = 0,
    limit: Annotated[int, Query(description="限制数量")] = 20
):
    """
    从数据库中获取已生成的Wiki产品服务清单。

    入参描述:
    - key_word: [可选] 关键字
        * 类型: str
        * 说明: 支持模糊匹配的关键字，模糊匹配产品服务仓库名称和产品服务所有者
        * 示例: "deepwiki-open"
        * 匹配规则: 支持部分匹配，不区分大小写
    
    - offset: [可选] 偏移量
        * 类型: int
        * 默认值: 0
        * 说明: 用于分页读取产品服务清单

    - limit: [可选] 限制数量
        * 类型: int
        * 默认值: 20
        * 说明: 每页返回的产品服务数量，可根据需要调整

    返回值:
    - 类型: str
    - 内容: 格式化的产品服务清单，包含产品服务仓库地址、分支、产品服务名称、产品服务所有者、产品服务类型、产品服务主题ID、产品服务主题ID代码、产品服务主题ID文档等信息

    $exampleData: key_word: "deepwiki-open" 请搜索产品服务清单包含"deepwiki"的产品服务

    """
    try:
        with session_scope() as session:
            wikis = search_wikis_like(session, statuses=["pending", "completed"], include_data=False, repo_name=key_word, offset=offset, limit=limit)
            
            result = []
            for wiki in wikis:
                # 查询主仓库的 topic_id_code
                from api.service.git_repository_service import list_by_wiki
                main_repos = list_by_wiki(wiki.wiki_id, only_main=True)
                main_topic_id_code = main_repos[0].code_topic_id if main_repos else None
                
                result.append({
                    "wiki_id": wiki.wiki_id,
                    "repo_url": wiki.repo_url,
                    "branch": wiki.branch,
                    "repo_name": wiki.repo_name,
                    "repo_owner": wiki.repo_owner,
                    "repo_type": wiki.repo_type,
                    "topic_id": wiki.project_topic_id,  # 新表中的项目topic
                    "topic_id_code": main_topic_id_code,  # 主仓库的代码topic
                    "topic_id_doc": None,  # 新表结构中没有doc topic
                })
            
            return result
    except Exception as e:
        logger.error(f"获取Wiki项目列表失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetWikiProjects"))

@app.delete("/api/wiki/projects")
async def delete_wiki_project(
    owner: str = Body(..., description="仓库所有者"),
    repo: str = Body(..., description="仓库名称"),
    repo_type: str = Body(..., description="仓库类型"),
    language: str = Body(..., description="Wiki语言"),
    audit_log=Depends(audit_logger)
):
    """
    删除指定的Wiki项目
    """
    try:
        with session_scope() as session:
            # 使用新表查询匹配的Wiki记录
            from api.model.wiki_info import WikiInfo
            statement = select(WikiInfo.wiki_id, WikiInfo.branch).where(
                WikiInfo.repo_owner == owner,
                WikiInfo.repo_name == repo,
                WikiInfo.repo_type == repo_type,
                WikiInfo.language == language
            )
            wiki_info = session.exec(statement).first()

            if not wiki_info:
                raise HTTPException(status_code=404, detail=get_translation("api.errors.projectNotFound"))

            # 设置审计日志数据
            audit_log(party_type="服务", party_id=wiki_info.wiki_id, party_name=f"{owner}/{repo}/{wiki_info.branch}")
            
            # 删除相关的所有记录
            from api.service.wiki_info_service import delete_wiki_info
            if delete_wiki_info(session, wiki_info.wiki_id):
                session.commit()
            else:
                raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToDeleteWikiProject"))
            
            return {"success": True, "message": get_translation("api.messages.wikiProjectDeletedSuccessfully")}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除Wiki项目失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToDeleteWikiProject"))

@app.post('/api/share_snapshot')
async def save_snapshot(request: Request):
    data = await request.json()
    # data 应包含 researchStages 和 messages
    expires_in = None
    if data.get('expires_in'):
        expires_in = data.get('expires_in') * 24 * 60
    else:
        expires_in = SHARE_EXPIRES_MINUTES

    chat_sid = data.get('sessionId')
    last_message_id = data.get('lastMessageId')

    # 后端容错：如果传入的 lastMessageId 在库中不存在，则回退为该会话最后一条已落库消息
    if last_message_id:
        try:
            last_msg = select_chat_history_by_msg_sid(last_message_id)
            if last_msg is None and chat_sid:
                chat_session = select_chat_session_by_chat_sid(chat_sid)
                if chat_session:
                    chat_id = chat_session.get('id')
                    histories = select_chat_histories_by_chat_id(chat_id)
                    if histories:
                        last_message_id = histories[-1].msg_sid
        except Exception:
            pass

    payload = {
        'chat_sid': chat_sid,
        'last_message_id': last_message_id
    }
    token = create_jwt_token(payload, timedelta(minutes=expires_in))
    return token

@app.get('/api/share_snapshot')
async def get_snapshot(token: str):
    try:
        res = verify_jwt_token(token).get('payload')
        if res:
            chat_sid = res.get('chat_sid')
            last_message_id = res.get('last_message_id')
            last_msg = select_chat_history_by_msg_sid(last_message_id)
            chat_session = select_chat_session_by_chat_sid(chat_sid)
            chat_id = chat_session.get('id')
            chat_history = select_chat_histories_by_chat_id(chat_id)
            chat_history_list = []
            for chat in chat_history[:]:
                if chat.id > last_msg.id:
                    chat_history.remove(chat)
                    continue
                if chat.deep_research and chat.deep_research_iter == 'research':
                    chat_history.remove(chat)
                created_at_ms = int(chat.created_date.timestamp() * 1000) if getattr(chat, "created_date", None) else None
                formatted_message = chat.model_dump()
                formatted_message['createdAt'] = created_at_ms
                chat_history_list.append(formatted_message)
            research_stages = []
    except Exception as e:
        logger.info(f"{get_translation('api.errors.sharePageError')}: {e}")
        return JSONResponse(status_code=400,content=get_translation("api.errors.tokenErrorWrongPage"))
    return {
        'messages': chat_history_list,
        'researchStages': research_stages
    }

@app.get("/api/announcements", response_model=AnnouncementsResponse)
async def get_announcements(
    type: Optional[str] = Query(None, description="公告类型过滤")
):
    """
    获取公告列表
    
    Args:
        type: 可选的公告类型过滤参数
        
    Returns:
        包含公告列表的响应
    """
    try:
        from api.model.announcement import Announcement
        result = []
        with session_scope() as session:
            if type:
                statement = select(Announcement).where(Announcement.state == 1, Announcement.type == type).order_by(Announcement.seq)
            else:
                statement = select(Announcement).where(Announcement.state == 1).order_by(Announcement.seq)
            announcements = session.exec(statement).all()
            for announcement in announcements:
                result.append(AnnouncementResponse(
                    id=announcement.id,
                    title=announcement.title,
                    type=announcement.type,
                    content=announcement.content,
                    seq=announcement.seq
                ))
        logger.info(f"Retrieved {len(result)} announcements")
        return AnnouncementsResponse(announcements=result)
        
    except Exception as e:
        logger.error(f"Error fetching announcements: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch announcements")
  
@app.post("/api/chat/completions/stream", operation_id="product_assistance")
async def chat_completions(request: ChatRequest, http_request: Request):
    """
    提供deepwiki的产品服务问答服务，包括gemini-cli和model-ask两种模式

    入参描述:
    - model: [可选] 模型名称
        * 类型: str
        * 默认值: "gemini-2.5-flash"
        * 说明: 指定的提供商下的模型名称，当provider为gemini-cli时，model可以为gemini-2.5-flash、gemini-2.5-pro中的一个。当provider为whalecloud时，model可以为gemini-2.5-flash、gemini-2.5-pro、DeepSeek-R1等模型中的一个。
        * 示例: "gemini-2.5-pro", "gemini-2.5-flash"

    - messages: [必填] 聊天消息列表
        * 类型: List[ChatMessage]
        * 默认值: []
        * 说明: 聊天消息历史记录，包含角色(role)和内容(content)，最多三条消息
        * 格式: [{"id": "chatcmpl-c5157813-6332-4b4c-8b3a-7d49e2da017f", "role": "user", "content": "问题内容"}, {"id": "chatcmpl-c5157812-6332-4b4c-8b3a-7d49e2da017f", "role": "assistant", "content": "回答内容"}]

    - repo_url: [必填] 仓库地址
        * 类型: str
        * 默认值: "https://git-nj.iwhalecloud.com/ptdev01/whale-deepwiki.git"
        * 说明: 代码仓库的URL地址，用于上下文理解
        * 示例: "https://github.com/user/repo.git"

    - api_key: [必填] API密钥
        * 类型: str
        * 说明: 用于访问AI模型服务的API密钥
        * 安全提示: 请妥善保管，不要在日志中记录

    - provider: [必填] 模型提供商
        * 类型: str
        * 默认值: "gemini-cli"
        * 说明: AI模型提供商标识
        * 可选值: "gemini-cli", "whalecloud"

    - branch: [必填] 分支
        * 类型: str
        * 默认值: "master"
        * 说明: 代码仓库的分支，用于上下文理解
        * 示例: "master", "dev"

    - wiki_id: [可选] Wiki ID
        * 类型: str
        * 默认值: ""
        * 说明: Wiki ID，用于上下文理解
        * 示例: "**********"

    返回值:
    - 类型: StreamingResponse
    - 内容: 产品相关信息的问答结果

    $exampleData: model: "gemini-2.5-flash", messages: [{"id": "chatcmpl-c5157813-6332-4b4c-8b3a-7d49e2da017f", "role": "user", "content": "2222"}], api_key: "**********", provider: "gemini-cli", repo_url: "https://git-nj.iwhalecloud.com/ptdev01/whale-deepwiki.git", branch: "master"  调用deepwiki的产品问答服务

    """
    
    chat_request = ChatCompletionRequest(
        model=request.model,
        messages=request.messages,
        repo_url=request.repo_url,
        provider=request.provider,
        session_id=str(uuid.uuid4()),
        api_key=request.api_key,
        branch=request.branch,
        wiki_id=request.wiki_id if request.wiki_id else None,
        caller=2
    )
    return await chat_completions_stream(chat_request, http_request)

@app.get("/api/chat/history")
async def get_chat_history(session_id: str = Query(..., description="会话ID")):
    """
    根据会话ID获取聊天历史记录
    """
    try:
        # 检查session_id是否为空
        if not session_id or session_id.strip() == "":
            raise HTTPException(status_code=400, detail=get_translation("api.errors.sessionIdRequired"))
        
        # 检查用户是否有权限访问此聊天记录
        # 这里可以根据业务需求添加更详细的权限检查逻辑
        
        # 先通过session_id(chat_sid)获取chat_session对象
        chat_session = select_chat_session_by_chat_sid(session_id)

        if not chat_session:
            logger.warning(f"Chat session not found for session_id: {session_id}")
            return []
        
        # 从chat_session中获取chat_id，然后查询聊天历史
        chat_id = chat_session.get('id')
        if not chat_id:
            logger.warning(f"Chat id not found in chat session for session_id: {session_id}")
            return []


        # 查询聊天历史记录
        from api.service.chat_history_service import select_chat_histories_by_chat_id
        chat_histories = select_chat_histories_by_chat_id(chat_id)
        
        # 转换为前端期望的格式，使用msg_sid作为消息id（与SSE保持一致）
        formatted_messages = []
        latest_model = None
        latest_provider = None
        
        for history in chat_histories:
            # 使用msg_sid作为消息ID，如果为空则使用数据库ID作为后备
            message_id = history.msg_sid if history.msg_sid else str(history.id)
            # 将数据库中的创建时间转换为毫秒时间戳，便于前端展示
            created_at_ms = int(history.created_date.timestamp() * 1000) if getattr(history, "created_date", None) else None
            formatted_message = {
                "id": message_id,
                "role": history.role,
                "content": history.content,
                "tool_calls": history.tool_calls,
                "createdAt": created_at_ms,
                "provider": history.provider,
                "model": history.model,
                "file_references": history.file_references,
                "command_params": history.command_params,
                "msg_data": history.msg_data
            }
            formatted_messages.append(formatted_message)
        
            # 记录最新的模型和提供商信息（来自最后一条助手消息）
            if history.role == "assistant":
                latest_model = history.model
                latest_provider = history.provider
        
        # 返回消息历史和模型信息
        return {
            "messages": formatted_messages,
            "model": latest_model,
            "provider": latest_provider
        }
    except Exception as e:
        logger.error(f"Error getting chat history for session_id {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetChatHistory"))

@app.get("/api/wiki/info")
def get_chat_session_info(session_id: str):
    """
    根据session_id获取Wiki信息（不包含wiki_data字段）
    如果会话不存在，返回空数据而不是404错误
    """
    try:
        from api.service.wiki_info_service import get_wiki_info_without_data_by_id
        from api.database.base import session_scope
        
        logger.info(f"Getting chat session info for session_id: {session_id}")
        
        # 1. 通过session_id查询chat_session表，获取wiki_id
        chat_session = select_chat_session_by_chat_sid(session_id)
        logger.info(f"Chat session result: {chat_session}")
        
        if not chat_session:
            # 会话不存在时返回空数据，而不是404错误
            # 这是正常情况，比如第一次从wiki页面跳转时还没有创建会话
            logger.info(f"Chat session not found for session_id: {session_id}, returning empty data")
            return {
                "repo_url": "",
                "branch": "master",
                "repo_owner": "",
                "repo_name": "",
                "repo_type": "WhaleDevCloud",
                "provider": "gemini-cli",
                "model": "gemini-2.5-flash",
                "language": "zh",
                "wiki_id": "",
                "status": "not_found"
            }
        
        wiki_id = chat_session.get('wiki_id')
        logger.info(f"Wiki ID from chat session: {wiki_id}")
        
        if not wiki_id:
            logger.warning(f"Wiki ID is None or empty for session_id: {session_id}")
            return {
                "repo_url": "",
                "branch": "master",
                "repo_owner": "",
                "repo_name": "",
                "repo_type": "WhaleDevCloud",
                "provider": "gemini-cli",
                "model": "gemini-2.5-flash",
                "language": "zh",
                "wiki_id": "",
                "status": "wiki_id_missing"
            }

        user_info = get_current_user()
        user_id = 1
        if user_info:
            user_id = user_info.get('id')
            created_by = chat_session.get('created_by')
            if user_id != created_by:
                return {
                    "repo_url": "",
                    "branch": "master",
                    "repo_owner": "",
                    "repo_name": "",
                    "repo_type": "WhaleDevCloud",
                    "provider": "gemini-cli",
                    "model": "gemini-2.5-flash",
                    "language": "zh",
                    "wiki_id": "",
                    "status": "not_current_user"
                }
                
        with session_scope() as session:
            wiki_info = get_wiki_info_by_primary(session, wiki_id)

            if not wiki_info:
                # Wiki信息不存在时也返回空数据
                logger.warning(f"Wiki info not found for wiki_id: {wiki_id}, returning empty data")
                return {
                    "repo_url": "",
                    "branch": "master",
                    "repo_owner": "",
                    "repo_name": "",
                    "repo_type": "WhaleDevCloud",
                    "provider": "gemini-cli",
                    "model": "gemini-2.5-flash",
                    "language": "zh",
                    "wiki_id": wiki_id,
                    "status": "wiki_not_found"
                }

            user_has_access_priv = check_user_wiki_priv(session, wiki_info)
            if not user_has_access_priv:
                return {
                    "repo_url": "",
                    "branch": "master",
                    "repo_owner": "",
                    "repo_name": "",
                    "repo_type": "WhaleDevCloud",
                    "provider": "gemini-cli",
                    "model": "gemini-2.5-flash",
                    "language": "zh",
                    "wiki_id": wiki_id,
                    "status": "forbidden"
                }
            
            # 返回仓库信息
            result = {
                "repo_url": wiki_info.repo_url,
                "branch": wiki_info.branch,
                "repo_owner": wiki_info.repo_owner,
                "repo_name": wiki_info.repo_name,
                "repo_type": wiki_info.repo_type,
                "provider": wiki_info.provider,
                "model": wiki_info.model,
                "language": wiki_info.language,
                "wiki_id": wiki_info.wiki_id,
                "status": wiki_info.status
            }
            logger.info(f"Returning wiki info: {result}")
            return result
            
    except Exception as e:
        logger.error(f"Error getting wiki info for session_id {session_id}: {str(e)}")
        # 发生异常时也返回空数据，而不是500错误
        return {
            "repo_url": "",
            "branch": "master",
            "repo_owner": "",
            "repo_name": "",
            "repo_type": "WhaleDevCloud",
            "provider": "gemini-cli",
            "model": "gemini-2.5-flash",
            "language": "zh",
            "wiki_id": "",
            "status": "error"
        }

@app.get("/api/wiki/projects-with-role")
async def get_wiki_projects_with_role(
    keyword: Optional[str] = Query(None, description="搜索关键字"),
    view_type: int = Query(None, description="视图类型"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=100, description="每页大小"),
):
    """
    搜索Wiki列表
    
    支持按以下字段模糊搜索：
    - 创建人工号、姓名
    - 产品线、产品版本、产品名称
    - 发布包名称、解决方案名称
    - 仓库名称、仓库所有者
    - 标签名称
    """
    try:
        user_info = get_current_user()
        user_id = None
        if user_info:
            user_id = user_info.get('id')

        result = search_wikis_4_login(user_id=user_id, keyword=keyword, page=page, page_size=page_size, view_type=view_type)

        return {
        "code": 200,
        "message": "success",
        "data": result
        }
    except Exception as e:
        logger.error(f"获取Wiki项目列表失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetWikiProjects"))

@app.get("/api/wiki/grant/users")
async def get_wiki_users_grant(
        wiki_id: Optional[str] = Query(None, description="wiki info id"),
        key_word: str = Query(default=None),
        page_num: int = Query(default=None),
        page_size: int = Query(default=20),
        filter: str = Query(default=None),
):
    """
    从数据库中获取用户列表，剔除已经被赋予角色的用户
    """
    try:
        with session_scope() as session:
            UsersResponse = select_users_for_grant(session, wiki_id=wiki_id, key_word=key_word, filter=filter ,page_num=page_num,page_size = page_size )
            return UsersResponse
    except Exception as e:
        logger.error(f"获取用户列表失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetUserList"))

@app.post("/api/wiki/grant")
async def grant(request: Request, audit_log=Depends(audit_logger), current_user: dict = Depends(get_current_user)):
    data = await request.json()
    wiki_id = data.get('wiki_id')
    users = data.get('users')
    role_id = data.get('role_id')
    visibility = data.get('visibility')
    try:
        with session_scope() as session:
            # 使用新表查询Wiki信息
            from api.model.wiki_info import WikiInfo
            wiki_info = session.exec(select(WikiInfo.id, WikiInfo.wiki_id, WikiInfo.repo_owner, WikiInfo.repo_name, WikiInfo.branch).where(WikiInfo.id == wiki_id)).first()

            if wiki_info:
                # 设置审计日志数据
                audit_log(code="WIKI_GRANT", party_type="服务", party_id=wiki_info.wiki_id, \
                        party_name=f"{wiki_info.repo_owner}/{wiki_info.repo_name}/{wiki_info.branch}", \
                        oper_data={"users": users, "role_id": role_id})
                update_wiki_info(session, wiki_info.wiki_id, visibility=1 if visibility else 2)
                wiki_tag = get_wiki_tag_by_wiki_id_and_tag_id(session, wiki_info.id, 1)
                if visibility:
                    if not wiki_tag:
                        add_wiki_tag(session, wiki_info.id, [1], current_user.get('id'))
                else:
                    if wiki_tag:
                        delete_wiki_tag_by_wiki_id_and_tag_id(session, wiki_info.id, 1)
                
                if users:
                    res = batch_add_wiki_user_role(session, users, wiki_id, role_id)
                else:
                    res = True
                return res

    except Exception as e:
        logger.error(f"批量授权失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGrantBatch"))

@app.delete("/api/wiki/grant")
async def grant(request: Request, audit_log=Depends(audit_logger)):
    data = await request.json()
    wiki_id = data.get('wiki_id')
    user_id = data.get('user_id')
    role_id = data.get('role_id')
    visibility = data.get('visibility')
    try:
        with session_scope() as session:
            # 使用新表查询Wiki信息
            from api.model.wiki_info import WikiInfo
            wiki_info = session.exec(select(WikiInfo.id, WikiInfo.wiki_id, WikiInfo.repo_owner, WikiInfo.repo_name, WikiInfo.branch).where(WikiInfo.id == wiki_id)).first()

            if wiki_info:
                # 设置审计日志数据
                audit_log(party_type="服务", party_id=wiki_info.wiki_id, \
                        party_name=f"{wiki_info.repo_owner}/{wiki_info.repo_name}/{wiki_info.branch}", \
                        oper_data={"user_id": user_id, "role_id": role_id})
                update_wiki_info(session, wiki_info.wiki_id, visibility=1 if visibility else 2)

                return delete_wiki_user_role(session, user_id, wiki_id, role_id)

    except Exception as e:
        logger.error(f"批量授权失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGrantBatch"))
    
@app.delete("/api/wiki/project/{wiki_id}")
async def delete_wiki_project_byid(
        wiki_id: str,
        current_user: dict = Depends(get_current_user),
        audit_log=Depends(audit_logger)
):
    """
    删除指定的Wiki项目
    """
    try:
        with session_scope() as session:
            # 使用通用权限检查方法,校验是否有删除权限
            if not can_delete_wiki(session, current_user['id'], wiki_id):
                raise HTTPException(status_code=403, detail=get_translation("api.errors.noPermissionToDeleteProject"))
            # 使用新表查询匹配的Wiki记录
            from api.model.wiki_info import WikiInfo
            statement = select(
                WikiInfo.id,
                WikiInfo.wiki_id,
                WikiInfo.repo_owner,
                WikiInfo.repo_name,
                WikiInfo.branch,
                WikiInfo.status,
                WikiInfo.owner_id
            ).where(WikiInfo.wiki_id == wiki_id)
            wiki_info = session.exec(statement).first()

            if not wiki_info:
                raise HTTPException(status_code=404, detail=get_translation("api.errors.projectNotFound"))

            # 收集与Wiki相关的用户工号，用于后续清理用户私有工作空间
            user_codes: Set[str] = set()
            try:
                from api.model.wiki_user_role import WikiUserRole
                from api.service.user_service import select_user_info_by_id

                role_user_ids = session.exec(
                    select(WikiUserRole.user_id).where(WikiUserRole.wiki_id == wiki_info.id)
                ).scalars().all()
                for role_user_id in role_user_ids:
                    user_info = select_user_info_by_id(session, role_user_id)
                    if user_info and getattr(user_info, "user_code", None):
                        user_codes.add(user_info.user_code)
            except Exception as role_error:
                logger.error(f"收集Wiki用户角色信息失败: {role_error}")

            # 将Wiki拥有者也纳入待清理范围，避免残留目录
            owner_id = getattr(wiki_info, "owner_id", None)
            if owner_id:
                try:
                    from api.service.user_service import select_user_info_by_id

                    owner_info = select_user_info_by_id(session, owner_id)
                    if owner_info and getattr(owner_info, "user_code", None):
                        user_codes.add(owner_info.user_code)
                except Exception as owner_error:
                    logger.error(f"收集Wiki拥有者信息失败: {owner_error}")

            # 在正式删除前清理所有关联的任务，避免生成流程继续运行
            related_jobs = session.exec(
                select(WikiJob).where(WikiJob.wiki_info_id == wiki_info.wiki_id)
            ).all()
            if related_jobs:
                active_statuses = {"pending", "pending_resume", "processing", "resuming", "paused"}
                pending_cancel_jobs = [job for job in related_jobs if str(job.status) in active_statuses]

                if pending_cancel_jobs:
                    try:
                        from api.wiki.wiki_job_manager import get_job_manager
                        job_manager = get_job_manager()
                    except Exception as e:
                        logger.error(f"获取WikiJobManager失败，无法停止正在运行的任务: {e}")
                        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToDeleteWikiProject"))

                    # 调用JobManager批量取消任务，确保不会继续生成
                    cancel_result = await job_manager.cancel_jobs_by_wiki(wiki_info.wiki_id)
                    if cancel_result.get("failed"):
                        logger.error(f"取消Wiki任务失败: {cancel_result['failed']}")
                        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToDeleteWikiProject"))

                # 无论任务是否需要取消，都删除数据库中的任务记录，避免遗留脏数据
                for job in related_jobs:
                    session.delete(job)
                logger.info(f"删除Wiki前已清理关联任务记录，数量: {len(related_jobs)}")

            # 设置审计日志数据
            audit_log(code="WIKI_DELETE", party_type="服务", party_id=wiki_info.wiki_id, \
                    party_name=f"{wiki_info.repo_owner}/{wiki_info.repo_name}/{wiki_info.branch}")
            
            # 使用统一的删除方法删除所有相关记录
            from api.service.wiki_info_service import delete_wiki_info
            if delete_wiki_info(session, wiki_id):
                session.commit()
            else:
                raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToDeleteWikiProject"))

            # 先清理磁盘数据，确保Wiki目录与用户私有目录被移除
            delete_wiki_files(wiki_info, user_codes)
            
            return {"success": True, "message": get_translation("api.messages.wikiProjectDeletedSuccessfully")}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除Wiki项目失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToDeleteWikiProject"))

def delete_wiki_files(wiki_info: WikiInfo, user_codes: Optional[Set[str]] = None):
    """
    删除Wiki对应的磁盘目录，包括i-doc、o-doc、project-gemini、代码仓库目录以及用户私有工作区。
    """
    try:
        # 兼容Row和模型对象的属性访问
        owner = getattr(wiki_info, "repo_owner", None)
        repo_name = getattr(wiki_info, "repo_name", None)
        branch = getattr(wiki_info, "branch", None) or "main"

        if not owner or not repo_name:
            logger.warning("删除Wiki文件时缺少仓库基础信息，跳过磁盘清理")
            return

        # 通过路径映射服务获取各类目录的物理路径
        path_service = get_path_mapping_service()
        project_root = FilePath(path_service.get_project_workspace_path(owner, repo_name, branch))
        repo_root = FilePath(path_service.get_code_path(owner, repo_name, branch))

        # 拆解待删除的具体目录
        targets: List[FilePath] = [
            project_root,
            repo_root,
        ]

        # 追加每个用户的私有工作区目录
        workspace_dirs: List[FilePath] = []
        if user_codes:
            for user_code in user_codes:
                try:
                    workspace_path = FilePath(path_service.get_workspace_path(owner, repo_name, branch, user_code))
                    targets.append(workspace_path)
                    workspace_dirs.append(workspace_path)
                except Exception as ws_error:
                    logger.error(f"解析用户 {user_code} 的工作区路径失败: {ws_error}")

        # 逐个删除指定目录
        for target_dir in targets:
            try:
                if target_dir.exists():
                    shutil.rmtree(target_dir)
                    logger.info(f"已删除目录: {target_dir}")
            except Exception as delete_error:
                logger.error(f"删除目录 {target_dir} 失败: {delete_error}")

        # 定义辅助方法用于递归清理空目录
        def cleanup_empty_dirs(start_path: Optional[FilePath], stop_path: FilePath):
            """
            向上递归删除空目录，直到遇到边界目录。
            """
            if not start_path:
                return

            current = start_path
            while current and current != stop_path:
                if stop_path not in current.parents:
                    break
                if current.exists():
                    if current.is_dir():
                        try:
                            next(current.iterdir())
                        except StopIteration:
                            current.rmdir()
                            current = current.parent
                            continue
                        except Exception as inspect_error:
                            logger.debug(f"检查目录 {current} 时发生异常: {inspect_error}")
                            break
                        else:
                            break
                    else:
                        break
                else:
                    current = current.parent
                    continue
                break

        # 清理项目工作区和代码目录下可能残留的空目录
        cleanup_empty_dirs(project_root, FilePath(path_service.project_workspace_path))
        cleanup_empty_dirs(FilePath(repo_root.parent) if repo_root else None, FilePath(path_service.repos_path))
        for workspace_dir in workspace_dirs:
            cleanup_empty_dirs(workspace_dir, FilePath(path_service.workspace_path))

        logger.info("Wiki磁盘目录清理完成")
    except Exception as ex:
        logger.error(f"删除Wiki文件失败: {ex}")

    
@app.get("/api/chat/session")
async def get_chat_sessions_by_user_and_wiki(
    wiki_id: str = Query(..., description="Wiki ID"),
    pageNum: int = Query(1, ge=1, description="页码，从1开始"),
    pageSize: int = Query(10, ge=1, le=100, description="每页条数，默认10，最大100"),
    search: str = Query(None, description="搜索关键字，可选")
):
    """
    根据用户ID和wiki ID分页查询会话，支持搜索，只返回title和chat_sid
    """
    user_info = get_current_user()
    user_id = user_info.get('id') if user_info else None
    sessions, total = select_chat_sessions_by_user_and_wiki(user_id, wiki_id, pageNum, pageSize, search)
    return {
        "total": total,
        "pageNum": pageNum,
        "pageSize": pageSize,
        "search": search,
        "sessions": [{"title": s.title, "id": s.chat_sid} for s in sessions]
    }


@app.post("/api/chat/session/update_title")
async def update_chat_session_title(request: ChatSessionUpdateRequest, audit_log=Depends(audit_logger)):
    """
    更新聊天会话标题
    """

    # 设置审计日志数据
    audit_log(code="CHAT_UPDATE", party_type="用户", party_id=request.chat_sid)
    user_info = get_current_user()
    user_id = user_info.get('id') if user_info else None
    try:
        # 直接调用 service 层统一的数据库操作
        success = update_chat_session(request.chat_sid, request.title, user_id)
        if success:
            return {"success": True, "message": get_translation("api.messages.chatSessionTitleUpdatedSuccessfully")}
        else:
            raise HTTPException(status_code=404, detail=get_translation("api.errors.chatSessionNotFound"))
    except Exception as e:
        logger.error(f"Error updating chat session title: {str(e)}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToUpdateChatSessionTitle"))

@app.delete("/api/chat/session/delete")
async def delete_chat_session(chat_sid: str = Query(..., description="Chat session ID"), audit_log=Depends(audit_logger)):
    """
    删除聊天会话（逻辑删除）
    """

    # 设置审计日志数据
    audit_log(code="CHAT_DELETE", party_type="用户", party_id=chat_sid)
    user_info = get_current_user()
    user_id = user_info.get('id') if user_info else None
    try:
        success = delete_chat_session_by_sid(chat_sid, user_id)
        if success:
            return {"success": True, "message": get_translation("api.messages.chatSessionDeletedSuccessfully")}
        else:
            raise HTTPException(status_code=404, detail=get_translation("api.errors.chatSessionNotFound"))
    except Exception as e:
        logger.error(f"Error deleting chat session: {str(e)}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToDeleteChatSession"))


@app.get("/api/wiki/grant/users/{wiki_id}")
async def grant_users(wiki_id: str):
    try:
        with session_scope() as session:
            creator_alias = aliased(UserInfo)
            # 构建查询
            query = (
                select(
                    UserInfo.id,
                    UserInfo.user_name,
                    UserInfo.user_code,
                    Role.role_code,
                    WikiUserRole.created_date,
                    creator_alias.user_name.label('creator_name'),
                    creator_alias.user_code.label('creator_code')
                )
                .select_from(WikiUserRole)
                .join(UserInfo, WikiUserRole.user_id == UserInfo.id)
                .join(Role, WikiUserRole.role_id == Role.id)
                .join(creator_alias, WikiUserRole.created_by == creator_alias.id)
                .where(WikiUserRole.wiki_id == wiki_id)
                .order_by(WikiUserRole.created_date.desc())
            )
            # 执行查询
            results = session.execute(query).all()

            # 格式化结果
            users_list = []
            for row in results:
                users_list.append({
                    "user_id": row.id,
                    "user_name": row.user_name,
                    "user_code": row.user_code,
                    "role_code": row.role_code,
                    "created_date": row.created_date,
                    "creator_info": {
                        "creator_name": row.creator_name,
                        "creator_code": row.creator_code
                    }
                })

            return users_list

    except Exception as e:
        logger.error(f"获取授权用户列表失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetGrantedUsers"))
    
@app.get("/api/users")
async def get_users(search: str, dept: str, page: int, size: int):
    """
    获取所有用户列表
    """
    try:
        with session_scope() as session:
            filters = []
            if search:
                filters.append(or_(UserInfo.user_name.like(f"%{search}%"), UserInfo.user_code.like(f"%{search}%")))
            if dept:
                filters.append(UserInfo.dept == dept)
            total = session.query(func.count(UserInfo.id)).filter(*filters).scalar()

            stmt = (select(
                    UserInfo.id,
                    UserInfo.user_name,
                    UserInfo.user_code,
                    UserInfo.dept,
                    UserInfo.org,
                    UserInfo.job,
                    UserInfo.state,
                    func.group_concat(Role.role_name).label('roles')
                )
                .select_from(UserInfo)
                .outerjoin(UserRole, UserInfo.id == UserRole.user_id)
                .outerjoin(Role, UserRole.role_id == Role.id)
                .group_by(UserInfo.id))

            if filters:
                stmt = stmt.where(and_(*filters))
            
            stmt = stmt.limit(size).offset(page * size)

            result = session.execute(stmt)
            columns = result.keys()
            rows = result.fetchall()
            users = [dict(zip(columns, row)) for row in rows]
            return { "total": total, "data": [{**user} for user in users] }
    except Exception as ex:
        logger.error(f"Failed to get users, cause by: {ex}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetUserList"))

@app.patch("/api/users/state")
async def modify_user_state(request: ModifyUserStateRequest):
    """
    修改用户状态
    """
    try:
        with session_scope() as session:
            session.query(UserInfo).filter(UserInfo.id == request.id).update({"state": request.state})
    except Exception as ex:
        logger.error(f"Failed to modify user state, cause by: {ex}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToUpdateUserState"))
    
@app.get("/api/roles")
async def get_roles():
    """
    获取所有角色列表
    """
    try:
        with session_scope() as session:
            roles = session.query(Role).where(Role.role_type == "S").all()
            return [Role(**role.model_dump()) for role in roles]
    except Exception as ex:
        logger.error(f"Failed to get roles, cause by: {ex}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetRoleList"))

@app.get("/api/departments")
async def get_departments():
    """
    获取所有部门列表
    """
    try:
        with session_scope() as session:
            departments = session.query(UserInfo.dept).group_by(UserInfo.dept).all()
            return [department[0] for department in departments]
    except Exception as ex:
        logger.error(f"Failed to get departments, cause by: {ex}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetDepartmentList"))

@app.get("/api/users/{user_id}/roles")
async def get_user_roles(user_id: int):
    """
    获取用户角色列表
    """
    try:
        with session_scope() as session:
            user_roles = session.query(UserRole).filter(UserRole.user_id == user_id).all()
            role_ids = [user_role.role_id for user_role in user_roles]
            roles = session.query(Role).filter(Role.id.in_(role_ids)).all()
            return [Role(**role.model_dump()) for role in roles]
    except Exception as ex:
        logger.error(f"Failed to modify user state, cause by: {ex}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetUserRoles"))

@app.post("/api/users/{user_id}/roles")
async def modify_user_roles(user_id: int, request: ModifyUserRolesRequest):
    """
    修改用户角色
    """
    try:
        new_role_ids = request.role_ids
        with session_scope() as session:
            # 获取用户当前的角色列表
            current_user_roles = session.query(UserRole).filter(UserRole.user_id == user_id).all()
            current_user_role_ids = [role.role_id for role in current_user_roles]
            # 删除
            removed = list(set(current_user_role_ids) - set(new_role_ids))
            if removed:
                session.query(UserRole).filter(UserRole.user_id == user_id, UserRole.role_id.in_(removed)).delete()
            # 新增
            added = list(set(new_role_ids) - set(current_user_role_ids))
            if added:
                new_user_roles = [UserRole(user_id=user_id, role_id=new_role_id) for new_role_id in added]
                session.add_all(new_user_roles)
            # 清空redis中用户角色信息
            delete_user_roles_from_redis(user_id=user_id)
    except Exception as ex:
        logger.error(f"Failed to modify user roles, cause by: {ex}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToModifyUserRoles"))
    

@app.post("/api/wiki/grant-for-dxp")
async def grant(request: Request,audit_log=Depends(audit_logger)):
    data = await request.json()
    wiki_id = data.get('wikiId')
    reqstaff = data.get('reqstaff')
    wikistaff = data.get('wikistaff')
    roleId = data.get('roleId')
    branch = data.get('branch')
    name = data.get('name')
    project = data.get('project')

    try:
        with session_scope() as session:
            wiki_info = get_wiki_info(session, wiki_id)
            if not wiki_info:
                return {
                    "code": "500",
                    "desc": get_translation("api.responses.wikiInfoNotFound")
                }

            wiki_info_branch = wiki_info.branch
            wiki_info_project = wiki_info.repo_owner
            wiki_info_name = wiki_info.repo_name

            if wiki_info_branch != branch or wiki_info_name != name or wiki_info_project != project:
                return {
                    "code": "500",
                    "desc": get_translation("api.responses.repoInfoMismatch")
                }

            # 查询申请人以及审批人信息
            req_user_info = select_user_info_by_code(reqstaff)
            wiki_admin_info = select_user_info_by_code(wikistaff)
            if not req_user_info or not wiki_admin_info:
                return {
                    "code": "500",
                    "desc": get_translation("api.responses.applicantOrApproverNotFound")
                }

            req_user_id = req_user_info.id
            wiki_admin_id = wiki_admin_info.id
            # 查看审批人是否有审批权限
            user_has_access_priv = check_user_wiki_priv(session = session, wiki_info = wiki_info, approver_id=wiki_admin_id)
            if not user_has_access_priv:
                return {
                    "code": "500",
                    "desc": get_translation("api.responses.approverNoPermission")
                }

            add_wiki_user_role(session=session,
                                user_id=str(req_user_id),
                                wiki_id=wiki_info.id,
                                role_id=roleId,
                                created_by=wiki_admin_id
                                )
            # 设置审计日志数据
            audit_log(code="WIKI_GRANT_DXP", party_type="服务", party_id=wiki_info.wiki_id, \
                      party_name=f"{wiki_info.repo_owner}/{wiki_info.repo_name}/{wiki_info.branch}", \
                      oper_data={"user_id": wiki_admin_id, "role_id": roleId}, oper_id=wiki_admin_id)

            return {
                "code": "200"
            }


    except Exception as e:
        logger.error(f"授权失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.authorizationFailed"))

@app.post("/api/users/role")
async def modify_user_role(request: Request,audit_log=Depends(audit_logger)):
    """
    修改用户角色
    """
    try:
        data = await request.json()
        user_code = data.get('userCode')
        with session_scope() as session:
            current_user = select_user_info_by_code(user_code)
            if not current_user:
                return {
                    "code": "500",
                    "desc": get_translation("api.responses.applicantInfoNotFound")
                }
            user_id = current_user.id
            # 获取用户当前的角色列表
            current_user_roles = session.query(UserRole).filter(UserRole.user_id == user_id).all()
            # 没有角色，直接新增
            if not current_user_roles:
                new_user_roles = UserRole(user_id=user_id, role_id=2)
                session.add(new_user_roles)
                # 删除缓存的用户角色信息
                delete_user_roles_from_redis(current_user.id)
                # 设置审计日志数据
                audit_log(code="USER_ROLE", party_type="服务", party_id=user_id, \
                          party_name=f"{current_user.user_name}", \
                          oper_data={"user_id": user_id, "role_id": 2})

                return {
                    "code": "200"
                }

            # 已经存在角色，判断是否已经是wiki管理员
            has_role_2 = any(role.role_id == 2 for role in current_user_roles)
            if has_role_2:
                return {
                    "code": "500",
                    "desc": get_translation("api.responses.userAlreadyWikiAdmin")
                }
            else:
                current_role = current_user_roles[0]
                current_role.role_id =2
                session.add(current_role)
                # 删除缓存的用户角色信息
                delete_user_roles_from_redis(current_user.id)
                audit_log(code="USER_ROLE", party_type="服务", party_id=user_id, \
                          party_name=f"{current_user.user_name}", \
                          oper_data={"user_id": user_id, "role_id": 2})
                return {
                    "code": "200"
                }
    except Exception as ex:
        logger.error(f"Failed to modify user role, cause by: {ex}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToModifyUserRoles"))

@app.get("/api/wiki/count")
async def get_wiki_count():
    """
    查询状态不为 failed 的 wiki 总数量
    """
    try:
        count = count_non_failed_wikis()
    
        return {
            "code": 200,
            "message": "success",
            "data": {"count": count}
        }
    except Exception as e:
        logger.error(f"获取Wiki项目列表失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToQueryRepoCount"))

@app.put("/api/wiki/info/{wiki_id}")
async def modify_wiki_info(wiki_id: str, request: ModifyWikiInfoRequest, current_user: dict = Depends(get_current_user)):
    """
    更新wiki的信息
    """
    try:
        with session_scope() as session:
            # 权限校验：检查用户是否有编辑wiki的权限
            from api.service.priv_checker import can_update_wiki
            if not can_update_wiki(session, current_user['id'], wiki_id):
                raise HTTPException(status_code=403, detail=get_translation("api.errors.noPermissionToEditWiki"))
            
            if request.comments:
                update_wiki_info(session=session, wiki_id=wiki_id, comments=request.comments)
            if request.repo_metadata:
                update_wiki_ext(session=session, wiki_id=wiki_id, repo_metadata=request.repo_metadata)
            if request.topic_id is not None:
                update_wiki_info(session=session, wiki_id=wiki_id, project_topic_id=request.topic_id)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新wiki信息失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToUpdateWikiInfo"))

@app.get("/api/owner/users")
async def get_owner_users(wiki_id: str, search: str, page: int, size: int):
    """
    分页获取针对某个wiki可用于授权转移的用户列表，会排除当前wiki的owner
    """
    try:
        with session_scope() as session:
            from api.model.wiki_info import WikiInfo
            subquery = select(func.coalesce(WikiInfo.owner_id, WikiInfo.created_by)).select_from(WikiInfo).where(WikiInfo.wiki_id==wiki_id)
            statement = select(UserInfo.id, UserInfo.user_name, UserInfo.user_code, UserInfo.org).select_from(UserInfo).where(not_(UserInfo.id.in_(subquery)))
            if search:
                statement = statement.where(or_(
                    UserInfo.user_name.ilike(f"%{search}%"),
                    UserInfo.user_code.ilike(f"%{search}%")
                ))
            total = session.scalar(select(func.count()).select_from(statement))
            statement= statement.limit(size).offset(page * size)
            result = session.exec(statement)
            columns = result.keys()
            rows = result.fetchall()
            users = [dict(zip(columns, row)) for row in rows]
            return { "total": total, "users": [{**user} for user in users] }
    except Exception as e:
        logger.error(f"获取可授权转移的用户列表失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetTransferableUsers"))

@app.patch("/api/owner")
async def modify_wiki_owner(request: ModifyWikiOwnerRequest):
    """
    修改wiki的所有者
    """
    user_id = get_current_user().get("id")
    now = datetime.now()
    try:
        with session_scope() as session:
            session: Session
            from api.model.wiki_info import WikiInfo
            wiki_info = session.exec(select(WikiInfo).where(WikiInfo.wiki_id == request.wiki_id)).first()
            if wiki_info:
                # 只有“wiki所有者”或者“超级管理员”才能执行“转移所有者”操作
                role_ids = get_user_privileges_by_type(user_id, PrivType.SERVICE).get("role_ids", [])
                if 1 not in role_ids and wiki_info.owner_id != user_id:
                    raise HTTPException(status_code=403, detail=get_translation("api.errors.notWikiOwner"))

                # 修改wiki所有者字段
                wiki_info.owner_id = request.owner_id
                wiki_info.updated_time = now
                wiki_info.updated_by = user_id

                wiki_user_role = session.exec(select(WikiUserRole).select_from(WikiUserRole).where(WikiUserRole.wiki_id == wiki_info.id, WikiUserRole.user_id == request.owner_id)).first()
                if wiki_user_role:
                    # 如果用户已经有这个仓库的相关权限，修改成“wiki授权【wiki_grant】”权限
                    wiki_user_role.role_id = 5
                    wiki_user_role.update_by = user_id
                    wiki_user_role.update_date = now
                else:
                    # 如果用户已经有这个仓库的相关权限，授予“wiki授权【wiki_grant】”权限
                    new_wiki_user_role = WikiUserRole(
                        wiki_id=wiki_info.id,
                        user_id=request.owner_id,
                        role_id=5,
                        created_by=user_id,
                        created_date=now
                    )
                    session.add(new_wiki_user_role)
            else:
                raise HTTPException(status_code=404, detail=get_translation("api.errors.failedToModifyWikiOwner").format(wiki_id=request.wiki_id))
    except HTTPException:
        raise
    except Exception as ex:
        logger.error(f"修改wiki所有者失败: {ex}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToModifyWikiOwnerGeneral"))

@app.get("/api/wiki/{wiki_id}/index-progress")
async def get_index_progress(
    wiki_id: str,
    request: Request,
    user: UserInfo = Depends(get_current_user),
):
    """
    获取索引进度
    """
    try:
        sync_worker = get_sync_worker_service()
        forwarded = await sync_worker.get_index_progress(
            wiki_id,
            headers=dict(request.headers),
            query=request.url.query or None,
        )
        if forwarded is not None:
            return forwarded

        sync_service = get_sync_index_service()
        progress = sync_service.calculate_index_progress(wiki_id)
        return progress
        
    except Exception as e:
        logger.error(f"获取索引进度失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="获取索引进度失败")


@app.post("/api/wiki/{wiki_id}/index-progress")
async def sync_wiki_index(
    wiki_id: str,
    request: Request,
    payload: dict = Body(...),
    user: UserInfo = Depends(get_current_user),
):
    """
    触发索引同步任务
    """
    try:
        strategy = payload.get('strategy', 'main_topic')
        if strategy not in ['main_topic', 'branch_topic']:
            raise HTTPException(status_code=400, detail="Invalid strategy")

        forward_payload = {
            "strategy": strategy,
            "user_id": user.get("id") if isinstance(user, dict) else getattr(user, "id", None),
        }

        sync_worker = get_sync_worker_service()
        forwarded = await sync_worker.trigger_sync_index(
            wiki_id,
            strategy=strategy,
            user_id=forward_payload["user_id"],
            headers=dict(request.headers),
        )
        if forwarded is not None:
            return forwarded

        sync_service = get_sync_index_service()
        result = await sync_service.start_sync_index(wiki_id, strategy, forward_payload["user_id"])
        return result

    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"同步索引失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="同步索引失败")


@app.post("/api/wiki/{wiki_id}/sync-index/cancel")
async def cancel_wiki_sync(
    wiki_id: str,
    request: Request,
    payload: Optional[dict] = Body(default=None),
    user: UserInfo = Depends(get_current_user),
):
    payload = payload or {}
    job_id = payload.get("job_id")
    reason = payload.get("reason")
    sync_worker = get_sync_worker_service()
    forwarded = await sync_worker.cancel_sync_job(
        wiki_id,
        job_id=job_id,
        reason=reason,
        headers=dict(request.headers),
    )
    if forwarded is not None:
        return forwarded

    try:
        sync_service = get_sync_index_service()
        return await sync_service.cancel_sync_job(wiki_id, job_id, reason)
    except ValueError as exc:
        raise HTTPException(status_code=404, detail=str(exc))
    except Exception as exc:
        logger.error("取消同步任务失败: %s", exc, exc_info=True)
        raise HTTPException(status_code=500, detail="取消同步任务失败")


@app.get("/api/wiki/sync-overview")
async def get_sync_overview(
    request: Request,
    user: UserInfo = Depends(get_current_user),
):
    """获取同步任务排队与执行状态的整体快照。"""
    sync_worker = get_sync_worker_service()
    forwarded = await sync_worker.get_queue_overview(headers=dict(request.headers))
    if forwarded is not None:
        return forwarded

    try:
        sync_service = get_sync_index_service()
        return sync_service.get_queue_snapshot()
    except Exception as exc:
        logger.error("获取同步队列信息失败: %s", exc, exc_info=True)
        raise HTTPException(status_code=500, detail="获取同步队列信息失败")


@app.get("/api/wiki/file_content")
async def get_wiki_file_content(
    virtual_file_path: str = Query(..., description="虚拟文件路径"),
    repo_url: str = Query(..., description="仓库URL"),
    branch: str = Query("main", description="分支名称"),
    user_code: str = Query(..., description="用户代码")
):
    """
    读取 wiki 文件内容
    
    Args:
        virtual_file_path: 虚拟文件路径
        repo_url: 仓库URL
        branch: 分支名称
        user_code: 用户代码
        
    Returns:
        文件内容字符串
    """
    try:
        from api.filesearch.file_manager_service import get_file_manager_service
        file_manager_service = get_file_manager_service()
        
        # 解析仓库信息
        owner, repo_name, _ = extract_repo_info(repo_url)
        
        # 验证仓库信息
        if not owner or not repo_name:
            logger.warning(f"无法解析仓库信息: {repo_url}")
            return JSONResponse(
                status_code=400,
                content={"error": "无法解析仓库信息", "content": ""}
            )
        
        # 读取文件内容
        content, file_info = file_manager_service.read_sandbox_file(
            owner, repo_name, branch, user_code, virtual_file_path
        )
        
        if content is None:
            error_msg = file_info.get("error", "未知错误") if file_info else "未知错误"
            logger.warning(f"无法读取文件 {virtual_file_path}: {error_msg}")
            return JSONResponse(
                status_code=404,
                content={"error": f"文件读取失败: {error_msg}", "content": ""}
            )
        
        return JSONResponse(
            status_code=200,
            content={"content": content, "error": ""}
        )
        
    except Exception as e:
        logger.error(f"读取 wiki 文件内容失败: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"文件读取失败: {str(e)}", "content": ""}
        )

@app.delete("/api/chat/session/context")
async def clear_chat_session(session_id: str, provider: str, model: str, wiki_id: str):
    user = get_current_user()
    result = {"success": True, "data": "Clear chat session successfully"}
    # 根据session_id找到数据库里的chat_id后面新增ai_dw_chat_history数据时要用
    chat_id = None
    with session_scope() as session:
        chat_id = session.exec(select(ChatSession.id).where(ChatSession.chat_sid == session_id)).first()
        if chat_id is None:
            return {"success": False, "errCode": "COMMON_0000", "errMsg": "Can not find chat session info from db"}
    # 如果是gemini-cli类型的模型，调用wct-cli的接口清除上下文
    if provider == 'gemini-cli':
        wiki_info = get_wiki_basic_info(wiki_id)
        result = await clear_wct_cli_session(user, session_id, wiki_info.get("id"))
    if not result.get("success"):
        return result
    # 在ai_dw_chat_history表里新增一条清空会话的消息，先删除已经存在的清空会话的消息，一个会话只保留一条清空消息
    try:
        with session_scope() as session:
            system_histories = session.exec(select(ChatHistory).where(and_(ChatHistory.chat_id == chat_id, ChatHistory.role == "system"))).all()
            for system_history in system_histories:
                if system_history.msg_data:
                    try:
                        msg_data = json.loads(system_history.msg_data)
                        if msg_data.get("clear_session_context", False):
                            session.delete(system_history)
                    except Exception as ex:
                        continue
            chat_history = ChatHistory(
                chat_id=chat_id,
                msg_sid=str(uuid.uuid4()),
                role="system",
                provider=provider,
                model=model,
                content="",
                state=True,
                qa_src=1,
                created_by=user.get("id"),
                created_date=datetime.now(),
                msg_data=json.dumps({"clear_session_context": True})
            )
            session.add(chat_history)
    except Exception as ex:
        logger.error(f"Failed to clear session, cause by: {ex}")
        return {"success": False, "errCode": "COMMON_0000", "errMsg": "Failed to clear chat session"}
    return result

async def clear_wct_cli_session(user, session_id, wiki_id):
    try:
        sandbox_url = None
        with session_scope() as session:
            repo_url, branch = session.exec(select(WikiInfo.repo_url, WikiInfo.branch).join(ChatSession, and_(ChatSession.wiki_id == WikiInfo.id, ChatSession.chat_sid == session_id))).first()
            if repo_url and branch:
                job = get_kubernetes_service().find_job_by_annotation(user.get("user_code", ""), wiki_id)
                job_name = job.metadata.name
                job_status = await sandbox_service.get_sandbox_detailed_status(user.get("user_code", ""), repo_url, branch, job_name)
                logger.info(f"job_status: {job_status}")
                if job_status.get("status") != SandboxStatus.READY.value:
                    return {"success": False, "errCode": "APP_0001", "errMsg": "The sandbox is not ready yet"}
                sandbox_url = sandbox_service.get_sandbox_url(user.get("user_code", ""), repo_url, branch, job_name)
        if sandbox_url:
            clear_session_url = sandbox_url.replace("completions", f"sessions/{session_id}")
            logger.info(f"Url for clearing chat session: {clear_session_url}")
            response = requests.delete(clear_session_url)
            if response.ok:
                return {"success": True, "data": "Clear chat session successfully"}
            else:
                return {"success": False, "errCode": "COMMON_0001", "errMsg": "Failed to clear chat session"}
        else:
            logger.error(f"Failed to retrieve sandbox url")
            return {"success": False, "errCode": "COMMON_0000", "errMsg": "Failed to retrieve sandbox url"}
    except Exception as ex:
        logger.error(f"Failed to clear session, cause by: {ex}")
        return {"success": False, "errCode": "COMMON_0000", "errMsg": "Failed to clear chat session"}

    
# 添加查询project的mcp，必须放在文件最后，注意不要删除！！！
mcp = FastApiMCP(app,
                 include_operations=["get_projects_wiki", "product_assistance"], headers=["cookie", "Authorization", "x-user-code"])
mcp.mount(mount_path="/sse")
