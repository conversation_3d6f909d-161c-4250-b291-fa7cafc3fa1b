import re
import os
import subprocess
import logging
from typing import Tuple, Optional
from urllib.parse import urlparse, urlunparse
from api.logging_config import setup_logging

setup_logging()
logger = logging.getLogger(__name__)

def validate_git_url(url: str) -> Tuple[bool, Optional[str]]:
    """
    验证Git URL是否规范
    
    Args:
        url: Git仓库URL
        
    Returns:
        Tuple[bool, Optional[str]]: (是否有效, 错误信息)
    """
    if not url or not isinstance(url, str):
        return False, "URL不能为空"
    
    url = url.strip()
    
    # 支持的Git URL格式
    patterns = [
        # HTTPS格式: https://github.com/user/repo.git 或 https://github.com/user/repo
        r'^https?://[a-zA-Z0-9\-\.]+/[a-zA-Z0-9\-_\.]+/[a-zA-Z0-9\-_\.]+(?:\.git)?/?$',
        # SSH格式: **************:user/repo.git
        r'^git@[a-zA-Z0-9\-\.]+:[a-zA-Z0-9\-_\.]+/[a-zA-Z0-9\-_\.]+(?:\.git)?$',
        # Git协议: git://github.com/user/repo.git
        r'^git://[a-zA-Z0-9\-\.]+/[a-zA-Z0-9\-_\.]+/[a-zA-Z0-9\-_\.]+(?:\.git)?/?$'
    ]
    
    # 检查是否匹配任何一种格式
    for pattern in patterns:
        if re.match(pattern, url):
            # 进一步验证URL结构
            try:
                if url.startswith('git@'):
                    # SSH格式特殊处理
                    if ':' not in url or '/' not in url.split(':')[1]:
                        return False, "SSH格式URL不正确，应为 git@host:user/repo.git"
                    
                    host_part = url.split('@')[1].split(':')[0]
                    repo_part = url.split(':')[1]
                    
                    if not host_part or not repo_part:
                        return False, "SSH格式URL缺少必要组件"
                    
                    if repo_part.count('/') != 1:
                        return False, "仓库路径格式不正确，应为 user/repo"
                        
                else:
                    # HTTP/HTTPS/Git协议格式
                    parsed = urlparse(url)
                    if not parsed.netloc:
                        return False, "URL缺少主机名"
                    
                    path_parts = parsed.path.strip('/').split('/')
                    if len(path_parts) < 2:
                        return False, "URL路径格式不正确，应包含用户名和仓库名"
                    
                    # 检查用户名和仓库名是否有效
                    username = path_parts[0]
                    repo_name = path_parts[1].replace('.git', '')
                    
                    if not username or not repo_name:
                        return False, "用户名或仓库名为空"
                    
                    # 检查字符是否有效
                    if not re.match(r'^[a-zA-Z0-9\-_\.]+$', username):
                        return False, "用户名包含无效字符"
                    
                    if not re.match(r'^[a-zA-Z0-9\-_\.]+$', repo_name):
                        return False, "仓库名包含无效字符"
                
                return True, None
                
            except Exception as e:
                return False, f"URL解析错误: {str(e)}"
    
    return False, "不支持的Git URL格式。支持的格式：https://host/user/repo.git, git@host:user/repo.git, git://host/user/repo.git"


def extract_repo_info(url: str) -> Tuple[str, str, str]:
    """
    从Git URL中提取仓库信息
    
    Args:
        url: Git仓库URL
        
    Returns:
        Tuple[str, str, str]: (owner, repo_name, host)
    """
    url = url.strip().rstrip('/')

    if url:
        if url.startswith('git@'):
            # SSH格式: **************:user/repo.git
            host = url.split('@')[1].split(':')[0]
            repo_path = url.split(':')[1]
            owner = repo_path.split('/')[0]
            repo_name = repo_path.split('/')[1].replace('.git', '')
        else:
            # HTTP/HTTPS/Git协议格式
            parsed = urlparse(url)
            host = parsed.netloc
            path_parts = parsed.path.strip('/').split('/')
            owner = path_parts[0]
            repo_name = path_parts[1].replace('.git', '')
        return owner, repo_name, host
    return "unknown", "unknown", "unknown"


def get_unified_repo_path(owner: str, repo_name: str, branch: str) -> str:
    """
    生成统一的本地仓库路径格式: ~/.adalflow/repos/{owner}/{repo_name}-{branch}/{repo_name}

    Args:
        owner: 仓库所有者
        repo_name: 仓库名称
        branch: 分支名称
        
    Returns:
        str: 统一的本地仓库路径
    """
    root_path = os.path.expanduser(os.path.join("~", ".adalflow"))
    branch_dir = f"{repo_name}-{branch}"
    return os.path.join(root_path, "repos", owner, branch_dir, repo_name)


def get_repo_cache_path(owner: str, repo_name: str, branch: str) -> str:
    """Return the cache directory used by wiki sync jobs for a repository."""
    root_path = os.path.expanduser(os.path.join("~", ".adalflow"))
    branch_dir = f"{repo_name}-{branch}"
    return os.path.join(root_path, "repos", owner, branch_dir, branch_dir)


def get_gemini_project_path(owner: str, repo_name: str, branch: str) -> str:
    """
    为Gemini CLI生成项目路径格式: /owner/repo_name-branch

    Args:
        owner: 仓库所有者
        repo_name: 仓库名称  
        branch: 分支名称
        
    Returns:
        str: Gemini CLI使用的项目路径
    """

    branch_dir = f"{repo_name}-{branch}"
    return f"/{owner}/{branch_dir}"


def build_repo_namespace(owner: str, repo_name: str, branch: str, prefix: str = "@") -> str:
    """构建DocChain使用的仓库命名空间串."""
    safe_owner = (owner or "unknown_owner").strip().replace(" ", "_") or "unknown_owner"
    safe_repo = (repo_name or "repository").strip().replace(" ", "_") or "repository"
    safe_branch = (branch or "main").strip().replace(" ", "_") or "main"
    safe_owner = safe_owner.replace("/", "_")
    safe_repo = safe_repo.replace("/", "_")
    safe_branch = safe_branch.replace("/", "_")
    if not prefix:
        prefix = "@"
    return f"{prefix}{safe_owner}/{safe_repo}#{safe_branch}"


def get_head_commit(repo_path: str) -> Optional[str]:
    """返回本地仓库当前HEAD的commit哈希."""
    if not repo_path:
        return None
    git_dir = os.path.join(repo_path, ".git")
    if not os.path.exists(repo_path) or not os.path.exists(git_dir):
        return None
    try:
        result = subprocess.run(
            ["git", "rev-parse", "HEAD"],
            cwd=repo_path,
            capture_output=True,
            text=True,
            check=True,
        )
        commit = (result.stdout or "").strip()
        return commit or None
    except subprocess.CalledProcessError as exc:
        logger.debug("git rev-parse failed for %s: %s", repo_path, exc)
        return None


def is_token_valid(repo_url: str, token: str) -> bool:
    """
    检测token是否有读取仓库的权限，如果repo_url或token为空则返回否

    Args:
        repo_url: 仓库地址  
        token: 仓库token
        
    Returns:
        bool: 是否有读取仓库的权限
    """
    if not token or not repo_url:
        return False

    token_url = repo_url
    if token:
        parsed = urlparse(repo_url)
        token_url = urlunparse((parsed.scheme, f"{token}:x-oauth-basic@{parsed.netloc}", parsed.path, '', '', ''))
    logging.info(f"{token_url}")
    try:
        result = subprocess.run(
            ["git", "ls-remote", token_url],
            capture_output=True, text=True
        )
        logging.info(f"{result}")
        if result.returncode == 0:
            return True
        else:
            return False
    except Exception:
        return False
    
