import uuid
from typing import Any, Optional
from api.cache.redis.manager import redis_manager

# Session 相关常量
SESSION_KEY_PREFIX = "session:"
SESSION_COOKIE_NAME = "session_id"

def generate_session_id() -> str:
    """生成唯一 session_id"""
    return str(uuid.uuid4())

def build_session_key(session_id: str) -> str:
    """构造 session 在 Redis 中的 key"""
    return f"{SESSION_KEY_PREFIX}{session_id}"

def set_session(session_id: str, user_info: dict, expiration: int) -> None:
    """写入 session 到 Redis"""
    client = redis_manager.get_client()
    if client:
        client.set(build_session_key(session_id), user_info, expiration=expiration)

def get_session(session_id: str) -> Optional[Any]:
    """从 Redis 获取 session"""
    client = redis_manager.get_client()
    if client:
        return client.get(build_session_key(session_id))
    return None

def delete_session(session_id: str) -> None:
    """删除 Redis 中的 session"""
    client = redis_manager.get_client()
    if client:
        client.delete(build_session_key(session_id))
        
def get_request_session_id(request):
    """从 request 的 cookie 或 header 获取 session_id，优先 cookie"""
    return request.cookies.get(SESSION_COOKIE_NAME) or request.headers.get("X-Session-Id")

def refresh_session_expire(session_id, timeout):
    """
    刷新 session 的过期时间（滑动过期），使用 Redis expire API
    """
    from api.cache.redis.manager import redis_manager
    redis_client = redis_manager.get_client()
    if redis_client:
        redis_client.expire(build_session_key(session_id), timeout)
