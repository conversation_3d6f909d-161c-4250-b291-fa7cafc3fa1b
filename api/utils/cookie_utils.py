from fastapi import Response
from datetime import timedelta, datetime
from typing import Optional

def set_cookie(
    response: Response,
    name: str,
    value: str,
    max_age: Optional[int] = None,
    path: str = "/",
    domain: Optional[str] = None,
    secure: bool = False, # 在生产环境中应设置为True
    httponly: bool = True,
    samesite: str = "lax", # 可选 'lax', 'strict', 'none'
):
    """
    将认证cookie设置到响应头中。

    Args:
        response (Response): FastAPI的Response对象。
        name (str): cookie的名称。
        value (str): 要在cookie中设置的认证token。
        max_age: cookie最大时间。
        path (str, optional): cookie的路径。 Defaults to "/".
        domain (Optional[str], optional): cookie的域。 Defaults to None.
        secure (bool, optional): 是否仅通过HTTPS发送cookie。 Defaults to False.
        httponly (bool, optional): 是否禁止JavaScript访问cookie。 Defaults to True.
        samesite (str, optional): SameSite策略。 Defaults to "lax".
    """

    if max_age == -1:
        max_age = None
    response.set_cookie(
        key=name,
        value=value,
        max_age=max_age,
        path=path,
        domain=domain,
        secure=secure,
        httponly=httponly,
        samesite=samesite,
    )

def delete_cookie(
    response: Response,
    name: str,
    path: str = "/",
    domain: Optional[str] = None,
):
    """
    从响应中删除认证cookie。

    Args:
        response (Response): FastAPI的Response对象。
        name (str): 要删除的cookie的名称。
        path (str, optional): cookie的路径。 Defaults to "/".
        domain (Optional[str], optional): cookie的域。 Defaults to None.
    """
    # 通过将max_age设置为0来使cookie立即失效
    response.delete_cookie(key=name, path=path, domain=domain)

