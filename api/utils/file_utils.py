import logging
import pkg_resources

logger = logging.getLogger(__name__)

def read_prompt_file(file_path):
    """读取提示词文件内容，返回dict，成功error: False，失败error: True，提示词内容在content字段"""
    try:
        # 使用 pkg_resources 读取文件内容
        content = pkg_resources.resource_string('api.prompts', file_path).decode('utf-8')
        return {"success": True, "content": content}
    except Exception as e:
        logger.error(f"Error reading prompt file {file_path}: {str(e)}")
        # 如果读取失败，返回一个默认的提示词
        return {"success": False, "content": "<role>You are a helpful assistant.</role>"}