import logging
import time
import jwt
from datetime import datetime, timedelta, timezone
from api.config import configs

jwt_config = configs.get("app", {}).get("security", {}).get("jwt", {})
SECRET_KEY = jwt_config.get("secret_key")
EXPIRES_MINUTES = jwt_config.get("expires_minutes", 60)
SHARE_EXPIRES_MINUTES = jwt_config.get("share_expires_minutes", 10080)
LLEWAY =  jwt_config.get("leeway", 5)
ALGORITHM = "HS256"

logger = logging.getLogger(__name__)

def create_jwt_token(data: dict, expires_delta: timedelta = None) -> dict:
    """
    创建一个JWT token。

    Args:
        data (dict): 要编码到token中的数据。
        expires_delta (timedelta, optional): token的过期时间。
                                             如果未提供，则使用配置文件中的默认值。
                                             Defaults to None.

    Returns:
        str: 生成的JWT token。
    """
    try:
        to_encode = data.copy()
        expire = None
        if expires_delta:
            expire = datetime.now() + expires_delta
        else:
            expire = datetime.now() + timedelta(minutes=EXPIRES_MINUTES)
        to_encode.update({"exp": expire, "iat": time.time(), "iss": "deepwiki"})
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return {
            "error": False,
            "token": encoded_jwt,
            "expires_in": expires_delta
        }
    except Exception as e:
        logger.error(f"[Token生成]Error generating JWT token: {to_encode} - {e}")
        return {
            "error": True,
            "errorMsg": f"Token generation failed: {str(e)}"
        }


def verify_jwt_token(token: str) -> dict:
    """
    验证JWT token。

    Args:
        token (str): 要验证的JWT token。

    Returns:
        dict: 一个包含验证结果的字典。
              成功: {'error': False, 'payload': dict}
              失败: {'error': True, 'errorMsg': str, 'payload': None}
    """
    try:
        leeway_time_delta = timedelta(minutes=LLEWAY)
        payload = jwt.decode(jwt=token, key=SECRET_KEY, algorithms=[ALGORITHM], leeway=leeway_time_delta)
        return {'error': False, 'payload': payload}
    except jwt.PyJWTError as e:
        logger.error(f"[Token解析]Error generating JWT token: {token} - {e}, api jwt lib - current time:{datetime.now(tz=timezone.utc).timestamp()}")
        return {'error': True, 'errorMsg': str(e), 'payload': None}

