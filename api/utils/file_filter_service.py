"""
统一文件过滤服务
提供文件类型、大小、路径过滤的统一接口
"""

import fnmatch
import logging
from pathlib import Path
from typing import Set, List, Tuple, Optional
from ..config import get_filemanager_config

logger = logging.getLogger(__name__)


class FileFilterService:
    """统一的文件过滤服务"""
    
    def __init__(self):
        """初始化文件过滤服务"""
        self._load_filter_config()
    
    def _load_filter_config(self):
        """从配置文件加载过滤规则"""
        try:
            file_manager_config = get_filemanager_config()
            
            # 最大文件大小配置
            self.max_file_size = file_manager_config.get("max_file_size", 10 * 1024 * 1024)
            self.max_scan_file_size = 50 * 1024 * 1024  # 50MB，扫描时的文件大小限制
            
            # 上传允许的文件扩展名（从配置中读取）
            self.upload_allowed_extensions = set(file_manager_config.get("upload_allowed_extensions", [
                ".md", ".txt", ".yml", ".yaml", ".properties", ".json", ".png", ".svg", ".jpg", 
                ".sql", ".java", ".js", ".ts", ".jsx", ".tsx", ".css", ".scss", ".sass", ".less", 
                ".html", ".htm", ".conf", ".cfg"
            ]))
            
            # 允许的文件扩展名（用于扫描和显示）
            self.allowed_extensions = set(file_manager_config.get("allowed_extensions", [
                # 文档类型
                '.txt', '.md', '.rst', '.doc', '.docx', '.pdf', '.rtf',
                # 代码类型
                '.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.c', '.cpp', '.h', '.hpp',
                '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala',
                '.html', '.htm', '.css', '.scss', '.sass', '.less',
                '.json', '.xml', '.yaml', '.yml', '.toml', '.properties', '.ini', '.cfg', '.conf',
                '.sh', '.bash', '.zsh', '.fish', '.ps1', '.bat', '.cmd',
                '.sql', '.graphql', '.proto',
                # 配置文件
                '.env', '.gitignore', '.dockerignore', '.editorconfig',
                '.eslintrc', '.prettierrc', '.babelrc', '.nvmrc', '.lock',
                # 数据文件
                '.csv', '.tsv', '.log',
                # 图片文件（小文件）
                '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.webp',
                # 其他
                '.LICENSE', '.gitkeep', '.keep'
            ]))
            
            # 禁止的文件扩展名
            self.blocked_extensions = set(file_manager_config.get("blocked_extensions", [
                # 可执行文件
                '.exe', '.msi', '.dmg', '.pkg', '.deb', '.rpm',
                '.bin', '.run', '.app', '.com', '.scr',
                # 脚本文件（危险）
                '.vbs', '.wsf', '.hta',
                # 二进制文件
                '.so', '.dll', '.dylib', '.lib', '.a', '.o', '.obj',
                # 压缩文件（大文件）
                '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz',
                # 数据库文件
                '.db', '.sqlite', '.sqlite3', '.mdb', '.accdb',
                # 临时文件
                '.tmp', '.temp', '.swp', '.swo', '.bak', '.backup'
            ]))
            
            # 禁止的文件名模式
            self.blocked_patterns = file_manager_config.get("blocked_patterns", [
                '.*',      # 隐藏文件（以.开头，但允许特定的配置文件）
                '__pycache__',
                '*.pyc',
                '*.pyo',
                'node_modules',
                '.git',
                '.svn',
                '.DS_Store',
                'Thumbs.db'
            ])
            
            # 允许的隐藏文件/目录
            self.allowed_hidden = set(file_manager_config.get("allowed_hidden", [
                '.env', '.gitignore', '.dockerignore', '.editorconfig',
                '.eslintrc', '.prettierrc', '.babelrc', '.nvmrc',
                '.gemini'  # 允许.gemini目录
            ]))
            
            # 忽略的目录（整个目录树都忽略）
            self.ignore_directories = {
                "__pycache__",
                ".git",
                ".vscode", 
                ".idea",
                "node_modules",
                "dist",
                "build",
                "coverage",
                ".pytest_cache",
                ".mypy_cache",
                ".tox",
                "venv",
                "env",
                ".env",
                "target",
                "bin",
                "obj",
            }
            
            # 忽略的文件模式（用于扫描）
            self.ignore_file_patterns = [
                "*.pyc", "*.pyo", "*.class", "*.o", "*.obj",
                "*.exe", "*.dll", "*.so", "*.dylib",
                "*.bin", "*.jar", "*.war", "*.zip",
                "*.tar", "*.gz", "*.bz2", "*.rar", "*.7z",
                "*.log", "*.tmp", "*.swp", "*.swo",
                ".*~", ".DS_Store"
            ]
            
        except Exception as e:
            logger.warning(f"加载文件过滤配置失败，使用默认配置: {e}")
            self._set_default_config()
    
    def _set_default_config(self):
        """设置默认配置"""
        self.max_file_size = 10 * 1024 * 1024
        self.max_scan_file_size = 50 * 1024 * 1024
        self.upload_allowed_extensions = {
            ".md", ".txt", ".yml", ".yaml", ".properties", ".json", ".png", ".svg", ".jpg", 
            ".sql", ".conf", ".cfg"
        }
        self.allowed_extensions = {
            '.txt', '.md', '.rst', '.doc', '.docx', '.pdf', '.rtf',
            '.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.c', '.cpp', '.h', '.hpp',
            '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala',
            '.html', '.htm', '.css', '.scss', '.sass', '.less',
            '.json', '.xml', '.yaml', '.yml', '.toml', '.properties', '.ini', '.cfg', '.conf',
            '.sh', '.bash', '.zsh', '.fish', '.ps1', '.bat', '.cmd',
            '.sql', '.graphql', '.proto',
            '.env', '.gitignore', '.dockerignore', '.editorconfig',
            '.eslintrc', '.prettierrc', '.babelrc', '.nvmrc', '.lock',
            '.csv', '.tsv', '.log',
            '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.webp',
            '.LICENSE', '.gitkeep', '.keep'
        }
        self.blocked_extensions = {
            '.exe', '.msi', '.dmg', '.pkg', '.deb', '.rpm',
            '.bin', '.run', '.app', '.com', '.scr',
            '.vbs', '.wsf', '.hta',
            '.so', '.dll', '.dylib', '.lib', '.a', '.o', '.obj',
            '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz',
            '.db', '.sqlite', '.sqlite3', '.mdb', '.accdb',
            '.tmp', '.temp', '.swp', '.swo', '.bak', '.backup'
        }
        self.blocked_patterns = [
            '.*', '__pycache__', '*.pyc', '*.pyo', 'node_modules',
            '.git', '.svn', '.DS_Store', 'Thumbs.db'
        ]
        self.allowed_hidden = {
            '.env', '.gitignore', '.dockerignore', '.editorconfig',
            '.eslintrc', '.prettierrc', '.babelrc', '.nvmrc', '.gemini'
        }
        self.ignore_directories = {
            "__pycache__", ".git", ".vscode", ".idea", "node_modules",
            "dist", "build", "coverage", ".pytest_cache", ".mypy_cache",
            ".tox", "venv", "env", ".env", "target", "bin", "obj"
        }
        self.ignore_file_patterns = [
            "*.pyc", "*.pyo", "*.class", "*.o", "*.obj",
            "*.exe", "*.dll", "*.so", "*.dylib",
            "*.bin", "*.jar", "*.war", "*.zip",
            "*.tar", "*.gz", "*.bz2", "*.rar", "*.7z",
            "*.log", "*.tmp", "*.swp", "*.swo",
            ".*~", ".DS_Store"
        ]
    
    def should_ignore_directory(self, dir_path: Path) -> bool:
        """
        检查目录是否应该被忽略
        
        Args:
            dir_path: 目录路径
            
        Returns:
            是否应该忽略
        """
        dir_name = dir_path.name
        
        # 直接匹配忽略的目录名
        if dir_name in self.ignore_directories:
            return True
            
        # 检查路径中是否包含需要忽略的目录
        for part in dir_path.parts:
            if part in self.ignore_directories:
                return True
        
        return False
    
    def should_ignore_file(self, file_path: Path) -> bool:
        """
        检查文件是否应该被忽略（用于扫描）
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否应该忽略
        """
        file_name = file_path.name
        
        # 检查是否在忽略的隐藏文件列表中（但不在允许列表中）
        if file_name.startswith('.') and file_name not in self.allowed_hidden:
            return True
        
        # 检查文件模式匹配
        for pattern in self.ignore_file_patterns:
            if fnmatch.fnmatch(file_name, pattern):
                return True
                
        # 检查父目录路径中是否包含需要忽略的目录
        for part in file_path.parts[:-1]:  # 排除文件名本身
            if part in self.ignore_directories:
                return True
        
        # 检查文件大小（扫描时的限制）
        try:
            if file_path.exists() and file_path.is_file():
                file_size = file_path.stat().st_size
                if file_size > self.max_scan_file_size:
                    return True
        except Exception:
            # 如果无法获取文件大小，默认不忽略
            pass
        
        return False
    
    def is_file_allowed_for_upload(self, filename: str, file_size: Optional[int] = None) -> Tuple[bool, str]:
        """
        检查文件是否允许上传（使用上传专用扩展名列表）
        
        Args:
            filename: 文件名
            file_size: 文件大小（字节），可选
            
        Returns:
            (是否允许, 错误信息)
        """
        if not filename:
            return False, "文件名不能为空"
        
        # 检查文件名是否包含危险字符
        dangerous_chars = ['..', '/', '\\', ':', '*', '?', '"', '<', '>', '|']
        for char in dangerous_chars:
            if char in filename:
                return False, f"文件名包含非法字符: {char}"
        
        # 检查是否是隐藏文件（除非在允许列表中）
        if filename.startswith('.') and filename not in self.allowed_hidden:
            return False, "不允许上传隐藏文件"
        
        # 检查黑名单模式
        for pattern in self.blocked_patterns:
            if fnmatch.fnmatch(filename.lower(), pattern.lower()):
                if filename not in self.allowed_hidden:
                    return False, f"文件名匹配禁止模式: {pattern}"
        
        # 获取文件扩展名
        file_extension = Path(filename).suffix.lower()
        
        # 检查黑名单扩展名
        if file_extension in self.blocked_extensions:
            return False, f"不允许的文件类型: {file_extension}"
        
        # 检查上传白名单扩展名（使用专门的上传扩展名列表）
        if file_extension and file_extension not in self.upload_allowed_extensions:
            return False, f"不支持的文件类型: {file_extension}"
        
        # 检查文件大小
        if file_size is not None and file_size > self.max_file_size:
            return False, f"文件大小超过限制（最大 {self.max_file_size // 1024 // 1024}MB）"
        
        # 无扩展名的文件也允许（可能是README、LICENSE等）
        return True, ""
    
    def _is_upload_file_allowed(self, filename: str, file_size: Optional[int] = None) -> Tuple[bool, str]:
        """
        检查文件是否允许上传（内部方法，使用上传专用扩展名列表）
        
        Args:
            filename: 文件名
            file_size: 文件大小（字节），可选
            
        Returns:
            (是否允许, 错误信息)
        """
        return self.is_file_allowed_for_upload(filename, file_size)
    
    def _is_upload_file_type_allowed(self, filename: str) -> Tuple[bool, str]:
        """
        检查文件类型是否允许上传（快速校验，不检查文件大小）
        
        Args:
            filename: 文件名
            
        Returns:
            (是否允许, 错误信息)
        """
        if not filename:
            return False, "文件名不能为空"
        
        # 检查文件名是否包含危险字符
        dangerous_chars = ['..', '/', '\\', ':', '*', '?', '"', '<', '>', '|']
        for char in dangerous_chars:
            if char in filename:
                return False, f"文件名包含非法字符: {char}"
        
        # 检查是否是隐藏文件（除非在允许列表中）
        if filename.startswith('.') and filename not in self.allowed_hidden:
            return False, "不允许上传隐藏文件"
        
        # 检查黑名单模式
        for pattern in self.blocked_patterns:
            if fnmatch.fnmatch(filename.lower(), pattern.lower()):
                if filename not in self.allowed_hidden:
                    return False, f"文件名匹配禁止模式: {pattern}"
        
        # 获取文件扩展名
        file_extension = Path(filename).suffix.lower()
        
        # 检查黑名单扩展名
        # if file_extension in self.blocked_extensions:
        #     return False, f"不允许的文件类型: {file_extension}"
        
        # 检查上传白名单扩展名（使用专门的上传扩展名列表）
        if file_extension and file_extension not in self.upload_allowed_extensions:
            return False, f"不支持的文件类型: {file_extension}"
        
        # 无扩展名的文件也允许（可能是README、LICENSE等）
        return True, ""
    
    def should_show_in_listing(self, file_path: Path) -> bool:
        """
        检查文件/目录是否应该在文件列表中显示
        
        Args:
            file_path: 文件或目录路径
            
        Returns:
            是否应该显示
        """
        file_name = file_path.name
        
        # 跳过隐藏文件/目录（除非在允许列表中）
        if file_name.startswith('.') and file_name not in self.allowed_hidden:
            return False
        
        # 跳过明确禁止的目录
        if file_path.is_dir() and file_name in self.ignore_directories:
            return False
        
        return True
    
    def get_allowed_extensions(self) -> Set[str]:
        """获取允许的文件扩展名集合（用于扫描和显示）"""
        return self.allowed_extensions.copy()
    
    def get_upload_allowed_extensions(self) -> Set[str]:
        """获取上传允许的文件扩展名集合"""
        return self.upload_allowed_extensions.copy()
    
    def get_blocked_extensions(self) -> Set[str]:
        """获取禁止的文件扩展名集合"""
        return self.blocked_extensions.copy()
    
    def get_max_file_size(self) -> int:
        """获取最大文件大小（用于上传）"""
        return self.max_file_size
    
    def get_max_scan_file_size(self) -> int:
        """获取最大扫描文件大小"""
        return self.max_scan_file_size


# 全局文件过滤服务实例
_file_filter_service = None

def get_file_filter_service() -> FileFilterService:
    """获取文件过滤服务实例"""
    global _file_filter_service
    if _file_filter_service is None:
        _file_filter_service = FileFilterService()
    return _file_filter_service
