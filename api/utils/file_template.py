from jinja2 import Environment, FileSystemLoader

def get_template_input(show_file_reference: bool = True, show_docchain: bool = True):
    env = Environment(loader=FileSystemLoader('api/prompts/editablePrompts'), autoescape=False)
    tmpl = env.get_template('gemini_cli_system_prompt.md')
    context = {
        'show_file_reference': show_file_reference,
        'show_docchain': show_docchain,
    }
    out = tmpl.render(**context)
    return out
