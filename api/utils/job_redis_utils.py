from typing import Any, Optional
# 延迟导入避免循环依赖
# from api.cache.redis.manager import redis_manager

# Wiki Group ID 相关常量
KEY_PREFIX = "linux:wiki:"
KEY_NAME = "wiki_group_id"

USER_KEY_PREFIX = "linux:user:"
USER_KEY_NAME = "user_group_id"

def build_wiki_group_id_key() -> str:
    """构造 wiki_group_id 在 Redis 中的 key"""
    return f"{KEY_PREFIX}{KEY_NAME}"

def set_wiki_group_id(wiki_group_id: int, expiration: int) -> None:
    """写入 wiki_group_id 到 Redis"""
    # 延迟导入避免循环依赖
    from api.cache.redis.manager import redis_manager
    client = redis_manager.get_client()
    if client:
        client.set(build_wiki_group_id_key(), wiki_group_id, expiration=expiration)

def get_wiki_group_id() -> Optional[Any]:
    """从 Redis 获取 wiki_group_id"""
    # 延迟导入避免循环依赖
    from api.cache.redis.manager import redis_manager
    from api.service.wiki_relation_service import get_max_linux_gid
    client = redis_manager.get_client()
    if client:
        if not client.exists(build_wiki_group_id_key()):
            id = get_max_linux_gid()
            client.set(build_wiki_group_id_key(), id, expiration=-1)
        return client.incr(build_wiki_group_id_key())
    return None

def delete_wiki_group_id() -> None:
    """删除 Redis 中的 wiki_group_id"""
    # 延迟导入避免循环依赖
    from api.cache.redis.manager import redis_manager
    client = redis_manager.get_client()
    if client:
        client.delete(build_wiki_group_id_key())

def refresh_session_expire(timeout):
    """
    刷新 wiki_group_id 的过期时间（滑动过期），使用 Redis expire API
    """
    from api.cache.redis.manager import redis_manager
    redis_client = redis_manager.get_client()
    if redis_client:
        redis_client.expire(build_wiki_group_id_key(), timeout)


def build_user_group_id_key() -> str:
    """构造 user_group_id 在 Redis 中的 key"""
    return f"{USER_KEY_PREFIX}{USER_KEY_NAME}"

def set_user_group_id(user_group_id: int, expiration: int) -> None:
    """写入 user_group_id 到 Redis"""
    # 延迟导入避免循环依赖
    from api.cache.redis.manager import redis_manager
    client = redis_manager.get_client()
    if client:
        client.set(build_user_group_id_key(), user_group_id, expiration=expiration)

def get_user_group_id() -> Optional[Any]:
    """从 Redis 获取 user_group_id"""
    # 延迟导入避免循环依赖
    from api.cache.redis.manager import redis_manager
    from api.service.user_service import get_max_user_linux_gid
    client = redis_manager.get_client()
    if client:
        if not client.exists(build_user_group_id_key()):
            id = get_max_user_linux_gid()
            client.set(build_user_group_id_key(), id, expiration=-1)
        return client.incr(build_user_group_id_key())
    return None

def delete_user_group_id() -> None:
    """删除 Redis 中的 user_group_id"""
    # 延迟导入避免循环依赖
    from api.cache.redis.manager import redis_manager
    client = redis_manager.get_client()
    if client:
        client.delete(build_user_group_id_key())

def refresh_user_group_id_expire(timeout):
    """
    刷新 user_group_id 的过期时间（滑动过期），使用 Redis expire API
    """
    from api.cache.redis.manager import redis_manager
    redis_client = redis_manager.get_client()
    if redis_client:
        redis_client.expire(build_user_group_id_key(), timeout)