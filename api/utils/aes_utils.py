import os
import base64
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.backends import default_backend
from typing import Optional, Union

# Import configuration
from api.config import configs


class AESUtils:
    """AES加解密工具类
    
    支持AES-128-CBC模式的加密和解密
    使用PBKDF2进行密钥派生，提供更好的安全性
    """
    
    def __init__(self, password: str = None):
        """初始化AES工具类
        
        Args:
            password: 用于生成密钥的密码，如果为None则从配置文件中获取
        """
        self.iv = "trehyjrqbtngvrgf".ljust(16, '0')[:16].encode('utf-8')
        self.salt = "brethouaafewvvja".ljust(16, '0')[:16].encode('utf-8')
        if password:
            self.password = password
        else:
            # 从配置文件中获取AES密钥
            self.password = self._get_aes_key_from_config()
        
    def _get_aes_key_from_config(self) -> str:
        """从配置文件中获取AES密钥
        
        Returns:
            str: AES密钥
            
        Raises:
            ValueError: 如果配置文件中没有找到AES密钥
        """
        try:
            # 尝试从配置文件中获取AES密钥
            aes_config = configs.get("app", {}).get("security", {}).get("aes", {})
            secret_key = aes_config.get("secret_key")
            
            if secret_key:
                return secret_key
            
            raise ValueError("AES密钥未配置")
            
        except Exception as e:
            raise ValueError(f"AES密钥未配置: {e}")
        
    def _derive_key(self, salt: bytes) -> bytes:
        """从密码派生密钥
        
        Args:
            salt: 盐值
            
        Returns:
            bytes: 派生的32字节密钥
        """
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=16,  # AES-128需要16字节密钥
            salt=salt,
            iterations=100000,  # 迭代次数
            backend=default_backend()
        )
        return kdf.derive(self.password.encode())
    
    def encrypt(self, plaintext: Union[str, bytes], salt: Optional[bytes] = None, iv: Optional[bytes] = None) -> str:
        """加密数据
        
        Args:
            plaintext: 要加密的明文（字符串或字节）
            
        Returns:
            str: Base64编码的加密结果（包含salt和iv）
        """
        if isinstance(plaintext, str):
            plaintext = plaintext.encode('utf-8')
            
        # 生成随机盐值和初始化向量
        salt = salt or os.urandom(16)
        iv = iv or os.urandom(16)
        
        # 派生密钥
        key = self._get_aes_key_from_config().encode()
        
        # 创建加密器
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
        encryptor = cipher.encryptor()
        
        # PKCS7填充
        padded_data = self._pad_data(plaintext)
        
        # 加密
        ciphertext = encryptor.update(padded_data) + encryptor.finalize()
        
        # 组合salt + iv + ciphertext
        encrypted_data = salt + iv + ciphertext
        
        # Base64编码
        return base64.b64encode(encrypted_data).decode('utf-8')
    
    def decrypt(self, encrypted_data: str) -> str:
        """解密数据
        
        Args:
            encrypted_data: Base64编码的加密数据
            
        Returns:
            str: 解密后的明文
            
        Raises:
            ValueError: 解密失败时抛出异常
        """
        try:
            # Base64解码
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            
            # 提取salt、iv和密文
            salt = encrypted_bytes[:16]
            iv = encrypted_bytes[16:32]
            ciphertext = encrypted_bytes[32:]
            
            # 派生密钥
            key = self._get_aes_key_from_config().encode()
            
            # 创建解密器
            cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
            decryptor = cipher.decryptor()
            
            # 解密
            padded_plaintext = decryptor.update(ciphertext) + decryptor.finalize()
            
            # 去除填充
            plaintext = self._unpad_data(padded_plaintext)
            
            return plaintext.decode('utf-8')
            
        except Exception as e:
            raise ValueError(f"解密失败: {str(e)}")
    
    def encrypt_fixed(self, plaintext: Union[str, bytes]):
        return self.encrypt(plaintext, self.salt, self.iv)
    
    def _pad_data(self, data: bytes) -> bytes:
        """PKCS7填充
        
        Args:
            data: 要填充的数据
            
        Returns:
            bytes: 填充后的数据
        """
        padding_length = 16 - (len(data) % 16)
        padding = bytes([padding_length] * padding_length)
        return data + padding
    
    def _unpad_data(self, padded_data: bytes) -> bytes:
        """移除PKCS7填充
        
        Args:
            padded_data: 填充后的数据
            
        Returns:
            bytes: 移除填充后的数据
        """
        padding_length = padded_data[-1]
        return padded_data[:-padding_length]
    
    @staticmethod
    def generate_key() -> str:
        """生成随机密钥
        
        Returns:
            str: Base64编码的随机密钥
        """
        key = os.urandom(16)  # 128位密钥
        return base64.b64encode(key).decode('utf-8')


# 便捷函数
def encrypt_text(text: str, password: str = None) -> str:
    """加密文本的便捷函数
    
    Args:
        text: 要加密的文本
        password: 密码，如果为None则从配置文件获取
        
    Returns:
        str: 加密后的Base64字符串
    """
    aes = AESUtils(password)
    return aes.encrypt(text)


def decrypt_text(encrypted_text: str, password: str = None) -> str:
    """解密文本的便捷函数
    
    Args:
        encrypted_text: 加密的Base64字符串
        password: 密码，如果为None则从配置文件获取
        
    Returns:
        str: 解密后的文本
    """
    aes = AESUtils(password)
    return aes.decrypt(encrypted_text)