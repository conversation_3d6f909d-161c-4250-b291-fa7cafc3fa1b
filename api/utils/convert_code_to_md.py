import logging
import os
import shutil
import sys
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Literal, TextIO

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ],
    encoding='utf-8'  # 确保整体日志编码为UTF-8
)


def delete_all_contents(directory_path: str) -> None:
    """
    删除目录下的所有文件和子目录，但保留根目录
    """
    path = Path(directory_path)
    for item in path.iterdir():
        try:
            if item.is_file() or item.is_symlink():
                item.unlink()
            elif item.is_dir():
                shutil.rmtree(item)
            logging.info(f"Deleted: {item}")
        except Exception as e:
            logging.error(f"Failed to delete {item}: {e}")


def is_windows():
    """判断是否运行在 Windows 系统"""
    return any([
        os.name == 'nt',
        sys.platform == 'Windows',
        sys.platform == 'win32',
        'microsoft' in sys.platform.lower()  # 适用于 WSL
    ])


def safe_open_long_path(
        path: str,
        mode: Literal['r', 'w'] = 'r',
        encoding: str = 'utf-8'
) -> TextIO:
    """
    安全打开可能具有长路径的文件（支持读写模式）

    参数:
        path: 文件路径
        mode: 打开模式，'r' 读取或 'w' 写入（默认 'r'）
        encoding: 文件编码格式（默认 'utf-8'）

    返回:
        文件对象

    异常:
        IOError: 当文件操作失败时抛出
    """
    abs_path = Path(path).absolute()

    # Windows长路径处理
    if is_windows() and len(str(abs_path)) >= 260:
        abs_path = Path(rf"\\?\{abs_path}")

    try:
        # 确保写入模式的目录存在
        if mode == 'w':
            abs_path.parent.mkdir(parents=True, exist_ok=True)

        return abs_path.open(mode, encoding=encoding)
    except Exception as e:
        error_msg = f"无法以 '{mode}' 模式打开文件 '{abs_path}': {str(e)}"
        raise IOError(error_msg) from e


def try_file_operation(
        file_path: str,
        mode: Literal['r', 'w'] = 'r',
        content: Optional[str] = None,
        encodings: Optional[list[str]] = None
) -> Tuple[bool, str]:
    """
    尝试对文件进行读写操作（支持多编码读取）

    参数:
        file_path: 文件路径
        mode: 操作模式，'r' 读取或 'w' 写入（默认 'r'）
        content: 写入模式下的内容（仅 mode='w' 时需要）
        encodings: 读取时尝试的编码列表（仅 mode='r' 时有效）

    返回:
        元组(操作是否成功, 内容或错误信息)
    """
    if mode == 'w':
        if content is None:
            return False, "写入模式需要提供 content 参数"
        try:
            with safe_open_long_path(file_path, 'w') as f:
                f.write(content)
            return True, "写入成功"
        except IOError as e:
            return False, str(e)

    # 读取模式
    default_encodings = [
        'utf-8',        # 最常用的Unicode编码
        'gb18030',      # 中文编码，兼容GBK
        'iso-8859-1',   # 西欧语言
        'utf-16',       # Unicode变体
        'ascii'         # 最后尝试ASCII
    ]
    encodings = encodings or default_encodings

    last_error = None
    for encoding in encodings:
        try:
            with safe_open_long_path(file_path, 'r', encoding) as f:
                return True, f.read()
        except UnicodeDecodeError as e:
            last_error = f"编码 {encoding} 不匹配: {str(e)}"
            continue
        except IOError as e:
            return False, str(e)

    return False, last_error or "所有编码尝试均失败"


class FileConverter:
    def __init__(self):
        self.stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'fail_reasons': {}
        }

    def create_md_files_from_source(
            self,
            directory: str,
            to_directory: str,
            extensions: List[str]
    ) -> Dict[str, int]:
        """
        将源目录中的指定类型文件转为md文件
        返回: 转换统计信息
        """
        if not os.path.isdir(directory):
            logging.error(f"{directory} is not a valid directory")
            return self.stats

        # 准备目标目录
        os.makedirs(to_directory, exist_ok=True)
        if os.listdir(to_directory):
            delete_all_contents(to_directory)

        # 遍历源目录
        for root, _, files in os.walk(directory):
            for file in files:
                file_extension = os.path.splitext(file)[1].lower()
                if file_extension in extensions:
                    self.stats['total'] += 1

                    # 准备路径
                    old_file_path = os.path.join(root, file)
                    relative_path = root.split(directory, 1)[1].strip(os.sep)
                    new_dir_path = os.path.join(to_directory, relative_path)
                    new_md_file_path = os.path.join(new_dir_path, f"{file}.md")

                    # 创建目录
                    os.makedirs(new_dir_path, exist_ok=True)

                    # 读取文件内容
                    success, content = try_file_operation(old_file_path)

                    if success:
                        # 写入MD文件
                        try:
                            with safe_open_long_path(new_md_file_path, 'w') as f:
                                f.write(content)
                            self.stats['success'] += 1
                            logging.info(f"Converted: {old_file_path} -> {new_md_file_path}")
                        except Exception as e:
                            self._record_failure(old_file_path, f"MD file creation failed: {e}")
                    else:
                        self._record_failure(old_file_path, content)

        return self.stats

    def _record_failure(self, file_path: str, reason: str) -> None:
        """记录失败信息"""
        self.stats['failed'] += 1
        self.stats['fail_reasons'][file_path] = reason
        logging.error(f"Failed to convert {file_path}: {reason}")

    def print_stats(self) -> None:
        """打印统计信息"""
        logging.info("\nConversion Statistics:")
        logging.info(f"Total files: {self.stats['total']}")
        logging.info(f"Successfully converted: {self.stats['success']}")
        logging.info(f"Failed conversions: {self.stats['failed']}")

        if self.stats['failed'] > 0:
            logging.info("\nFailure Details:")
            for file, reason in self.stats['fail_reasons'].items():
                logging.info(f"- {file}: {reason}")


if __name__ == '__main__':
    converter = FileConverter()

    # 配置路径
    source_dir = 'D:\\test\\code\\oss\\OSS_WFM_POST_SP-dev\\oss_wfm_post_sp'
    target_dir = 'D:\\test\\code\\oss\\OSS_WFM_POST_SP-dev\\oss_wfm_post_sp\\md'

    # 支持的文件扩展名
    supported_extensions = [
        '.java', '.py', '.xml', '.md', '.txt', '.properties',
        '.yaml', '.yml', '.sh', '.bat', '.js', '.ts',
        '.html', '.css', '.hbs'
    ]

    # 执行转换
    stats = converter.create_md_files_from_source(source_dir, target_dir, supported_extensions)

    # 打印统计信息
    converter.print_stats()
