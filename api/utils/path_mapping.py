"""
路径映射管理模块
负责管理主web容器与沙箱容器之间的路径映射关系
"""

import os
import logging
from pathlib import Path
from typing import Dict, Optional, Tuple, List
from api.config import get_kubernetes_config

logger = logging.getLogger(__name__)

class PathMappingService:
    """
    路径映射服务类

    规则：
    1. 容器内 /code 路径 -> 主机 /[base]/repos/{owner}/{repo_name}-{branch_clean}/
    2. 容器内其他任何路径 -> 主机 /base_workspace_path/{user_code}/{owner}/{repo_name}-{branch_clean}/
    """

    def __init__(self, base_path: Optional[str] = None):
        """
        初始化路径映射服务

        Args:
            base_path: 基础路径，优先使用传入参数，其次从配置文件获取，最后从环境变量获取
        """
        # 优先级：传入参数 > 配置文件 > 环境变量 > 默认值
        if base_path:
            base_path_str = base_path
        else:
            try:
                k8s_config = get_kubernetes_config()
                base_path_str = k8s_config.get("base_path", "/root/.adalflow")
            except Exception:
                # 如果配置文件读取失败，回退到环境变量
                base_path_str = os.environ.get("ADALFLOW_BASE_PATH", "/root/.adalflow")

        self.base_path = Path(base_path_str)
        self.repos_path = self.base_path / "repos"  # 代码目录
        self.workspace_path = self.base_path / "workspace"  # 用户工作空间目录（含 workspace 子目录等）
        self.project_workspace_path = self.base_path / "project_workspace"  # 项目共享工作区（i-doc/o-doc）

    def _clean_branch_name(self, branch: str) -> str:
        """清理分支名称，移除特殊字符"""
        # 移除或替换特殊字符，确保分支名称可以作为目录名
        import re
        return re.sub(r'[^\w\-_]', '_', branch)

    def get_code_path(self, owner: str, repo_name: str, branch: str) -> Path:
        """
        获取代码目录的实际路径

        Args:
            owner: 仓库所有者
            repo_name: 仓库名称
            branch: 分支名称

        Returns:
            代码目录实际路径
        """
        # branch_clean = self._clean_branch_name(branch)
        project_key = f"{repo_name}-{branch}"
        return self.repos_path / owner / project_key

    def get_workspace_path(self, owner: str, repo_name: str, branch: str, user_code: str) -> Path:
        """
        获取工作空间目录的实际路径

        Args:
            owner: 仓库所有者
            repo_name: 仓库名称
            branch: 分支名称
            user_code: 用户代码

        Returns:
            工作空间目录实际路径
        """
        # branch_clean = self._clean_branch_name(branch)
        project_key = f"{repo_name}-{branch}"
        return self.workspace_path / user_code / owner / project_key

    def get_project_workspace_path(self, owner: str, repo_name: str, branch: str) -> Path:
        """
        获取项目共享工作区的实际路径（不区分用户），用于 i-doc / o-doc
        """
        # branch_clean = self._clean_branch_name(branch)
        project_key = f"{repo_name}-{branch}"
        return self.project_workspace_path / owner / project_key

    def convert_virtual_to_real_path(self, virtual_path: str, owner: str, repo_name: str, branch: str, user_code: str) -> Optional[Path]:
        """
        将虚拟路径转换为实际路径

        Args:
            virtual_path: 容器内的虚拟路径
            owner: 仓库所有者
            repo_name: 仓库名称
            branch: 分支名称
            user_code: 用户代码

        Returns:
            主机上的实际路径，如果路径无效则返回None
        """
        # 标准化虚拟路径，移除开头的斜杠
        virtual_path = virtual_path.strip("/")

        if virtual_path.startswith("code"):
            # /code 路径映射到代码目录
            code_base_path = self.get_code_path(owner, repo_name, branch)

            if virtual_path == "code":
                # /code -> 代码根目录
                return code_base_path
            elif virtual_path.startswith("code/"):
                # /code/xxx -> 代码目录下的子路径
                sub_path = virtual_path[5:]  # 移除 "code/"
                return code_base_path / sub_path
        else:
            # i-doc / o-doc / project-gemini 映射到项目共享工作区
            if virtual_path.startswith("i-doc"):
                proj_base = self.get_project_workspace_path(owner, repo_name, branch)
                sub_path = virtual_path[6:] if virtual_path.startswith("i-doc/") else ""
                return (proj_base / "i-doc" / sub_path) if sub_path else (proj_base / "i-doc")
            if virtual_path.startswith("o-doc"):
                proj_base = self.get_project_workspace_path(owner, repo_name, branch)
                sub_path = virtual_path[6:] if virtual_path.startswith("o-doc/") else ""
                return (proj_base / "o-doc" / sub_path) if sub_path else (proj_base / "o-doc")
            if virtual_path.startswith("g-doc"):
                sub_path = virtual_path[6:] if virtual_path.startswith("g-doc/") else ""
                return (self.base_path / "g-doc" / sub_path) if sub_path else (self.base_path / "g-doc")
            if virtual_path.startswith("project-gemini"):
                proj_base = self.get_project_workspace_path(owner, repo_name, branch)
                sub_path = virtual_path[15:] if virtual_path.startswith("project-gemini/") else ""
                return (proj_base / "project-gemini" / sub_path) if sub_path else (proj_base / "project-gemini")

            # 其他路径（如 workspace/.gemini 等）映射到用户工作空间目录
            workspace_base_path = self.get_workspace_path(owner, repo_name, branch, user_code)
            if not virtual_path:
                return workspace_base_path
            else:
                return workspace_base_path / virtual_path

        return None

    def convert_real_to_virtual_path(self, real_path: Path, owner: str, repo_name: str, branch: str, user_code: str) -> Optional[str]:
        """
        将实际路径转换为虚拟路径

        Args:
            real_path: 主机上的实际路径
            owner: 仓库所有者
            repo_name: 仓库名称
            branch: 分支名称
            user_code: 用户代码

        Returns:
            容器内的虚拟路径，如果路径不可访问则返回None
        """
        try:
            code_base_path = self.get_code_path(owner, repo_name, branch)
            workspace_base_path = self.get_workspace_path(owner, repo_name, branch, user_code)
            project_workspace_base_path = self.get_project_workspace_path(owner, repo_name, branch)

            # 尝试匹配代码目录
            try:
                relative_path = real_path.resolve().relative_to(code_base_path.resolve())
                if str(relative_path) == ".":
                    return "/code"
                else:
                    virtual_path = f"/code/{relative_path.as_posix()}"
                    if real_path.is_dir() and not virtual_path.endswith("/"):
                        virtual_path += "/"
                    return virtual_path
            except ValueError:
                pass

            # 尝试匹配工作空间目录
            try:
                relative_path = real_path.resolve().relative_to(workspace_base_path.resolve())
                if str(relative_path) == ".":
                    return "/"
                else:
                    virtual_path = f"/{relative_path.as_posix()}"
                    if real_path.is_dir() and not virtual_path.endswith("/"):
                        virtual_path += "/"
                    return virtual_path
            except ValueError:
                pass

            # 尝试匹配项目共享工作区（i-doc / o-doc）
            try:
                rel = real_path.resolve().relative_to(project_workspace_base_path.resolve())
                parts = rel.parts
                if not parts:
                    return "/"  # 极少见
                prefix = parts[0]
                sub = "/".join(parts[1:])
                if prefix in ("i-doc", "o-doc"):
                    vp = f"/{prefix}" + (f"/{sub}" if sub else "")
                    if real_path.is_dir() and not vp.endswith("/"):
                        vp += "/"
                    return vp
            except ValueError:
                pass

        except Exception:
            pass

        return None

    def is_path_accessible(self, real_path: Path, owner: str, repo_name: str, branch: str, user_code: str) -> bool:
        """
        检查路径是否可访问

        Args:
            real_path: 实际路径
            owner: 仓库所有者
            repo_name: 仓库名称
            branch: 分支名称
            user_code: 用户代码

        Returns:
            路径是否可访问
        """
        code_base_path = self.get_code_path(owner, repo_name, branch)
        workspace_base_path = self.get_workspace_path(owner, repo_name, branch, user_code)
        project_workspace_base_path = self.get_project_workspace_path(owner, repo_name, branch)
        g_doc_base_path = self.base_path / "g-doc"

        try:
            real_path_resolved = real_path.resolve()

            # 检查是否在代码目录下（只读访问）
            try:
                real_path_resolved.relative_to(code_base_path.resolve())
                return True
            except ValueError:
                pass

            # 检查是否在工作空间目录下（读写访问）
            try:
                real_path_resolved.relative_to(workspace_base_path.resolve())
                return True
            except ValueError:
                pass

            # 检查是否在项目共享工作区下（读写访问）
            try:
                real_path_resolved.relative_to(project_workspace_base_path.resolve())
                return True
            except ValueError as e:
                logger.error(f"{e}")
            
            # 检查是否在全局文档目录下（只读访问）
            try:
                real_path_resolved.relative_to(g_doc_base_path.resolve())
                return True
            except ValueError:
                pass
                
        except Exception:
            pass
        
        return False

    def get_root_virtual_directories(self, owner: str, repo_name: str, branch: str, user_code: str) -> List[str]:
        """
        获取根目录下的虚拟目录列表
        根据新规则：容器根目录 = code目录 + workspace目录的合并

        Args:
            owner: 仓库所有者
            repo_name: 仓库名称
            branch: 分支名称
            user_code: 用户代码

        Returns:
            虚拟目录名称列表
        """
        # 固定返回根目录：code、i-doc、o-doc、g-doc、userspace、.gemini
        return ["code", "i-doc", "o-doc", "g-doc", "userspace", ".gemini"]

    def get_real_path_from_virtual_prefix(self, virtual_path_prefix: str, owner: str, repo_name: str, branch: str, user_code: str) -> Optional[Path]:
        """
        根据虚拟路径前缀获取对应的实际基础路径

        Args:
            virtual_path_prefix: 虚拟路径前缀，如 "code", "i-doc", "o-doc", "g-doc", "userspace"
            owner: 仓库所有者
            repo_name: 仓库名称
            branch: 分支名称
            user_code: 用户代码

        Returns:
            对应的实际基础路径，如果前缀无效则返回None
        """
        # 标准化前缀，移除开头和结尾的斜杠
        prefix = virtual_path_prefix.strip("/")

        if prefix == "code":
            return self.get_code_path(owner, repo_name, branch)
        if prefix == "i-doc":
            return self.get_project_workspace_path(owner, repo_name, branch) / "i-doc"
        if prefix == "o-doc":
            return self.get_project_workspace_path(owner, repo_name, branch) / "o-doc"
        if prefix == "g-doc":
            return self.get_project_workspace_path(owner, repo_name, branch) / "g-doc"
        if prefix == "project-gemini":
            return self.get_project_workspace_path(owner, repo_name, branch) / "project-gemini"
        # 其他目录走用户工作区
        workspace_base = self.get_workspace_path(owner, repo_name, branch, user_code)
        if prefix:
            return workspace_base / prefix
        else:
            return workspace_base

    def parse_virtual_path(self, virtual_path: str) -> Tuple[str, str]:
        """
        解析虚拟路径，分离前缀和子路径

        Args:
            virtual_path: 虚拟路径，如 "code/src/main.py", "i-doc/requirements.txt", "/workspace/test/"

        Returns:
            (前缀, 子路径) 元组，如 ("code", "src/main.py"), ("i-doc", "requirements.txt")
        """
        # 标准化路径，移除开头的斜杠
        path = virtual_path.strip("/")

        if not path:
            return "", ""

        # 分割路径
        parts = path.split("/", 1)
        prefix = parts[0]
        sub_path = parts[1] if len(parts) > 1 else ""

        return prefix, sub_path

    # 为了兼容性保留的方法
    def get_project_paths(self, owner: str, repo_name: str, branch: str, user_code: str) -> Dict[str, Path]:
        """
        获取项目相关的路径映射（兼容性方法）
        """
        return {
            "real_code_path": self.get_code_path(owner, repo_name, branch),
            "real_workspace_path": self.get_workspace_path(owner, repo_name, branch, user_code),
            "real_project_workspace_path": self.get_project_workspace_path(owner, repo_name, branch)
        }


# 全局路径映射服务实例
_path_mapping_service = None

def get_path_mapping_service() -> PathMappingService:
    """获取路径映射服务实例"""
    global _path_mapping_service
    if _path_mapping_service is None:
        _path_mapping_service = PathMappingService()
    return _path_mapping_service

