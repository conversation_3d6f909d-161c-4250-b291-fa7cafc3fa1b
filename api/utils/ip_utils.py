import socket
from fastapi import Request
from typing import Optional

def get_client_ip(request: Request) -> str:
    """
    获取客户端真实IP地址
    优先从代理头中获取，如果没有则使用直接连接的IP
    """
    # 检查代理头
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        # X-Forwarded-For可能包含多个IP，取第一个
        return forwarded_for.split(",")[0].strip()
    
    # 检查其他代理头
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip
    
    # 检查CF-Connecting-IP (Cloudflare)
    cf_ip = request.headers.get("CF-Connecting-IP")
    if cf_ip:
        return cf_ip
    
    # 最后使用直接连接的客户端IP
    if request.client:
        return request.client.host
    
    return "unknown"

def get_server_ip():
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    try:
        # 连接到一个外部地址（不需要真的连通）
        s.connect(('*******', 80))
        ip = s.getsockname()[0]
    except Exception:
        ip = '127.0.0.1'
    finally:
        s.close()
    return ip 