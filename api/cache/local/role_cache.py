import threading
import logging
from typing import Dict
from sqlmodel import select

from api.database.base import session_scope
from api.model.user_role import Role

logger = logging.getLogger(__name__)

_ai_dw_role_cache: Dict[str, dict] = {}
_ai_dw_role_cache_lock = threading.Lock()

def load_cache() -> Dict[str, dict]:
    """
    从数据库加载 ai_dw_role 表所有数据，role_code 作为 key 存入缓存。
    """
    global _ai_dw_role_cache
    with _ai_dw_role_cache_lock:
        with session_scope() as session:
            stmt = select(Role)
            rows = session.exec(stmt).all()
            cache = {}
            for row in rows:
                row_dict = row.model_dump()
                role_code = row_dict.get("role_code")
                if role_code:
                    cache[role_code] = row_dict
            _ai_dw_role_cache = cache
            logger.info(f"ai_dw_role 缓存加载完成，数量: {len(_ai_dw_role_cache)}")
    return _ai_dw_role_cache

def refresh_cache() -> Dict[str, dict]:
    """
    刷新缓存，供API调用。
    """
    logger.info("刷新 ai_dw_role 缓存")
    return load_cache()

def get_cache() -> Dict[str, dict]:
    """
    获取当前缓存内容（只读）。
    """
    return dict(_ai_dw_role_cache)

def init_cache():
    """
    应用启动时调用，初始化缓存。
    """
    logger.info("应用启动，初始化 ai_dw_role 缓存")
    load_cache()

def get_role_by_code(role_code: str) -> dict | None:
    """
    根据 role_code 查询角色缓存记录，未找到返回 None。
    """
    return _ai_dw_role_cache.get(role_code)
