import threading
import logging
from typing import List, Dict, Any
from sqlmodel import select

from api.common.constants import PrivType
from api.database.base import session_scope
from api.model.priv import Priv, PrivState

logger = logging.getLogger(__name__)

# 全局只读缓存和锁，区分服务权限与组件权限
_ai_dw_priv_cache: Dict[int, Dict[str, List[Dict[str, Any]]]] = {}
_ai_dw_priv_cache_lock = threading.Lock()

def load_cache() -> Dict[int, Dict[str, List[Dict[str, Any]]]]:
    """
    加载所有角色关联的权限数据，key为角色id，值为{"service": [...], "component": [...]}
    """
    from api.model.user_role import RolePriv
    global _ai_dw_priv_cache
    with _ai_dw_priv_cache_lock:
        with session_scope() as session:
            stmt = (
                select(RolePriv.role_id, Priv)
                .select_from(RolePriv)
                .join(Priv, RolePriv.priv_id == Priv.id)
                .where(Priv.state == PrivState.VALID.value)
            )
            rows = session.exec(stmt).all()
            cache: Dict[int, Dict[str, List[Dict[str, Any]]]] = {}
            for role_id, priv in rows:
                priv_dict = priv.model_dump(include={"id", "priv_type", "priv_el", "priv_code", "priv_name"})
                priv_type = priv_dict.get("priv_type")
                if role_id not in cache:
                    cache[role_id] = {"service": [], "component": []}
                if priv_type == PrivType.SERVICE.value:
                    cache[role_id]["service"].append(priv_dict)
                elif priv_type == PrivType.COMPONENT.value:
                    cache[role_id]["component"].append(priv_dict)
            _ai_dw_priv_cache = cache
            logger.info(f"ai_dw_priv 角色权限缓存加载完成，角色数: {len(_ai_dw_priv_cache)}")
    return _ai_dw_priv_cache

def refresh_cache() -> Dict[int, Dict[str, List[Dict[str, Any]]]]:
    """
    刷新缓存，供API调用。
    """
    logger.info("刷新 ai_dw_priv 角色权限缓存")
    return load_cache()

def get_cache() -> Dict[int, Dict[str, List[Dict[str, Any]]]]:
    """
    获取当前缓存内容（只读）。
    """
    return {rid: {"service": list(data.get("service", [])), "component": list(data.get("component", []))}
                for rid, data in _ai_dw_priv_cache.items()}

def init_cache():
    """
    应用启动时调用，初始化缓存。
    """
    logger.info("应用启动，初始化 ai_dw_priv 角色权限缓存")
    load_cache()

def get_privs_by_type(role_id: int, priv_type: PrivType) -> List[Dict[str, Any]]:
    """
    根据权限类型（"service"/"component"）和角色id查询对应权限数据
    """
    real_priv_type = "service"
    if PrivType.COMPONENT.value == priv_type.value:
        real_priv_type = "component"
        
    return list(_ai_dw_priv_cache.get(role_id, {}).get(real_priv_type, []))

def get_service_privs(role_id: int) -> List[Dict[str, Any]]:
    """
    获取指定角色的服务权限列表
    """
    return get_privs_by_type(role_id, "service")

def get_component_privs(role_id: int) -> List[Dict[str, Any]]:
    """
    获取指定角色的组件权限列表
    """
    return get_privs_by_type(role_id, "component")