import os
import shutil
import logging

from api.config import get_kubernetes_config

logger = logging.getLogger(__name__)

def copy_gemini_config():
    """
    将 GEMINI.md 文件复制到目标目录
    """
    # 源文件路径
    source_file = "docs/.gemini/GEMINI.md"

    root_path = os.path.expanduser(os.path.join("~", ".adalflow"))
    target_dir = os.path.join(root_path, "base", ".gemini")
    
    # 目标文件路径
    target_file = os.path.join(target_dir, "GEMINI.md")
    
    try:
        # 检查源文件是否存在
        if not os.path.exists(source_file):
            logger.error(f"源文件不存在: {source_file}")
            return False
        
        # 创建目标目录（如果不存在）
        os.makedirs(target_dir, exist_ok=True)
        
        # 复制文件
        shutil.copy2(source_file, target_file)
        logger.info(f"文件已成功复制: {source_file} -> {target_file}")
        return True
        
    except Exception as e:
        logger.error(f"复制文件时发生错误: {e}")
        return False