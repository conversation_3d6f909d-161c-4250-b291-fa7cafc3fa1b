import logging


from api.cache.local import i18n_cache, priv_cache
from api.cache.local import event_code_cache

# from api.cache.local import role_cache

logger = logging.getLogger(__name__)

def load_all_caches():
    """
    统一加载所有本地缓存（权限、事件码等）。
    """

    priv_cache.init_cache()
    logger.info("ai_dw_priv 缓存已初始化")

    event_code_cache.init_cache()
    logger.info("ai_dw_event_code 缓存已初始化")

    i18n_cache.load_translations()
    logger.info("翻译文件已加载")

    # role_cache.init_cache() 暂时用不到
    # logger.info("ai_dw_role 缓存已初始化")
