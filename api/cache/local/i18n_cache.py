import os
import json
from typing import Dict, Any
import logging
logger = logging.getLogger(__name__)

# 全局翻译数据存储
_translations: Dict[str, Dict[str, Any]] = {}
def load_translations():
    """加载翻译文件到内存中"""
    global _translations
    
    # 获取当前文件的目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    i18n_dir = os.path.join(current_dir, '..', '..', 'i18n')
    
    # 加载中文翻译
    zh_file = os.path.join(i18n_dir, 'deepwiki_zh.json')
    if os.path.exists(zh_file):
        try:
            with open(zh_file, 'r', encoding='utf-8') as f:
                _translations['zh'] = json.load(f)
            logger.info("成功加载中文翻译文件")
        except Exception as e:
            logger.error(f"加载中文翻译文件失败: {e}")
            _translations['zh'] = {}
    else:
        logger.warning(f"中文翻译文件不存在: {zh_file}")
        _translations['zh'] = {}
    
    # 加载英文翻译
    en_file = os.path.join(i18n_dir, 'deepwiki_en.json')
    if os.path.exists(en_file):
        try:
            with open(en_file, 'r', encoding='utf-8') as f:
                _translations['en'] = json.load(f)
            logger.info("成功加载英文翻译文件")
        except Exception as e:
            logger.error(f"加载英文翻译文件失败: {e}")
            _translations['en'] = {}
    else:
        logger.warning(f"英文翻译文件不存在: {en_file}")
        _translations['en'] = {}