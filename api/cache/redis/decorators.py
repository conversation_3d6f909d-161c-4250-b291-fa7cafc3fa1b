import asyncio
import functools
import logging
from typing import Callable
from concurrent.futures import ThreadPoolExecutor

from api.core.exceptions import RedisOperationException


logger = logging.getLogger(__name__)

# 线程池执行器
# executor = ThreadPoolExecutor(max_workers=10, thread_name_prefix="redis-worker")


# def redis_operation_2(func: Callable) -> Callable:
#     """Redis操作装饰器，用于异常处理和日志记录"""
    
#     @functools.wraps(func)
#     async def async_wrapper(self, *args, **kwargs):
#         try:
#             logger.debug(f"Executing Redis operation: {func.__name__}")
#             # 在线程池中执行同步Redis操作
#             loop = asyncio.get_event_loop()
#             result = await loop.run_in_executor(
#                 executor,
#                 functools.partial(func, self, *args, **kwargs)
#             )
#             logger.debug(f"Redis operation completed: {func.__name__}")
#             return result
#         except Exception as e:
#             logger.error(f"Redis operation failed: {func.__name__}, error: {str(e)}")
#             raise RedisOperationException(f"Operation {func.__name__} failed: {str(e)}") from e
    
#     @functools.wraps(func)
#     def sync_wrapper(self, *args, **kwargs):
#         try:
#             logger.debug(f"Executing Redis operation: {func.__name__}")
#             result = func(self, *args, **kwargs)
#             logger.debug(f"Redis operation completed: {func.__name__}")
#             return result
#         except Exception as e:
#             logger.error(f"Redis operation failed: {func.__name__}, error: {str(e)}")
#             raise RedisOperationException(f"Operation {func.__name__} failed: {str(e)}") from e
    
#     # 判断是否为异步函数
#     if asyncio.iscoroutinefunction(func):
#         return async_wrapper
#     return sync_wrapper

def redis_operation(func: Callable) -> Callable:
    """
    Redis 操作装饰器，处理异常和集群路由

    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(self, *args, **kwargs):
        try:
            return func(self, *args, **kwargs)
        except Exception as e:
            logger.error(f"Redis operation failed: {func.__name__}, error: {str(e)}")
            raise RedisOperationException(f"Operation {func.__name__} failed: {str(e)}") from e
    return wrapper
