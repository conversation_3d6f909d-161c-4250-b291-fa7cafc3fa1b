from typing import Optional
import logging

from api.cache.redis.client import RedisClient
from api.cache.redis.config import settings

logger = logging.getLogger(__name__)


class RedisManager:
    """Redis管理器，单例模式"""
    
    _instance: Optional["RedisManager"] = None
    _client: Optional[RedisClient] = None
    
    def __new__(cls) -> "RedisManager":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._client is None:
            self._client = RedisClient()
    
    async def startup(self) -> None:
        """启动Redis连接"""
        if settings.redis.enabled:
            logger.info("Starting Redis connection...")
            self._client.connect()
        else:
            logger.info("Redis is disabled, skipping connection")
    
    async def shutdown(self) -> None:
        """关闭Redis连接"""
        if self._client and self._client.is_connected():
            logger.info("Shutting down Redis connection...")
            self._client.disconnect()
    
    def get_client(self) -> Optional[RedisClient]:
        """获取Redis客户端"""
        if not settings.redis.enabled:
            return None
        return self._client


# 全局Redis管理器实例
redis_manager = RedisManager()
