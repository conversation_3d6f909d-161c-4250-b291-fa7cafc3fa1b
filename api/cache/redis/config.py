from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field, field_validator

from api.config import get_config


class RedisNodeConfig(BaseModel):
    host: str
    port: int


class RedisSingleConfig(BaseModel):
    host: str = "localhost"
    port: int = 6379
    db: int = 0
    password: Optional[str] = None


class RedisSentinelConfig(BaseModel):
    sentinels: List[RedisNodeConfig]
    service_name: str
    password: Optional[str] = None
    sentinel_password: Optional[str] = None
    db: int = 0


class RedisClusterConfig(BaseModel):
    nodes: List[RedisNodeConfig]
    password: Optional[str] = None
    skip_full_coverage_check: bool = False


class RedisConnectionConfig(BaseModel):
    decode_responses: bool = False
    encoding: str = "utf-8"
    socket_timeout: float = 5.0
    socket_connect_timeout: float = 5.0
    socket_keepalive: bool = True
    socket_keepalive_options: Dict[str, Any] = Field(default_factory=dict)


class RedisPoolConfig(BaseModel):
    max_connections: int = 50
    retry_on_timeout: bool = True


class RedisHealthCheckConfig(BaseModel):
    enabled: bool = True
    interval: int = 30


class RedisRetryConfig(BaseModel):
    retries: int = 6
    backoff_factor: float = 0.1
    max_backoff: float = 20.0


class RedisConfig(BaseModel):
    enabled: bool = False
    # single, sentinel, cluster
    mode: str = "cluster"
    key_prefix: str = "deepwiki"
    default_ttl: int = 3600
    serializer: str = "json"
    
    connection: RedisConnectionConfig = Field(default_factory=RedisConnectionConfig)
    pool: RedisPoolConfig = Field(default_factory=RedisPoolConfig)
    health_check: RedisHealthCheckConfig = Field(default_factory=RedisHealthCheckConfig)
    retry: RedisRetryConfig = Field(default_factory=RedisRetryConfig)
    
    
    single: Optional[RedisSingleConfig] = None
    sentinel: Optional[RedisSentinelConfig] = None
    cluster: Optional[RedisClusterConfig] = None
    
    @field_validator("mode")
    def validate_mode(cls, v):
        if v not in ["single", "sentinel", "cluster"]:
            raise ValueError("mode must be one of: single, sentinel, cluster")
        return v


class Settings(BaseModel):
    redis: RedisConfig = Field(default_factory=RedisConfig)
    
    @classmethod
    def load_from_config(cls) -> "Settings":
        config_data = get_config()
        return cls(**config_data)


# 全局配置实例
settings = Settings.load_from_config()
