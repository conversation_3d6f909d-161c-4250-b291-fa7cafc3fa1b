import redis
from redis import Red<PERSON>, RedisCluster, RedisError, Sentinel
from redis.backoff import ExponentialBackoff
from redis.retry import Retry
from redis.cluster import ClusterNode
from typing import Optional, Any, Dict, List, Tuple, Union
import logging

from api.cache.redis.config import settings
from api.cache.redis.decorators import redis_operation
from api.cache.redis.serializers import SerializerFactory
from api.core.exceptions import RedisConnectionException


logger = logging.getLogger(__name__)


class RedisClient:
    """Redis客户端封装"""
    
    def __init__(self):
        self._client: Optional[Union[Redis, RedisCluster]] = None
        self._serializer = SerializerFactory.get_serializer(settings.redis.serializer)
        self._key_prefix = settings.redis.key_prefix
        self._default_ttl = settings.redis.default_ttl
        self._is_cluster = False

    def _get_key(self, key: str) -> str:
        """获取带前缀的key"""
        return f"{self._key_prefix}:{key}"
    
    def _get_retry(self) -> Retry:
        """获取重试配置"""
        return Retry(
            ExponentialBackoff(),
            settings.redis.retry.retries
        )
    
    def _get_client_kwargs(self) -> Dict[str, Any]:
        """获取连接参数"""
        kwargs = {
            "decode_responses": settings.redis.connection.decode_responses,
            "encoding": settings.redis.connection.encoding,
            "socket_timeout": settings.redis.connection.socket_timeout,
            "socket_connect_timeout": settings.redis.connection.socket_connect_timeout,
            "socket_keepalive": settings.redis.connection.socket_keepalive,
            "socket_keepalive_options": settings.redis.connection.socket_keepalive_options,
            "retry": self._get_retry(),
            "retry_on_timeout": settings.redis.pool.retry_on_timeout,
            "max_connections": settings.redis.pool.max_connections,
        }
        
        if settings.redis.health_check.enabled:
            kwargs["health_check_interval"] = settings.redis.health_check.interval
        
        return kwargs
    
    def connect(self) -> None:
        """建立Redis连接"""
        if not settings.redis.enabled:
            logger.info("Redis is disabled in configuration")
            return
        
        try:
            connection_kwargs = self._get_client_kwargs()
            
            if settings.redis.mode == "single":
                # 单机模式
                config = settings.redis.single
                self._client = Redis(
                    host=config.host,
                    port=config.port,
                    db=config.db,
                    password=config.password,
                    **connection_kwargs
                )
                
            elif settings.redis.mode == "sentinel":
                # 哨兵模式
                config = settings.redis.sentinel
                sentinels = [(node.host, node.port) for node in config.sentinels]
                sentinel = Sentinel(
                    sentinels,
                    sentinel_kwargs={"password": config.sentinel_password} if config.sentinel_password else {},
                    **connection_kwargs
                )
                self._client = sentinel.master_for(
                    config.service_name,
                    password=config.password,
                    db=config.db,
                )
                
            elif settings.redis.mode == "cluster":
                # 集群模式
                self._is_cluster = True
                config = settings.redis.cluster
                startup_nodes = [
                    ClusterNode(node.host, node.port)
                    for node in config.nodes
                ]
                self._client = RedisCluster(
                    startup_nodes=startup_nodes,
                    password=config.password,
                    **connection_kwargs
                )
            
            # 测试连接
            self._client.ping()
            logger.info(f"Successfully connected to Redis in {settings.redis.mode} mode")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {str(e)}")
            raise RedisConnectionException(f"Failed to connect to Redis: {str(e)}") from e
    
    def disconnect(self) -> None:
        """断开Redis连接"""
        if self._client:
            self._client.close()
            logger.info("Disconnected from Redis")
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        if not self._client:
            return False
        
        try:
            self._client.ping()
            return True
        except Exception:
            return False
        
    @redis_operation
    def set(self, key: str, value: Any, expiration: Optional[int] = None) -> bool:
        """
        设置键值对
        
        Args:
            key: Redis 键
            value: 要存储的值
            expiration: 过期时间(秒)，None 表示使用默认过期时间
            
        Returns:
            bool: 操作是否成功
            
        Raises:
            SerializationError: 序列化失败
            RedisError: Redis 操作失败
        """
        if not self._client:
            return False
        
        full_key = self._get_key(key)
        expiration = expiration if expiration is not None else self._default_ttl
        serialized = self._serializer.serialize(value)
        # -1 表示不设置过期时间
        if expiration == -1:
            return bool(self._client.set(full_key, serialized))
        return bool(self._client.setex(full_key, expiration, serialized))

    @redis_operation
    def setnx(self, key: str, value: Any, expiration: Optional[int] = None) -> bool:
        """
        如果键不存在，则设置键值对
        
        Args:
            key: Redis 键
            value: 要存储的值
            expiration: 过期时间(秒)，None 表示使用默认过期时间
            
        Returns:
            bool: 操作是否成功
            
        Raises:
            SerializationError: 序列化失败
            RedisError: Redis 操作失败
        """
        if not self._client:
            return False
        
        full_key = self._get_key(key)
        expiration = expiration if expiration is not None else self._default_ttl
        serialized = self._serializer.serialize(value)
        
        # 使用管道确保原子性
        with self._client.pipeline() as pipe:
            pipe.setnx(full_key, serialized)
            pipe.expire(full_key, expiration)
            results = pipe.execute()
            
        return bool(results[0])

    @redis_operation
    def get(self, key: str) -> Any:
        """
        获取键对应的值
        
        Args:
            key: Redis 键
            
        Returns:
            Any: 反序列化后的值，键不存在则返回 None
            
        Raises:
            SerializationError: 反序列化失败
            RedisError: Redis 操作失败
        """
        if not self._client:
            return None
        
        full_key = self._get_key(key)
        value = self._client.get(full_key)
        return self._serializer.deserialize(value)

    @redis_operation
    def delete(self, *keys: str) -> int:
        """
        删除一个或多个键
        
        Args:
            *keys: 要删除的键
            
        Returns:
            int: 删除的键数量
            
        Raises:
            RedisError: Redis 操作失败
        """
        if not self._client:
            return 0
        
        full_keys = [self._get_key(key) for key in keys]
        return self._client.delete(*full_keys)

    @redis_operation
    def exists(self, key: str) -> bool:
        """
        检查键是否存在
        
        Args:
            key: Redis 键
            
        Returns:
            bool: 键是否存在
            
        Raises:
            RedisError: Redis 操作失败
        """
        if not self._client:
            return False
        
        full_key = self._get_key(key)
        return bool(self._client.exists(full_key))

    @redis_operation
    def ttl(self, key: str) -> int:
        """获取剩余过期时间"""
        if not self._client:
            return -2
        
        full_key = self._get_key(key)
        return self._client.ttl(full_key)
    
    @redis_operation
    def expire(self, key: str, expiration: int) -> bool:
        """
        设置键的过期时间
        
        Args:
            key: Redis 键
            expiration: 过期时间(秒)
            
        Returns:
            bool: 操作是否成功
            
        Raises:
            RedisError: Redis 操作失败
        """
        if not self._client:
            return False
        
        full_key = self._get_key(key)
        return bool(self._client.expire(full_key, expiration))

    @redis_operation
    def incr(self, key: str, amount: int = 1) -> int:
        """
        将键的值递增指定的量
        
        Args:
            key: Redis 键
            amount: 递增量
            
        Returns:
            int: 递增后的值
            
        Raises:
            RedisError: Redis 操作失败
        """
        if not self._client:
            return 0
        
        full_key = self._get_key(key)
        return self._client.incr(full_key, amount)

    @redis_operation
    def decr(self, key: str, amount: int = 1) -> int:
        """
        将键的值递减指定的量

        Args:
            key: Redis 键
            amount: 递减量

        Returns:
            int: 递减后的值

        Raises:
            RedisError: Redis 操作失败
        """
        if not self._client:
            return 0

        full_key = self._get_key(key)
        return self._client.decr(full_key, amount)

    @redis_operation
    def hset(self, name: str, mapping: Dict[str, Any], expiration: Optional[int] = None) -> int:
        """
        设置哈希表中的多个字段
        
        Args:
            name: 哈希表名
            mapping: 字段和值的映射
            expiration: 过期时间(秒)，None 表示使用默认过期时间
            
        Returns:
            int: 新增字段的数量
            
        Raises:
            RedisError: Redis 操作失败
        """
        if not self._client:
            return 0
        
        full_key = self._get_key(name)
        expiration = expiration if expiration is not None else self._default_ttl
        
        # 序列化字典值
        serialized_mapping = {k: self._serializer.serialize(v) for k, v in mapping.items()}
        
        with self._client.pipeline() as pipe:
            result = pipe.hset(full_key, mapping=serialized_mapping)
            if expiration:
                pipe.expire(full_key, expiration)
            results = pipe.execute()
            
        return results[0]

    @redis_operation
    def hsetkey(self, name: str, key: str, value: Any, expiration: Optional[int] = None) -> int:
        """
        设置哈希表中的单个字段
        
        Args:
            name: 哈希表名
            key: 字段名
            value: 字段值
            expiration: 过期时间(秒)，None 表示使用默认过期时间，-1 表示不设置过期时间
            
        Returns:
            int: 操作是否成功
            
        Raises:
            SerializationError: 序列化失败
            RedisError: Redis 操作失败
        """
        if not self._client:
            return 0
        
        full_key = self._get_key(name)
        serialized = self._serializer.serialize(value)
        
        # -1 表示不设置过期时间
        if expiration == -1:
            return self._client.hset(full_key, key, serialized)
        
        expiration = expiration if expiration is not None else self._default_ttl
        
        with self._client.pipeline() as pipe:
            pipe.hset(full_key, key, serialized)
            if expiration:
                pipe.expire(full_key, expiration)
            results = pipe.execute()
            
        return results[0]

    @redis_operation
    def hget(self, name: str, key: str) -> Any:
        """
        获取哈希表中字段的值
        
        Args:
            name: 哈希表名
            key: 字段名
            
        Returns:
            Any: 反序列化后的值，字段不存在则返回 None
            
        Raises:
            SerializationError: 反序列化失败
            RedisError: Redis 操作失败
        """
        if not self._client:
            return None
        
        full_key = self._get_key(name)
        value = self._client.hget(full_key, key)
        return self._serializer.deserialize(value)

    @redis_operation
    def hgetall(self, name: str) -> Dict[str, Any]:
        """
        获取哈希表中所有字段和值
        
        Args:
            name: 哈希表名
            
        Returns:
            Dict[str, Any]: 字段和反序列化后的值的映射
            
        Raises:
            SerializationError: 反序列化失败
            RedisError: Redis 操作失败
        """
        if not self._client:
            return {}
        
        full_key = self._get_key(name)
        result = self._client.hgetall(full_key)
        if not result:
            return {}
            
        # 反序列化所有值
        return {k.decode('utf-8') if isinstance(k, bytes) else k: 
                self._serializer.deserialize(v) for k, v in result.items()}

    @redis_operation
    def hdel(self, name: str, *keys: str) -> int:
        """
        删除哈希表中的一个或多个字段
        
        Args:
            name: 哈希表名
            *keys: 要删除的字段
            
        Returns:
            int: 删除的字段数量
            
        Raises:
            RedisError: Redis 操作失败
        """
        if not self._client:
            return 0
        
        full_key = self._get_key(name)
        return self._client.hdel(full_key, *keys)

    @redis_operation
    def rpush(self, key: str, *values: Any, expiration: Optional[int] = None) -> int:
        """
        将一个或多个值插入到列表的尾部
        
        Args:
            key: 列表键
            *values: 要插入的值
            expiration: 过期时间(秒)，None 表示使用默认过期时间
            
        Returns:
            int: 操作后列表的长度
            
        Raises:
            SerializationError: 序列化失败
            RedisError: Redis 操作失败
        """
        if not self._client:
            return 0
        
        full_key = self._get_key(key)
        expiration = expiration if expiration is not None else self._default_ttl
        serialized_values = [self._serializer.serialize(v) for v in values]
        
        with self._client.pipeline() as pipe:
            pipe.rpush(full_key, *serialized_values)
            if expiration:
                pipe.expire(full_key, expiration)
            results = pipe.execute()
            
        return results[0]

    @redis_operation
    def lpop(self, key: str, count: int = 1) -> Union[Any, List[Any], None]:
        """
        移除并返回列表的第一个元素
        
        Args:
            key: 列表键
            count: 要弹出的元素数量
            
        Returns:
            Union[Any, List[Any], None]: 弹出的元素或元素列表，列表为空则返回 None
            
        Raises:
            SerializationError: 反序列化失败
            RedisError: Redis 操作失败
        """
        if not self._client:
            return None
        
        full_key = self._get_key(key)
        result = self._client.lpop(full_key, count)
        
        if result is None:
            return None
            
        if isinstance(result, list):
            return [self._serializer.deserialize(item) for item in result]
        else:
            return self._serializer.deserialize(result)

    @redis_operation
    def lrange(self, key: str, start: int, end: int) -> List[Any]:
        """
        获取列表指定范围内的元素
        
        Args:
            key: 列表键
            start: 开始索引
            end: 结束索引
            
        Returns:
            List[Any]: 指定范围内的元素列表
            
        Raises:
            SerializationError: 反序列化失败
            RedisError: Redis 操作失败
        """
        if not self._client:
            return []
        
        full_key = self._get_key(key)
        result = self._client.lrange(full_key, start, end)
        return [self._serializer.deserialize(item) for item in result]

    @redis_operation
    def lrem(self, key: str, count: int, value: Any) -> int:
        """
        删除列表中与值匹配的元素

        Args:
            key: 列表键
            count: 删除数量 (0 表示全部)
            value: 目标值

        Returns:
            int: 删除的元素数量

        Raises:
            SerializationError: 序列化失败
            RedisError: Redis 操作失败
        """
        if not self._client:
            return 0

        full_key = self._get_key(key)
        serialized_value = self._serializer.serialize(value)
        return self._client.lrem(full_key, count, serialized_value)

    @redis_operation
    def mset(self, mapping: Dict[str, Any], expiration: Optional[int] = None) -> bool:
        """
        批量设置键值对
        
        Args:
            mapping: 键值对字典
            expiration: 过期时间(秒)
            
        Returns:
            bool: 操作是否成功
            
        Raises:
            SerializationError: 序列化失败
            RedisError: Redis 操作失败
        """
        if not self._client:
            return False
        
        full_mapping = {self._get_key(k): self._serializer.serialize(v) for k, v in mapping.items()}
        expiration = expiration if expiration is not None else self._default_ttl
        
        # 检查集群兼容性
        self._ensure_cluster_compatibility(list(full_mapping.keys()))
        
        with self._client.pipeline() as pipe:
            pipe.mset(full_mapping)
            if expiration:
                for key in full_mapping.keys():
                    pipe.expire(key, expiration)
            results = pipe.execute()
            
        return all(results)

    @redis_operation
    def mget(self, keys: List[str]) -> List[Any]:
        """
        批量获取多个键的值
        
        Args:
            keys: 键列表
            
        Returns:
            List[Any]: 值列表，键不存在则为 None
            
        Raises:
            SerializationError: 反序列化失败
            RedisError: Redis 操作失败
        """
        if not self._client:
            return [None] * len(keys)
        
        if not keys:
            return []
        
        full_keys = [self._get_key(key) for key in keys]
        values = self._client.mget(full_keys)
        return [self._serializer.deserialize(v) if v is not None else None for v in values]

    @redis_operation
    def set_raw(self, key: str, value: bytes, expiration: Optional[int] = None) -> bool:
        """
        设置原始字节值
        
        Args:
            key: Redis 键
            value: 原始字节值
            expiration: 过期时间(秒)
            
        Returns:
            bool: 操作是否成功
        """
        if not self._client:
            return False
        
        full_key = self._get_key(key)
        expiration = expiration if expiration is not None else self._default_ttl
        return bool(self._client.setex(full_key, expiration, value))

    @redis_operation
    def get_raw(self, key: str) -> Optional[bytes]:
        """
        获取原始字节值
        
        Args:
            key: Redis 键
            
        Returns:
            Optional[bytes]: 原始字节值，键不存在则返回 None
        """
        if not self._client:
            return None
        
        full_key = self._get_key(key)
        return self._client.get(full_key)

    @redis_operation
    def execute_pipeline(self, commands: List[Tuple]) -> List[Any]:
        """
        执行管道命令
        
        Args:
            commands: 命令列表，每个命令是一个元组 (method_name, args, kwargs)
            
        Returns:
            List[Any]: 命令执行结果列表
            
        Raises:
            RedisError: Redis 操作失败
        """
        if not self._client or not commands:
            return []
        
        with self._client.pipeline() as pipe:
            for method_name, args, kwargs in commands:
                method = getattr(pipe, method_name)
                method(*args, **kwargs)
            return pipe.execute()

    @redis_operation
    def sismember(self, key: str, member: Any) -> bool:
        """
        检查成员是否在集合中
        
        Args:
            key: 集合键
            member: 要检查的成员
            
        Returns:
            bool: 成员是否存在
            
        Raises:
            RedisError: Redis 操作失败
        """
        if not self._client:
            return False
        
        full_key = self._get_key(key)
        return bool(self._client.sismember(full_key, member))
    
    @redis_operation
    def sadd(self, key: str, *members: Any, expiration: Optional[int] = None) -> int:
        """
        向集合添加一个或多个成员
        
        Args:
            key: 集合键
            *members: 要添加的成员
            expiration: 过期时间(秒)，None 表示使用默认过期时间，-1 表示不设置过期时间
            
        Returns:
            int: 成功添加的成员数量
            
        Raises:
            RedisError: Redis 操作失败
        """
        if not self._client:
            return 0
        
        full_key = self._get_key(key)
        
        # -1 表示不设置过期时间
        if expiration == -1:
            return self._client.sadd(full_key, *members)
        
        expiration = expiration if expiration is not None else self._default_ttl
        
        with self._client.pipeline() as pipe:
            pipe.sadd(full_key, *members)
            if expiration:
                pipe.expire(full_key, expiration)
            results = pipe.execute()
            
        return results[0]
    
    @redis_operation
    def spop(self, key: str, count: Optional[int] = None) -> Union[Any, List[Any], None]:
        """
        移除并返回集合中的一个或多个随机成员
        
        Args:
            key: 集合键
            count: 要弹出的成员数量，None表示弹出一个
            
        Returns:
            Union[Any, List[Any], None]: 弹出的成员或成员列表，集合为空则返回 None
            
        Raises:
            RedisError: Redis 操作失败
        """
        if not self._client:
            return None
        
        full_key = self._get_key(key)
        if count is None:
            result = self._client.spop(full_key)
            return result.decode('utf-8') if isinstance(result, bytes) else result
        else:
            results = self._client.spop(full_key, count)
            if not results:
                return None
            return [r.decode('utf-8') if isinstance(r, bytes) else r for r in results]
    
    @redis_operation
    def srem(self, key: str, *members: Any) -> int:
        """
        从集合中移除一个或多个成员
        
        Args:
            key: 集合键
            *members: 要移除的成员
            
        Returns:
            int: 成功移除的成员数量
            
        Raises:
            RedisError: Redis 操作失败
        """
        if not self._client:
            return 0
        
        full_key = self._get_key(key)
        return self._client.srem(full_key, *members)
    
    @redis_operation
    def scard(self, key: str) -> int:
        """
        获取集合的成员数量
        
        Args:
            key: 集合键
            
        Returns:
            int: 集合的成员数量
            
        Raises:
            RedisError: Redis 操作失败
        """
        if not self._client:
            return 0
        
        full_key = self._get_key(key)
        return self._client.scard(full_key)
    
    @redis_operation
    def hlen(self, key: str) -> int:
        """
        获取哈希表中字段的数量
        
        Args:
            key: 哈希表键
            
        Returns:
            int: 字段数量
            
        Raises:
            RedisError: Redis 操作失败
        """
        if not self._client:
            return 0
        
        full_key = self._get_key(key)
        return self._client.hlen(full_key)
    
    @redis_operation
    def ping(self) -> bool:
        """
        检查 Redis 连接是否正常
        
        Returns:
            bool: 连接是否正常
        """
        return self._client.ping()
    
    @redis_operation
    def scan(self, cursor: int = 0, match: Optional[str] = None, count: int = 10) -> Tuple[int, List[str]]:
        """
        迭代扫描数据库中的键
        
        Args:
            cursor: 游标位置，0表示开始新的迭代
            match: 匹配模式，支持glob风格的模式匹配
            count: 每次迭代返回的键数量提示（实际数量可能不同）
            
        Returns:
            Tuple[int, List[str]]: (新游标位置, 键列表)
            
        Raises:
            RedisError: Redis 操作失败
        """
        if not self._client:
            return 0, []
        
        kwargs = {"count": count}
        if match:
            kwargs["match"] = match
        
        cursor, keys = self._client.scan(cursor, **kwargs)
        
        # 处理返回的键，去除前缀
        processed_keys = []
        prefix_len = len(self._key_prefix) + 1  # +1 for the colon
        for key in keys:
            # 将bytes转换为字符串
            if isinstance(key, bytes):
                key = key.decode('utf-8')
            # 如果键有前缀，去除前缀
            if key.startswith(f"{self._key_prefix}:"):
                processed_keys.append(key[prefix_len:])
            else:
                processed_keys.append(key)
        
        return cursor, processed_keys
    
    def _ensure_cluster_compatibility(self, keys: List[str]) -> None:
        """
        确保集群模式下所有键在同一槽中
        
        Args:
            keys: 要检查的键列表
            
        Raises:
            RedisError: 键不在同一槽中
        """
        if not self._is_cluster or len(keys) <= 1:
            return
            
        first_slot = self._client.cluster_keyslot(keys[0])
        for key in keys[1:]:
            if self._client.cluster_keyslot(key) != first_slot:
                raise RedisError("Cross-slot keys are not allowed in cluster mode")
