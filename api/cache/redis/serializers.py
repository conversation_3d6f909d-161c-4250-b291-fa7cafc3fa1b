import json
import pickle
from abc import ABC, abstractmethod
from typing import Any

# try:
#     import msgpack
#     HAS_MSGPACK = True
# except ImportError:
#     HAS_MSGPACK = False


class BaseSerializer(ABC):
    """序列化基类"""
    
    @abstractmethod
    def serialize(self, value: Any) -> bytes:
        """序列化数据"""
        pass
    
    @abstractmethod
    def deserialize(self, value: bytes) -> Any:
        """反序列化数据"""
        pass


class JsonSerializer(BaseSerializer):
    """JSON序列化器"""
    
    def serialize(self, value: Any) -> bytes:
        return json.dumps(value, ensure_ascii=False).encode('utf-8')
    
    def deserialize(self, value: bytes) -> Any:
        if value is None:
            return None
        return json.loads(value.decode('utf-8'))


class PickleSerializer(BaseSerializer):
    """Pickle序列化器"""
    
    def serialize(self, value: Any) -> bytes:
        return pickle.dumps(value, protocol=pickle.HIGHEST_PROTOCOL)
    
    def deserialize(self, value: bytes) -> Any:
        if value is None:
            return None
        return pickle.loads(value)


# class MsgpackSerializer(BaseSerializer):
#     """Msgpack序列化器"""
    
#     def __init__(self):
#         if not HAS_MSGPACK:
#             raise ImportError("msgpack is not installed. Install it with: pip install msgpack")
    
#     def serialize(self, value: Any) -> bytes:
#         return msgpack.packb(value, use_bin_type=True)
    
#     def deserialize(self, value: bytes) -> Any:
#         if value is None:
#             return None
#         return msgpack.unpackb(value, raw=False)


class SerializerFactory:
    """序列化器工厂"""
    
    _serializers = {
        "json": JsonSerializer,
        "pickle": PickleSerializer,
        # "msgpack": MsgpackSerializer,
    }
    
    @classmethod
    def get_serializer(cls, serializer_type: str) -> BaseSerializer:
        """获取序列化器实例"""
        serializer_class = cls._serializers.get(serializer_type)
        if not serializer_class:
            raise ValueError(f"Unknown serializer type: {serializer_type}")
        return serializer_class()
