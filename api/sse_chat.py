import datetime
import logging
import os
import sys
import hashlib
import json  # 添加json模块导入
import uuid
from typing import List, Optional, Dict, Any, Tuple
from urllib.parse import unquote
from logging.handlers import TimedRotatingFileHandler

# 导入Google Generative AI依赖
import google.generativeai as genai

from adalflow.core.types import ModelType, Document
from adalflow.core.types import ModelType, Document
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
import httpx
from pydantic import BaseModel, Field
from fastapi import Request
from requests import Session

from api.config import get_model_config, configs, OPENAI_API_KEY
from api.data_pipeline import count_tokens
from api.middleware.auth_middleware import get_current_user
from api.model.chat_session import ChatSession
from api.openai_client import OpenAIClient
from api.gemini_cli_client import GeminiCliClient
from api.docchain.manager import docchain_manager
from api.service.chat_session_service import add_chat_session, select_chat_session_by_chat_sid
from api.service.chat_history_service import add_chat_history
from api.model.chat_history import ChatHistory
from api.utils.file_utils import read_prompt_file
from api.utils.ip_utils import get_client_ip
from api.langfuse_utils import (
    bind_langfuse_span,  # 绑定Langfuse跨度到固定context，避免上下文错位
    build_langfuse_metadata,
    create_langfuse_span,
    get_langfuse_context,
)
try:  # 可选导入，用于Langfuse追踪ID复用
    from langfuse import Langfuse  # type: ignore
except Exception:  # pragma: no cover
    Langfuse = None


DEEP_RESEARCH_TRACE_CACHE: Dict[str, Dict[str, str]] = {}

# Configure logging
from api.logging_config import setup_logging
from api.service.wiki_info_service import get_wiki_info_for_chat, get_wiki_info
from api.database.base import session_scope


# 获取当前工作目录
current_dir = os.getcwd()
log_dir = os.path.join(current_dir, "logs")
log_file = os.path.join(log_dir, "chat.log")  # 新日志文件

# 确保日志目录存在
os.makedirs(log_dir, exist_ok=True)

# 创建独立的新 Logger
logger = logging.getLogger(__name__)
logger.propagate = False  # 阻止日志向上传播到根 Logger

# 创建文件处理器
file_handler = TimedRotatingFileHandler(filename=log_file, encoding="utf-8", when="MIDNIGHT", interval=1, backupCount=7, delay=True)
stream_handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)

# 将处理器添加到新 Logger
logger.addHandler(file_handler)
logger.addHandler(stream_handler)

# Get API keys from environment variables
google_api_key = os.environ.get('GOOGLE_API_KEY')

# Configure Google Generative AI 
# 注意：这里的 configure 方法确实存在，但 linter 可能报错
# 可以在 /home/<USER>/code/deepwiki-open/.venv/lib/python3.12/site-packages/google/generativeai/__init__.py 中看到
if google_api_key:
    try:
        # 忽略linter错误，实际上这个方法存在
        genai.configure(api_key=google_api_key)  # type: ignore
    except Exception as e:
        logger.warning(f"Error configuring Google Generative AI: {str(e)}")
else:
    logger.warning("GOOGLE_API_KEY not found in environment variables")


# Initialize FastAPI app
app = FastAPI(
    title="Simple Chat API",
    description="Simplified API for streaming chat completions"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Models for the API
class ChatMessage(BaseModel):
    id: str
    role: str  # 'user' or 'assistant'
    content: str

class FileReference(BaseModel):
    """文件引用模型"""
    id: str = Field(..., description="文件引用ID")
    path: str = Field(..., description="文件相对路径")
    isDirectory: bool = Field(False, description="是否为目录")
    name: str = Field(..., description="文件名称")

class ChatCompletionRequest(BaseModel):
    """
    Model for requesting a chat completion.
    """
    session_id: str = Field(..., description="Session ID")
    repo_url: str = Field(..., description="URL of the repository to query")
    messages: List[ChatMessage] = Field(..., description="List of chat messages")
    filePath: Optional[str] = Field(None, description="Optional path to a file in the repository to include in the prompt")
    file_references: Optional[List[FileReference]] = Field(None, description="List of file references to include in the prompt")
    command_params: Optional[Dict[str, Any]] = Field(None, description="命令参数")
    token: Optional[str] = Field(None, description="Personal access token for private repositories")
    type: Optional[str] = Field("whaleDevCloud", description="Type of repository (e.g., 'github', 'gitlab', 'bitbucket')")
    branch: str = Field("master", description="Branch of repository")
    wiki_id: Optional[str] = Field(None, description="Wiki ID")

    # model parameters
    provider: str = Field("google", description="Model provider (google, openai, openrouter, ollama, bedrock, gemini-cli)")
    model: Optional[str] = Field(None, description="Model name for the specified provider")

    # DocChain topic parameters
    existing_topic_id: Optional[str] = Field(None, description="Existing DocChain Topic ID to use instead of creating new one")
    existing_topic_id_code: Optional[str] = Field(None, description="Repo Code Existing DocChain Topic ID to use instead of creating new one")
    existing_topic_id_doc: Optional[str] = Field(None, description="Repo Doc Existing DocChain Topic ID to use instead of creating new one")

    language: Optional[str] = Field("en", description="Language for content generation (e.g., 'en', 'ja', 'zh', 'es', 'kr', 'vi')")
    excluded_dirs: Optional[str] = Field(None, description="Comma-separated list of directories to exclude from processing")
    excluded_files: Optional[str] = Field(None, description="Comma-separated list of file patterns to exclude from processing")
    included_dirs: Optional[str] = Field(None, description="Comma-separated list of directories to include exclusively")
    included_files: Optional[str] = Field(None, description="Comma-separated list of file patterns to include exclusively")
    api_key: Optional[str] = Field(None, description="API key")
    caller: Optional[int] = Field(None, description="调用来源，区分来自deepwiki还是mcp, 1: deepwiki, 2: mcp")
    images: Optional[List[str]] = Field(None, description="需要AI进行识别的图片列表")

class AppChatCompletionRequest(BaseModel):
    """
    App级别沙箱对话请求（仅支持gemini-cli）
    """
    session_id: str = Field(..., description="Session ID")
    wiki_id: Optional[str] = Field(None, description="Wiki ID")
    user_id: int = Field(..., description="User ID")
    messages: List[ChatMessage] = Field(..., description="List of chat messages")
    model: Optional[str] = Field("gemini-2.5-flash", description="Model name")
    provider: str = Field("gemini-cli", description="Only gemini-cli is supported")
    app_id: Optional[str] = Field(None, description="App ID")
    app_code: Optional[str] = Field(None, description="App Code")
    api_key: Optional[str] = Field(None, description="API key")
    timeout: Optional[float] = Field(600.0, description="Timeout seconds")
    caller: Optional[int] = Field(None, description="调用来源，区分来自deepwiki还是mcp, 1: deepwiki, 2: mcp, 3: app")

# 定义需要保留的文件扩展名列表
TEXT_EXTENSIONS = [
    # 编程语言
    ".py", ".js", ".ts", ".java", ".cpp", ".c", ".h", ".hpp", ".go", ".rs",
    ".jsx", ".tsx", ".html", ".css", ".php", ".swift", ".cs", ".sh", ".bat",
    ".ps1", ".pl", ".rb", ".lua", ".dart",
    # 标记/文档格式
    ".md", ".markdown", ".txt", ".rst", ".tex", ".adoc", ".org",
    # 配置文件
    ".json", ".yaml", ".yml", ".ini", ".conf", ".cfg", ".toml", ".env",
    ".properties", ".xml",
    # 数据/脚本
    ".sql", ".diff", ".patch", ".csv", ".tsv"
]

def ensure_markdown_extension(file_path: str) -> str:
    """
    检查文件路径是否以指定的文本扩展名结尾，若不是则添加 .md 后缀
    
    Args:
        file_path: 原始文件路径
    
    Returns:
        处理后的文件路径
    """
    # 转换为小写进行比较
    lower_path = file_path.lower()
    
    # 检查是否以任意允许的扩展名结尾
    for ext in TEXT_EXTENSIONS:
        if lower_path.endswith(ext):
            return file_path  # 已匹配有效扩展名，无需处理
    
    # 未匹配到有效扩展名，添加 .md
    return f"{file_path}.md"


def _truncate_for_langfuse(value: Optional[str], limit: int = 2000) -> Optional[str]:
    """避免向Langfuse发送过大的有效载荷。"""

    if value is None:
        return None

    text = str(value)
    if len(text) <= limit:
        return text

    truncated = text[:limit]
    return f"{truncated}… (truncated {len(text) - limit} chars)"


def _build_user_label(
    user_info: Optional[Dict[str, Any]],
    fallback_user_id: Optional[int] = None,
) -> Tuple[Optional[str], Optional[str], Optional[str]]:
    """构建用户标签信息，返回 (user_code, user_name, user_label)."""
    user_code: Optional[str] = None
    user_name: Optional[str] = None

    if user_info:
        user_code = (
            user_info.get("user_code")
            or user_info.get("username")
            or user_info.get("userCode")
            or user_info.get("id")
        )
        user_name = (
            user_info.get("username")
            or user_info.get("user_name")
            or user_info.get("name")
        )

    if not user_code and fallback_user_id is not None:
        user_code = str(fallback_user_id)

    # 构建用户标签
    user_label: Optional[str] = None
    if user_name and user_code:
        user_label = f"{user_name}[{user_code}]"
    elif user_code:
        user_label = str(user_code)
    elif user_name:
        user_label = user_name

    return user_code, user_name, user_label


def _build_langfuse_labels(
    user_label: Optional[str],
    wiki_identifier: Optional[str],
    repo_url: Optional[str],
    branch: Optional[str],
) -> Tuple[Optional[str], list]:
    """
    构建Langfuse标签

    Args:
        user_label: 用户标签
        wiki_identifier: Wiki标识符
        repo_url: 仓库URL
        branch: 分支名

    Returns:
        Tuple[repo_label, label_values]: 仓库标签和所有标签列表
    """
    # 使用wiki_id统一构建wiki标签，避免出现主键ID等混淆
    wiki_label = None
    if wiki_identifier:
        wiki_label = f"wiki:{wiki_identifier}"

    # 构造repo级别标签
    repo_label = None
    if repo_url:
        repo_label = f"{repo_url}#{branch or ''}".rstrip('#')

    # 添加wiki绑定仓库的Git地址和分支标签，便于Langfuse筛选
    wiki_git_url_label = f"wiki_git_url:{repo_url}" if repo_url else None
    wiki_git_branch_label = f"wiki_git_branch:{branch}" if branch else None

    label_values: List[str] = []

    def _append_if_value(value: Optional[str]) -> None:
        if value:
            normalized = str(value)
            if normalized not in label_values:
                label_values.append(normalized)

    _append_if_value(user_label)
    _append_if_value(wiki_label)
    _append_if_value(repo_label)
    _append_if_value(wiki_git_url_label)
    _append_if_value(wiki_git_branch_label)
    # 如果已经生成wiki专属Git标签，则不再追加通用repo/branch标签，避免重复展示
    if repo_url and not wiki_git_url_label:
        _append_if_value(f"repo:{repo_url}")
    if branch and not wiki_git_branch_label:
        _append_if_value(f"branch:{branch}")

    return repo_label, label_values


def _prepare_chat_langfuse_context(
    request: ChatCompletionRequest,
    user_info: Optional[Dict[str, Any]],
    wiki_info_db: Optional[Dict[str, Any]],
    client_ip: str,
    is_deep_research: bool,
    research_iteration: int,
    input_too_large: bool,
    *,
    langfuse_ctx: Optional[Dict[str, Any]] = None,
) -> Tuple[Dict[str, Any], Dict[str, Any], str, Optional[Dict[str, str]], str]:
    """
    准备聊天接口的Langfuse追踪上下文

    Returns:
        Tuple[base_metadata, root_metadata, root_span_name, trace_context, conversation_type]
    """
    langfuse_ctx = langfuse_ctx or get_langfuse_context()
    langfuse_env = langfuse_ctx.get("environment")
    conversation_type = "deep_research" if is_deep_research else "normal_qa"

    # 构建用户标签
    fallback_user_id = None
    if user_info:
        fallback_user_id = user_info.get("id") or user_info.get("user_id")
    user_code, user_name, user_label = _build_user_label(user_info, fallback_user_id)

    # 获取wiki标识符，优先使用传入的wiki_id保障一致性
    wiki_identifier: Optional[str] = None
    if request.wiki_id:
        wiki_identifier = str(request.wiki_id)
    elif wiki_info_db:
        wiki_identifier = (
            wiki_info_db.get("wiki_id")
            or wiki_info_db.get("wikiId")
            or wiki_info_db.get("id")
        )
        if wiki_identifier is not None:
            wiki_identifier = str(wiki_identifier)

    # 构建标签
    _, label_values = _build_langfuse_labels(
        user_label, wiki_identifier, request.repo_url, request.branch
    )

    user_id_for_trace = user_label or user_code

    # 构建基础元数据
    base_metadata: Dict[str, Any] = {
        "session_id": request.session_id,
        "user_id": user_id_for_trace,
        "user_code": user_code,
        "user_name": user_name,
        "wiki_id": wiki_identifier,
        "repo_url": wiki_info_db.get("repo_url"),
        "branch": wiki_info_db.get("branch"),
        "provider": request.provider,
        "model": request.model,
        "caller": request.caller,
        "language": request.language,
        "client_ip": client_ip,
        "environment": langfuse_env,
        "labels": label_values,
        "user_label": user_label,
    }

    # 处理Deep Research的trace复用
    trace_context: Optional[Dict[str, str]] = None
    if is_deep_research and request.session_id and Langfuse is not None:
        cache_entry = DEEP_RESEARCH_TRACE_CACHE.get(request.session_id)
        if cache_entry:
            cached_trace = cache_entry.get("trace_id")
            if cached_trace:
                trace_context = {"trace_id": cached_trace}
                parent_id = cache_entry.get("root_span_id")
                if parent_id:
                    trace_context["parent_span_id"] = parent_id
        else:
            try:
                trace_context = {
                    "trace_id": Langfuse.create_trace_id(seed=str(request.session_id)),
                }
            except Exception as exc:
                logger.debug(f"Failed to create deterministic trace id: {exc}")
                trace_context = None

    # 构建root元数据
    root_metadata = build_langfuse_metadata(
        base_metadata,
        conversation_type,
        stage="request",
        extra={
            "deep_research_iteration": research_iteration if is_deep_research else None,
            "input_too_large": input_too_large,
        },
    )

    # 确定root span名称
    if request.provider == "gemini-cli":
        root_span_name = "gemini-cli"
    elif is_deep_research:
        root_span_name = "model-ask(deep-research)"
    else:
        root_span_name = "model-ask"

    return base_metadata, root_metadata, root_span_name, trace_context, conversation_type


def _prepare_app_langfuse_context(
    request: AppChatCompletionRequest,
    user_info: Optional[Dict[str, Any]],
    wiki_identifier: Optional[str],
    repo_url: str,
    branch: str,
    client_ip: str,
    app_code_value: Optional[str],
    app_name_value: Optional[str],
    app_id_value: Optional[str],
    topic_id_val: str,
    *,
    langfuse_ctx: Optional[Dict[str, Any]] = None,
) -> Tuple[Dict[str, Any], Dict[str, Any], Dict[str, Any], str, str]:
    """
    准备App聊天接口的Langfuse追踪上下文

    Returns:
        Tuple[base_metadata, root_metadata, generation_metadata, root_span_name, conversation_type]
    """
    conversation_type = "app_chat"
    langfuse_ctx = langfuse_ctx or get_langfuse_context()
    langfuse_env = langfuse_ctx.get("environment")

    # 构建用户标签
    user_code, user_name, user_label = _build_user_label(user_info, request.user_id)

    # 构建标签
    _, label_values = _build_langfuse_labels(
        user_label, wiki_identifier, repo_url, branch
    )

    # 判断是否为MCP来源
    normalized_app_code = str(app_code_value or "").strip().lower()
    is_mcp_source = (request.caller == 2) or (normalized_app_code == "edo")

    if is_mcp_source and "source:mcp" not in label_values:
        label_values.append("source:mcp")

    # 构建app标识符和slug
    app_code_baseline = str(app_code_value or app_name_value or app_id_value or "unknown").strip()
    if not app_code_baseline:
        app_code_baseline = "unknown"

    app_code_slug = app_code_baseline.replace(" ", "_")
    app_code_slug = "".join(
        ch if ch.isalnum() or ch in {"-", "_"} else "_"
        for ch in app_code_slug
    )
    if not app_code_slug:
        app_code_slug = "unknown"

    root_span_name = (
        f"open_mcp:{app_code_slug}"
        if is_mcp_source
        else f"open_api:{app_code_slug}"
    )

    app_id_str = str(app_id_value) if app_id_value is not None else None
    app_code_str = str(app_code_value) if app_code_value is not None else None
    app_name_str = str(app_name_value) if app_name_value is not None else None

    user_id_for_trace = user_label or user_code

    # 构建基础元数据
    base_metadata: Dict[str, Any] = {
        "session_id": request.session_id,
        "user_id": user_id_for_trace,
        "user_code": user_code,
        "user_name": user_name,
        "user_label": user_label,
        "wiki_id": wiki_identifier,
        "repo_url": repo_url,
        "branch": branch,
        "provider": request.provider,
        "model": request.model,
        "caller": request.caller,
        "client_ip": client_ip,
        "environment": langfuse_env,
        "labels": label_values,
        "app_id": app_id_str,
        "app_code": app_code_str,
        "app_name": app_name_str,
        "topic_id": topic_id_val,
    }

    if is_mcp_source:
        base_metadata["source"] = "mcp"

    # 构建root和generation元数据
    root_metadata = build_langfuse_metadata(
        base_metadata,
        conversation_type,
        stage="request",
    )

    generation_metadata = build_langfuse_metadata(
        base_metadata,
        conversation_type,
        stage="generation",
        extra={"app_mode": True},
    )

    return base_metadata, root_metadata, generation_metadata, root_span_name, conversation_type

async def get_gemini_cli_provider_id():
    """获取gemini-cli在配置中的provider ID"""
    providers = configs.get("providers", {})
    for provider_id in providers.keys():
        if provider_id == "gemini-cli":
            return provider_id
    return "gemini-cli"  # 默认ID

@app.post("/chat/completions/stream")
async def chat_completions_stream(request: ChatCompletionRequest, http_request: Request):
    """Stream a chat completion response directly using Google Generative AI"""
    api_key = http_request.headers.get("x-api-key") or request.api_key
    genai.configure(api_key=api_key)
    client_ip = get_client_ip(http_request)
    user_info = get_current_user() or {}
    wiki_info_db = None
    # 如果没有用户信息（开发模式或未认证），使用默认用户ID
    if user_info is None:
        user_info = {'id': 1}  # 默认用户ID
    logger.info(f"Client IP: {client_ip}")
    try:
        start_time = datetime.datetime.now()
        # Check if request contains very large input
        input_too_large = False
        if request.messages and len(request.messages) > 0:
            last_message = request.messages[-1]
            if hasattr(last_message, 'content') and last_message.content:
                tokens = count_tokens(last_message.content, request.provider == "ollama")
                logger.info(f"Request size: {tokens} tokens")
                if tokens > 8000:
                    logger.warning(f"Request exceeds recommended token limit ({tokens} > 7500)")
                    input_too_large = True

                gemini_cli_provider_id = await get_gemini_cli_provider_id()
        is_gemini_cli_mode = request.provider == gemini_cli_provider_id
        wiki_info_db = get_wiki_info_for_chat(request.wiki_id, request.repo_url, request.branch)
        # 确保wiki_info_db对象不为None
        if wiki_info_db is None:
            raise HTTPException(status_code=400, detail="Wiki info not found for the given repository and branch")
            
        request_rag = docchain_manager.get_rag_instance() if not is_gemini_cli_mode else None   # gemini-cli模式不使用RAG，直接对话
        # 提取需要的属性值，避免延迟加载问题
        if wiki_info_db is not None:
            coop_topic_id = wiki_info_db.get('topic_id')
            dept_topic_id = wiki_info_db.get('topic_id_code')
        else:
            coop_topic_id = None
            dept_topic_id = None

        # Process previous messages to build conversation history (only for non-gemini-cli modes)
        if request_rag is not None:
            for i in range(0, len(request.messages) - 1, 2):
                if i + 1 < len(request.messages):
                    user_msg = request.messages[i]
                    assistant_msg = request.messages[i + 1]

                    if user_msg.role == "user" and assistant_msg.role == "assistant":
                        request_rag.memory.add_dialog_turn(
                            user_query=user_msg.content,
                            assistant_response=assistant_msg.content
                        )

        # Check if this is a Deep Research request
        is_deep_research = False
        research_iteration = 1

        # Process messages to detect Deep Research requests
        for msg in request.messages:
            if hasattr(msg, 'content') and msg.content and "[DEEP RESEARCH]" in msg.content:
                is_deep_research = True
                # Only remove the tag from the last message
                if msg == request.messages[-1]:
                    # 移除所有的[DEEP RESEARCH]标记，可能有多个
                    while "[DEEP RESEARCH]" in msg.content:
                        msg.content = msg.content.replace("[DEEP RESEARCH]", "", 1).strip()

        # Count research iterations if this is a Deep Research request
        real_last_maessage = last_message.content
        if is_deep_research:
            # 查找当前深度研究的起始位置
            last_deep_research_start_index = -1
            for i in range(len(request.messages) - 1, -1, -1):
                msg = request.messages[i]
                if msg.role == 'user' and hasattr(msg, 'content') and "[DEEP RESEARCH]" in msg.content:
                    last_deep_research_start_index = i
                    break
            
            # 计算从起始位置开始的助手消息数量
            if last_deep_research_start_index != -1:
                assistant_count = sum(1 for i in range(last_deep_research_start_index + 1, len(request.messages))
                                      if request.messages[i].role == 'assistant')
                research_iteration = assistant_count + 1
            else:
                # This case should ideally not be hit if is_deep_research is true
                research_iteration = 1
            
            logger.info(f"Deep Research request detected - iteration {research_iteration}")
            
            # 添加进度信息到日志，便于前端显示
            total_iterations = 5  # 默认总迭代次数
            progress = min(research_iteration / total_iterations, 1.0)
            logger.info(f"Deep Research progress: {research_iteration}/{total_iterations} ({progress:.0%})")

            # Check if this is a continuation request
            if "continue" in last_message.content.lower() and "research" in last_message.content.lower():
                # Find the original topic from the user message that started this research
                original_topic = None
                if last_deep_research_start_index != -1:
                    original_topic_msg = request.messages[last_deep_research_start_index]
                    if hasattr(original_topic_msg, 'content'):
                        original_topic = original_topic_msg.content.replace("[DEEP RESEARCH]", "").strip()
                        logger.debug(f"Found original research topic: {original_topic}")

                if original_topic:
                    # Replace the continuation message with the original topic
                    real_last_maessage = last_message.content
                    last_message.content = original_topic
                    logger.debug(f"Using original topic for research: {original_topic}")
                else:
                    real_last_maessage = last_message.content
                    # 如果找不到原始问题，记录警告
                    logger.warning("Could not find the original research question for a continuation request.")

            # 确保记录当前使用的研究问题
            logger.debug(f"最终研究问题: {last_message.content}")

        chat_session = {
            "chat_sid": request.session_id,
            "wiki_id": wiki_info_db.get('id'),
            "ip": client_ip,
            "state": 2,
            "created_by": user_info.get('id'),
            "created_date": datetime.datetime.now(),
            "qa_src": request.caller or 1,
            "app_id": None,
        }
        chat_session_db = select_chat_session_by_chat_sid(request.session_id)
        if not chat_session_db:
            # 获取第一条用户消息用于生成标题
            first_message = None
            if request.messages and len(request.messages) > 0:
                # 找到第一条用户消息
                for msg in request.messages:
                    if msg.role == "user":
                        first_message = msg.content
                        break
            
            chat_session_db = add_chat_session(ChatSession(**chat_session), first_message)
        # else:
        #     update_chat_session(ChatSession(**chat_session), chat_session_db.get('id'))
        # Validate request
        if not request.messages or len(request.messages) == 0:
            raise HTTPException(status_code=400, detail="No messages provided")

        last_message = request.messages[-1]
        if last_message.role != "user":
            raise HTTPException(status_code=400, detail="Last message must be from the user")
        if is_deep_research:
            if research_iteration == 1:
                deep_research_remark = 'start'
            else:
                deep_research_remark = 'research'
        else:
            deep_research_remark = None
        # 存储用户消息到数据库
        # 确保msg_sid不超过63个字符的限制，使用项目统一的格式
        user_msg_sid = last_message.id
        if len(user_msg_sid) > 63:
            # 如果太长，使用项目统一的chatcmpl格式
            user_msg_sid = f"chatcmpl-{str(uuid.uuid4())}"
        
        user_chat_history = ChatHistory(
            msg_sid=user_msg_sid,
            chat_id=chat_session_db.get('id'),
            role="user",
            content=real_last_maessage,
            provider="gemini-cli" if is_gemini_cli_mode else "whalecloud",
            deep_research=1 if is_deep_research else 0,
            deep_research_iter=deep_research_remark,
            model=request.model or "",
            msg_data="",
            file_references=json.dumps([fr.model_dump() for fr in (request.file_references or [])], ensure_ascii=False) if request.file_references else None,
            command_params=json.dumps(request.command_params, ensure_ascii=False) if request.command_params else None,
            state=1,
            created_by=user_info.get('id'),
            created_date=datetime.datetime.now(),
            qa_src=request.caller or 1
        )
        if request.images:
            user_chat_history.msg_data = json.dumps({ "images": request.images })
        chat_his_dict = add_chat_history(user_chat_history)

        # Get the query from the last message
        query = last_message.content

        # Prepare Langfuse tracing context using helper function
        langfuse_ctx = get_langfuse_context()
        langfuse_enabled = bool(langfuse_ctx.get("enabled"))

        (
            base_metadata,
            root_metadata,
            root_span_name,
            trace_context,
            conversation_type,
        ) = _prepare_chat_langfuse_context(
            request,
            user_info,
            wiki_info_db,
            client_ip,
            is_deep_research,
            research_iteration,
            input_too_large,
            langfuse_ctx=langfuse_ctx,
        )

        langfuse_env = base_metadata.get("environment")
        label_values = base_metadata.get("labels", [])

        root_span_handle = bind_langfuse_span(
            create_langfuse_span(
                langfuse_enabled,
                root_span_name,
                root_metadata,
                trace_context=trace_context,
            )
        )
        root_span = root_span_handle.enter()
        root_span_closed = not bool(root_span)

        def _close_root_span(exc_type=None, exc_val=None, exc_tb=None):
            """在统一上下文中关闭根跨度，避免Langfuse上下文错乱。"""

            nonlocal root_span_closed
            if not root_span_closed:
                root_span_handle.exit(exc_type, exc_val, exc_tb)
                root_span_closed = True

        # 提取root跨度的trace上下文，供后续子跨度与下游客户端复用，确保统一trace
        root_trace_context: Optional[Dict[str, Any]] = None
        if root_span:
            try:
                root_trace_context = {"trace_id": str(getattr(root_span, "trace_id", ""))}
                if hasattr(root_span, "id"):
                    root_trace_context["parent_span_id"] = getattr(root_span, "id", None)
            except Exception:
                root_trace_context = None

        if root_span:
            def _initialize_root_span() -> None:
                """填充普通聊天流的根跨度数据。"""

                trace_messages = [
                    {
                        "id": getattr(msg, "id", None),
                        "role": msg.role,
                        "content": _truncate_for_langfuse(getattr(msg, "content", None), 800),
                    }
                    for msg in request.messages
                ]
                root_span.update(
                    input={
                        "query": _truncate_for_langfuse(query, 2000),
                        "messages": trace_messages,
                        "has_rag": bool(request_rag),
                        "file_path": request.filePath,
                    }
                )
                trace_metadata = {
                    "request_type": "sse_chat",
                    "conversation_type": conversation_type,
                }
                if langfuse_env:
                    trace_metadata["environment"] = langfuse_env
                if request.repo_url:
                    trace_metadata["repo_url"] = request.repo_url
                if request.branch:
                    trace_metadata["branch"] = request.branch
                if label_values:
                    trace_metadata["labels"] = label_values
                root_span.update_trace(metadata=trace_metadata)

                if (
                    is_deep_research
                    and request.session_id
                    and hasattr(root_span, "trace_id")
                    and hasattr(root_span, "id")
                ):
                    DEEP_RESEARCH_TRACE_CACHE[request.session_id] = {
                        "trace_id": getattr(root_span, "trace_id", ""),
                        "root_span_id": getattr(root_span, "id", ""),
                    }

            root_span_handle.run(_initialize_root_span)

        # Only retrieve documents if input is not too large and not in gemini-cli mode
        context_text = ""
        retrieved_documents = None
        rag_time: Optional[datetime.timedelta] = None

        if not input_too_large and not is_gemini_cli_mode and request_rag is not None:
            rag_query = query
            if request.filePath:
                rag_query = f"Contexts related to {request.filePath}"
                logger.info(f"Modified RAG query to focus on file: {request.filePath}")

            rag_metadata = build_langfuse_metadata(
                base_metadata,
                conversation_type,
                stage="retrieval",
                extra={
                    "iteration": research_iteration if is_deep_research else 1,
                    "rag_query": _truncate_for_langfuse(rag_query, 800),
                },
            )

            rag_span_handle = bind_langfuse_span(
                create_langfuse_span(
                    langfuse_enabled,
                    "chat.retrieval",
                    rag_metadata,
                    trace_context=root_trace_context,
                )
            )
            rag_span = rag_span_handle.enter()
            rag_span_closed = not bool(rag_span)

            def _close_rag_span(exc_type=None, exc_val=None, exc_tb=None):
                nonlocal rag_span_closed
                if not rag_span_closed:
                    rag_span_handle.exit(exc_type, exc_val, exc_tb)
                    rag_span_closed = True

            if rag_span:
                rag_span.update(
                    input={
                        "query": _truncate_for_langfuse(rag_query, 1200),
                        "file_path": request.filePath,
                        "coop_topic_id": coop_topic_id,
                        "dept_topic_id": dept_topic_id,
                    }
                )

                rag_start_time = datetime.datetime.now()
                try:
                    logger.info("使用DocChain检索模式")
                    rag_result, retrieved_contexts = request_rag.call(
                        rag_query,
                        language=request.language,
                        coop_topic_id=coop_topic_id,
                        dept_topic_id=dept_topic_id,
                    )

                    rag_time = datetime.datetime.now() - rag_start_time

                    documents: List[Document] = []
                    if retrieved_contexts:
                        documents = [
                            Document(text=ctx.get("text", ""), meta_data=ctx.get("meta_data", {}))
                            for ctx in retrieved_contexts
                        ]

                        class MockRAGResult:
                            def __init__(self, documents):
                                self.documents = documents

                        retrieved_documents = [MockRAGResult(documents)]
                    else:
                        retrieved_documents = None

                    if retrieved_documents and retrieved_documents[0].documents:
                        documents = retrieved_documents[0].documents
                        logger.info(f"Retrieved {len(documents)} documents")

                        docs_by_file: Dict[str, List[Document]] = {}
                        for doc in documents:
                            file_path = doc.meta_data.get('file_path', 'unknown')
                            docs_by_file.setdefault(file_path, []).append(doc)

                        context_parts = []
                        for file_path, docs in docs_by_file.items():
                            file_path_temp = ensure_markdown_extension(file_path)
                            header = f"## File Path: {file_path_temp}\n\n"
                            content = "\n\n".join([doc.text for doc in docs])
                            context_parts.append(f"{header}{content}")

                        context_text = "\n\n" + "-" * 10 + "\n\n".join(context_parts)
                    else:
                        logger.warning("No documents retrieved from RAG")

                    if rag_span:
                        rag_span.update(
                            output={
                                "document_count": len(documents),
                                "context_characters": len(context_text),
                                "duration_ms": int(rag_time.total_seconds() * 1000),
                            }
                        )
                except Exception as e:
                    logger.error(f"Error in RAG retrieval: {str(e)}")
                    context_text = ""
                    retrieved_documents = None
                    if rag_span:
                        rag_span.update(level="ERROR", status_message=str(e))
                finally:
                    _close_rag_span(None, None, None)
        elif is_gemini_cli_mode:
            logger.info("Gemini-CLI模式：跳过RAG检索，直接进行对话")

        # Get repository information
        repo_url = request.repo_url
        repo_name = repo_url.split("/")[-1] if "/" in repo_url else repo_url

        # Determine repository type
        repo_type = request.type

        # Get language information
        language_code = request.language or "en"
        language_name = {
            "en": "English",
            "ja": "Japanese (日本語)",
            "zh": "Mandarin Chinese (中文)",
            "es": "Spanish (Español)",
            "kr": "Korean (한국어)",
            "vi": "Vietnamese (Tiếng Việt)"
        }.get(language_code, "English")

        logger.info(f"use language: {language_name}")

        # Create system prompt
        if is_deep_research and not is_gemini_cli_mode:
            # Check if this is the first iteration
            is_first_iteration = research_iteration == 1

            # Check if this is the final iteration
            is_final_iteration = research_iteration >= 5

            # 发送带有进度信息的特殊消息，前端可以解析这些消息来更新进度条
            # 注意：在SSE中我们无法直接发送WebSocket类型的消息，但可以在响应流中包含进度信息

            if is_first_iteration:
                # 读取深度研究第一轮的提示词文件
                base_prompt = read_prompt_file('chat_system_prompt_deep_research_first.md')
                system_prompt = base_prompt['content'].format(
                    repo_type=repo_type,
                    repo_url=repo_url,
                    repo_name=repo_name,
                    language_name=language_name
                )
            elif is_final_iteration:
                # 读取深度研究最终轮的提示词文件
                base_prompt = read_prompt_file('chat_system_prompt_deep_research_final.md')
                system_prompt = base_prompt['content'].format(
                    repo_type=repo_type,
                    repo_url=repo_url,
                    repo_name=repo_name,
                    language_name=language_name
                )
            else:
                # 读取深度研究中间轮的提示词文件
                base_prompt = read_prompt_file('chat_system_prompt_deep_research_middle.md')
                system_prompt = base_prompt['content'].format(
                    repo_type=repo_type,
                    repo_url=repo_url,
                    repo_name=repo_name,
                    language_name=language_name,
                    research_iteration=research_iteration
                )
        else:
            # 读取普通聊天请求的提示词文件
            base_prompt = read_prompt_file('chat_system_prompt_normal.md')
            system_prompt = base_prompt['content'].format(
                repo_type=repo_type,
                repo_url=repo_url,
                repo_name=repo_name,
                language_name=language_name
            )

        # Create the prompt with context
        prompt = f"/no_think {system_prompt}\n\n"

        # Only include context if it's not empty
        CONTEXT_START = "<START_OF_CONTEXT>"
        CONTEXT_END = "<END_OF_CONTEXT>"
        if context_text.strip():
            prompt += f"{CONTEXT_START}\n{context_text}\n{CONTEXT_END}\n\n"
        else:
            # Add a note that we're skipping RAG due to size constraints or because it's the isolated API
            logger.warning("No context available from RAG")
            prompt += "<note>Answering without retrieval augmentation.</note>\n\n"

        generation_metadata = build_langfuse_metadata(
            base_metadata,
            conversation_type,
            stage="generation",
            extra={
                "iteration": research_iteration if is_deep_research else 1,
                "has_context": bool(context_text.strip()),
            },
        )
        generation_name = f"{conversation_type}.generation"

        query = optimize_file_paths(query)
        prompt += f"<query>\n{query}\n</query>\n\nAssistant: "

        provider_model_config = get_model_config(request.provider, request.model)
        model_kwargs_config = provider_model_config.get("model_kwargs", {})
        resolved_model = request.model or provider_model_config.get("model")
        base_metadata["model"] = resolved_model
        if root_span:
            root_span.update(metadata={"resolved_model": resolved_model})

        logger.info(f"model prompt: {prompt}")

        if request.provider == "openai" or request.provider == "whalecloud":
            logger.info(f"Using Openai protocol with model: {request.model}")

            # Check if an API key is set for Openai
            if not OPENAI_API_KEY:
                logger.warning("OPENAI_API_KEY not configured, but continuing with request")
                # We'll let the OpenAIClient handle this and return an error message

            # Initialize Openai client
            model = OpenAIClient()
            model_kwargs = {
                "model": request.model,
                "stream": True,
                "temperature": model_kwargs_config.get("temperature"),
                "top_p": model_kwargs_config.get("top_p"),
                "api_key": api_key,
            }

            api_kwargs = model.convert_inputs_to_api_kwargs(
                input=prompt,
                model_kwargs=model_kwargs,
                model_type=ModelType.LLM
            )
        elif request.provider == "gemini-cli":
            logger.info(f"Using Gemini CLI with model: {request.model}")

            # Check if Gemini CLI API key is set
            if not OPENAI_API_KEY: # Gemini CLI uses OpenAI API key
                logger.warning("OPENAI_API_KEY not configured for Gemini CLI, but continuing with request")
                # We'll let the GeminiCliClient handle this and return a friendly error message

            model = GeminiCliClient()
            messages = [{"role": msg.role, "content": msg.content} for msg in request.messages]

            images = []
            if request.images:
                for image in request.images:
                    mime_type, data = resolve_base64(image)
                    images.append({
                        "role": "user",
                        "content": {
                            "type": "image",
                            "image_info": {
                                "mime_type": mime_type,
                                "data": data,
                            }
                        }
                    })
            
            project_name = request.repo_url.split("/")[-1] if "/" in request.repo_url else request.repo_url
            if project_name.endswith(".git"):
                project_name = project_name[:-4]
                logger.info(f"Removed .git suffix from project name: {project_name}")
    
            model_kwargs = {
                "messages": messages,
                "images": images,
                "stream": True,
                "session_id": request.session_id,
                "api_key": api_key,
                "repo_url": repo_url,  # 使用新的参数格式
                "branch": request.branch,      # 传递分支信息
                "project_name": project_name,  # 保留向后兼容
                "model": request.model,  # 添加模型名称
                "file_references": request.file_references,  # 添加文件引用
                "command_params": request.command_params,  # 添加命令参数
                "timeout": 300.0,
                "topic_id": coop_topic_id,
            "wiki_id": wiki_info_db.get('id'),
                "langfuse_parent_span_active": True,  # 标记在SSE层已开启Langfuse跨度，避免在Gemini客户端重复创建
                "user_name": base_metadata.get("user_name"),  # 传递用户姓名，保持Langfuse记录一致
                "userName": base_metadata.get("user_name"),  # 兼容外部需要的CamelCase命名
            }

            api_kwargs = model.convert_inputs_to_api_kwargs(
                input=prompt,
                model_kwargs=model_kwargs,
                model_type=ModelType.LLM
            )
        else:
            # Initialize Google Generative AI model
            model = genai.GenerativeModel(
                model_name=provider_model_config.get("model"),
                generation_config={
                    "temperature": model_kwargs_config.get("temperature"),
                    "top_p": model_kwargs_config.get("top_p"),
                }
            )

        # Create a streaming response
        async def response_stream():
            # 用于累积AI助手的响应
            logger.info(f"开始调用大模型!")
            assistant_response = ""
            error_code = None  # 用于存储错误码
            tool_calls_info = None
            chat_history_sid = f"chatcmpl-{str(uuid.uuid4())}"  # Initialize early to ensure it's always available
            usage_details: Optional[Dict[str, int]] = None
            span_exc_info: Tuple[Optional[type], Optional[BaseException], Optional[Any]] = (None, None, None)

            generation_span_handle = bind_langfuse_span(
                create_langfuse_span(
                    langfuse_enabled,
                    "chat.generation",
                    generation_metadata,
                    as_generation=True,
                    trace_context=root_trace_context,
                )
            )
            generation_span = generation_span_handle.enter()
            generation_span_closed = not bool(generation_span)

            def _close_generation_span(exc_type=None, exc_val=None, exc_tb=None):
                nonlocal generation_span_closed
                if not generation_span_closed:
                    generation_span_handle.exit(exc_type, exc_val, exc_tb)
                    generation_span_closed = True

            if generation_span:
                generation_input_payload = {
                    "prompt": _truncate_for_langfuse(prompt, 4000),
                    "query": _truncate_for_langfuse(query, 2000),
                    "context_available": bool(context_text.strip()),
                }
                generation_span.update(
                    input={
                        **generation_input_payload,
                    },
                    model=resolved_model,
                    metadata={
                        "conversation_type": conversation_type,
                        "provider": request.provider,
                    },
                    model_parameters={
                        "temperature": model_kwargs_config.get("temperature"),
                        "top_p": model_kwargs_config.get("top_p"),
                    },
                )
                # 将本次generation输入摘要同步到root跨度的input，便于在根视图快速查看
                if root_span:
                    def _append_generation_input_to_root():
                        payload = {
                            "generation_input": {
                                **generation_input_payload,
                                "model": resolved_model,
                                "model_parameters": {
                                    "temperature": model_kwargs_config.get("temperature"),
                                    "top_p": model_kwargs_config.get("top_p"),
                                },
                            }
                        }
                        root_span.update(input=payload)

                    try:
                        root_span_handle.run(_append_generation_input_to_root)
                    except Exception as _e:
                        logger.debug(f"同步generation输入到root跨度失败: {_e}")

            try:
                # 若使用gemini-cli，下游工具调用需要继承当前generation作为父级，显式传递trace上下文
                if request.provider == "gemini-cli" and generation_span and root_trace_context:
                    gen_trace_ctx = {"trace_id": root_trace_context.get("trace_id")}
                    try:
                        if hasattr(generation_span, "id"):
                            gen_trace_ctx["parent_span_id"] = getattr(generation_span, "id", None)
                    except Exception:
                        pass
                    try:
                        api_kwargs["langfuse_trace_context"] = gen_trace_ctx
                    except Exception:
                        pass
                if request.provider == "openai" or request.provider == "whalecloud":
                    try:
                        # Get the response and handle it properly using the previously created api_kwargs
                        logger.info("Making Openai API call")
                        response = await model.acall(api_kwargs=api_kwargs, model_type=ModelType.LLM)
                        # Handle streaming response from Openai
                        async for chunk in response:
                            choices = getattr(chunk, "choices", [])
                            chat_history_sid = getattr(chunk, "id", "") or chat_history_sid

                            chunk_usage = getattr(chunk, "usage", None)
                            if chunk_usage:
                                usage_details = {
                                    "prompt_tokens": getattr(chunk_usage, "prompt_tokens", None),
                                    "completion_tokens": getattr(chunk_usage, "completion_tokens", None),
                                    "total_tokens": getattr(chunk_usage, "total_tokens", None),
                                }

                            if len(choices) > 0:
                                delta = getattr(choices[0], "delta", None)
                                if delta is not None:
                                    text = getattr(delta, "content", None)
                                    if text is not None:
                                        logger.debug(f"whalecloud model returns: {text}")
                                        assistant_response += text
                                        res = {
                                            'content': text
                                        }
                                        yield build_event_stream(chat_history_sid, "data", json.dumps(res))
                        if rag_time is not None:
                            logger.info(f"docchain rag 总耗时 {rag_time}")
                        logger.info(f"请求总耗时: {datetime.datetime.now() - start_time}")
                        yield build_event_stream(chat_history_sid, "data", "[DONE]")
                    except Exception as e_openai:
                        logger.error(f"Error with Openai API: {e_openai}")
                        # 提取错误码
                        error_code, res = build_error_response(error_code, 'ailab-', f'调用whalecloud错误: {str(e_openai)}', True)

                        assistant_response = res.get('content')
                        error_message = str(e_openai)
                        try:
                            if error_message.startswith('API错误'):
                                error = error_message.split(':')[0].strip()
                                error_code = error.replace('API错误(', '').replace(')', '')
                        except Exception as e:
                            logger.error(f"Error with Openai API，openai报错格式不正确: {e}")

                        yield build_event_stream(chat_history_sid, "data", json.dumps(res))
                elif request.provider == "gemini-cli":
                    try:
                        # Get the response and handle it properly using the previously created api_kwargs
                        logger.info("Making Gemini CLI API call")
                        response = model.acall(api_kwargs=api_kwargs)
                        # Handle streaming response from Gemini CLI
                        tool_calls_info = []
                        async for chunk, line_str in response:
                            # Handle tool calls and regular content
                            if isinstance(chunk, str):
                                ai_res = json.loads(chunk)
                                # Fallback to pre-generated sid when missing in provider chunk
                                chat_history_sid = ai_res.get('id') or chat_history_sid
                                res_type = ai_res.get('type')
                                if res_type in ['tool_call', 'tool_execution_status', 'tool_error']:
                                    # Forward tool messages with proper SSE event types
                                    if res_type == 'tool_call':
                                        event_type = 'tool_call'
                                        content = ai_res.get('content')
                                        logger.info(f"tool_call: {content}")
                                        if content:
                                            tools = content
                                            tools['status'] = 'executing'   
                                            if tools['tool_calls'][0]['function']['name'] == 'searchByDocchain':
                                              # arguments is a JSON string, need to parse it first
                                              args_str = tools['tool_calls'][0]['function']['arguments']
                                              if isinstance(args_str, str):
                                                  args_dict = json.loads(args_str)
                                                  args_dict['docchainApiKey'] = '********'
                                                  tools['tool_calls'][0]['function']['arguments'] = json.dumps(args_dict, ensure_ascii=False)
                                              elif isinstance(args_str, dict):
                                                  args_str['docchainApiKey'] = '********'
                                            tool_calls_info.append(tools)
                                        yield build_event_stream(chat_history_sid, event_type, f"TOOL_CALL:{json.dumps(content, ensure_ascii=False)}")
                                    elif res_type == 'tool_execution_status':
                                        event_type = 'tool_execution_status'
                                        content = ai_res.get('content')
                                        logger.info(f"tool_execution_status: {content}")
                                        if content:
                                            tools = content
                                            if tools.get('status') == 'completed':
                                                for tool in tool_calls_info:
                                                    if tool['status'] == 'executing':
                                                        tool['status'] = 'completed'
                                        yield build_event_stream(chat_history_sid, event_type, f"TOOL_STATUS:{json.dumps(content, ensure_ascii=False)}")
                                    else:
                                        event_type = 'tool_error'
                                        error_code = ai_res.get('error', {}).get('code', '500')
                                        if not error_code.startswith('wct-cli-'):
                                            error_code = 'wct-cli-' + error_code
                                        assistant_response = ai_res.get('error', {}).get('message', '')
                                        logger.info(f"tool_error: {chunk}")
                                        for tool in tool_calls_info:
                                            if tool['status'] == 'executing':
                                                tool['status'] = 'failed'
                                        yield build_event_stream(chat_history_sid, event_type, f"TOOL_ERROR:{chunk}")
                                        break
                                    # Format as SSE

                                else:
                                    # Forward structured sandbox status inside content string for frontend compatibility
                                    if res_type == 'sandbox_status':
                                        try:
                                            logger.info(f"sandbox_status: {ai_res}")
                                            # Wrap as { content: JSON_STRING } so frontend can parse from content
                                            sandbox_payload = {
                                                'content': json.dumps(ai_res)
                                            }
                                            yield build_event_stream(chat_history_sid, "data", json.dumps(sandbox_payload))
                                        except Exception as e:
                                            logger.warning(f"Failed to forward sandbox_status: {e}")
                                        continue
                                    if res_type == 'message':
                                        content = ai_res.get('content')
                                        if content is not None:
                                            logger.debug(f"Forwarded content: {content}")
                                            assistant_response += content
                                            # Yield regular content
                                            res = {
                                                'content': content
                                            }
                                            logger.info(f"gemini-cli model returns: {content}")
                                            yield build_event_stream(chat_history_sid, "data", json.dumps(res, ensure_ascii=False))
                            else:
                                logger.warning(f"gemini 返回了非字符串类型: {chunk}")
                                chunk_str = str(chunk)
                                assistant_response += chunk_str
                                yield build_event_stream(chat_history_sid, "data", chunk_str)
                        logger.info(f"请求总耗时: {datetime.datetime.now() - start_time}")
                        yield build_event_stream(chat_history_sid, "data", "[DONE]")
                    except httpx.TimeoutException as e_gemini:
                        logger.error(f"Timeout error with Gemini CLI API: {e_gemini}")
                        error_code = error_code if error_code is not None else '408'  # 请求超时
                        error_code, res = build_error_response(error_code, 'wct-cli-', f'调用gemini-cli超时(timeout): {str(e_gemini)}', False)
                        assistant_response += res.get('content')
                        yield build_event_stream(chat_history_sid, "data", json.dumps(res))
                    except Exception as e_gemini:
                        logger.error(f"Error with Gemini CLI API: {e_gemini}")
                        # 提取错误码
                        error_code, res = build_error_response(error_code, 'wct-cli-', str(e_gemini), True)
                        assistant_response += res.get('content')
                        yield build_event_stream(chat_history_sid, "data", json.dumps(res))
                else:
                    # Generate streaming response
                    response = model.generate_content(prompt, stream=True)
                    # Stream the response
                    for chunk in response:
                        if hasattr(chunk, 'text'):
                            assistant_response += chunk.text
                            res = {
                                'content': chunk.text
                            }
                            yield build_event_stream(chat_history_sid, "data", json.dumps(res))
                    yield build_event_stream(chat_history_sid, "data", "[DONE]")

                if is_deep_research:
                    if research_iteration >=5:
                        deep_research_remark = 'end'
                    else:
                        deep_research_remark = 'research'
                else:
                    deep_research_remark = None
                # 存储AI助手消息到数据库
                if error_code is not None and tool_calls_info is not None:
                    for tool in tool_calls_info:
                        if tool.get('status') == 'executing':
                            tool['status'] = 'failed'
                if chat_history_sid is None or chat_history_sid == "":
                    chat_history_sid = f"chatcmpl-{str(uuid.uuid4())}"
                assistant_chat_history = ChatHistory(
                    msg_sid=chat_history_sid,
                    chat_id=chat_session_db.get('id'),
                    role="assistant",
                    content=assistant_response,
                    provider="gemini-cli" if is_gemini_cli_mode else "whalecloud",
                    deep_research= 1 if is_deep_research else 0,
                    deep_research_iter=deep_research_remark,
                    model=request.model or "",
                    tool_calls=json.dumps(tool_calls_info, ensure_ascii=False) if tool_calls_info else "",
                    command_params=json.dumps(request.command_params, ensure_ascii=False) if request.command_params else None,
                    file_references=json.dumps([fr.model_dump() for fr in (request.file_references or [])], ensure_ascii=False) if request.file_references else None,
                    msg_data="",
                    state=1,
                    parent_id=chat_his_dict.get('id'),
                    error_code=error_code,
                    created_by=user_info.get('id'),
                    created_date=datetime.datetime.now(),
                    qa_src=request.caller or 1
                )
                add_chat_history(assistant_chat_history)

            except Exception as e_outer:
                logger.error(f"Error in streaming response: {e_outer}")
                error_message = str(e_outer)

                # Check for token limit errors
                if "maximum context length" in error_message or "token limit" in error_message or "too many tokens" in error_message:
                    # If we hit a token limit error, try again without context
                    logger.warning("Token limit exceeded, retrying without context")
                    try:
                        # Create a simplified prompt without context
                        simplified_prompt = f"/no_think {system_prompt}\n\n"
                        simplified_prompt += "<note>Answering without retrieval augmentation due to input size constraints.</note>\n\n"
                        simplified_prompt += f"<query>\n{query}\n</query>\n\nAssistant: "

                        if request.provider == "openai" or request.provider == "whalecloud":
                            try:
                                # Create new api_kwargs with the simplified prompt
                                fallback_api_kwargs = model.convert_inputs_to_api_kwargs(
                                    input=simplified_prompt,
                                    model_kwargs=model_kwargs,
                                    model_type=ModelType.LLM
                                )

                                # Get the response using the simplified prompt
                                logger.info("Making fallback Openai API call")
                                fallback_response = await model.acall(api_kwargs=fallback_api_kwargs, model_type=ModelType.LLM, client_ip=client_ip)

                                # Handle streaming fallback_response from Openai
                                async for chunk in fallback_response:
                                    text = chunk if isinstance(chunk, str) else getattr(chunk, 'text', str(chunk))
                                    assistant_response += text
                                    yield build_event_stream(chat_history_sid, "data", text)
                            except Exception as e_fallback:
                                logger.error(f"Error with Openai API fallback: {str(e_fallback)}")
                                # 提取错误码
                                error_code, res = build_error_response(error_code, 'ailab-', f'调用whalecloud API 报错: {str(e_fallback)}', False)
                                assistant_response = json.dumps(res)
                                yield f"event: data\ndata: {json.dumps(res)}\n\n"
                        elif request.provider == "gemini-cli":
                            try:
                                # Create new api_kwargs with the simplified prompt
                                fallback_api_kwargs = model.convert_inputs_to_api_kwargs(
                                    input=simplified_prompt,
                                    model_kwargs=model_kwargs,
                                    model_type=ModelType.LLM
                                )

                                # Get the response using the simplified prompt
                                logger.info("Making fallback Gemini CLI API call")
                                fallback_response = await model.acall(api_kwargs=fallback_api_kwargs, model_type=ModelType.LLM)

                                # Handle streaming fallback_response from Gemini CLI
                                async for chunk, line_str in fallback_response:
                                    text = chunk if isinstance(chunk, str) else getattr(chunk, 'text', str(chunk))
                                    assistant_response += text
                                    yield text
                            except Exception as e_fallback:
                                logger.error(f"Error with Gemini CLI API fallback: {str(e_fallback)}")
                                # 提取错误码
                                error_code, res = build_error_response(error_code, 'wct-cli-', f'调用wct-cli 服务报错: {str(e_fallback)}', False)
                                assistant_response = json.dumps(res)
                                yield f"event: data\ndata: {json.dumps(res)}\n\n"
                        else:
                            # Initialize Google Generative AI model
                            fallback_model_config = get_model_config(request.provider, request.model)
                            fallback_model = genai.GenerativeModel(
                                model_name=fallback_model_config["model"],
                                generation_config={
                                    "temperature": fallback_model_config["model_kwargs"].get("temperature", 0.7),
                                    "top_p": fallback_model_config["model_kwargs"].get("top_p", 0.8),
                                }
                            )

                            # Get streaming response using simplified prompt
                            fallback_response = fallback_model.generate_content(simplified_prompt, stream=True)
                            # Stream the fallback response
                            for chunk in fallback_response:
                                if hasattr(chunk, 'text'):
                                    assistant_response += chunk.text
                                    yield chunk.text
                    except Exception as e2:
                        logger.error(f"Error in fallback streaming response: {str(e2)}")
                        # 提取错误码
                        error_code, res = build_error_response('500', '', str(e2), True)
                        assistant_response = json.dumps(res)
                        yield f"event: data\ndata: {json.dumps(res)}\n\n"
                else:
                    # For other errors, return the error message
                    error_code, res = build_error_response(error_code, '', f"Error: {error_message}", True)
                    assistant_response = json.dumps(res)
                    yield f"event: data\ndata: {json.dumps(res)}\n\n"

                # 即使在出错的情况下，也尝试存储AI助手的消息（如果有的话）
                if assistant_response:
                    try:
                        assistant_chat_history = ChatHistory(
                            msg_sid=chat_history_sid,
                            chat_id=chat_session_db.get('id'),
                            role="assistant",
                            content=assistant_response,
                            model=request.model or "",
                            provider="gemini-cli" if is_gemini_cli_mode else "whalecloud",
                            msg_data="",
                            state=1,
                            error_code=error_code,
                            created_by=user_info.get('id'),
                            parent_id=chat_his_dict.get('id'),
                            created_date=datetime.datetime.now(),
                            qa_src=request.caller or 1
                        )
                        add_chat_history(assistant_chat_history)
                    except Exception as e:
                        logger.error(f"Error saving assistant message to database: {str(e)}")
                        res = {
                            'content': f"\n内部服务错误"
                        }
                        yield f"event: data\ndata: {json.dumps(res)}\n\n"
            except Exception as stream_error:
                span_exc_info = (type(stream_error), stream_error, stream_error.__traceback__)
                logger.error(f"Unexpected error in response stream: {stream_error}")
                if generation_span:
                    generation_span.update(level="ERROR", status_message=str(stream_error))
                raise
            finally:
                duration_ms = int((datetime.datetime.now() - start_time).total_seconds() * 1000)

                output_payload = {
                    "assistant_response": _truncate_for_langfuse(assistant_response, 4000),
                    "error_code": error_code,
                    "duration_ms": duration_ms,
                }

                if generation_span:
                    if span_exc_info[0] is None:
                        update_kwargs: Dict[str, Any] = {"output": output_payload}
                        if usage_details:
                            filtered_usage = {
                                key: value for key, value in usage_details.items() if value is not None
                            }
                            if filtered_usage:
                                update_kwargs["usage_details"] = filtered_usage
                        generation_span.update(**update_kwargs)
                    else:
                        generation_span.update(output=output_payload)

                _close_generation_span(*span_exc_info)

                if root_span:
                    try:
                        if span_exc_info[0] is None:
                            root_output = {**output_payload, "conversation_type": conversation_type}
                            # 附带usage与工具调用摘要，便于在根视图直接看到关键指标
                            if usage_details:
                                filtered_usage = {
                                    key: value for key, value in (usage_details or {}).items() if value is not None
                                }
                                if filtered_usage:
                                    root_output["usage_details"] = filtered_usage
                            if 'tool_calls_info' in locals() and tool_calls_info:
                                root_output["tool_calls"] = tool_calls_info
                            # 同步一个明确的 generation_output 字段，兼容外部看板聚合
                            root_output["generation_output"] = {
                                "assistant_response": _truncate_for_langfuse(assistant_response, 2000),
                                "error_code": error_code,
                            }
                            root_span.update_trace(output=root_output)
                        else:
                            root_span.update(level="ERROR", status_message=str(span_exc_info[1]))
                    except Exception as _e:
                        logger.debug(f"同步generation输出到root跨度失败: {_e}")
                _close_root_span(*span_exc_info)

        # Return streaming response
        return StreamingResponse(
            root_span_handle.run(response_stream),
            media_type="text/event-stream",
        )

    except HTTPException:
        raise
    except Exception as e_handler:
        error_msg = f"Error in streaming chat completion: {e_handler}"
        logger.error(error_msg)
        if 'root_span_handle' in locals():
            if 'root_span' in locals() and root_span and 'root_span_closed' in locals() and not root_span_closed:
                try:
                    root_span_handle.run(
                        lambda: root_span.update(level="ERROR", status_message=str(e_handler))
                    )
                except Exception as span_update_error:
                    logger.debug(f"Failed to update Langfuse root span on error: {span_update_error}")
            if '_close_root_span' in locals():
                _close_root_span(*sys.exc_info())
        chat_history_sid = f"chatcmpl-{str(uuid.uuid4())}"

        error_code = '500'

        # 在没调用ai的时候报错，走到这里，也需要插入一条ai消息保证数据库的完整性
        # 检查chat_session_db是否已定义，避免访问未定义的变量
        if chat_session_db:
            assistant_chat_history = ChatHistory(
                msg_sid=chat_history_sid,
                chat_id=chat_session_db.get('id'),
                role="assistant",
                content=error_msg,
                provider="gemini-cli" if is_gemini_cli_mode else "whalecloud",
                model=request.model or "",
                msg_data="",
                state=1,
                error_code=error_code,
                created_by=user_info.get('id') if user_info else 1,
                parent_id=chat_his_dict.get('id') if chat_his_dict else None,
                created_date=datetime.datetime.now(),
                qa_src=request.caller or 1
            )
            add_chat_history(assistant_chat_history)
        else:
            logger.warning("chat_session_db not available, skipping assistant chat history insertion")
        raise HTTPException(status_code=500, detail=error_msg)

@app.post("/chat/app/stream")
async def app_chat_stream(request: AppChatCompletionRequest, http_request: Request):
    """App级别沙箱对话，只支持gemini-cli"""
    logger.info(f"开始调用app_chat_stream，参数: {request.app_id}, {request.app_code}, {request.wiki_id}, {request.model}, {request.provider}, {request.api_key}, {request.timeout}, {request.caller}")
    api_key = http_request.headers.get("x-api-key") or request.api_key
    if request.provider != "gemini-cli":
        raise HTTPException(status_code=400, detail="Only gemini-cli is supported for app chat")

    client_ip = get_client_ip(http_request)
    # 判空与安全默认值，避免wiki缺失导致异常
    branch = "main"
    repo_url = ""
    wiki_primary_id = None
    topic_id_val = ""
    from api.database.base import session_scope
    with session_scope() as session:
        try:
            wiki_info_db = get_wiki_info(session, request.wiki_id) if request.wiki_id else None
            if wiki_info_db:
                branch = getattr(wiki_info_db, "branch", None) or "main"
                repo_url = getattr(wiki_info_db, "repo_url", None) or ""
                wiki_primary_id = getattr(wiki_info_db, "id", None)
                topic_id_val = getattr(wiki_info_db, "project_topic_id", "") or ""
        except Exception:
            # 保持默认值
            pass

    # 查询App信息并提取属性值（在session外使用）
    app_code_value = None
    app_name_value = None
    app_id_value = None
    try:
        from sqlmodel import select
        from api.database.base import session_scope
        from api.model.app import App
        with session_scope() as session:
            app_row = None
            # 优先使用 app_id 查询（兼容数字主键或字符串 app_id）
            if request.app_id:
                # 尝试按数字主键
                try:
                    app_pk = int(str(request.app_id))
                    app_row = session.exec(select(App).where(App.id == app_pk)).first()
                except Exception:
                    app_row = None
                # 退回按业务 app_id 字段
                if app_row is None:
                    app_row = session.exec(select(App).where(App.app_id == str(request.app_id))).first()
            # 其次使用 app_code 查询
            if app_row is None and request.app_code:
                app_row = session.exec(select(App).where(App.app_code == str(request.app_code))).first()

            # 在session内提取所有需要的属性值
            if app_row:
                app_code_value = getattr(app_row, "app_code", None)
                app_name_value = getattr(app_row, "app_name", None)
                app_id_value = getattr(app_row, "id", None)
    except Exception as _e:
        logger.warning(f"查询应用创建人失败，使用当前用户作为对话人: {_e}")

    langfuse_ctx = get_langfuse_context()
    langfuse_enabled = bool(langfuse_ctx.get("enabled"))

    user_info = get_current_user() or {}

    wiki_identifier: Optional[str] = None
    # 优先使用请求携带的wiki_id，确保标签取值稳定
    if request.wiki_id:
        wiki_identifier = str(request.wiki_id)
    elif wiki_info_db:
        candidate_identifier = getattr(wiki_info_db, "wiki_id", None) or getattr(wiki_info_db, "wikiId", None)
        if candidate_identifier is not None:
            wiki_identifier = str(candidate_identifier)
    elif wiki_primary_id:
        wiki_identifier = str(wiki_primary_id)
    app_code_value = app_code_value or request.app_code
    app_id_value = app_id_value or request.app_id

    (
        base_metadata,
        root_metadata,
        generation_metadata,
        root_span_name,
        conversation_type,
    ) = _prepare_app_langfuse_context(
        request,
        user_info,
        wiki_identifier,
        repo_url,
        branch,
        client_ip,
        app_code_value,
        app_name_value,
        app_id_value,
        topic_id_val,
        langfuse_ctx=langfuse_ctx,
    )

    label_values = base_metadata.get("labels", [])
    langfuse_env = base_metadata.get("environment")
    generation_name = f"{conversation_type}.generation"

    root_span_handle = None
    root_span = None
    root_span_closed = True

    try:
        start_time = datetime.datetime.now()

        # 基本校验
        if not request.messages or len(request.messages) == 0:
            raise HTTPException(status_code=400, detail="No messages provided")
        last_message = request.messages[-1]
        if last_message.role != "user":
            raise HTTPException(status_code=400, detail="Last message must be from the user")

        # 构造对话上下文（app对话不做RAG，直接透传消息）
        messages = [{"role": m.role, "content": m.content} for m in request.messages]

        # 记录会话与历史（创建人为应用创建人：此处沿用当前用户，若需严格绑定可调整为查询App.created_by）
        chat_session = {
            "chat_sid": request.session_id,
            "wiki_id": wiki_primary_id,  # app对话也绑定wiki
            "ip": client_ip,
            "state": 2,
            "created_by": request.user_id,
            "created_date": datetime.datetime.now(),
            "qa_src": request.caller,
            "app_id": request.app_id
        }
        chat_session_db = select_chat_session_by_chat_sid(request.session_id)
        if not chat_session_db:
            first_user_msg = next((m.content for m in request.messages if m.role == "user"), "" )
            chat_session_db = add_chat_session(ChatSession(**chat_session), first_user_msg)

        user_msg_sid = last_message.id
        if len(user_msg_sid) > 63:
            user_msg_sid = f"chatcmpl-{str(uuid.uuid4())}"

        user_chat_history = ChatHistory(
            msg_sid=user_msg_sid,
            chat_id=chat_session_db.get('id'),
            role="user",
            content=last_message.content,
            provider="gemini-cli",
            deep_research=0,
            deep_research_iter=None,
            model=request.model or "",
            msg_data="",
            state=1,
            created_by=request.user_id,
            created_date=datetime.datetime.now(),
            qa_src=request.caller
        )
        chat_his_dict = add_chat_history(user_chat_history)

        root_span_handle = bind_langfuse_span(
            create_langfuse_span(
                langfuse_enabled,
                root_span_name,
                root_metadata,
            )
        )
        root_span = root_span_handle.enter()
        root_span_closed = not bool(root_span)

        def _close_root_span(exc_type=None, exc_val=None, exc_tb=None):
            """在统一上下文中关闭根跨度，保证Langfuse记录一致。"""

            nonlocal root_span_closed
            if not root_span_closed:
                root_span_handle.exit(exc_type, exc_val, exc_tb)
                root_span_closed = True

        # 提取root跨度trace上下文，传递给子跨度和下游，保证统一trace
        root_trace_context: Optional[Dict[str, Any]] = None
        if root_span:
            try:
                root_trace_context = {"trace_id": str(getattr(root_span, "trace_id", ""))}
                if hasattr(root_span, "id"):
                    root_trace_context["parent_span_id"] = getattr(root_span, "id", None)
            except Exception:
                root_trace_context = None

        if root_span:
            def _prepare_root_span() -> None:
                """写入App对话模式的根跨度信息。"""

                trace_messages = [
                    {
                        "id": getattr(msg, "id", None),
                        "role": msg.role,
                        "content": _truncate_for_langfuse(getattr(msg, "content", None), 800),
                    }
                    for msg in request.messages
                ]

                app_context = {
                    key: value
                    for key, value in {
                        "app_id": app_id_value,
                        "app_code": app_code_value,
                        "app_name": app_name_value,
                    }.items()
                    if value not in (None, "")
                }

                root_input: Dict[str, Any] = {
                    "messages": trace_messages,
                }
                if app_context:
                    root_input["app"] = app_context

                root_span.update(input=root_input)

                trace_metadata: Dict[str, Any] = {
                    "request_type": conversation_type,
                }
                if langfuse_env:
                    trace_metadata["environment"] = langfuse_env
                if repo_url:
                    trace_metadata["repo_url"] = repo_url
                if branch:
                    trace_metadata["branch"] = branch
                if app_code_value:
                    trace_metadata["app_code"] = str(app_code_value)
                if app_name_value:
                    trace_metadata["app_name"] = str(app_name_value)
                if label_values:
                    trace_metadata["labels"] = label_values

                root_span.update_trace(metadata=trace_metadata)

            root_span_handle.run(_prepare_root_span)

        # 构建GeminiCliClient参数（App模式）
        model = GeminiCliClient()

        model_kwargs = {
            "messages": messages,
            "stream": True,
            "session_id": request.session_id,
            "api_key": api_key,
            "repo_url": repo_url,  
            "branch": branch,
            "model": request.model,
            "app_code": request.app_code,
            "app_id": request.app_id,
            "timeout": request.timeout,
            "topic_id": topic_id_val,
            "langfuse_parent_span_active": True,  # 提醒Gemini客户端沿用当前Langfuse上下文，避免重复根跨度
            "user_name": base_metadata.get("user_name"),  # 传递用户姓名，保持Langfuse追踪信息一致
            "userName": base_metadata.get("user_name"),  # 兼容CamelCase参数，满足外部依赖
        }
        api_kwargs = model.convert_inputs_to_api_kwargs(input=None, model_kwargs=model_kwargs, model_type=ModelType.LLM)

        async def response_stream():
            logger.info("开始调用app级别gemini-cli对话")
            assistant_response = ""
            chat_history_sid = f"chatcmpl-{str(uuid.uuid4())}"
            error_code = None
            usage_details: Optional[Dict[str, int]] = None
            span_exc_info: Tuple[Optional[type], Optional[BaseException], Optional[Any]] = (None, None, None)

            generation_span_handle = bind_langfuse_span(
                create_langfuse_span(
                    langfuse_enabled,
                    generation_name,
                    generation_metadata,
                    as_generation=True,
                    trace_context=root_trace_context,
                )
            )
            generation_span = generation_span_handle.enter()
            generation_span_closed = not bool(generation_span)

            def _close_generation_span(exc_type=None, exc_val=None, exc_tb=None):
                nonlocal generation_span_closed
                if not generation_span_closed:
                    generation_span_handle.exit(exc_type, exc_val, exc_tb)
                    generation_span_closed = True

            if generation_span:
                trace_messages = [
                    {
                        "id": getattr(msg, "id", None),
                        "role": msg.role,
                        "content": _truncate_for_langfuse(getattr(msg, "content", None), 800),
                    }
                    for msg in request.messages
                ]

                generation_input: Dict[str, Any] = {
                    "messages": trace_messages,
                    "app_mode": True,
                }
                app_context = {
                    key: value
                    for key, value in {
                        "app_id": app_id_value,
                        "app_code": app_code_value,
                        "app_name": app_name_value,
                    }.items()
                    if value not in (None, "")
                }
                if app_context:
                    generation_input["app"] = app_context

                generation_update_kwargs: Dict[str, Any] = {
                    "input": generation_input,
                    "model": request.model,
                    "metadata": {
                        "conversation_type": conversation_type,
                        "provider": request.provider,
                        "app_code": app_code_value,
                        "app_name": app_name_value,
                    },
                }

                model_parameters: Dict[str, Any] = {}
                if request.timeout is not None:
                    model_parameters["timeout"] = request.timeout
                if model_parameters:
                    generation_update_kwargs["model_parameters"] = model_parameters

                generation_span.update(**generation_update_kwargs)
                # 将app模式的generation输入同步到root跨度
                if root_span:
                    def _append_app_generation_input_to_root():
                        payload = {
                            "generation_input": {
                                **generation_input,
                                "model": request.model,
                                "model_parameters": model_parameters or None,
                                "app": app_context or None,
                            }
                        }
                        # 清理None
                        payload["generation_input"] = {
                            k: v for k, v in payload["generation_input"].items() if v not in (None, {})
                        }
                        root_span.update(input=payload)

                    try:
                        root_span_handle.run(_append_app_generation_input_to_root)
                    except Exception as _e:
                        logger.debug(f"同步app generation输入到root跨度失败: {_e}")

            tool_calls_info: List[Dict[str, Any]] = []

            try:
                # 将当前generation作为父级，显式传递给gemini客户端的工具跨度
                if root_trace_context and generation_span:
                    gen_trace_ctx = {"trace_id": root_trace_context.get("trace_id")}
                    try:
                        if hasattr(generation_span, "id"):
                            gen_trace_ctx["parent_span_id"] = getattr(generation_span, "id", None)
                    except Exception:
                        pass
                    try:
                        api_kwargs["langfuse_trace_context"] = gen_trace_ctx
                    except Exception:
                        pass
                response = model.acall(api_kwargs=api_kwargs)
                sent_chunks = set()
                tool_calls_info = []
                async for chunk, line_str in response:
                    if line_str == "":
                        continue
                    if isinstance(chunk, str):
                        try:
                            ai_res = json.loads(chunk)
                        except Exception:
                            ai_res = {"type": "message", "content": chunk}
                        chat_history_sid = ai_res.get('id') or chat_history_sid
                        res_type = ai_res.get('type')
                        if res_type in ['tool_call', 'tool_execution_status', 'tool_error']:
                            event_type = res_type
                            content = ai_res.get('content') if res_type != 'tool_error' else chunk
                            # 记录工具调用到数组，保持与通用流一致
                            try:
                                if res_type == 'tool_call':
                                    parsed = json.loads(content) if isinstance(content, str) else content
                                    if isinstance(parsed, list):
                                        for t in parsed:
                                            if isinstance(t, dict):
                                                if not t.get('status'):
                                                    t['status'] = 'executing'
                                                tool_calls_info.append(t)
                                    elif isinstance(parsed, dict):
                                        if not parsed.get('status'):
                                            parsed['status'] = 'executing'
                                        tool_calls_info.append(parsed)
                                elif res_type == 'tool_execution_status':
                                    parsed = json.loads(content) if isinstance(content, str) else content
                                    status_val = None
                                    tool_id = None
                                    tool_name = None
                                    if isinstance(parsed, dict):
                                        status_val = parsed.get('status')
                                        tool_id = parsed.get('id') or parsed.get('tool_id')
                                        tool_name = parsed.get('name')
                                    if status_val in ['completed', 'failed']:
                                        for t in tool_calls_info:
                                            if t.get('status') == 'executing' and (tool_id is None or t.get('id') == tool_id) and (tool_name is None or t.get('name') == tool_name):
                                                t['status'] = status_val
                                elif res_type == 'tool_error':
                                    for t in tool_calls_info:
                                        if t.get('status') == 'executing':
                                            t['status'] = 'failed'
                            except Exception as _e:
                                logger.warning(f"记录工具调用信息失败: {_e}")

                            yield line_str + "\n\n"
                            if res_type == 'tool_error':
                                error_code = ai_res.get('error', {}).get('code', '500')
                                break
                            continue
                        if res_type == 'sandbox_status':
                            sandbox_payload = { 'content': json.dumps(ai_res) }
                            yield line_str + "\n\n"
                            continue
                        if res_type == 'message':
                            content = ai_res.get('content', '')
                            if content:
                                chunk_hash = hashlib.md5(content.encode()).hexdigest()
                                if chunk_hash not in sent_chunks:
                                    sent_chunks.add(chunk_hash)
                                    assistant_response += content
                                    yield line_str + "\n\n"
                    else:
                        chunk_str = str(chunk)
                        assistant_response += chunk_str
                        yield line_str + "\n\n"
                yield f"data: [DONE]\n\n"
            except Exception as e:
                logger.error(f"App对话流出错: {e}")
                span_exc_info = (type(e), e, e.__traceback__)
                error_code, res = build_error_response(error_code, 'wct-cli-', f'调用gemini-cli错误: {str(e)}', True)
                yield build_standard_error_response(res, error_code)
            finally:
                # 统一记录工具调用：若报错，则将未完成的工具标记为失败
                try:
                    if error_code is not None and tool_calls_info is not None:
                        for tool in tool_calls_info:
                            if tool.get('status') == 'executing':
                                tool['status'] = 'failed'
                except Exception:
                    pass

                duration_ms = int((datetime.datetime.now() - start_time).total_seconds() * 1000)
                output_payload = {
                    "assistant_response": _truncate_for_langfuse(assistant_response, 4000),
                    "error_code": error_code,
                    "duration_ms": duration_ms,
                }

                if generation_span:
                    update_kwargs: Dict[str, Any] = {"output": output_payload}
                    if usage_details:
                        filtered_usage = {
                            key: value for key, value in usage_details.items() if value is not None
                        }
                        if filtered_usage:
                            update_kwargs["usage_details"] = filtered_usage
                    if span_exc_info[0] is not None:
                        update_kwargs["level"] = "ERROR"
                        update_kwargs["status_message"] = str(span_exc_info[1])
                    generation_span.update(**update_kwargs)
                _close_generation_span(*span_exc_info)

                if root_span:
                    try:
                        if span_exc_info[0] is None:
                            root_output = {**output_payload, "conversation_type": conversation_type}
                            if usage_details:
                                filtered_usage = {
                                    key: value for key, value in (usage_details or {}).items() if value is not None
                                }
                                if filtered_usage:
                                    root_output["usage_details"] = filtered_usage
                            if 'tool_calls_info' in locals() and tool_calls_info:
                                root_output["tool_calls"] = tool_calls_info
                            root_output["generation_output"] = {
                                "assistant_response": _truncate_for_langfuse(assistant_response, 2000),
                                "error_code": error_code,
                            }
                            root_span.update_trace(output=root_output)
                        else:
                            root_span.update(level="ERROR", status_message=str(span_exc_info[1]))
                    except Exception as _e:
                        logger.debug(f"同步app generation输出到root跨度失败: {_e}")
                _close_root_span(*span_exc_info)

                assistant_chat_history = ChatHistory(
                    msg_sid=chat_history_sid,
                    chat_id=chat_session_db.get('id'),
                    role="assistant",
                    content=assistant_response,
                    provider="gemini-cli",
                    deep_research=0,
                    deep_research_iter=None,
                    model=request.model or "",
                    tool_calls=json.dumps(tool_calls_info, ensure_ascii=False) if tool_calls_info else "",
                    file_references=None,
                    msg_data="",
                    state=1,
                    parent_id=chat_his_dict.get('id'),
                    error_code=error_code,
                    created_by=request.user_id,
                    created_date=datetime.datetime.now(),
                    qa_src=request.caller
                )
                add_chat_history(assistant_chat_history)

        return StreamingResponse(
            root_span_handle.run(response_stream),
            media_type="text/event-stream",
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in app chat stream: {e}")
        if 'root_span_handle' in locals():
            if (
                'root_span' in locals()
                and root_span
                and 'root_span_closed' in locals()
                and not root_span_closed
            ):
                try:
                    root_span_handle.run(
                        lambda: root_span.update(level="ERROR", status_message=str(e))
                    )
                except Exception as span_update_error:
                    logger.debug(f"Failed to update Langfuse root span on error: {span_update_error}")
            if '_close_root_span' in locals():
                _close_root_span(*sys.exc_info())
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/")
async def root():
    """Root endpoint to check if the API is running"""
    return {"status": "API is running", "message": "Navigate to /docs for API documentation"}

def optimize_file_paths(paths_str):
    """
    参数:
    paths_str (str): 包含多个文件路径的字符串，路径之间用换行符分隔
    
    返回:
    str: 优化后的路径字符串
    """
    logger.debug(f"原始日志信息长度: {len(paths_str)}")
    
    # 找到<file_tree>和</file_tree>标签的位置
    start_tag = "<file_tree>"
    end_tag = "</file_tree>"
    start_pos = paths_str.find(start_tag)
    end_pos = paths_str.find(end_tag)
    
    if start_pos == -1 or end_pos == -1 or end_pos <= start_pos:
        # 如果没有找到标签或标签顺序错误，直接返回原始字符串
        logger.warning("未找到有效的<file_tree>标签，返回原始字符串")
        return paths_str
    
    # 分割字符串为三部分
    prefix = paths_str[:start_pos + len(start_tag)]
    file_tree_content = paths_str[start_pos + len(start_tag):end_pos].strip()
    suffix = paths_str[end_pos:]
    
    # 检查内容长度，如果不超过600000则不优化
    if len(file_tree_content) <= 800000:
        logger.debug("文件树内容长度未超过600000，无需优化")
        return paths_str
    
    # 按行分割<file_tree>内容并过滤空行
    paths = [line.strip() for line in file_tree_content.split('\n') if line.strip()]
    
    # 用于存储每个目录下的文件列表
    dir_files_map = {}
    
    # 处理每个路径
    for path in paths:
        # 分离目录和文件名
        dir_path, file_name = os.path.split(path)
        
        # 如果目录不在映射中，添加它
        if dir_path not in dir_files_map:
            dir_files_map[dir_path] = []
        
        # 添加文件名到对应的目录列表中
        dir_files_map[dir_path].append(file_name)
    
    # 构建优化后的路径字符串
    optimized_paths = []
    current_length = 0
    MAX_LENGTH = 800000  # <file_tree>内容的最大长度限制
    
    for dir_path, files in dir_files_map.items():
        # 简化目录路径，移除common前缀和项目根目录
        simplified_dir = dir_path
        
        # 尝试移除项目根目录部分（假设是第一个目录）
        parts = simplified_dir.split(os.sep)
        if len(parts) > 1 and (parts[0] == "code" or parts[0] == "local"):
            simplified_dir = os.sep.join(parts[1:])
        
        # 进一步简化：如果目录以"feature"开头，保留feature之后的部分
        if simplified_dir.startswith("feature" + os.sep):
            simplified_dir = simplified_dir[len("feature" + os.sep):]
        
        # 每个目录最多保留两个文件
        files_to_keep = files[:2]
        
        # 如果目录下只有一个文件，直接添加完整路径
        if len(files_to_keep) == 1:
            line = os.path.join(simplified_dir, files_to_keep[0]) + "\n"
        else:
            # 如果有多个文件，将目录路径和文件名列在同一行，用逗号分隔
            files_str = ", ".join(files_to_keep)
            line = f"{simplified_dir}\\{files_str}\n"
        
        # 检查添加此行后的总长度
        if current_length + len(line) > MAX_LENGTH:
            # 如果超过最大长度，停止处理
            break
        
        # 添加行并更新当前长度
        optimized_paths.append(line)
        current_length += len(line)
    
    # 用换行符连接所有优化后的路径，并移除末尾的换行符
    optimized_file_tree = "".join(optimized_paths).rstrip("\n")
    
    # 重新组合三部分内容
    result = prefix + optimized_file_tree + suffix
    logger.debug(f"websocket_wiki.py: 优化后的日志信息长度: {len(result)}")
    return result

def build_event_stream(id:str, event_type:str, content:str):
    return f"id: {id}\nevent: {event_type}\ndata: {content}\n\n"

def build_error_response(error_code, prefix, error_message, with_internal_error=True):
    """
    构造统一的错误码和错误消息字典
    :param error_code: 原始错误码（如 None 或 '500'）
    :param prefix: 错误码前缀（如 'ailab-', 'wct-cli-'）
    :param message: 错误内容（如 '调用whalecloud错误'）
    :param with_internal_error: 是否添加“内部服务错误”前缀
    :return: (error_code, res_dict)
    """
    code = str(error_code) if error_code is not None else '500'
    if prefix and not code.startswith(prefix):
        code = prefix + code
    if with_internal_error:
        content = f"\n内部服务错误: {error_message}\n"
    else:
        content = f"\n{error_message}\n"
    res = {
        'content': content,
        'error': True
    }
    return code, res

def build_standard_error_response(content: any, code: str):
    data = {
        "error": {
            "message": content,
            "type": "api_error",
            "code": code or "500",
            "timestamp": int(datetime.datetime.now().timestamp())
        },
        "status_code": code or "500"
    }
    return f"type: error\ndata: {json.dumps(data, ensure_ascii=False)}\n\n"

def resolve_base64(raw: str) -> Tuple[str, str]:
    splited = raw.split(sep=",", maxsplit=1)
    mime_type = splited[0].replace("data:", "").replace(";base64", "")
    return mime_type, splited[1]
