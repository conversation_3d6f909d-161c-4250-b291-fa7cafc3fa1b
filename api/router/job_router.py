from fastapi import APIRouter
from fastapi import Depends, HTTPException, Request
from fastapi.responses import JSONResponse
from sqlmodel import select
import logging
from datetime import datetime

from api.api import parse_sub_repos_str
from api.database.base import session_scope
from api.middleware.audit_log_middleware import audit_logger
from api.middleware.language_middleware import get_translation
from api.model.git_repository import AiDwGitRepository
from api.model.user_info import UserInfo
from api.model.wiki_job import WikiJob
from api.model.wiki_repository_relation import AiDwWikiRepositoryRelation
from api.utils import git_utils



job_router = APIRouter(prefix="/api/wiki/jobs", tags=["job"])


def _get_creator_info(session, job):
    if not job.created_by:
        return None
    user = session.exec(select(UserInfo).where(UserInfo.id == job.created_by)).first()
    if not user:
        return None
    return {
        "id": user.id,
        "user_name": user.user_name,
        "user_code": user.user_code,
    }


def _build_repo_payload(repo, is_main=False):
    if not repo:
        return None
    return {
        "url": getattr(repo, "repo_url", None),
        "branch": getattr(repo, "branch", None),
        "owner": getattr(repo, "repo_owner", None),
        "name": getattr(repo, "repo_name", None),
        "type": getattr(repo, "repo_type", None),
        "is_main": is_main,
    }


def _resolve_repositories(session, job, fallback_owner, fallback_name):
    fallback_main = {
        "url": job.repo_url,
        "branch": job.branch,
        "owner": fallback_owner,
        "name": fallback_name,
        "type": None,
        "is_main": True,
    }

    wiki_id = getattr(job, "wiki_info_id", None)
    if not wiki_id:
        fallback_subs = _fallback_sub_repositories(job, fallback_main)
        return fallback_main, fallback_subs

    relations = session.exec(
        select(AiDwWikiRepositoryRelation).where(AiDwWikiRepositoryRelation.wiki_id == wiki_id)
    ).all()
    if not relations:
        fallback_subs = _fallback_sub_repositories(job, fallback_main)
        return fallback_main, fallback_subs

    repo_ids = [rel.repository_id for rel in relations if rel.repository_id is not None]
    if not repo_ids:
        fallback_subs = _fallback_sub_repositories(job, fallback_main)
        return fallback_main, fallback_subs

    repositories = session.exec(
        select(AiDwGitRepository).where(AiDwGitRepository.id.in_(repo_ids))
    ).all()
    repo_map = {repo.id: repo for repo in repositories}

    main_repo_payload = None
    sub_repo_payloads = []

    for relation in relations:
        repo = repo_map.get(relation.repository_id)
        if not repo:
            continue
        payload = _build_repo_payload(repo, is_main=relation.is_main_repo)
        if relation.is_main_repo:
            main_repo_payload = payload
        else:
            sub_repo_payloads.append(payload)

    if not main_repo_payload:
        # 尝试使用与任务主仓库匹配的记录
        for relation in relations:
            repo = repo_map.get(relation.repository_id)
            if repo and repo.repo_url == job.repo_url:
                main_repo_payload = _build_repo_payload(repo, is_main=True)
                break

    if not main_repo_payload:
        main_repo_payload = fallback_main
    else:
        main_repo_payload.setdefault("owner", main_repo_payload.get("owner") or fallback_owner)
        main_repo_payload.setdefault("name", main_repo_payload.get("name") or fallback_name)
        main_repo_payload["is_main"] = True

    # 过滤重复的子仓库，并补齐缺失信息
    normalized_subs = []
    for payload in sub_repo_payloads:
        if not payload:
            continue
        if (
            payload.get("url") == main_repo_payload.get("url")
            and payload.get("branch") == main_repo_payload.get("branch")
        ):
            continue
        payload.setdefault("owner", None)
        payload.setdefault("name", None)
        payload["is_main"] = False
        normalized_subs.append(payload)

    if not normalized_subs:
        normalized_subs = _fallback_sub_repositories(job, main_repo_payload)

    return main_repo_payload, normalized_subs


def _fallback_sub_repositories(job, main_repo_payload):
    fallback = []
    parsed = parse_sub_repos_str(job.sub_repos)
    if not parsed:
        return fallback
    for item in parsed:
        url = item.get("url")
        branch = item.get("branch")
        if not url:
            continue
        if (
            url == main_repo_payload.get("url")
            and branch == main_repo_payload.get("branch")
        ):
            continue
        try:
            owner, name, _ = git_utils.extract_repo_info(url)
        except Exception:
            owner, name = None, None
        fallback.append(
            {
                "url": url,
                "branch": branch,
                "owner": owner,
                "name": name,
                "type": None,
                "is_main": False,
            }
        )
    return fallback


def _serialize_job(session, job):
    repo_owner, repo_name, _ = git_utils.extract_repo_info(job.repo_url)
    creator_info = _get_creator_info(session, job)

    main_repo, sub_repos = _resolve_repositories(session, job, repo_owner, repo_name)

    if not main_repo.get("owner") or not main_repo.get("name"):
        parsed_owner, parsed_name, _ = git_utils.extract_repo_info(main_repo.get("url") or "")
        main_repo.setdefault("owner", parsed_owner)
        main_repo.setdefault("name", parsed_name)

    repo_owner = main_repo.get("owner") or repo_owner
    repo_name = main_repo.get("name") or repo_name
    branch = main_repo.get("branch") or job.branch

    return {
        "id": job.id,
        "repo_url": job.repo_url,
        "repo_owner": repo_owner,
        "repo_name": repo_name,
        "branch": branch,
        "status": job.status,
        "job_type": int(job.job_type) if job.job_type is not None else 0,
        "progress": job.progress,
        "total_files": job.total_files,
        "processed_files": job.processed_files,
        "stage": job.stage,
        "stage_progress": job.stage_progress,
        "stage_message": job.stage_message,
        "created_by": job.created_by,
        "creator_info": creator_info,
        "main_repo": main_repo,
        "sub_repos": sub_repos,
        "created_time": job.created_time.isoformat() if hasattr(job.created_time, 'isoformat') else str(job.created_time),
        "updated_time": job.updated_time.isoformat() if hasattr(job.updated_time, 'isoformat') else str(job.updated_time),
        "wiki_info_id": job.wiki_info_id,
        "error_message": job.error_message,
        "message": getattr(job, "stage_message", None),
    }

@job_router.get("/active")
async def get_active_wiki_jobs():
    """
    获取所有进行中(仅processing或resuming)的Wiki生成任务
    """
    try:
        with session_scope() as session:
            # 仅查询进行中的任务：processing、resuming
            active_jobs = (
                session.query(WikiJob)
                .filter(WikiJob.status.in_(["processing", "resuming"]))
                .order_by(WikiJob.updated_time.desc())
                .all()
            )
            
            results = [_serialize_job(session, job) for job in active_jobs]
            return results
    except Exception as e:
        logging.error(f"获取进行中任务列表失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetActiveJobs"))

@job_router.get("/pending")
async def get_pending_wiki_jobs():
    """
    获取所有排队中(pending、pending_resume、paused)的Wiki生成任务
    """
    try:
        with session_scope() as session:
            # 查询状态为pending、pending_resume或paused的任务
            pending_jobs = (
                session.query(WikiJob)
                .filter(WikiJob.status.in_(["pending", "pending_resume", "paused"]))
                .order_by(WikiJob.updated_time.desc())
                .all()
            )
            
            results = [_serialize_job(session, job) for job in pending_jobs]
            return results
    except Exception as e:
        logging.error(f"获取排队中任务列表失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetPendingJobs"))

@job_router.get("/failed")
async def get_failed_jobs(request: Request):
    """获取失败的Wiki生成任务列表"""
    try:
        # 获取所有失败的任务
        with session_scope() as session:
            failed_jobs = (
                session.query(WikiJob)
                .filter(WikiJob.status.in_(["failed", "timeout"]))
                .order_by(WikiJob.updated_time.desc())
                .limit(20)  # 限制返回最近20个失败任务
                .all()
            )
            
            results = [_serialize_job(session, job) for job in failed_jobs]
            return results
    except Exception as e:
        logging.error(f"获取失败任务列表失败: {e}")
        return JSONResponse(status_code=500, content={"message": get_translation("api.errors.failedToGetFailedJobs")})

@job_router.post("/{job_id}/retry")
async def retry_wiki_job(job_id: str, request: Request, audit_log=Depends(audit_logger)):
    """重试失败的Wiki生成任务，使用JobManager进行统一管理"""
    
    try:
        from api.wiki.wiki_job_manager import get_job_manager
        
        # 使用JobManager重试任务
        job_manager = get_job_manager()
        success = await job_manager.retry_job(job_id)

        # 设置审计日志数据
        audit_log(code="TASK_RETRY", party_type="系统", party_id=job_id)
        
        if success:
            return {
                "success": True,
                "message": get_translation("api.errors.taskSubmittedForRetry"),
                "resume": True
            }
        else:
            audit_log(is_success=False)
            return JSONResponse(
                status_code=400, 
                content={"success": False, "message": get_translation("api.errors.taskRetryFailed")}
            )
            
    except Exception as e:
        logging.error(f"重试任务失败: {e}")
        audit_log(is_success=False)
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": get_translation("api.errors.retryTaskFailed")}
        )

# 阶段名称中文映射
stageNames = {
    'init': '初始化',
    'download': '下载代码',
    'upload': '上传文件',
    'structure': '生成结构',
    'pages': '生成页面',
    'generate': '生成Wiki',  # 兼容旧版
    'completed': '已完成'
}



@job_router.get("/{job_id}/manager/status")
async def get_job_manager_job_status(job_id: str):
    """获取指定job在JobManager中的状态"""
    try:
        from api.wiki.wiki_job_manager import get_job_manager
        
        job_manager = get_job_manager()
        job_status = job_manager.get_job_status(job_id)
        
        return job_status
    except Exception as e:
        logging.error(f"获取job状态失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetJobStatus"))

@job_router.post("/{job_id}/cancel")
async def cancel_wiki_job(job_id: str, audit_log=Depends(audit_logger)):
    """取消指定的Wiki生成任务"""
    try:
        from api.wiki.wiki_job_manager import get_job_manager
        
        job_manager = get_job_manager()
        success = await job_manager.cancel_job(job_id)
        
        # 设置审计日志数据
        audit_log(code="TASK_CANCEL", party_type="系统", party_id=job_id)
        
        if success:
            return {"success": True, "message": get_translation("api.errors.taskCancelled")}
        else:
            audit_log(is_success=False)
            return JSONResponse(
                status_code=400, 
                content={"success": False, "message": get_translation("api.errors.cancelTaskFailed")}
            )
    except Exception as e:
        logging.error(f"取消任务失败: {e}")
        audit_log(is_success=False)
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": get_translation("api.errors.cancelTaskFailed")}
        )

@job_router.get("/cancelled")
async def get_cancelled_jobs(request: Request):
    """获取取消的Wiki生成任务列表"""
    try:
        # 获取所有取消的任务
        with session_scope() as session:
            cancelled_jobs = (
                session.query(WikiJob)
                .filter(WikiJob.status == "cancelled")
                .order_by(WikiJob.created_time.desc())
                .all()
            )
            
            results = [_serialize_job(session, job) for job in cancelled_jobs]
            return results
    except Exception as e:
        logging.error(f"获取取消任务列表失败: {e}")
        return JSONResponse(status_code=500, content={"message": get_translation("api.errors.failedToGetCancelledJobs")})

@job_router.post("/{job_id}/pause")
async def pause_wiki_job(job_id: str):
    """暂停指定的Wiki生成任务"""
    try:
        from api.wiki.wiki_job_manager import get_job_manager
        
        job_manager = get_job_manager()
        success = await job_manager.pause_job(job_id)
        
        if success:
            return {"success": True, "message": get_translation("api.errors.taskPaused")}
        else:
            return JSONResponse(
                status_code=400, 
                content={"success": False, "message": get_translation("api.errors.pauseTaskFailed")}
            )
    except Exception as e:
        logging.error(f"暂停任务失败: {e}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": get_translation("api.errors.pauseTaskFailed")}
        )

@job_router.post("/{job_id}/resume")
async def resume_wiki_job(job_id: str):
    """继续指定的Wiki生成任务"""
    try:
        from api.wiki.wiki_job_manager import get_job_manager
        
        job_manager = get_job_manager()
        success = await job_manager.resume_job(job_id)
        
        if success:
            return {"success": True, "message": get_translation("api.errors.taskResumed")}
        else:
            return JSONResponse(
                status_code=400, 
                content={"success": False, "message": get_translation("api.errors.resumeTaskFailed")}
            )
    except Exception as e:
        logging.error(f"继续任务失败: {e}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": get_translation("api.errors.resumeTaskFailed")}
        )

@job_router.post("/{job_id}/restart")
async def restart_wiki_job(job_id: str, audit_log=Depends(audit_logger)):
    """重新开始指定的Wiki生成任务（从头开始）"""
    try:
        from api.wiki.wiki_job_manager import get_job_manager
        # 设置审计日志数据
        audit_log(code="TASK_RESTART", party_type="系统", party_id=job_id)
        job_manager = get_job_manager()
        success = await job_manager.restart_job(job_id)
        
        if success:
            return {"success": True, "message": get_translation("api.errors.taskRestarted")}
        else:
            audit_log(is_success=False)
            return JSONResponse(
                status_code=400, 
                content={"success": False, "message": get_translation("api.errors.restartTaskFailed")}
            )
    except Exception as e:
        logging.error(f"重新开始任务失败: {e}")
        audit_log(is_success=False)
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": get_translation("api.errors.restartTaskFailed")}
        )

def parse_repo_owner_and_name(repo_url: str) -> tuple[str, str]:
    """
    从仓库URL解析出owner和repo名称
    
    Args:
        repo_url: 仓库URL
        
    Returns:
        tuple: (owner, repo_name)
    """
    try:
        from urllib.parse import urlparse
        parsed_url = urlparse(repo_url)
        path_parts = parsed_url.path.strip('/').split('/')
        
        if len(path_parts) >= 2:
            owner = path_parts[-2]
            repo_name = path_parts[-1].replace('.git', '')
            return owner, repo_name
        else:
            return "unknown", "unknown"
    except Exception as e:
        logging.error(f"Failed to parse repo URL {repo_url}: {e}")
        return "unknown", "unknown"

@job_router.get("/global/status")
async def get_global_jobs_status():
    """获取全局任务并发状态信息"""
    try:
        from api.wiki.wiki_job_manager import get_job_manager
        
        job_manager = get_job_manager()
        
        # 获取全局并发状态
        global_active_count = await job_manager.distributed_lock.get_global_active_jobs_count()
        
        # 获取所有实例信息
        available_instances = await job_manager.distributed_lock.get_available_instances()
        
        instance_info = []
        total_instance_jobs = 0
        for instance in available_instances:
            instance_info.append({
                "instance_id": instance.instance_id,
                "hostname": instance.hostname,
                "pid": instance.pid,
                "status": instance.status,
                "max_concurrent_jobs": instance.max_concurrent_jobs,
                "current_jobs": instance.current_jobs,
                "last_heartbeat": instance.last_heartbeat.isoformat() if instance.last_heartbeat else None
            })
            total_instance_jobs += instance.current_jobs
        
        return {
            "global_active_count": global_active_count,
            "global_max_concurrent": job_manager.global_max_concurrent,
            "total_instance_jobs": total_instance_jobs,
            "instance_count": len(available_instances),
            "instances": instance_info,
            "global_limit_reached": global_active_count >= job_manager.global_max_concurrent
        }
    except Exception as e:
        logging.error(f"获取全局任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetGlobalTaskStatus"))

@job_router.post("/instances/cleanup")
async def cleanup_dead_instances():
    """手动清理死进程实例"""
    try:
        from api.wiki.wiki_job_manager import get_job_manager
        
        job_manager = get_job_manager()
        
        # 执行清理操作
        await job_manager.distributed_lock.cleanup_crashed_instances()
        
        # 获取清理后的实例信息
        available_instances = await job_manager.distributed_lock.get_available_instances()
        
        return {
            "success": True,
            "message": get_translation("api.errors.deadProcessInstancesCleared"),
            "remaining_instances": len(available_instances),
            "instances": [
                {
                    "instance_id": instance.instance_id,
                    "hostname": instance.hostname,
                    "pid": instance.pid,
                    "status": instance.status,
                    "last_heartbeat": instance.last_heartbeat.isoformat() if instance.last_heartbeat else None
                }
                for instance in available_instances
            ]
        }
    except Exception as e:
        logging.error(f"清理死进程实例失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToClearDeadInstances"))

@job_router.delete("/instances/{instance_id}")
async def force_delete_instance(instance_id: str):
    """强制删除指定的实例记录"""
    try:
        from api.wiki.wiki_job_manager import get_job_manager
        from api.database.base import session_scope
        from api.model.job_manager_lock import JobManagerInstance, JobLock
        from sqlmodel import select
        
        with session_scope() as session:
            # 查找实例
            instance_query = select(JobManagerInstance).where(
                JobManagerInstance.instance_id == instance_id
            )
            instance = session.exec(instance_query).first()
            
            if not instance:
                raise HTTPException(status_code=404, detail=get_translation("api.errors.instanceNotExists"))
            
            # 释放该实例的所有锁
            lock_query = select(JobLock).where(
                JobLock.instance_id == instance_id
            )
            locks = session.exec(lock_query).all()
            
            for lock in locks:
                session.delete(lock)
                logging.info(f"强制删除实例 {instance_id} 的锁: {lock.job_id}")
            
            # 删除实例记录
            session.delete(instance)
            session.commit()
            
            logging.info(f"强制删除实例: {instance_id} (主机: {instance.hostname}, PID: {instance.pid})")
            
            return {
                "success": True,
                "message": get_translation("api.errors.instanceForceDeleted").format(instance_id=instance_id),
                "deleted_locks": len(locks),
                "instance_info": {
                    "instance_id": instance.instance_id,
                    "hostname": instance.hostname,
                    "pid": instance.pid,
                    "status": instance.status
                }
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"强制删除实例失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToForceDeleteInstance"))

@job_router.get("/instances/cleanup/status")
async def get_cleanup_status():
    """获取实例清理状态和统计信息"""
    try:
        from api.wiki.wiki_job_manager import get_job_manager
        from api.database.base import session_scope
        from api.model.job_manager_lock import JobManagerInstance
        from sqlmodel import select
        from datetime import datetime, timedelta
        
        current_time = datetime.now()
        heartbeat_timeout = timedelta(minutes=5)
        
        with session_scope() as session:
            # 获取所有实例
            all_query = select(JobManagerInstance)
            all_instances = session.exec(all_query).all()
            
            active_instances = []
            timeout_instances = []
            dead_process_instances = []
            
            for instance in all_instances:
                instance_info = {
                    "instance_id": instance.instance_id,
                    "hostname": instance.hostname,
                    "pid": instance.pid,
                    "status": instance.status,
                    "last_heartbeat": instance.last_heartbeat.isoformat() if instance.last_heartbeat else None,
                    "current_jobs": instance.current_jobs
                }
                
                if instance.status == "active":
                    # 检查是否心跳超时
                    if instance.last_heartbeat and current_time - instance.last_heartbeat > heartbeat_timeout:
                        instance_info["timeout_reason"] = "心跳超时"
                        timeout_instances.append(instance_info)
                    else:
                        # 检查进程是否存活（仅本机）
                        job_manager = get_job_manager()
                        if hasattr(job_manager.distributed_lock, '_is_process_alive'):
                            if not job_manager.distributed_lock._is_process_alive(instance.hostname, instance.pid):
                                instance_info["timeout_reason"] = "进程不存在"
                                dead_process_instances.append(instance_info)
                            else:
                                active_instances.append(instance_info)
                        else:
                            active_instances.append(instance_info)
                
        return {
            "summary": {
                "total_instances": len(all_instances),
                "active_instances": len(active_instances),
                "timeout_instances": len(timeout_instances),
                "dead_process_instances": len(dead_process_instances),
                "cleanup_threshold_minutes": 5
            },
            "instances": {
                "active": active_instances,
                "timeout": timeout_instances,
                "dead_process": dead_process_instances
            },
            "cleanup_info": {
                "next_cleanup_in_seconds": "每30秒自动清理",
                "manual_cleanup_endpoint": "/api/wiki/jobs/instances/cleanup"
            }
        }
    except Exception as e:
        logging.error(f"获取清理状态失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetCleanupStatus"))

@job_router.delete("/{job_id}")
async def delete_job(job_id: str):
    """删除指定的Wiki任务（仅允许删除已取消或已失败的任务）"""
    try:
        from api.wiki.wiki_job_manager import get_job_manager
        from api.database.base import session_scope
        from api.model.wiki_job import WikiJob
        from api.service.wiki_service import get_job
        from sqlmodel import select
        
        with session_scope() as session:
            job = get_job(session, job_id)
            if not job:
                raise HTTPException(status_code=404, detail=get_translation("api.errors.taskNotExists").format(job_id=job_id))
            
            # 检查任务状态，只允许删除已取消、已失败或已完成的任务
            allowed_statuses = ["cancelled", "failed", "completed", "timeout"]
            if job.status not in allowed_statuses:
                raise HTTPException(
                    status_code=400, 
                    detail=f"只能删除状态为 {', '.join(allowed_statuses)} 的任务，当前状态: {job.status}"
                )
            
            # 检查任务是否在JobManager中处于活跃状态
            job_manager = get_job_manager()
            if job_id in job_manager.active_jobs or job_id in job_manager.paused_jobs:
                raise HTTPException(
                    status_code=400, 
                    detail="任务正在执行中或已暂停，无法删除"
                )
            
            # 删除分布式锁（如果存在）
            try:
                await job_manager.distributed_lock.release_job_lock(job_id)
                await job_manager.distributed_lock.update_global_job_state(job_id, "deleted")
            except Exception as e:
                logging.warning(f"清理任务 {job_id} 的分布式锁时出错: {e}")
            
            # 获取关联的wiki_info_id（如果存在）
            wiki_info_id = job.wiki_info_id
            
            # 删除任务记录
            session.delete(job)
            session.commit()
            
            logging.info(f"任务 {job_id} 已被删除，状态: {job.status}")
            
            result = {
                "message": get_translation("api.errors.taskDeletedSuccessfully").format(job_id=job_id),
                "job_id": job_id,
                "deleted_status": job.status
            }
            
            # 如果有关联的wiki_info，提供相关信息
            if wiki_info_id:
                result["wiki_info_id"] = wiki_info_id
                result["note"] = "关联的WikiInfo记录仍然保留，只删除了任务执行记录"
            
            return result
            
    except HTTPException:
        raise  # 重新抛出HTTP异常
    except Exception as e:
        logging.error(f"删除任务 {job_id} 失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.deleteTaskFailed"))

@job_router.post("/{job_id}/delete-with-wiki")
async def delete_job_with_wiki(job_id: str, audit_log=Depends(audit_logger)):
    """删除已取消任务及其关联的Wiki记录"""
    try:
        from api.wiki.wiki_job_manager import get_job_manager
        from api.database.base import session_scope
        from api.model.wiki_job import WikiJob
        from api.model.wiki_info import WikiInfo
        from api.service.wiki_service import get_job
        from sqlmodel import select
        # 设置审计日志数据
        audit_log(code="TASK_DELETE", party_type="系统", party_id=job_id)
        
        with session_scope() as session:
            job = get_job(session, job_id)
            if not job:
                raise HTTPException(status_code=404, detail=f"任务 {job_id} 不存在")
            
            # 只允许删除已取消或失败的任务
            if job.status not in ["cancelled", "failed", "timeout"]:
                audit_log(is_success=False)
                raise HTTPException(
                    status_code=400, 
                    detail=f"只能删除已取消或失败的任务，当前状态: {job.status}"
                )
            
            # 检查任务是否在JobManager中处于活跃状态
            job_manager = get_job_manager()
            if job_id in job_manager.active_jobs or job_id in job_manager.paused_jobs:
                audit_log(is_success=False)
                raise HTTPException(
                    status_code=400, 
                    detail="任务正在执行中或已暂停，无法删除"
                )
            
            # 获取关联的wiki_info
            wiki_info_id = job.wiki_info_id
            wiki_info = None
            if wiki_info_id:
                wiki_info_query = select(WikiInfo).where(WikiInfo.wiki_id == wiki_info_id)
                wiki_info = session.exec(wiki_info_query).first()
            
            # 删除分布式锁（如果存在）
            try:
                await job_manager.distributed_lock.release_job_lock(job_id)
                await job_manager.distributed_lock.update_global_job_state(job_id, "deleted")
            except Exception as e:
                logging.warning(f"清理任务 {job_id} 的分布式锁时出错: {e}")
                audit_log(is_success=False)
            
            # 删除任务记录
            session.delete(job)
            
            # 删除关联的Wiki记录（如果存在）
            wiki_deleted = False
            if wiki_info:
                session.delete(wiki_info)
                wiki_deleted = True
                logging.info(f"已删除关联的Wiki记录: {wiki_info_id}")
            
            session.commit()
            
            logging.info(f"任务 {job_id} 及其关联的Wiki已被删除")
            
            result = {
                "message": get_translation("api.errors.taskAndWikiDeletedSuccessfully").format(job_id=job_id),
                "job_id": job_id,
                "wiki_deleted": wiki_deleted,
                "wiki_info_id": wiki_info_id if wiki_info else None
            }
            
            return result
            
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"删除任务及Wiki失败 {job_id}: {e}")
        audit_log(is_success=False)
        raise HTTPException(status_code=500, detail=get_translation("api.errors.deleteTaskAndWikiFailed"))

@job_router.post("/{job_id}/force-delete")
async def force_delete_job(job_id: str):
    """强制删除指定的Wiki任务（管理员功能，可删除任何状态的任务）"""
    try:
        from api.wiki.wiki_job_manager import get_job_manager
        from api.database.base import session_scope
        from api.model.wiki_job import WikiJob
        from api.service.wiki_service import get_job
        
        with session_scope() as session:
            job = get_job(session, job_id)
            if not job:
                raise HTTPException(status_code=404, detail=f"任务 {job_id} 不存在")
            
            original_status = job.status
            job_manager = get_job_manager()
            
            # 如果任务正在执行，先取消它
            if job_id in job_manager.active_jobs or job_id in job_manager.paused_jobs:
                logging.info(f"强制删除正在执行的任务 {job_id}")
                cancel_success = await job_manager.cancel_job(job_id)
                if not cancel_success:
                    logging.warning(f"取消任务 {job_id} 失败，继续强制删除")
            
            # 清理分布式锁
            try:
                await job_manager.distributed_lock.release_job_lock(job_id)
                await job_manager.distributed_lock.update_global_job_state(job_id, "force_deleted")
            except Exception as e:
                logging.warning(f"清理任务 {job_id} 的分布式锁时出错: {e}")
            
            # 获取关联信息
            wiki_info_id = job.wiki_info_id
            
            # 删除任务记录
            session.delete(job)
            session.commit()
            
            logging.info(f"任务 {job_id} 已被强制删除，原状态: {original_status}")
            
            return {
                "message": get_translation("api.errors.taskForceDeleted").format(job_id=job_id),
                "job_id": job_id,
                "original_status": original_status,
                "wiki_info_id": wiki_info_id,
                "warning": "这是管理员操作，已强制删除任务记录"
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"强制删除任务 {job_id} 失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.forceDeleteTaskFailed"))

@job_router.get("/deletable")
async def get_deletable_jobs():
    """获取可删除的任务列表（已取消、已失败、已完成的任务）"""
    try:
        from api.database.base import session_scope
        from api.model.wiki_job import WikiJob
        from sqlmodel import select
        
        with session_scope() as session:
            # 查询可删除状态的任务
            deletable_statuses = ["cancelled", "failed", "completed", "timeout"]
            statement = select(WikiJob).where(WikiJob.status.in_(deletable_statuses)).order_by(WikiJob.updated_time.desc())
            jobs = session.exec(statement).all()
            
            result = []
            for job in jobs:
                result.append({
                    "job_id": job.id,
                    "repo_url": job.repo_url,
                    "branch": job.branch,
                    "status": job.status,
                    "error_message": job.error_message,
                    "created_time": job.created_time.isoformat() if job.created_time else None,
                    "updated_time": job.updated_time.isoformat() if job.updated_time else None,
                    "wiki_info_id": job.wiki_info_id,
                    "stage": job.stage,
                    "progress": job.progress
                })
            
            return {
                "total": len(result),
                "deletable_statuses": deletable_statuses,
                "jobs": result
            }
            
    except Exception as e:
        logging.error(f"获取可删除任务列表失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetDeletableJobs"))

@job_router.get("/reassignment/status")
async def get_job_reassignment_status():
    """
    获取任务重新分配状态信息
    
    故障转移机制说明：
    - 心跳检测：每30秒检查实例存活状态，3分钟无心跳视为离线
    - 进程检测：本机实例还会检查进程是否真实存在  
    - 快速转移：发现离线实例后立即释放其锁并重新分配任务
    - 智能重试：任务最多重新分配3次，超过后标记为失败
    - 并发限制：重新分配时仍遵守全局和实例并发限制
    """
    try:
        from api.wiki.wiki_job_manager import get_job_manager
        from api.database.base import session_scope
        from api.model.job_manager_lock import GlobalJobState
        from sqlmodel import select
        import json
        
        with session_scope() as session:
            # 获取所有全局任务状态
            all_global_jobs_query = select(GlobalJobState)
            all_global_jobs = session.exec(all_global_jobs_query).all()
            
            # 分类统计
            status_counts = {}
            reassignable_jobs = []
            recent_reassignments = []
            
            for global_job in all_global_jobs:
                status = global_job.global_status
                status_counts[status] = status_counts.get(status, 0) + 1
                
                # 收集可重新分配的任务
                if status == "reassignable":
                    job_metadata = {}
                    if global_job.job_metadata:
                        try:
                            job_metadata = json.loads(global_job.job_metadata)
                        except:
                            job_metadata = {}
                    
                    reassignable_jobs.append({
                        "job_id": global_job.job_id,
                        "last_processing_instance": global_job.last_processing_instance,
                        "reassign_count": job_metadata.get("reassign_count", 0),
                        "last_reassign_time": job_metadata.get("last_reassign_time"),
                        "reassign_reason": job_metadata.get("reassign_reason"),
                        "previous_instance": job_metadata.get("previous_instance")
                    })
                
                # 收集最近的重新分配记录
                if global_job.job_metadata:
                    try:
                        job_metadata = json.loads(global_job.job_metadata)
                        if "last_reassign_time" in job_metadata:
                            recent_reassignments.append({
                                "job_id": global_job.job_id,
                                "status": status,
                                "reassign_count": job_metadata.get("reassign_count", 0),
                                "last_reassign_time": job_metadata.get("last_reassign_time"),
                                "reassign_reason": job_metadata.get("reassign_reason"),
                                "previous_instance": job_metadata.get("previous_instance"),
                                "current_instance": global_job.processing_instance_id
                            })
                    except:
                        pass
            
            # 按重新分配时间排序
            recent_reassignments.sort(key=lambda x: x.get("last_reassign_time", ""), reverse=True)
            recent_reassignments = recent_reassignments[:20]  # 只取最近20个
            
            return {
                "summary": {
                    "total_jobs": len(all_global_jobs),
                    "status_distribution": status_counts,
                    "reassignable_count": len(reassignable_jobs),
                    "recent_reassignments_count": len(recent_reassignments)
                },
                "reassignable_jobs": reassignable_jobs,
                "recent_reassignments": recent_reassignments,
                "help": {
                    "reassignable": "这些任务因JobManager离线而等待重新分配",
                    "processing": "这些任务正在处理中",
                    "paused": "这些任务已暂停",
                    "completed": "这些任务已完成",
                    "failed": "这些任务已失败"
                }
            }
            
    except Exception as e:
        logging.error(f"获取任务重新分配状态失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetReassignmentStatus"))

@job_router.post("/{job_id}/force-reassign")
async def force_reassign_job(job_id: str):
    """强制重新分配指定的任务（管理员功能）"""
    try:
        from api.wiki.wiki_job_manager import get_job_manager
        from api.database.base import session_scope
        from api.model.job_manager_lock import GlobalJobState
        from sqlmodel import select
        import json
        
        with session_scope() as session:
            # 查找全局任务状态
            global_job_query = select(GlobalJobState).where(GlobalJobState.job_id == job_id)
            global_job = session.exec(global_job_query).first()
            
            if not global_job:
                raise HTTPException(status_code=404, detail=get_translation("api.errors.taskGlobalStateNotExists").format(job_id=job_id))
            
            if global_job.global_status in ["completed", "failed"]:
                raise HTTPException(
                    status_code=400, 
                    detail=f"任务 {job_id} 已{global_job.global_status}，无法重新分配"
                )
            
            # 获取当前的metadata
            job_metadata = {}
            if global_job.job_metadata:
                try:
                    job_metadata = json.loads(global_job.job_metadata)
                except:
                    job_metadata = {}
            
            # 更新重新分配信息
            from datetime import datetime
            job_metadata.update({
                "reassign_count": job_metadata.get("reassign_count", 0) + 1,
                "last_reassign_time": datetime.now().isoformat(),
                "previous_instance": global_job.processing_instance_id,
                "reassign_reason": "管理员强制重新分配"
            })
            
            # 释放当前实例的锁
            job_manager = get_job_manager()
            try:
                await job_manager.distributed_lock.release_job_lock(job_id)
            except Exception as e:
                logging.warning(f"释放任务 {job_id} 的锁时出错: {e}")
            
            # 更新全局状态
            global_job.global_status = "reassignable"
            global_job.last_processing_instance = global_job.processing_instance_id
            global_job.processing_instance_id = None
            global_job.job_metadata = json.dumps(job_metadata)
            
            session.commit()
            
            logging.info(f"任务 {job_id} 已被管理员强制重新分配")
            
            return {
                "message": get_translation("api.errors.taskForceReassigned").format(job_id=job_id),
                "job_id": job_id,
                "previous_instance": job_metadata.get("previous_instance"),
                "reassign_count": job_metadata.get("reassign_count"),
                "note": "任务将在下一个心跳周期内被其他实例接手"
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"强制重新分配任务 {job_id} 失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.forceReassignTaskFailed"))

@job_router.get("/manager/election/status")
async def get_manager_election_status():
    """
    获取管理实例选举状态和所有实例健康信息
    
    动态选举机制说明：
    - 选举规则：心跳最近的实例优先，心跳相近时选择instance_id最小
    - 故障转移：当前管理实例离线时自动重新选举
    - 健康检查：5分钟内有心跳视为健康实例
    - 协调清理：非管理实例也会执行备用清理（频率较低）
    """
    try:
        from api.wiki.wiki_job_manager import get_job_manager
        from api.database.base import session_scope
        from api.model.job_manager_lock import JobManagerInstance
        from sqlmodel import select
        from datetime import datetime, timedelta
        
        job_manager = get_job_manager()
        current_instance_id = job_manager.distributed_lock.instance_id
        
        with session_scope() as session:
            # 获取所有活跃实例
            active_query = select(JobManagerInstance).where(
                JobManagerInstance.status == "active"
            ).order_by(JobManagerInstance.instance_id)
            active_instances = session.exec(active_query).all()
            
            if not active_instances:
                return {
                    "summary": {
                        "total_instances": 0,
                        "healthy_instances": 0,
                        "elected_manager": None,
                        "current_instance_is_manager": False
                    },
                    "instances": [],
                    "election_info": {
                        "rules": "心跳最近优先，心跳相近时选择instance_id最小",
                        "health_threshold_minutes": 5,
                        "heartbeat_timeout_minutes": 3
                    }
                }
            
            # 使用分布式锁服务的选举方法
            elected_manager = job_manager.distributed_lock._elect_manager_instance(active_instances)
            current_time = datetime.now()
            
            # 构建实例详细信息
            instances_info = []
            healthy_count = 0
            
            for instance in active_instances:
                # 计算心跳状态
                heartbeat_status = "unknown"
                time_since_heartbeat = None
                is_healthy = False
                
                if instance.last_heartbeat:
                    time_since_heartbeat = current_time - instance.last_heartbeat
                    if time_since_heartbeat < timedelta(minutes=3):
                        heartbeat_status = "excellent"
                        is_healthy = True
                    elif time_since_heartbeat < timedelta(minutes=5):
                        heartbeat_status = "good"
                        is_healthy = True
                    else:
                        heartbeat_status = "timeout"
                else:
                    heartbeat_status = "no_record"
                
                if is_healthy:
                    healthy_count += 1
                
                # 检查是否是当前管理实例
                is_elected_manager = (elected_manager and instance.instance_id == elected_manager.instance_id)
                
                instance_info = {
                    "instance_id": instance.instance_id,
                    "hostname": instance.hostname,
                    "pid": instance.pid,
                    "status": instance.status,
                    "current_jobs": instance.current_jobs,
                    "max_concurrent_jobs": instance.max_concurrent_jobs,
                    "last_heartbeat": instance.last_heartbeat.isoformat() if instance.last_heartbeat else None,
                    "time_since_heartbeat_seconds": int(time_since_heartbeat.total_seconds()) if time_since_heartbeat else None,
                    "heartbeat_status": heartbeat_status,
                    "is_healthy": is_healthy,
                    "is_elected_manager": is_elected_manager,
                    "is_current_instance": instance.instance_id == current_instance_id
                }
                instances_info.append(instance_info)
            
            return {
                "summary": {
                    "total_instances": len(active_instances),
                    "healthy_instances": healthy_count,
                    "elected_manager": elected_manager.instance_id if elected_manager else None,
                    "current_instance_id": current_instance_id,
                    "current_instance_is_manager": elected_manager and elected_manager.instance_id == current_instance_id
                },
                "instances": instances_info,
                "election_info": {
                    "rules": "心跳最近优先，心跳相近时选择instance_id最小",
                    "health_threshold_minutes": 5,
                    "heartbeat_timeout_minutes": 3,
                    "last_election_time": current_time.isoformat(),
                    "election_criteria": {
                        "primary": "最近心跳时间",
                        "secondary": "instance_id最小值",
                        "fallback": "无健康实例时选择instance_id最小"
                    }
                }
            }
            
    except Exception as e:
        logging.error(f"获取管理实例选举状态失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetManagerElectionStatus"))

@job_router.get("/manager/status")
async def get_job_manager_status():
    """获取JobManager状态信息"""
    try:
        from api.wiki.wiki_job_manager import get_job_manager
        
        job_manager = get_job_manager()
        active_jobs = job_manager.get_active_jobs()
        
        # 获取全局并发状态
        global_active_count = await job_manager.distributed_lock.get_global_active_jobs_count()
        
        return {
            "active_jobs_count": len(active_jobs),
            "max_concurrent_jobs": job_manager.max_concurrent_jobs,
            "global_active_count": global_active_count,
            "global_max_concurrent": job_manager.global_max_concurrent,
            "job_timeout_minutes": job_manager.job_timeout_minutes,
            "active_jobs": active_jobs
        }
    except Exception as e:
        logging.error(f"获取JobManager状态失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.failedToGetJobManagerStatus"))

@job_router.get("/validation/atomicity/{job_id}")
async def validate_job_atomicity(job_id: str):
    """验证指定任务的原子性一致性 - 测试修复效果"""
    try:
        from api.wiki.wiki_job_manager import get_job_manager
        
        job_manager = get_job_manager()
        validation_result = job_manager.validate_job_assignment_atomicity(job_id)
        
        return {
            "success": True,
            "validation_result": validation_result,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logging.error(f"验证任务 {job_id} 原子性失败: {e}")
        raise HTTPException(status_code=500, detail=f"{get_translation('api.errors.validateTaskAtomicityFailed')}: {str(e)}")

@job_router.get("/validation/instance")
async def validate_instance_atomicity():
    """验证整个实例的原子性一致性 - 测试修复效果"""
    try:
        from api.wiki.wiki_job_manager import get_job_manager
        
        job_manager = get_job_manager()
        validation_result = await job_manager.validate_instance_atomicity_async()
        
        return {
            "success": True,
            "validation_result": validation_result,
            "timestamp": datetime.now().isoformat(),
            "fix_status": {
                "atomic_assignment_fixed": True,
                "orphan_cleanup_fixed": True,
                "validation_available": True
            }
        }
    except Exception as e:
        logging.error(f"验证实例原子性失败: {e}")
        raise HTTPException(status_code=500, detail=f"{get_translation('api.errors.validateInstanceAtomicityFailed')}: {str(e)}")


@job_router.post("/manager/force-election")
async def force_manager_election():
    """
    强制触发管理实例重新选举
    
    该接口用于手动触发选举，主要用于：
    1. 测试选举机制
    2. 在管理实例异常时手动干预
    3. 验证选举结果
    """
    try:
        from api.wiki.wiki_job_manager import get_job_manager
        from api.database.base import session_scope
        from api.model.job_manager_lock import JobManagerInstance
        from sqlmodel import select
        
        job_manager = get_job_manager()
        
        with session_scope() as session:
            # 获取所有活跃实例
            active_query = select(JobManagerInstance).where(
                JobManagerInstance.status == "active"
            )
            active_instances = session.exec(active_query).all()
            
            if not active_instances:
                return {
                    "success": False,
                    "message": get_translation("api.errors.noActiveInstancesForElection"),
                    "elected_manager": None,
                    "total_instances": 0
                }
            
            # 执行选举
            previous_manager = await job_manager.distributed_lock.get_current_manager_instance()
            elected_manager = job_manager.distributed_lock._elect_manager_instance(active_instances)
            
            if not elected_manager:
                return {
                    "success": False,
                    "message": get_translation("api.errors.electionFailed"),
                    "previous_manager": previous_manager,
                    "total_instances": len(active_instances)
                }
            
            # 构建选举结果
            election_result = {
                "success": True,
                "message": get_translation("api.errors.electionCompleted"),
                "previous_manager": previous_manager,
                "elected_manager": elected_manager.instance_id,
                "manager_changed": previous_manager != elected_manager.instance_id,
                "election_details": {
                    "manager_hostname": elected_manager.hostname,
                    "manager_pid": elected_manager.pid,
                    "manager_heartbeat": elected_manager.last_heartbeat.isoformat() if elected_manager.last_heartbeat else None,
                    "manager_current_jobs": elected_manager.current_jobs,
                    "total_candidates": len(active_instances)
                },
                "all_candidates": []
            }
            
            # 添加所有候选实例信息
            from datetime import datetime
            current_time = datetime.now()
            
            for instance in active_instances:
                candidate_info = {
                    "instance_id": instance.instance_id,
                    "hostname": instance.hostname,
                    "pid": instance.pid,
                    "heartbeat": instance.last_heartbeat.isoformat() if instance.last_heartbeat else None,
                    "current_jobs": instance.current_jobs,
                    "is_elected": instance.instance_id == elected_manager.instance_id
                }
                
                if instance.last_heartbeat:
                    candidate_info["time_since_heartbeat_seconds"] = int((current_time - instance.last_heartbeat).total_seconds())
                
                election_result["all_candidates"].append(candidate_info)
            
            # 按心跳时间排序候选者
            election_result["all_candidates"].sort(
                key=lambda x: (x.get("time_since_heartbeat_seconds", 999999), x["instance_id"])
            )
            
            return election_result
            
    except Exception as e:
        logging.error(f"强制选举失败: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.forceElectionFailed"))
  
