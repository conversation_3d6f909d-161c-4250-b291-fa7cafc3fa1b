"""
目录缓存管理路由 - 基于现有Redis架构
提供目录树构建、缓存管理等API接口
"""

import logging
from fastapi import APIRouter, HTTPException, BackgroundTasks, Query
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from pathlib import Path

from api.filesearch.directory_cache_service import get_directory_cache_service
from api.filesearch.file_search_service import get_file_search_service

logger = logging.getLogger(__name__)

directory_cache_router = APIRouter(prefix="/api/filecache", tags=["filecache"])


class BuildCacheRequest(BaseModel):
    """构建缓存请求"""
    repo_url: str
    branch: str = "main"
    user_code: str
    force_rebuild: bool = False


class PreBuildRequest(BaseModel):
    """预构建请求"""
    projects: List[Dict[str, str]]  # [{"repo_url": "", "branch": "", "root_path": ""}]


@directory_cache_router.post("/build")
async def build_directory_cache(
    request: BuildCacheRequest,
    background_tasks: BackgroundTasks
):
    """
    构建目录缓存
    
    构建策略：
    1. 全量扫描项目目录
    2. 多层Redis数据结构优化查询
    3. 支持手动预缓存
    """
    try:
        cache_service = get_directory_cache_service()
        file_service = get_file_search_service()
        
        # 解析repo_url获取owner和repo_name
        if "/" in request.repo_url:
            parts = request.repo_url.strip("/").split("/")
            owner = parts[-2] if len(parts) >= 2 else ""
            repo_name = parts[-1]
        else:
            owner = ""
            repo_name = request.repo_url
        
        # 获取项目路径
        path_mappings = file_service.get_project_paths(
            owner=owner,
            repo_name=repo_name,
            branch=request.branch,
            user_code=request.user_code
        )
        
        real_code_path = path_mappings["real_code_path"]
        
        # 检查路径是否存在
        if not real_code_path.exists():
            raise HTTPException(
                status_code=404,
                detail=f"项目路径不存在: {real_code_path}"
            )
        
        # 如果需要强制重建，先清除缓存
        if request.force_rebuild:
            cache_service.invalidate_cache(request.repo_url, request.branch)
        
        # 后台构建缓存
        background_tasks.add_task(
            _build_cache_task,
            real_code_path,
            request.repo_url,
            request.branch
        )
        
        return {
            "success": True,
            "message": "缓存构建任务已启动",
            "project_path": str(real_code_path),
            "cache_strategy": "multi_layer_redis"
        }
        
    except Exception as e:
        logger.error(f"构建目录缓存失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def _build_cache_task(real_path: Path, repo_url: str, branch: str):
    """后台构建缓存任务"""
    try:
        cache_service = get_directory_cache_service()
        logger.info(f"开始构建缓存: {repo_url}:{branch}")
        
        success = cache_service.build_directory_cache(real_path, repo_url, branch)
        
        if success:
            stats = cache_service.get_cache_stats(repo_url, branch)
            logger.info(f"缓存构建成功: {repo_url}:{branch}, 文件: {stats.get('file_count')}, 目录: {stats.get('dir_count')}")
        else:
            logger.error(f"缓存构建失败: {repo_url}:{branch}")
            
    except Exception as e:
        logger.error(f"后台缓存构建任务失败: {e}")


@directory_cache_router.post("/prebuild")
async def prebuild_multiple_caches(
    request: PreBuildRequest,
    background_tasks: BackgroundTasks
):
    """
    预构建多个项目的缓存
    
    用于管理员手动预缓存常用项目
    """
    try:
        cache_service = get_directory_cache_service()
        
        # 后台批量构建
        background_tasks.add_task(
            _prebuild_task,
            request.projects
        )
        
        return {
            "success": True,
            "message": f"预构建任务已启动，共 {len(request.projects)} 个项目",
            "projects_count": len(request.projects)
        }
        
    except Exception as e:
        logger.error(f"预构建失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def _prebuild_task(projects: List[Dict[str, str]]):
    """后台预构建任务"""
    try:
        cache_service = get_directory_cache_service()
        results = cache_service.prebuild_cache_for_projects(projects)
        
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        
        logger.info(f"预构建完成: {success_count}/{total_count} 成功")
        
        # 记录失败的项目
        for project_key, success in results.items():
            if not success:
                logger.warning(f"预构建失败: {project_key}")
                
    except Exception as e:
        logger.error(f"预构建任务失败: {e}")


@directory_cache_router.get("/stats")
async def get_cache_stats(
    repo_url: str = Query(..., description="仓库URL"),
    branch: str = Query("main", description="分支名称")
):
    """
    获取缓存统计信息
    """
    try:
        cache_service = get_directory_cache_service()
        stats = cache_service.get_cache_stats(repo_url, branch)
        
        return {
            "success": True,
            "data": stats
        }
            
    except Exception as e:
        logger.error(f"获取缓存统计失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@directory_cache_router.delete("/clear")
async def clear_cache(
    repo_url: str = Query(..., description="仓库URL"),
    branch: str = Query("main", description="分支名称")
):
    """
    清除指定项目的缓存
    """
    try:
        cache_service = get_directory_cache_service()
        cache_service.invalidate_cache(repo_url, branch)
        
        return {
            "success": True,
            "message": f"缓存已清除: {repo_url}:{branch}"
        }
        
    except Exception as e:
        logger.error(f"清除缓存失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@directory_cache_router.get("/search")
async def search_cached_files(
    repo_url: str = Query(..., description="仓库URL"),
    pattern: str = Query("", description="搜索模式"),
    branch: str = Query("main", description="分支名称")
):
    """
    从缓存中搜索文件
    
    支持：
    - 文件名模糊搜索
    - 路径搜索
    - 目录浏览（pattern以/结尾）
    """
    try:
        cache_service = get_directory_cache_service()
        
        # 检查缓存是否存在
        if not cache_service.is_cached(repo_url, branch):
            raise HTTPException(
                status_code=404,
                detail=f"缓存不存在，请先构建缓存: {repo_url}:{branch}"
            )
        
        # 执行搜索
        if pattern.endswith("/"):
            # 目录浏览模式
            results = cache_service.browse_directory(repo_url, branch, pattern.rstrip("/"))
        else:
            # 模糊搜索模式
            results = cache_service.search_files(repo_url, branch, pattern)
        
        return {
            "success": True,
            "data": results,
            "total": len(results),
            "pattern": pattern,
            "from_cache": True,
            "cache_type": "redis_multi_layer"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"缓存搜索失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@directory_cache_router.get("/browse")
async def browse_cached_directory(
    repo_url: str = Query(..., description="仓库URL"),
    path: str = Query("", description="目录路径"),
    branch: str = Query("main", description="分支名称")
):
    """
    浏览缓存中的目录
    """
    try:
        cache_service = get_directory_cache_service()
        
        # 检查缓存是否存在
        if not cache_service.is_cached(repo_url, branch):
            raise HTTPException(
                status_code=404,
                detail=f"缓存不存在，请先构建缓存: {repo_url}:{branch}"
            )
        
        # 浏览目录
        results = cache_service.browse_directory(repo_url, branch, path)
        
        return {
            "success": True,
            "data": results,
            "total": len(results),
            "path": path,
            "from_cache": True
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"缓存目录浏览失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@directory_cache_router.get("/health")
async def cache_health_check():
    """缓存服务健康检查"""
    try:
        cache_service = get_directory_cache_service()
        
        # 检查Redis连接
        redis_connected = cache_service.redis_client and cache_service.redis_client.is_connected()
        
        return {
            "success": True,
            "redis_connected": redis_connected,
            "cache_ttl": cache_service.cache_ttl,
            "max_depth": cache_service.max_depth,
            "memory_cache_size": len(cache_service._memory_cache),
            "cache_strategy": "multi_layer_redis_with_fallback"
        }
        
    except Exception as e:
        logger.error(f"缓存健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
