"""
Kubernetes和沙盒管理API
API路由模块，提供K8s Job和沙盒相关的接口
"""

import logging
from typing import Optional, Dict, Any, List
from fastapi import APIRouter, Depends, Query, Body, Request
import asyncio
from concurrent.futures import ThreadPoolExecutor
from api.middleware.auth_middleware import get_current_user
from api.middleware.language_middleware import get_translation
from api.middleware.audit_log_middleware import audit_logger
from api.model.git_repository import AiDwGitRepository
from api.model.wiki_repository_relation import AiDwWikiRepositoryRelation
from api.sandbox.sandbox_service import sandbox_service
from api.database.base import session_scope
from api.service.git_repository_service import update_repository
from api.service.wiki_info_service import search_wikis, get_wiki_info, update_wiki_info, create_wiki_info, get_wiki_info_by_repo_and_branch
from api.model.user_info import UserInfo
from sqlmodel import select
from api.service import user_service
from api.service.wiki_query_service import get_wiki_basic_info
from api.service.wiki_service import create_job, update_job, get_active_jobs_by_wiki_id
from api.type.sandbox_status import SandboxStatus
from api.utils.git_utils import extract_repo_info
from api.utils.job_redis_utils import get_wiki_group_id
from api.wiki.generator import WikiGenerator
from api.docchain.client import docchain_manager
from api.database.base import db_service
from api.model.wiki_info import WikiInfo
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

EXECUTOR = ThreadPoolExecutor(max_workers=15)

# 创建路由器
k8s_router = APIRouter(prefix="/api/k8s", tags=["Kubernetes管理"])

# ==================== 沙盒管理路由 ====================

@k8s_router.get("/sandbox/list")
async def list_sandboxes(
    user_code: Optional[str] = Query(None, description="用户代码"),
    project: Optional[str] = Query(None, description="项目名称")
):
    """获取沙盒列表"""
    try:
        if user_code and project:
            # 获取特定用户和项目的沙盒
            sandbox = await sandbox_service.get_sandbox_info(user_code, project)
            return {"success": True, "data": [sandbox] if sandbox else []}
        elif user_code:
            # 获取用户的所有沙盒
            sandboxes = await sandbox_service.list_user_sandboxes(user_code)
            return {"success": True, "data": sandboxes}
        elif project:
            # 获取项目的所有沙盒
            sandboxes = await sandbox_service.list_project_sandboxes(project)
            return {"success": True, "data": sandboxes}
        else:
            # 获取所有沙盒
            sandboxes = await sandbox_service.list_all_sandboxes()
            return {"success": True, "data": sandboxes}
    except Exception as e:
        logger.error(f"获取沙盒列表失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.get("/sandbox/list/paginated")
async def list_sandboxes_paginated(
    user_code: Optional[str] = Query(None, description="用户代码"),
    project: Optional[str] = Query(None, description="项目名称"),
    limit: Optional[int] = Query(50, ge=1, le=500),
    continue_token: Optional[str] = Query(None, description="翻页令牌")
):
    """分页获取沙盒列表（基于k8s SDK limit/_continue）。"""
    try:
        result = await sandbox_service.list_sandboxes_paginated(user_code, project, limit or 50, continue_token)
        return {"success": True, "data": result}
    except Exception as e:
        logger.error(f"分页获取沙盒列表失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.post("/sandbox/create")
async def create_sandbox(
    user_code: str = Body(..., description="用户代码"),
    git_url: str = Body(..., description="Git仓库URL"),
    branch: str = Body("main", description="分支名称"),
    wct_api_key: str = Body("", description="WCT API密钥"),
    user_name: Optional[str] = Body(None, description="用户姓名，可选")
):
    """创建或获取沙盒"""
    try:
        # 如果未传入user_name，则尝试从用户服务查询
        if not user_name:
            try:
                from api.service.user_service import select_user_info_by_code
                _u = select_user_info_by_code(user_code)
                if _u:
                    user_name = getattr(_u, 'user_name', None) or getattr(_u, 'name', None)
            except Exception:
                user_name = None
        sandbox = await sandbox_service.get_or_create_sandbox_for_external_sandbox(user_code, git_url, branch, wct_api_key, user_name)
        return {"success": True, "data": sandbox}
    except Exception as e:
        logger.error(f"创建沙盒失败: {e}")
        err_text = str(e)
        if "配额" in err_text:
            return {"success": False, "error": "已达到个人沙盒配额上限，请稍后再试或释放已有沙盒", "code": "QUOTA_EXCEEDED"}
        if "系统繁忙" in err_text:
            return {"success": False, "error": "系统资源繁忙，请稍后再试", "code": "SYSTEM_BUSY"}
        return {"success": False, "error": str(e)}

@k8s_router.post("/sandbox/me/create")
async def create_sandbox_me(
    request: dict = Body(..., description="Request body containing wiki_id")
):
    """创建或获取沙盒（当前用户）"""
    try:
        wiki_id = request.get("wiki_id")
        if not wiki_id:
            return {"success": False, "error": "缺少wiki_id参数"}
        
        from api.middleware.auth_middleware import get_current_user
        current_user = get_current_user()
        if not current_user:
            return {"success": False, "error": get_translation("sandbox.k8s.errors.unauthenticated")}
        user_code = current_user.get("user_code") or current_user.get("username") or current_user.get("id")
        if not user_code:
            return {"success": False, "error": "无法获取用户代码"}
        sandbox = await sandbox_service.get_or_create_sandbox_for_external_sandbox(user_code, wiki_id, current_user.get("id"), current_user.get("user_name"))
        return {"success": True, "data": sandbox}
    except Exception as e:
        logger.error(f"创建沙盒失败: {e}")
        err_text = str(e)
        if "配额" in err_text:
            return {"success": False, "error": "已达到个人沙盒配额上限，请稍后再试或释放已有沙盒", "code": "QUOTA_EXCEEDED"}
        if "系统繁忙" in err_text:
            return {"success": False, "error": "系统资源繁忙，请稍后再试", "code": "SYSTEM_BUSY"}
        return {"success": False, "error": str(e)}

@k8s_router.delete("/sandbox/delete")
async def delete_sandbox(
    job_name: str = Query(..., description="Job名称"),
):
    """删除沙盒"""
    user_id = None
    wiki_id = None
    user_code = None
    try:
        from api.sandbox.kubernetes_service import get_kubernetes_service
        kubernetes_service = get_kubernetes_service()
        job_info = kubernetes_service.get_job(job_name)
        user_id = job_info.get('annotations', {}).get('user.id')
        wiki_id = job_info.get('annotations', {}).get('wiki.id')
        user_code = job_info.get('annotations', {}).get('user.code')
        result = await sandbox_service.delete_sandbox(job_name, user_code)
        # 删除成功后，还需从redis的job_pool中删除job
        from api.service.job_pool_service import get_job_pool_service
        result = get_job_pool_service().remove_job_from_pool(job_name)
        if not result:
            logger.error(f"从Redis中移除Job失败: {job_name}")

        # 删除成功后，还需从redis的job_pool中删除用户分配记录
        from api.service.job_pool_service import get_job_pool_service
        result = get_job_pool_service().delete_user_allocation(user_id, wiki_id)

        # 删除成功后，redis的个人沙盒配额计数减1

        sandbox_service.decrement_user_quota(user_code)
        return {"success": result, "message": "删除成功" if result else "删除失败"}
    except Exception as e:
        logger.error(f"删除沙盒失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.delete("/sandbox/me/delete")
async def release_sandbox_me(
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """
    释放用户的job，将其放回可用池
    """
    from api.service.job_pool_service import get_job_pool_service
    
    data = await request.json()
    wiki_id = data.get("wiki_id")
    user_id = current_user.get("id")
    
    if not wiki_id:
        return {"success": False, "message": "wiki_id不能为空"}
    
    # 释放job
    job_pool_service = get_job_pool_service()
    success, error_msg = job_pool_service.release_job(
        user_id=user_id,
        wiki_id=wiki_id
    )
    
    if success:
        # 释放成功后，redis的个人沙盒配额计数减1
        from api.sandbox.sandbox_service import sandbox_service
        sandbox_service.decrement_user_quota(current_user.get("user_code"))
        return {
            "success": True,
            "message": "Job释放成功"
        }
    else:
        return {
            "success": False,
            "message": error_msg or "Job释放失败"
        }

@k8s_router.post("/sandbox/update-access")
async def update_sandbox_access(
    user_code: str = Body(..., description="用户代码"),
    git_url: str = Body(..., description="Git仓库URL"),
    branch: str = Body("main", description="分支名称")
):
    """更新沙盒访问时间"""
    try:
        result = await sandbox_service.update_sandbox_access(user_code, git_url, branch)
        return {"success": result, "message": "更新成功" if result else "更新失败"}
    except Exception as e:
        logger.error(f"更新沙盒访问时间失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.post("/sandbox/cleanup")
async def cleanup_idle_sandboxes():
    """清理空闲沙盒"""
    try:
        cleaned_count = await sandbox_service.cleanup_idle_sandboxes()
        return {"success": True, "cleaned_count": cleaned_count}
    except Exception as e:
        logger.error(f"清理空闲沙盒失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.get("/sandbox/url")
async def get_sandbox_url(
    user_code: str = Query(..., description="用户代码"),
    git_url: str = Query(..., description="Git仓库URL"),
    branch: str = Query("main", description="分支名称")
):
    """获取沙盒访问URL"""
    try:
        url = sandbox_service.get_sandbox_url(user_code, git_url, branch)
        return {"success": True, "url": url}
    except Exception as e:
        logger.error(f"获取沙盒URL失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.get("/sandbox/me/url")
async def get_sandbox_url_me(
    git_url: str = Query(..., description="Git仓库URL"),
    branch: str = Query("main", description="分支名称")
):
    """获取沙盒访问URL（当前用户）"""
    try:
        from api.middleware.auth_middleware import get_current_user
        current_user = get_current_user()
        if not current_user:
            return {"success": False, "error": "用户未认证，请先登录"}
        user_code = current_user.get("user_code") or current_user.get("username") or current_user.get("id")
        if not user_code:
            return {"success": False, "error": "无法获取用户代码"}
        logger.info(f"使用当前用户获取沙盒URL: user_code={user_code}, git_url={git_url}, branch={branch}")
        url = sandbox_service.get_sandbox_url(user_code, git_url, branch)
        return {"success": True, "url": url}
    except Exception as e:
        logger.error(f"获取沙盒URL失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.get("/sandbox/status")
async def get_sandbox_status(
    user_code: str = Query(..., description="用户代码"),
    git_url: str = Query(..., description="Git仓库URL"),
    branch: str = Query("main", description="分支名称"),
    job_name: str = Query(..., description="job_name")
):
    """获取沙盒详细状态"""
    try:
        status_info = await sandbox_service.get_sandbox_detailed_status(user_code, git_url, branch, job_name)
        return {"success": True, "data": status_info}
    except Exception as e:
        logger.error(f"获取沙盒状态失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.get("/sandbox/me/status")
async def get_sandbox_status_me(
    git_url: str = Query(..., description="Git仓库URL"),
    branch: str = Query("main", description="分支名称")
):
    """获取沙盒详细状态（当前用户）"""
    try:
        from api.middleware.auth_middleware import get_current_user
        current_user = get_current_user()
        if not current_user:
            return {"success": False, "error": "用户未认证，请先登录"}
        user_code = current_user.get("user_code") or current_user.get("username") or current_user.get("id")
        if not user_code:
            return {"success": False, "error": "无法获取用户代码"}
        logger.info(f"使用当前用户获取沙盒状态: user_code={user_code}, git_url={git_url}, branch={branch}")
        status_info = await sandbox_service.get_sandbox_detailed_status(user_code, git_url, branch)
        return {"success": True, "data": status_info}
    except Exception as e:
        logger.error(f"获取沙盒状态失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.get("/sandbox/me/status/new")
async def get_sandbox_status_me_new(
    wiki_id: str = Query(..., description="Wiki ID")
):
    """获取沙盒详细状态（当前用户）"""
    try:
        from api.middleware.auth_middleware import get_current_user
        current_user = get_current_user()
        if not current_user:
            return {"success": False, "error": "用户未认证，请先登录"}
        
        user_id = current_user.get("id")
        user_code = current_user.get("user_code") or current_user.get("username") or str(user_id)
        
        if not user_code or not user_id:
            return {"success": False, "error": "无法获取用户信息"}
        
        logger.info(f"使用当前用户获取沙盒状态: user_code={user_code}, user_id={user_id}, wiki_id={wiki_id}")
        # 获取wiki信息
        wiki_info = get_wiki_basic_info(wiki_id=wiki_id)
        
        # 从Redis中查询用户在该Wiki下分配的job
        from api.service.job_pool_service import get_job_pool_service
        job_pool_service = get_job_pool_service()
        job_name = job_pool_service.get_user_job(user_id=user_id, wiki_id=wiki_info.get('id'))
        
        if not job_name:
            # 用户暂未分配job
            return {
                "success": True,
                "data": {
                    "status": SandboxStatus.NOT_CREATED.value,
                    "status_description": get_translation("sandbox.status.description.NOT_CREATED"),
                    "job_name": "",
                    "message": get_translation("sandbox.status.message.NOT_CREATED"),
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            }
        
        
        # 获取job详细状态
        status_info = await sandbox_service.get_sandbox_detailed_status(
            user_code, 
            git_url=wiki_info.get('repo_url'), 
            branch=wiki_info.get('branch'), 
            job_name=job_name
        )
        
        # 添加分配状态标识
        status_info["allocated"] = True
        status_info["job_name"] = job_name
        
        return {"success": True, "data": status_info}
        
    except Exception as e:
        logger.error(f"获取沙盒状态失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.get("/sandbox/me/portal")
async def get_sandbox_portal_link(
    wiki_id: Optional[str] = Query(None, description="Wiki ID"),
    git_url: Optional[str] = Query(None, description="Git仓库URL"),
    branch: Optional[str] = Query("main", description="分支名称")
):
    """获取容器控制台跳转链接（当前用户）"""
    try:
        current_user = get_current_user()
        if not current_user:
            return {"success": False, "error": "用户未认证，请先登录"}

        user_id = current_user.get("id")
        user_code = current_user.get("user_code") or current_user.get("username") or (str(user_id) if user_id else None)

        if not user_code:
            return {"success": False, "error": get_translation("sandbox.k8s.errors.userInfoMissing")}

        resolved_git_url = git_url
        resolved_branch = branch or "main"
        wiki_db_id = None

        if wiki_id:
            wiki_info = get_wiki_basic_info(wiki_id=wiki_id)
            if not wiki_info:
                return {"success": False, "error": get_translation("sandbox.k8s.errors.wikiNotFound")}
            resolved_git_url = wiki_info.get("repo_url") or resolved_git_url
            resolved_branch = wiki_info.get("branch") or resolved_branch or "main"
            wiki_db_id = wiki_info.get("id")

        if not resolved_git_url:
            return {"success": False, "error": get_translation("api.k8s.errors.repoInfoMissing")}

        from api.sandbox.kubernetes_service import get_kubernetes_service
        k8s = get_kubernetes_service()

        job_name: Optional[str] = None
        if wiki_db_id and user_id:
            try:
                from api.service.job_pool_service import get_job_pool_service
                job_pool_service = get_job_pool_service()
                job_name = job_pool_service.get_user_job(user_id=user_id, wiki_id=wiki_db_id)
            except Exception as pool_err:
                logger.warning(f"查询Job Pool失败，使用默认命名: {pool_err}")

        if not job_name:
            job_name = k8s._generate_job_name_from_git(user_code, resolved_git_url, resolved_branch)

        job_info = k8s.get_job(job_name)
        if not job_info:
            return {"success": False, "error": get_translation("sandbox.k8s.errors.jobNotReady")}

        pods = job_info.get("pods") or k8s.get_job_pods(job_name)
        if not pods:
            return {"success": False, "error": get_translation("sandbox.k8s.errors.podStarting")}

        target_pod = None
        for pod in pods:
            if pod.get("pod_ip"):
                target_pod = pod
                break
        if target_pod is None:
            target_pod = pods[0]

        pod_ip = target_pod.get("pod_ip") or target_pod.get("host_ip")
        pod_name = target_pod.get("name")
        container_statuses = target_pod.get("container_statuses") or []
        target_container = None
        for status in container_statuses:
            if status.get("container_id"):
                target_container = status
                break

        if not target_container:
            return {"success": False, "error": get_translation("sandbox.k8s.errors.containerNotReady")}

        container_name = target_container.get("name")
        container_id = target_container.get("container_id")

        if not pod_ip or not container_name or not container_id:
            return {"success": False, "error": get_translation("sandbox.k8s.errors.containerInfoIncomplete")}

        portal_base = getattr(k8s, "portal_base_url", "") or ""
        if not portal_base:
            return {"success": False, "error": get_translation("sandbox.k8s.errors.portalNotConfigured")}

        namespace = job_info.get("namespace") or k8s.namespace

        if not portal_base.endswith('/'):
            portal_base = f"{portal_base}/"

        params: List[tuple[str, str]] = [
            ("applicationName", job_name),
            ("ip", pod_ip),
            ("containerName", container_name),
            ("containerId", container_id),
            ("podName", pod_name or ""),
            ("jobName", job_name),
            ("privileger", "DEEP_WIKI"),
            ("k8sMasterUrl", k8s.api_server or ""),
            ("k8sNamespace", namespace or ""),
            ("applicationType", "STATELESS"),
            ("concise", "true"),
            ("url", "zcm-tool/modules/newconsole/views/newConsole"),
            ("channel", "k8s"),
            ("cmd", "sudo -E -u wct env HOME=/home/<USER>"cd /data/workspace && wct-cli\""),
        ]

        from urllib.parse import quote

        def _format_param(key: str, value: str) -> Optional[str]:
            if value is None:
                return None
            if key in {"k8sMasterUrl", "url"}:
                return f"{key}={value}"
            return f"{key}={quote(str(value))}"

        query_string = "&".join(
            filter(None, (_format_param(k, v) for k, v in params))
        )

        portal_url = f"{portal_base}?{query_string}" if query_string else portal_base

        return {
            "success": True,
            "data": {
                "url": portal_url,
                "job_name": job_name,
                "pod_name": pod_name,
                "container_name": container_name,
                "namespace": namespace,
            }
        }

    except Exception as e:
        logger.error(f"获取容器跳转链接失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.get("/sandbox/me/list")
async def list_my_sandboxes():
    """获取当前用户的所有沙盒"""
    try:
        from api.middleware.auth_middleware import get_current_user
        current_user = get_current_user()
        if not current_user:
            return {"success": False, "error": "用户未认证，请先登录"}
        user_code = current_user.get("user_code") or current_user.get("username") or current_user.get("id")
        if not user_code:
            return {"success": False, "error": "无法获取用户代码"}
        sandboxes = await sandbox_service.list_user_sandboxes(user_code)

        # 追加详细状态（并发查询）
        try:
            import asyncio as _asyncio
            async def _get_detail(sb):
                git_url = sb.get('git_url') or (sb.get('annotations') or {}).get('git.url')
                branch = sb.get('branch') or (sb.get('annotations') or {}).get('git.branch') or 'main'
                job_name = (sb.get('labels') or {}).get('app')
                if not git_url:
                    return None
                detail = await sandbox_service.get_sandbox_detailed_status(user_code, git_url, branch, job_name)
                return (sb.get('name'), detail)

            details = await _asyncio.gather(*[_get_detail(sb) for sb in sandboxes if sb], return_exceptions=True)
            name_to_detail = {}
            for res in details:
                if isinstance(res, tuple) and res[0]:
                    name_to_detail[res[0]] = res[1]

            # 合并到原数据
            for sb in sandboxes:
                d = name_to_detail.get(sb.get('name'))
                if isinstance(d, dict):
                    sb['detailed_status'] = d.get('status')
                    sb['detailed_status_description'] = d.get('status_description')
                    sb['detailed_message'] = d.get('message')
                    sb['pod_ip'] = d.get('pod_ip')
        except Exception as _e:
            logger.warning(f"追加详细状态失败: {_e}")

        return {"success": True, "data": sandboxes}
    except Exception as e:
        logger.error(f"获取个人沙盒列表失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.get("/sandbox/me/quota")
async def get_my_sandbox_quota():
    """获取当前用户的沙盒配额与使用情况"""
    try:
        from api.middleware.auth_middleware import get_current_user
        current_user = get_current_user()
        if not current_user:
            return {"success": False, "error": "用户未认证，请先登录"}
        user_code = current_user.get("user_code") or current_user.get("username") or current_user.get("id")
        if not user_code:
            return {"success": False, "error": "无法获取用户代码"}

        from api.sandbox.kubernetes_service import get_kubernetes_service
        k8s = get_kubernetes_service()
        job_cfg = k8s.k8s_config.get("job", {})

        # 优先读取数据库中的个性化配额
        per_user_max = None
        try:
            from api.service.user_service import get_user_sandbox_quota_by_user_code
            quota_val = get_user_sandbox_quota_by_user_code(user_code)
            if quota_val is not None:
                per_user_max = int(quota_val)
        except Exception:
            per_user_max = None

        if not per_user_max or per_user_max <= 0:
            per_user_max = int(job_cfg.get("per_user_max_containers", 0))

        user_sandboxes = await sandbox_service.list_user_sandboxes(user_code)
        used = len(user_sandboxes)
        remaining = max(per_user_max - used, 0) if per_user_max and per_user_max > 0 else None
        return {"success": True, "data": {"per_user_max": per_user_max, "used": used, "remaining": remaining}}
    except Exception as e:
        logger.error(f"获取个人沙盒配额失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.post("/sandbox/symbolic-link")
async def create_symbolic_link(
    pod_name: str = Body(..., description="pod名称"),
    paths: List[Dict[str, str]] = Body(..., description="软链接列表")
):
    return await sandbox_service.create_symbolic_link(pod_name, paths)

@k8s_router.delete("/sandbox/symbolic-link")
async def delete_symbolic_link(
    pod_name: str = Body(..., description="pod名称"),
    paths: List[str] = Body(..., description="软链接路径")
):
    return await sandbox_service.unlink_symbolic(pod_name, paths)


# ==================== 应用级沙盒（仅手动创建/删除，不参与自动清理） ====================

def _get_app_info_by_id(app_id: int) -> Optional[Dict[str, str]]:
    """
    查询应用基础信息，返回 {app_id, app_name, app_code}
    优先使用服务方法，其次尝试原生SQL（表名按常见命名尝试）。
    """
    try:
        # 明确使用 App.app_id 字段查询
        from api.model.app import App
        from sqlmodel import select
        with session_scope() as session:
            try:
                app = session.exec(select(App).where(App.id == int(app_id))).first()
                if app:
                    return {
                        "app_pk": int(app.id),
                        "app_id": str(app.app_id),
                        "app_name": app.app_name or str(app.app_id),
                        "app_code": str(app.app_code)
                    }
            except Exception:
                pass
    except Exception as e:
        logger.error(f"查询应用信息失败: {e}")
    return None


@k8s_router.post("/sandbox/app/{app_id}")
async def create_app_sandbox(app_id: str, audit_log=Depends(audit_logger)):
    """创建应用级沙盒（仅挂载代码目录，名称 job-{env}-app-{appCode}）。"""
    try:
        app = _get_app_info_by_id(app_id)
        if not app:
            return {"success": False, "error": f"未找到应用: {app_id}"}
        # 获取当前登录用户作为创建者
        creator_id = None
        creator_code = None
        creator_name = None
        try:
            from api.middleware.auth_middleware import get_current_user
            cu = get_current_user()
            if cu:
                creator_id = cu.get("id")
                creator_code = cu.get("user_code") or cu.get("username")
                creator_name = cu.get("user_name") or cu.get("name")
        except Exception:
            pass

        # 获取创建者 API Key，参考Wiki的方式
        wct_api_key = None
        try:
            if creator_id:
                from api.service.user_service import select_user_ext
                from api.utils.aes_utils import AESUtils
                user_ext = select_user_ext(int(creator_id))
                if user_ext and user_ext.ai_api_key:
                    aes = AESUtils()
                    wct_api_key = aes.decrypt(user_ext.ai_api_key)
        except Exception:
            wct_api_key = None

        job = await sandbox_service.create_app_sandbox(
            app_id=app["app_id"],
            app_code=app["app_code"],
            app_name=app.get("app_name"),
            creator_id=creator_id,
            creator_code=creator_code,
            creator_name=creator_name,
            wct_api_key=wct_api_key,
            app_pk=app.get("app_pk"),
        )

        # 设置审计日志数据
        audit_log(code="APP_SANDBOX_START", party_type="服务", party_id=app_id, party_code=app["app_code"], party_name=app["app_name"])

        return {"success": True, "data": job}
    except Exception as e:
        logger.error(f"创建应用级沙盒失败: {e}")
        return {"success": False, "error": str(e)}


@k8s_router.get("/sandbox/app/{app_id}/status")
async def get_app_sandbox_status(app_id: str):
    """查询应用级沙盒状态。"""
    try:
        app = _get_app_info_by_id(app_id)
        if not app:
            return {"success": False, "error": f"未找到应用: {app_id}"}
        status = await sandbox_service.get_app_sandbox_status(app_code=app["app_code"])
        return {"success": True, "data": status}
    except Exception as e:
        logger.error(f"查询应用级沙盒状态失败: {e}")
        return {"success": False, "error": str(e)}


@k8s_router.delete("/sandbox/app/{app_id}")
async def delete_app_sandbox(app_id: str, audit_log=Depends(audit_logger)):
    """删除应用级沙盒。"""
    try:
        app = _get_app_info_by_id(app_id)
        if not app:
            return {"success": False, "error": f"未找到应用: {app_id}"}
        ok = await sandbox_service.delete_app_sandbox(app_code=app["app_code"])

        # 设置审计日志数据
        audit_log(code="APP_SANDBOX_STOP", party_type="服务", party_id=app_id, party_code=app["app_code"], party_name=app["app_name"])

        return {"success": ok, "message": "删除成功" if ok else "删除失败"}
    except Exception as e:
        logger.error(f"删除应用级沙盒失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.post("/sandbox/app/{app_id}/restart")
async def rolling_restart_app_sandbox(
    app_id: str,
    max_surge: Optional[int] = Body(1, description="并行新建版本数量（占位，当前按顺序创建）"),
    wait_ready_timeout: Optional[int] = Body(300, description="新版本就绪等待超时（秒）")
):
    """滚动重启应用级沙盒。
    - 行为：先返回（前端立即能看到老任务+新任务），后台等待新任务就绪后清理旧任务。
    - 命名冲突通过 app-series 与修订时间戳解决。
    """
    try:
        app = _get_app_info_by_id(app_id)
        if not app:
            return {"success": False, "error": f"未找到应用: {app_id}"}

        # 异步重启：创建后立即返回，使任务列表短时间内显示两个任务
        result = await sandbox_service.rolling_restart_app_async(
            app_id=app["app_id"],
            app_code=app["app_code"],
            app_name=app.get("app_name"),
            wait_ready_timeout=int(wait_ready_timeout or 300)
        )
        return {"success": bool(result.get("success", True)), "data": result} if isinstance(result, dict) else {"success": True, "data": result}
    except Exception as e:
        logger.error(f"滚动重启应用级沙盒失败: {e}")
        return {"success": False, "error": str(e)}

# ==================== 用户配额管理（新增） ====================

@k8s_router.get("/sandbox/quotas")
async def list_user_quotas(keyword: Optional[str] = Query(None, description="关键字(用户工号或姓名)"),
                           page: Optional[int] = Query(1, ge=1),
                           page_size: Optional[int] = Query(20, ge=1, le=500)):
    """列出用户及其配额（支持查询与分页）。"""
    try:
        result = user_service.list_users_with_quota(keyword, page, page_size)

        # 计算effective_quota（如果未设置，使用系统默认）
        from api.sandbox.kubernetes_service import get_kubernetes_service
        k8s = get_kubernetes_service()
        default_quota = int(k8s.k8s_config.get("job", {}).get("per_user_max_containers", 0))
        for u in result.get("users", []):
            u["effective_quota"] = u.get("sandbox_quota") if u.get("sandbox_quota") is not None else (default_quota or None)

        return {"success": True, "data": result}
    except Exception as e:
        logger.error(f"列出用户配额失败: {e}")
        return {"success": False, "error": str(e)}


@k8s_router.post("/sandbox/quotas/set")
async def set_user_quota(user_code: str = Body(..., description="用户工号"),
                         quota: Optional[int] = Body(None, description="个性化配额(为空则清除个性化设置)")):
    """设置指定用户的个性化沙盒配额。"""
    try:
        ok = user_service.set_user_sandbox_quota_by_user_code(user_code, quota)
        if ok:
            return {"success": True, "message": "设置成功"}
        return {"success": False, "error": "设置失败"}
    except Exception as e:
        logger.error(f"设置用户配额失败: {e}")
        return {"success": False, "error": str(e)}

# ==================== Kubernetes Job管理路由 ====================

@k8s_router.get("/jobs/list")
async def list_k8s_jobs(
    user_code: Optional[str] = Query(None, description="用户代码"),
    project: Optional[str] = Query(None, description="项目名称")
):
    """获取K8s Job列表"""
    try:
        from api.sandbox.kubernetes_service import get_kubernetes_service
        kubernetes_service = get_kubernetes_service()
        jobs = kubernetes_service.list_jobs(user_code, project)
        return {"success": True, "data": jobs}
    except Exception as e:
        logger.error(f"获取Job列表失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.get("/jobs/list/paginated")
async def list_k8s_jobs_paginated(
    user_code: Optional[str] = Query(None, description="用户代码"),
    project: Optional[str] = Query(None, description="项目名称"),
    limit: Optional[int] = Query(50, ge=1, le=500),
    continue_token: Optional[str] = Query(None, description="翻页令牌")
):
    """分页获取K8s Job列表（利用k8s SDK的limit/_continue能力）。"""
    try:
        from api.sandbox.kubernetes_service import get_kubernetes_service
        kubernetes_service = get_kubernetes_service()
        result = kubernetes_service.list_jobs_paginated(user_code, project, limit or 50, continue_token)
        return {"success": True, "data": result}
    except Exception as e:
        logger.error(f"分页获取Job列表失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.get("/jobs/{job_name}")
async def get_k8s_job(job_name: str):
    """获取单个Job详细信息"""
    try:
        from api.sandbox.kubernetes_service import get_kubernetes_service
        kubernetes_service = get_kubernetes_service()
        job = kubernetes_service.get_job(job_name)
        if job:
            return {"success": True, "data": job}
        else:
            return {"success": False, "error": "Job not found"}
    except Exception as e:
        logger.error(f"获取Job信息失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.post("/jobs/{job_name}/metadata")
async def update_k8s_job_metadata(job_name: str, payload: Dict[str, Any] = Body(...)):
    """更新或删除指定Job的标签与注解"""
    try:
        from api.sandbox.kubernetes_service import get_kubernetes_service
        kubernetes_service = get_kubernetes_service()

        annotations = payload.get("annotations") if isinstance(payload.get("annotations"), dict) else None
        labels = payload.get("labels") if isinstance(payload.get("labels"), dict) else None
        remove_annotations_value = payload.get("remove_annotations")
        remove_labels_value = payload.get("remove_labels")
        remove_annotations = remove_annotations_value if isinstance(remove_annotations_value, list) else []
        remove_labels = remove_labels_value if isinstance(remove_labels_value, list) else []

        # 逐步更新与删除元数据
        update_success = True
        delete_success = True

        if annotations or labels:
            update_success = kubernetes_service.update_job_metadata(job_name, annotations, labels)

        if remove_annotations or remove_labels:
            delete_success = kubernetes_service.delete_job_metadata(
                job_name,
                [str(item) for item in remove_annotations],
                [str(item) for item in remove_labels]
            )

        if not update_success or not delete_success:
            return {"success": False, "error": "更新Job元数据失败"}

        job = kubernetes_service.get_job(job_name)
        return {"success": True, "data": job}

    except Exception as e:
        logger.error(f"更新Job元数据失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.post("/jobs/create")
async def create_k8s_job(
    user_code: str = Body(..., description="用户代码"),
    git_url: str = Body(..., description="Git仓库URL"),
    branch: str = Body("main", description="分支名称"),
    job_name: Optional[str] = Body(None, description="Job名称，可选"),
    wct_api_key: Optional[str] = Body(None, description="WCT API密钥，可选"),
    user_name: Optional[str] = Body(None, description="用户姓名，可选")
):
    """创建K8s Job"""
    try:
        # 如果未传入user_name，则尝试从用户服务查询
        if not user_name:
            try:
                from api.service.user_service import select_user_info_by_code
                _u = select_user_info_by_code(user_code)
                if _u:
                    user_name = getattr(_u, 'user_name', None) or getattr(_u, 'name', None)
            except Exception:
                user_name = None
        
        # 获取配额限制
        from api.sandbox.kubernetes_service import get_kubernetes_service
        kubernetes_service = get_kubernetes_service()
        per_user_max = 0
        try:
            from api.service.user_service import get_user_sandbox_quota_by_user_code
            quota_val = get_user_sandbox_quota_by_user_code(user_code)
            if quota_val is not None:
                per_user_max = int(quota_val)
        except Exception:
            pass

        if not per_user_max or per_user_max <= 0:
            try:
                job_cfg = kubernetes_service.k8s_config.get("job", {})
                per_user_max = int(job_cfg.get("per_user_max_containers", 0))
            except Exception:
                per_user_max = 0

        max_containers = kubernetes_service.get_max_containers()

        # 在创建前使用Redis做原子自增检查
        ok_to_create = sandbox_service._atomic_increment_before_create(user_code, per_user_max, max_containers)
        if not ok_to_create:
            return {"success": False, "error": "系统繁忙或已达配额上限，请稍后再试", "code": "QUOTA_EXCEEDED"}

        try:
            job = kubernetes_service.create_job(user_code, git_url, branch, job_name, wct_api_key, user_name)
            logger.info(f"创建K8s Job成功，已更新Redis计数: {job.get('name')}, user_code={user_code}")
            return {"success": True, "data": job}
        except Exception as e:
            # 创建失败则回滚Redis自增
            sandbox_service._rollback_increment_on_create_failure(user_code)
            raise
            
    except Exception as e:
        logger.error(f"创建Job失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.post("/jobs/create/custom")
async def create_custom_k8s_job(
    job_name: str = Body(..., description="Job名称"),
    image: str = Body(..., description="容器镜像"),
    volumes: List[Dict[str, Any]] = Body(..., description="卷挂载配置列表，格式：[{\"name\": \"volume-name\", \"host_path\": \"/host/path\", \"container_path\": \"/container/path\", \"read_only\": false}]"),
    env_vars: List[Dict[str, str]] = Body(..., description="环境变量列表，格式：[{\"name\": \"VAR_NAME\", \"value\": \"var_value\"}]"),
    cpu_request: str = Body("10m", description="CPU请求资源，默认10m"),
    memory_request: str = Body("256Mi", description="内存请求资源，默认256Mi"),
    cpu_limit: str = Body("100m", description="CPU限制资源，默认100m"),
    memory_limit: str = Body("256Mi", description="内存限制资源，默认256Mi"),
    user_code: Optional[str] = Body(None, description="用户代码（可选）"),
    git_url: Optional[str] = Body(None, description="Git仓库URL（可选）"),
    branch: Optional[str] = Body(None, description="分支名称（可选）"),
    user_name: Optional[str] = Body(None, description="用户姓名（可选）"),
    namespace: Optional[str] = Body(None, description="命名空间（可选，默认使用系统配置的命名空间）")
):
    """创建自定义配置的K8s Job"""
    try:
        # 验证必需参数
        if not job_name or not image:
            return {"success": False, "error": "Job名称和容器镜像是必需参数"}
        
        # 验证卷配置
        if not isinstance(volumes, list):
            return {"success": False, "error": "volumes必须是列表格式"}
        
        for volume in volumes:
            if not isinstance(volume, dict) or not volume.get("host_path") or not volume.get("container_path"):
                return {"success": False, "error": "每个volume必须包含host_path和container_path"}
        
        # 验证环境变量
        if not isinstance(env_vars, list):
            return {"success": False, "error": "env_vars必须是列表格式"}
        
        for env_var in env_vars:
            if not isinstance(env_var, dict) or not env_var.get("name") or env_var.get("value") is None:
                return {"success": False, "error": "每个环境变量必须包含name和value"}
        
        # 验证资源规格格式
        import re
        cpu_pattern = r'^(\d+(?:\.\d+)?)(m)?$'
        memory_pattern = r'^(\d+)(Ki|Mi|Gi|Ti|K|M|G|T)?$'
        
        if not re.match(cpu_pattern, cpu_request) or not re.match(cpu_pattern, cpu_limit):
            return {"success": False, "error": "CPU资源格式无效，请使用如100m、0.1、1等格式"}
        
        if not re.match(memory_pattern, memory_request) or not re.match(memory_pattern, memory_limit):
            return {"success": False, "error": "内存资源格式无效，请使用如256Mi、1Gi、1024等格式"}
        
        # 如果未传入user_name且传入了user_code，则尝试从用户服务查询
        if not user_name and user_code:
            try:
                from api.service.user_service import select_user_info_by_code
                _u = select_user_info_by_code(user_code)
                if _u:
                    user_name = getattr(_u, 'user_name', None) or getattr(_u, 'name', None)
            except Exception:
                user_name = None
        
        from api.sandbox.kubernetes_service import get_kubernetes_service
        kubernetes_service = get_kubernetes_service()
        
        # 如果有user_code，需要进行配额检查和Redis计数管理
        if user_code:
            # 获取配额限制
            per_user_max = 0
            try:
                from api.service.user_service import get_user_sandbox_quota_by_user_code
                quota_val = get_user_sandbox_quota_by_user_code(user_code)
                if quota_val is not None:
                    per_user_max = int(quota_val)
            except Exception:
                pass

            if not per_user_max or per_user_max <= 0:
                try:
                    job_cfg = kubernetes_service.k8s_config.get("job", {})
                    per_user_max = int(job_cfg.get("per_user_max_containers", 0))
                except Exception:
                    per_user_max = 0

            max_containers = kubernetes_service.get_max_containers()

            # 在创建前使用Redis做原子自增检查
            ok_to_create = sandbox_service._atomic_increment_before_create(user_code, per_user_max, max_containers)
            if not ok_to_create:
                return {"success": False, "error": "系统繁忙或已达配额上限，请稍后再试", "code": "QUOTA_EXCEEDED"}

            try:
                job = kubernetes_service.create_custom_job(
                    job_name=job_name,
                    image=image,
                    volumes=volumes,
                    env_vars=env_vars,
                    cpu_request=cpu_request,
                    memory_request=memory_request,
                    cpu_limit=cpu_limit,
                    memory_limit=memory_limit,
                    user_code=user_code,
                    git_url=git_url,
                    branch=branch,
                    user_name=user_name,
                    namespace=namespace
                )
                logger.info(f"创建自定义Job成功，已更新Redis计数: {job.get('name')}, user_code={user_code}")
                return {"success": True, "data": job}
            except Exception as e:
                # 创建失败则回滚Redis自增
                sandbox_service._rollback_increment_on_create_failure(user_code)
                raise
        else:
            # 没有user_code的情况，直接创建，不涉及Redis计数
            job = kubernetes_service.create_custom_job(
                job_name=job_name,
                image=image,
                volumes=volumes,
                env_vars=env_vars,
                cpu_request=cpu_request,
                memory_request=memory_request,
                cpu_limit=cpu_limit,
                memory_limit=memory_limit,
                user_code=user_code,
                git_url=git_url,
                branch=branch,
                user_name=user_name,
                namespace=namespace
            )
            
            return {"success": True, "data": job}
        
    except Exception as e:
        logger.error(f"创建自定义Job失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.delete("/jobs/{job_name}")
async def delete_k8s_job(job_name: str):
    """删除K8s Job"""
    try:
        from api.sandbox.kubernetes_service import get_kubernetes_service
        kubernetes_service = get_kubernetes_service()
        
        # 在删除前先获取job信息，以便获取user_code进行Redis计数管理
        job_info = kubernetes_service.get_job(job_name)
        
        result = kubernetes_service.delete_job(job_name)
        
        return {"success": result, "message": "删除成功" if result else "删除失败"}
    except Exception as e:
        logger.error(f"删除Job失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.get("/jobs/{job_name}/pods")
async def get_job_pods(job_name: str):
    """获取Job关联的Pod信息"""
    try:
        from api.sandbox.kubernetes_service import get_kubernetes_service
        kubernetes_service = get_kubernetes_service()
        pods = kubernetes_service.get_job_pods(job_name)
        return {"success": True, "data": pods}
    except Exception as e:
        logger.error(f"获取Job Pods失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.post("/jobs/{job_name}/update-access")
async def update_job_access_time(job_name: str):
    """更新Job访问时间"""
    try:
        from api.sandbox.kubernetes_service import get_kubernetes_service
        kubernetes_service = get_kubernetes_service()
        result = kubernetes_service.update_job_access_time(job_name)
        return {"success": result, "message": "更新成功" if result else "更新失败"}
    except Exception as e:
        logger.error(f"更新Job访问时间失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.post("/jobs/cleanup")
async def cleanup_idle_jobs():
    """清理空闲Jobs"""
    try:
        from api.sandbox.kubernetes_service import get_kubernetes_service
        kubernetes_service = get_kubernetes_service()
        cleaned_jobs = kubernetes_service.cleanup_idle_jobs()
        
        # 对清理的Job进行Redis计数自减
        if cleaned_jobs:
            for j in cleaned_jobs:
                user_code = j.get('user_code')
                try:
                    sandbox_service._decrement_after_delete(user_code)
                except Exception as redis_error:
                    logger.warning(f"清理Job {j.get('name')} 成功但Redis计数更新失败: {redis_error}")
            logger.info(f"清理了 {len(cleaned_jobs)} 个空闲Job，已更新Redis计数")
        
        cleaned_count = len(cleaned_jobs or [])
        return {"success": True, "cleaned_count": cleaned_count}
    except Exception as e:
        logger.error(f"清理空闲Jobs失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.get("/jobs/{job_name}/metrics")
async def get_job_metrics(job_name: str):
    """获取Job的实时CPU和内存使用情况"""
    try:
        from api.sandbox.kubernetes_service import get_kubernetes_service
        kubernetes_service = get_kubernetes_service()
        metrics = kubernetes_service.get_job_metrics(job_name)
        if metrics:
            return {"success": True, "data": metrics}
        else:
            return {"success": False, "error": "无法获取Job metrics信息"}
    except Exception as e:
        logger.error(f"获取Job metrics失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.get("/pods/{pod_name}/metrics")
async def get_pod_metrics(pod_name: str):
    """获取Pod的实时CPU和内存使用情况"""
    try:
        from api.sandbox.kubernetes_service import get_kubernetes_service
        kubernetes_service = get_kubernetes_service()
        metrics = kubernetes_service.get_pod_metrics(pod_name)
        if metrics:
            return {"success": True, "data": metrics}
        else:
            return {"success": False, "error": "无法获取Pod metrics信息"}
    except Exception as e:
        logger.error(f"获取Pod metrics失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.get("/config")
async def get_k8s_basic_config():
    """返回K8s命名空间、环境及门户配置"""
    try:
        from api.sandbox.kubernetes_service import get_kubernetes_service
        k8s = get_kubernetes_service()
        # 统一返回命名空间、环境、主控地址与门户基础地址供前端使用
        return {
            "success": True,
            "data": {
                "namespace": k8s.namespace,
                "environment": k8s.environment,
                "api_server": k8s.api_server,
                "portal_base_url": getattr(k8s, "portal_base_url", ""),
                "enable_container_portal_button": bool(getattr(k8s, "enable_container_portal_button", False)),
                "enable_jump_server_button": bool(getattr(k8s, "enable_jump_server_button", False)),
                "jump_server_url": getattr(k8s, "jump_server_url", "")
            }
        }
    except Exception as e:
        logger.error(f"获取K8s配置失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.get("/wiki/info/pending-failed")
async def list_pending_failed_wikis():
    """查询 wiki_info 中 status 为 archived 或 deleted 的记录，返回 wiki_id、repo_url、branch、status"""
    try:
        with session_scope() as session:
            # 查询异常状态的wiki记录
            wikis = search_wikis(session, statuses=['archived', 'deleted'])
            
            # 收集所有创建者ID
            creator_ids = [wiki.created_by for wiki in wikis if wiki.created_by]
            
            # 批量查询用户信息
            users = {}
            if creator_ids:
                user_statement = select(UserInfo).where(UserInfo.id.in_(creator_ids))
                user_results = session.exec(user_statement).all()
                users = {user.id: {"user_code": user.user_code, "user_name": user.user_name} for user in user_results}
            
            # 组装返回数据
            result_data = []
            for wiki in wikis:
                wiki_data = {
                    "wiki_id": wiki.wiki_id,
                    "repo_url": wiki.repo_url,
                    "branch": wiki.branch,
                    "status": wiki.status,
                    "created_time": wiki.created_time.isoformat() if wiki.created_time else None
                }
                
                # 添加创建者信息
                if wiki.created_by and wiki.created_by in users:
                    user_info = users[wiki.created_by]
                    wiki_data["user_code"] = user_info["user_code"]
                    wiki_data["user_name"] = user_info["user_name"]
                else:
                    wiki_data["user_code"] = None
                    wiki_data["user_name"] = None
                
                result_data.append(wiki_data)
            
            return {"success": True, "data": result_data}
    except Exception as e:
        logger.error(f"查询异常Wiki失败: {e}")
        return {"success": False, "error": str(e)}

# ==================== Wiki快速生成路由 ====================

@k8s_router.post("/wiki/generate/fast")
async def generate_wiki_fast_mode(
    wiki_id: str = Body(..., description="Wiki ID"),
    comprehensive: bool = Body(True, description="是否生成详细模式"),
    model_settings: Dict[str, Any] = Body(None, description="模型设置"),
    token: Optional[str] = Body(None, description="Git访问令牌"),
    user_id: Optional[str] = Body(None, description="用户ID")
):
    """快速模式生成Wiki - 接管现有wiki job，跳过文件上传直接生成内容"""
    try:
        # 1. 从数据库获取wiki信息并提取所有需要的属性
        with session_scope() as session:
            wiki_info = get_wiki_info(session, wiki_id)
            if not wiki_info:
                return {"success": False, "error": f"未找到Wiki ID: {wiki_id}"}
            
            # 查询wiki创建者信息
            created_by = wiki_info.created_by
            
            # 在session内提取所有需要的属性值
            repo_url = wiki_info.repo_url
            repo_type = getattr(wiki_info, 'repo_type', 'github')
            branch = wiki_info.branch or "main"
            language = getattr(wiki_info, 'language', 'zh')
            provider = getattr(wiki_info, 'provider', 'google')
            model = getattr(wiki_info, 'model', 'gemini-pro')
            wiki_type = getattr(wiki_info, 'wiki_type', 1)
            project_topic_id = getattr(wiki_info, 'project_topic_id', None)
            
            # 更新状态为活跃
            update_wiki_info(session, wiki_id, status="active")
            
        # 2. 准备生成参数 - 优先使用传入的model_settings
        use_custom_key = False
        if model_settings:
            # 使用传入的model_settings，并处理API密钥
            final_model_settings = model_settings.copy()
            if "api_key" in final_model_settings and final_model_settings["api_key"]:
                # 如果传递了API key，使用AES解密
                try:
                    from api.utils.aes_utils import AESUtils
                    aes = AESUtils()
                    api_key_value = final_model_settings["api_key"]
                    # 清理可能的●符号
                    api_key_value = api_key_value.replace('●', '')
                    
                    # 使用AES解密
                    decrypted_api_key = aes.decrypt(api_key_value)
                    final_model_settings["api_key"] = decrypted_api_key
                    use_custom_key = True
                    logger.info("成功解密传递的API key")
                except Exception as e:
                    logger.warning(f"解密传递的API key失败: {e}")
                    # 解密失败时移除API key
                    final_model_settings.pop("api_key", None)
        else:
            # 使用数据库中的model_settings作为后备
            final_model_settings = {
                "provider": provider,
                "model": model,
                "api_key": "",
                "model_kwargs": {}
            }
        
        # 使用wiki创建者的信息查询API key和token（仅当前台没有传递API key时）
        if created_by and created_by > 0 and not use_custom_key:
            try:
                from api.service.user_service import select_user_ext
                from api.utils.aes_utils import AESUtils
                
                user_ext = select_user_ext(created_by)
                if user_ext and user_ext.ai_api_key:
                    # 解密API key
                    aes = AESUtils()
                    decrypted_api_key = aes.decrypt(user_ext.ai_api_key)
                    
                    # 使用wiki创建者的API key
                    final_model_settings["api_key"] = decrypted_api_key
                    logger.info(f"使用wiki创建者 {created_by} 的API key")
                else:
                    logger.info(f"wiki创建者 {created_by} 没有配置API key，使用默认设置")
                    
                # 如果用户有dev_cloud_token，也尝试使用
                if user_ext and user_ext.dev_cloud_token and not token_from_db:
                    try:
                        decrypted_token = aes.decrypt(user_ext.dev_cloud_token)
                        if not token:  # 只有在没有传入token时才使用用户的token
                            token_from_db = decrypted_token
                            logger.info(f"使用wiki创建者 {created_by} 的dev_cloud_token")
                    except Exception as token_error:
                        logger.warning(f"解密用户token失败: {token_error}")
                        
            except Exception as key_error:
                logger.warning(f"获取wiki创建者API key失败: {key_error}")
        elif final_model_settings.get("api_key"):
            logger.info("使用前台传递的API key")
        
        # 使用传入的token或数据库中的token或用户解密后的token
        final_token = token.replace('●', '') if token else (token_from_db.replace('●', '') if token_from_db else None)
        
        # 3. 创建必要的服务实例
        # 使用全局服务实例
        wiki_generator = WikiGenerator(db_service, docchain_manager)
        
        # 4. 创建job记录（用于状态跟踪）
        import uuid
        job_id = f"fast-wiki-{uuid.uuid4().hex[:8]}"
        
        # 创建job记录
        with session_scope() as session:
            job = create_job(
                db=session,
                repo_url=repo_url,
                branch=branch,
                wiki_info_id=wiki_id,
                model_settings=final_model_settings,
                created_by=created_by  # 使用wiki原有创建者
            )
            
            # 使用数据库生成的job ID
            job_id = str(job.id)
            
            # 如果传入了user_id，为创建者分配wiki管理员角色
            if user_id:
                try:
                    from api.service.wiki_info_service import add_wiki_user_role
                    add_wiki_user_role(
                        session=session,
                        user_id=int(user_id),
                        wiki_id=wiki_info.id,
                        role_id=5  # 5表示wiki授权角色
                    )
                    logger.info(f"为用户 {user_id} 分配了wiki管理员角色")
                except Exception as role_error:
                    logger.warning(f"分配wiki角色失败: {role_error}")
        
        logger.info(f"开始快速模式生成Wiki: wiki_id={wiki_id}, job_id={job_id}, repo_url={repo_url}")
        
        # 5. 立即返回响应，避免接口超时
        # 提交任务到全局线程池执行，避免阻塞事件循环
        def run_wiki_generation():
            """在线程中运行wiki生成"""
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(wiki_generator.start(
                        job_id=job_id,
                        wiki_id=wiki_id,
                        repo_url=repo_url,
                        repo_type=repo_type,
                        branch=branch,
                        token=final_token,
                        language=language,
                        model_settings=final_model_settings,
                        comprehensive=comprehensive,
                        fast_mode=True,
                        job_context=None
                    ))
                    logger.info(f"Wiki快速生成完成: wiki_id={wiki_id}, job_id={job_id}")
                finally:
                    loop.close()
            except Exception as e:
                logger.error(f"Wiki快速生成过程中发生错误: wiki_id={wiki_id}, job_id={job_id}, error={e}")
                # 同步更新状态为失败
                try:
                    with session_scope() as session:
                        update_wiki_info(session, wiki_id, status="archived", error_message=str(e))
                        update_job(session, job_id, status="archived", error_message=str(e))
                except Exception as update_error:
                    logger.warning(f"更新失败状态时出错: {update_error}")
        
        # 提交任务到线程池，不等待结果
        EXECUTOR.submit(run_wiki_generation)
        
        return {"success": True, "message": f"Wiki快速生成已启动", "job_id": job_id, "wiki_id": wiki_id}
         
    except Exception as e:
        logger.error(f"快速生成Wiki失败: {e}")
        # 更新状态为失败
        try:
            with session_scope() as session:
                update_wiki_info(session, wiki_id, status="archived", error_message=str(e))
                # 如果job_id已经创建，也更新job状态
                if 'job_id' in locals():
                    try:
                        update_job(session, job_id, status="archived", error_message=str(e))
                    except Exception as job_error:
                        logger.warning(f"更新job状态失败: {job_error}")
        except Exception as update_error:
            logger.warning(f"更新状态失败: {update_error}")
        return {"success": False, "error": str(e)}

async def update_status_async(wiki_id, job_id, error_message):
    """异步更新状态的辅助函数"""
    try:
        with session_scope() as session:
            update_wiki_info(session, wiki_id, status="archived", error_message=error_message)
            # 更新job状态
            try:
                update_job(session, job_id, status="archived", error_message=error_message)
            except Exception as job_error:
                logger.warning(f"更新job状态失败: {job_error}")
    except Exception as update_error:
        logger.warning(f"异步更新状态失败: {update_error}")

@k8s_router.post("/wiki/create-quick")
async def create_quick_wiki(
    repo_url: str = Body(..., description="仓库URL"),
    branch: str = Body("main", description="分支名称"),
    language: str = Body("zh", description="Wiki语言"),
    user_id: Optional[str] = Body(None, description="用户ID"),
    comprehensive: bool = Body(True, description="是否生成详细模式"),
    model_settings: Dict[str, Any] = Body(None, description="模型设置"),
    token: Optional[str] = Body(None, description="Git访问令牌"),
    sub_repos: Optional[List[Dict[str, str]]] = Body([], description="子仓库信息列表"),
    excluded_dirs: Optional[str] = Body("", description="排除目录"),
    excluded_files: Optional[str] = Body("", description="排除文件"),
    included_dirs: Optional[str] = Body("", description="包含目录"),
    included_files: Optional[str] = Body("", description="包含文件"),
    topic_id: Optional[str] = Body(None, description="DocChain主题ID"),
    topic_id_code: Optional[str] = Body(None, description="DocChain代码主题ID")
):
    """创建快速Wiki - 自动创建wiki_info（如果不存在），复用已存在的job"""
    try:
        # 1. 从repo_url提取仓库信息
        try:
            owner, repo_name, host = extract_repo_info(repo_url)
            if not owner or not repo_name:
                return {"success": False, "error": "无法从仓库URL中提取有效的仓库信息"}
        except Exception as e:
            logger.error(f"提取仓库信息失败: {e}")
            return {"success": False, "error": f"提取仓库信息失败: {str(e)}"}
        
        logger.info(f"开始创建快速Wiki: repo_url={repo_url}, branch={branch}, owner={owner}, repo_name={repo_name}")
        
        # 注意：新表结构不需要处理子仓库数据
        
        # 2. 检查或创建wiki_info
        with session_scope() as session:
            # 检查是否已存在wiki_info
            existing_wiki_info = get_wiki_info_by_repo_and_branch(session, repo_url, branch)

            # 清理API密钥
            if "api_key" in model_settings and model_settings["api_key"]:
                model_settings["api_key"] = model_settings["api_key"].replace('●', '')
            
            # 清理token
            clean_token = token.replace('●', '') if token else None
            
            if existing_wiki_info:
                wiki_info = existing_wiki_info
                wiki_id = wiki_info.wiki_id
                logger.info(f"使用已存在的wiki_info: wiki_id={wiki_id}")
                
                # 更新状态为处理中
                update_wiki_info(session, wiki_id, status="processing")
            else:
                # 创建新的wiki_info
                logger.info(f"创建新的wiki_info")
                
                # 准备model_settings，使用传入的参数或默认值
                if not model_settings:
                    model_settings = {
                        "provider": "whalecloud",
                        "model": "gemini-2.5-flash",
                        "api_key": "",
                        "model_kwargs": {}
                    }
                
                wiki_info = create_wiki_info(
                    session=session,
                    repo_url=repo_url,
                    branch=branch,
                    repo_owner=owner,
                    repo_name=repo_name,
                    repo_type="whaleDevCloud",  # 默认使用whaleDevCloud
                    topic_id=topic_id,
                    provider=model_settings.get("provider", "google"),
                    model=model_settings.get("model", "gemini-pro"),
                    language=language,
                    excluded_dirs=excluded_dirs,
                    excluded_files=excluded_files,
                    included_dirs=included_dirs,
                    included_files=included_files,
                    comprehensive=comprehensive,
                    created_by=int(user_id) if user_id else 0  # 使用传入的user_id
                )
                
                wiki_id = wiki_info.wiki_id
                logger.info(f"创建了新的wiki_info: wiki_id={wiki_id}")
                
                # 更新项目主题ID字段
                update_wiki_info(session, wiki_id, 
                                project_topic_id=topic_id)

            # 新增wiki角色 - 为创建者分配wiki管理员角色
            if user_id:
                try:
                    from api.service.wiki_info_service import add_wiki_user_role
                    add_wiki_user_role(
                        session=session,
                        user_id=int(user_id),
                        wiki_id=wiki_info.id,
                        role_id=5  # 5表示wiki授权角色
                    )
                    logger.info(f"为用户 {user_id} 分配了wiki管理员角色")
                except Exception as role_error:
                    logger.warning(f"分配wiki角色失败: {role_error}")
            
            # 3. 检查是否有可接管的job（除processing状态外）
            active_job = get_active_jobs_by_wiki_id(session, wiki_id)
            
            if active_job:
                # 如果有可接管job，直接返回该job信息
                logger.info(f"发现可接管job，复用现有job: job_id={active_job.id}, status={active_job.status}")
                return {
                    "success": True, 
                    "message": "找到可接管的任务，已复用现有任务", 
                    "job_id": str(active_job.id), 
                    "wiki_id": wiki_id,
                    "status": active_job.status,
                    "is_existing_job": True
                }
            else:
                # 4. 创建新的job并启动快速生成
                logger.info(f"未找到可接管job，创建新job")

                # 查询用户是否存在，并查询解密api_key
                final_model_settings = model_settings.copy() if model_settings else {
                    "provider": "whalecloud",
                    "model": "gemini-2.5-flash",
                    "api_key": "",
                    "model_kwargs": {}
                }
                
                # 如果传入了user_id，尝试获取用户的API key
                if user_id:
                    try:
                        from api.service.user_service import select_user_ext
                        from api.utils.aes_utils import AESUtils
                        
                        user_ext = select_user_ext(int(user_id))
                        if user_ext and user_ext.ai_api_key:
                            # 解密API key
                            aes = AESUtils()
                            decrypted_api_key = aes.decrypt(user_ext.ai_api_key)
                            
                            # 优先使用用户的API key
                            final_model_settings["api_key"] = decrypted_api_key
                            logger.info(f"使用用户 {user_id} 的API key")
                        else:
                            logger.info(f"用户 {user_id} 没有配置API key，使用传入的model_settings")
                            
                        # 如果用户有dev_cloud_token，也尝试使用
                        if user_ext and user_ext.dev_cloud_token:
                            try:
                                decrypted_token = aes.decrypt(user_ext.dev_cloud_token)
                                # 优先使用用户的token
                                clean_token = decrypted_token
                                logger.info(f"使用用户 {user_id} 的dev_cloud_token")
                            except Exception as token_error:
                                logger.warning(f"解密用户token失败: {token_error}")
                                # 解密失败时使用传入的token
                                clean_token = token.replace('●', '') if token else None
                        else:
                            # 用户没有token时使用传入的token
                            clean_token = token.replace('●', '') if token else None
                            
                    except Exception as key_error:
                        logger.warning(f"获取用户API key失败: {key_error}")
                        # 获取失败时使用传入的配置
                        clean_token = token.replace('●', '') if token else None
                else:
                    # 没有传入user_id时使用传入的配置
                    clean_token = token.replace('●', '') if token else None
                
                # 创建job记录
                job = create_job(
                    db=session,
                    repo_url=repo_url,
                    branch=branch,
                    wiki_info_id=wiki_id,
                    model_settings=final_model_settings,
                    created_by=int(user_id) if user_id else 0,  # 使用传入的user_id
                    excluded_dirs=excluded_dirs,
                    excluded_files=excluded_files,
                    included_dirs=included_dirs,
                    included_files=included_files
                )
                
                job_id = str(job.id)
                logger.info(f"创建了新job: job_id={job_id}")
        
        # 5. 启动快速Wiki生成
        try:
            # 创建必要的服务实例
            wiki_generator = WikiGenerator(db_service, docchain_manager)
            
            logger.info(f"启动快速模式生成Wiki: wiki_id={wiki_id}, job_id={job_id}")
            
            # 提交任务到全局线程池执行，避免阻塞事件循环
            def run_quick_wiki_generation():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        loop.run_until_complete(wiki_generator.start(
                            job_id=job_id,
                            wiki_id=wiki_id,
                            repo_url=repo_url,
                            repo_type="whaleDevCloud",
                            branch=branch,
                            token=clean_token,
                            language=language,
                            model_settings=final_model_settings,
                            comprehensive=comprehensive,
                            fast_mode=True,
                            job_context=None,
                            excluded_dirs=excluded_dirs,
                            excluded_files=excluded_files,
                            included_dirs=included_dirs,
                            included_files=included_files
                        ))
                        logger.info(f"快速Wiki生成完成: wiki_id={wiki_id}, job_id={job_id}")
                    finally:
                        loop.close()
                except Exception as e:
                    logger.error(f"快速Wiki生成过程中发生错误: wiki_id={wiki_id}, job_id={job_id}, error={e}")
                    # 同步更新状态为失败
                    try:
                        with session_scope() as session:
                            update_job(session, job_id, status="archived", error_message=str(e))
                            update_wiki_info(session, wiki_id, status="archived", error_message=str(e))
                    except Exception as update_error:
                        logger.warning(f"更新失败状态时出错: {update_error}")
            
            # 提交任务到线程池，不等待结果
            EXECUTOR.submit(run_quick_wiki_generation)
            
            return {
                "success": True, 
                "message": "快速Wiki创建已启动", 
                "job_id": job_id, 
                "wiki_id": wiki_id,
                "is_existing_job": False
            }
            
        except Exception as wiki_error:
            logger.error(f"启动Wiki生成失败: {wiki_error}")
            # 更新job和wiki状态为失败
            try:
                with session_scope() as session:
                    update_job(session, job_id, status="archived", error_message=str(wiki_error))
                    update_wiki_info(session, wiki_id, status="archived", error_message=str(wiki_error))
            except:
                pass
            return {"success": False, "error": f"启动Wiki生成失败: {str(wiki_error)}"}
         
    except Exception as e:
        logger.error(f"创建快速Wiki失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.get("/wiki/{wiki_id}/status")
async def get_wiki_status(wiki_id: str):
    """获取Wiki生成状态"""
    try:
        with session_scope() as session:
            wiki_info = get_wiki_info(session, wiki_id)
            if not wiki_info:
                return {"success": False, "error": f"未找到Wiki ID: {wiki_id}"}
            
            result = {
                "wiki_id": wiki_id,
                "status": wiki_info.status,
                "repo_url": wiki_info.repo_url,
                "branch": wiki_info.branch,
                "language": getattr(wiki_info, 'language', 'zh'),
                "error_message": getattr(wiki_info, 'error_message', None),
                "created_time": wiki_info.created_time.isoformat() if hasattr(wiki_info, 'created_time') and wiki_info.created_time else None,
                "updated_time": wiki_info.updated_time.isoformat() if hasattr(wiki_info, 'updated_time') and wiki_info.updated_time else None,
            }
            
            # 添加wiki类型信息
            result["wiki_type"] = getattr(wiki_info, 'wiki_type', 1)
            result["project_topic_id"] = getattr(wiki_info, 'project_topic_id', None)
            
            return {"success": True, "data": result}
            
    except Exception as e:
        logger.error(f"获取Wiki状态失败: {e}")
        return {"success": False, "error": str(e)} 

@k8s_router.get("/models/config")
async def get_models_config():
    """获取模型配置信息"""
    try:
        from api.config import load_generator_config
        
        config = load_generator_config()
        
        # 提取whalecloud的模型信息
        whalecloud_config = config.get("providers", {}).get("whalecloud", {})
        models = whalecloud_config.get("models", {})
        
        # 转换为前端需要的格式
        model_list = []
        for model_name, model_config in models.items():
            model_list.append({
                "id": model_name,
                "name": model_name,
                "temperature": model_config.get("temperature", 0.4),
                "top_p": model_config.get("top_p", 0.8)
            })
        
        return {
            "success": True,
            "data": {
                "provider": "whalecloud",
                "default_model": whalecloud_config.get("default_model", "gemini-2.5-flash"),
                "models": model_list
            }
        }
    except Exception as e:
        logger.error(f"获取模型配置失败: {e}")
        return {"success": False, "error": str(e)} 

@k8s_router.get("/users/search")
async def search_users(
    search: Optional[str] = Query(None, description="搜索关键字（用户姓名或工号）"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量")
):
    """搜索用户列表（支持分页和模糊搜索）"""
    try:
        with session_scope() as session:
            from sqlalchemy import or_
            
            # 构建查询条件
            query = session.query(UserInfo)
            
            # 添加搜索条件
            if search and search.strip():
                search_term = f"%{search.strip()}%"
                query = query.filter(
                    or_(
                        UserInfo.user_name.like(search_term),
                        UserInfo.user_code.like(search_term)
                    )
                )
            
            # 计算总数
            total = query.count()
            
            # 分页
            offset = (page - 1) * page_size
            users = query.offset(offset).limit(page_size).all()
            
            # 转换为字典格式
            user_list = []
            for user in users:
                user_list.append({
                    "id": user.id,
                    "user_code": user.user_code,
                    "user_name": user.user_name,
                    "email": user.email,
                    "dept": user.dept,
                    "org": user.org
                })
            
            return {
                "success": True,
                "data": {
                    "users": user_list,
                    "total": total,
                    "page": page,
                    "page_size": page_size,
                    "total_pages": (total + page_size - 1) // page_size
                }
            }
    except Exception as e:
        logger.error(f"搜索用户失败: {e}")
        return {"success": False, "error": str(e)} 

# ==================== Wiki信息管理路由 ====================

@k8s_router.get("/wiki/info/list")
async def list_wiki_info(
    keyword: Optional[str] = Query(None, description="关键字(仓库名称、所有者或URL)"),
    status: Optional[str] = Query(None, description="状态筛选"),
    repo_type: Optional[str] = Query(None, description="仓库类型筛选"),
    language: Optional[str] = Query(None, description="语言筛选"),
    created_by: Optional[int] = Query(None, description="创建人ID筛选"),
    job_status: Optional[str] = Query(None, description="按关联Job状态过滤(running|failed|cancelled|pending)"),
    has_wiki_data: Optional[bool] = Query(None, description="是否有wiki_data数据"),
    page: Optional[int] = Query(1, ge=1),
    page_size: Optional[int] = Query(20, ge=1, le=500)
):
    """列出Wiki信息记录（支持查询与分页）。"""
    try:
        with session_scope() as session:
            from sqlalchemy import or_, and_, func
            from api.model.wiki_job import WikiJob
            from sqlalchemy.orm import defer
        
            # 使用更高效的查询结构
            id_statement = select(WikiInfo.id).order_by(WikiInfo.id.desc())
            
            # 添加搜索条件（优化LIKE查询）
            if keyword and keyword.strip():
                search_term = f"%{keyword.strip()}%"
                id_statement = id_statement.where(
                    or_(
                        WikiInfo.repo_name.like(search_term),
                        WikiInfo.repo_owner.like(search_term),
                        WikiInfo.repo_url.like(search_term)
                    )
                )
            
            # 添加状态筛选
            if status:
                id_statement = id_statement.where(WikiInfo.status == status)
            
            # 添加仓库类型筛选
            if repo_type:
                id_statement = id_statement.where(WikiInfo.repo_type == repo_type)
            
            # 添加语言筛选
            if language:
                id_statement = id_statement.where(WikiInfo.language == language)
            
            # 添加创建人筛选
            if created_by is not None:
                id_statement = id_statement.where(WikiInfo.created_by == created_by)
            
            # 注意：新表结构中没有wiki_data字段，has_wiki_data参数将被忽略
            
            # 关联Job状态筛选
            target_statuses = None
            job_wiki_ids = None
            if job_status:
                status_map = {
                    "running": ["active"],
                    "failed": ["archived"],
                    "cancelled": ["deleted"],
                    "pending": ["active"]
                }
                target_statuses = status_map.get(job_status.lower())
                if target_statuses:
                    # 优化：直接查询wiki_info_id而不是整个job对象
                    job_wiki_ids = session.exec(
                        select(WikiJob.wiki_info_id).where(WikiJob.status.in_(target_statuses))
                    ).all()
                    if not job_wiki_ids:
                        return {
                            "success": True,
                            "data": {
                                "wikis": [],
                                "total": 0,
                                "page": page,
                                "page_size": page_size,
                                "total_pages": 0
                            }
                        }
                    id_statement = id_statement.where(WikiInfo.wiki_id.in_(job_wiki_ids))
            
            # 计算总数 - 使用count查询而不是查询所有记录
            count_statement = select(func.count(WikiInfo.id))
            
            # 复制所有筛选条件到count查询
            if keyword and keyword.strip():
                search_term = f"%{keyword.strip()}%"
                count_statement = count_statement.where(
                    or_(
                        WikiInfo.repo_name.like(search_term),
                        WikiInfo.repo_owner.like(search_term),
                        WikiInfo.repo_url.like(search_term)
                    )
                )
            if status:
                count_statement = count_statement.where(WikiInfo.status == status)
            if repo_type:
                count_statement = count_statement.where(WikiInfo.repo_type == repo_type)
            if language:
                count_statement = count_statement.where(WikiInfo.language == language)
            if created_by is not None:
                count_statement = count_statement.where(WikiInfo.created_by == created_by)
            # 注意：新表结构中没有wiki_data字段，has_wiki_data参数将被忽略
            if job_status and target_statuses:
                count_statement = count_statement.where(WikiInfo.wiki_id.in_(job_wiki_ids))
            
            total_result = session.exec(count_statement).first()
            total = total_result if total_result is not None else 0
            
            # 第二步：分页获取ID列表
            offset = (page - 1) * page_size
            id_statement = id_statement.offset(offset).limit(page_size)
            
            id_rows = session.exec(id_statement).all()
            
            if not id_rows:
                return {
                    "success": True,
                    "data": {
                        "wikis": [],
                        "total": total,
                        "page": page,
                        "page_size": page_size,
                        "total_pages": (total + page_size - 1) // page_size
                    }
                }
            
            # 第三步：根据ID列表获取完整信息
            wiki_ids = [row for row in id_rows]  # row本身就是id值
            
            statement = (
                select(
                    WikiInfo.id,
                    WikiInfo.wiki_id,
                    WikiInfo.repo_url,
                    WikiInfo.branch,
                    WikiInfo.repo_owner,
                    WikiInfo.repo_name,
                    WikiInfo.repo_type,
                    WikiInfo.project_topic_id,
                    WikiInfo.provider,
                    WikiInfo.model,
                    WikiInfo.language,
                    WikiInfo.excluded_dirs,
                    WikiInfo.excluded_files,
                    WikiInfo.included_dirs,
                    WikiInfo.included_files,
                    WikiInfo.comprehensive,
                    WikiInfo.created_time,
                    WikiInfo.updated_time,
                    WikiInfo.created_by,
                    WikiInfo.updated_by,
                    WikiInfo.status,
                    WikiInfo.visibility,
                    WikiInfo.description,
                    WikiInfo.owner_id,
                    WikiInfo.wiki_type.label("wiki_type")
                )
                .where(WikiInfo.id.in_(wiki_ids))
                .order_by(WikiInfo.id.desc())
            )
            
            rows = session.exec(statement).all()
            
            # 批量查询用户信息
            user_ids = list(set([row.created_by for row in rows if row.created_by]))
            users = {}
            if user_ids:
                user_statement = select(UserInfo).where(UserInfo.id.in_(user_ids))
                user_results = session.exec(user_statement).all()
                users = {user.id: {"user_code": user.user_code, "user_name": user.user_name} for user in user_results}
            
            # 组装返回数据
            result_data = []
            for row in rows:
                wiki_data = {
                    "id": row.id,
                    "wiki_id": row.wiki_id,
                    "repo_url": row.repo_url,
                    "branch": row.branch,
                    "repo_owner": row.repo_owner,
                    "repo_name": row.repo_name,
                    "repo_type": row.repo_type,
                    "provider": row.provider,
                    "model": row.model,
                    "language": row.language,
                    "comprehensive": row.comprehensive,
                    "status": row.status,
                    "created_time": row.created_time.isoformat() if row.created_time else None,
                    "updated_time": row.updated_time.isoformat() if row.updated_time else None,
                    "created_by": row.created_by,
                    "updated_by": row.updated_by,
                    "wiki_type": row.wiki_type  # 新表结构中的wiki类型
                }
                
                # 添加创建者信息
                if row.created_by and row.created_by in users:
                    user_info = users[row.created_by]
                    wiki_data["user_code"] = user_info["user_code"]
                    wiki_data["user_name"] = user_info["user_name"]
                else:
                    wiki_data["user_code"] = None
                    wiki_data["user_name"] = None
                
                result_data.append(wiki_data)
            
            return {
                "success": True,
                "data": {
                    "wikis": result_data,
                    "total": total,
                    "page": page,
                    "page_size": page_size,
                    "total_pages": (total + page_size - 1) // page_size
                }
            }
    except Exception as e:
        logger.error(f"列出Wiki信息失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.post("/wiki/info/{wiki_id}/status")
async def update_wiki_status(
    wiki_id: str,
    status: str = Body(..., description="新的状态"),
    error_message: Optional[str] = Body(None, description="错误信息（可选）")
):
    """更新Wiki状态"""
    try:
        with session_scope() as session:
            wiki = session.exec(select(WikiInfo).where(WikiInfo.wiki_id == wiki_id)).first()
            if not wiki:
                return {"success": False, "error": "Wiki不存在"}
            
            # 验证状态值
            valid_statuses = ["pending", "processing", "completed", "failed", "cancelled"]
            if status not in valid_statuses:
                return {"success": False, "error": f"无效的状态值，必须是以下之一: {', '.join(valid_statuses)}"}
            
            # 更新状态
            wiki.status = status
            if error_message is not None:
                wiki.error_message = error_message
            wiki.updated_time = datetime.datetime.now()
            
            session.commit()
            
            logger.info(f"Wiki状态更新成功: {wiki_id} -> {status}")
            return {"success": True, "message": f"状态已更新为 {status}"}
            
    except Exception as e:
        logger.error(f"更新Wiki状态失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.post("/wiki/jobs/{wiki_id}/prepare")
async def prepare_wiki_job(
    wiki_id: str,
):
    from api.service.git_repository_service import get_main_repository_by_wiki_id
    from api.sandbox.kubernetes_service import get_kubernetes_service
    wiki_basic_info = get_wiki_basic_info(wiki_id=wiki_id)
    if not wiki_basic_info:
        return {"success": False, "error": "获取Wiki基本信息失败"}
    owner = wiki_basic_info.get("repo_owner")
    repo_name = wiki_basic_info.get("repo_name")
    branch = wiki_basic_info.get("branch")
    # branch_clean = branch.replace('/', '-').replace(' ', '-')

    wiki_repo = get_main_repository_by_wiki_id(wiki_id=wiki_id)
    if not wiki_repo:
        return {"success": False, "error": "获取主仓库失败"}
    wiki_group_name = wiki_repo.linux_group_name
    wiki_group_id = wiki_repo.linux_gid

    if not wiki_group_name or not wiki_group_id:
        wiki_group_id = get_wiki_group_id()
        wiki_group_name = get_kubernetes_service().generate_linux_group_name(id=wiki_group_id - 40000)
    from api.service.user_group_bind_service import get_user_group_bind_service
    linux_code_file_perm, linux_pw_file_perm = get_user_group_bind_service().setup_code_and_project_workspace_permissions(
        owner=owner,
        repo_name=repo_name,
        branch_clean=branch_clean,
        wiki_group_name=wiki_group_name,
        wiki_group_gid=wiki_group_id,
        wiki_repo=wiki_repo
    )
    if not linux_code_file_perm and not linux_pw_file_perm:
        return {"success": False, "error": "准备Wiki Job失败"}

    with session_scope() as session:
        from api.service.git_repository_service import update_repository
        update_repository(
            repo_id=wiki_repo.id,
            linux_code_file_perm=linux_code_file_perm,
            linux_pw_file_perm=linux_pw_file_perm,
            linux_group_name=wiki_group_name,
            linux_gid=wiki_group_id,
            session=session
        )
        session.commit()
    return {"success": True, "message": "准备Wiki Job成功"}

@k8s_router.get("/wiki/jobs/{wiki_id}")
async def get_wiki_jobs(
    wiki_id: str,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=200, description="每页数量")
):
    """获取指定Wiki的Job记录（分页，避免加载大字段）。"""
    try:
        with session_scope() as session:
            from api.model.wiki_job import WikiJob
            from sqlalchemy.orm import defer
            
            # 仅加载必要字段，延迟加载大字段，使用索引友好的排序
            statement = (
                select(WikiJob)
                .where(WikiJob.wiki_info_id == wiki_id)
                .order_by(WikiJob.created_time.desc())
                .options(
                    # 这些字段可能较大，延迟加载以降低排序内存与传输压力
                    defer(WikiJob.token),
                    defer(WikiJob.model_settings),
                    defer(WikiJob.result),
                    defer(WikiJob.sub_repos),
                    defer(WikiJob.excluded_dirs),
                    defer(WikiJob.excluded_files),
                    defer(WikiJob.included_dirs),
                    defer(WikiJob.included_files),
                    defer(WikiJob.topic_id_doc),
                )
            )

            # 分页
            offset = (page - 1) * page_size
            statement = statement.offset(offset).limit(page_size)

            jobs = session.exec(statement).all()
            
            # 转换为字典格式（仅返回必要字段）
            job_list = []
            for job in jobs:
                job_data = {
                    "id": job.id,
                    "status": job.status,
                    "job_type": int(job.job_type) if job.job_type is not None else 0,
                    "stage": job.stage,
                    "stage_progress": job.stage_progress,
                    "stage_message": job.stage_message,
                    "progress": job.progress,
                    "total_files": job.total_files,
                    "processed_files": job.processed_files,
                    "error_message": job.error_message,
                    "repo_url": job.repo_url,
                    "branch": job.branch,
                    "language": job.language,
                    "comprehensive": job.comprehensive,
                    "created_time": job.created_time.isoformat() if job.created_time else None,
                    "updated_time": job.updated_time.isoformat() if job.updated_time else None,
                    "created_by": job.created_by,
                    "updated_by": job.updated_by
                }
                job_list.append(job_data)
            
            return {
                "success": True,
                "data": {
                    "jobs": job_list,
                    "page": page,
                    "page_size": page_size
                }
            }
    except Exception as e:
        logger.error(f"获取Wiki Jobs失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.delete("/wiki/jobs/{job_id}")
async def delete_wiki_job(job_id: str):
    """删除指定的Wiki Job"""
    try:
        with session_scope() as session:
            from api.model.wiki_job import WikiJob
            
            job = session.exec(select(WikiJob).where(WikiJob.id == job_id)).first()
            if not job:
                return {"success": False, "error": "Job不存在"}
            
            session.delete(job)
            session.commit()
            
            return {"success": True, "message": "Job删除成功"}
    except Exception as e:
        logger.error(f"删除Wiki Job失败: {e}")
        return {"success": False, "error": str(e)}

@k8s_router.delete("/wiki/info/{wiki_id}")
async def delete_wiki_info(wiki_id: str):
    """删除指定的Wiki信息记录"""
    try:
        with session_scope() as session:
            wiki = session.exec(select(WikiInfo).where(WikiInfo.wiki_id == wiki_id)).first()
            if not wiki:
                return {"success": False, "error": "Wiki不存在"}
            
            # 先删除关联的jobs
            from api.model.wiki_job import WikiJob
            jobs = session.exec(select(WikiJob).where(WikiJob.wiki_info_id == wiki_id)).all()
            for job in jobs:
                session.delete(job)
            
            # 删除wiki信息
            session.delete(wiki)
            session.commit()
            
            return {"success": True, "message": "Wiki删除成功"}
    except Exception as e:
        logger.error(f"删除Wiki信息失败: {e}")
        return {"success": False, "error": str(e)}
