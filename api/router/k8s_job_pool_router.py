import logging
from fastapi import APIRouter, Request, Depends
from api.middleware.auth_middleware import get_current_user
from api.sandbox.kubernetes_service import get_kubernetes_service
from api.logging_config import setup_logging

k8s_job_pool_router = APIRouter(prefix="/api/k8s/job/pool", tags=["k8s_job_pool"])

setup_logging()
logger = logging.getLogger(__name__)

@k8s_job_pool_router.post("/batch/jobs", operation_id="batch_jobs")
async def batch_jobs(request: Request, current_user: dict = Depends(get_current_user)):
    """
    批量创建k8s job池
    """
    from api.service.job_pool_service import get_job_pool_service
    from api.config import get_kubernetes_config
    
    data = await request.json()
    job_number = data.get("job_number")
    wct_api_key = data.get("wct_api_key")
    start_id = data.get("start_id", 1)  # 起始ID，默认从1开始
    
    if not job_number:
        return {"success": False, "message": "job_number不能为空"}
    
    job_number = int(job_number)
    start_id = int(start_id)
    
    # 获取环境标识
    k8s_config = get_kubernetes_config()
    environment = k8s_config.get("environment", "dev")
    
    # 调用job池服务批量创建
    job_pool_service = get_job_pool_service()
    success, message, extra_data = job_pool_service.batch_create_jobs(
        job_number=job_number,
        wct_api_key=wct_api_key,
        environment=environment,
        start_id=start_id
    )
    
    return {
        "success": success,
        "message": message,
        **extra_data
    }

@k8s_job_pool_router.post("/bind", operation_id="bind_linux_group")
async def bind_job(request: Request, current_user: dict = Depends(get_current_user)):
    """
    为用户分配job（从job池中分配）
    """
    from api.service.job_pool_service import get_job_pool_service
    
    data = await request.json()
    wiki_id = data.get("wiki_id")
    user_id = current_user.get("id")
    user_code = current_user.get("user_code")
    
    if not wiki_id:
        return {"success": False, "message": "wiki_id不能为空"}
    
    # 从job池中为用户分配job
    job_pool_service = get_job_pool_service()
    success, job_name, error_msg = job_pool_service.allocate_job(
        user_id=user_id,
        wiki_id=wiki_id,
        user_code=user_code
    )
    
    if success:
        return {
            "success": True,
            "message": "Job分配成功",
            "job_name": job_name,
            "user_id": user_id,
            "wiki_id": wiki_id
        }
    else:
        return {
            "success": False,
            "message": error_msg or "Job分配失败"
        }

@k8s_job_pool_router.get("/status", operation_id="get_pool_status")
async def get_pool_status(request: Request, current_user: dict = Depends(get_current_user)):
    """
    获取job池状态
    """
    from api.service.job_pool_service import get_job_pool_service
    
    job_pool_service = get_job_pool_service()
    status = job_pool_service.get_pool_status()
    
    return {
        "success": True,
        "data": status
    }

@k8s_job_pool_router.post("/release", operation_id="release_job")
async def release_job(request: Request, current_user: dict = Depends(get_current_user)):
    """
    释放用户的job，将其放回可用池
    """
    from api.service.job_pool_service import get_job_pool_service
    
    data = await request.json()
    wiki_id = data.get("wiki_id")
    user_id = current_user.get("id")
    
    if not wiki_id:
        return {"success": False, "message": "wiki_id不能为空"}
    
    # 释放job
    job_pool_service = get_job_pool_service()
    success, error_msg = job_pool_service.release_job(
        user_id=user_id,
        wiki_id=wiki_id
    )
    
    if success:
        # 释放成功后，redis的个人沙盒配额计数减1
        from api.sandbox.sandbox_service import sandbox_service
        sandbox_service.decrement_user_quota(current_user.get("user_code"))
        return {
            "success": True,
            "message": "Job释放成功"
        }
    else:
        return {
            "success": False,
            "message": error_msg or "Job释放失败"
        }

@k8s_job_pool_router.get("/user/job", operation_id="get_user_job")
async def get_user_job(wiki_id: int, request: Request, current_user: dict = Depends(get_current_user)):
    """
    获取用户已分配的job
    """
    from api.service.job_pool_service import get_job_pool_service
    
    user_id = current_user.get("id")
    
    job_pool_service = get_job_pool_service()
    job_name = job_pool_service.get_user_job(
        user_id=user_id,
        wiki_id=wiki_id
    )
    
    if job_name:
        return {
            "success": True,
            "job_name": job_name,
            "user_id": user_id,
            "wiki_id": wiki_id
        }
    else:
        return {
            "success": False,
            "message": "未分配job"
        }

@k8s_job_pool_router.get("/jobs/{job_name}", operation_id="get_jobs")
async def get_jobs(job_name: str, request: Request, current_user: dict = Depends(get_current_user)):
    """
    获取k8s job详细状态
    """
    from api.sandbox.sandbox_service import sandbox_service
    try:
        # 使用get_sandbox_detailed_status获取详细状态
        detailed_status = await sandbox_service.get_sandbox_detailed_status(
            user_code=current_user.get("user_code"),
            git_url=current_user.get("git_url"),
            branch=current_user.get("branch"),
            job_name=job_name
        )
        job_info = get_kubernetes_service().get_job(job_name=job_name)
        
    except Exception as e:
        logger.error(f"获取k8s job详细状态失败: {e}")
        return {"success": False, "error": str(e)}
    return {"success": True, "data": detailed_status}
