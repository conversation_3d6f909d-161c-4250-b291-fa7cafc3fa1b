# 创建路由器
from datetime import datetime
from typing import Optional, List
import uuid
from pydantic import BaseModel
from fastapi import APIRouter, Depends, HTTPException
from api.utils.aes_utils import AESUtils
from api.database.base import session_scope
from api.middleware.auth_middleware import get_current_user
from api.model.user_access_token import UserAccessToken
from api.service.user_access_token_service import add_user_access_token, get_user_access_token, disable_user_access_token, delete_user_access_token, enable_user_access_token, get_accurate_user_access_token
from api.middleware.audit_log_middleware import audit_logger
from api.middleware.language_middleware import get_translation


user_access_token_router = APIRouter(prefix="/api/user_token", tags=["user_access_token"])

class UserAccessTokenCreateRequest(BaseModel):
    name: str
    use_type: Optional[str]
    effective_at: Optional[datetime]
    expires_at: Optional[datetime]

class UserAccessTokenCreateResponse(BaseModel):
    user_access_token: UserAccessToken

aes = AESUtils()

@user_access_token_router.post("")
async def create_user_access_token(user_access_token: UserAccessTokenCreateRequest, current_user=Depends(get_current_user), audit_log=Depends(audit_logger)):
    if user_access_token.effective_at and user_access_token.expires_at and user_access_token.effective_at >= user_access_token.expires_at:
        raise HTTPException(status_code=400, detail=get_translation("user_token.errors.effectiveAtMustBeBeforeExpiresAt"))
    with session_scope() as session:
        user_access_token_db = get_accurate_user_access_token(session, current_user.get("id"), user_access_token.name)
        if user_access_token_db:
            raise HTTPException(status_code=400, detail=get_translation("user_token.errors.userAccessTokenAlreadyExists"))
        user_access_token = add_user_access_token(session, current_user.get("id"), user_access_token.name, aes.encrypt_fixed(str(uuid.uuid4())), user_access_token.use_type, user_access_token.effective_at, user_access_token.expires_at, current_user.get("id"), datetime.now(), current_user.get("id"), datetime.now(), None)
        audit_log(code="USER_TOKEN_CREATE", party_type="服务", party_id=user_access_token.id, \
        party_name=user_access_token.name)
        return aes.decrypt(user_access_token.token)

@user_access_token_router.get("")
async def get_user_access_tokens(name: Optional[str] = None, current_user=Depends(get_current_user)):
    with session_scope() as session:
        user_access_tokens = get_user_access_token(session, current_user.get("id"), name)
        return [{"id": user_access_token.id, "name": user_access_token.name, "effective_at": user_access_token.effective_at, "expires_at": user_access_token.expires_at, "use_type": user_access_token.use_type, "last_used_time": user_access_token.last_used_time, "created_date": user_access_token.created_date, "created_by": user_access_token.created_by, "update_by": user_access_token.update_by, "update_date": user_access_token.update_date} for user_access_token in user_access_tokens]

@user_access_token_router.delete("/{id}")
async def delete_access_token(id: int, current_user=Depends(get_current_user), audit_log=Depends(audit_logger)):
    with session_scope() as session:
        user_access_token_db = get_accurate_user_access_token(session, current_user.get("id"), None)
        if not user_access_token_db:
            raise HTTPException(status_code=400, detail=get_translation("user_token.errors.userAccessTokenNotFound"))
        user_access_token = delete_user_access_token(session, id, current_user.get("id"))
        audit_log(code="USER_TOKEN_DELETE", party_type="服务", party_id=user_access_token.id, \
        party_name=user_access_token.name)

@user_access_token_router.put("/disable/{id}")
async def disable_access_token(id: int, current_user=Depends(get_current_user), audit_log=Depends(audit_logger)):
    with session_scope() as session:
        user_access_token_db = get_accurate_user_access_token(session, current_user.get("id"), None)
        if not user_access_token_db:
            raise HTTPException(status_code=400, detail=get_translation("user_token.errors.userAccessTokenNotFound"))
        user_access_token = disable_user_access_token(session, id, current_user.get("id"))
        audit_log(code="USER_TOKEN_DISABLE", party_type="服务", party_id=user_access_token.id, \
        party_name=user_access_token.name)

@user_access_token_router.put("/enable/{id}")
async def enable_access_token(id: int, current_user=Depends(get_current_user), audit_log=Depends(audit_logger)):
    with session_scope() as session:
        user_access_token_db = get_accurate_user_access_token(session, current_user.get("id"), None)
        if not user_access_token_db:
            raise HTTPException(status_code=400, detail=get_translation("user_token.errors.userAccessTokenNotFound"))
        user_access_token = enable_user_access_token(session, id, current_user.get("id"))
        audit_log(code="USER_TOKEN_ENABLE", party_type="服务", party_id=user_access_token.id, \
        party_name=user_access_token.name)