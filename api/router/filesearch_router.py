"""
文件搜索API路由
"""

from fastapi import APIRouter, HTTPException, Depends, Query, UploadFile, File
from fastapi.responses import StreamingResponse
from typing import List, Dict, Optional, Any
from pydantic import BaseModel
import logging
import os
import urllib.parse
from pathlib import Path

from api.utils.git_utils import extract_repo_info
from api.filesearch.file_search_service import get_file_search_service, FileSearchService
from api.filesearch.file_manager_service import get_file_manager_service, FileManagerService
from api.middleware.auth_middleware import get_current_user
from api.middleware.audit_log_middleware import audit_logger
from api.database.base import session_scope
from api.service.wiki_info_service import get_wiki_info_by_repo_and_branch, get_wiki_info_by_id
from api.service.priv_checker import can_upload_project_docs, check_is_super_admin
from api.service.chat_session_service import select_chat_session_by_chat_sid
import mimetypes

logger = logging.getLogger(__name__)

filesearch_router = APIRouter(prefix="/api/file", tags=["filesearch"])


# 请求体模型
class DownloadRequest(BaseModel):
    repo_url: str
    branch: str = "main"
    user_code: str
    file_path: str


class DeleteRequest(BaseModel):
    repo_url: str
    branch: str = "main"
    user_code: str
    item_path: str


class CreateDirectoryRequest(BaseModel):
    repo_url: str
    branch: str = "main"
    user_code: str
    path: str
    name: str


class DownloadDirectoryRequest(BaseModel):
    repo_url: str
    branch: str = "main"
    user_code: str
    dir_path: str


def get_current_user_optional():
    """可选的用户认证，允许匿名访问"""
    try:
        return get_current_user()
    except:
        return None


def encode_filename_for_download(filename: str) -> str:
    """
    为下载响应正确编码文件名，支持中文字符

    Args:
        filename: 原始文件名

    Returns:
        编码后的Content-Disposition值
    """
    try:
        # 尝试使用ASCII编码（对于纯英文文件名）
        filename.encode('ascii')
        return f'attachment; filename="{filename}"'
    except UnicodeEncodeError:
        # 包含非ASCII字符，使用RFC 5987标准
        encoded_filename = urllib.parse.quote(filename, safe='')
        return f"attachment; filename*=UTF-8''{encoded_filename}"


@filesearch_router.get("/search")
async def search_files(
    repo_url: str = Query(..., description="仓库URL"),
    branch: str = Query(default="main", description="分支名称"),
    pattern: str = Query(default="", description="搜索模式"),
    user_code: str = Query(..., description="用户代码"),
    current_user=Depends(get_current_user_optional),
    file_search_service: FileSearchService = Depends(get_file_search_service)
) -> Dict[str, Any]:
    """
    搜索文件和目录

    Args:
        repo_url: 仓库URL
        branch: 分支名称
        pattern: 搜索模式（可选）
        max_results: 最大结果数
        user_code: 用户代码
        current_user: 当前用户（可选）
        file_search_service: 文件搜索服务

    Returns:
        搜索结果
    """
    try:
        # 解析仓库信息
        try:
            owner, repo_name, _ = extract_repo_info(repo_url)
        except Exception as e:
            logger.error(f"解析仓库URL失败: {repo_url}, 错误: {e}")
            raise HTTPException(status_code=400, detail=f"无效的仓库URL: {repo_url}")

        # 执行文件搜索
        results = file_search_service.search_files(
            owner=owner,
            repo_name=repo_name,
            branch=branch,
            user_code=user_code,
            pattern=pattern
        )

        return {
            "success": True,
            "data": results,
            "total": len(results),
            "pattern": pattern,
            "repo_info": {
                "owner": owner,
                "repo_name": repo_name,
                "branch": branch,
                "user_code": user_code
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"文件搜索失败: {str(e)}")


@filesearch_router.get("/read")
async def read_file(
    repo_url: str = Query(..., description="仓库URL"),
    branch: str = Query(default="main", description="分支名称"),
    virtual_path: str = Query(..., description="虚拟文件路径"),
    user_code: str = Query(..., description="用户代码"),
    current_user=Depends(get_current_user_optional),
    file_manager_service: FileManagerService = Depends(get_file_manager_service)
) -> Dict[str, Any]:
    """
    读取文件内容

    Args:
        repo_url: 仓库URL
        branch: 分支名称
        virtual_path: 虚拟文件路径
        user_code: 用户代码
        current_user: 当前用户（可选）
        file_search_service: 文件搜索服务

    Returns:
        文件内容
    """
    try:
        # 解析仓库信息
        try:
            owner, repo_name, _ = extract_repo_info(repo_url)
        except Exception as e:
            logger.error(f"解析仓库URL失败: {repo_url}, 错误: {e}")
            raise HTTPException(status_code=400, detail=f"无效的仓库URL: {repo_url}")

        # 统一通过文件管理服务读取，兼容 /data/workspace 与 /code|/docs 路径
        content, file_info = file_manager_service.read_sandbox_file(
            owner=owner,
            repo_name=repo_name,
            branch=branch,
            user_code=user_code,
            virtual_file_path=virtual_path
        )

        if content is None:
            error_msg = file_info.get("error", "读取失败") if file_info else "读取失败"
            raise HTTPException(status_code=400, detail=error_msg)

        return {
            "success": True,
            "data": {
                "virtual_path": virtual_path,
                "real_path": file_info.get("name", ""),
                "content": content,
                "size": file_info.get("content_length", len(content.encode('utf-8'))),
                "encoding": file_info.get("encoding"),
                "is_truncated": file_info.get("is_truncated", False),
                "mime_type": file_info.get("mime_type", "application/octet-stream")
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"读取文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"读取文件失败: {str(e)}")


@filesearch_router.get("/path-mappings")
async def get_path_mappings(
    repo_url: str = Query(..., description="仓库URL"),
    branch: str = Query(default="main", description="分支名称"),
    user_code: str = Query(..., description="用户代码"),
    current_user=Depends(get_current_user_optional),
    file_search_service: FileSearchService = Depends(get_file_search_service)
) -> Dict[str, Any]:
    """
    获取路径映射信息

    Args:
        repo_url: 仓库URL
        branch: 分支名称
        user_code: 用户代码
        current_user: 当前用户（可选）
        file_search_service: 文件搜索服务

    Returns:
        路径映射信息
    """
    try:
        # 解析仓库信息
        try:
            owner, repo_name, _ = extract_repo_info(repo_url)
        except Exception as e:
            logger.error(f"解析仓库URL失败: {repo_url}, 错误: {e}")
            raise HTTPException(status_code=400, detail=f"无效的仓库URL: {repo_url}")

        # 获取路径映射
        path_mappings = file_search_service.get_project_paths(owner, repo_name, branch, user_code)

        return {
            "success": True,
            "data": path_mappings,
            "repo_info": {
                "owner": owner,
                "repo_name": repo_name,
                "branch": branch,
                "user_code": user_code
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取路径映射失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取路径映射失败: {str(e)}")


# ==================== 文件管理相关接口 ====================

@filesearch_router.get("/directory")
async def list_directory(
    repo_url: str = Query(..., description="仓库URL"),
    branch: str = Query(default="main", description="分支名称"),
    user_code: str = Query(..., description="用户代码"),
    path: str = Query(default="", description="目录路径"),
    current_user=Depends(get_current_user_optional),
    file_manager_service: FileManagerService = Depends(get_file_manager_service)
) -> Dict[str, Any]:
    """
    列出目录内容

    Args:
        repo_url: 仓库URL
        branch: 分支名称
        user_code: 用户代码
        path: 目录路径（相对于可访问根目录）
        current_user: 当前用户（可选）
        file_manager_service: 文件管理服务

    Returns:
        目录内容列表
    """
    try:
        # 解析仓库信息
        try:
            owner, repo_name, _ = extract_repo_info(repo_url)
        except Exception as e:
            logger.error(f"解析仓库URL失败: {repo_url}, 错误: {e}")
            raise HTTPException(status_code=400, detail=f"无效的仓库URL: {repo_url}")

        # 列出目录内容
        result = file_manager_service.list_directory(
            owner=owner,
            repo_name=repo_name,
            branch=branch,
            user_code=user_code,
            virtual_path=path
        )

        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])

        return {
            "success": True,
            "data": result,
            "repo_info": {
                "owner": owner,
                "repo_name": repo_name,
                "branch": branch,
                "user_code": user_code
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"列出目录失败: {e}")
        raise HTTPException(status_code=500, detail=f"列出目录失败: {str(e)}")


@filesearch_router.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    repo_url: str = Query(..., description="仓库URL"),
    branch: str = Query(default="main", description="分支名称"),
    user_code: str = Query(..., description="用户代码"),
    path: str = Query(default="", description="目标目录路径"),
    current_user=Depends(get_current_user_optional),
    file_manager_service: FileManagerService = Depends(get_file_manager_service),
    audit_log=Depends(audit_logger)
) -> Dict[str, Any]:
    """
    上传文件 - 支持通过Query参数或FormData传递参数

    Args:
        file: 上传的文件
        repo_url: 仓库URL（可选，可通过FormData传递）
        branch: 分支名称（可选，可通过FormData传递）
        user_code: 用户代码（可选，可通过FormData传递或从当前用户获取）
        path: 目标目录路径（可选，可通过FormData传递）
        current_user: 当前用户（可选）
        file_manager_service: 文件管理服务

    Returns:
        上传结果
    """
    try:
        # 解析仓库信息
        try:
            owner, repo_name, _ = extract_repo_info(repo_url)
        except Exception as e:
            logger.error(f"解析仓库URL失败: {repo_url}, 错误: {e}")
            raise HTTPException(status_code=400, detail=f"无效的仓库URL: {repo_url}")

        # 检查文件是否为空
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")

        # 权限校验：i-doc/o-doc/project-gemini 仅限超管、wiki管理员、拥有者
        norm_path = (path or "").lstrip('/')
        if norm_path.startswith("i-doc") or norm_path.startswith("o-doc") or norm_path.startswith("project-gemini"):
            if not current_user:
                raise HTTPException(status_code=401, detail="未认证用户不允许上传")
            with session_scope() as session:
                wiki_info = get_wiki_info_by_repo_and_branch(session, repo_url=repo_url, branch=branch)
                if not wiki_info:
                    raise HTTPException(status_code=404, detail="未找到对应的wiki信息")
                allowed = can_upload_project_docs(session, current_user.get("id", 0), wiki_info.wiki_id)
                if not allowed:
                    raise HTTPException(status_code=403, detail="无权在 i-doc/o-doc/project-gemini 目录下上传文件")

        # 预置审计日志基础信息（失败也能记录到事件编码）
        audit_log(
            code="FILE_UPLOAD",
            party_type="服务",
            party_name=f"{owner}/{repo_name}/{branch}"
        )

        # 上传文件
        result = file_manager_service.upload_file(
            owner=owner,
            repo_name=repo_name,
            branch=branch,
            user_code=user_code,
            virtual_path=path,
            filename=file.filename,
            file_content=file.file
        )

        if not result.get("success"):
            raise HTTPException(status_code=400, detail=result.get("error", "上传失败"))

        # 成功后补充审计数据
        try:
            audit_log(
                oper_data={
                    "action": "upload",
                    "user_code": user_code,
                    "path": path,
                    "filename": file.filename
                }
            )
        except Exception:
            pass

        return {
            "success": True,
            "data": result,
            "repo_info": {
                "owner": owner,
                "repo_name": repo_name,
                "branch": branch,
                "user_code": user_code
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"上传文件失败: {str(e)}")


@filesearch_router.post("/download")
async def download_file(
    request: DownloadRequest,
    current_user=Depends(get_current_user_optional),
    file_manager_service: FileManagerService = Depends(get_file_manager_service),
    audit_log=Depends(audit_logger)
):
    """
    下载文件 - POST方法，参数在请求体中

    Args:
        request: 下载请求参数
        current_user: 当前用户（可选）
        file_manager_service: 文件管理服务

    Returns:
        文件流
    """
    try:
        # 解析仓库信息
        try:
            owner, repo_name, _ = extract_repo_info(request.repo_url)
        except Exception as e:
            logger.error(f"解析仓库URL失败: {request.repo_url}, 错误: {e}")
            raise HTTPException(status_code=400, detail=f"无效的仓库URL: {request.repo_url}")

        # 预置审计日志基础信息（失败也能记录到事件编码）
        audit_log(
            code="FILE_DOWNLOAD",
            party_type="服务",
            party_name=f"{owner}/{repo_name}/{request.branch}"
        )

        # 下载文件
        file_stream, file_info = file_manager_service.download_file(
            owner=owner,
            repo_name=repo_name,
            branch=request.branch,
            user_code=request.user_code,
            virtual_file_path=request.file_path
        )

        if file_stream is None:
            error_msg = file_info.get("error", "下载失败") if file_info else "下载失败"
            raise HTTPException(status_code=404, detail=error_msg)

        # 创建流式响应
        def file_generator():
            try:
                while True:
                    chunk = file_stream.read(8192)  # 8KB chunks
                    if not chunk:
                        break
                    yield chunk
            finally:
                file_stream.close()

        # 设置响应头
        headers = {
            "Content-Disposition": encode_filename_for_download(file_info["name"]),
            "Content-Length": str(file_info["size"])
        }

        # 成功后补充审计数据
        try:
            audit_log(
                oper_data={
                    "action": "download",
                    "user_code": request.user_code,
                    "file_path": request.file_path,
                    "filename": file_info.get("name"),
                    "size": file_info.get("size"),
                    "mime_type": file_info.get("mime_type")
                }
            )
        except Exception:
            pass

        return StreamingResponse(
            file_generator(),
            media_type=file_info["mime_type"],
            headers=headers
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"下载文件失败: {str(e)}")


@filesearch_router.post("/create-directory")
async def create_directory(
    repo_url: str = Query(..., description="仓库URL"),
    branch: str = Query(default="main", description="分支名称"),
    user_code: str = Query(..., description="用户代码"),
    path: str = Query(..., description="父目录路径"),
    name: str = Query(..., description="新目录名称"),
    current_user=Depends(get_current_user_optional),
    file_manager_service: FileManagerService = Depends(get_file_manager_service),
    audit_log=Depends(audit_logger)
) -> Dict[str, Any]:
    """
    创建目录

    Args:
        repo_url: 仓库URL
        branch: 分支名称
        user_code: 用户代码
        path: 父目录路径
        name: 新目录名称
        current_user: 当前用户（可选）
        file_manager_service: 文件管理服务

    Returns:
        创建结果
    """
    try:
        # 解析仓库信息
        try:
            owner, repo_name, _ = extract_repo_info(repo_url)
        except Exception as e:
            logger.error(f"解析仓库URL失败: {repo_url}, 错误: {e}")
            raise HTTPException(status_code=400, detail=f"无效的仓库URL: {repo_url}")
        # 权限校验：i-doc/o-doc 仅限超管、wiki管理员、拥有者
        norm_path = (path or "").lstrip('/')
        if norm_path.startswith("i-doc") or norm_path.startswith("o-doc"):
            if not current_user:
                raise HTTPException(status_code=401, detail="未认证用户不允许创建目录")
            with session_scope() as session:
                wiki_info = get_wiki_info_by_repo_and_branch(session, repo_url=repo_url, branch=branch)
                if not wiki_info:
                    raise HTTPException(status_code=404, detail="未找到对应的wiki信息")
                allowed = can_upload_project_docs(session, current_user.get("id", 0), wiki_info.wiki_id)
                if not allowed:
                    raise HTTPException(status_code=403, detail="无权在 i-doc/o-doc 目录下创建目录")

        # 预置审计日志基础信息（失败也能记录到事件编码）
        audit_log(
            code="DIR_CREATE",
            party_type="服务",
            party_name=f"{owner}/{repo_name}/{branch}"
        )

        # 创建目录
        result = file_manager_service.create_directory(
            owner=owner,
            repo_name=repo_name,
            branch=branch,
            user_code=user_code,
            virtual_path=path,
            directory_name=name
        )

        if not result.get("success"):
            raise HTTPException(status_code=400, detail=result.get("error", "创建目录失败"))

        # 成功后补充审计数据
        try:
            audit_log(
                oper_data={
                    "action": "create-directory",
                    "user_code": user_code,
                    "path": path,
                    "name": name
                }
            )
        except Exception:
            pass

        return {
            "success": True,
            "data": result,
            "repo_info": {
                "owner": owner,
                "repo_name": repo_name,
                "branch": branch,
                "user_code": user_code
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建目录失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建目录失败: {str(e)}")


@filesearch_router.post("/download-directory")
async def download_directory(
    request: DownloadDirectoryRequest,
    current_user=Depends(get_current_user_optional),
    file_manager_service: FileManagerService = Depends(get_file_manager_service),
    audit_log=Depends(audit_logger)
):
    """
    下载目录 - POST方法，参数在请求体中，将目录打包成zip文件下载

    Args:
        request: 下载目录请求参数
        current_user: 当前用户（可选）
        file_manager_service: 文件管理服务

    Returns:
        文件流 (zip文件)
    """
    try:
        # 解析仓库信息
        try:
            owner, repo_name, _ = extract_repo_info(request.repo_url)
        except Exception as e:
            logger.error(f"解析仓库URL失败: {request.repo_url}, 错误: {e}")
            raise HTTPException(status_code=400, detail=f"无效的仓库URL: {request.repo_url}")

        # 预置审计日志基础信息（失败也能记录到事件编码）
        audit_log(
            code="DIR_DOWNLOAD",
            party_type="服务",
            party_name=f"{owner}/{repo_name}/{request.branch}"
        )

        # 打包目录
        zip_file_path, file_info = file_manager_service.zip_directory(
            owner=owner,
            repo_name=repo_name,
            branch=request.branch,
            user_code=request.user_code,
            virtual_dir_path=request.dir_path
        )

        if zip_file_path is None:
            error_msg = file_info.get("error", "打包目录失败") if file_info else "打包目录失败"
            raise HTTPException(status_code=404, detail=error_msg)

        # 创建流式响应
        def file_generator():
            try:
                with open(zip_file_path, 'rb') as f:
                    while True:
                        chunk = f.read(8192)  # 8KB chunks
                        if not chunk:
                            break
                        yield chunk
            finally:
                # 清理临时zip文件
                if zip_file_path.exists():
                    os.remove(zip_file_path)
                    logger.info(f"临时zip文件已清理: {zip_file_path}")

        # 设置响应头
        headers = {
            "Content-Disposition": encode_filename_for_download(file_info["name"]),
            "Content-Length": str(file_info["size"])
        }

        # 成功后补充审计数据
        try:
            audit_log(
                oper_data={
                    "action": "download-directory",
                    "user_code": request.user_code,
                    "dir_path": request.dir_path,
                    "filename": file_info.get("name"),
                    "size": file_info.get("size"),
                    "mime_type": file_info.get("mime_type")
                }
            )
        except Exception:
            pass

        return StreamingResponse(
            file_generator(),
            media_type=file_info["mime_type"],
            headers=headers
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载目录失败: {e}")
        raise HTTPException(status_code=500, detail=f"下载目录失败: {str(e)}")


@filesearch_router.get("/allowed-extensions")
async def get_allowed_extensions(
    current_user=Depends(get_current_user_optional),
    file_manager_service: FileManagerService = Depends(get_file_manager_service)
) -> Dict[str, Any]:
    """
    获取允许上传的文件扩展名列表

    Args:
        current_user: 当前用户（可选）
        file_manager_service: 文件管理服务

    Returns:
        允许的文件扩展名列表
    """
    try:
        # 从文件管理服务获取允许的扩展名
        allowed_extensions = file_manager_service.file_filter.get_upload_allowed_extensions()

        # 转换为前端需要的格式（去掉点号）
        extensions_list = [ext[1:] for ext in allowed_extensions]  # 去掉开头的点号

        return {
            "success": True,
            "data": {
                "allowed_extensions": extensions_list,
                "accept_attr": ",".join([f".{ext}" for ext in extensions_list])
            }
        }

    except Exception as e:
        logger.error(f"获取允许的文件扩展名失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取允许的文件扩展名失败: {str(e)}")


@filesearch_router.get("/doc-upload-permission")
async def get_doc_upload_permission(
    repo_url: str = Query(..., description="仓库URL"),
    branch: str = Query(default="main", description="分支名称"),
    current_user=Depends(get_current_user_optional),
):
    """
    查询当前用户是否具备在 i-doc/o-doc 下上传/新建的权限。
    条件：超级管理员 或 wiki 拥有者 或 当前 wiki 管理员。
    """
    try:
        if not current_user:
            return {"success": True, "data": {"allowed": False}}
        with session_scope() as session:
            wiki_info = get_wiki_info_by_repo_and_branch(session, repo_url=repo_url, branch=branch)
            if not wiki_info:
                return {"success": True, "data": {"allowed": False}}
            allowed = can_upload_project_docs(session, current_user.get("id", 0), wiki_info.wiki_id)
            return {"success": True, "data": {"allowed": bool(allowed)}}
    except Exception as e:
        logger.error(f"查询 i-doc/o-doc 权限失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询权限失败: {str(e)}")


@filesearch_router.get("/doc-upload-global-docs-permission")
async def get_global_docs_upload_permission(
    current_user=Depends(get_current_user_optional),
):
    """
    查询当前用户是否具备查看全局文档目录(g-doc)的权限。
    条件：超级管理员。
    """
    try:
        if not current_user:
            return {"success": True, "data": {"canView": False}}
        with session_scope() as session:
            can_view = check_is_super_admin(session, current_user.get("id", 0))
            return {"success": True, "data": {"canView": bool(can_view)}}
    except Exception as e:
        logger.error(f"查询全局文档权限失败: {e}")
        return {"success": False, "message": "查询权限失败", "data": {"canView": False}}


@filesearch_router.delete("/delete")
async def delete_item(
    repo_url: str = Query(..., description="仓库URL"),
    branch: str = Query(default="main", description="分支名称"),
    user_code: str = Query(..., description="用户代码"),
    item_path: str = Query(..., description="要删除的文件或目录路径"),
    current_user=Depends(get_current_user_optional),
    file_manager_service: FileManagerService = Depends(get_file_manager_service)
) -> Dict[str, Any]:
    """
    删除文件或目录

    Args:
        repo_url: 仓库URL
        branch: 分支名称
        user_code: 用户代码
        item_path: 要删除的文件或目录路径
        current_user: 当前用户（可选）
        file_manager_service: 文件管理服务

    Returns:
        删除结果
    """
    try:
        # 解析仓库信息
        try:
            owner, repo_name, _ = extract_repo_info(repo_url)
        except Exception as e:
            logger.error(f"解析仓库URL失败: {repo_url}, 错误: {e}")
            raise HTTPException(status_code=400, detail=f"无效的仓库URL: {repo_url}")

        # 删除文件或目录
        result = file_manager_service.delete_item(
            owner=owner,
            repo_name=repo_name,
            branch=branch,
            user_code=user_code,
            virtual_path=item_path
        )

        if not result.get("success"):
            raise HTTPException(status_code=400, detail=result.get("error", "删除失败"))

        return {
            "success": True,
            "data": result,
            "repo_info": {
                "owner": owner,
                "repo_name": repo_name,
                "branch": branch,
                "user_code": user_code
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除项目失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除项目失败: {str(e)}")


@filesearch_router.get("/image/{chat_sid}")
async def download_image(
        chat_sid: str,
        file_path: str = Query(..., description="图片文件路径"),
        current_user=Depends(get_current_user_optional),
        audit_log=Depends(audit_logger)
):
    """
    下载图片文件 - 基于chat_sid和文件路径

    Args:
        chat_sid: 聊天会话ID
        file_path: 图片文件在服务器上的相对路径
        current_user: 当前用户（可选）
        audit_log: 审计日志

    Returns:
        图片文件流
    """
    try:
        logger.info(f"开始处理图片下载请求, chat_sid: {chat_sid}, file_path: {file_path}")

        # 验证chat_sid
        if not chat_sid or not chat_sid.strip():
            logger.warning(f"无效的chat_sid: {chat_sid}")
            raise HTTPException(status_code=400, detail="chat_sid不能为空")

        # 验证文件路径
        if not file_path or not file_path.strip():
            logger.warning(f"无效的文件路径: {file_path}")
            raise HTTPException(status_code=400, detail="文件路径不能为空")

        # 安全检查：防止路径遍历攻击
        normalized_path = os.path.normpath(file_path)
        if normalized_path.startswith('../') or '/../' in normalized_path:
            logger.warning(f"检测到非法文件路径: {file_path}")
            raise HTTPException(status_code=400, detail="非法的文件路径")

        # 根据chat_sid查询chat_session
        logger.debug(f"查询chat_session, chat_sid: {chat_sid}")
        chat_session = select_chat_session_by_chat_sid(chat_sid)
        if not chat_session:
            logger.warning(f"未找到chat_session, chat_sid: {chat_sid}")
            raise HTTPException(status_code=404, detail="聊天会话不存在")

        wiki_id = chat_session.get('wiki_id')
        if not wiki_id:
            logger.warning(f"chat_session中未找到wiki_id, chat_sid: {chat_sid}")
            raise HTTPException(status_code=404, detail="未找到关联的wiki信息")

        # 根据wiki_id查询wiki信息
        logger.debug(f"查询wiki信息, wiki_id: {wiki_id}")
        with session_scope() as session:
            wiki_info = get_wiki_info_by_id(session, wiki_id)
            if not wiki_info:
                logger.warning(f"未找到wiki信息, wiki_id: {wiki_id}")
                raise HTTPException(status_code=404, detail="wiki信息不存在")

        # 构建真实文件路径
        # 基于wiki信息构建项目根目录
        branch_safe = wiki_info.branch.replace('/', '_').replace('\\', '_')
        project_root = Path("/root/.adalflow/project_workspace") / f"{wiki_info.repo_owner}/{wiki_info.repo_name}-{branch_safe}"

        logger.debug(f"项目根目录: {project_root}")

        # 如果文件路径以 /data/workspace 开头，则替换为实际路径
        if file_path.startswith('/data/workspace'):
            # 移除 /data/workspace 前缀
            relative_path = file_path[len('/data/workspace'):].lstrip('/')
            full_file_path = project_root / relative_path
            logger.debug(f"处理/data/workspace路径, relative_path: {relative_path}")
        else:
            # 直接使用相对路径
            full_file_path = project_root / normalized_path
            logger.debug(f"使用相对路径: {normalized_path}")

        # 安全检查：确保文件在项目目录内
        try:
            full_file_path = full_file_path.resolve()
            project_root = project_root.resolve()
            if not str(full_file_path).startswith(str(project_root)):
                logger.warning(f"文件路径超出允许范围, full_file_path: {full_file_path}, project_root: {project_root}")
                raise HTTPException(status_code=403, detail="访问被拒绝：文件不在允许的目录范围内")
        except Exception as e:
            logger.error(f"路径解析失败: {e}")
            raise HTTPException(status_code=400, detail="无效的文件路径")

        logger.info(f"解析后的文件路径: {full_file_path}")

        # 检查文件是否存在
        if not full_file_path.exists():
            logger.warning(f"文件不存在: {full_file_path}")
            raise HTTPException(status_code=404, detail="文件不存在")

        if not full_file_path.is_file():
            logger.warning(f"路径不是文件: {full_file_path}")
            raise HTTPException(status_code=400, detail="路径不是文件")

        # 验证是否为图片文件
        mime_type, _ = mimetypes.guess_type(str(full_file_path))
        if not mime_type or not mime_type.startswith('image/'):
            logger.warning(f"文件不是有效的图片格式, mime_type: {mime_type}, file_path: {full_file_path}")
            raise HTTPException(status_code=400, detail="文件不是有效的图片格式")

        # 检查文件大小（限制为10MB）
        file_size = full_file_path.stat().st_size
        max_size = 10 * 1024 * 1024  # 10MB
        if file_size > max_size:
            logger.warning(f"图片文件过大, size: {file_size}, max_size: {max_size}")
            raise HTTPException(status_code=400, detail="图片文件过大")

        logger.info(f"文件验证通过, 准备开始传输, file_path: {full_file_path}, size: {file_size}, mime_type: {mime_type}")

        # 创建文件流
        def file_generator():
            try:
                with open(full_file_path, 'rb') as f:
                    logger.debug(f"开始读取文件: {full_file_path}")
                    while True:
                        chunk = f.read(8192)  # 8KB chunks
                        if not chunk:
                            break
                        yield chunk
                    logger.debug(f"文件读取完成: {full_file_path}")
            except Exception as e:
                logger.error(f"读取图片文件失败: {e}, file_path: {full_file_path}")
                raise

        # 设置响应头
        headers = {
            "Content-Length": str(file_size),
            "Cache-Control": "public, max-age=3600"  # 缓存1小时
        }

        # 记录审计日志
        try:
            audit_log(
                oper_data={
                    "action": "download-image",
                    "chat_sid": chat_sid,
                    "file_path": file_path,
                    "wiki_id": wiki_id,
                    "file_size": file_size,
                    "mime_type": mime_type
                }
            )
        except Exception:
            pass

        logger.info(f"图片下载成功: {full_file_path} (chat_sid: {chat_sid})")

        return StreamingResponse(
            file_generator(),
            media_type=mime_type,
            headers=headers
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载图片失败: {e}, chat_sid: {chat_sid}, file_path: {file_path}")
        raise HTTPException(status_code=500, detail=f"下载图片失败: {str(e)}")

