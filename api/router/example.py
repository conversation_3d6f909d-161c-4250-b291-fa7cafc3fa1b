from fastapi import APIRouter, HTTPException
from typing import Optional, Any
from pydantic import BaseModel

from api.cache.redis.client import RedisClient
from api.core.dependencies import RedisClientDep

router = APIRouter()


class KeyValue(BaseModel):
    key: str
    value: Any
    ttl: Optional[int] = None


class KeyResponse(BaseModel):
    key: str
    value: Any
    exists: bool


@router.post("/cache/set")
async def set_cache(data: KeyValue, redis: Optional[RedisClient] = RedisClientDep):
    """设置缓存"""
    if not redis:
        raise HTTPException(status_code=503, detail="Redis service unavailable")
    
    success = redis.set(data.key, data.value, data.ttl)
    return {"success": success, "key": data.key}


@router.get("/cache/{key}")
async def get_cache(key: str, redis: Optional[RedisClient] = RedisClientDep):
    """获取缓存"""
    if not redis:
        raise HTTPException(status_code=503, detail="Redis service unavailable")
    
    value = redis.get(key)
    exists = value is not None
    
    return KeyResponse(key=key, value=value, exists=exists)


@router.delete("/cache/{key}")
async def delete_cache(key: str, redis: Optional[RedisClient] = RedisClientDep):
    """删除缓存"""
    if not redis:
        raise HTTPException(status_code=503, detail="Redis service unavailable")
    
    deleted = redis.delete(key)
    return {"deleted": deleted, "key": key}


@router.get("/cache/{key}/ttl")
async def get_ttl(key: str, redis: Optional[RedisClient] = RedisClientDep):
    """获取TTL"""
    if not redis:
        raise HTTPException(status_code=503, detail="Redis service unavailable")
    
    ttl = redis.ttl(key)
    return {"key": key, "ttl": ttl}


@router.post("/counter/{key}/incr")
async def increment_counter(key: str, amount: int = 1, redis: Optional[RedisClient] = RedisClientDep):
    """递增计数器"""
    if not redis:
        raise HTTPException(status_code=503, detail="Redis service unavailable")
    
    value = redis.incr(key, amount)
    return {"key": key, "value": value}
