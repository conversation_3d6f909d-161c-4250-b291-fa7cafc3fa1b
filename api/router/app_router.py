from collections import defaultdict
from datetime import datetime
import logging
import secrets
from typing import List, Optional
import uuid
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field
from sqlalchemy import and_, func, not_, or_
from sqlmodel import Session, delete, select

from api.database.base import session_scope
from api.logging_config import setup_logging
from api.middleware.audit_log_middleware import audit_logger
from api.middleware.auth_middleware import get_current_user
from api.model.access_token import AppAccessToken
from api.model.api_def import ApiDef
from api.model.app import App
from api.model.app_api_rel import AppApiRel
from api.model.app_token_user_rel import AppTokenUserRel
from api.model.user_info import UserInfo
from api.model.wiki_info import WikiInfo
from api.service.app_wiki_service import list_app_wikis, replace_app_wikis, list_wiki_ids_for_app
from api.utils.aes_utils import AESUtils

app_router = APIRouter(prefix="/api/apps", tags=["app"])

setup_logging()
logger = logging.getLogger(__name__)

aes = AESUtils()

class CreateAppRequest(BaseModel):
    """创建应用请求模型"""
    app_name: str = Field(..., max_length=60, description="应用名称")
    app_code: str = Field(..., max_length=60, description="应用编码")
    comments: Optional[str] = Field(..., max_length=255, description="应用描述")

class UpdateAppRequest(BaseModel):
    """修改应用信息请求模型"""
    app_name: str = Field(..., max_length=60, description="应用名称")
    app_code: str = Field(..., max_length=60, description="应用编码")
    comments: Optional[str] = Field(..., max_length=255, description="应用描述")

class ModifyAppStateRequest(BaseModel):
    """修改应用状态请求模型"""
    state: bool = Field(..., description="应用状态")

class GenerateTokenRequest(BaseModel):
    """生成token请求模型"""
    app_primary_key: int = Field(..., description="应用主键")
    # 按最新约定：1-用户级，2-系统级；均不强制绑定用户
    token_type: int = Field(..., description="Token类型：1-用户级，2-系统级")
    effective_at: Optional[datetime] = Field(None, description="Token生效时间，为空表示立即生效")
    expires_at: Optional[datetime] = Field(None, description="Token失效日期,如果为空则永远有效")
    # 兼容历史参数，前端不再传；后端不再强制要求
    users: Optional[List[int]] = Field(None, description="已废弃：用户级Token绑定的用户列表")

class UpdateTokenRequest(BaseModel):
    """更新token请求模型"""
    token_id: int = Field(..., description="token主键")
    token_type: int = Field(..., description="Token类型：1-用户级，2-系统级")
    effective_at: Optional[datetime] = Field(None, description="Token生效时间，为空表示立即生效")
    expires_at: Optional[datetime] = Field(None, description="Token失效日期,如果为空则永远有效")
    users: Optional[List[int]] = Field(None, description="已废弃：用户级Token绑定的用户列表")

class ModifyAppTokenStateRequest(BaseModel):
    """修改Token状态请求模型"""
    state: bool = Field(..., description="Token状态")

class ModifyAppApiStateRequest(BaseModel):
    """修改应用绑定接口状态请求模型"""
    state: bool = Field(..., description="绑定接口状态")

class BindApiRequest(BaseModel):
    """绑定请求模型"""
    api_list: List[int] = Field(..., description="要绑定的接口列表")


class BindWikiRequest(BaseModel):
    """绑定Wiki请求模型"""
    wiki_list: List[int] = Field(default_factory=list, description="要绑定的Wiki主键列表")

@app_router.post("")
async def create_app(request: CreateAppRequest, audit_log=Depends(audit_logger)):
    try:
        user = get_current_user()
        with session_scope() as session:
            #检查app_code是否存在
            exist_code = session.exec(select(App.app_code).select_from(App).where(App.app_code == request.app_code)).first()
            if exist_code:
                raise HTTPException(status_code=409, detail=f"The app code [{exist_code}] already exists.")
            new_app = App(
                app_id=str(uuid.uuid4()),
                app_secret=aes.encrypt_fixed(secrets.token_urlsafe(32)),
                app_name=request.app_name,
                app_code=request.app_code,
                comments=request.comments,
                state=True,
                created_by=user.get("id", 0),
                created_date=datetime.now()
            )

            session.add(new_app)
            session.flush()

            # 设置审计日志数据
            audit_log(code="APP_CREATE", party_type="服务", party_id=new_app.id, party_name=new_app.app_name, party_code=new_app.app_code)
    except HTTPException:
        raise
    except Exception as ex:
        logger.error(ex)
        raise HTTPException(status_code=500, detail="Failed to create app.")

@app_router.put("/{id}")
async def create_app(id: int, request: UpdateAppRequest, audit_log=Depends(audit_logger)):
    """
    创建应用
    """
    try:
        user = get_current_user()
        with session_scope() as session:
            # 检查应用是否存在
            exist_app = session.exec(select(App).where(App.id == id)).first()
            if exist_app is None:
                raise HTTPException(status_code=404, detail=f"Can not find app by id [{id}].")
            # 检查应用编码是否重复
            if exist_app.app_code != request.app_code:
                exist_code = session.exec(select(App.app_code).select_from(App).where(App.app_code == request.app_code)).first()
                if exist_code:
                    raise HTTPException(status_code=409, detail=f"The app code [{exist_code}] already exists.")
            exist_app.app_name = request.app_name
            exist_app.app_code = request.app_code
            exist_app.comments = request.comments
            exist_app.update_by = user.get("id", 0)
            exist_app.update_date = datetime.now()

            # 设置审计日志数据
            audit_log(code="APP_UPDATE", party_type="服务", party_id=exist_app.id, party_name=exist_app.app_name, party_code=exist_app.app_code)
    except HTTPException:
        raise
    except Exception as ex:
        logger.error(ex)
        raise HTTPException(status_code=500, detail="Failed to update app.")

@app_router.get("")
async def get_apps(search: Optional[str], page: int, size: int):
    """
    获取应用列表
    """
    try:
        with session_scope() as session:
            filters = []
            if search:
                filters.append(or_(App.app_name.like(f"%{search}%"), App.app_code.like(f"%{search}%")))
            stmt = select(App, UserInfo).join(UserInfo, App.created_by == UserInfo.id)
            if filters:
                stmt = stmt.where(and_(*filters))
            total = session.query(func.count(App.id)).filter(*filters).scalar()
            stmt = stmt.limit(size).offset(page * size)
            result = session.exec(stmt).all()
            apps = []
            for (app, user) in result:
                app = {
                    **app.model_dump(),
                    "created_date": app.created_date.strftime('%Y-%m-%d %H:%M'),
                    "app_secret": aes.decrypt(app.app_secret),
                    "created_by": f"{user.user_name}[{user.user_code}]"
                }
                apps.append(app)
            return {"total": total, "data": apps}
    except Exception as ex:
        logger.error(ex)
        raise HTTPException(status_code=500, detail="Failed to get app list.")

@app_router.delete("/{id}")
async def delete_app(id: int, audit_log=Depends(audit_logger)):
    """
    删除应用
    """
    try:
        with session_scope() as session:
            exist_app = session.exec(select(App).where(App.id == id)).first()
            if exist_app:
                # 设置审计日志数据
                audit_log(code="APP_DELETE", party_type="服务", party_id=exist_app.id, party_name=exist_app.app_name, party_code=exist_app.app_code)

            # 删除与接口的绑定关系
            session.exec(delete(AppApiRel).where(AppApiRel.app_id == id))
            # 获取应用生成的Token列表
            token_ids = session.exec(select(AppAccessToken.id).where(AppAccessToken.app_id == id)).all()
            # 删除Token与User的绑定关系
            session.exec(delete(AppTokenUserRel).where(AppTokenUserRel.token_id.in_(token_ids)))
            # 删除应用生成的Token
            session.exec(delete(AppAccessToken).where(AppAccessToken.id.in_(token_ids)))
            # 删除应用
            session.exec(delete(App).where(App.id == id))

            
    except Exception as ex:
        logger.error(ex)
        raise HTTPException(status_code=500, detail="Failed to delete app.")
    

@app_router.get("/{app_primary_key}/wikis")
async def get_app_wikis(app_primary_key: int):
    """获取应用已绑定的Wiki列表"""
    try:
        with session_scope() as session:
            exist_app = session.exec(select(App).where(App.id == app_primary_key)).first()
            if exist_app is None:
                raise HTTPException(status_code=404, detail=f"Can not find app by id [{app_primary_key}].")
            wiki_rows = list_app_wikis(app_primary_key, session=session)
            return [
                {
                    **wiki.model_dump(include={
                        "id",
                        "wiki_id",
                        "name",
                        "description",
                        "repo_owner",
                        "repo_name",
                        "branch",
                        "status",
                    })
                }
                for wiki in wiki_rows
            ]
    except HTTPException:
        raise
    except Exception as ex:
        logger.error(ex)
        raise HTTPException(status_code=500, detail="Failed to get app wiki bindings.")


@app_router.get("/{app_primary_key}/wikis/ids")
async def get_app_wiki_ids(app_primary_key: int):
    """获取应用已绑定的Wiki主键ID列表"""
    try:
        with session_scope() as session:
            exist_app = session.exec(select(App).where(App.id == app_primary_key)).first()
            if exist_app is None:
                raise HTTPException(status_code=404, detail=f"Can not find app by id [{app_primary_key}].")
            wiki_ids = list_wiki_ids_for_app(app_primary_key, session=session)
            return wiki_ids
    except HTTPException:
        raise
    except Exception as ex:
        logger.error(ex)
        raise HTTPException(status_code=500, detail="Failed to get app wiki ids.")


@app_router.get("/{app_primary_key}/wikis/options")
async def get_app_wiki_options(app_primary_key: int, search: Optional[str] = Query(None, description="搜索关键字")):
    """获取应用可绑定的Wiki列表，标记已绑定状态"""
    try:
        with session_scope() as session:
            exist_app = session.exec(select(App).where(App.id == app_primary_key)).first()
            if exist_app is None:
                raise HTTPException(status_code=404, detail=f"Can not find app by id [{app_primary_key}].")
            bound_ids = set(list_wiki_ids_for_app(app_primary_key, session=session))
            stmt = select(WikiInfo).where(WikiInfo.status != "failed")
            if search:
                pattern = f"%{search}%"
                stmt = stmt.where(
                    or_(
                        WikiInfo.name.like(pattern),
                        WikiInfo.repo_name.like(pattern),
                        WikiInfo.repo_owner.like(pattern),
                    )
                )
            stmt = stmt.order_by(WikiInfo.created_time.desc())
            rows = session.exec(stmt).all()
            data = []
            for wiki in rows:
                record = wiki.model_dump(include={
                    "id",
                    "wiki_id",
                    "name",
                    "description",
                    "repo_owner",
                    "repo_name",
                    "branch",
                    "language",
                    "status",
                })
                record["is_bound"] = wiki.id in bound_ids
                data.append(record)
            return data
    except HTTPException:
        raise
    except Exception as ex:
        logger.error(ex)
        raise HTTPException(status_code=500, detail="Failed to get wiki options.")


@app_router.post("/{app_primary_key}/wikis")
async def bind_wikis(app_primary_key: int, request: BindWikiRequest, audit_log=Depends(audit_logger)):
    """绑定应用可访问的Wiki"""
    try:
        user = get_current_user()
        operator_id = user.get("id", 0) if user else 0
        with session_scope() as session:
            exist_app = session.exec(select(App).where(App.id == app_primary_key)).first()
            if exist_app is None:
                raise HTTPException(status_code=404, detail=f"Can not find app by id [{app_primary_key}].")

            replace_app_wikis(
                app_primary_key,
                request.wiki_list,
                operator_id=operator_id,
                session=session,
            )

            audit_log(
                code="APP_WIKI_BIND",
                party_type="服务",
                party_id=exist_app.id,
                party_code=exist_app.app_code,
                party_name=exist_app.app_name,
                oper_data={"wiki_list": request.wiki_list},
            )
        return {"success": True}
    except HTTPException:
        raise
    except ValueError as ex:
        logger.error(ex)
        raise HTTPException(status_code=400, detail=str(ex))
    except Exception as ex:
        logger.error(ex)
        raise HTTPException(status_code=500, detail="Failed to bind wikis to app.")

@app_router.patch("/{id}/state")
async def modify_app_state(id: int, request: ModifyAppStateRequest, audit_log=Depends(audit_logger)):
    """
    修改应用状态
    """
    try:
        user = get_current_user()
        with session_scope() as session:
            # 检查应用是否存在
            exist_app = session.exec(select(App).where(App.id == id)).first()
            if exist_app is None:
                raise HTTPException(status_code=404, detail=f"Can not find app by id [{id}].")
            exist_app.state = request.state
            exist_app.update_by = user.get("id", 0)
            exist_app.update_date = datetime.now()

            # 设置审计日志数据
            audit_log(code="APP_STATE", party_type="服务", party_id=exist_app.id, party_name=exist_app.app_name, party_code=exist_app.app_code, oper_data=f"{request.state}")
    except HTTPException:
        raise
    except Exception as ex:
        logger.error(ex)
        raise HTTPException(status_code=500, detail="Failed to modify app state.")

@app_router.post("/{app_primary_key}/tokens")
async def generate_token(request: GenerateTokenRequest, audit_log=Depends(audit_logger)):
    """
    生成应用token
    """
    try:
        user = get_current_user()
        now = datetime.now()
        with session_scope() as session:
            # 检查应用是否存在
            exist_app = session.exec(select(App).where(App.id == request.app_primary_key)).first()
            if exist_app is None:
                raise HTTPException(status_code=404, detail=f"Can not find app by id [{id}].")
            token = AppAccessToken(
                token=aes.encrypt_fixed(str(uuid.uuid4())),
                app_id=request.app_primary_key,
                token_type=request.token_type,
                effective_at=request.effective_at,
                expires_at=request.expires_at,
                state=True,
                created_by=user.get("id", 0),
                created_date=now
            )
            session.add(token)
            session.flush()

            # 设置审计日志数据
            audit_log(code="TOKEN_CREATE", party_type="服务", party_id=token.id)

            # 兼容：若仍传入用户列表，则建立绑定关系；否则不创建
            if request.users:
                token_user_relations = [AppTokenUserRel(
                    token_id=token.id,
                    user_id=user_id,
                    created_by=user.get("id", 0),
                    created_date=now
                ) for user_id in request.users]
                session.add_all(token_user_relations)
    except HTTPException:
        raise
    except Exception as ex:
        logger.error(ex)
        raise HTTPException(status_code=500, detail="Failed to generate app token.")
    
@app_router.put("/{app_primary_key}/tokens/{token_id}")
async def update_token(app_primary_key: int, token_id: int, request: UpdateTokenRequest, audit_log=Depends(audit_logger)):
    """
    更新应用token
    """
    try:
        user = get_current_user()
        now = datetime.now()
        with session_scope() as session:
            exist_token = session.exec(select(AppAccessToken).where(AppAccessToken.id == token_id)).first()
            if exist_token is None:
                raise HTTPException(status_code=404, detail=f"Can not find token by id [{token_id}].")
            # 更新token信息
            exist_token.token_type = request.token_type
            exist_token.effective_at = request.effective_at
            exist_token.expires_at = request.expires_at
            exist_token.update_by = user.get("id", 0)
            exist_token.update_date = now
            # 更新token绑定用户（仅当显式传入时）
            if request.users is not None:
                session.exec(delete(AppTokenUserRel).where(AppTokenUserRel.token_id == token_id))
                if request.users:
                    token_user_relations = [AppTokenUserRel(
                        token_id=token_id,
                        user_id=user_id,
                        created_by=user.get("id", 0),
                        created_date=now
                    ) for user_id in request.users]
                    session.add_all(token_user_relations)

            # 设置审计日志数据
            audit_log(code="TOKEN_UPDATE", party_type="服务", party_id=exist_token.id)
    except HTTPException:
        raise
    except Exception as ex:
        logger.error(ex)
        raise HTTPException(status_code=500, detail="Failed to update app token.")

@app_router.get("/{app_primary_key}/tokens")
async def get_app_tokens(app_primary_key: int, page: int, size: int):
    """
    获取应用token列表
    """
    def optional_format(value):
        return value.strftime('%Y-%m-%d %H:%M') if value is not None else None
    try:
        with session_scope() as session:
            total = session.exec(select(func.count(AppAccessToken.id)).where(AppAccessToken.app_id == app_primary_key)).one()
            stmt = (select(AppAccessToken, UserInfo.id, UserInfo.user_name, UserInfo.user_code)
                    .select_from(AppAccessToken)
                    .outerjoin(AppTokenUserRel, AppTokenUserRel.token_id == AppAccessToken.id)
                    .outerjoin(UserInfo, UserInfo.id == AppTokenUserRel.user_id)
                    .where(AppAccessToken.app_id == app_primary_key)
                    .limit(size)
                    .offset(page * size))
            rows = session.exec(stmt).all()
            token_map = defaultdict(lambda: {"token": None})
            for token, user_id, user_name, user_code in rows:
                if token_map[token.id]["token"] is None:
                    token_map[token.id]["token"] = {
                    **token.model_dump(),
                    "token": aes.decrypt(token.token),
                    "created_date": token.created_date.strftime('%Y-%m-%d %H:%M'),
                    "effective_at": (token.effective_at or token.created_date).strftime('%Y-%m-%d %H:%M'),
                    "expires_at": optional_format(token.expires_at),
                    "users": []
                }
                if user_id is not None:
                    token_map[token.id]["token"]["users"].append({
                        "id": user_id,
                        "user_name": user_name,
                        "user_code": user_code,
                    })
            return {"total": total, "data": [token_dict["token"] for token_dict in token_map.values()]}
    except HTTPException:
        raise
    except Exception as ex:
        logger.error(ex)
        raise HTTPException(status_code=500, detail="Failed to get app token list.")

@app_router.delete("/{app_primary_key}/tokens/{token_id}")
async def delete_app_token(app_primary_key: int, token_id: int, audit_log=Depends(audit_logger)):
    """
    删除应用token
    """
    try:
        with session_scope() as session:
            exist_token = session.exec(select(AppAccessToken).where(AppAccessToken.id == token_id)).first()
            if exist_token:
                # 设置审计日志数据
                audit_log(code="TOKEN_DELETE", party_type="服务", party_id=exist_token.id)
            # 删除Token与用户的绑定关系
            session.exec(delete(AppTokenUserRel).where(AppTokenUserRel.token_id == token_id))
            # 删除Token
            session.exec(delete(AppAccessToken).where(AppAccessToken.id == token_id))
    except HTTPException:
        raise
    except Exception as ex:
        logger.error(ex)
        raise HTTPException(status_code=500, detail="Failed to delete app token.")

@app_router.patch("/{app_primary_key}/tokens/{token_id}/state")
async def modify_app_token(app_primary_key: int, token_id: int, request: ModifyAppTokenStateRequest, audit_log=Depends(audit_logger)):
    """
    修改应用Token状态
    """
    try:
        user = get_current_user()
        with session_scope() as session:
            # 检查Token是否存在
            exist_token = session.exec(select(AppAccessToken).where(AppAccessToken.id == token_id)).first()
            if exist_token is None:
                raise HTTPException(status_code=404, detail=f"Can not find token by id [{token_id}].")
            exist_token.state = request.state
            exist_token.update_by = user.get("id", 0)
            exist_token.update_date = datetime.now()

            # 设置审计日志数据
            audit_log(code="TOKEN_STATE", party_type="服务", party_id=exist_token.id, oper_data=f"{request.state}")
    except HTTPException:
        raise
    except Exception as ex:
        logger.error(ex)
        raise HTTPException(status_code=500, detail="Failed to modify token state.")

@app_router.get("/{app_primary_key}/api")
async def get_app_api(app_primary_key: int):
    """
    获取应用关联的接口列表
    """
    try:
        with session_scope() as session:
            rows = session.exec(select(ApiDef, AppApiRel.state)
                        .join(AppApiRel, ApiDef.id == AppApiRel.api_id)
                        .join(App, App.id == AppApiRel.app_id)
                        .where(App.id == app_primary_key)).all()
            api_list = []
            for api, state in rows:
                api_list.append({**api.model_dump(), "state": state})
            return api_list
    except HTTPException:
        raise
    except Exception as ex:
        logger.error(ex)
        raise HTTPException(status_code=500, detail="Failed to get api list.")

@app_router.delete("/{app_primary_key}/api/{api_id}")
async def delete_app_api(app_primary_key: int, api_id: int, audit_log=Depends(audit_logger)):
    """
    解绑应用关联的接口
    """
    try:
        with session_scope() as session:
            exist_app = session.exec(select(App).where(App.id == app_primary_key)).first()
            if exist_app:
                # 设置审计日志数据
                audit_log(code="API_UNBIND", party_type="服务", party_id=exist_app.id, party_code=exist_app.app_code, party_name=exist_app.app_name, oper_data={"api_id": api_id})

            session.exec(delete(AppApiRel).where(AppApiRel.app_id == app_primary_key, AppApiRel.api_id == api_id))
    except HTTPException:
        raise
    except Exception as ex:
        logger.error(ex)
        raise HTTPException(status_code=500, detail="Failed to delete unbind api.")

@app_router.patch("/{app_primary_key}/api/{api_id}/state")
async def modify_app_api_state(app_primary_key: int, api_id: int, request: ModifyAppApiStateRequest, audit_log=Depends(audit_logger)):
    """
    修改应用关联的接口的状态
    """
    try:
        user = get_current_user()
        with session_scope() as session:
            exist_app_api_ref = session.exec(select(AppApiRel).where(AppApiRel.app_id == app_primary_key, AppApiRel.api_id == api_id)).first()
            if exist_app_api_ref is None:
                raise HTTPException(status_code=404, detail=f"Can not find relationship by app id [{app_primary_key}] and api id [{api_id}].")

            exist_app = session.exec(select(App).where(App.id == app_primary_key)).first()
            if exist_app:
                # 设置审计日志数据
                audit_log(code="APP_API_STATE", party_type="服务", party_id=exist_app.id, party_code=exist_app.app_code, party_name=exist_app.app_name, oper_data={"api_id": api_id, "state": request.state})

            exist_app_api_ref.state = request.state
            exist_app_api_ref.update_by = user.get("id", 0)
            exist_app_api_ref.update_date = datetime.now()
    except HTTPException:
        raise
    except Exception as ex:
        logger.error(ex)
        raise HTTPException(status_code=500, detail="Failed to modify api state.")

@app_router.get("/{app_primary_key}/api/unbound")
async def get_app_unbound_api(app_primary_key: int):
    """
    获取应用未绑定的接口列表
    """
    try:
        with session_scope() as session:
            subquery = select(AppApiRel.api_id).where(AppApiRel.app_id == app_primary_key)
            api_list = session.exec(select(ApiDef).where(ApiDef.state == 1, not_(ApiDef.id.in_(subquery)))).all()
            return [ApiDef(**api.model_dump()) for api in api_list]
    except HTTPException:
        raise
    except Exception as ex:
        logger.error(ex)
        raise HTTPException(status_code=500, detail="Failed to get unbound api list.")

@app_router.post("/{app_primary_key}/api")
async def bind_api(app_primary_key: int, request: BindApiRequest, audit_log=Depends(audit_logger)):
    """
    应用绑定接口
    """
    try:
        user = get_current_user()
        now = datetime.now()
        with session_scope() as session:
            # 检查应用是否存在
            exist_app = session.exec(select(App).where(App.id == app_primary_key)).first()
            if exist_app is None:
                raise HTTPException(status_code=404, detail=f"Can not find app by id [{app_primary_key}].")

            # 设置审计日志数据
            audit_log(code="API_BIND", party_type="服务", party_id=exist_app.id, party_code=exist_app.app_code, party_name=exist_app.app_name, oper_data={"api_list": request.api_list})

            refs = [AppApiRel(
                app_id=app_primary_key,
                api_id=api_id,
                created_by=user.get("id", 0),
                created_date=now,
                state=True
            ) for api_id in request.api_list]
            session.add_all(refs)
    except HTTPException:
        raise
    except Exception as ex:
        logger.error(ex)
        raise HTTPException(status_code=500, detail="Failed to get unbound api list.")
