from fastapi import APIRouter
from api.cache.local.priv_cache import refresh_cache, get_cache
from api.cache.local.role_cache import refresh_cache as refresh_role_cache, get_cache as get_role_cache
from api.cache.local.event_code_cache import refresh_cache as refresh_event_code_cache, get_cache as get_event_code_cache

cache_router = APIRouter(
    prefix="/api/cache",
    tags=["deepwiki_cache"]
)

@cache_router.post("/priv/refresh", summary="刷新 ai_dw_priv 缓存并返回最新数据")
def refresh_ai_dw_priv_cache():
    data = refresh_cache()
    return {"success": True, "data": data}

@cache_router.get("/priv", summary="获取当前 ai_dw_priv 缓存内容")
def get_ai_dw_priv_cache():
    data = get_cache()
    return {"success": True, "data": data}

@cache_router.post("/role/refresh", summary="刷新 ai_dw_role 缓存并返回最新数据")
def refresh_ai_dw_role_cache():
    data = refresh_role_cache()
    return {"success": True, "data": data}

@cache_router.get("/role", summary="获取当前 ai_dw_role 缓存内容")
def get_ai_dw_role_cache():
    data = get_role_cache()
    return {"success": True, "data": data}

@cache_router.post("/event/refresh", summary="刷新 ai_dw_event_code 缓存并返回最新数据")
def refresh_ai_dw_event_code_cache():
    data = refresh_event_code_cache()
    return {"success": True, "data": data}

@cache_router.get("/event", summary="获取当前 ai_dw_event_code 缓存内容")
def get_ai_dw_event_code_cache():
    data = get_event_code_cache()
    return {"success": True, "data": data}