import json
import logging
from sqlalchemy import and_, or_
from api.api import get_wiki_projects_with_role
from api.model.chat_session import ChatSession
from api.model.user_ext import UserExt
from api.model.wiki_user_role import WikiUserRole
from api.service.priv_checker import check_is_super_admin
from api.type.chat_request import WikiQARequest
from fastapi import APIRouter, HTTPException, Query, Request
from typing import Any, Optional
from datetime import datetime
from api.logging_config import setup_logging
from api.config import is_redis_enabled
from api.middleware.auth_middleware import get_current_user
from api.database.base import session_scope
from sqlmodel import Session, select
from api.model.wiki_info import WikiInfo
from api.model.app_wiki_rel import AppWikiRel
from api.type.sandbox_status import SandboxStatus
from api.utils.aes_utils import AESUtils
from api.sse_chat import chat_completions_stream, ChatCompletionRequest, app_chat_stream, AppChatCompletionRequest
import uuid
from api.cache.redis.manager import redis_manager
from api.sandbox.sandbox_service import sandbox_service
from .k8s_router import _get_app_info_by_id
import requests
from api.sandbox.kubernetes_service import get_kubernetes_service
from api.service.user_service import select_user_info_by_code

open_router = APIRouter(prefix="/api/open", tags=["open"])

setup_logging()
logger = logging.getLogger(__name__)

aes = AESUtils()

def json_response(success: bool = False, data: Any = "", err_code: str = "", err_msg: str = ""):
    if success:
        return {"success": success, "data": data}
    else:
        return {"success": success, "errCode": err_code, "errMsg": err_msg}

@open_router.get("/sandbox/permission/check", operation_id="check_sandbox_permission")
async def check_sandbox_permission(
    user_code: str = Query(..., description="用户代码，用于权限校验"),
    job_name: Optional[str] = Query(None, description="Job名称，可选，与pod_name/pod_ip三选一"),
    pod_name: Optional[str] = Query(None, description="Pod名称，可选"),
    pod_ip: Optional[str] = Query(None, description="Pod IP，可选")
):
    """
    根据 jobName 或 podName 或 podIP 与用户编码校验该用户是否拥有对应沙箱权限。

    规则说明（任一满足即认为有权限）：
    - Job 注释 annotations 中 user.code 与入参 user_code 完全匹配。
    - Job 标签 labels 中 user-code 与 user_code 规整化后匹配（遵循 k8s label 清洗规则）。
    - 若 Job 注释含有 user.id，则将 user_code 映射为用户ID后比对一致。
    - 兜底：若 Job 注释含有 wiki.id，且用户对该 wiki 具备访问权限，则认为有权限。

    优先级：job_name > pod_name > pod_ip。
    """
    # 获取 K8s 服务
    k8s = get_kubernetes_service()

    # 最终用于查询的 job 名称
    resolved_job_name: Optional[str] = None
    resolved_pod_name: Optional[str] = None
    resolved_pod_ip: Optional[str] = None

    try:
        # 1) 优先使用 job_name
        if job_name:
            resolved_job_name = job_name
        # 2) 其次使用 pod_name 推断 job_name（读取 pod 的 job-name 标签）
        elif pod_name:
            try:
                pod = k8s._core_v1_api.read_namespaced_pod(name=pod_name, namespace=k8s.namespace)
                resolved_pod_name = pod.metadata.name if pod and pod.metadata else None
                labels = (pod.metadata.labels or {}) if pod and pod.metadata else {}
                # Kubernetes Job 会为 Pod 添加 job-name 标签
                resolved_job_name = labels.get("job-name")
                resolved_pod_ip = pod.status.pod_ip if pod and pod.status else None
            except Exception as e:
                logger.error(f"根据pod_name读取Pod失败: {e}")
        # 3) 最后使用 pod_ip 查询 Pod 并推断 job_name
        elif pod_ip:
            try:
                # 使用 field_selector 过滤 IP，减少数据量
                pods = k8s._core_v1_api.list_namespaced_pod(namespace=k8s.namespace, field_selector=f"status.podIP={pod_ip}")
                item = pods.items[0] if pods and pods.items else None
                if item:
                    resolved_pod_name = item.metadata.name
                    labels = item.metadata.labels or {}
                    resolved_job_name = labels.get("job-name")
                    resolved_pod_ip = item.status.pod_ip
            except Exception as e:
                logger.error(f"根据pod_ip查询Pod失败: {e}")

        if not resolved_job_name:
            return json_response(success=True, data={
                "authorized": False,
                "reason": "未找到对应的Job，请检查入参（job_name/pod_name/pod_ip）",
                "job_name": None,
                "pod_name": resolved_pod_name,
                "pod_ip": pod_ip or resolved_pod_ip
            })

        # 读取 Job 详情（内含 annotations/labels）
        job_info = k8s.get_job(resolved_job_name)
        if not job_info:
            return json_response(success=True, data={
                "authorized": False,
                "reason": "未找到Job或已被删除",
                "job_name": resolved_job_name,
                "pod_name": resolved_pod_name,
                "pod_ip": pod_ip or resolved_pod_ip
            })

        annotations = job_info.get("annotations", {}) or {}
        labels = job_info.get("labels", {}) or {}

        # 1) 注释 user.code 精确匹配
        job_user_code_anno = annotations.get("user.code")
        if job_user_code_anno and str(job_user_code_anno) == str(user_code):
            return json_response(success=True, data={
                "authorized": True,
                "reason": "注释user.code匹配",
                "job_name": resolved_job_name,
                "pod_name": resolved_pod_name,
                "pod_ip": pod_ip or resolved_pod_ip
            })

        # 2) 标签 user-code 使用与K8s相同的清洗规则再比对
        try:
            sanitized_input_code = k8s._sanitize_label_value(user_code)
        except Exception:
            sanitized_input_code = str(user_code).lower()

        job_user_code_label = labels.get("user-code") or labels.get("user.code")
        if job_user_code_label and str(job_user_code_label) == str(sanitized_input_code):
            return json_response(success=True, data={
                "authorized": True,
                "reason": "标签user-code匹配",
                "job_name": resolved_job_name,
                "pod_name": resolved_pod_name,
                "pod_ip": pod_ip or resolved_pod_ip
            })

        # 3) 注释 user.id 与 user_code 映射后的用户ID匹配
        job_user_id_anno = annotations.get("user.id")
        if job_user_id_anno:
            try:
                user_info = select_user_info_by_code(user_code)
                if user_info and str(user_info.id) == str(job_user_id_anno):
                    return json_response(success=True, data={
                        "authorized": True,
                        "reason": "注释user.id匹配",
                        "job_name": resolved_job_name,
                        "pod_name": resolved_pod_name,
                        "pod_ip": pod_ip or resolved_pod_ip
                    })
            except Exception as e:
                logger.warning(f"根据user_code查询用户失败，跳过user.id校验: {e}")

        # 4) 兜底：若具备wiki访问权限也认为有权限（当Job未记录user信息时）
        wiki_id_anno = annotations.get("wiki.id")
        if wiki_id_anno:
            try:
                user_info = select_user_info_by_code(user_code)
                if user_info:
                    with session_scope() as session:
                        # 复用本文件的有效性校验逻辑
                        id_in_db, wiki_guid, _, _ = valid_wiki(session, user_info.id, str(wiki_id_anno))
                        if id_in_db is not None:
                            return json_response(success=True, data={
                                "authorized": True,
                                "reason": "用户具备wiki访问权限",
                                "job_name": resolved_job_name,
                                "pod_name": resolved_pod_name,
                                "pod_ip": pod_ip or resolved_pod_ip
                            })
            except Exception as e:
                logger.warning(f"校验wiki权限失败: {e}")

        # 未命中任何规则，视为无权限
        return json_response(success=True, data={
            "authorized": False,
            "reason": "未匹配到用户与Job的绑定信息",
            "job_name": resolved_job_name,
            "pod_name": resolved_pod_name,
            "pod_ip": pod_ip or resolved_pod_ip
        })
    except Exception as ex:
        logger.error(f"沙箱权限校验异常: {ex}")
        return json_response(success=False, err_code="COMMON_0000", err_msg="校验沙箱权限失败")

@open_router.post("/v1/chat/completions", operation_id="wiki_qa")
async def wiki_qa(data: WikiQARequest, request: Request):
    """
    提供deepwiki的产品知识问答服务，用户可以针对项目仓库进行提问，后台会检索仓库的相关代码，提供符合用户问题的答案

    入参描述:
    - provider: [可选] 模型提供商
        * 类型: str
        * 默认值: "gemini-cli"
        * 说明: AI模型提供商标识
        * 可选值: "gemini-cli"

    - model: [可选] 模型名称
        * 类型: str
        * 默认值: "gemini-2.5-flash"
        * 说明: 指定的提供商下的模型名称，当provider为gemini-cli时，model可以为gemini-2.5-flash、gemini-2.5-pro中的一个。当provider为whalecloud时，model可以为gemini-2.5-flash、gemini-2.5-pro、DeepSeek-R1等模型中的一个。
        * 示例: "gemini-2.5-pro", "gemini-2.5-flash"

    - content: [必填] 聊天消息列表
        * 类型: str
        * 说明: 当前提问的内容
        * 示例: whale-deepwiki这个项目有什么功能

    - repo_name: [必填] git仓库名称
        * 类型: str
        * 说明: git仓库的名称
        * 示例: "whale-deepwiki"
    
    - branch: [可选] git仓库的分支名称
        * 类型: str
        * 说明: git仓库的分支名称
        * 示例: "master"
    
    - wiki_id: [可选] wiki的唯一标识
        * 类型: str
        * 说明: wiki的唯一标识，如果不传，会根据repo_name、branch、repo_owner等字段推断
        * 示例: "3aa6058f-9ceb-483d-8520-f4f921aa6e2f"
    
    - session_id: [可选] 问答会话的唯一标识
        * 类型: str
        * 说明: 问答会话的唯一标识，如果不传，系统会自动创建一个有效期为30分钟的会话id
        * 示例: "3aa6058f-9ceb-483d-8520-f4f921aa6e2f"

    返回值:
    - 类型: StreamingResponse
    - 内容: 产品相关信息的问答结果

    $exampleData: whale-deepwiki这个项目有什么功能

    """
    logger.info(f"开始调用open_router的wiki_qa，参数: {data.app_id}, {data.app_code}, {data.wiki_id}, {data.model}, {data.repo_name}, {data.branch}, {data.repo_owner}")
    user = get_current_user()
    user_id = user.get("id")
    is_app_request = (
        hasattr(request.state, "request_auth_way")
        and getattr(request.state, "request_auth_way") in {"app_token", "app_user_token"}
        and getattr(request.state, "auth_successful", False)
    )
    ai_api_key = None
    app_id = None
    id, wiki_id, wiki_branch, wiki_repo_url = None, None, None, None
    with session_scope() as session:
        # 根据用户id获取用户个人设置里的ai_api_key（公司大模型token）
        user_ext: UserExt = session.exec(select(UserExt).where(UserExt.user_id == user_id)).first()
        if not user_ext or not user_ext.ai_api_key:
            return json_response(success=False, err_code="USER_0000", err_msg="Unable to find ai_api_key. Please confirm whether the user has set the ai_api_key in personal settings.")
        else:
            ai_api_key = aes.decrypt(user_ext.ai_api_key)
        # 获取用户可访问的wiki
        if data.wiki_id is None:
            id, wiki_id, wiki_branch, wiki_repo_url = get_access_wiki(session, user_id, data.repo_owner, data.repo_name, data.branch)
        else:
            id, wiki_id, wiki_branch, wiki_repo_url = valid_wiki(session, user_id, data.wiki_id)
        if not wiki_id:
            return json_response(success=False, err_code="WIKI_0000", err_msg=f"You do not have permission to access the {data.wiki_id or data.repo_name} project.")
        if is_app_request:
            app_id = getattr(request.state, "app_id", None)
            if app_id is None:
                return json_response(success=False, err_code="APP_0000", err_msg=f"App identity missing or invalid.")
            has_permission = session.exec(
                select(AppWikiRel.id)
                .where(AppWikiRel.app_id == app_id, AppWikiRel.wiki_id == id)
            ).first()
            if not has_permission:
                return json_response(success=False, err_code="WIKI_0000", err_msg=f"The app does not have permission to access this wiki.")
    # 获取wct会话session
    session_id = get_wct_session(user_id, wiki_id, app_id, request) if data.session_id is None else data.session_id

    messages = [
        {"id": f"chatcmpl-{str(uuid.uuid4())}", "role": "user", "content": data.content}
    ]
    if data.system_prompt:
        messages.insert(0, {"id": f"chatcmpl-{str(uuid.uuid4())}", "role": "system", "content": data.system_prompt})
    if is_app_request:
        app_id = request.state.app_id
        app_info = _get_app_info_by_id(app_id)
        app_code = app_info.get("app_code")
        is_mcp_source = bool(app_code) and str(app_code).strip().lower() == "edo"
        app_chat_request = AppChatCompletionRequest(
            model=data.model,
            messages=messages,
            provider="gemini-cli",
            session_id=session_id,
            api_key=ai_api_key,
            app_code=app_code,
            app_id=str(app_id),
            user_id=user_id,
            wiki_id=wiki_id,
            timeout=600.0,
            caller=2 if is_mcp_source else 3
        )
        return await app_chat_stream(app_chat_request, request)

    else:
        chat_request = ChatCompletionRequest(
            model=data.model,
            messages=messages,
            repo_url=wiki_repo_url,
            provider=data.provider,
            session_id=session_id,
            api_key=ai_api_key,
            branch=wiki_branch,
            wiki_id=wiki_id,
            caller=3
        )
        return await chat_completions_stream(chat_request, request)

@open_router.get("/wiki_list", operation_id="wiki_list")
async def wiki_list(request: Request):
    """
    获取wiki列表（获取项目列表）
    """
    user = get_current_user()
    is_app_request = (
        hasattr(request.state, "request_auth_way")
        and getattr(request.state, "request_auth_way") in {"app_token", "app_user_token"}
        and getattr(request.state, "auth_successful", False)
    )
    try:
        with session_scope() as session:
            if is_app_request:
                app_id = getattr(request.state, "app_id", None)
                if app_id is None:
                    return json_response(success=False, err_code="APP_0000", err_msg=f"App identity missing or invalid.")
                result = session.exec(
                    select(WikiInfo.repo_url, WikiInfo.repo_owner, WikiInfo.repo_name, WikiInfo.branch)
                    .join(AppWikiRel, AppWikiRel.wiki_id == WikiInfo.id)
                    .where(AppWikiRel.app_id == app_id, WikiInfo.status != "failed")
                ).all()
            else:
                user_id = user.get("id")
                is_super_admin = check_is_super_admin(session, user_id)
                if is_super_admin:
                    result = session.exec((select(WikiInfo.repo_url, WikiInfo.repo_owner, WikiInfo.repo_name, WikiInfo.branch).where(WikiInfo.status != "failed"))).all()
                else:
                    result = session.exec((select(WikiInfo.repo_url, WikiInfo.repo_owner, WikiInfo.repo_name, WikiInfo.branch)
                        .join(WikiUserRole, and_(WikiUserRole.wiki_id == WikiInfo.id, or_(WikiUserRole.user_id == user_id, WikiInfo.visibility == 1)))
                        .where(WikiInfo.status != "failed"))).all()
            data = [{"repo_url": repo_url, "repo_owner": repo_owner, "repo_name": repo_name, "repo_branch": repo_branch} for (repo_url, repo_owner, repo_name, repo_branch) in result]
            return json_response(success=True, data=data)
    except Exception as ex:
        logger.error(f"{ex}")
        return json_response(success=False, err_code="COMMON_0000", err_msg=f"Failed to query wiki list")

@open_router.get("/projects-with-role")
async def projects_with_role(
    keyword: Optional[str] = Query(None, description="搜索关键字"),
    view_type: int = Query(None, description="视图类型"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=100, description="每页大小"),
):
    try:
        result = await get_wiki_projects_with_role(keyword, view_type, page, page_size)
        if result.get("code", 0) == 200:
            return json_response(success=True, data=result.get("data"))
    except Exception as ex:
        logger.error(f"Failed to query wiki list, cause by: {ex}")
        return json_response(success=False, err_code="COMMON_0000", err_msg=f"Failed to query wiki list")


@open_router.delete("/wct-cli-session")
async def clear_wct_cli_session(session_id: str, request: Request):
    user = get_current_user()
    is_app_request = (
        hasattr(request.state, "request_auth_way")
        and getattr(request.state, "request_auth_way") in {"app_token", "app_user_token"}
        and getattr(request.state, "auth_successful", False)
    )
    try:
        sandbox_url = None
        if is_app_request:
            app_id = request.state.app_id
            app_info = _get_app_info_by_id(app_id)
            app_code = app_info.get("app_code")
            sandbox_url = sandbox_service.get_app_sandbox_url(app_code or str(app_id))
        else:
            with session_scope() as session:
                repo_url, branch = session.exec(select(WikiInfo.repo_url, WikiInfo.branch).join(ChatSession, and_(ChatSession.wiki_id == WikiInfo.id, ChatSession.chat_sid == session_id))).first()
                if repo_url and branch:
                    status_info = await sandbox_service.get_sandbox_detailed_status(user.get("user_code", ""), repo_url, branch)
                    job_status = status_info.get("status", "UNKNOWN")
                    if job_status != SandboxStatus.READY.value:
                        return json_response(success=False, err_code="APP_0001", err_msg="The sandbox is not ready yet")
                    sandbox_url = sandbox_service.get_sandbox_url(user.get("user_code", ""), repo_url, branch)
        if sandbox_url:
            clear_session_url = sandbox_url.replace("completions", f"sessions/{session_id}")
            logger.info(f"Url for clearing chat session: {clear_session_url}")
            response = requests.delete(clear_session_url)
            if response.ok:
                return json_response(success=True, data="Clear wct-cli chat session successfully")
            else:
                return json_response(success=False, err_code="COMMON_0001", err_msg="Failed to clear wct-cli chat session")
        else:
            logger.error(f"Failed to retrieve sandbox url")
            return {"success": False, "errCode": "COMMON_0000", "errMsg": "Failed to retrieve sandbox url"}
    except Exception as ex:
        logger.error(f"Failed to clean session, cause by: {ex}")
        return json_response(success=False, err_code="COMMON_0000", err_msg="Failed to clear wiki chat session")

@open_router.get("/check-ai-token")
async def check_ai_token():
    user = get_current_user()
    try:
        ai_api_key = None
        with session_scope() as session:
            # 根据用户id获取用户个人设置里的ai_api_key（公司大模型token）
            user_ext: UserExt = session.exec(select(UserExt).where(UserExt.user_id == user.get("id"))).first()
            if not user_ext or not user_ext.ai_api_key:
                return json_response(success=False, err_code="USER_0000", err_msg="The AI token does not exist.")
            else:
                ai_api_key = aes.decrypt(user_ext.ai_api_key)
        if test_token(ai_api_key):
            return json_response(success=True, data="The AI token is valid")
        else:
            return json_response(success=False, err_code="USER_0001", err_msg=f"The AI token '{ai_api_key}' exists, but is invalid.")
    except Exception as ex:
        logger.error(f"Failed to check AI token, cause by: {ex}")
        return json_response(success=False, err_code="COMMON_0000", err_msg="Failed to check AI token")

def test_token(token: str):
    url = "https://lab.iwhalecloud.com/gpt-proxy/v1/chat/completions"
    payload = json.dumps({
        "model": "doubao-lite-32k",
        "messages": [
            {
                "role": "system",
                "content": "You are a helpful assistant."
            },
            {
                "role": "user",
                "content": "Hello!"
            }
        ],
        "stream": "false"
    })
    headers = {
        'Authorization': f'Bearer {token}',
        'content-type': 'application/json'
    }
    response = requests.request("POST", url, headers=headers, data=payload)
    return response.status_code == 200

def valid_wiki(session: Session, user_id: int, wiki_id: str):
    is_super_admin = check_is_super_admin(session, user_id)
    filter = []
    filter.append(and_(WikiInfo.wiki_id == wiki_id, WikiInfo.status != "failed"))
    if is_super_admin:
        result = session.exec((select(WikiInfo.id, WikiInfo.wiki_id, WikiInfo.branch, WikiInfo.repo_url).where(and_(*filter)))).first()
    else:
        result = session.exec((select(WikiInfo.id, WikiInfo.wiki_id, WikiInfo.branch, WikiInfo.repo_url)
            .join(WikiUserRole, and_(WikiUserRole.wiki_id == WikiInfo.id, or_(WikiUserRole.user_id == user_id, WikiInfo.visibility == 1)))
            .where(and_(*filter)))).first()
    return result if result is not None else (None, None, None, None)

def get_access_wiki(session: Session, user_id: int, repo_owner: str, repo_name: str, branch: Optional[str]):
    """
    根据用户id和仓库名称查找用户有权限访问的wiki
    """
    is_super_admin = check_is_super_admin(session, user_id)
    filter = []
    filter.append(and_(WikiInfo.repo_name == repo_name, WikiInfo.status != "failed"))
    if branch:
        filter.append(WikiInfo.branch == branch)
    if repo_owner:
        filter.append(WikiInfo.repo_owner == repo_owner)
    if is_super_admin:
        result = session.exec((select(WikiInfo.id, WikiInfo.wiki_id, WikiInfo.branch, WikiInfo.repo_url).where(and_(*filter)))).first()
    else:
        result = session.exec((select(WikiInfo.id, WikiInfo.wiki_id, WikiInfo.branch, WikiInfo.repo_url)
            .join(WikiUserRole, and_(WikiUserRole.wiki_id == WikiInfo.id, or_(WikiUserRole.user_id == user_id, WikiInfo.visibility == 1)))
            .where(and_(*filter)))).first()
    return result if result is not None else (None, None, None, None)

def get_wct_session(user_id: int, wiki_id: str, app_id: Optional[int], request: Request):
    """
    从缓存里查找session_id（wct-cli内部会话id，用来维护上下文），如果redis服务不可用，则暂时缓存在应用全局变量里
    """
    context_key = f"wiki_qa_context_{app_id}_{user_id}_{wiki_id}" if app_id is not None else f"wiki_qa_context_{user_id}_{wiki_id}"
    session_id = None
    if is_redis_enabled():
        client = redis_manager.get_client()
        if client:
            context = client.hgetall(context_key)
            if context:
                session_id = context.get("session_id")
                client.expire(context_key, 30 * 60)
            else:
                session_id = str(uuid.uuid4())
                client.hset(context_key, {"session_id": session_id}, 30 * 60)
    else:
        wiki_qa_context = request.app.state.wiki_qa_context
        if context_key in wiki_qa_context:
            session_id = wiki_qa_context[context_key].get("session_id")
            wiki_qa_context[context_key]["last_access_time"] = datetime.now()
        else:
            session_id = str(uuid.uuid4())
            wiki_qa_context[context_key] = { "session_id": session_id, "last_access_time": datetime.now() }
    return session_id
