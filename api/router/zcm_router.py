import json
import logging
from fastapi import APIRouter, HTTPException, Depends, Query
import requests

from api.config import get_zcm_devspace_config
from api.logging_config import setup_logging
from api.middleware.language_middleware import get_translation

zcm_router = APIRouter(prefix="/api/zcm", tags=["zcm"])

setup_logging()
logger = logging.getLogger(__name__)

@zcm_router.get("/repo/metadata")
async def get_repo_metadata(repo_url: str):
    """
    通过仓库地址获取仓库所属的产品，产品线，解决方案
    """
    base_url, token = get_zcm_devspace_config()
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    try:
        if repo_url:
            url = f"{base_url}/rpc/v3/repos/metadata?cloneUrl={repo_url}"
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            body = response.json()
            if body and body.get("code") == "9999":
                data = body.get("data")
                return data
        else:
            raise HTTPException(status_code=400, detail=get_translation("api.zcm.errors.invalidRepoUrl"))
    except Exception as ex:
        logger.error(f"Failed to get repo metadata by {repo_url}, cause by: {ex}")
        if isinstance(ex, HTTPException):
            raise ex
        else:
            raise HTTPException(status_code=500, detail=get_translation("api.zcm.errors.failedToGetRepoMetadata"))

@zcm_router.get("/product-lines")
async def get_products(search: str = ""):
    """
    根据产品线名称模糊查询产品线列表
    """
    base_url, token = get_zcm_devspace_config()
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    try:
        url = f"{base_url}/rpc/v3/master-data/product-line-list"
        request_data = {
            "productLineNameSearch": search,
            "minMatchLength": 0
        }
        response = requests.post(url, data=json.dumps(request_data), headers=headers)
        response.raise_for_status()
        body = response.json()
        if body and body.get("code") == "9999":
            data = body.get("data") or []
            return data
    except Exception as ex:
        logger.error(f"Failed to get products, cause by: {ex}")
        if isinstance(ex, HTTPException):
            raise ex
        else:
            raise HTTPException(status_code=500, detail=get_translation("api.zcm.errors.failedToGetProductLines"))

@zcm_router.get("/products")
async def get_products(search: str = ""):
    """
    根据产品名称模糊查询产品列表
    """
    base_url, token = get_zcm_devspace_config()
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    try:
        url = f"{base_url}/rpc/v3/master-data/product-list"
        request_data = {
            "productNameSearch": search,
            "minMatchLength": 0
        }
        response = requests.post(url, data=json.dumps(request_data), headers=headers)
        response.raise_for_status()
        body = response.json()
        if body and body.get("code") == "9999":
            data = body.get("data") or []
            return data
    except Exception as ex:
        logger.error(f"Failed to get products, cause by: {ex}")
        if isinstance(ex, HTTPException):
            raise ex
        else:
            raise HTTPException(status_code=500, detail=get_translation("api.zcm.errors.failedToGetProducts"))

@zcm_router.get("/product-versions")
async def get_products(search: str = ""):
    """
    根据产品版本名称模糊查询产品版本列表
    """
    base_url, token = get_zcm_devspace_config()
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    try:
        url = f"{base_url}/rpc/v3/master-data/product-version-list"
        request_data = {
            "keyword": search,
            "minMatchLength": 0
        }
        response = requests.post(url, data=json.dumps(request_data), headers=headers)
        response.raise_for_status()
        body = response.json()
        if body and body.get("code") == "9999":
            data = body.get("data") or []
            return data
    except Exception as ex:
        logger.error(f"Failed to get products, cause by: {ex}")
        if isinstance(ex, HTTPException):
            raise ex
        else:
            raise HTTPException(status_code=500, detail=get_translation("api.zcm.errors.failedToGetProductVersions"))

@zcm_router.get("/distributions")
async def get_products(search: str = ""):
    """
    根据发布包名称模糊查询发布包列表
    """
    base_url, token = get_zcm_devspace_config()
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    try:
        url = f"{base_url}/rpc/v3/master-data/distribution-list"
        request_data = {
            "keyword": search,
            "minMatchLength": 0
        }
        response = requests.post(url, data=json.dumps(request_data), headers=headers)
        response.raise_for_status()
        body = response.json()
        if body and body.get("code") == "9999":
            data = body.get("data") or []
            return data
    except Exception as ex:
        logger.error(f"Failed to get products, cause by: {ex}")
        if isinstance(ex, HTTPException):
            raise ex
        else:
            raise HTTPException(status_code=500, detail=get_translation("api.zcm.errors.failedToGetDistributions"))

@zcm_router.get("/solutions")
async def get_solutions(search: str = ""):
    """
    根据解决方案名称模糊查询解决方案列表
    """
    base_url, token = get_zcm_devspace_config()
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    try:
        url = f"{base_url}/rpc/v3/master-data/solution-list"
        request_data = {
            "productNameSearch": search,
            "minMatchLength": 0
        }
        response = requests.post(url, data=json.dumps(request_data), headers=headers)
        response.raise_for_status()
        body = response.json()
        if body and body.get("code") == "9999":
            data = body.get("data") or []
            return data
    except Exception as ex:
        logger.error(f"Failed to get solutions, cause by: {ex}")
        if isinstance(ex, HTTPException):
            raise ex
        else:
            raise HTTPException(status_code=500, detail=get_translation("api.zcm.errors.failedToGetSolutions"))