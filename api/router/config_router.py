from fastapi import APIRouter, Request, Depends, HTTPException, Body
from fastapi.responses import JSONResponse
import base64
import logging
from datetime import datetime
import json
import requests

from api.config import configs, load_generator_config
from api.database.base import session_scope
from api.dto.common_dto import ModelConfig, Model, Provider, TestModelRequest, TestModelResponse
from api.dto.config_settings_request import ConfigSettingsRequest
from api.middleware.auth_middleware import get_current_user
from api.middleware.audit_log_middleware import audit_logger
from api.middleware.language_middleware import get_translation
from api.model.user_ext import UserExt
from api.service.priv_checker import check_is_super_admin
from api.service.user_service import select_user_ext, add_user_ext, update_user_ext
from api.utils.aes_utils import AESUtils

logger = logging.getLogger(__name__)

config_router = APIRouter(prefix="/api/config", tags=["config"])

aes = AESUtils()

async def validate_api_key(api_key: str) -> bool:
    """验证API Key是否有效"""
    try:
        # 使用现有的testConnection接口逻辑
        from api.dto.common_dto import TestModelRequest
        
        # 构建测试请求
        test_request = TestModelRequest(
            model="doubao-lite-32k",  # 使用默认模型
            authorization=api_key
        )
        
        # 调用现有的test_model_connection函数
        from api.api import test_model_connection
        result = await test_model_connection(test_request)
        
        return result.get("success", False)
    except Exception as e:
        logger.error(f"API Key validation failed: {e}")
        return False

async def validate_whale_dev_cloud_token(token: str) -> bool:
    """验证WhaleDevCloud Token是否有效"""
    try:
        # 使用现有的verifyToken接口逻辑
        from api.dto.common_dto import WhaleDevCloudTokenRequest
        
        # 构建测试请求
        verify_request = WhaleDevCloudTokenRequest(token=token)
        
        # 调用现有的verify_whaledevcloud_token函数
        from api.api import verify_whaledevcloud_token
        result = await verify_whaledevcloud_token(verify_request)
        
        return result.get("success", False)
    except Exception as e:
        logger.error(f"WhaleDevCloud Token validation failed: {e}")
        return False

@config_router.get("/lang")
async def get_lang_config():
    """获取语言配置"""
    return configs["lang_config"]

@config_router.get("/global")
async def get_global_config(current_user: dict = Depends(get_current_user)):
    """获取全局配置"""
    with session_scope() as session:
        if not check_is_super_admin(session, current_user.get("id")):
            return None
    return configs["global_config"]

@config_router.post("/settings")
async def config_settings(request: ConfigSettingsRequest, audit_log=Depends(audit_logger)):
    """保存用户配置设置"""
    user_info = get_current_user()
    
    # 解码并验证参数
    ai_api_key = None
    dev_cloud_token = None
    
    if request.ai_api_key:
        ai_api_key = base64.b64decode(request.ai_api_key).decode('utf-8')
        # 验证API Key
        if not await validate_api_key(ai_api_key):
            raise HTTPException(status_code=400, detail="API Key验证失败，请检查API Key是否正确")
    
    if request.dev_cloud_token:
        dev_cloud_token = base64.b64decode(request.dev_cloud_token).decode('utf-8')
        # 验证WhaleDevCloud Token
        if not await validate_whale_dev_cloud_token(dev_cloud_token):
            raise HTTPException(status_code=400, detail="WhaleDevCloud Token验证失败，请检查Token是否正确")

    # 使用aes算法加密token
    if ai_api_key:
        ai_api_key = aes.encrypt(ai_api_key)
    if dev_cloud_token:
        dev_cloud_token = aes.encrypt(dev_cloud_token)
    
    user_ext = select_user_ext(user_info.get('id'))
    if user_ext is None:
        add_user_ext(UserExt(user_id=user_info.get('id'), ai_api_key=ai_api_key, dev_cloud_token=dev_cloud_token, created_by=user_info.get('id'), created_date=datetime.now()))
    else:
        # 只更新提供的字段
        if ai_api_key is not None:
            user_ext.ai_api_key = ai_api_key
        if dev_cloud_token is not None:
            user_ext.dev_cloud_token = dev_cloud_token
        user_ext.update_by = user_info.get('id')
        user_ext.update_date = datetime.now()
        update_user_ext(user_ext, user_ext.id)
    
    # 设置审计日志数据
    audit_log(code="CONFIG_SETTINGS", party_type="用户", party_id=user_info.get('id'), party_name=user_info.get('user_name', ""), \
              party_code=user_info.get('user_code', ""), dept_id=user_info.get('dept_id'), dept=user_info.get('dept'))
    
    return JSONResponse(
        status_code=200,
        content={"success": True}
    )

@config_router.get("/settings")
async def get_key():
    """获取用户配置设置"""
    user_info = get_current_user()
    user_ext = select_user_ext(user_info.get('id'))
    if user_ext and user_ext.ai_api_key: 
        ai_api_key = aes.decrypt(user_ext.ai_api_key)
        ai_api_key = base64.b64encode(ai_api_key.encode())
    else:
        ai_api_key = b""
    if user_ext and user_ext.dev_cloud_token:
        dev_cloud_token = aes.decrypt(user_ext.dev_cloud_token)
        dev_cloud_token = base64.b64encode(dev_cloud_token.encode())
    else:
        dev_cloud_token = b""
    return JSONResponse(
        status_code=200,
        content={"ai_api_key": str(ai_api_key, 'utf-8'), "dev_cloud_token": str(dev_cloud_token, 'utf-8')}
    )

@config_router.get("/models", response_model=ModelConfig)
async def get_model_config_endpoint():
    """
    Get the model configuration from the generator.json file.
    """
    try:
        # Reload the generator config to ensure it's fresh
        generator_config = load_generator_config()
        
        if not generator_config:
            raise HTTPException(status_code=500, detail=get_translation("api.errors.generatorConfigNotLoaded"))

        providers_list = []
        # Ensure 'providers' key exists and is a dictionary
        if "providers" in generator_config and isinstance(generator_config["providers"], dict):
            for provider_id, provider_data in generator_config["providers"].items():
                models_list = []
                # Ensure 'models' key exists and is a dictionary
                if "models" in provider_data and isinstance(provider_data["models"], dict):
                    for model_id, model_info in provider_data["models"].items():
                        # Use model_id as both id and name, or create a more descriptive name if needed
                        models_list.append(Model(id=model_id, name=model_id))
                
                providers_list.append(Provider(
                    id=provider_id,
                    name=provider_id.capitalize(), # Capitalize the provider ID for a friendlier name
                    models=models_list,
                    supportsCustomModel=provider_data.get("supportsCustomModel", False)
                ))

        default_provider = generator_config.get("default_provider", "")

        return ModelConfig(providers=providers_list, defaultProvider=default_provider)

    except Exception as e:
        logger.error(f"Error reading or parsing generator config: {e}")
        raise HTTPException(status_code=500, detail=get_translation("api.errors.internalServerErrorModelConfig"))
