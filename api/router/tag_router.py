from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime

from api.database.base import session_scope
from api.service.priv_checker import check_is_super_admin
from api.service.tag_service import (
    get_system_tags,
    get_tag_by_name, 
    create_tag,
    query_tags_with_filters, 
    update_tag, 
    delete_tag,
    get_tag_by_id,
    get_tag_by_name_like,
    disable_tag as disable_tag_service,
    enable_tag as enable_tag_service,
    get_all_tags
)
from api.model.tag import Tag
from api.middleware.auth_middleware import get_current_user
from api.middleware.language_middleware import get_translation
from api.service.wiki_tag_service import add_wiki_tag, batch_delete_wiki_tag_by_tag_id, delete_wiki_tag, get_wiki_tag_by_wiki_id_and_tag_id, get_wiki_tags_with_tag_info

# 创建路由器
tag_router = APIRouter(prefix="/api/tags", tags=["tags"])

# Pydantic模型定义
class TagCreateRequest(BaseModel):
    """创建标签请求模型"""
    name: str = Field(..., max_length=60, description="标签名称")
    type: int = Field(..., ge=1, le=2, description="标签类型（1:系统 2:用户）")
    color: str = Field(..., max_length=16, description="标签颜色（如#FF0000）")
    comments: Optional[str] = Field(None, max_length=255, description="标签描述")
    module_type: int = Field(1, ge=1, description="标签归属模块, 1:deepwiki")
    state: int = Field(1, ge=0, le=1, description="标签状态, 1:有效 0:失效")

class TagUpdateRequest(BaseModel):
    """更新标签请求模型"""
    name: str = Field(..., max_length=60, description="标签名称")
    type: int = Field(..., ge=1, le=2, description="标签类型（1:系统 2:用户）")
    color: str = Field(..., max_length=16, description="标签颜色（如#FF0000）")
    comments: Optional[str] = Field(None, max_length=255, description="标签描述")
    module_type: int = Field(1, ge=1, description="标签归属模块, 1:deepwiki")
    state: int = Field(1, ge=0, le=1, description="标签状态, 1:有效 0:失效")

class TagResponse(BaseModel):
    """标签响应模型"""
    id: int
    name: str
    type: int
    color: str
    comments: Optional[str]
    module_type: int
    state: int
    created_by: int
    created_date: datetime
    update_by: Optional[int]
    update_date: Optional[datetime]

    class Config:
        from_attributes = True

class TagListResponse(BaseModel):
    """标签列表响应模型"""
    code: str = "200"
    message: str = "success"
    data: List[TagResponse]
    total: int

class TagDetailResponse(BaseModel):
    """标签详情响应模型"""
    code: str = "200"
    message: str = "success"
    data: TagResponse

class TagCreateResponse(BaseModel):
    """创建标签响应模型"""
    code: str = "200"
    message: str = "success"
    data: TagResponse

class TagUpdateResponse(BaseModel):
    """更新标签响应模型"""
    code: str = "200"
    message: str = "success"
    data: TagResponse

class TagDeleteResponse(BaseModel):
    """删除标签响应模型"""
    code: str = "200"
    message: str = "success"

class WikiTagDataResponse(BaseModel):
    """wiki标签响应模型"""
    code: str = "200"
    message: str = "success"
    data: List[TagResponse] = []

class WikiTagInfoResponse(BaseModel):
    """wiki标签信息响应模型 - 匹配实际返回的数据结构"""
    id: int
    wiki_id: int
    tag_id: int
    name: str
    type: int
    color: str
    comments: Optional[str]

class WikiTagDataListResponse(BaseModel):
    """wiki标签列表响应模型"""
    code: str = "200"
    message: str = "success"
    data: List[WikiTagInfoResponse] = []

class WikiTagResponse(BaseModel):
    """wiki标签响应模型"""
    code: str = "200"
    message: str = "success"

class WikiTagRequest(BaseModel):
    """添加wiki标签请求模型"""
    wiki_id: int = Field(..., description="wiki ID")
    tag_ids: List[int] = Field(..., description="标签ID列表")

class WikiTagDeleteRequest(BaseModel):
    """删除wiki标签请求模型"""
    wiki_id: int = Field(..., description="wiki ID")
    tag_id: int = Field(..., description="标签ID")

# API端点定义
@tag_router.get("/tag", response_model=TagListResponse)
async def get_tags(
    name: Optional[str] = Query(None, description="标签名称（模糊查询）"),
    current_user: dict = Depends(get_current_user)
):
    """
    获取标签列表
    """
    try:
        with session_scope() as session:
            is_super_admin = check_is_super_admin(session, current_user.get("id"))
            if is_super_admin:
                all_tags = get_all_tags(session)
            else:
                all_tags = get_tag_by_name_like(session, name=name, user_id=current_user.get("id"))
            
            return TagListResponse(
                data=[TagResponse.from_orm(tag) for tag in all_tags],
                total=len(all_tags)
            )
    except Exception as e:
        raise HTTPException(status_code=500, detail=get_translation("api.tag.errors.failedToGetTagList"))

@tag_router.post("/tag", response_model=TagCreateResponse)
async def create_new_tag(
    tag_data: TagCreateRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    创建新标签
    """
    try:
        with session_scope() as session:
            # 检查标签名称是否已存在
            existing_tag = get_tag_by_name(session, name=tag_data.name, user_id=current_user.get("id"))
            if existing_tag:
                raise HTTPException(status_code=400, detail=get_translation("api.tag.errors.tagNameExists"))
            
            # 创建标签
            tag = create_tag(
                session=session,
                name=tag_data.name,
                type=tag_data.type,
                color=tag_data.color,
                created_by=current_user.get("id"),
                comments=tag_data.comments,
                module_type=tag_data.module_type,
                state=tag_data.state
            )
            
            return TagCreateResponse(
                message=get_translation("api.tag.messages.tagCreatedSuccessfully"),
                data=TagResponse.from_orm(tag)
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=get_translation("api.tag.errors.failedToCreateTag"))

@tag_router.put("/tag/{tag_id}", response_model=TagUpdateResponse)
async def update_existing_tag(
    tag_id: int,
    tag_data: TagUpdateRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    更新标签
    """
    try:
        with session_scope() as session:
            # 检查标签是否存在
            existing_tag = get_tag_by_id(session, id=tag_id)
            if not existing_tag:
                raise HTTPException(status_code=404, detail=get_translation("api.tag.errors.tagNotExists"))
            
            if tag_data.name != existing_tag.name:
                # 检查新名称是否与其他标签冲突
                name_conflict = get_tag_by_name(session, name=tag_data.name, user_id=current_user.get("id"))
                if name_conflict:
                    raise HTTPException(status_code=400, detail=get_translation("api.tag.errors.tagNameExists"))
            
            # 更新标签
            tag = update_tag(
                session=session,
                tag_id=tag_id,
                name=tag_data.name,
                type=tag_data.type,
                color=tag_data.color,
                comments=tag_data.comments,
                module_type=tag_data.module_type,
                state=tag_data.state,
                update_by=current_user.get("id")
            )
            
            return TagUpdateResponse(
                message=get_translation("api.tag.messages.tagUpdatedSuccessfully"),
                data=TagResponse.from_orm(tag)
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=get_translation("api.tag.errors.failedToUpdateTag"))

@tag_router.delete("/tag/{tag_id}", response_model=TagDeleteResponse)
async def delete_existing_tag(
    tag_id: int,
    current_user: dict = Depends(get_current_user)
):
    """
    删除标签
    """
    try:
        with session_scope() as session:
            # 检查标签是否存在
            existing_tag = get_tag_by_id(session, id=tag_id)
            if not existing_tag:
                raise HTTPException(status_code=404, detail=get_translation("api.tag.errors.tagNotExists"))
            
            # 批量删除标签的关联关系
            batch_delete_wiki_tag_by_tag_id(session, tag_id)
            
            # 删除标签
            delete_tag(session=session, id=tag_id, update_by=current_user.get("id"))
            
            return TagDeleteResponse(
                message=get_translation("api.tag.messages.tagDeletedSuccessfully")
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=get_translation("api.tag.errors.failedToDeleteTag"))

@tag_router.put("/tag/{tag_id}/disable", response_model=TagUpdateResponse)
def disable_tag(tag_id: int, current_user: dict = Depends(get_current_user)):
    """
    禁用标签
    """
    try:
        with session_scope() as session:
            existing_tag = get_tag_by_id(session, id=tag_id)
            if not existing_tag:
                raise HTTPException(status_code=404, detail=get_translation("api.tag.errors.tagNotExists"))
            disable_tag_service(session=session, id=tag_id, update_by=current_user.get("id"))
            return TagUpdateResponse(data=TagResponse.from_orm(existing_tag))
    except Exception as e:
        raise HTTPException(status_code=500, detail=get_translation("api.tag.errors.failedToDisableTag"))
    
@tag_router.put("/tag/{tag_id}/enable", response_model=TagUpdateResponse)
def enable_tag(tag_id: int, current_user: dict = Depends(get_current_user)):
    """
    启用标签
    """
    try:
        with session_scope() as session:
            existing_tag = get_tag_by_id(session, id=tag_id)
            if not existing_tag:
                raise HTTPException(status_code=404, detail=get_translation("api.tag.errors.tagNotExists"))
            enable_tag_service(session=session, id=tag_id, update_by=current_user.get("id"))
            return TagUpdateResponse(data=TagResponse.from_orm(existing_tag))
    except Exception as e:
        raise HTTPException(status_code=500, detail=get_translation("api.tag.errors.failedToEnableTag"))

@tag_router.get("/wiki/{wiki_id}", response_model=WikiTagDataListResponse)
def get_wiki_tags(wiki_id: int, current_user: dict = Depends(get_current_user)):
    """
    获取wiki标签
    """
    with session_scope() as session:
        wiki_tags_data = get_wiki_tags_with_tag_info(session, wiki_id)
        # 转换数据为WikiTagInfoResponse对象
        wiki_tags = [WikiTagInfoResponse(**tag_data) for tag_data in wiki_tags_data]
    return WikiTagDataListResponse(data=wiki_tags)

@tag_router.post("/wiki", response_model=WikiTagResponse)
def add_wiki_tags(request: WikiTagRequest, current_user: dict = Depends(get_current_user)):
    """
    添加wiki标签
    """
    try:
        with session_scope() as session:
            wiki_tags = get_wiki_tags_with_tag_info(session, request.wiki_id)
            for tag in wiki_tags:
                if tag.get("tag_id") in request.tag_ids:
                    raise HTTPException(status_code=400, detail=get_translation("api.tag.errors.wikiTagAlreadyExists"))
            add_wiki_tag(session, request.wiki_id, request.tag_ids, current_user.get("id"))
        return WikiTagResponse()
    except Exception as e:
        raise HTTPException(status_code=500, detail=get_translation("api.tag.errors.failedToAddWikiTag"))

@tag_router.delete("/wiki", response_model=WikiTagResponse)
def delete_wiki_tags(request: WikiTagDeleteRequest, current_user: dict = Depends(get_current_user)):
    """
    删除wiki标签
    """
    try:
        with session_scope() as session:
            wiki_tag = get_wiki_tag_by_wiki_id_and_tag_id(session, request.wiki_id, request.tag_id)
            if wiki_tag:
                delete_wiki_tag(session, wiki_tag.id)
            else:
                raise HTTPException(status_code=404, detail=get_translation("api.tag.errors.wikiTagNotExists"))
        return WikiTagResponse()
    except Exception as e:
        raise HTTPException(status_code=500, detail=get_translation("api.tag.errors.failedToDeleteWikiTag"))

