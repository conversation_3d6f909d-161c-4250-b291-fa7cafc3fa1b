import logging
import os
import asyncio
from fastapi import APIRouter, Request, Depends, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import json

from api.logging_config import setup_logging
from api.openai_client import OpenAIClient
from adalflow.core.types import ModelType
from api.middleware.auth_middleware import get_current_user
from api.utils.aes_utils import AESUtils
from api.database.base import session_scope
from api.model.user_ext import UserExt
from sqlmodel import select


meta_router = APIRouter(prefix="/api/optimize", tags=["optimize"])

setup_logging()
logger = logging.getLogger(__name__)

aes = AESUtils()

class OptimizeRequest(BaseModel):
    original_query: str
    model: str
    

@meta_router.post("/question", operation_id="question")
async def optimize_query(data: OptimizeRequest, request: Request, current_user: dict = Depends(get_current_user)):
    """
    优化用户查询，将模糊的问题转换为更精确、结构化的查询

    入参描述:
    - original_query: [必填] 原始问题
        * 类型: str
        * 说明: 原始问题
        * 示例: "这个怎么用？"

    - model: [必填] 模型名称
        * 类型: str
        * 说明: 模型名称
        * 示例: "gemini-2.5-pro", "gemini-2.5-flash"

    返回值:
    - 类型: StreamingResponse
    - 内容: 流式文本响应（优化后的查询内容）

    """
    try:
        logger.info(f"开始优化查询，原始问题: {data.original_query}, 模型: {data.model}")

        if not current_user:
            logger.error("用户未登录")
            raise HTTPException(status_code=401, detail="User not logged in")
        
        # 获取API密钥
        id = current_user.get('id')
        api_key = None
        with session_scope() as session:
            user_ext: UserExt = session.exec(select(UserExt).where(UserExt.user_id == id)).first()
            if user_ext and user_ext.ai_api_key:
                api_key = aes.decrypt(user_ext.ai_api_key)
        if not api_key:
            logger.error("API密钥未提供")
            raise HTTPException(status_code=400, detail="API key is required")
        
        # 读取元提示词
        current_dir = os.path.dirname(os.path.abspath(__file__))
        prompts_dir = os.path.join(os.path.dirname(current_dir), "prompts", "editablePrompts")
        meta_prompts_path = os.path.join(prompts_dir, "meta_prompts.md")
        
        if not os.path.exists(meta_prompts_path):
            logger.error(f"元提示词文件不存在: {meta_prompts_path}")
            raise HTTPException(status_code=500, detail="Meta prompts file not found")
        
        with open(meta_prompts_path, 'r', encoding='utf-8') as f:
            system_prompt = f.read()
        
        # 构建用户提示
        user_prompt = f"请优化以下问题：\n\n[原始问题]: {data.original_query}"
        
        # 构建完整提示
        prompt = f"<START_OF_SYSTEM_PROMPT>\n{system_prompt}\n<END_OF_SYSTEM_PROMPT>\n<START_OF_USER_PROMPT>\n{user_prompt}\n<END_OF_USER_PROMPT>"
        
        # 初始化OpenAI客户端
        model = OpenAIClient(input_type="messages")
        
        # 设置模型参数（流式）
        model_kwargs = {
            "model": data.model,
            "stream": True,
            "temperature": 0.3,  # 较低的温度以获得更一致的输出
            "top_p": 0.95,
            "api_key": api_key
        }
        
        # 转换输入为API格式
        api_kwargs = model.convert_inputs_to_api_kwargs(
            input=prompt,
            model_kwargs=model_kwargs,
            model_type=ModelType.LLM
        )
        
        logger.info(f"调用OpenAI客户端进行查询优化")
        
        # 调用模型获取流式响应
        response_stream = await model.acall(api_kwargs=api_kwargs, model_type=ModelType.LLM)
        
        # 定义流式生成器
        async def generate_stream():
            try:
                async for chunk in response_stream:
                    if chunk and hasattr(chunk, 'choices') and len(chunk.choices) > 0:
                        delta = chunk.choices[0].delta
                        if hasattr(delta, 'content') and delta.content:
                            # 使用 SSE 格式
                            sse_data = f"data: {json.dumps({'content': delta.content}, ensure_ascii=False)}\n\n"
                            yield sse_data
                            # 让出控制权，确保数据立即发送
                            await asyncio.sleep(0)
                # 发送完成标记
                yield "data: [DONE]\n\n"
            except Exception as e:
                logger.error(f"流式响应生成错误: {e}", exc_info=True)
                error_data = json.dumps({'error': str(e)}, ensure_ascii=False)
                yield f"data: {error_data}\n\n"
        
        # 返回流式响应
        return StreamingResponse(
            generate_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no"  # 禁用nginx缓冲
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"优化查询失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))