from fastapi import APIRouter, Request, Response, Depends
from fastapi.responses import JSONResponse
import logging
import jwt
import requests
from datetime import datetime

from api.common.constants import AuthMode
from api.service.login_manager import login_with_jwt, login_with_session
from api.utils.cookie_utils import set_cookie
from api.middleware.audit_log_middleware import audit_logger
from api.middleware.language_middleware import get_translation
from api.config import get_auth_mode, get_sso_config
from api.model.user_info import UserInfo
from api.service.user_service import (
    select_user_info, 
    init_user_when_login, 
    update_user_info_when_login,
)

logger = logging.getLogger(__name__)

auth_router = APIRouter(prefix="/api/auth", tags=["auth"])

@auth_router.get("/isLogged")
async def is_logged(request: Request):
    """
    Check if the user is logged in.
    """
    from api.service.login_manager import is_logged_by_jwt, is_logged_by_session

    auth_mode = get_auth_mode()
    if auth_mode == AuthMode.JWT:
        return is_logged_by_jwt(request)
    else:
        return is_logged_by_session(request)
    
@auth_router.get("/logout")
async def log_out(request: Request, audit_log=Depends(audit_logger)):
    from api.utils.session_utils import (
        delete_session, SESSION_COOKIE_NAME
    )

    res = Response(
        status_code=200,
    )
    auth_mode = get_auth_mode()
    from api.utils.session_utils import get_request_session_id
    if auth_mode == AuthMode.SESSION:
        session_id = get_request_session_id(request)
        if session_id:
            delete_session(session_id)
    # 将所有cookie置为失效        
    set_cookie(res, SESSION_COOKIE_NAME, "", max_age=0)
    set_cookie(res, "token", "", max_age=0)
    set_cookie(res, "user_code", "", max_age=0)
    # 设置审计日志数据
    audit_log(code="USER_LOGOUT", party_type="用户")
    return res

@auth_router.get("/getToken")
async def get_token(request: Request, audit_log=Depends(audit_logger)):
    """
    Get the token for the user.
    """
    sso_config = get_sso_config()
    app_key = sso_config["app_key"]
    app_secret = sso_config["app_secret"]
    base_url = sso_config["base_url"]
    if not app_key or not app_secret:
        return JSONResponse(
            status_code=500,
            content={"error": "app_key or app_secret is not set"}
        )
    # 获取token
    code = request.query_params.get('code')
    if not code:
        return get_translation("api.errors.authorizationFailedNoCode"), 400
    
    # 使用code换取token
    token_url = f"{base_url}/get_token"
    params = {
        'code': code,
        'app_key': app_key,
        'app_secret': app_secret
    }

    try:
        response = requests.get(token_url, params=params)
        result = response.json()

        if result.get('errno') == 0:
            decoded = jwt.decode(result['token'], options={"verify_signature": False})

            user_info = {
                'wcp_user_id': int(decoded.get('id')),
                'user_code': decoded.get('code'),
                'user_name': decoded.get('name'),
                'email': decoded.get('mail'),
                'phone': decoded.get('phone'),
                'dept': decoded.get('dept'),
                'org': decoded.get('org'),
                'job': decoded.get('job'),
                'job_id': decoded.get('job_id'),
                'dept_id': decoded.get('dept_id'),
                'org_id': decoded.get('org_id'),
                'last_login_date': datetime.now()
            }

            # 检查数据库中是否存在用户信息，如果不存在则添加
            user_info_db = select_user_info(user_info['wcp_user_id'])
            if user_info_db is None:
                user_info_db = init_user_when_login(UserInfo(**user_info))
            else:
                # 判断用户是否有效
                if not getattr(user_info_db, "state", True):
                    logger.error(f"[登录]用户无效，user_id={user_info_db.id}, user_code={getattr(user_info_db, 'user_code', '')}")
                    return JSONResponse(status_code=401, content={"error": get_translation("api.errors.userInvalidOrDisabled")})
                update_user_info_when_login(UserInfo(**user_info), user_info_db.id)

            token_user_info = {
                'id': user_info_db.id,
                'wcp_user_id': int(decoded.get('id')),
                'user_code': decoded.get('code'),
                'user_name': decoded.get('name'),
                'dept_id': decoded.get('dept_id'),
                'org_id': decoded.get('org_id'),
                'dept': decoded.get('dept'),
                'org': decoded.get('org'),
                'job': decoded.get('job'),
            }

            auth_mode = get_auth_mode()

            result = None
            if auth_mode == AuthMode.SESSION:
                result = login_with_session(token_user_info)
            else:
                result = login_with_jwt(token_user_info)
            
            # 记录登录成功的日志
            audit_log(code="USER_LOGIN", party_type="用户", party_id=token_user_info.get("id", 0),
                party_name=user_info.get("user_name", ""), party_code=user_info.get("user_code", ""),
                dept_id=user_info.get("dept_id"), dept=user_info.get("dept"))
            return result
        else:
            # 设置审计日志数据
            audit_log(is_success=False)
            return f"{get_translation('api.errors.getSSOTokenFailed')}: {result.get('reason', get_translation('api.errors.unknownError'))}", 400

    except Exception as e:
        logger.error(f"[登录]login failed : sso code = {code}, error:{str(e)}")
        # 设置审计日志数据
        audit_log(is_success=False)
        return JSONResponse(
            status_code=500,
            content={"error": get_translation("api.errors.internalErrorContactAdmin")}
        )
