
# MySQL 数据库连接配置
# 数据库配置
database:
  url:
    driver_name: "mysql+pymysql"
    username: "dbops"
    password: "abc@123A"  # 直接传入原始密码
    host: "************"
    port: 3307
    database: "deepwiki_open_dev"

  connect_timeout: 10  # 连接超时(秒)

  # 连接池配置
  pool:
    size: 20           # 连接池保持的连接数
    max_overflow: 20   # 允许临时扩展的连接数
    recycle: 3600      # 连接自动回收时间(秒)
    pre_ping: true     # 执行前检查连接活性
    timeout: 30        # 获取连接超时时间(秒)

  echo: false  # 是否输出SQL日志

# 平台SSO
sso:
  app_key: "eaccec38af18ed371dea"
  app_secret: "212964d9bdcaa1e6c83322e616f2f30e027e58a420482d6dc8ec802dfc2d91a4"
  base_url: "https://ssodr.iwhalecloud.com:40083/login/v2/auth"

# 配置cookie中的token的过期时间，单位s
session:
  # session超时时间, 单位秒, 时间小于等于cookie的过期时间
  timeout: 3600
  # 前端浏览器cookie有效期, 一般大于等于session的超时时间
  cookie:
    # 默认会话级别, 单位秒
    max-age: -1
    
wiki_generation_mode: 'thread' 

# 同步Worker服务配置
sync_worker:
  base_url: "http://localhost:8011"
  # 单实例允许的最大并行同步任务数量
  max_concurrent_jobs: 2
  global_concurrent_jobs: 2
  # Redis 槽位键的过期时间（秒）
  slot_ttl_seconds: 900
  # 对 Sync Worker 的 HTTP 请求超时时间配置（秒）
  http_timeout_seconds: 30
  http_connect_timeout_seconds: 5

# Wiki任务管理配置
wiki_jobs:
  # 全局最大并发任务数（所有实例总计）
  global_max_concurrent: 6
  # 单实例最大并发任务数
  instance_max_concurrent: 2
  # 任务超时时间（分钟）- 增加到180分钟，适应复杂任务
  timeout_minutes: 180
  # 分布式锁超时时间（分钟）- 默认为任务超时的一半
  lock_timeout_minutes: 90
  # 心跳检测阈值（分钟）- 超过此时间无心跳才认为实例崩溃
  heartbeat_timeout_minutes: 1
  # 清理检查间隔（秒）- 管理实例执行清理的间隔
  cleanup_interval_seconds: 45
  # 启用分布式锁 - 设为false时不会注册到数据库，适用于本地开发
  enable_distributed_lock: true
  # 是否启用严格的一致性检查（默认False）
  consistency_check_strict: false
  # 一致性检查的宽限期，单位秒（默认300秒）
  consistency_check_grace_period: 300
  # 触发一致性检查的差异阈值（默认2）
  consistency_check_threshold: 2
  # 快速模式开关 - 默认为true，跳过RAG直接生成Wiki
  fast_mode: true

# DocChain 配置
docchain:
  # 控制Wiki生成时是否异步触发DocChain代码上传
  async_upload_on_wiki_generation: false

# 文件系统类型对应acl工具 nfs4-acl-tools、acl
acl_version: 'nfs4-acl-tools'

# 后端请求拦截配置 
app:
  security:
    # excludeUrl配置不需要拦截的地址（正则表达式），内部可配置一个数组，具体配置放行的方法，例如 GET 方法
    excludeUrl:
      ^/api/config/lang$:
        - GET
      ^/api/auth/.*:
        - GET
      ^(/api/wiki/projects|/api/wiki/count)$:
        - GET
      ^/api/announcements$:
        - GET
      ^/health$:
        - GET
    # 开发模式，为true时不拦截请求，默认为false
    developMode: false
    # 是否开启角色服务权限,默认为true
    enable_role_service_privilege: true
    # jwttoken相关配置
    jwt:
      # 密钥
      secret_key: "327eRYDrz2q2NZlTWtrupJYP3Z4Zb3I4ZLs_PktOEeQ"
      # token过期时间 默认24小时
      expires_minutes: 1440
      # token最小刷新间隔 默认5分钟 单位秒
      token_min_refresh_interval: 300
      # 是否启用token刷新功能
      refresh_token: true
      # token解析 颁发时间、过期时间检验时间宽容值,单位分钟,默认5分钟
      leeway: 5
      # 共享token的过期时间，单位分钟，默认7天
      share_expires_minutes: 10080
    # AES工具配置
    aes:
      # 密钥
      secret_key: "hwKmb8WdqQVRjl1g"

# Langfuse 配置
langfuse:
  # 当前运行环境，可通过环境变量 LANGFUSE_ENVIRONMENT 覆盖
  # 如果需要为追踪单独指定环境，可设置 LANGFUSE_TRACING_ENVIRONMENT
  environment: "local"
  enabled: true
  host: "http://************:3000"
  public_key: "${LANGFUSE_PUBLIC_KEY_LOCAL}"
  secret_key: "${LANGFUSE_SECRET_KEY_LOCAL}"

# Kubernetes配置
kubernetes:
  # k8s API地址
  api_server: "http://**********:8080"
  # api_server: "https://*************:6443"
  # 命名空间
  namespace: "ptdev01"
  # 环境标识, 用于区分部署环境（如：prod, dev）
  # 影响k8s job的命名和标签，以避免在同一命名空间下的冲突
  environment: "local-dev"
  # 禁用SSL验证
  verify_ssl: false
  # 研发云门户基础地址，用于跳转容器控制台
  portal_base_url: "https://***********:25000/portal/"
  # 前端UI控制
  ui_features:
    enable_container_portal_button: false
    enable_jump_server_button: true
    jump_server_url: "https://dev.iwhalecloud.com/portal/main.html?ip=***********&type=host&concise=true&url=zcm-tool/modules/newconsole/views/newConsole"
  # 主web容器中的基础路径，用于路径映射
  base_path: "/home/<USER>/.adalflow"  
  # APP_Job配置
  app_job:  
    # 容器资源配置
    resources:
      # CPU和内存限制
      limits:
        cpu: "300m"      # CPU限制
        memory: "512Mi"  # 内存限制
      # CPU和内存请求
      requests:
        cpu: "50m"       # CPU请求
        memory: "256Mi"  # 内存请求
  # Job配置
  job:
    # Job空闲超时时间（分钟），超过此时间没有访问的job将被删除
    idle_timeout_minutes: 60
    # Job清理检查间隔（分钟）
    cleanup_interval_minutes: 10
    # 默认镜像
    default_image: "hub-nj.iwhalecloud.com/ptdev01/wct-cli-api-patch:D_20251024134628"
    # 全局最大容器数量限制
    max_containers: 10
    # 每个用户最大容器数量限制（并发沙盒配额）
    per_user_max_containers: 2
    # 基于最后访问时间的最小占用安全期（分钟），超过该时间后才允许被驱逐
    min_occupancy_minutes: 5
    # 基于最后访问时间的最大生命周期（分钟），超过该时间将被强制清理；不配置则不启用
    max_lifetime_minutes: 10
    # 节点选择器
    node_selector:
      deepwiki-open-dev-01: "deepwiki-open-dev-01"
    # 挂载配置
    volumes:
      code_path: "/app/deepwiki/adalflow/repos/"  # 代码目录（只读）：映射到容器内 /data/workspace/code
      workspace_path: "/app/deepwiki/adalflow/workspace/"  # 工作空间目录（读写）：映射到容器内 /data/workspace/
      project_workspace_path: "/app/deepwiki/adalflow/project_workspace/"  # 项目共享工作区（读写）：存放 i-doc、o-doc（与用户无关）
      gemini_path: "/app/deepwiki/adalflow/base/.gemini"  # 主机上的统一 gemini 目录
    # 环境变量，会见下述列表中的环境变量都加入到job容器中
    env:
      DEFAULT_MODEL: "gemini-2.5-pro"
      DEFAULT_FALLBACK_MODEL: "glm-4.5"
    # WCT API 超时配置（秒）
    wct_timeouts:
      request_timeout: 300     # 请求超时时间，默认5分钟
      tool_timeout: 60         # 工具执行超时时间，默认1分钟
    # 容器资源配置
    resources:
      # CPU和内存限制
      limits:
        cpu: "100m"      # CPU限制
        memory: "256Mi"  # 内存限制
      # CPU和内存请求
      requests:
        cpu: "10m"       # CPU请求
        memory: "256Mi"  # 内存请求

# 大屏配置
global_config:
  # 监控大屏地址配置
  monitor_url: "https://dev.iwhalecloud.com:25000/zcm-grafpub/d/XpKkIQ_Nz/deepwikijian-kong-da-ping"
  # 运营大屏地址配置
  operation_url: "https://dev.iwhalecloud.com:25000/zcm-grafpub/d/tkUoDxQHz/deepwikiyun-ying-qing-kuang"
  # 运营数据地址配置
  operation_data_url: "https://dev.iwhalecloud.com/doi/cjt3Nx/si7u5C3k/si7wFx2G/sitdLaps?t=40f1a_qbr&single=true&head=false"

zcm:
  devspace:
    base_url: "https://dev.iwhalecloud.com/portal/ai-gateway/devspace"
    token: "e62c1781a4334eff969570c6b325e467"
    
# Redis配置
redis:
  # Redis开关
  enabled: false
  # single, sentinel, cluster
  mode: "cluster"
  # 根据实际的环境修改
  key_prefix: "deepwiki-local-test"
  # 默认过期时间（秒）
  default_ttl: 3600
  
  # 序列化配置 json, pickle
  serializer: "json"
  
  # 连接配置
  connection:
    decode_responses: false
    encoding: "utf-8"
    # socket超时时间（秒）
    socket_timeout: 5.0
    # socket连接超时时间（秒）
    socket_connect_timeout: 5.0
    socket_keepalive: true
    socket_keepalive_options: {}
    
  # 连接池配置
  pool:
    max_connections: 50
    retry_on_timeout: true
    
  # 健康检查配置
  health_check:
    enabled: true
    # 健康检查间隔（秒
    interval: 30
    
  # 重试配置
  retry:
    retries: 6
    
  # 单机模式配置
  single:
    host: "localhost"
    port: 6379
    db: 0
    password: null
    
  # 哨兵模式配置
  sentinel:
    sentinels:
      - host: "localhost"
        port: 26379
      - host: "localhost"
        port: 26380
    service_name: "mymaster"
    password: null
    sentinel_password: null
    db: 0
    
  # 集群模式配置
  cluster:
    nodes:
      - host: "************"
        port: 7380
      - host: "************"
        port: 7380
      - host: "************"
        port: 7380
    password: "Lcap@2021"

# 文件管理配置
file_manager:
  # 最大文件大小（字节），默认10MB
  max_file_size: 10485760
  upload_allowed_extensions:
    - ".md"
    - ".txt"
    - ".yml"
    - ".yaml"
    - ".properties"
    - ".json"
    - ".png"
    - ".svg"
    - ".jpg"
    - ".sql"
    - ".conf"
    - ".cfg"
  # 允许的文件扩展名
  allowed_extensions:
    # 文档类型
    - ".txt"
    - ".md"
    - ".rst"
    - ".doc"
    - ".docx"
    - ".pdf"
    - ".rtf"
    # 代码类型
    - ".py"
    - ".js"
    - ".ts"
    - ".jsx"
    - ".tsx"
    - ".java"
    - ".c"
    - ".cpp"
    - ".h"
    - ".hpp"
    - ".cs"
    - ".php"
    - ".rb"
    - ".go"
    - ".rs"
    - ".swift"
    - ".kt"
    - ".scala"
    - ".html"
    - ".htm"
    - ".css"
    - ".scss"
    - ".sass"
    - ".less"
    - ".json"
    - ".xml"
    - ".yaml"
    - ".yml"
    - ".toml"
    - ".properties"
    - ".ini"
    - ".cfg"
    - ".conf"
    - ".sh"
    - ".bash"
    - ".zsh"
    - ".fish"
    - ".ps1"
    - ".bat"
    - ".cmd"
    - ".sql"
    - ".graphql"
    - ".proto"
    # 配置文件
    - ".env"
    - ".gitignore"
    - ".dockerignore"
    - ".editorconfig"
    - ".eslintrc"
    - ".prettierrc"
    - ".babelrc"
    - ".nvmrc"
    - ".lock"
    # 数据文件
    - ".csv"
    - ".tsv"
    - ".log"
    # 图片文件（小文件）
    - ".png"
    - ".jpg"
    - ".jpeg"
    - ".gif"
    - ".svg"
    - ".ico"
    - ".webp"
    # 其他
    - ".LICENSE"
    - ".gitkeep"
    - ".keep"
  # 禁止的文件扩展名
  blocked_extensions:
    # 可执行文件
    - ".exe"
    - ".msi"
    - ".dmg"
    - ".pkg"
    - ".deb"
    - ".rpm"
    - ".bin"
    - ".run"
    - ".app"
    - ".com"
    - ".scr"
    # 脚本文件（危险）
    - ".vbs"
    - ".ps1"
    - ".wsf"
    - ".hta"
    # 二进制文件
    - ".so"
    - ".dll"
    - ".dylib"
    - ".lib"
    - ".a"
    - ".o"
    - ".obj"
    # 压缩文件（大文件）
    - ".zip"
    - ".rar"
    - ".7z"
    - ".tar"
    - ".gz"
    - ".bz2"
    - ".xz"
    # 数据库文件
    - ".db"
    - ".sqlite"
    - ".sqlite3"
    - ".mdb"
    - ".accdb"
    # 临时文件
    - ".tmp"
    - ".temp"
    - ".swp"
    - ".swo"
    - ".bak"
    - ".backup"
  # 禁止的文件名模式
  blocked_patterns:
    - ".*"           # 隐藏文件（以.开头，但允许特定的配置文件）
    - "__pycache__"
    - "*.pyc"
    - "*.pyo"
    - "node_modules"
    - ".git"
    - ".svn"
    - ".DS_Store"
    - "Thumbs.db"
  # 允许的隐藏文件/目录
  allowed_hidden:
    - ".env"
    - ".gitignore"
    - ".dockerignore"
    - ".editorconfig"
    - ".eslintrc"
    - ".prettierrc"
    - ".babelrc"
    - ".nvmrc"
    - ".gemini"  # 允许.gemini目录

# 文件管理配置
input_template:
  show_docchain: true
