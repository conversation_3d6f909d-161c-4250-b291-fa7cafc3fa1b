"""Utilities to integrate Langfuse tracing across the API layer."""

from __future__ import annotations

import logging
from contextvars import copy_context
from typing import Any, Dict, Iterable, List, Optional

try:  # pragma: no cover - optional dependency during bootstrap
    from api.openai_client import LANGFUSE_SDK_ACTIVE
except Exception:  # pragma: no cover
    LANGFUSE_SDK_ACTIVE = False

logger = logging.getLogger(__name__)


class LangfuseSpanContext:
    """Context manager that safely opens a Langfuse span if SDK is active."""

    def __init__(
        self,
        enabled: bool,
        name: str,
        metadata: Optional[Dict[str, Any]] = None,
        *,
        as_generation: bool = False,
        trace_context: Optional[Dict[str, Any]] = None,
    ) -> None:
        self._enabled = enabled and LANGFUSE_SDK_ACTIVE
        self._name = name
        self._metadata = dict(metadata or {})
        self._as_generation = as_generation
        self._trace_context = trace_context
        self._span_cm = None
        self._span = None

    def __enter__(self):  # type: ignore[override]
        if not self._enabled:
            logger.debug(
                f"Langfuse span '{self._name}' skipped: "
                f"enabled={self._enabled}, SDK_ACTIVE={LANGFUSE_SDK_ACTIVE}"
            )
            return None
        try:
            from langfuse import get_client  # type: ignore

            client = get_client()
            if not client:
                logger.debug("Langfuse client unavailable; skipping span '%s'", self._name)
                return None

            start_callable = (
                getattr(client, "start_as_current_generation")
                if self._as_generation and hasattr(client, "start_as_current_generation")
                else client.start_as_current_span
            )

            span_kwargs = {
                "name": self._name,
                "metadata": self._metadata,
            }
            if self._trace_context:
                span_kwargs["trace_context"] = self._trace_context

            self._span_cm = start_callable(**span_kwargs)
            self._span = self._span_cm.__enter__()

            if self._metadata and hasattr(self._span, "update_trace"):
                session_value = (
                    self._metadata.get("langfuse_session_id")
                    or self._metadata.get("session_id")
                )

                user_value = (
                    self._metadata.get("langfuse_user_id")
                    or self._metadata.get("user_id")
                )

                user_code_value = self._metadata.get("user_code") or self._metadata.get("userCode")
                user_name_value = self._metadata.get("user_name") or self._metadata.get("username")

                user_label_value = self._metadata.get("user_label")

                if user_name_value and user_code_value:
                    user_value = f"{user_name_value}[{user_code_value}]"
                elif not user_value and user_code_value:
                    user_value = str(user_code_value)
                elif not user_value and user_name_value:
                    user_value = str(user_name_value)
                elif not user_value and user_label_value:
                    user_value = str(user_label_value)

                tags_value = self._metadata.get("langfuse_tags")

                trace_kwargs: Dict[str, Any] = {}
                if session_value:
                    trace_kwargs["session_id"] = str(session_value)
                if user_value:
                    trace_kwargs["user_id"] = str(user_value)
                if tags_value:
                    trace_kwargs["tags"] = tags_value

                trace_metadata: Dict[str, Any] = {}
                environment_value = self._metadata.get("environment")
                if environment_value:
                    trace_metadata["environment"] = environment_value
                labels_value = self._metadata.get("labels")
                if labels_value:
                    trace_metadata["labels"] = labels_value
                conversation_type = self._metadata.get("conversation_type")
                if conversation_type:
                    trace_metadata["conversation_type"] = conversation_type
                stage_value = self._metadata.get("stage")
                if stage_value:
                    trace_metadata["stage"] = stage_value

                if trace_metadata:
                    trace_kwargs["metadata"] = trace_metadata

                if trace_kwargs:
                    self._span.update_trace(**trace_kwargs)

            return self._span
        except Exception as exc:  # pragma: no cover - defensive logging
            logger.warning("Failed to start Langfuse span '%s': %s", self._name, exc)
            self._span_cm = None
            self._span = None
            return None

    def __exit__(self, exc_type, exc_val, exc_tb):  # type: ignore[override]
        if self._span_cm:
            return self._span_cm.__exit__(exc_type, exc_val, exc_tb)
        return False


def create_langfuse_span(
    enabled: bool,
    name: str,
    metadata: Optional[Dict[str, Any]] = None,
    *,
    as_generation: bool = False,
    trace_context: Optional[Dict[str, Any]] = None,
) -> LangfuseSpanContext:
    """Factory helper that hides the optional nature of Langfuse instrumentation."""

    return LangfuseSpanContext(
        enabled,
        name,
        metadata,
        as_generation=as_generation,
        trace_context=trace_context,
    )


class LangfuseSpanHandle:
    """将 Langfuse 跨度与 contextvars 绑定，确保进入与退出发生在同一上下文。

    使用方式：
    - handle = bind_langfuse_span(create_langfuse_span(...))
    - span = handle.enter()
    - ... 执行业务逻辑（可跨文件/函数）...
    - handle.exit()
    """

    def __init__(self, span_cm: Optional[LangfuseSpanContext]) -> None:
        # 保存原始上下文管理器并复制当前上下文
        self._span_cm = span_cm
        self._context = copy_context()
        self._span = None
        self._closed = True

    def enter(self):
        """进入跨度并缓存返回对象；若未启用SDK则返回None。"""
        if not self._span_cm:
            self._span = None
            self._closed = True
            return None
        if not self._closed:
            return self._span
        self._span = self._context.run(self._span_cm.__enter__)
        self._closed = False
        return self._span

    def exit(self, exc_type=None, exc_val=None, exc_tb=None):
        """在相同的 contextvars.Context 中退出跨度，避免 Token 跨上下文异常。"""
        if not self._span_cm or self._closed:
            return False
        result = self._context.run(self._span_cm.__exit__, exc_type, exc_val, exc_tb)
        self._closed = True
        return result

    def run(self, func, *args, **kwargs):
        """在绑定的上下文中执行函数，使其与跨度共享同一上下文。"""
        return self._context.run(func, *args, **kwargs)

    @property
    def span(self):
        """返回最近一次 enter 获取的跨度对象。"""
        return self._span

    @property
    def closed(self) -> bool:
        """指示跨度是否已关闭。"""
        return self._closed


def bind_langfuse_span(span_cm: Optional[LangfuseSpanContext]) -> LangfuseSpanHandle:
    """将 Langfuse 跨度上下文与当前 contextvars 绑定并返回句柄。"""
    return LangfuseSpanHandle(span_cm)


def get_langfuse_context() -> Dict[str, Any]:
    """Return Langfuse configuration derived from settings/environment."""

    try:  # pragma: no cover - avoid circular import during bootstrap
        from api.config import configs as _configs  # type: ignore
    except Exception:
        _configs = {}

    config = _configs.get("langfuse", {}) if isinstance(_configs, dict) else {}
    enabled = bool(config.get("enabled", True)) and LANGFUSE_SDK_ACTIVE
    environment = config.get("environment")
    return {
        "enabled": enabled,
        "environment": environment,
        "config": config,
    }


def flush_langfuse() -> None:
    """Best-effort flush of pending Langfuse telemetry for near-real-time visibility."""

    if not LANGFUSE_SDK_ACTIVE:
        return
    try:  # pragma: no cover - safeguard against runtime issues
        from langfuse import get_client  # type: ignore

        client = get_client()
        if client and hasattr(client, "flush"):
            client.flush()
    except Exception as exc:  # pragma: no cover
        logger.debug("Failed to flush Langfuse client: %s", exc)


def _normalize_tags(values: Iterable[Optional[str]]) -> List[str]:
    seen = set()
    tags: List[str] = []
    for value in values:
        if not value:
            continue
        if value in seen:
            continue
        seen.add(value)
        tags.append(value)
    return tags


def build_langfuse_metadata(
    base: Dict[str, Any],
    conversation_type: str,
    stage: Optional[str] = None,
    extra: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """Assemble a normalized metadata payload for Langfuse spans."""

    metadata = dict(base)
    metadata["conversation_type"] = conversation_type
    if stage:
        metadata["stage"] = stage
    if extra:
        metadata.update(extra)

    environment = metadata.get("environment")
    provider = metadata.get("provider")
    wiki_id = metadata.get("wiki_id")

    # 将自定义标签（如git信息）加入Langfuse标签列表，确保在控制台可见
    extra_label_values: List[str] = []
    raw_labels = metadata.get("labels")
    if isinstance(raw_labels, (list, tuple, set)):
        for label in raw_labels:
            if label:
                extra_label_values.append(str(label))

    tags = _normalize_tags(
        [
            conversation_type,
            f"env:{environment}" if environment else None,
            f"provider:{provider}" if provider else None,
            f"wiki:{wiki_id}" if wiki_id else None,
            f"stage:{stage}" if stage else None,
            *extra_label_values,
        ]
    )

    metadata["langfuse_session_id"] = (
        str(metadata.get("session_id") or metadata.get("session"))
        if metadata.get("session_id") or metadata.get("session")
        else None
    )
    user_code_value = metadata.get("user_code") or metadata.get("userCode")
    user_name_value = metadata.get("user_name") or metadata.get("username")
    user_label_value = metadata.get("user_label")

    # 构建Langfuse用户标识，优先使用“用户名[用户编码]”的组合形式，保证平台展示一致
    if user_name_value and user_code_value:
        metadata["langfuse_user_id"] = f"{user_name_value}[{user_code_value}]"
    elif user_label_value:
        metadata["langfuse_user_id"] = str(user_label_value)
    elif user_code_value or metadata.get("user_id"):
        metadata["langfuse_user_id"] = str(user_code_value or metadata.get("user_id"))
    else:
        metadata["langfuse_user_id"] = None
    metadata["langfuse_tags"] = tags

    return {key: value for key, value in metadata.items() if value not in (None, [], {}, "")}


# 旧的 BindLangfuseSpanContext 已被 LangfuseSpanHandle 替代，
# 以确保 __enter__/__exit__ 在同一 contextvars.Context 中执行。
