"""
文件管理服务
提供文件上传、下载、目录管理等功能
只允许操作docs和.gemini目录，code目录为只读
"""

import os
import logging
import shutil
import mimetypes
from pathlib import Path
from typing import Dict, Optional, BinaryIO, Any, Tuple
from datetime import datetime
import zipfile

from api.database.base import session_scope
from api.middleware.auth_middleware import get_current_user
from api.service.priv_checker import can_upload_project_docs
from api.service.wiki_info_service import get_wiki_info_by_repo # Import zipfile

from ..utils.path_mapping import get_path_mapping_service
from ..utils.file_filter_service import get_file_filter_service
from ..config import get_kubernetes_config

logger = logging.getLogger(__name__)


class FileManagerService:
    """文件管理服务类"""
    
    def __init__(self):
        """初始化文件管理服务"""
        self.path_mapping_service = get_path_mapping_service()
        
        # 使用统一的文件过滤服务
        self.file_filter = get_file_filter_service()
        
        # 从配置文件加载文件管理配置（主要用于其他设置）
        self._load_file_manager_config()
    
    def _load_file_manager_config(self):
        """从配置文件加载文件管理相关配置"""
        try:
            k8s_config = get_kubernetes_config()
            file_manager_config = k8s_config.get("file_manager", {})
            
            # 注意：文件过滤相关配置现在由统一的 FileFilterService 处理
            # 这里只加载一些其他的管理配置
            
            # 上传单个文件的最大大小限制（可能与过滤服务中的限制不同）
            self.upload_max_file_size = file_manager_config.get("upload_max_file_size", 
                                                               self.file_filter.get_max_file_size())
            
        except Exception as e:
            logger.warning(f"加载文件管理配置失败，使用默认配置: {e}")
            # 使用默认配置
            self.upload_max_file_size = self.file_filter.get_max_file_size()
    
    def _ensure_directory_exists(self, directory_path: Path) -> bool:
        """
        确保目录存在，如果不存在则创建
        
        Args:
            directory_path: 目录路径
            
        Returns:
            是否成功
        """
        try:
            directory_path.mkdir(parents=True, exist_ok=True)
            # 创建完成后设置权限为 777
            try:
                directory_path.chmod(0o777)
            except Exception as chmod_ex:
                logger.warning(f"设置目录权限为777失败 {directory_path}: {chmod_ex}")
            return True
        except Exception as e:
            logger.error(f"创建目录失败 {directory_path}: {e}")
            return False
    
    def _display_project_gemini(self, owner, repo_name, branch) -> bool:
        current_user = get_current_user()
        try:
            with session_scope() as session:
                wiki_info = get_wiki_info_by_repo(session=session, repo_name=repo_name, branch=branch, owner=owner)
                if not wiki_info:
                    return False
                return can_upload_project_docs(session, current_user.get("id", 0), wiki_info.wiki_id)
        except Exception as ex:
            logger.error(f"判断用户是否有展示project-gemini目录时发生异常：{ex}")
            return False
    
    def _convert_symlink_path(self, path: Path, owner: str, repo_name: str, branch: str, user_code: str) -> Optional[Path]:
        """
        检测沙箱软链接，转换成本地的路径
        """
        # 初始化解析后的路径
        resolved_path_parts = []
        current_base = Path()

        logger.info(f"convert symlink path to local path")

        try:
            for part in path.parts:
                # 拼接当前部分的完整路径
                current_path = current_base / part

                # 检查当前路径是否是符号链接
                if current_path.is_symlink():
                    # 获取符号链接指向的目标路径
                    target_path = current_path.readlink()

                    logger.info(f"target_path: {target_path.as_posix()}")

                    target_path_str = target_path.as_posix().lstrip("/")
                    if target_path_str.startswith("data/workspace"):
                        logger.info(f"sandbox symlink")
                        target_path_str = target_path_str.replace("data/workspace", "")
                        target_path = self.path_mapping_service.convert_virtual_to_real_path(
                            target_path_str, owner, repo_name, branch, user_code
                        )
                    else:
                        target_path = current_path

                    logger.info(f"target_path_str: {target_path_str}")
                    logger.info(f"converted_target_path: {target_path.as_posix()}")

                    # 如果目标路径是相对路径，转换为绝对路径
                    target_full_path = (current_base / target_path).resolve()

                    logger.info(f"target_full_path: {target_full_path.as_posix()}")

                    resolved_path_parts.append(target_full_path)
                    current_base = target_full_path
                else:
                    # 如果不是符号链接，直接添加当前部分
                    resolved_path_parts.append(current_path)
                    current_base = current_path  # 更新基础路径为当前路径
        except Exception as e:
            logger.error(f"Failed to convert symlink, cause by: {e}")
            return None

        # 返回拼接后的完整路径
        return Path(*resolved_path_parts)

    def zip_directory(self, owner: str, repo_name: str, branch: str, user_code: str,
                      virtual_dir_path: str) -> Tuple[Optional[Path], Optional[Dict[str, Any]]]:
        """
        将指定虚拟目录打包成zip文件
        
        Args:
            owner: 仓库所有者
            repo_name: 仓库名称
            branch: 分支名称
            user_code: 用户代码
            virtual_dir_path: 虚拟目录路径
            
        Returns:
            (Path, 文件信息) 或 (None, 错误信息)
        """
        try:
            # 转换为实际路径
            real_dir_path = self.path_mapping_service.convert_virtual_to_real_path(
                virtual_dir_path, owner, repo_name, branch, user_code
            )

            logger.info(f"real_dir_path: {real_dir_path.as_posix()}")

            if not real_dir_path:
                return None, {"error": "无法访问目录路径"}
            
            if not real_dir_path.exists():
                # 如果路径不存在，有可能是沙箱软链接，尝试转换成本地路径
                converted_symlink_path = self._convert_symlink_path(real_dir_path, owner, repo_name, branch, user_code)
                if converted_symlink_path:
                    real_dir_path = converted_symlink_path
                logger.info(f"real_dir_path: {real_dir_path.as_posix()}")
            
            if not real_dir_path.exists():
                return None, {"error": "目录不存在"}
            
            if not real_dir_path.is_dir():
                return None, {"error": "路径不是目录"}
            
            # 创建一个临时zip文件
            temp_zip_dir = Path("/tmp/deepwiki_zips") # 使用/tmp作为临时目录
            temp_zip_dir.mkdir(parents=True, exist_ok=True)
            
            zip_file_name = f"{real_dir_path.name}_{datetime.now().strftime('%Y%m%d%H%M%S')}.zip"
            temp_zip_path = temp_zip_dir / zip_file_name
            
            with zipfile.ZipFile(temp_zip_path, 'w', zipfile.ZIP_DEFLATED) as zf:
                for root, _, files in os.walk(real_dir_path):
                    for file in files:
                        file_path = Path(root) / file
                        # 确保只打包允许的文件类型
                        if file_path.exists() and self.file_filter.should_show_in_listing(file_path):
                            # 计算在zip文件中的相对路径
                            arcname = file_path.relative_to(real_dir_path)
                            zf.write(file_path, arcname)
            
            # 获取文件信息
            stat_info = temp_zip_path.stat()
            mime_type, _ = mimetypes.guess_type(temp_zip_path.name)
            
            file_info = {
                "name": temp_zip_path.name,
                "size": stat_info.st_size,
                "modified": datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
                "mime_type": mime_type or "application/zip"
            }
            
            logger.info(f"目录打包成功: {real_dir_path} -> {temp_zip_path}")
            return temp_zip_path, file_info
            
        except Exception as e:
            logger.error(f"打包目录失败 {virtual_dir_path}: {e}")
            return None, {"error": f"打包目录失败: {str(e)}"}
    
    def list_directory(self, owner: str, repo_name: str, branch: str, user_code: str, 
                      virtual_path: str = "") -> Dict[str, Any]:
        """
        列出目录内容
        
        Args:
            owner: 仓库所有者
            repo_name: 仓库名称
            branch: 分支名称
            user_code: 用户代码
            virtual_path: 虚拟路径
            
        Returns:
            目录内容信息
        """
        try:
            # 标准化虚拟路径
            if not virtual_path:
                # 返回根目录：根目录 = code + workspace下的目录
                root_dirs = self.path_mapping_service.get_root_virtual_directories(
                    owner, repo_name, branch, user_code
                )

                # 如果用户是超级管理员、wiki管理员或wiki拥有者，则可以访问sys-gemini目录
                if self._display_project_gemini(owner=owner, repo_name=repo_name, branch=branch):
                    root_dirs.append("project-gemini")

                items = []
                
                for dir_name in root_dirs:
                    if dir_name == "code":
                        continue
                    items.append({
                        "name": dir_name,
                        "type": "directory",
                        "path": f"{dir_name}/",
                        "size": 0,
                        "modified": None,
                        "is_accessible": True
                    })
                
                return {
                    "path": "",
                    "items": items,
                    "parent": None
                }
            
            # 转换为实际路径
            real_path = self.path_mapping_service.convert_virtual_to_real_path(
                virtual_path, owner, repo_name, branch, user_code
            )

            logger.info(f"real_path: {real_path.as_posix()}")
            
            if not real_path:
                return {"error": "无效的路径", "path": virtual_path}
            
            if not real_path.exists():
                # 如果路径不存在，有可能是沙箱软链接，尝试转换成本地路径
                converted_symlink_path = self._convert_symlink_path(real_path, owner, repo_name, branch, user_code)
                if converted_symlink_path:
                    real_path = converted_symlink_path
                logger.info(f"real_path: {real_path.as_posix()}")
            
            # 检查路径是否可访问
            if not self.path_mapping_service.is_path_accessible(real_path, owner, repo_name, branch, user_code):
                return {"error": "路径不可访问", "path": virtual_path}
            
            # 检查目录是否存在
            if not real_path.exists():
                # 只对workspace路径尝试创建目录（code路径为只读）
                if not virtual_path.startswith("code/"):
                    if self._ensure_directory_exists(real_path):
                        logger.info(f"创建目录: {real_path}")
                    else:
                        return {"error": "目录不存在且创建失败", "path": virtual_path}
                else:
                    return {"error": "代码目录不存在", "path": virtual_path}
            
            if not real_path.is_dir():
                return {"error": "路径不是目录", "path": virtual_path}
            
            # 列出目录内容
            items = []
            try:
                for item in real_path.iterdir():

                    # 如果路径不存在，尝试解析沙箱软链接
                    if not item.exists():
                        converted_symlink_path = self._convert_symlink_path(item, owner, repo_name, branch, user_code)
                        if converted_symlink_path:
                            item = converted_symlink_path
                        logger.info(f"item: {item.as_posix()}")

                    # 跳过不应该显示的文件/目录
                    if not self.file_filter.should_show_in_listing(item):
                        continue
                    
                    # 获取文件信息
                    try:
                        stat_info = item.stat()
                        
                        # 构建子项的虚拟路径
                        if virtual_path.endswith('/'):
                            child_virtual_path = f"{virtual_path}{item.name}"
                        else:
                            child_virtual_path = f"{virtual_path}/{item.name}" if virtual_path else item.name
                        
                        if item.is_dir():
                            child_virtual_path += "/"
                        
                        item_info = {
                            "name": item.name,
                            "type": "directory" if item.is_dir() else "file",
                            "path": child_virtual_path,
                            "size": stat_info.st_size if item.is_file() else 0,
                            "modified": datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
                            "is_accessible": True
                        }
                        
                        # 为文件添加MIME类型
                        if item.is_file():
                            mime_type, _ = mimetypes.guess_type(item.name)
                            item_info["mime_type"] = mime_type or "application/octet-stream"
                        
                        items.append(item_info)
                    except Exception as e:
                        logger.warning(f"获取文件信息失败 {item}: {e}")
                        continue
            except PermissionError:
                return {"error": "权限不足，无法访问目录", "path": virtual_path}
            except Exception as e:
                logger.error(f"列出目录内容失败 {real_path}: {e}")
                return {"error": f"读取目录失败: {str(e)}", "path": virtual_path}
            
            # 排序：目录在前，文件在后，同类型按名称排序
            items.sort(key=lambda x: (x["type"] == "file", x["name"].lower()))
            
            # 计算父目录路径
            parent_path = None
            if virtual_path:
                # 移除结尾的斜杠然后分割
                path_clean = virtual_path.rstrip('/')
                if '/' in path_clean:
                    parent_parts = path_clean.split('/')
                    parent_path = '/'.join(parent_parts[:-1]) + '/'
                else:
                    # 顶级目录的父目录是根目录
                    parent_path = ""
            
            return {
                "path": virtual_path,
                "items": items,
                "parent": parent_path
            }
            
        except Exception as e:
            logger.error(f"列出目录失败: {e}")
            return {"error": f"列出目录失败: {str(e)}", "path": virtual_path}
    
    def upload_file(self, owner: str, repo_name: str, branch: str, user_code: str,
                   virtual_path: str, filename: str, file_content: BinaryIO) -> Dict[str, Any]:
        """
        上传文件
        
        Args:
            owner: 仓库所有者
            repo_name: 仓库名称
            branch: 分支名称
            user_code: 用户代码
            virtual_path: 目标虚拟路径
            filename: 文件名
            file_content: 文件内容
            
        Returns:
            上传结果
        """
        try:
            # 先快速检查文件类型
            type_allowed, type_error_msg = self.file_filter._is_upload_file_type_allowed(filename)
            if not type_allowed:
                return {"success": False, "error": type_error_msg}
            
            # 然后获取文件大小
            file_content.seek(0, 2)  # 移动到文件末尾
            file_size = file_content.tell()
            file_content.seek(0)  # 回到文件开头
            
            # 检查文件大小是否允许
            if file_size > self.file_filter.get_max_file_size():
                return {"success": False, "error": f"文件大小超过限制（最大 {self.file_filter.get_max_file_size() // 1024 // 1024}MB）"}
            
            # 构建完整的虚拟文件路径
            if virtual_path.endswith('/'):
                full_virtual_path = f"{virtual_path}{filename}"
            else:
                full_virtual_path = f"{virtual_path}/{filename}" if virtual_path else filename
            
            # 转换为实际路径
            real_file_path = self.path_mapping_service.convert_virtual_to_real_path(
                full_virtual_path, owner, repo_name, branch, user_code
            )

            logger.info(f"real_file_path: {real_file_path.as_posix()}")

            if not real_file_path.exists():
                # 如果路径不存在，有可能是沙箱软链接，尝试转换成本地路径
                converted_symlink_path = self._convert_symlink_path(real_file_path, owner, repo_name, branch, user_code)
                if converted_symlink_path:
                    real_file_path = converted_symlink_path
                logger.info(f"real_file_path: {real_file_path.as_posix()}")
            
            if not real_file_path:
                return {"success": False, "error": "无法访问目标路径"}
            
            # 确保父目录存在
            if not self._ensure_directory_exists(real_file_path.parent):
                return {"success": False, "error": "无法创建目标目录"}
            
            # 写入文件
            try:
                with open(real_file_path, 'wb') as f:
                    shutil.copyfileobj(file_content, f)
                
                # 上传完成后设置权限为 777
                try:
                    real_file_path.chmod(0o777)
                except Exception as chmod_ex:
                    logger.warning(f"设置文件权限为777失败 {real_file_path}: {chmod_ex}")

                # 获取文件信息
                stat_info = real_file_path.stat()
                
                logger.info(f"文件上传成功: {real_file_path}")
                
                return {
                    "success": True,
                    "file": {
                        "name": filename,
                        "path": full_virtual_path,
                        "size": stat_info.st_size,
                        "modified": datetime.fromtimestamp(stat_info.st_mtime).isoformat()
                    }
                }
                
            except Exception as e:
                logger.error(f"写入文件失败 {real_file_path}: {e}")
                return {"success": False, "error": f"写入文件失败: {str(e)}"}
                
        except Exception as e:
            logger.error(f"上传文件失败: {e}")
            return {"success": False, "error": f"上传失败: {str(e)}"}
    
    def download_file(self, owner: str, repo_name: str, branch: str, user_code: str,
                     virtual_file_path: str) -> Tuple[Optional[BinaryIO], Optional[Dict[str, Any]]]:
        """
        下载文件
        
        Args:
            owner: 仓库所有者
            repo_name: 仓库名称
            branch: 分支名称
            user_code: 用户代码
            virtual_file_path: 虚拟文件路径
            
        Returns:
            (文件流, 文件信息) 或 (None, 错误信息)
        """
        try:
            # 转换为实际路径
            real_file_path = self.path_mapping_service.convert_virtual_to_real_path(
                virtual_file_path, owner, repo_name, branch, user_code
            )

            logger.info(f"real_file_path: {real_file_path.as_posix()}")
            
            if not real_file_path:
                return None, {"error": "无法访问文件路径"}
            
            if not real_file_path.exists():
                # 如果路径不存在，有可能是沙箱软链接，尝试转换成本地路径
                converted_symlink_path = self._convert_symlink_path(real_file_path, owner, repo_name, branch, user_code)
                if converted_symlink_path:
                    real_file_path = converted_symlink_path
                logger.info(f"real_file_path: {real_file_path.as_posix()}")
            
            # 检查文件是否存在
            if not real_file_path.exists():
                return None, {"error": "文件不存在"}
            
            if not real_file_path.is_file():
                return None, {"error": "路径不是文件"}
            
            # 获取文件信息
            stat_info = real_file_path.stat()
            
            # 检查文件大小
            if stat_info.st_size > self.file_filter.get_max_file_size():
                return None, {"error": "文件大小超过下载限制"}
            
            # 打开文件流
            try:
                file_stream = open(real_file_path, 'rb')
                
                # 获取MIME类型
                mime_type, _ = mimetypes.guess_type(real_file_path.name)
                
                file_info = {
                    "name": real_file_path.name,
                    "size": stat_info.st_size,
                    "modified": datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
                    "mime_type": mime_type or "application/octet-stream"
                }
                
                logger.info(f"文件下载: {real_file_path}")
                return file_stream, file_info
                
            except Exception as e:
                logger.error(f"打开文件失败 {real_file_path}: {e}")
                return None, {"error": f"无法打开文件: {str(e)}"}
                
        except Exception as e:
            logger.error(f"下载文件失败: {e}")
            return None, {"error": f"下载失败: {str(e)}"}
    
    def read_sandbox_file(self, owner: str, repo_name: str, branch: str, user_code: str,
                         virtual_file_path: str) -> Tuple[Optional[str], Optional[Dict[str, Any]]]:
        """
        读取沙箱文件内容
        
        Args:
            owner: 仓库所有者
            repo_name: 仓库名称
            branch: 分支名称
            user_code: 用户代码
            virtual_file_path: 虚拟文件路径
            
        Returns:
            (文件内容字符串, 文件信息) 或 (None, 错误信息)
        """
        try:
            # 转换为实际路径
            real_file_path = self.path_mapping_service.convert_virtual_to_real_path(
                virtual_file_path, owner, repo_name, branch, user_code
            )
            
            if not real_file_path:
                return None, {"error": "无法访问文件路径"}
            
            # 检查文件是否存在
            if not real_file_path.exists():
                return None, {"error": "文件不存在"}
            
            if not real_file_path.is_file():
                return None, {"error": "路径不是文件"}
            
            # 获取文件信息
            stat_info = real_file_path.stat()
            
            # 检查文件大小
            if stat_info.st_size > self.file_filter.get_max_file_size():
                return None, {"error": "文件大小超过读取限制"}
            
            # 读取文件内容
            try:
                # 尝试多种编码方式读取文本文件
                encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
                content = None
                used_encoding = None
                
                for encoding in encodings:
                    try:
                        with open(real_file_path, 'r', encoding=encoding) as f:
                            content = f.read()
                            used_encoding = encoding
                            break
                    except UnicodeDecodeError:
                        continue
                    except Exception as e:
                        logger.warning(f"使用编码 {encoding} 读取文件失败: {e}")
                        continue
                
                if content is None:
                    # 如果所有编码都失败，尝试读取为二进制并转换
                    try:
                        with open(real_file_path, 'rb') as f:
                            raw_content = f.read()
                            # 尝试用utf-8解码，失败则使用latin-1（不会失败）
                            try:
                                content = raw_content.decode('utf-8')
                                used_encoding = 'utf-8'
                            except UnicodeDecodeError:
                                content = raw_content.decode('latin-1')
                                used_encoding = 'latin-1'
                    except Exception as e:
                        logger.error(f"读取文件失败 {real_file_path}: {e}")
                        return None, {"error": f"无法读取文件: {str(e)}"}
                
                # 限制文件内容长度（避免过长）
                max_length = 100000  # 100KB 文本限制
                is_truncated = False
                if len(content) > max_length:
                    content = content[:max_length]
                    is_truncated = True
                
                # 获取MIME类型
                mime_type, _ = mimetypes.guess_type(real_file_path.name)
                
                file_info = {
                    "name": real_file_path.name,
                    "size": stat_info.st_size,
                    "modified": datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
                    "mime_type": mime_type or "application/octet-stream",
                    "encoding": used_encoding,
                    "is_truncated": is_truncated,
                    "content_length": len(content)
                }
                
                logger.info(f"成功读取文件内容: {real_file_path} (编码: {used_encoding}, 长度: {len(content)})")
                return content, file_info
                
            except Exception as e:
                logger.error(f"读取文件内容失败 {real_file_path}: {e}")
                return None, {"error": f"无法读取文件内容: {str(e)}"}
                
        except Exception as e:
            logger.error(f"读取沙箱文件失败: {e}")
            return None, {"error": f"读取失败: {str(e)}"}

    def read_file(self, owner: str, repo_name: str, branch: str, user_code: str,
                  virtual_path: str) -> Tuple[Optional[str], Optional[Dict[str, Any]]]:
        """
        读取文件（兼容多种虚拟路径前缀）
        支持以下形式：
        - /data/workspace/code/...
        - /data/workspace/docs/...
        - /code/...
        - /docs/...

        Args:
            owner: 仓库所有者
            repo_name: 仓库名称
            branch: 分支名称
            user_code: 用户代码
            virtual_path: 虚拟文件路径

        Returns:
            (文件内容字符串, 文件信息) 或 (None, 错误信息)
        """
        try:
            if not virtual_path:
                return None, {"error": "虚拟路径不能为空"}

            # 规范化路径，去掉 /data/workspace 前缀
            norm_path = virtual_path
            try:
                norm_path = str(norm_path)
            except Exception:
                norm_path = virtual_path

            if norm_path.startswith("/data/workspace/"):
                norm_path = norm_path[len("/data/workspace/"):]

            # 只保留以 code/ 或 docs/ 开头的路径，并移除多余的前导斜杠
            if norm_path.startswith("/"):
                norm_path = norm_path[1:]

            if not (norm_path.startswith("code/") or norm_path.startswith("docs/")):
                # 向后兼容：如果传入的是 /i-doc/ 之类其他别名，可根据需要在此扩展映射
                return None, {"error": f"不支持的虚拟路径: {virtual_path}"}

            # 转换为实际路径
            real_file_path = self.path_mapping_service.convert_virtual_to_real_path(
                norm_path, owner, repo_name, branch, user_code
            )

            if not real_file_path:
                return None, {"error": "无法访问文件路径"}

            # 检查文件是否存在
            if not real_file_path.exists():
                return None, {"error": "文件不存在"}

            if not real_file_path.is_file():
                return None, {"error": "路径不是文件"}

            # 获取文件信息
            stat_info = real_file_path.stat()

            # 检查文件大小
            if stat_info.st_size > self.file_filter.get_max_file_size():
                return None, {"error": "文件大小超过读取限制"}

            # 读取文件内容（沿用 read_sandbox_file 的多编码策略与截断策略）
            try:
                encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
                content = None
                used_encoding = None

                for encoding in encodings:
                    try:
                        with open(real_file_path, 'r', encoding=encoding) as f:
                            content = f.read()
                            used_encoding = encoding
                            break
                    except UnicodeDecodeError:
                        continue
                    except Exception as e:
                        logger.warning(f"使用编码 {encoding} 读取文件失败: {e}")
                        continue

                if content is None:
                    try:
                        with open(real_file_path, 'rb') as f:
                            raw_content = f.read()
                            try:
                                content = raw_content.decode('utf-8')
                                used_encoding = 'utf-8'
                            except UnicodeDecodeError:
                                content = raw_content.decode('latin-1')
                                used_encoding = 'latin-1'
                    except Exception as e:
                        logger.error(f"读取文件失败 {real_file_path}: {e}")
                        return None, {"error": f"无法读取文件: {str(e)}"}

                max_length = 100000
                is_truncated = False
                if len(content) > max_length:
                    content = content[:max_length]
                    is_truncated = True

                mime_type, _ = mimetypes.guess_type(real_file_path.name)

                file_info = {
                    "name": real_file_path.name,
                    "size": stat_info.st_size,
                    "modified": datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
                    "mime_type": mime_type or "application/octet-stream",
                    "encoding": used_encoding,
                    "is_truncated": is_truncated,
                    "content_length": len(content)
                }

                logger.info(f"成功读取文件内容: {real_file_path} (编码: {used_encoding}, 长度: {len(content)})")
                return content, file_info

            except Exception as e:
                logger.error(f"读取文件内容失败 {real_file_path}: {e}")
                return None, {"error": f"无法读取文件内容: {str(e)}"}

        except Exception as e:
            logger.error(f"读取文件失败: {e}")
            return None, {"error": f"读取失败: {str(e)}"}
    
    def create_directory(self, owner: str, repo_name: str, branch: str, user_code: str,
                        virtual_path: str, directory_name: str) -> Dict[str, Any]:
        """
        创建目录
        
        Args:
            owner: 仓库所有者
            repo_name: 仓库名称
            branch: 分支名称
            user_code: 用户代码
            virtual_path: 父目录虚拟路径
            directory_name: 目录名称
            
        Returns:
            创建结果
        """
        try:
            # 检查目录名是否合法
            if not directory_name or '..' in directory_name or '/' in directory_name:
                return {"success": False, "error": "无效的目录名称"}
            
            # 构建完整的虚拟目录路径
            if virtual_path.endswith('/'):
                full_virtual_path = f"{virtual_path}{directory_name}/"
            else:
                full_virtual_path = f"{virtual_path}/{directory_name}/" if virtual_path else f"{directory_name}/"
            
            # 转换为实际路径
            real_dir_path = self.path_mapping_service.convert_virtual_to_real_path(
                full_virtual_path, owner, repo_name, branch, user_code
            )

            logger.info(f"real_dir_path: {real_dir_path.as_posix()}")

            if not real_dir_path.exists():
                # 如果路径不存在，有可能是沙箱软链接，尝试转换成本地路径
                converted_symlink_path = self._convert_symlink_path(real_dir_path, owner, repo_name, branch, user_code)
                if converted_symlink_path:
                    real_dir_path = converted_symlink_path
                logger.info(f"real_dir_path: {real_dir_path.as_posix()}")
            
            if not real_dir_path:
                return {"success": False, "error": "无法访问目标路径"}
            
            # 检查目录是否已存在
            if real_dir_path.exists():
                return {"success": False, "error": "目录已存在"}
            
            # 创建目录
            if self._ensure_directory_exists(real_dir_path):
                # 再次确保权限为 777（作为双重保险）
                try:
                    real_dir_path.chmod(0o777)
                except Exception as chmod_ex:
                    logger.warning(f"设置目录权限为777失败 {real_dir_path}: {chmod_ex}")
                logger.info(f"创建目录: {real_dir_path}")
                return {
                    "success": True,
                    "directory": {
                        "name": directory_name,
                        "path": full_virtual_path
                    }
                }
            else:
                return {"success": False, "error": "创建目录失败"}
                
        except Exception as e:
            logger.error(f"创建目录失败: {e}")
            return {"success": False, "error": f"创建目录失败: {str(e)}"}
    
    def delete_item(self, owner: str, repo_name: str, branch: str, user_code: str,
                   virtual_path: str) -> Dict[str, Any]:
        """
        删除文件或目录
        
        Args:
            owner: 仓库所有者
            repo_name: 仓库名称
            branch: 分支名称
            user_code: 用户代码
            virtual_path: 虚拟路径
            
        Returns:
            删除结果
        """
        try:
            # 转换为实际路径
            real_path = self.path_mapping_service.convert_virtual_to_real_path(
                virtual_path, owner, repo_name, branch, user_code
            )

            logger.info(f"real_path: {real_path.as_posix()}")
            
            if not real_path:
                return {"success": False, "error": "无法访问路径"}
            
            if not real_path.exists():
                # 如果路径不存在，有可能是沙箱软链接，尝试转换成本地路径
                converted_symlink_path = self._convert_symlink_path(real_path, owner, repo_name, branch, user_code)
                if converted_symlink_path:
                    real_path = converted_symlink_path
                logger.info(f"real_path: {real_path.as_posix()}")

            # 检查路径是否存在
            if not real_path.exists():
                return {"success": False, "error": "文件或目录不存在"}
            
            # 删除文件或目录
            try:
                if real_path.is_file():
                    real_path.unlink()
                    logger.info(f"删除文件: {real_path}")
                elif real_path.is_dir():
                    shutil.rmtree(real_path)
                    logger.info(f"删除目录: {real_path}")
                else:
                    return {"success": False, "error": "无法识别的路径类型"}
                
                return {"success": True}
                
            except Exception as e:
                logger.error(f"删除失败 {real_path}: {e}")
                return {"success": False, "error": f"删除失败: {str(e)}"}
                
        except Exception as e:
            logger.error(f"删除项目失败: {e}")
            return {"success": False, "error": f"删除失败: {str(e)}"}


# 全局文件管理服务实例
_file_manager_service = None

def get_file_manager_service() -> FileManagerService:
    """获取文件管理服务实例"""
    global _file_manager_service
    if _file_manager_service is None:
        _file_manager_service = FileManagerService()
    return _file_manager_service

