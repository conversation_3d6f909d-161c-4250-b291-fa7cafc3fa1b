"""
目录树缓存服务
基于现有Redis架构缓存项目目录结构，提高文件搜索性能
只缓存代码目录，使用相对路径
"""

import logging
import json
from pathlib import Path
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from ..utils.file_filter_service import get_file_filter_service

logger = logging.getLogger(__name__)


@dataclass
class FileNode:
    """文件节点"""
    name: str
    path: str
    is_directory: bool
    size: Optional[int] = None
    modified_time: Optional[str] = None
    children: Optional[List['FileNode']] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        if self.children:
            data['children'] = [child.to_dict() for child in self.children]
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FileNode':
        """从字典创建"""
        children = None
        if data.get('children'):
            children = [cls.from_dict(child) for child in data['children']]
        
        return cls(
            name=data['name'],
            path=data['path'],
            is_directory=data['is_directory'],
            size=data.get('size'),
            modified_time=data.get('modified_time'),
            children=children
        )


class DirectoryCacheService:
    """目录缓存服务 - 基于现有Redis架构，只缓存代码目录"""
    
    def __init__(self):
        """初始化缓存服务"""
        from api.cache.redis.manager import redis_manager
        self.redis_client = redis_manager.get_client()
        
        # 内存缓存作为回退
        self._memory_cache: Dict[str, Any] = {}
        
        # 缓存配置
        self.cache_ttl = 7200  # 2小时过期
        self.max_depth = 8     # 最大扫描深度
        
        # Redis数据结构前缀 - 包含namespace和environment
        self.key_prefix = self._get_cache_key_prefix()
        
        # 使用统一的文件过滤服务
        self.file_filter = get_file_filter_service()
    
    def _get_cache_key_prefix(self) -> str:
        """获取缓存键前缀，包含namespace和environment信息"""
        try:
            from ..sandbox.kubernetes_service import get_kubernetes_service
            k8s = get_kubernetes_service()
            return f"file_cache:{k8s.namespace}:{k8s.environment}"
        except Exception:
            # 如果获取失败，使用默认前缀
            return "file_cache:default:default"
    
    def _get_project_key(self, repo_url: str, branch: str) -> str:
        """生成项目基础键"""
        # 使用项目名和分支作为键，避免哈希让键更可读
        repo_name = repo_url.split('/')[-1] if '/' in repo_url else repo_url
        return f"{self.key_prefix}:project:{repo_name}:{branch}"
    
    def _get_tree_key(self, repo_url: str, branch: str) -> str:
        """生成目录树键"""
        return f"{self._get_project_key(repo_url, branch)}:tree"
    
    def _get_files_key(self, repo_url: str, branch: str) -> str:
        """生成文件列表键 - 用于快速搜索"""
        return f"{self._get_project_key(repo_url, branch)}:files"
    
    def _get_dirs_key(self, repo_url: str, branch: str) -> str:
        """生成目录列表键 - 用于快速搜索"""
        return f"{self._get_project_key(repo_url, branch)}:dirs"
    
    def _get_files_by_path_key(self, repo_url: str, branch: str) -> str:
        """生成按路径索引的文件键"""
        return f"{self._get_project_key(repo_url, branch)}:files_by_path"
    
    def _get_files_by_ext_key(self, repo_url: str, branch: str) -> str:
        """生成按扩展名索引的文件键"""
        return f"{self._get_project_key(repo_url, branch)}:files_by_ext"
    
    def _get_path_key(self, repo_url: str, branch: str, path: str) -> str:
        """生成特定路径键 - 用于目录浏览"""
        return f"{self._get_project_key(repo_url, branch)}:path:{path.replace('/', ':')}"
    
    def _should_ignore_directory(self, dir_path: Path) -> bool:
        """检查目录是否应该被忽略"""
        return self.file_filter.should_ignore_directory(dir_path)
    
    def _should_ignore_file(self, file_path: Path) -> bool:
        """检查文件是否应该被忽略"""
        return self.file_filter.should_ignore_file(file_path)
    
    def _scan_directory_recursive(self, directory: Path, max_depth: int = 10, current_depth: int = 0) -> FileNode:
        """递归扫描目录"""
        if current_depth >= max_depth:
            return FileNode(
                name=directory.name,
                path=str(directory),
                is_directory=True,
                children=[]
            )
        
        try:
            stat = directory.stat()
            modified_time = datetime.fromtimestamp(stat.st_mtime).isoformat()
            
            node = FileNode(
                name=directory.name,
                path=str(directory),
                is_directory=True,
                modified_time=modified_time,
                children=[]
            )
            
            # 扫描子项
            for item_path in directory.iterdir():
                try:
                    if item_path.is_dir():
                        if not self._should_ignore_directory(item_path):
                            child_node = self._scan_directory_recursive(
                                item_path, max_depth, current_depth + 1
                            )
                            node.children.append(child_node)
                    else:
                        if not self._should_ignore_file(item_path):
                            stat = item_path.stat()
                            file_node = FileNode(
                                name=item_path.name,
                                path=str(item_path),
                                is_directory=False,
                                size=stat.st_size,
                                modified_time=datetime.fromtimestamp(stat.st_mtime).isoformat()
                            )
                            node.children.append(file_node)
                except (PermissionError, OSError) as e:
                    logger.warning(f"无法访问: {item_path}, 错误: {e}")
                    continue
            
            return node
            
        except Exception as e:
            logger.error(f"扫描目录失败 {directory}: {e}")
            return FileNode(
                name=directory.name,
                path=str(directory),
                is_directory=True,
                children=[]
            )
    
    def build_code_directory_cache(self, code_root_path: Path, repo_url: str, branch: str) -> bool:
        """
        构建代码目录缓存 - 只缓存代码目录，使用相对路径
        
        Args:
            code_root_path: 代码根目录的实际路径
            repo_url: 仓库URL
            branch: 分支名称
            
        Returns:
            是否构建成功
        """
        try:
            if not code_root_path.exists() or not code_root_path.is_dir():
                logger.warning(f"代码目录不存在或不是目录: {code_root_path}")
                return False
            
            logger.info(f"开始构建代码目录缓存: {code_root_path}")
            
            # 收集所有文件和目录信息（使用相对路径）
            all_files = []
            all_dirs = []
            path_children = {}  # relative_path -> [children]
            
            # 从根目录开始收集，使用相对路径
            self._collect_directory_info_relative(code_root_path, "", all_files, all_dirs, path_children)
            
            # 使用Redis管道批量写入
            if self.redis_client:
                self._write_to_redis_pipeline(repo_url, branch, all_files, all_dirs, path_children)
            else:
                self._write_to_memory_cache(repo_url, branch, all_files, all_dirs, path_children)
            
            logger.info(f"代码目录缓存构建完成: {len(all_files)} 文件, {len(all_dirs)} 目录")
            return True
            
        except Exception as e:
            logger.error(f"构建代码目录缓存失败: {e}")
            return False
    
    # 向后兼容的方法名
    def build_directory_cache(self, root_path: Path, repo_url: str, branch: str) -> bool:
        """向后兼容的方法，调用新的代码目录缓存方法"""
        return self.build_code_directory_cache(root_path, repo_url, branch)
    
    def _collect_directory_info_relative(self, root_path: Path, relative_path: str, 
                                         all_files: List[Dict], all_dirs: List[Dict], 
                                         path_children: Dict[str, List[Dict]], 
                                         current_depth: int = 0):
        """递归收集目录信息，使用相对路径"""
        if current_depth >= self.max_depth:
            return
            
        try:
            children = []
            
            for item_path in root_path.iterdir():
                if self._should_ignore_directory(item_path) if item_path.is_dir() else self._should_ignore_file(item_path):
                    continue
                
                # 构建相对路径
                item_relative = f"{relative_path}/{item_path.name}".lstrip("/")
                
                try:
                    stat = item_path.stat()
                    item_info = {
                        "name": item_path.name,
                        "path": item_relative,  # 只使用相对路径
                        "is_directory": item_path.is_dir(),
                        "size": stat.st_size if item_path.is_file() else None,
                        "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat()
                    }
                    
                    children.append(item_info)
                    
                    if item_path.is_dir():
                        all_dirs.append(item_info)
                        # 递归处理子目录
                        self._collect_directory_info_relative(
                            item_path, item_relative, all_files, all_dirs, 
                            path_children, current_depth + 1
                        )
                    else:
                        all_files.append(item_info)
                        
                except (PermissionError, OSError) as e:
                    logger.warning(f"无法访问: {item_path}, 错误: {e}")
                    continue
            
            # 记录当前路径的子项
            path_children[relative_path] = children
            
        except (PermissionError, OSError) as e:
            logger.warning(f"无法访问目录: {root_path}, 错误: {e}")
    
    def _collect_directory_info(self, root_path: Path, relative_path: str, 
                               all_files: List[Dict], all_dirs: List[Dict], 
                               path_children: Dict[str, List[Dict]], 
                               current_depth: int = 0):
        """递归收集目录信息（向后兼容）"""
        return self._collect_directory_info_relative(root_path, relative_path, all_files, all_dirs, path_children, current_depth)
    
    def _write_to_redis_pipeline(self, repo_url: str, branch: str, 
                                all_files: List[Dict], all_dirs: List[Dict], 
                                path_children: Dict[str, List[Dict]]):
        """使用Redis管道批量写入缓存"""
        try:
            # 构建优化的搜索索引
            files_by_name = {}  # 文件名 -> [文件信息列表]
            files_by_path = {}  # 路径 -> 文件信息
            files_by_extension = {}  # 扩展名 -> [文件信息列表]
            
            for f in all_files:
                name = f["name"].lower()
                path = f["path"].lower()
                
                # 按文件名索引（支持多个同名文件）
                if name not in files_by_name:
                    files_by_name[name] = []
                files_by_name[name].append(f)
                
                # 按路径索引（唯一）
                files_by_path[path] = f
                
                # 按扩展名索引（支持复合扩展名）
                if '.' in name:
                    parts = name.split('.')
                    # 简单扩展名（如 .js）
                    ext = parts[-1]
                    if ext not in files_by_extension:
                        files_by_extension[ext] = []
                    files_by_extension[ext].append(f)
                    
                    # 复合扩展名（如 .min.js）
                    if len(parts) >= 3:
                        compound_ext = f"{parts[-2]}.{parts[-1]}"
                        if compound_ext not in files_by_extension:
                            files_by_extension[compound_ext] = []
                        files_by_extension[compound_ext].append(f)
            
            # 目录索引
            dirs_mapping = {d["name"].lower(): d for d in all_dirs}
            
            # 批量写入
            self.redis_client.mset({
                self._get_files_key(repo_url, branch): files_by_name,
                self._get_files_by_path_key(repo_url, branch): files_by_path,
                self._get_files_by_ext_key(repo_url, branch): files_by_extension,
                self._get_dirs_key(repo_url, branch): dirs_mapping,
                self._get_tree_key(repo_url, branch): {
                    "files": all_files,
                    "dirs": all_dirs,
                    "path_children": path_children,
                    "last_updated": datetime.now().isoformat()
                }
            }, expiration=self.cache_ttl)
            
            # 为每个路径创建子项集合
            path_data = {}
            for path, children in path_children.items():
                path_key = self._get_path_key(repo_url, branch, path)
                path_data[path_key] = children
            
            if path_data:
                self.redis_client.mset(path_data, expiration=self.cache_ttl)
            
            logger.debug(f"Redis缓存写入完成: {repo_url}:{branch}")
            
        except Exception as e:
            logger.error(f"Redis缓存写入失败: {e}")
            # 回退到内存缓存
            self._write_to_memory_cache(repo_url, branch, all_files, all_dirs, path_children)
    
    def _write_to_memory_cache(self, repo_url: str, branch: str,
                              all_files: List[Dict], all_dirs: List[Dict], 
                              path_children: Dict[str, List[Dict]]):
        """写入内存缓存"""
        cache_key = f"{repo_url}:{branch}"
        
        # 构建与Redis一致的索引结构
        files_by_name, files_by_path, files_by_extension = self._build_search_indices(all_files)
        dirs_mapping = {d["name"].lower(): d for d in all_dirs}
        
        self._memory_cache[cache_key] = {
            "files": all_files,
            "dirs": all_dirs,
            "files_by_name": files_by_name,
            "files_by_path": files_by_path,  
            "files_by_ext": files_by_extension,
            "dirs_mapping": dirs_mapping,
            "path_children": path_children,
            "last_updated": datetime.now().isoformat()
        }
        logger.debug(f"内存缓存写入完成: {cache_key}")
    
    def _count_nodes(self, node: FileNode) -> int:
        """统计节点数量"""
        count = 1
        if node.children:
            for child in node.children:
                count += self._count_nodes(child)
        return count
    
    def get_cache_info(self, repo_url: str, branch: str) -> Optional[Dict[str, Any]]:
        """获取缓存信息"""
        try:
            if self.redis_client:
                tree_key = self._get_tree_key(repo_url, branch)
                cache_data = self.redis_client.get(tree_key)
                if cache_data:
                    return cache_data
            
            # 回退到内存缓存
            cache_key = f"{repo_url}:{branch}"
            return self._memory_cache.get(cache_key)
            
        except Exception as e:
            logger.warning(f"获取缓存信息失败: {e}")
            return None
    
    def is_cached(self, repo_url: str, branch: str) -> bool:
        """检查是否已缓存"""
        return self.get_cache_info(repo_url, branch) is not None
    
    def search_files(self, repo_url: str, branch: str, pattern: str, 
                    code_root_path: Optional[Path] = None) -> List[Dict[str, Any]]:
        """快速搜索文件和目录（在代码缓存中搜索）"""
        try:
            if not self.is_cached(repo_url, branch):
                return []
            
            pattern_lower = pattern.lower()
            results = []
            
            # 获取不同的索引数据
            if self.redis_client:
                files_by_name = self.redis_client.get(self._get_files_key(repo_url, branch)) or {}
                files_by_path = self.redis_client.get(self._get_files_by_path_key(repo_url, branch)) or {}
                files_by_ext = self.redis_client.get(self._get_files_by_ext_key(repo_url, branch)) or {}
                dirs_data = self.redis_client.get(self._get_dirs_key(repo_url, branch)) or {}
            else:
                # 从内存缓存获取
                cache_data = self.get_cache_info(repo_url, branch)
                if not cache_data:
                    return []
                # 使用预构建的索引或重建索引
                files_by_name = cache_data.get("files_by_name")
                files_by_path = cache_data.get("files_by_path")
                files_by_ext = cache_data.get("files_by_ext")
                dirs_data = cache_data.get("dirs_mapping")
                
                # 如果没有预构建索引（旧缓存），则重建
                if not files_by_name:
                    files_by_name, files_by_path, files_by_ext = self._build_search_indices(cache_data.get("files", []))
                    dirs_data = {d["name"].lower(): d for d in cache_data.get("dirs", [])}
            
            found_files = set()  # 避免重复结果
            
            # 策略1: 精确文件名匹配 (O(1))
            if pattern_lower in files_by_name:
                for file_info in files_by_name[pattern_lower]:
                    if file_info["path"] not in found_files:
                        found_files.add(file_info["path"])
                        self._add_file_result(results, file_info, code_root_path)
            
            # 策略2: 文件名包含匹配
            for name, file_list in files_by_name.items():
                if pattern_lower in name:
                    for file_info in file_list:
                        if file_info["path"] not in found_files:
                            found_files.add(file_info["path"])
                            self._add_file_result(results, file_info, code_root_path)
            
            # 策略3: 扩展名匹配 (如果pattern看起来像扩展名)
            if '.' in pattern_lower:
                # 支持复合扩展名搜索，如 "min.js"
                if pattern_lower in files_by_ext:
                    for file_info in files_by_ext[pattern_lower]:
                        if file_info["path"] not in found_files:
                            found_files.add(file_info["path"])
                            self._add_file_result(results, file_info, code_root_path)
                
                # 同时支持简单扩展名搜索，如 "js"
                ext_pattern = pattern_lower.split('.')[-1]
                if ext_pattern in files_by_ext:
                    for file_info in files_by_ext[ext_pattern]:
                        if file_info["path"] not in found_files:
                            found_files.add(file_info["path"])
                            self._add_file_result(results, file_info, code_root_path)
            
            # 策略4: 路径包含匹配（如果前面策略找到的结果不够）
            for path, file_info in files_by_path.items():
                    if pattern_lower in path and file_info["path"] not in found_files:
                        found_files.add(file_info["path"])
                        self._add_file_result(results, file_info, code_root_path)
            
            # 搜索目录
            for name, dir_info in dirs_data.items():
                if pattern_lower in name or pattern_lower in dir_info["path"].lower():
                    self._add_dir_result(results, dir_info, code_root_path)
            
            # 按相关性排序
            results.sort(key=lambda x: (
                not x["is_directory"],  # 目录优先
                x["virtual_path"].lower().find(pattern_lower) if pattern_lower in x["virtual_path"].lower() else 999,  # 匹配位置
                len(x["virtual_path"]),  # 较短路径优先
                x["virtual_path"].lower()  # 字母顺序
            ))
            
            return results
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return []
    
    def _build_search_indices(self, all_files: List[Dict]) -> Tuple[Dict, Dict, Dict]:
        """构建搜索索引（内存缓存回退时使用）"""
        files_by_name = {}
        files_by_path = {}
        files_by_extension = {}
        
        for f in all_files:
            name = f["name"].lower()
            path = f["path"].lower()
            
            # 按文件名索引
            if name not in files_by_name:
                files_by_name[name] = []
            files_by_name[name].append(f)
            
            # 按路径索引
            files_by_path[path] = f
            
            # 按扩展名索引（支持复合扩展名）
            if '.' in name:
                parts = name.split('.')
                # 简单扩展名（如 .js）
                ext = parts[-1]
                if ext not in files_by_extension:
                    files_by_extension[ext] = []
                files_by_extension[ext].append(f)
                
                # 复合扩展名（如 .min.js）
                if len(parts) >= 3:
                    compound_ext = f"{parts[-2]}.{parts[-1]}"
                    if compound_ext not in files_by_extension:
                        files_by_extension[compound_ext] = []
                    files_by_extension[compound_ext].append(f)
        
        return files_by_name, files_by_path, files_by_extension
    
    def _add_file_result(self, results: List, file_info: Dict, code_root_path: Optional[Path]):
        """添加文件结果到搜索结果列表"""
        real_path = str(code_root_path / file_info["path"]) if code_root_path else file_info["path"]
        results.append({
            "real_path": real_path,
            "virtual_path": file_info["path"],
            "is_directory": False,
            "size": file_info.get("size"),
            "modified_time": file_info.get("modified_time")
        })
    
    def _add_dir_result(self, results: List, dir_info: Dict, code_root_path: Optional[Path]):
        """添加目录结果到搜索结果列表"""
        real_path = str(code_root_path / dir_info["path"]) if code_root_path else dir_info["path"]
        virtual_path = dir_info["path"] + "/" if not dir_info["path"].endswith("/") else dir_info["path"]
        results.append({
            "real_path": real_path,
            "virtual_path": virtual_path,
            "is_directory": True,
            "size": None,
            "modified_time": dir_info.get("modified_time")
        })
    
    def browse_directory(self, repo_url: str, branch: str, virtual_path: str = "") -> List[Dict[str, Any]]:
        """浏览指定目录"""
        try:
            if not self.is_cached(repo_url, branch):
                return []
            
            # 规范化路径
            virtual_path = virtual_path.strip("/")
            
            if self.redis_client:
                # 从Redis获取路径子项
                path_key = self._get_path_key(repo_url, branch, virtual_path)
                children = self.redis_client.get(path_key) or []
            else:
                # 从内存缓存获取
                cache_data = self.get_cache_info(repo_url, branch)
                if not cache_data:
                    return []
                children = cache_data.get("path_children", {}).get(virtual_path, [])
            
            results = []
            for child in children:
                child_path = child["path"]
                if child["is_directory"] and not child_path.endswith("/"):
                    child_path += "/"
                
                results.append({
                    "real_path": child["real_path"],
                    "virtual_path": child_path,
                    "is_directory": child["is_directory"],
                    "size": child.get("size"),
                    "modified_time": child.get("modified_time")
                })
            
            # 排序：目录在前，文件在后
            results.sort(key=lambda x: (not x["is_directory"], x["virtual_path"].lower()))
            
            return results
            
        except Exception as e:
            logger.error(f"浏览目录失败: {e}")
            return []
    
    def _find_node_by_path(self, tree: FileNode, virtual_path: str) -> Optional[FileNode]:
        """根据虚拟路径找到节点"""
        if not virtual_path or virtual_path == "/" or virtual_path == tree.name:
            return tree
        
        path_parts = virtual_path.strip("/").split("/")
        current_node = tree
        
        for part in path_parts:
            if not current_node.children:
                return None
            
            found = False
            for child in current_node.children:
                if child.name == part:
                    current_node = child
                    found = True
                    break
            
            if not found:
                return None
        
        return current_node
    
    def _set_cache(self, key: str, data: Any, ttl: int):
        """设置缓存"""
        try:
            if self.redis_client:
                self.redis_client.setex(key, ttl, json.dumps(data, ensure_ascii=False))
            else:
                # 内存缓存
                expire_time = datetime.now() + timedelta(seconds=ttl)
                self._memory_cache[key] = {
                    "data": data,
                    "expire_time": expire_time
                }
        except Exception as e:
            logger.warning(f"缓存设置失败: {e}")
    
    def _get_cache(self, key: str) -> Optional[Any]:
        """获取缓存"""
        try:
            if self.redis_client:
                cached = self.redis_client.get(key)
                if cached:
                    return json.loads(cached)
            else:
                # 内存缓存
                cached = self._memory_cache.get(key)
                if cached:
                    if datetime.now() < cached["expire_time"]:
                        return cached["data"]
                    else:
                        del self._memory_cache[key]
            
            return None
        except Exception as e:
            logger.warning(f"缓存获取失败: {e}")
            return None
    
    def invalidate_cache(self, repo_url: str, branch: str):
        """清除缓存"""
        try:
            if self.redis_client:
                # 获取所有相关的键
                project_key = self._get_project_key(repo_url, branch)
                pattern = f"{project_key}*"
                
                # 删除所有相关键
                keys_to_delete = []
                keys_to_delete.extend([
                    self._get_tree_key(repo_url, branch),
                    self._get_files_key(repo_url, branch),
                    self._get_dirs_key(repo_url, branch)
                ])
                
                # 删除路径键需要先获取缓存信息
                cache_data = self.get_cache_info(repo_url, branch)
                if cache_data and "path_children" in cache_data:
                    for path in cache_data["path_children"].keys():
                        keys_to_delete.append(self._get_path_key(repo_url, branch, path))
                
                # 批量删除
                if keys_to_delete:
                    self.redis_client.delete(*keys_to_delete)
            else:
                # 清除内存缓存
                cache_key = f"{repo_url}:{branch}"
                self._memory_cache.pop(cache_key, None)
            
            logger.info(f"缓存已清除: {repo_url}:{branch}")
        except Exception as e:
            logger.warning(f"缓存清除失败: {e}")
    
    def get_cache_stats(self, repo_url: str, branch: str) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            cache_data = self.get_cache_info(repo_url, branch)
            if not cache_data:
                return {
                    "cached": False,
                    "last_updated": None,
                    "file_count": 0,
                    "dir_count": 0
                }
            
            return {
                "cached": True,
                "last_updated": cache_data.get("last_updated"),
                "file_count": len(cache_data.get("files", [])),
                "dir_count": len(cache_data.get("dirs", [])),
                "cache_type": "redis" if self.redis_client else "memory"
            }
            
        except Exception as e:
            logger.error(f"获取缓存统计失败: {e}")
            return {"cached": False, "error": str(e)}
    
    def prebuild_cache_for_projects(self, projects: List[Dict[str, str]]) -> Dict[str, bool]:
        """
        预构建多个项目的缓存
        
        Args:
            projects: 项目列表，每个项目包含 repo_url, branch, root_path
            
        Returns:
            每个项目的构建结果
        """
        results = {}
        
        for project in projects:
            repo_url = project.get("repo_url")
            branch = project.get("branch", "main")
            root_path = Path(project.get("root_path", ""))
            
            try:
                logger.info(f"预构建缓存: {repo_url}:{branch}")
                success = self.build_directory_cache(root_path, repo_url, branch)
                results[f"{repo_url}:{branch}"] = success
                
                if success:
                    logger.info(f"预构建成功: {repo_url}:{branch}")
                else:
                    logger.warning(f"预构建失败: {repo_url}:{branch}")
                    
            except Exception as e:
                logger.error(f"预构建异常: {repo_url}:{branch}, 错误: {e}")
                results[f"{repo_url}:{branch}"] = False
        
        return results


# 全局缓存服务实例
_directory_cache_service = None

def get_directory_cache_service() -> DirectoryCacheService:
    """获取目录缓存服务实例"""
    global _directory_cache_service
    if _directory_cache_service is None:
        _directory_cache_service = DirectoryCacheService()
    return _directory_cache_service
