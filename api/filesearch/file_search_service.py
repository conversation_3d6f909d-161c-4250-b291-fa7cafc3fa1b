"""
文件搜索服务
在主web容器中搜索文件，将路径映射为沙箱容器的虚拟路径进行显示
"""

import os
import logging
import subprocess
import shlex
from pathlib import Path
from typing import List, Dict, Optional, Tuple

from ..utils.path_mapping import get_path_mapping_service
from ..utils.file_filter_service import get_file_filter_service

logger = logging.getLogger(__name__)


class FileSearchService:
    """文件搜索服务类"""
    
    DEFAULT_EXCLUDE_DIRS = [
        'env', 'dist', 'venv', 'obj', '.git', '.tox', 'coverage', 'bin',
        '.idea', '__pycache__', '.mypy_cache', 'build', '.pytest_cache',
        '.vscode', 'node_modules', 'target', '.env', '.gemini'
    ]
    
    def __init__(self, base_path: Optional[str] = None, use_fast_search: bool = True):
        """初始化文件搜索服务"""
        # 初始化路径映射服务
        self.path_mapping_service = get_path_mapping_service()
        
        # 初始化文件过滤服务
        self.file_filter = get_file_filter_service()
        
        # 搜索配置常量
        self.MAX_SEARCH_DEPTH = 15  # 降低搜索深度
        self.FIND_TIMEOUT = 120     # 增加超时时间到2分钟
        self.MAX_RESULTS = 1000     # 最大结果数量
        self.LARGE_DIR_THRESHOLD = 500  # 大目录阈值
        
        # 检测可用工具并设置优先级
        self.available_tools = self._detect_available_tools()
        self.use_fast_search = use_fast_search and len(self.available_tools) > 0
        logger.info(f"可用搜索工具: {self.available_tools}")
    
    def _detect_available_tools(self) -> List[str]:
        """检测可用的搜索工具，按优先级排序"""
        tools = []
        # 优先级：fd > ripgrep > find
        fd_commands = ['fd', 'fdfind']
        for fd_cmd in fd_commands:
            if self._check_tool_available(fd_cmd):
                tools.append(fd_cmd)
                break

        for tool in ['rg', 'find']:
            if self._check_tool_available(tool):
                tools.append(tool)
        return tools
    
    def _check_tool_available(self, tool_name: str) -> bool:
        """检查工具是否可用"""
        try:
            result = subprocess.run([tool_name, '--version'], 
                                  capture_output=True, 
                                  timeout=3)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, Exception):
            return False
    
    def get_project_paths(self, owner: str, repo_name: str, branch: str, user_code: str) -> Dict[str, Path]:
        """
        获取项目相关的路径映射
        
        Args:
            owner: 仓库所有者
            repo_name: 仓库名称
            branch: 分支名称
            user_code: 用户代码
            
        Returns:
            路径映射字典
        """
        return self.path_mapping_service.get_project_paths(owner, repo_name, branch, user_code)
    
    def _should_ignore(self, file_path: Path) -> bool:
        """
        检查文件或目录是否应该被忽略
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否应该忽略
        """
        # 使用统一的文件过滤服务
        if file_path.is_dir():
            return self.file_filter.should_ignore_directory(file_path)
        else:
            return self.file_filter.should_ignore_file(file_path)
    
    def _should_ignore_directory(self, dir_path: Path) -> bool:
        """
        检查目录是否应该被忽略
        
        Args:
            dir_path: 目录路径
            
        Returns:
            是否应该忽略
        """
        return self.file_filter.should_ignore_directory(dir_path)
    
    def _should_ignore_file(self, file_path: Path) -> bool:
        """
        检查文件是否应该被忽略
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否应该忽略
        """
        return self.file_filter.should_ignore_file(file_path)

    def search_files(self, owner: str, repo_name: str, branch: str, user_code: str, 
                    pattern: str = "") -> List[Dict[str, str]]:
        """
        搜索文件，根据pattern自动判断搜索模式
        
        Args:
            owner: 仓库所有者
            repo_name: 仓库名称
            branch: 分支名称
            user_code: 用户代码
            pattern: 搜索模式，支持以下格式：
                    - "ai" 或 "/ai": 在所有根目录搜索
                    - "code/src": 在code目录下搜索
                    - "code/": 浏览code目录
                    - "/workspace/test/": 浏览workspace/test目录
            
        Returns:
            搜索结果列表，每个元素包含 real_path 和 virtual_path
        """
        # 解析搜索pattern
        search_mode, search_prefix, search_path, search_query = self._parse_search_pattern(pattern)
        
        logger.debug(f"搜索模式: {search_mode}, 前缀: {search_prefix}, 路径: {search_path}, 查询: {search_query}")
        
        if search_mode == "browse":
            # 目录浏览模式
            return self._browse_directory(owner, repo_name, branch, user_code, search_prefix, search_path)
        elif search_mode == "search_in_dir":
            # 在指定目录下搜索
            return self._search_in_directory(owner, repo_name, branch, user_code, search_prefix, search_path, search_query)
        else:
            # 全局搜索模式
            return self._search_globally(owner, repo_name, branch, user_code, search_query)

    def _browse_directory(self, owner: str, repo_name: str, branch: str, user_code: str,
                         prefix: str, sub_path: str) -> List[Dict[str, str]]:
        """
        浏览指定目录的内容
        
        Args:
            owner: 仓库所有者
            repo_name: 仓库名称
            branch: 分支名称
            user_code: 用户代码
            prefix: 目录前缀 (code, i-doc, o-doc, workspace)
            sub_path: 子路径
            
        Returns:
            目录内容列表
        """
        try:
            # 如果没有前缀，返回根目录列表
            if not prefix:
                return self._get_root_directories(owner, repo_name, branch, user_code)
            
            # sys-gemini文件夹不能被问答引用
            if prefix.strip("/") == "project-gemini":
                return []

            # 获取实际路径
            base_real_path = self.path_mapping_service.get_real_path_from_virtual_prefix(
                prefix, owner, repo_name, branch, user_code
            )

            logger.info(f"base_real_path: {base_real_path}")
            
            if not base_real_path:
                logger.warning(f"无效的路径前缀: {prefix}")
                return []
            
            # 构建完整的实际路径
            if sub_path:
                real_path = base_real_path / sub_path
            else:
                real_path = base_real_path
            
            if not real_path.exists():
                # 如果路径不存在，有可能是沙箱软链接，尝试转换成本地路径
                converted_symlink_path = self._convert_symlink_path(real_path, owner, repo_name, branch, user_code)
                if converted_symlink_path:
                    real_path = converted_symlink_path
                logger.info(f"real_path: {real_path.as_posix()}")
            
            if not real_path.exists() or not real_path.is_dir():
                logger.warning(f"目录不存在: {real_path}")
                return []
            
            # 列出目录内容
            results = []
            for item in real_path.iterdir():
                if self._should_ignore(item):
                    continue

                # 如果路径不存在，尝试解析沙箱软链接
                if not item.exists():
                    converted_symlink_path = self._convert_symlink_path(item, owner, repo_name, branch, user_code)
                    if converted_symlink_path:
                        item = converted_symlink_path
                    logger.info(f"item: {item.as_posix()}")
                
                # 构建虚拟路径
                if sub_path:
                    virtual_path = f"{prefix}/{sub_path}/{item.name}"
                else:
                    virtual_path = f"{prefix}/{item.name}"
                
                if item.is_dir():
                    virtual_path += "/"
                
                results.append({
                    "real_path": str(item),
                    "virtual_path": virtual_path,
                    "is_directory": item.is_dir()
                })
                
            
            # 排序：目录在前，文件在后
            results.sort(key=lambda x: (not x["is_directory"], x["virtual_path"].lower()))
            return results
                    
        except Exception as e:
            logger.error(f"浏览目录失败: {e}")
            return []
    
    def _search_in_directory(self, owner: str, repo_name: str, branch: str, user_code: str,
                           prefix: str, sub_path: str, search_query: str) -> List[Dict[str, str]]:
        """
        在指定目录下搜索文件
        
        Args:
            owner: 仓库所有者
            repo_name: 仓库名称
            branch: 分支名称
            user_code: 用户代码
            prefix: 目录前缀 (code, i-doc, o-doc, workspace)
            sub_path: 子路径
            search_query: 搜索查询词
            
        Returns:
            搜索结果列表
        """
        try:
            # 获取实际路径
            base_real_path = self.path_mapping_service.get_real_path_from_virtual_prefix(
                prefix, owner, repo_name, branch, user_code
            )

            logger.info(f"base_real_path: {base_real_path.as_posix()}")
            
            if not base_real_path:
                logger.warning(f"无效的路径前缀: {prefix}")
                return []
            
            # 构建搜索路径
            if sub_path:
                search_path = base_real_path / sub_path
            else:
                search_path = base_real_path

            if not search_path.exists():
                # 如果路径不存在，有可能是沙箱软链接，尝试转换成本地路径
                converted_symlink_path = self._convert_symlink_path(search_path, owner, repo_name, branch, user_code)
                if converted_symlink_path:
                    search_path = converted_symlink_path
                logger.info(f"search_path: {search_path.as_posix()}")
            
            if not search_path.exists() or not search_path.is_dir():
                logger.warning(f"搜索目录不存在: {search_path}")
                return []
            
            # 统一使用直接搜索方法（文件）
            file_results = self._search_file_in_directory(search_path, search_query, prefix, sub_path)

            # 同时收集一级子目录中名称匹配的目录
            dir_results: List[Dict[str, str]] = []
            try:
                pattern_lower = (search_query or "").lower()
                for item in search_path.iterdir():
                    # 如果路径不存在，尝试解析沙箱软链接
                    if not item.exists():
                        converted_symlink_path = self._convert_symlink_path(item, owner, repo_name, branch, user_code)
                        if converted_symlink_path:
                            item = converted_symlink_path
                        logger.info(f"item: {item.as_posix()}")

                    if not item.is_dir():
                        continue
                    if self._should_ignore(item):
                        continue
                    # 仅匹配一级子目录名称
                    if pattern_lower and pattern_lower not in item.name.lower():
                        continue
                    # 构建虚拟路径
                    if sub_path:
                        virtual_path = f"{prefix}/{sub_path}/{item.name}/"
                    else:
                        virtual_path = f"{prefix}/{item.name}/"
                    dir_results.append({
                        "real_path": str(item),
                        "virtual_path": virtual_path,
                        "is_directory": True
                    })
            except Exception as e:
                logger.warning(f"列举一级子目录失败: {search_path}, 错误: {e}")

            # 合并并按目录优先、路径字母序排序；去重基于 virtual_path
            merged = {r["virtual_path"]: r for r in (dir_results + file_results)}
            results = list(merged.values())
            results.sort(key=lambda x: (not x["is_directory"], x["virtual_path"].lower()))
            return results
                
        except Exception as e:
            logger.error(f"目录搜索失败: {e}")
            return []
    
    def _search_globally(self, owner: str, repo_name: str, branch: str, user_code: str,
                        search_query: str) -> List[Dict[str, str]]:
        """
        全局搜索文件
        
        Args:
            owner: 仓库所有者
            repo_name: 仓库名称
            branch: 分支名称
            user_code: 用户代码
            search_query: 搜索查询词
            
        Returns:
            搜索结果列表
        """
        try:
            all_results = []
            
            # 获取项目路径
            code_path = self.path_mapping_service.get_code_path(owner, repo_name, branch)
            workspace_path = self.path_mapping_service.get_workspace_path(owner, repo_name, branch, user_code)
            project_paths = self.path_mapping_service.get_project_paths(owner, repo_name, branch, user_code) or {}
            project_workspace_path = project_paths.get("real_project_workspace_path")
            
            # 搜索code目录
            if code_path.exists():
                code_results = self._search_file_in_directory(code_path, search_query, "code", "")
                all_results.extend(code_results)
            
            # 搜索共享 i-doc/o-doc 目录
            try:
                if project_workspace_path:
                    from pathlib import Path as _P
                    pwp = _P(project_workspace_path)
                    i_doc_dir = pwp / "i-doc"
                    o_doc_dir = pwp / "o-doc"
                    # project_gemini_dir = pwp / "project-gemini"
                    if i_doc_dir.exists() and i_doc_dir.is_dir():
                        all_results.extend(self._search_file_in_directory(i_doc_dir, search_query, "i-doc", ""))
                    if o_doc_dir.exists() and o_doc_dir.is_dir():
                        all_results.extend(self._search_file_in_directory(o_doc_dir, search_query, "o-doc", ""))
                    # if project_gemini_dir.exists() and project_gemini_dir.is_dir():
                    #     all_results.extend(self._search_file_in_directory(project_gemini_dir, search_query, "project-gemini", ""))
            except Exception:
                pass
            
            # 搜索用户 userspace 目录  
            if workspace_path.exists():
                workspace_results = self._search_file_in_directory(workspace_path, search_query, "userspace", "")
                all_results.extend(workspace_results)
            
            # 排序并限制结果数量
            all_results.sort(key=lambda x: (not x["is_directory"], x["virtual_path"].lower()))
            return all_results
            
        except Exception as e:
            logger.error(f"全局搜索失败: {e}")
            return []
    
    def _search_file_in_directory(self, search_path: Path, search_query: str,
                              prefix: str, sub_path: str) -> List[Dict[str, str]]:
        """直接搜索文档目录"""
        try:
            results = []
            
            # 使用_scan_directory扫描目录，直接传入搜索模式
            all_files = self._scan_directory(search_path, search_pattern=search_query, max_depth=self.MAX_SEARCH_DEPTH)
            
            for file_path in all_files:
                try:
                    # 计算相对路径
                    relative_path = file_path.relative_to(search_path)
                    
                    # 构建虚拟路径
                    if sub_path:
                        virtual_path = f"{prefix}/{sub_path}/{relative_path.as_posix()}"
                    else:
                        # 避免重复前缀
                        rel_posix = relative_path.as_posix()
                        if prefix and rel_posix.startswith(f"{prefix}/"):
                            virtual_path = rel_posix
                        else:
                            virtual_path = f"{prefix}/{rel_posix}"
                    
                    if file_path.is_dir():
                        virtual_path += "/"
            
                    results.append({
                        "real_path": str(file_path),
                        "virtual_path": virtual_path,
                        "is_directory": file_path.is_dir()
                    })
                    
                except ValueError:
                    # 如果无法计算相对路径，跳过
                    continue
        
            # 排序：目录在前，文件在后
            results.sort(key=lambda x: (not x["is_directory"], x["virtual_path"].lower()))
            return results
        except Exception as e:
            logger.error(f"搜索文档目录失败: {e}")
            return []

    def _scan_directory(self, directory: Path, search_pattern: str = None, max_depth: int = None) -> List[Path]:
        """
        扫描目录 - 智能选择搜索方法
        
        Args:
            directory: 目录路径
            search_pattern: 搜索模式（可选）
            max_depth: 最大深度（可选，默认使用配置值）
            
        Returns:
            文件路径列表
        """
        # 使用配置的默认深度
        if max_depth is None:
            max_depth = self.MAX_SEARCH_DEPTH
            
        # 对于大型目录，优先使用迭代搜索避免超时
        if self._is_large_directory(directory):
            logger.debug(f"检测到大型目录，使用迭代扫描: {directory}")
            return self._scan_directory_iterative(directory, search_pattern, max_depth)
        elif self.use_fast_search:
            # 优先使用 find 命令进行快速搜索
            logger.debug(f"使用 find 命令搜索: {directory}, 模式: {search_pattern}")
            return self._scan_directory_fast(directory, search_pattern, max_depth)
        else:
            # 回退到递归搜索
            logger.debug(f"使用迭代扫描: {directory}, 模式: {search_pattern}")
            return self._scan_directory_iterative(directory, search_pattern, max_depth)
    
    def _is_large_directory(self, directory: Path) -> bool:
        """
        检查目录是否为大型目录
        
        Args:
            directory: 目录路径
            
        Returns:
            是否为大型目录
        """
        try:
            # 更精确地检查目录大小
            item_count = 0
            for item in directory.iterdir():
                item_count += 1
                # 如果项目数量超过阈值，认为是大型目录
                if item_count > self.LARGE_DIR_THRESHOLD:
                    logger.debug(f"目录 {directory} 项目数量超过阈值 {self.LARGE_DIR_THRESHOLD}")
                    return True
            return False
        except Exception as e:
            logger.warning(f"无法检查目录大小 {directory}: {e}")
            # 如果无法访问目录，默认认为是大型目录
            return True

    def _scan_directory_fast(self, directory: Path, search_pattern: str = None, max_depth: int = None) -> List[Path]:
        """使用最快可用的命令快速搜索目录"""
        if max_depth is None:
            max_depth = self.MAX_SEARCH_DEPTH
            
        if not directory.exists() or not directory.is_dir():
            return []
        
        # 根据可用工具选择搜索方法，优先级：fd > ripgrep > find
        if any(fd_cmd in self.available_tools for fd_cmd in ['fd', 'fdfind']):
            fd_cmd = next((fd_cmd for fd_cmd in ['fd', 'fdfind'] if fd_cmd in self.available_tools), 'fd')
            logger.debug(f"使用 {fd_cmd} 搜索: {directory}, 模式: {search_pattern}")
            return self._scan_with_fd(directory, search_pattern, max_depth, fd_cmd)
        elif 'rg' in self.available_tools:
            logger.debug(f"使用 ripgrep 搜索: {directory}, 模式: {search_pattern}")
            return self._scan_with_ripgrep(directory, search_pattern, max_depth)
        elif 'find' in self.available_tools:
            logger.debug(f"使用 find 搜索: {directory}, 模式: {search_pattern}")
            return self._scan_with_find(directory, search_pattern, max_depth)
        else:
            # 没有可用工具，回退到迭代搜索
            logger.warning("没有可用的快速搜索工具，使用迭代搜索")
            return self._scan_directory_iterative(directory, search_pattern, max_depth)
    
    def _scan_with_ripgrep(self, directory: Path, search_pattern: str = None, max_depth: int = None) -> List[Path]:
        """使用 ripgrep 搜索"""
        files = []
        try:
            cmd = ['rg', '--files']
            
            # 设置搜索根目录
            cmd.extend(['--search-zip', '--hidden'])  # 包含隐藏文件但排除.git等
            
            # 深度限制
            if max_depth:
                cmd.extend([f'--max-depth={max_depth}'])
            
            # 排除目录（ripgrep 默认排除 .git 等）
            for dir_name in self.DEFAULT_EXCLUDE_DIRS:
                cmd.extend([f'--glob=!{dir_name}/**'])
            
            # 如果有搜索模式
            if search_pattern:
                # ripgrep 默认就是模糊搜索，这里可以添加更精确的控制
                cmd.extend([f'--glob=*{search_pattern}*'])
            
            # 设置搜索目录
            cmd.append(str(directory))
            
            logger.debug(f"执行 ripgrep 命令: {' '.join(shlex.quote(str(arg)) for arg in cmd)}")
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=self.FIND_TIMEOUT
            )
            
            if result.returncode == 0 or result.returncode == 1:  # rg 成功或未找到匹配都算成功
                lines = result.stdout.strip().split('\n')
                processed_count = 0
                
                for line in lines:
                    if line.strip() and processed_count < self.MAX_RESULTS:
                        path = Path(line.strip())
                        if path.exists() and not self._should_filter_file(path):
                            files.append(path)
                            processed_count += 1
                    elif processed_count >= self.MAX_RESULTS:
                        logger.warning(f"ripgrep 结果过多，限制为 {self.MAX_RESULTS} 个文件")
                        break
            else:
                logger.warning(f"ripgrep 命令执行失败: {result.stderr}")
                return self._scan_directory_iterative(directory, search_pattern, max_depth)
                
        except subprocess.TimeoutExpired:
            logger.warning(f"ripgrep 命令超时 ({self.FIND_TIMEOUT}秒)，回退到迭代搜索模式")
            return self._scan_directory_iterative(directory, search_pattern, max_depth)
        except Exception as e:
            logger.error(f"ripgrep 命令出错: {e}，回退到迭代搜索模式")
            return self._scan_directory_iterative(directory, search_pattern, max_depth)
        
        return files

    def _scan_with_fd(self, directory: Path, search_pattern: str = None, max_depth: int = None, fd_cmd: str = 'fd') -> List[Path]:
        """使用 fd 搜索"""
        files = []
        try:
            cmd = [fd_cmd, '--type', 'f']

            if max_depth:
                cmd.extend([f'--max-depth={max_depth}'])

            for dir_name in self.DEFAULT_EXCLUDE_DIRS:
                cmd.extend([f'--exclude={dir_name}'])

            if search_pattern:
                cmd.append(search_pattern)

            cmd.append(str(directory))

            logger.debug(f"执行 fd 命令: {' '.join(shlex.quote(str(arg)) for arg in cmd)}")

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=self.FIND_TIMEOUT
            )

            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                processed_count = 0

                for line in lines:
                    if line.strip() and processed_count < self.MAX_RESULTS:
                        path = Path(line.strip())
                        if path.exists() and not self._should_filter_file(path):
                            files.append(path)
                            processed_count += 1
                    elif processed_count >= self.MAX_RESULTS:
                        logger.warning(f"fd 结果过多，限制为 {self.MAX_RESULTS} 个文件")
                        break
            else:
                logger.warning(f"fd 命令执行失败: {result.stderr}")
                return self._scan_directory_iterative(directory, search_pattern, max_depth)

        except subprocess.TimeoutExpired:
            logger.warning(f"fd 命令超时 ({self.FIND_TIMEOUT}秒)，回退到迭代搜索模式")
            return self._scan_directory_iterative(directory, search_pattern, max_depth)
        except Exception as e:
            logger.error(f"fd 命令出错: {e}，回退到迭代搜索模式")
            return self._scan_directory_iterative(directory, search_pattern, max_depth)

        return files

    def _scan_with_find(self, directory: Path, search_pattern: str = None, max_depth: int = None) -> List[Path]:
        """使用 find 命令搜索（作为备选）"""
        files = []
        try:
            # 构建 find 命令
            cmd = ['find', str(directory), '-maxdepth', str(max_depth)]
            
            # 排除目录 - 确保所有常见目录都被排除
            all_exclude_dirs = set(self.DEFAULT_EXCLUDE_DIRS)
            if hasattr(self.file_filter, 'ignore_directories'):
                all_exclude_dirs.update(self.file_filter.ignore_directories)
            
            for dir_name in all_exclude_dirs:
                cmd.extend(['-not', '-path', f'*/{dir_name}/*'])
            
            # 只查找文件
            cmd.extend(['-type', 'f'])
            
            # 如果有搜索模式，使用更精确的匹配
            if search_pattern:
                # 对于较长的搜索词，使用更精确的匹配
                if len(search_pattern) > 2:
                    cmd.extend(['-name', f'*{search_pattern}*'])
                else:
                    # 对于很短的搜索词，使用大小写不敏感匹配
                    cmd.extend(['-iname', f'*{search_pattern}*'])
            
            # 限制结果数量
            cmd.extend(['-print'])
            
            logger.debug(f"执行 find 命令: {' '.join(shlex.quote(str(arg)) for arg in cmd)}")
            
            # 执行命令 - 使用配置的超时时间
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=self.FIND_TIMEOUT
            )
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                processed_count = 0
                
                for line in lines:
                    if line.strip() and processed_count < self.MAX_RESULTS:
                        path = Path(line.strip())
                        # 应用文件过滤
                        if not self._should_filter_file(path):
                            files.append(path)
                            processed_count += 1
                    elif processed_count >= self.MAX_RESULTS:
                        logger.warning(f"find 命令结果过多，限制为 {self.MAX_RESULTS} 个文件")
                        break
            else:
                logger.warning(f"find 命令执行失败: {result.stderr}")
                # 回退到迭代搜索
                logger.info("回退到迭代搜索模式")
                return self._scan_directory_iterative(directory, search_pattern, max_depth)
                
        except subprocess.TimeoutExpired:
            logger.warning(f"find 命令超时 ({self.FIND_TIMEOUT}秒)，回退到迭代搜索模式")
            return self._scan_directory_iterative(directory, search_pattern, max_depth)
        except Exception as e:
            logger.error(f"find 命令出错: {e}，回退到迭代搜索模式")
            return self._scan_directory_iterative(directory, search_pattern, max_depth)
        
        return files
    
    def _should_filter_file(self, file_path: Path) -> bool:
        """检查文件是否应该被过滤"""
        return self.file_filter.should_ignore_file(file_path)
    
    def _scan_directory_iterative(self, directory: Path, search_pattern: str = None, max_depth: int = None) -> List[Path]:
        """使用迭代方式扫描目录，支持最大深度与名称过滤。"""
        if max_depth is None:
            max_depth = self.MAX_SEARCH_DEPTH
            
        if not directory.exists() or not directory.is_dir():
            return []
        from collections import deque
        queue = deque([(directory, 0)])
        results: List[Path] = []
        pattern_lower = search_pattern.lower() if search_pattern else None
        
        while queue and len(results) < self.MAX_RESULTS:
            current_dir, depth = queue.popleft()
            if depth >= max_depth:
                continue
            try:
                for item_path in current_dir.iterdir():
                    if self._should_ignore(item_path):
                        continue
                    # 名称匹配（若提供）
                    if pattern_lower and pattern_lower not in item_path.name.lower():
                        # 目录仍需进入以便发现更深层匹配
                        if item_path.is_dir() and depth + 1 < max_depth:
                            queue.append((item_path, depth + 1))
                        continue
                    results.append(item_path)
                    if item_path.is_dir() and depth + 1 < max_depth:
                        queue.append((item_path, depth + 1))
                        
                    # 如果结果过多，提前退出
                    if len(results) >= self.MAX_RESULTS:
                        logger.warning(f"搜索结果过多，限制为{self.MAX_RESULTS}个文件")
                        break
            except PermissionError:
                logger.warning(f"无法访问目录: {current_dir}")
            except Exception as e:
                logger.error(f"扫描目录时出错 {current_dir}: {e}")
        return results[:self.MAX_RESULTS]  # 确保不超过最大结果数
    
    def _scan_directory_recursive(self, root_dir: Path, current_dir: Path, files: List[Path], max_depth: int, current_depth: int):
        """
        递归扫描目录
        
        Args:
            root_dir: 根目录
            current_dir: 当前目录
            files: 文件列表
            max_depth: 最大深度
            current_depth: 当前深度
        """
        if current_depth >= max_depth:
            return
            
        try:
            for item_path in current_dir.iterdir():
                # 检查是否应该忽略
                if self._should_ignore(item_path):
                    continue
                
                if item_path.is_dir():
                    # 添加目录到结果
                    files.append(item_path)
                    # 递归扫描子目录
                    self._scan_directory_recursive(root_dir, item_path, files, max_depth, current_depth + 1)
                else:
                    # 添加文件到结果
                    files.append(item_path)
                    
        except PermissionError:
            logger.warning(f"无法访问目录: {current_dir}")
        except Exception as e:
            logger.error(f"扫描目录时出错 {current_dir}: {e}")
    
    def _parse_search_pattern(self, pattern: str) -> Tuple[str, str, str, str]:
        """
        解析搜索模式
        
        Args:
            pattern: 搜索模式字符串
            
        Returns:
            (搜索模式, 前缀, 路径, 查询词) 元组
            搜索模式: "browse" | "search_in_dir" | "global_search"
        """
        if not pattern:
            return "global_search", "", "", ""
        
        # 标准化pattern
        pattern = pattern.strip()
        
        # 如果以/结尾，则是目录浏览模式
        if pattern.endswith("/"):
            # 移除结尾的/，然后解析前缀和路径
            dir_path = pattern.rstrip("/")
            prefix, sub_path = self.path_mapping_service.parse_virtual_path(dir_path)
            return "browse", prefix, sub_path, ""
        
        # 检查是否包含/，如果包含则可能是在目录下搜索
        if "/" in pattern:
            # 分离目录部分和搜索词部分
            parts = pattern.rsplit("/", 1)
            if len(parts) == 2:
                dir_part, search_part = parts
                # 如果目录部分不为空，则是在目录下搜索
                if dir_part:
                    prefix, sub_path = self.path_mapping_service.parse_virtual_path(dir_part)
                    return "search_in_dir", prefix, sub_path, search_part
        
        # 否则是全局搜索
        # 移除开头的/
        search_query = pattern.lstrip("/")
        return "global_search", "", "", search_query
    
    def _get_root_directories(self, owner: str, repo_name: str, branch: str, user_code: str) -> List[Dict[str, str]]:
        """获取根目录列表（简化版）"""
        # 使用路径映射服务获取根目录列表
        root_dirs = self.path_mapping_service.get_root_virtual_directories(
            owner, repo_name, branch, user_code
        )
        
        return [
            {
                "real_path": "",
                "virtual_path": f"{dir_name}/",
                "is_directory": True
            }
            for dir_name in root_dirs
        ]
    
    def _convert_symlink_path(self, path: Path, owner: str, repo_name: str, branch: str, user_code: str) -> Optional[Path]:
        """
        检测沙箱软链接，转换成本地的路径
        """
        # 初始化解析后的路径
        resolved_path_parts = []
        current_base = Path()

        logger.info(f"convert symlink path to local path")

        try:
            for part in path.parts:
                # 拼接当前部分的完整路径
                current_path = current_base / part

                # 检查当前路径是否是符号链接
                if current_path.is_symlink():
                    # 获取符号链接指向的目标路径
                    target_path = current_path.readlink()

                    logger.info(f"target_path: {target_path.as_posix()}")

                    target_path_str = target_path.as_posix().lstrip("/")
                    if target_path_str.startswith("data/workspace"):
                        logger.info(f"sandbox symlink")
                        target_path_str = target_path_str.replace("data/workspace", "")
                        target_path = self.path_mapping_service.convert_virtual_to_real_path(
                            target_path_str, owner, repo_name, branch, user_code
                        )
                    else:
                        target_path = current_path

                    logger.info(f"target_path_str: {target_path_str}")
                    logger.info(f"converted_target_path: {target_path.as_posix()}")

                    # 如果目标路径是相对路径，转换为绝对路径
                    target_full_path = (current_base / target_path).resolve()

                    logger.info(f"target_full_path: {target_full_path.as_posix()}")

                    resolved_path_parts.append(target_full_path)
                    current_base = target_full_path
                else:
                    # 如果不是符号链接，直接添加当前部分
                    resolved_path_parts.append(current_path)
                    current_base = current_path  # 更新基础路径为当前路径
        except Exception as e:
            logger.error(f"Failed to convert symlink, cause by: {e}")
            return None

        # 返回拼接后的完整路径
        return Path(*resolved_path_parts)
    
    def read_file_content(self, real_path: str, max_size: int = 1024 * 1024) -> Optional[str]:
        """
        读取文件内容
        
        Args:
            real_path: 实际文件路径
            max_size: 最大文件大小（字节）
            
        Returns:
            文件内容或 None
        """
        try:
            # 转换为 Path 对象
            file_path = Path(real_path)
            
            if not file_path.exists() or not file_path.is_file():
                return None
            
            file_size = file_path.stat().st_size
            if file_size > max_size:
                logger.warning(f"文件太大，跳过读取: {file_path} ({file_size} bytes)")
                return None
            
            # 尝试以文本方式读取
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
            
            for encoding in encodings:
                try:
                    content = file_path.read_text(encoding=encoding)
                    return content
                except UnicodeDecodeError:
                    continue
            
            logger.warning(f"无法解码文件: {file_path}")
            return None
            
        except Exception as e:
            logger.error(f"读取文件失败 {real_path}: {e}")
            return None

# 全局文件搜索服务实例
_file_search_service = None

def get_file_search_service() -> FileSearchService:
    """获取文件搜索服务实例"""
    global _file_search_service
    if _file_search_service is None:
        _file_search_service = FileSearchService()
    return _file_search_service
