# 安装 uv
echo "正在安装 uv ..."
if ! command -v uv &> /dev/null; then
  # 使用本地 uv 安装脚本
  echo "正在执行本地 uv 安装脚本 ..."
  chmod +x uv-install.sh
  sh ./uv-install.sh
  
  export PATH="$HOME/.local/bin:$PATH"
else
  echo "uv 已安装"
fi

# 使用 uv 创建虚拟环境并安装依赖
echo "正在使用 uv 创建虚拟环境并安装依赖 ..."
uv sync -i https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com

echo "激活虚拟环境 ..."
source .venv/bin/activate

