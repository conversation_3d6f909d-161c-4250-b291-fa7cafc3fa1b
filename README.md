# DeepWiki

**DeepWiki**可以为任何研发云代码仓库自动创建美观、交互式的Wiki！只需输入仓库名称，DeepWiki将：

1. 分析代码结构
2. 生成全面的文档
3. 创建可视化图表解释一切如何运作
4. 将所有内容整理成易于导航的Wiki

## ✨ 特点

- **即时文档**：几秒钟内将研发云的仓库转换为Wiki
- **私有仓库支持**：使用个人访问令牌安全访问私有仓库
- **智能分析**：AI驱动的代码结构和关系理解
- **精美图表**：自动生成Mermaid图表可视化架构和数据流
- **简易导航**：简单、直观的界面探索Wiki
- **提问功能**：使用RAG驱动的AI与您的仓库聊天，获取准确答案
- **深度研究**：多轮研究过程，彻底调查复杂主题
- **多模型提供商**：支持Google Gemini、OpenAI等

## 🚀 快速开始

### 本地启动

#### 步骤1：设置API密钥

在项目根目录创建一个`.env`文件，包含以下密钥：

```
GOOGLE_API_KEY=your_google_api_key
OPENAI_API_KEY=your_openai_api_key
# 可选：如果您想使用OpenRouter模型，添加此项
OPENROUTER_API_KEY=your_openrouter_api_key
```

有关更完整的配置说明（配置层次、配置文件与环境变量示例），请参见《[DeepWiki 配置指南](docs/i-docs/configuration.md)》。

#### 步骤2：启动后端

```bash
# 安装Python依赖
pip install -r api/requirements.txt

# 启动API服务器
source .venv/bin/activate && python -m uvicorn api.main:app --host 0.0.0.0 --port 8001 --reload
```

#### 步骤3：启动前端

```bash
# 安装JavaScript依赖
npm install
# 或
yarn install

# 启动Web应用
npm run dev
# 或
yarn dev
```

#### 步骤4：使用DeepWiki！

1. 在浏览器中打开[http://localhost:3000](http://localhost:3000)
2. 输入GitHub、GitLab或Bitbucket仓库（如`https://github.com/openai/codex`、`https://github.com/microsoft/autogen`、`https://gitlab.com/gitlab-org/gitlab`或`https://bitbucket.org/redradish/atlassian_app_versions`）
3. 对于私有仓库，点击"+ 添加访问令牌"并输入您的GitHub或GitLab个人访问令牌
4. 点击"生成Wiki"，见证奇迹的发生！

## 🔍 工作原理

DeepWiki使用AI来：

1. 克隆并分析GitHub、GitLab或Bitbucket仓库（包括使用令牌认证的私有仓库）
2. 创建代码嵌入用于智能检索
3. 使用上下文感知AI生成文档（使用Google Gemini、OpenAI、OpenRouter或本地Ollama模型）
4. 创建可视化图表解释代码关系
5. 将所有内容组织成结构化Wiki
6. 通过提问功能实现与仓库的智能问答
7. 通过深度研究功能提供深入研究能力

```mermaid
graph TD
    A[用户输入研发云仓库] --> AA{私有仓库?}
    AA -->|是| AB[添加访问令牌]
    AA -->|否| B[克隆仓库]
    AB --> B
    B --> C[分析代码结构]
    C --> D[创建代码嵌入]

    D --> M{选择模型提供商}
    M -->|Google Gemini| E1[使用Gemini生成]
    M -->|OpenAI| E2[使用OpenAI生成]
    M -->|OpenRouter| E3[使用OpenRouter生成]
    M -->|本地Ollama| E4[使用Ollama生成]

    E1 --> E[生成文档]
    E2 --> E
    E3 --> E
    E4 --> E

    D --> F[创建可视化图表]
    E --> G[组织为Wiki]
    F --> G
    G --> H[交互式DeepWiki]

    classDef process stroke-width:2px;
    classDef data stroke-width:2px;
    classDef result stroke-width:2px;
    classDef decision stroke-width:2px;

    class A,D data;
    class AA,M decision;
    class B,C,E,F,G,AB,E1,E2,E3,E4 process;
    class H result;
```

## 🛠️ 项目结构

```
deepwiki/
├── api/                          # 后端 API 服务
│   ├── main.py                   # FastAPI 入口
│   ├── api.py                    # API 路由装载
│   ├── rag.py                    # 顶层 RAG 封装
│   ├── data_pipeline.py          # 数据处理/构建流水线
│   ├── bedrock_client.py         # AWS Bedrock 客户端
│   ├── openai_client.py          # OpenAI 客户端
│   ├── openrouter_client.py      # OpenRouter 客户端
│   ├── gemini_cli_client.py      # Gemini CLI 客户端
│   ├── ollama_patch.py           # Ollama 兼容处理
│   ├── sse_chat.py               # SSE 聊天流式接口
│   ├── websocket_wiki.py         # WebSocket 推送
│   ├── config.py                 # 配置加载
│   ├── logging_config.py         # 日志配置
│   ├── settings.yaml             # 后端设置
│   ├── config/                   # 模型与处理配置
│   │   ├── generator.json
│   │   ├── embedder.json
│   │   ├── lang.json
│   │   └── repo.json
│   ├── database/                 # 数据库访问
│   │   ├── base.py
│   │   └── service.py
│   ├── model/                    # ORM/数据模型
│   ├── router/                   # FastAPI 路由
│   ├── middleware/               # 中间件（鉴权、审计、语言等）
│   ├── cache/                    # 缓存实现（local/redis）
│   ├── docchain/                 # 文档链与 RAG 相关模块
│   ├── filesearch/               # 文件搜索/目录索引
│   ├── service/                  # 业务服务层
│   ├── tools/                    # 嵌入器等工具
│   ├── utils/                    # 通用工具
│   ├── prompts/                  # 提示词与模板
│   └── i18n/                     # 后端国际化
│
├── src/                          # 前端 Next.js 应用
│   ├── app/                      # 应用与路由
│   ├── components/               # UI 组件
│   ├── contexts/                 # React Contexts
│   ├── hooks/                    # 自定义 Hooks
│   ├── messages/                 # 前端国际化文案
│   ├── types/                    # TS 类型
│   └── utils/                    # 前端工具
│
├── public/                       # 静态资源
├── worker/
│   └── wiki_worker.py            # 后台任务/工作器
├── scripts/
│   ├── full_script/              # 初始化 SQL 脚本
│   └── increment_script/         # 增量 SQL 脚本
├── test/                         # 后端测试
├── docker-compose.yml
├── Dockerfile                    # 后端构建
├── Dockerfile.node20             # 前端构建（Node 20）
├── package.json                  # 前端依赖
├── requirements.txt              # 根级 Python 依赖
├── api/requirements.txt          # 后端 Python 依赖
└── .env                          # 环境变量（需自行创建）
```

## 🤖 提问和深度研究功能

### 提问功能

提问功能允许您使用检索增强生成（RAG）与您的仓库聊天：

- **上下文感知响应**：基于仓库中实际代码获取准确答案
- **RAG驱动**：系统检索相关代码片段，提供有根据的响应
- **实时流式传输**：实时查看生成的响应，获得更交互式的体验
- **对话历史**：系统在问题之间保持上下文，实现更连贯的交互

### 深度研究功能

深度研究通过多轮研究过程将仓库分析提升到新水平：

- **深入调查**：通过多次研究迭代彻底探索复杂主题
- **结构化过程**：遵循清晰的研究计划，包含更新和全面结论
- **自动继续**：AI自动继续研究直到达成结论（最多5次迭代）
- **研究阶段**：
  1. **研究计划**：概述方法和初步发现
  2. **研究更新**：在前一轮迭代基础上增加新见解
  3. **最终结论**：基于所有迭代提供全面答案

要使用深度研究，只需在提交问题前在提问界面中切换"深度研究"开关。


### 环境变量

根据实际代码与 `env-example`，系统使用的环境变量如下（按模块分类）：

- 后端（模型与生成）
  - GOOGLE_API_KEY: 使用 Google Gemini 必需
  - OPENAI_API_KEY: 使用 OpenAI 或 OpenAI 兼容服务时必需
  - OPENROUTER_API_KEY: 使用 OpenRouter 时必需
  - OPENAI_BASE_URL: 后端 OpenAI 客户端的基础 URL（默认 `https://api.openai.com/v1`）
  - OPENAI_API_BASE_URL: 仅用于 `api/config/embedder.json` 占位符替换（OpenAI 兼容 embedding 服务）

- 后端（授权与配置）
  - DEEPWIKI_AUTH_MODE: `true`/`1` 启用前端授权模式
  - DEEPWIKI_AUTH_CODE: 授权码（启用授权模式时必需）
  - DEEPWIKI_CONFIG_DIR: 自定义配置目录（覆盖 `api/config/`）

- 后端/前端（DocChain 集成，可选）
  - USE_DOCCHAIN: `true`/`false` 开关
  - DOCCHAIN_BASE_URL: DocChain 服务地址（本地默认 `http://localhost:7000`）
  - DOCCHAIN_API_KEY: DocChain API 密钥
  - DOCCHAIN_DIRECT_MODE: `true`/`false` 直连模式
  - LAB_DOCCHAIN_BASE_URL: 实验/生产 DocChain 地址（前端 API 路由使用）
  - LAB_DOCCHAIN_API_KEY: 实验/生产 DocChain API 密钥
  - LAB_DOCCHAIN_DIRECT_MODE: 实验/生产直连模式

- 前端（运行时）
  - NEXT_PUBLIC_SERVER_BASE_URL: 前端访问后端 API 的地址（默认 `http://localhost:8001`）
  - NEXT_PUBLIC_DXP_BASE_URL: 可选外链地址（若相关功能启用）

- 其他（按需）
  - SSO_APP_KEY: 若启用 SSO 相关逻辑，可在前端配置（默认内置一个占位值）

最少配置建议：
- 仅使用 OpenAI 模型：设置 `OPENAI_API_KEY`（可选 `OPENAI_BASE_URL`）
- 使用 Google Gemini：设置 `GOOGLE_API_KEY`
- 使用 OpenRouter：设置 `OPENROUTER_API_KEY`
- 前后端本地联调：设置 `NEXT_PUBLIC_SERVER_BASE_URL=http://localhost:8001`

示例（.env）：
```
# 基础模型配置
OPENAI_API_KEY=your_openai_api_key
# 可选：自定义 OpenAI 兼容端点
OPENAI_BASE_URL=https://your-openai-compatible.com/v1
# 仅 embedder.json 占位符替换用（如使用 OpenAI 兼容 embedding）
OPENAI_API_BASE_URL=https://your-openai-compatible.com/v1

# 可选：Google / OpenRouter
GOOGLE_API_KEY=your_google_api_key
OPENROUTER_API_KEY=your_openrouter_api_key

# 前端访问后端 API
NEXT_PUBLIC_SERVER_BASE_URL=http://localhost:8001

# DocChain（可选）
USE_DOCCHAIN=true
DOCCHAIN_BASE_URL=http://localhost:7000
DOCCHAIN_API_KEY=your_docchain_api_key
DOCCHAIN_DIRECT_MODE=false
# 生产/实验 DocChain（可选，前端 API 路由可能使用）
LAB_DOCCHAIN_BASE_URL=https://example.com/docchain
LAB_DOCCHAIN_API_KEY=your_lab_docchain_api_key
LAB_DOCCHAIN_DIRECT_MODE=false

# 授权模式（可选）
DEEPWIKI_AUTH_MODE=false
DEEPWIKI_AUTH_CODE=

# 可选：自定义配置目录
DEEPWIKI_CONFIG_DIR=/path/to/custom/config

# 可选：前端其他集成
NEXT_PUBLIC_DXP_BASE_URL=https://da.example.com/davinci/...
SSO_APP_KEY=your_sso_app_key
```

### 为服务提供者设计的自定义模型选择

自定义模型选择功能专为需要以下功能的服务提供者设计：

- 您可在您的组织内部为用户提供多种 AI 模型选择
- 您无需代码更改即可快速适应快速发展的 LLM 领域
- 您可支持预定义列表中没有的专业或微调模型

使用者可以通过从服务提供者预定义选项中选择或在前端界面中输入自定义模型标识符来实现其模型产品。

### 为企业私有渠道设计的基础 URL 配置

OpenAI 客户端的 base_url 配置主要为拥有私有 API 渠道的企业用户设计。此功能：

- 支持连接到私有或企业特定的 API 端点
- 允许组织使用自己的自托管或自定义部署的 LLM 服务
- 支持与第三方 OpenAI API 兼容服务的集成

**即将推出**：在未来的更新中，DeepWiki 将支持一种模式，用户需要在请求中提供自己的 API 密钥。这将允许拥有私有渠道的企业客户使用其现有的 API 安排，而不是与 DeepWiki 部署共享凭据。

（说明）如果不使用 Ollama 模式，embedding 默认走 OpenAI 兼容接口，请确保至少配置 `OPENAI_API_KEY`；如使用自定义兼容端点，请同时设置 `OPENAI_API_BASE_URL` 以便替换 `api/config/embedder.json` 中的占位符。

更多细节与示例请参见《[DeepWiki 配置指南](docs/i-docs/configuration.md)》。

## 授权模式

DeepWiki 可以配置为在授权模式下运行，在该模式下，生成 Wiki 需要有效的授权码。如果您想控制谁可以使用生成功能，这将非常有用。
限制使用前端页面生成wiki并保护已生成页面的缓存删除，但无法完全阻止直接访问 API 端点生成wiki。主要目的是为了保护管理员已生成的wiki页面，防止被访问者重新生成。

要启用授权模式，请设置以下环境变量：

- `DEEPWIKI_AUTH_MODE`: 将此设置为 `true` 或 `1`。启用后，前端将显示一个用于输入授权码的字段。
- `DEEPWIKI_AUTH_CODE`: 将此设置为所需的密钥。限制使用前端页面生成wiki并保护已生成页面的缓存删除，但无法完全阻止直接访问 API 端点生成wiki。

如果未设置 `DEEPWIKI_AUTH_MODE` 或将其设置为 `false`（或除 `true`/`1` 之外的任何其他值），则授权功能将被禁用，并且不需要任何代码。

### 配置文件

DeepWiki使用JSON配置文件管理系统的各个方面：

1. **`generator.json`**：文本生成模型配置
   - 定义可用的模型提供商（Google、OpenAI、OpenRouter、Ollama）
   - 指定每个提供商的默认和可用模型
   - 包含特定模型的参数，如temperature和top_p

2. **`embedder.json`**：嵌入模型和文本处理配置
   - 定义用于向量存储的嵌入模型
   - 包含用于RAG的检索器配置
   - 指定文档分块的文本分割器设置

3. **`repo.json`**：仓库处理配置
   - 包含排除特定文件和目录的文件过滤器
   - 定义仓库大小限制和处理规则

默认情况下，这些文件位于`api/config/`目录中。您可以使用`DEEPWIKI_CONFIG_DIR`环境变量自定义它们的位置。

### 面向服务提供商的自定义模型选择

自定义模型选择功能专为需要以下功能的服务提供者设计：

- 您可在您的组织内部为用户提供多种 AI 模型选择
- 您无需代码更改即可快速适应快速发展的 LLM 领域
- 您可支持预定义列表中没有的专业或微调模型

使用者可以通过从服务提供者预定义选项中选择或在前端界面中输入自定义模型标识符来实现其模型产品。

### 为企业私有渠道设计的基础 URL 配置

OpenAI 客户端的 base_url 配置主要为拥有私有 API 渠道的企业用户设计。此功能：

- 支持连接到私有或企业特定的 API 端点
- 允许组织使用自己的自托管或自定义部署的 LLM 服务
- 支持与第三方 OpenAI API 兼容服务的集成

**即将推出**：在未来的更新中，DeepWiki 将支持一种模式，用户需要在请求中提供自己的 API 密钥。这将允许拥有私有渠道的企业客户使用其现有的 API 安排，而不是与 DeepWiki 部署共享凭据。

## 🧩 使用 OpenAI 兼容的 Embedding 模型（如阿里巴巴 Qwen）

如果你希望使用 OpenAI 以外、但兼容 OpenAI 接口的 embedding 模型（如阿里巴巴 Qwen），请参考以下步骤：

1. 用 `api/config/embedder_openai_compatible.json` 的内容替换 `api/config/embedder.json`。
2. 在项目根目录的 `.env` 文件中，配置相应的环境变量，例如：
   ```
   OPENAI_API_KEY=你的_api_key
   OPENAI_API_BASE_URL=你的_openai_兼容接口地址
   ```
3. 程序会自动用环境变量的值替换 embedder.json 里的占位符。

这样即可无缝切换到 OpenAI 兼容的 embedding 服务，无需修改代码。