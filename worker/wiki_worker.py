import logging
import time
import traceback

from api.database.base import session_scope
from api.service import wiki_service
from api.wiki_generator import process_wiki_job

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main_loop():
    """
    The main loop for the worker process.
    Continuously checks for and processes queued wiki generation jobs.
    """
    logger.info("Worker process started. Waiting for jobs...")
    while True:
        job_to_process = None
        try:
            with session_scope() as session:
                # Find the next job to process
                job_to_process = session.query(wiki_service.WikiGenerationJob).filter(
                    wiki_service.WikiGenerationJob.status == 'QUEUED'
                ).order_by(wiki_service.WikiGenerationJob.created_at).first()

                if job_to_process:
                    logger.info(f"Picked up job: {job_to_process.job_id} for repo: {job_to_process.repo_url}")
                    # Mark the job as PROCESSING immediately to prevent other workers from picking it up
                    wiki_service.update_job_status(session, job_to_process.job_id, 'PROCESSING', 'Worker picked up the task.')
                
            # If a job was found, process it outside the session scope
            # to allow the generator to manage its own sessions.
            if job_to_process:
                process_wiki_job(job_to_process.job_id)
                logger.info(f"Finished processing job: {job_to_process.job_id}")
            else:
                # If no job is found, wait for a bit before checking again.
                time.sleep(10) # Sleep for 10 seconds

        except Exception as e:
            logger.error(f"An unexpected error occurred in the main worker loop: {e}")
            if job_to_process:
                logger.error(f"Marking job {job_to_process.job_id} as FAILED due to worker loop error.")
                with session_scope() as session:
                    wiki_service.mark_job_as_failed(session, job_to_process.job_id, traceback.format_exc())
            # Wait a bit longer after an error to prevent rapid-fire failures.
            time.sleep(30)


if __name__ == "__main__":
    main_loop() 