# Git
.git
.gitignore
.github

# Node.js
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Next.js
.next
out

# Python cache files (but keep api/ directory)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
# Keep api/ directory but exclude cache
api/__pycache__/
api/*.pyc

# Environment variables
# .env is now allowed to be included in the build
.env.local
.env.development.local
.env.test.local
.env.production.local

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Keep start.sh for container startup
!start.sh

# lock files - keep essential lock files for reproducible builds
yarn.lock
# Keep lock files for dependency management
!pnpm-lock.yaml
!uv.lock

# Misc
.DS_Store
*.pem
README.md
LICENSE
screenshots/
*.md
!api/README.md

# Development and testing files
.vscode/
.pytest_cache/
test/
.coverage
coverage.xml
*.log
new_logger.log
conversion.log

# Virtual environments
.venv/
venv/
.env

# Temporary files
.tmp/
tmp/
