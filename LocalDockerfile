# =============================================================================
# Stage 1: Node.js Base with PNPM
# =============================================================================
FROM hub.iwhalecloud.com:5555/hub_docker/library/node:20-alpine AS node_base
# 安装 pnpm 并设置国内镜像源
RUN npm install -g pnpm@10.12.4 && \
    pnpm config set registry http://npm.iwhalecloud.com:8081/repository/npm-all/

# =============================================================================
# Stage 2: 安装Node.js依赖 (使用pnpm)
# =============================================================================
FROM node_base AS node_deps
WORKDIR /app
COPY package.json ./
RUN pnpm install --force

# =============================================================================
# Stage 3: 构建Next.js应用 (使用pnpm)
# =============================================================================
FROM node_base AS node_builder

WORKDIR /app
COPY --from=node_deps /app/node_modules ./node_modules
# 只复制构建所需的文件
COPY package.json next.config.ts tsconfig.json tailwind.config.js postcss.config.mjs ./
COPY src/ ./src/
COPY public/ ./public/
# 增加Node.js内存限制并禁用遥测
ENV NODE_OPTIONS="--max-old-space-size=4096"
ENV NEXT_TELEMETRY_DISABLED=1
# 构建时参数，用于Next.js构建时内联到静态文件
ARG NEXT_PUBLIC_SERVER_BASE_URL
ENV NEXT_PUBLIC_SERVER_BASE_URL=${NEXT_PUBLIC_SERVER_BASE_URL}

ARG NEXT_PUBLIC_DXP_BASE_URL
ENV NEXT_PUBLIC_DXP_BASE_URL=${NEXT_PUBLIC_DXP_BASE_URL}

# 调试：打印环境变量值
RUN echo "构建时 NEXT_PUBLIC_SERVER_BASE_URL: ${NEXT_PUBLIC_SERVER_BASE_URL}"
RUN echo "构建时 NEXT_PUBLIC_DXP_BASE_URL: ${NEXT_PUBLIC_DXP_BASE_URL}"
# 使用 pnpm run build
RUN pnpm run build

# =============================================================================
# Stage 5: 最终运行镜像 (精简)
# =============================================================================
FROM hub-nj.iwhalecloud.com/ptdev01/python3.12-node20-uv-base:v2

WORKDIR /app

# 复制Python虚拟环境
COPY V/.venv /app/.venv
ENV PATH="/app/.venv/bin:$PATH"

# 复制Python应用代码
COPY /api ./api

# 复制Node.js构建产物
COPY --from=node_builder /app/public ./public
COPY --from=node_builder /app/.next/standalone ./
COPY --from=node_builder /app/.next/static ./.next/static

# 暴露端口
EXPOSE 8001 3000

# 复制启动脚本并设置执行权限
COPY start.sh /app/start.sh
RUN chmod +x /app/start.sh

# 设置环境变量
# Set environment variables
ENV PORT=3006
ENV NODE_ENV=production
ENV BASE_URL=http://localhost
ENV SERVER_BASE_URL=${BASE_URL:-http://localhost}:${PORT:-3006}
ENV NEXT_PUBLIC_SERVER_BASE_URL=${BASE_URL:-http://localhost}:${PORT:-3006}
# ENV PORT=8001
# ENV NODE_ENV=production
# ENV PYTHONUNBUFFERED=1
# ENV SERVER_BASE_URL=http://localhost:${PORT:-8001}

# 创建空的.env文件（运行时可被覆盖）
RUN touch .env

# 启动命令
CMD ["/app/start.sh"]

RUN echo "Verifying installations..." && \
    python --version && \
    uv --version && \
    node --version && \
    npm --version && \
    echo "Verification complete."
